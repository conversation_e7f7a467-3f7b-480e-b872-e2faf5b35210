import React from "react";
import { Flex, Typography } from "antd";
import PROFILE_IMG from "../../../assets/Images/iconamoon_profile-fill.png";
import HAND_IMG from "../../../assets/Images/solar_hand-money-outline-1.png";
import CONNECT_IMG from "../../../assets/Images/connect.png";
import CUSTOMER_IMG from "../../../assets/Images/vaadin_handshake.png";
import Style from "../_style.module.scss";
import {
  SMART_COLLECT_MENU_IDS,
  formatAmount,
  formatDigits,
} from "../../../constant";
import { Link } from "react-router";
import { useTour } from "../../../ContextApis/TourProvider";
import PropTypes from "prop-types";

const { Text } = Typography;

export default function CustomerDetails({ dashboardData }) {
  const { customerDetailsRef } = useTour();

  return (
    <Flex
      gap={0}
      justify="space-evenly"
      className={Style.customer_details}
      ref={customerDetailsRef}
    >
      <Flex className={Style.container} gap={10}>
        {/* Customer Details */}
        <Link to={SMART_COLLECT_MENU_IDS.TOTAL_CUSTOMERS}>
          <Flex
            vertical
            align="center"
            gap={1}
            style={{
              borderRight: "1px solid black",
              height: "100%",
              padding: "0 1rem 0 0",
            }}
          >
            <div className={Style.customer_img_div}>
              <img src={PROFILE_IMG} alt="profile" />
            </div>
            <Text className={Style.customer_value}>
              {formatDigits(dashboardData?.total_loans || 0)}
            </Text>
            <Text className={Style.customer_text}>Total Customers</Text>
          </Flex>
        </Link>

        <Link to={SMART_COLLECT_MENU_IDS.TOTAL_CUSTOMERS_CONNECTED}>
          <Flex
            vertical
            align="center"
            gap={1}
            style={{
              borderRight: "1px solid black",
              height: "100%",
              padding: "0 1rem 0 0",
            }}
          >
            <div className={Style.customer_img_div}>
              <img src={CUSTOMER_IMG} alt="customer" />
            </div>
            <Text className={Style.customer_value}>
              {formatDigits(dashboardData?.customers_connected || 0)}
            </Text>
            <Text className={Style.customer_text}>
              Total Customers Connected
            </Text>
          </Flex>
        </Link>
      </Flex>

      <Flex className={Style.container} gap={10}>
        <Link to={SMART_COLLECT_MENU_IDS.TOTAL_AMOUNT_PROMISED}>
          <Flex
            vertical
            align="center"
            gap={1}
            style={{
              borderRight: "1px solid black",
              height: "100%",
              padding: "0 1rem 0 0",
            }}
            className={Style.customer_container}
          >
            <div className={Style.customer_img_div}>
              <img src={CONNECT_IMG} alt="connect" />
            </div>
            <Text className={Style.customer_value}>
              ₹{formatAmount(dashboardData?.promised_amount || 0)}
            </Text>
            <Text className={Style.customer_text}>
              Total Amount Promised by Customers
            </Text>
          </Flex>
        </Link>

        <Link to={SMART_COLLECT_MENU_IDS.TOTAL_AMOUNT_COLLECTED}>
          <Flex
            vertical
            align="center"
            gap={1}
            className={Style.customer_container}
          >
            <div className={Style.customer_img_div}>
              <img src={HAND_IMG} alt="hand" />
            </div>
            <Text className={Style.customer_value}>
              ₹{formatAmount(dashboardData?.actual_collected || 0)}
            </Text>
            <Text className={Style.customer_text}>
              Total Amount Collected from Customers
            </Text>
          </Flex>
        </Link>
      </Flex>
    </Flex>
  );
}

CustomerDetails.propTypes = {
  dashboardData: PropTypes.object,
};

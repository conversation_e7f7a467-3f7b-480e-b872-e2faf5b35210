import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta

def generate_emi_schedule(df):
    """
    Generate EMI schedule based on the stored procedure logic
    """
    # Frequency mapping as provided
    frequency_mapping = {
        'M': 30,        # Monthly
        'MONTHLY': 30,
        'Q': 90,        # Quarterly
        'QUARTERLY': 90,
        'QUATERLY': 90,
        'H': 180,       # Half-yearly
        'HALF-YEARLY': 180,
        'HALF YEARLY': 180,
        'Y': 365,       # Yearly
        'YEARLY': 365,
        'W': 7,         # Weekly
        'WEEKLY': 7,
        'F': 15,        # Fortnightly
        'FORTNIGHTLY': 15,
        'U': 30,        # Unknown - default to Monthly
        'UNKNOWN': 30,
        '': 30,         # Empty - default to Monthly
        'NAN': 30,       # NaN - default to Monthly
        "ON EXPIRY": 0
    }
    
    # Convert payment_frequency to numeric using mapping
    df['payment_frequency_mapped'] = df['payment_frequency'].astype(str).str.upper().map(frequency_mapping)
    
    # Handle any unmapped values by defaulting to 30 (Monthly)
    df['payment_frequency_mapped'] = df['payment_frequency_mapped'].fillna(30)
    
    # Convert ins_start_dt to datetime with error handling
    print("Converting ins_start_dt to datetime...")
    df['ins_start_dt'] = pd.to_datetime(df['ins_start_dt'], errors='coerce', dayfirst=True)
    
    # Check for invalid dates
    invalid_dates = df[df['ins_start_dt'].isna() & df['ins_start_dt'].notna()]
    if len(invalid_dates) > 0:
        print(f"Warning: Found {len(invalid_dates)} records with invalid dates")
    
    # Remove records with invalid or missing start dates
    original_count = len(df)
    df = df.dropna(subset=['ins_start_dt'])
    if len(df) < original_count:
        print(f"Removed {original_count - len(df)} records with invalid/missing start dates")
    
    # Also handle missing repayment_tenure and emi_amount
    print("Cleaning data...")
    df = df.dropna(subset=['repayment_tenure', 'emi_amount'])
    
    # Convert to appropriate types
    df['repayment_tenure'] = pd.to_numeric(df['repayment_tenure'], errors='coerce')
    df['emi_amount'] = pd.to_numeric(df['emi_amount'], errors='coerce')
    
    # Remove records with invalid numeric values
    df = df.dropna(subset=['repayment_tenure', 'emi_amount'])
    
    print(f"Processing {len(df)} valid records...")
    
    # Prepare the result list
    installment_records = []
    
    # Process each loan record
    for idx, row in df.iterrows():
        disbursement_id = row['disbursement_id']
        installment_start_date = row['ins_start_dt']
        repayment_tenure = int(row['repayment_tenure'])
        payment_frequency = int(row['payment_frequency_mapped'])
        emi_amount = row['emi_amount']
        bank_mst_id = 36  # Fixed value as specified
        customer_mst_id = row['customer_id']
        
        # Generate installments for this loan (equivalent to GENERATE_SERIES(0, repayment_tenure - 1))
        for n in range(repayment_tenure):
            schedule_id = n + 1
            
            # Calculate schedule date based on payment frequency (replicating SP CASE logic)
            if payment_frequency == 1:
                schedule_date = installment_start_date + timedelta(days=n * 1)
            elif payment_frequency == 7:
                schedule_date = installment_start_date + timedelta(days=n * 7)
            elif payment_frequency == 15:
                schedule_date = installment_start_date + timedelta(days=n * 15)
            elif payment_frequency == 30:
                schedule_date = installment_start_date + relativedelta(months=n * 1)
            elif payment_frequency == 90:
                schedule_date = installment_start_date + relativedelta(months=n * 3)
            elif payment_frequency in [182, 180]:
                schedule_date = installment_start_date + relativedelta(months=n * 6)
            elif payment_frequency == 365:
                schedule_date = installment_start_date + relativedelta(years=n * 1)
            else:
                # Default case: add n * payment_frequency days
                schedule_date = installment_start_date + timedelta(days=n * payment_frequency)
            
            # Create installment record
            installment_record = {
                'DisbursementID': disbursement_id,
                'ScheduleID': schedule_id,
                'ScheduleDate': schedule_date,
                'PrincipleAmt': 0,  # As per SP logic
                'InterestAmt': 0,   # As per SP logic
                'Demand': emi_amount,  # EMI Amount as Demand
                'BankMstID': bank_mst_id,
                'CustomerMstID': customer_mst_id
            }
            
            installment_records.append(installment_record)
    
    # Convert to DataFrame
    result_df = pd.DataFrame(installment_records)
    
    # Sort by DisbursementID and ScheduleID (as per SP)
    result_df = result_df.sort_values(['DisbursementID', 'ScheduleID']).reset_index(drop=True)
    
    return result_df

def get_next_emi_info(df):
    """
    Get next EMI date and amount for each loan (first installment)
    """
    full_schedule = generate_emi_schedule(df)
    
    # Get only the first installment for each loan (next EMI)
    next_emi = full_schedule[full_schedule['ScheduleID'] == 1].copy()
    
    # Rename columns for clarity
    next_emi = next_emi.rename(columns={
        'ScheduleDate': 'NextEMIDate',
        'Demand': 'NextEMIAmount'
    })
    
    # Select relevant columns
    next_emi_info = next_emi[['DisbursementID', 'CustomerMstID', 'NextEMIDate', 'NextEMIAmount']].copy()
    
    return next_emi_info

# Example usage:
if __name__ == "__main__":
    # Read your Excel file
    df = pd.read_excel(r"C:\Users\<USER>\Downloads\03-07-2025\Loan Details.xls")
    
    print("Original Data:")
    print(df.head())
    
    # Generate complete EMI schedule
    emi_schedule = generate_emi_schedule(df)
    print(f"\nGenerated {len(emi_schedule)} installment records")
    print("\nFirst few installment records:")
    print(emi_schedule.head(10))
    
    # Get next EMI info only
    next_emi_info = get_next_emi_info(df)
    print("\nNext EMI Information:")
    print(next_emi_info.head())
    
    # Save results to Excel
    with pd.ExcelWriter('EMI_Schedule_Output.xlsx') as writer:
        emi_schedule.to_excel(writer, sheet_name='Full_EMI_Schedule', index=False)
        next_emi_info.to_excel(writer, sheet_name='Next_EMI_Info', index=False)
    
    print("\nResults saved to 'EMI_Schedule_Output.xlsx'")
    print("- Sheet 'Full_EMI_Schedule': Complete installment schedule for all loans")
    print("- Sheet 'Next_EMI_Info': Next EMI date and amount for each loan")
    
    # Also save just the next EMI info as a separate file
    next_emi_info.to_excel('Next_EMI_Details.xlsx', index=False)
    print("\nNext EMI details also saved separately to 'Next_EMI_Details.xlsx'")
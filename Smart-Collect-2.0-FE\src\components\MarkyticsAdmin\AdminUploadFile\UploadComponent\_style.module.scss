@import url("../../../../index.css");

$light-blue:#E3F5F6;

.container {
    width: 100%;
  .uploadZone {
    position: relative;
    border-radius: 8px;
    padding: 20px;
    background-color: $light-blue;
    transition: all 0.3s;  
    width: 100%;
    border:none;
      .content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 16px;

        .upload_img{
          width: 50px;
          img{
              width: 100%;
              object-fit: contain;
          }
        }

        .text {
          color: #5583B0;
          font-weight: 400;
          font-size: 14px;
          font-family: 'Kanit','Courier New', Courier, monospace;
        }

        .browseButton {
          position: relative;
          padding: 0;
          height: auto;
          line-height: inherit;
          color: #1651D7;
          text-decoration: underline;
          font-weight: 500;
          font-family: 'Kanit','Courier New', Courier, monospace;
        }

        .fileInput {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          opacity: 0;
          cursor: pointer;
        }
    } 
  } 
}

@media only screen and (max-width:768px){
  .container {
    .uploadZone {
      padding: 10px;  
      .content {
        gap: 10px;

        .upload_img{
          width: 30px;
        }

        .text {
          font-size: 12px;
        }

        .browseButton {
          font-size: 12px;
        }
    } 
  } 
}}
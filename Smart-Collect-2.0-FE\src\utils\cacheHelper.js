import CryptoJS from "crypto-js";

/* Generic helper to open cache */
async function getCache(cacheName) {
    return await caches.open(cacheName);
}

/* Create unique key */
export function getCacheKey({ endpoint, params = {} }) {
    const query = Object.entries(params)
        .map(([k, v]) => `${k}=${encodeURIComponent(v ?? "")}`)
        .join("&");
    return `${endpoint}?${query}`;
}

/* Create unique post key */
export function getPostCacheKey({ endpoint, body }) {
    const sorted = Object.keys(body)
        .sort((a, b) => a.localeCompare(b))
        .reduce((acc, key) => {
            acc[key] = body[key];
            return acc;
        }, {});
    const hash = CryptoJS.SHA256(JSON.stringify(sorted)).toString();
    return `${endpoint}::${hash}`;
}

/* Store to cache */
export async function storeToCache({ cacheName, key, data, ttl = 10 * 60 * 1000 }) {
    const cache = await getCache(cacheName);
    const payload = {
        data,
        cachedAt: Date.now(),
        ttl,
    };
    const response = new Response(JSON.stringify(payload), {
        headers: { "Content-Type": "application/json" },
    });
    await cache.put(key, response);
}

/* Get from cache */
export async function getFromCache({ cacheName, key }) {
    const cache = await getCache(cacheName);
    const match = await cache.match(key);
    if (!match) return null;
    const cached = await match.json();

    const now = Date.now();
    const isExpired = cached.ttl && now - cached.cachedAt > cached.ttl;

    if (isExpired) {
        await cache.delete(key); // Clean up expired cache
        return null;
    }

    return cached.data;
}

/* Clear cache */
export async function clearCache({ cacheName, key = null }) {
    const cache = await getCache(cacheName);
    if (key) {
        await cache.delete(key);
    } else {
        const keys = await cache.keys();
        await Promise.all(keys.map((req) => cache.delete(req)));
    }
}
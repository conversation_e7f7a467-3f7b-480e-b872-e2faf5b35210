import PropTypes from "prop-types";
import React from "react";
import ApplicationLoader from "../ApplicationLoader";
import Style from "./_style.module.scss";
import { Flex, Table, Typography } from "antd";
import { handleDownloadTable } from "../../constant";
import DOWNLOAD_IMG from "../../assets/Images/download.svg";

const { Text } = Typography;

export default function TodayAllocation({ data, isLoading, dates }) {
  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      render: (_, data) => <Text>{data.key}.</Text>,
    },
    {
      title: "Bank ID",
      dataIndex: "Bank ID",
      width: 200,
      render: (value) => <Text>{value ?? "--"}</Text>,
    },
    {
      title: "Bank Name",
      dataIndex: "Bank Name",
      render: (value) => <Text>{value ?? "--"}</Text>,
    },
    {
      title: "Total WhatsApps",
      dataIndex: "Total WhatsApps",
      render: (value) => <Text>{value ?? 0}</Text>,
    },
    {
      title: "Total AI Calls",
      dataIndex: "Total AI Calls",
      render: (value) => <Text>{value ?? 0}</Text>,
    },
    {
      title: "Total Blasters",
      dataIndex: "Total Blasters",
      render: (value) => <Text>{value ?? 0}</Text>,
    },
    {
      title: "Total Responses",
      dataIndex: "Total Responses",
      render: (value) => <Text>{value ?? 0}</Text>,
    },
  ];

  const dataSource = Array.isArray(data)
    ? data.map((data, i) => ({
        key: i + 1,
        ...data,
      }))
    : [];

  // Handle downloading
  const handleDownload = async () => {
    if (data.length === 0) {
      return message.error("There is no data!");
    } else {
      // Get list of visible column keys
      const visibleColumns = columns.map((col) => col.dataIndex);
      // Filter only those keys from each row
      const filteredData = dataSource.map((row) => {
        const filteredRow = {};
        visibleColumns.forEach((key) => {
          filteredRow[key] = row[key];
        });
        return filteredRow;
      });
      await handleDownloadTable({
        excelName: "TodayAllocation",
        worksheetName: "Today-Allocation",
        tableData: filteredData,
        fromDate: dates?.fromDate,
        toDate: dates?.toDate,
      });
    }
  };

  const getSummaryRow = (columns, dataSource) => {
    return columns.map((column) => {
      if (column.dataIndex === "key") {
        return {
          children: <p>Total</p>,
        };
      } else if (
        [
          "Total WhatsApps",
          "Total AI Calls",
          "Total Blasters",
          "Total Responses",
        ].includes(column.dataIndex)
      ) {
        const total = dataSource.reduce(
          (sum, row) => sum + (parseFloat(row[column.dataIndex]) || 0),
          0
        );
        return {
          children: <p>{total}</p>,
        };
      } else {
        return {
          children: "--",
        };
      }
    });
  };

  return isLoading ? (
    <ApplicationLoader />
  ) : (
    <Flex vertical gap={10}>
      <Flex justify="end">
        <button className={Style.download_button} onClick={handleDownload}>
          <img src={DOWNLOAD_IMG} alt="download-button" />
        </button>
      </Flex>
      <Table
        virtual
        className={Style.customTable}
        columns={columns}
        dataSource={dataSource}
        scroll={{
          x: 800,
          y: 350,
        }}
        pagination={{
          showSizeChanger: false,
        }}
        summary={() => (
          <Table.Summary fixed>
            <Table.Summary.Row>
              {getSummaryRow(columns, dataSource).map((cell, index) => (
                <Table.Summary.Cell key={index}>
                  {cell.children}
                </Table.Summary.Cell>
              ))}
            </Table.Summary.Row>
          </Table.Summary>
        )}
      />
    </Flex>
  );
}

TodayAllocation.propTypes = {
  data: PropTypes.array,
  isLoading: PropTypes.bool,
  dates: PropTypes.object,
};

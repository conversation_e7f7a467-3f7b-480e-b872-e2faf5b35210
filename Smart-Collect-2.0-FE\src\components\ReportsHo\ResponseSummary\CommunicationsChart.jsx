import { Flex, Typography } from "antd";
import Style from "./_style.module.scss";
import Chart from "react-apexcharts";

const { Text } = Typography;

export function CommunicationsChart() {
  const series = [
    {
      name: "Denials",
      data: Array(4).fill(99),
    },
    {
      name: "Promises",
      data: Array(4).fill(99),
    },
    {
      name: "Claims",
      data: Array(4).fill(99),
    },
    {
      name: "Wrong numbers",
      data: Array(4).fill(99),
    },
  ];

  const options = {
    chart: {
      type: "bar",
      stacked: true,
      toolbar: {
        show: false,
      },
    },
    title: {
      text: "",
    },
    xaxis: {
      categories: ["09-09-2024", "10-09-2024", "11-09-2024", "12-09-2024"],
      title: {
        text: "Days",
        style: {
          fontSize: "16px",
          color: "#407BFF",
          fontWeight: "normal",
          fontFamily: "Kanit",
        },
      },
      labels: {
        rotate: -45,
        style: {
          colors: "#407BFF", // Blue color for X-axis labels
          fontWeight: 500,
          fontSize: "9px",
        },
      },
      axisBorder: {
        show: true,
        color: "#0F2050", // X-axis line color
      },
      axisTicks: {
        show: false,
      },
    },
    yaxis: {
      title: {
        text: "",
      },
      labels: {
        style: {
          colors: "#407BFF", // Blue color for Y-axis labels
          fontWeight: 500,
          fontSize: "9px",
        },
      },
      axisBorder: {
        show: true,
        color: "#0F2050", // Y-axis line color
      },
      axisTicks: {
        show: false,
      },
    },
    legend: {
      position: "top",
      horizontalAlign: "right",
    },
    plotOptions: {
      bar: {
        columnWidth: "30px",
        horizontal: false,
        borderRadius: 0,
        dataLabels: {
          position: "center",
        },
      },
    },
    dataLabels: {
      enabled: true,
      style: {
        fontSize: "9px",
        colors: ["#fff"],
      },
    },
    colors: ["#FF631A", "#FDBF00", "#33AB3C", "#14F3F7"],
    grid: {
      show: true,
      xaxis: {
        lines: {
          show: false, // X-axis grid lines
        },
      },
      yaxis: {
        lines: {
          show: true, // Y-axis grid lines
        },
      },
      borderColor: "#C6D1ED", // Applies to both X and Y grid lines
      strokeDashArray: 2,
    },
    tooltip: {
      shared: true,
      intersect: false,
    },
  };

  return (
    <Flex className={Style.chart_container} vertical gap={5}>
      <Chart options={options} series={series} type="bar" height={320} />
    </Flex>
  );
}

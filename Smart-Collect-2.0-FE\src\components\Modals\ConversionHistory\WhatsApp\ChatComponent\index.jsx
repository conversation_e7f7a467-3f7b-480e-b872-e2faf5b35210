import React from "react";
import Style from "./_style.module.scss";
import { Card, Flex } from "antd";

export function ChatComponent(messages) {
  const UserDetails = JSON.parse(localStorage.getItem("user"));
  const bankName = UserDetails?.BankMstName;

  const shouldAddLineBreak = (line) => {
    const rules = [
      "कंपनीचा",
      "कृपया दिलेल्या",
      "खाली संबंधित तपशील दिलेला आहे",
      "आपल्या कर्जाचा हप्ता",
      "तुमची कर्जाची रक्कम",
      "त्याचा तपशील खालीलप्रमाणे",
      "तुम्ही तात्काळ देये",
      "कृपया खालीलपैकी",
    ];
    return (
      rules.some((rule) => line.includes(rule)) && !line.startsWith("<br>")
    );
  };

  const isBoldText = (text) => /^\*[^*]+\*$/.test(text);
  const isNumberedPoint = (text) => /^\d+\.$/.test(text.trim());
  const isCurrency = (text) => /₹\d+(\.\d{1,2})?/.test(text);
  const isPunctuation = (text) => text === "," || text === ".";

  const pushTempLine = (state) => {
    if (state.tempLine.trim()) {
      state.formattedParts.push(state.tempLine.trim());
      state.tempLine = "";
    }
  };

  const handleBold = (state, text) => {
    const bold = `<strong>${text.slice(1, -1)}</strong>`;
    if (state.expectingValueAfterBullet || state.tempLine.endsWith(":")) {
      state.tempLine += bold;
    } else {
      pushTempLine(state);
      state.tempLine = bold;
    }
    state.expectingValueAfterBullet = false;
  };

  const handleBulletPoint = (state, text) => {
    pushTempLine(state);
    state.tempLine = text;
    state.expectingValueAfterBullet = true;
  };

  const appendToPreviousBullet = (state, text) => {
    const lastIndex = state.formattedParts.length - 1;
    state.formattedParts[lastIndex] += ` ${text}`;
    state.expectingValueAfterBullet = false;
  };

  const handleExpectingValue = (state, text) => {
    if (
      state.expectingValueAfterBullet &&
      state.formattedParts.length &&
      state.formattedParts[state.formattedParts.length - 1].endsWith("▪")
    ) {
      appendToPreviousBullet(state, text);
      return true;
    }

    if (state.expectingValueAfterBullet && state.tempLine.endsWith(":")) {
      state.tempLine += ` ${text}`;
      state.expectingValueAfterBullet = false;
      return true;
    }

    return false;
  };

  const processPart = (trimmed, state) => {
    if (trimmed === "▪") return handleBulletPoint(state, trimmed);
    if (isBoldText(trimmed)) return handleBold(state, trimmed);
    if (isNumberedPoint(trimmed)) {
      if (state.tempLine.trim() && !state.tempLine.includes("₹"))
        pushTempLine(state);
      state.tempLine += trimmed;
      return;
    }
    if (isCurrency(trimmed)) {
      state.tempLine += trimmed;
      return;
    }
    if (trimmed.includes(":")) {
      state.tempLine += trimmed;
      state.expectingValueAfterBullet = true;
      return;
    }
    if (isPunctuation(trimmed)) {
      pushTempLine(state);
      state.expectingValueAfterBullet = false;
      return;
    }
    if (trimmed && !handleExpectingValue(state, trimmed)) {
      state.tempLine += trimmed;
    }
  };

  // Format message if sender is "AARTHSIDDHI PRIVATE LTD"
  const formatMessage = (message) => {
    if (!message.includes("नमस्कार")) return message;

    // Step 1: Extract PDF link
    const pdfRegex = /(https?:\/\/[\s\S]*?\.pdf)/i;
    const pdfMatch = message.match(pdfRegex);
    let pdfLink = null;
    let messageWithoutPdf = message;

    if (pdfMatch) {
      pdfLink = pdfMatch[0];
      messageWithoutPdf = message.replace(
        pdfLink,
        "___PDF_LINK_PLACEHOLDER___"
      );
    }

    // Step 2: Split into parts
    const parts = messageWithoutPdf.split(/(\*[^*]+\*\d+\.|[.])/);
    const state = {
      formattedParts: [`<strong>${bankName}</strong><br><br>`],
      tempLine: "",
      expectingValueAfterBullet: false,
    };

    parts.forEach((part, index) => {
      const trimmed = part.trim();
      if (trimmed === "___PDF_LINK_PLACEHOLDER___" && pdfLink) {
        state.formattedParts.push(pdfLink);
      } else {
        processPart(trimmed, state);
        if (index === parts.length - 1 && state.tempLine.trim()) {
          state.formattedParts.push(state.tempLine.trim());
        }
      }
    });

    // Step 3: Add line breaks using `shouldAddLineBreak`
    const linesWithBreaks = state.formattedParts.map((line) => {
      if (shouldAddLineBreak(line)) return `<br>${line}`;
      if (line.includes("धन्यवाद!")) return `<br>${line}<br><br>`;
      return line;
    });

    // Step 4: Render final lines as JSX
    const rendered = linesWithBreaks.map((line, index) => {
      let linkToUse = null;

      if (line.includes("___PDF_LINK_PLACEHOLDER___") && pdfLink) {
        linkToUse = pdfLink;
      } else {
        const pdfRegex = /(https?:\/\/[\s\S]*?\.pdf)/i;
        const match = pdfRegex.exec(line);
        if (match) linkToUse = match[0];
      }

      if (linkToUse) {
        return (
          <div key={`${line}-${index}`}>
            <a
              href={encodeURI(linkToUse)}
              target="_blank"
              rel="noopener noreferrer"
              style={{ color: "white", textDecoration: "underline" }}
            >
              {linkToUse}
            </a>
          </div>
        );
      }

      return (
        <div
          key={`${line}-${index}`}
          style={{ whiteSpace: "pre-line" }} // important for <br> to work
          dangerouslySetInnerHTML={{ __html: line }}
        />
      );
    });

    return rendered;
  };

  return (
    <Flex vertical gap={10} className={Style.chat_container}>
      {messages.messages?.map((msg, index) => {
        const isBank = msg.Sender === bankName || msg.Sender === "bank";

        // Only format message for specific bank
        const formattedMessage = [
          "AARTHSIDDHI PRIVATE LTD",
          "AARTHSIDDHI SERVICES PRIVATE LTD",
          "PAVANA BANK",
        ].includes(msg.Sender)
          ? formatMessage(msg.message)
          : msg.message;

        return (
          <React.Fragment key={`${index}-${isBank}`}>
            <Flex
              className={`${Style.chat_message} ${
                isBank ? Style.bot : Style.user
              }`}
              justify={isBank ? "start" : "end"}
            >
              <Card className={Style.chat_bubble}>
                {Array.isArray(formattedMessage) ? (
                  formattedMessage
                ) : (
                  <span
                    dangerouslySetInnerHTML={{ __html: formattedMessage }}
                  />
                )}
              </Card>
            </Flex>
          </React.Fragment>
        );
      })}
    </Flex>
  );
}

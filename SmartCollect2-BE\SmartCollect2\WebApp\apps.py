# WebApp/apps.py - Enhanced Django app configuration with connection management
import os
import logging
import atexit
import signal
from django.apps import AppConfig
from django.conf import settings

logger = logging.getLogger(__name__)

class WebappConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "WebApp"
    _connections_registry = set()  # Track active connections
    
    def ready(self):
        """Called when Django starts up"""
        
        # Skip initialization in specific scenarios
        if self._should_skip_initialization():
            logger.info("Skipping database initialization (reloader/testing process)")
            return
        
        # Register cleanup handlers
        self._register_cleanup_handlers()
        
        # Import here to avoid circular imports
        from DB.db_manager import db_manager, is_django_reloader_process
        
        try:
            # Clean up any existing connections first
            self._cleanup_existing_connections()
            
            # Log process information
            pid = os.getpid()
            logger.info(f"Django app ready in process {pid}")
            
            # Initialize database engine
            if not is_django_reloader_process():
                db_manager.initialize_engine()
                logger.info("✅ Database engine initialized successfully during Django startup")
            else:
                logger.info("⏭️  Skipping database initialization in reloader process")
                
        except Exception as e:
            logger.error(f"❌ Failed to initialize database engine during Django startup: {e}")
            # Don't re-raise in production to allow Django to continue starting
            if not settings.DEBUG:
                logger.error("Continuing Django startup despite database initialization failure")
            else:
                raise
    
    def _should_skip_initialization(self):
        """Determine if we should skip database initialization"""
        
        # Skip in Django's auto-reloader process
        if os.environ.get('RUN_MAIN') != 'true':
            return True
        
        # Skip during migrations
        import sys
        if 'migrate' in sys.argv:
            return True
        
        # Skip during management commands that don't need DB
        management_commands_skip = [
            'collectstatic', 'makemigrations', 'check', 
            'compilemessages', 'createcachetable'
        ]
        
        if any(cmd in sys.argv for cmd in management_commands_skip):
            return True
        
        # Skip during testing (unless explicitly enabled)
        if 'test' in sys.argv and not os.environ.get('INIT_DB_FOR_TESTS'):
            return True
        
        return False
    
    def _register_cleanup_handlers(self):
        """Register cleanup handlers for graceful shutdown"""
        # Register atexit handler
        atexit.register(self._cleanup_on_exit)
        
        # Register signal handlers for graceful shutdown
        if hasattr(signal, 'SIGTERM'):
            signal.signal(signal.SIGTERM, self._signal_handler)
        if hasattr(signal, 'SIGINT'):
            signal.signal(signal.SIGINT, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle system signals for graceful shutdown"""
        logger.info(f"Received signal {signum}, cleaning up connections...")
        self._cleanup_existing_connections()
    
    def _cleanup_on_exit(self):
        """Cleanup function called on process exit"""
        logger.info("Process exiting, cleaning up connections...")
        self._cleanup_existing_connections()
    
    def _cleanup_existing_connections(self):
        """Clean up existing WebSocket connections and database connections"""
        try:
            # Close WebSocket connections
            self._close_websocket_connections()
            
            # Close database connections
            self._close_database_connections()
            
            logger.info("✅ Connections cleaned up successfully")
            
        except Exception as e:
            logger.error(f"❌ Error during connection cleanup: {e}")
    
    def _close_websocket_connections(self):
        """Close all active WebSocket connections"""
        try:
            from channels.layers import get_channel_layer
            from asgiref.sync import async_to_sync
            
            channel_layer = get_channel_layer()
            if channel_layer:
                # Send disconnect message to all groups
                async_to_sync(channel_layer.group_send)(
                    "websocket_connections",
                    {
                        "type": "disconnect_all",
                        "message": "Server restarting"
                    }
                )
                logger.info("WebSocket disconnect signal sent")
            
            # Clear connections registry
            self._connections_registry.clear()
            
        except Exception as e:
            logger.error(f"Error closing WebSocket connections: {e}")
    
    def _close_database_connections(self):
        """Close database connections"""
        try:
            from django.db import connections
            
            # Close all database connections
            for conn in connections.all():
                conn.close()
            
            # Also close custom DB manager connections if available
            try:
                from DB.db_manager import db_manager
                if hasattr(db_manager, 'close_connections'):
                    db_manager.close_connections()
                elif hasattr(db_manager, 'engine') and db_manager.engine:
                    db_manager.engine.dispose()
            except ImportError:
                pass
            
            logger.info("Database connections closed")
            
        except Exception as e:
            logger.error(f"Error closing database connections: {e}")
    
    @classmethod
    def register_connection(cls, connection):
        """Register a new connection for tracking"""
        cls._connections_registry.add(connection)
    
    @classmethod
    def unregister_connection(cls, connection):
        """Unregister a connection"""
        cls._connections_registry.discard(connection)

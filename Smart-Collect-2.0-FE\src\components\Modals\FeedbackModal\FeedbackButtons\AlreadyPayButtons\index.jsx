import { Button, Flex, message } from 'antd'
import React, { useState } from 'react'
import Style from "./_already-button.module.scss";
import {AXIOS} from "../../../../../apis/ho-Instance";
import PropTypes from 'prop-types';

export default function AlreadyPayButtons({loanmstid}) {
  const [isMarkDoneLoading, setIsMarkDoneLoading] = useState(false);
  const [isMarkFalseLoading, setIsMarkFalseLoading] = useState(false);

  const handleMarkPaymentDone = async()=>{
    setIsMarkDoneLoading(true);
    try {
        const res = await AXIOS.post('alreadypaidfeedback/',{
          Loanmstid:loanmstid,
          status: "Fulfilled"
        });
        if(res.status ===200){
          message.success('Payment marked as done!');
          window.location.reload();
        }
    } catch (error) {
        console.log("Error in mark as payment", error);
    }finally{
      setIsMarkDoneLoading(false);
    }
  };
  
  const handleMarkAsFalseClaim = async ()=>{
      setIsMarkFalseLoading(true);
      try {
          const res = await AXIOS.post('alreadypaidfeedback/',{
            Loanmstid:loanmstid,
            status: "Terminated"
          });
          if(res.status ===200){
            message.success('Payment marked as false!');
            window.location.reload();
          }
      } catch (error) {
          console.log("Error in mark as false", error);
      }finally{
        setIsMarkFalseLoading(false);
      }
  };

  return (
    <Flex justify='space-between' align='center' gap={20} className={Style.button_container}>
        <Button loading={isMarkDoneLoading} onClick={handleMarkPaymentDone} >Mark Payment as done</Button>
        <Button loading={isMarkFalseLoading} onClick={handleMarkAsFalseClaim}>Mark as a false claim</Button>
    </Flex>
  )
}

AlreadyPayButtons.propTypes={
  loanmstid: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
}
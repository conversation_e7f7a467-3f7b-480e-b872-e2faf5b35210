import React, { useEffect, useState } from 'react';
import {Col, Flex, message, Row, Typography} from "antd";
import AppHeader from "../AppHeader";
import STATE_IMG from "../../assets/Images/state.svg";
import REGION_IMG from "../../assets/Images/region.svg";
import BRANCH_IMG from "../../assets/Images/branch.svg";
import VILLAGE_IMG from "../../assets/Images/village.svg";
import ACTIVATE_IMG from "../../assets/Images/activate.svg";
import ACTIVE_DISBURSED_IMG from "../../assets/Images/active-disbursed.png";
import ACTIVE_OVERDUE_IMG from "../../assets/Images/active-overdue.png";
import ACTIVE_OVERDUE_POS_IMG from "../../assets/Images/active-overdue-pos.png";
import COLLECTION_OFFICER_IMG from "../../assets/Images/collection-officer.png";
import DOWNLOAD_IMG from "../../assets/Images/download.svg";
import { Card } from './Card';
import { ProductTable } from './ProductTable';
import { ActiveCard } from './ActiveCard';
import { formatAmount, formatDigits, handleDownloadTable } from '../../constant';
import { SwitchComponent } from '../SwitchComponent';
import { ReusableTable } from './ReusableTable';
import { PortfolioChart } from './Chart';
import { AXIOS } from  "../../apis/ho-Instance";
import Style from "./_style.module.scss";
import ApplicationLoader from '../ApplicationLoader';
import { TotalDpdTable } from './TotalDpdTable';

const {Text}  = Typography;

export default function Portfolio() {
  const [isBranchSwitchOn, setIsBranchSwitchOn] = useState(false);
  const [isRegionSwitchOn, setIsRegionSwitchOn] = useState(false);
  const [isStateSwitchOn, setIsStateSwitchOn] = useState(false);
  const [isTableSwitchOn, setIsTableSwitchOn] = useState(false);
  const [portfolioCard, setPortfolioCard] = useState({});
  const [activePortfolioDetails, setActivePortfolioDetails] = useState({});
  const [activePortfolioType, setActivePortfolioType] = useState("good");
  const [activePortfolioTableData, setActivePortfolioTableData] = useState([]);
  const [activePortfolioTablePagination, setActivePortfolioTablePagination]= useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [customerDetailsTableData, setCustomerDetailsTableData] = useState([]);
  const [customerFilterParams, setCustomerFilterParams] = useState({
    stateId: "all",
    regionId: "all",
    branchId: "all",
    tablewise: "State",
    range: "0",
  });
  const [customerTablePagination, setCustomerTablePagination] = useState({ current: 1, pageSize: 10, total: 0 });

  const [isLoading, setIsLoading] = useState({
    state: false,
    region: false,
    branch: false,
    activePortfolio:false,
    customerLoading:false
  });
  const [tableData, setTableData] = useState({
    state: [],
    region: [],
    branch: [],
  });
  const [pagination, setPagination] = useState({
    state: { current: 1, pageSize: 10, total: 0 },
    region: { current: 1, pageSize: 10, total: 0 },
    branch: { current: 1, pageSize: 10, total: 0 },
  });
  const userDetails = JSON.parse(localStorage.getItem("user"));

  // Handle get format active portfolio type
  const getActivePortfolioType = () =>{
    if(activePortfolioType === "good") return "Good";
    else if(activePortfolioType === "arrear") return "Arrear";
    else if(activePortfolioType === "npa") return "NPA";
    else if(activePortfolioType === "npaplus") return "NPA+";
  }

  // Handle transform active portfolio data
  const handleTransformActivePortfolioData = ({rawData, type}) => {
    const portfolio = rawData?.data?.[type];
    if (!portfolio) return [];
  
    const keys = Object.keys(portfolio);
    if (keys.length === 0) return [];
  
    const rowCount = portfolio[keys[0]].length;
  
    const result = [];
  
    for (let i = 0; i < rowCount; i++) {
      const row = {};
      for (const key of keys) {
        row[key] = portfolio[key][i];
      }
      result.push(row);
    }
  
    return result;
  };

  // Handle portfolio card details
  const handleFetchPortfolioCardDetails = async() => {
    try {
      const res = await AXIOS.post('portfolio_cards/',{
        State_id: "all",  
        Region_id: "all",    
        Branch_id: "all",          
        CollectionOfficerID: "all",  
        LoanType: "all",
        BankMstID : userDetails?.BankMstID
    });
    if(res.status === 200){
      setPortfolioCard(res.data.portfolio_cards[0]);
    }
    } catch (error) {
      console.log("Error in Portfolio", error);
    }
  }
  
  // Handle active portfolio details
  const handleActivePortfolioDetails = async(page = 1)=>{
    setIsLoading((prev) => ({ ...prev, activePortfolio: true }));
    try {
      const res = await AXIOS.post('portfolio_dpdwise_cards/',{
        State_id: "all",  
        Region_id: "all",    
        Branch_id: "all",          
        CollectionOfficerID: "all",  
        LoanType: "all",
        BankMstID : userDetails.BankMstID,
        search_term : "",
        page_limit : 10,
        offset : page
      });
      if(res.status === 200){
        setActivePortfolioDetails(res.data);
        
        // Update pagination
        setActivePortfolioTablePagination({
          current: res.data.current_page,
          pageSize: res.data.page_size,
          total: res.data.total_records
        });
      }
    } catch (error) {
      console.log("Error in Active portfolio", error);
    }finally{
      setIsLoading((prev) => ({ ...prev, activePortfolio: false }));
    }
  }

  // Handle active portfolio pagination
  const handleActivePortfolioPagination = async (page = 1)=>{
    await handleActivePortfolioDetails(page);
  }
  
  // Handle Show active portfolio data 
  const handleSetActivePortfolioDetails = (type="good")=>{
    setActivePortfolioType(type)
  }

  // Handle state, regions, branches
  const handleFetchData = async ({tableType, page = 1}) => {
    setIsLoading((prev) => ({ ...prev, [tableType]: true }));
    const groupby_columns = {
      state: ["State", "State_id"],
      region: ["Region", "Region_id"],
      branch: ["Branch", "Branch_id"],
    };

    try {
      const response = await AXIOS.post("portfolio_other_tables/", {
        State_id: "all",
        Region_id: "all",
        Branch_id: "all",
        CollectionOfficerID: "all",
        LoanType: "all",
        BankMstID: userDetails?.BankMstID,
        search_term: "",
        page_limit:  pagination[tableType].pageSize,
        offset: page,
        groupby_columns: groupby_columns[tableType]
      });
  
      if (response.status === 200 && response.data.data) {
        setTableData((prev) => ({ ...prev, [tableType]: response.data.data }));
        setPagination((prev) => ({
          ...prev,
          [tableType]: {
            ...prev[tableType],
            current: response.data.current_page,
            total: response.data.total_records,
          },
        }));
      }
    } catch (error) {
      console.error(`Error fetching ${groupBy[0]} data`, error);
    } finally {
      setIsLoading((prev) => ({ ...prev, [tableType]: false }));
    }
  };
  
  // Handle pagination
  const handlePageChange = async ({tableType, page}) => {
    await handleFetchData({tableType, page});
  }

  // Handle download functionality
  const handleDownload = async ({tableType, data}) => {
    if (data.length === 0 || !data) {
    return  message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: `${tableType}-data`,
        worksheetName: `${tableType}-sheet`,
        tableData: data,
      });
    }
  };

  // Handle customer details
  const handleFetchCustomerDetails = async({
    stateId ="all",
    regionId="all", 
    branchId="all", 
    tablewise="State",
    range="0",
    page=1,
  })=>{
    setIsLoading((prev) => ({ ...prev, customerLoading: true }));
    // Save current filters (except page)
    setCustomerFilterParams({ stateId, regionId, branchId, tablewise, range });
    try {
      const res = await AXIOS.post('dpd_snapshot_customers/',{
        State_id: stateId,  
        Region_id: regionId,    
        Branch_id: branchId,          
        CollectionOfficerID: "all",  
        LoanType: "all",
        BankMstID : 4,
        tablewise: tablewise,
        dpd_range: range,
        page_limit: 10,
        offset: page,
        page_name:"portfolio"
      })
      if(res.status === 200 && res.data?.data.length>0){
        setCustomerDetailsTableData(res.data?.data);
        setCustomerTablePagination((prev) => ({
          ...prev,
            current: res.data.current_page,
            total: res.data.total_records,
            pageSize: res.data.page_size
        }));
        }
        else{
          message.error("No customers found!");
          setCustomerDetailsTableData([])
        }
      } catch (error) {
        console.log("Error in customer details", error?.message);
      }finally{
        setIsLoading((prev) => ({ ...prev, customerLoading: true }));
      }
    }
  
  // Handle Customer table pagination
  const handleCustomerTablePagination = async({page =1}) =>{
    await handleFetchCustomerDetails({
      ...customerFilterParams,
      page,
    });
  }
  

  // handle active portfolio
  useEffect(()=>{
    const tableData = handleTransformActivePortfolioData({rawData:activePortfolioDetails, type:activePortfolioType});
    setActivePortfolioTableData(tableData);
  },[activePortfolioType,activePortfolioDetails]);

  // Usage:  
  useEffect(()=>{
    handleFetchPortfolioCardDetails();
    handleActivePortfolioDetails();
    handleFetchData({tableType:"state"});
    handleFetchData({tableType:"region"});
    handleFetchData({tableType:"branch"});
  },[]);

  return (
    <Flex vertical gap={20}>
      <AppHeader title={"Portfolio"}/>

      <Flex vertical gap={50} style={{width:"99.9%"}}>
         <Flex 
         vertical 
         justify='space-evenly' 
         gap={40} 
         className={Style.portfolio}>
            <Row gutter={[60,20]} justify={"end"}>
              <Col sm={6} xs={24}>
                <Card 
                  title={"State"} 
                  value={formatDigits(portfolioCard?.states || 0)} 
                  imgSrc={STATE_IMG}/>
              </Col>
              <Col sm={6} xs={24}>
                <Card 
                  title={"Region"}
                  value={formatDigits(portfolioCard?.regions|| 0)} 
                  imgSrc={REGION_IMG}/>
              </Col>
              <Col sm={6} xs={24}>
                <Card 
                  title={"Branch"} 
                  value={formatDigits(portfolioCard?.branches || 0)} 
                  imgSrc={BRANCH_IMG}/>
              </Col>
              <Col sm={6} xs={24}>
                <Card 
                  title={"Collection Officers"} 
                  value={formatDigits(portfolioCard?.collectionofficers|| 0)} 
                  imgSrc={COLLECTION_OFFICER_IMG}/>
              </Col>
              
              {/* <Col sm={6} xs={24}>
                <Card 
                  title={"Village"} 
                  value={0} 
                  imgSrc={VILLAGE_IMG}/>
              </Col> */}
              {/* <Col sm={6} xs={24}>
                <Card 
                  title={"Group"} 
                  value={formatDigits(portfolioCard?.groupnames)} 
                  imgSrc={GROUP_IMG}/>
              </Col> */}
              <Col sm={6} xs={24}>
                <Card 
                  title={"Activate Account"} 
                  value={formatDigits(portfolioCard?.activeaccounts|| 0)} 
                  imgSrc={ACTIVATE_IMG}/>
              </Col>
              <Col sm={6} xs={24}>
                <Card 
                  title={"Active Disbursed"} 
                  value={`₹ ${formatAmount(portfolioCard?.activeamountdisbursed|| 0)}`} 
                  imgSrc={ACTIVE_DISBURSED_IMG}/>
              </Col>
              <Col sm={6} xs={24}>
                <Card 
                  title={"Active Overdue"} 
                  value={`₹ ${formatAmount(portfolioCard?.activeamountoverdue || 0)}`} 
                  imgSrc={ACTIVE_OVERDUE_IMG}/>
              </Col>
              <Col sm={6} xs={24}>
                <Card 
                  title={"Active Overdue POS"} 
                  value={`₹ ${formatAmount(portfolioCard?.activeamountpos || 0)}`} 
                  imgSrc={ACTIVE_OVERDUE_POS_IMG}/>
              </Col>
              {/* <Col sm={6} xs={24}>
                <Card 
                  title={"Total Counts"} 
                  value={formatDigits(portfolioCard?.totalaccounts|| 0)} 
                  imgSrc={TOTAL_COUNTS_IMG}/>
              </Col> */}
            </Row>
         </Flex>

          {/* Active Portfolio */}
          <Flex vertical gap={10} >
              <Text style={{fontFamily:"Kanit", fontSize:"1rem"}}>Active Portfolio</Text>
              <Flex 
              vertical 
              justify='space-evenly' 
              gap={40}
              className={Style.active_portfolio}>
                {/* cards  */}
                <Row gutter={[60,20]}>
                  <Col 
                    sm={6} 
                    xs={24} 
                    style={{cursor:"pointer"}} 
                    onClick={handleSetActivePortfolioDetails.bind(null, "good")}>
                    <ActiveCard textColor={"#0E8321"} title={"Good"} value={formatDigits(activePortfolioDetails?.cards_data?.good || 0)} imgSrc={STATE_IMG}/>
                  </Col>
                  <Col 
                    sm={6} 
                    xs={24} 
                    style={{cursor:"pointer"}} 
                    onClick={handleSetActivePortfolioDetails.bind(null, "arrear")}>
                    <ActiveCard textColor={"#CC6D07"} title={"Arrear"} value={formatDigits(activePortfolioDetails?.cards_data?.arrear || 0)} imgSrc={REGION_IMG}/>
                  </Col>
                  <Col 
                    sm={6} 
                    xs={24} 
                    style={{cursor:"pointer"}} 
                    onClick={handleSetActivePortfolioDetails.bind(null, "npa")}>
                    <ActiveCard textColor={"#149B9E"} title={"NPA"} value={formatDigits(activePortfolioDetails?.cards_data?.npa || 0)} imgSrc={BRANCH_IMG}/>
                  </Col>
                  <Col 
                    sm={6} 
                    xs={24} 
                    style={{cursor:"pointer"}} 
                    onClick={handleSetActivePortfolioDetails.bind(null, "npaplus")}>
                    <ActiveCard textColor={"#08369C"} title={"NPA+"} value={formatDigits(activePortfolioDetails?.cards_data?.npaplus || 0)} imgSrc={VILLAGE_IMG}/>
                  </Col>
                </Row>

                {/* Table */}
                {isLoading.activePortfolio
                 ?<ApplicationLoader/>
                 :<>
                  <Text style={{fontFamily:"Kanit", fontSize:"1rem", margin:0}}>{getActivePortfolioType()} Data</Text>
                  <ProductTable 
                    data={activePortfolioTableData} 
                    activePortfolioType={activePortfolioType}
                    pagination={activePortfolioTablePagination}
                    onPaginationChange={handleActivePortfolioPagination}
                  />
                 </>}
              </Flex>
          </Flex>

          {/* Graph */}
          <Flex vertical gap={20}>
            <Flex justify='space-between' align='center'>
              <Text style={{fontFamily:"Kanit", fontSize:"1rem"}}>State Level Graph</Text>
              <Flex gap={20}> 
                <SwitchComponent
                  offText={"Table"}
                  onText={"Graph"}
                  onColor={"#0F2050"}
                  offColor={"#E4F8F9"}
                  checked={isTableSwitchOn}
                  onToggle={() => setIsTableSwitchOn(prev => !prev)}
                  />
                <SwitchComponent
                  isSnapshot={false}
                  offText={"Outstanding"}
                  onText={"Accounts"}
                  onColor={"#0F2050"}
                  offColor={"#E4F8F9"}
                  checked={isStateSwitchOn}
                  onToggle={() => setIsStateSwitchOn(prev => !prev)}
                /> 
                <Flex justify='end'>
                  <button className={Style.download_button} onClick={handleDownload.bind(null,{tableType:"State", data:tableData.state})}>
                    <img 
                    src={DOWNLOAD_IMG} 
                    alt='download-button'
                    />
                  </button>
                </Flex>
              </Flex>
             
            </Flex>
           
            {isTableSwitchOn
            ?<PortfolioChart  data={tableData.state}/>
            :<ReusableTable 
                isLoading={isLoading.state}
                table={"state"}
                isSwitch={isStateSwitchOn}
                data={tableData.state}
                pagination={pagination.state}
                handleFetchCustomerDetails={handleFetchCustomerDetails}
                onPaginationChange={(page) => handlePageChange({tableType:"state", page})}
             />}
          </Flex>

          {/* Region Details */}
          <Flex vertical gap={20}>
             {/* Details */}
             <Flex justify='space-between'>
               <Text style={{fontFamily:"Kanit", fontSize:"1rem"}}>
               Regions:
               </Text>
               <Flex gap={20}>
                  <SwitchComponent
                    isSnapshot={true}
                    offText={"Outstanding"}
                    onText={"Accounts"}
                    onColor={"#0F2050"}
                    offColor={"#E4F8F9"}
                    checked={isRegionSwitchOn}
                    onToggle={() => setIsRegionSwitchOn(!isRegionSwitchOn)}
                  />

                  <Flex justify='end'>
                    <button className={Style.download_button} onClick={handleDownload.bind(null,{tableType:"Region", data:tableData.region})}>
                      <img 
                      src={DOWNLOAD_IMG} 
                      alt='download-button'
                      />
                    </button>
                  </Flex>

               </Flex>
             </Flex>

             {/* Table */}
             <ReusableTable 
             isLoading={isLoading.region}
             table={"region"}
             data={tableData.region}
             isSwitch={isRegionSwitchOn}
             pagination={pagination.region}
             handleFetchCustomerDetails={handleFetchCustomerDetails}
             onPaginationChange={(page) => handlePageChange({tableType:"region", page})}/>
         </Flex>


          {/* Branch Details */}
          <Flex vertical gap={20}>
             {/* Details */}
             <Flex justify='space-between'>
               <Text style={{fontFamily:"Kanit", fontSize:"1rem"}}>
               Branches:
               </Text>
               <Flex gap={20}>
                  <SwitchComponent
                    isSnapshot={true}
                    offText={"Outstanding"}
                    onText={"Accounts"}
                    onColor={"#0F2050"}
                    offColor={"#E4F8F9"}
                    checked={isBranchSwitchOn}
                    onToggle={() => setIsBranchSwitchOn(!isBranchSwitchOn)}
                  />

                  <Flex justify='end'>
                    <button className={Style.download_button} onClick={handleDownload.bind(null,{tableType:"Branch", data:tableData.branch})}>
                      <img 
                      src={DOWNLOAD_IMG} 
                      alt='download-button'
                     />
                    </button>
                  </Flex>

               </Flex>
             </Flex>

             {/* Table */}
             <ReusableTable 
              table={"branch"}
              data={tableData.branch}
              isSwitch={isBranchSwitchOn}
              isLoading={isLoading.branch}
              pagination={pagination.branch}
              handleFetchCustomerDetails={handleFetchCustomerDetails}
              onPaginationChange={(page) => handlePageChange({tableType:"branch", page})}/>
         </Flex>

          {/* Customer Details */}
       {customerDetailsTableData?.length>0 
        ?<Flex vertical gap={20}>
             {/* Details */}
             <Flex justify='space-between'>
               <Text style={{fontFamily:"Kanit", fontSize:"1rem"}}>
                 Customer Details
               </Text>
               <Flex gap={20}>
                  {/* <SwitchComponent
                    isSnapshot={true}
                    offText={"Outstanding"}
                    onText={"Accounts"}
                    onColor={"#0F2050"}
                    offColor={"#E4F8F9"}
                    checked={true}
                    onToggle={() => setIsDpdSwitchOn(!isDpdSwitchOn)}
                  /> */}

                  <Flex justify='end'>
                    <div className={Style.download_button}>
                      <img src={DOWNLOAD_IMG} alt='download-button'/>
                    </div>
                  </Flex>

               </Flex>
             </Flex>

             {/* Table */}
             <TotalDpdTable 
             data={customerDetailsTableData} 
             isLoading={isLoading.customerLoading} 
             pagination={customerTablePagination}
             onPaginationChange={handleCustomerTablePagination}/>
        </Flex>
        :null
      }
      </Flex>
    </Flex>
  )
}

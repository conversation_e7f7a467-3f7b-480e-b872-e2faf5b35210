@import url("../../../index.css");

$light-gray:#D9D9D9;
$tab-height: 7px;

.view_modal{
    .icon_img{
        width: 13px;
        height: 20px;
        img{
            width: 100%;
            object-fit: contain;
        }
    }
    .text{
        color: var(--dark-blue);
        font-family: '<PERSON><PERSON><PERSON>','Courier New', Courier, monospace;
        white-space: nowrap;
    }

    :global(.ant-modal-close){
        right: 4px;
        top: 10px;  
        :global(.ant-modal-close-x){
          font-size:12px;
        }
    }  

    :global(.ant-modal-header){
        background: var(--dark-blue);
        padding: 0.5rem 0px;
        text-align: center;
    }

    :global(.ant-modal-content){
       background-color: transparent;
       box-shadow: none;
    }
}

// Responsive view
@media screen and (max-width:768px) {
    .view_modal{
        .icon_img{
            width: 10px;
            height: 24px;
        }
        .modal_title{
            font-size: 12px;
        }
      :global(.ant-modal-content){
        padding: 4px;

        :global(.ant-modal-body){  
            padding: 0.3rem;
        }
      }
    }
}
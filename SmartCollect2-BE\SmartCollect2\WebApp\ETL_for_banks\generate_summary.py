from datetime import datetime,date
import pandas as pd
import numpy as np

def convert_for_json_serialization(df):
    """
    Convert DataFrame to JSON-serializable format by handling dates and NaN values
    
    Args:
        df (pandas.DataFrame): DataFrame to convert
        
    Returns:
        list: List of dictionaries ready for JSON serialization
    """
    # Create a copy to avoid modifying original DataFrame
    df_copy = df.copy()
    
    # Convert date/datetime columns to string format
    for col in df_copy.columns:
        if df_copy[col].dtype == 'datetime64[ns]' or df_copy[col].dtype == '<M8[ns]':
            df_copy[col] = df_copy[col].dt.strftime('%Y-%m-%d')
        elif isinstance(df_copy[col].iloc[0] if len(df_copy) > 0 else None, (date, datetime)):
            df_copy[col] = df_copy[col].apply(lambda x: x.strftime('%Y-%m-%d') if pd.notnull(x) else None)
    
    # Replace NaN values with None (which converts to null in JSON)
    df_copy = df_copy.replace({np.nan: None})
    
    # Convert to list of dictionaries
    return df_copy.to_dict('records')

def generate_summary_response(result_data, file_type,bank_id):
    """Generate formatted summary response returning the same data from processing functions"""
    
    # Convert summary DataFrame to dict for JSON serialization with proper handling
    summary_df = result_data.get('summary_df', pd.DataFrame())
    sample_summary_actions = convert_for_json_serialization(summary_df.head()) if not summary_df.empty else []
    
    # Get sample data based on file type with exact columns from paste
    
    # Base response structure
    response_data = {
        'message': 'File processed successfully',
        'status': 'success',
        'db_operation_success': result_data.get('db_operation_success', True),
        'db_error_message': result_data.get('db_error_message', None),
    }
    
    # Add specific data based on file type - return the same data from processing functions
    if bank_id == 13:

        if file_type == 'combined':
            response_data.update({
                'total_records': result_data.get('total records', 0),
                "message":result_data.get("message"),
                'New Records': result_data.get("New Records"),
                "Duplicates in DB": result_data.get("Duplicates in DB"),

            })
            
        elif file_type in ['demand', '2pm demand']:
            response_data.update({
                'total_records': result_data.get('total_records', 0),
                "New Records to Insert": result_data.get('insert_records'),
                "Total Records to be Updated": result_data.get("update_records"),
                "No Change in Records":result_data.get("no_change"),
                "message":result_data.get("message"),
            })
    elif bank_id in (21,36,28,378):
        response_data.update({
            "Total records created":result_data.get('Total records processed'),
            "Records with guarantors": result_data.get("Records with guarantors"),
            "Records without guarantors":result_data.get("Records without guarantors"),
            "New Records Found":result_data.get("New records inserted"),
            "Duplicate records found":result_data.get("Duplicate records found"),
            "message":result_data.get("message")
        })
    elif bank_id in (23,27,29,30,32,34,35,37,369):
        response_data.update({
            "Total records processed":result_data.get('Total records processed'),
            "New records inserted": result_data.get( "New records inserted"),
            "Duplicate records found":result_data.get("Duplicate records found"),
            "message":result_data.get("message")
        })
    return response_data

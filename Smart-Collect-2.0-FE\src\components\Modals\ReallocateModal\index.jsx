import { Modal, Flex, Typo<PERSON>, Select, DatePicker, But<PERSON>, message } from 'antd'
import Style from "./_style.module.scss";
import { useEffect, useState } from 'react';
import { AXIOS } from '../../../apis/ho-Instance';
import dayjs from 'dayjs';
import PropTypes from 'prop-types';

const {Title, Text} = Typography;

export function ReallocateModal({ modalStatus, handleCancel, handleOK, customerData, loading}) {
  const userDetails = JSON.parse(localStorage.getItem("user"));
  const [actionType, setActionType] = useState("temporary");
  const [selectedCollectOfficer, setSelectedCollectOfficer] = useState(null);
  const [collectOfficerOptions, setCollectOfficerOptions] = useState([]);
  const [rangeKey, setRangeKey] = useState({
    fromDate: null,
    toDate: null
  }); 
  const isBulk = Array.isArray(customerData);

  const handleModalClose = () => {
    setActionType("temporary");
    setSelectedCollectOfficer(null);
    setRangeKey({ fromDate: null, toDate: null });
    setCollectOfficerOptions([]);
    handleCancel();
  }

  // Function to handle the change in date range
  const handleRangeChange = ({type, key}) => {
    setRangeKey((prev) => ({
      ...prev,
      [type]: key
    }));
  }

  // Function to handle the OK action
  const handleAdd = async () => {
    if (!selectedCollectOfficer) {
      message.error("Please select a new Collection Officer.");
      return;
    }

    if (actionType === "temporary") {
      const { fromDate, toDate } = rangeKey;
      if (!fromDate || !toDate) {
        message.error("Please select both From and To dates for a temporary action.");
        return;
      }
    }

    await handleOK({ selectedCollectOfficer, rangeKey, actionType, customerData});

    // Reset the state after adding
    setActionType("temporary");
    setSelectedCollectOfficer(null);
    setRangeKey({ fromDate: null, toDate: null });
  }

  // Fetching options for the Select component
  const handleFetchCollectOfficerOptions = async () => {
    try {
      const response = await AXIOS.get('officer_details/',{
        params:{
          current_officer_id: isBulk ? Number(customerData[0].CollectionOfficerID) : Number(customerData.CollectionOfficerID),
          Designation: userDetails?.designation,
          bankmstid: userDetails?.BankMstID,
        }
      });      
      if(response.status === 200 && response.data.officers?.length > 0) {
        const updatedOptions = response.data.officers.map(officer => ({
          label: officer.CollectionOfficerName,
          value: officer.CollectionOfficerID
        }));
        setCollectOfficerOptions(updatedOptions);
     }
    } catch (error) {
      console.error("Error fetching collection officers:", error);
      return [];
    }
  }

  useEffect(() => { 
    if (customerData) handleFetchCollectOfficerOptions();
  }, [customerData]);
 return (
  <Modal 
    centered
    className={Style.dialer_modal} 
    title={
      <Flex align='center' gap={5} justify='center' >
        <Title level={4} className={Style.feedback_title}>{isBulk ? "Bulk Reallocation" : "Reallocate"}</Title>
      </Flex>
    }
    open={modalStatus} 
    onOk={handleAdd} 
    onCancel={handleModalClose}
    footer={null}
    >
      <Flex vertical gap={20}> 
         {/*Action Type  */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Choose Action Type:</Text>
          <Select 
            value={actionType}
            onChange={(value) => setActionType(value)}
            className={Style.custom_select}
           options={[
            {
              label: "Temporary",
              value: "temporary"
            },
            {
              label: "Permanent",
              value: "permanent"
            }
          ]}/>
        </Flex> 

         {/*Collected Officers  */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Select new collector officer:</Text>
          <Select 
            showSearch
            value={selectedCollectOfficer}
            onChange={(value) => {
              const selected = collectOfficerOptions.find(opt => opt.value === value);
              setSelectedCollectOfficer(selected);
            }}
            className={Style.custom_select}
            placeholder={"Collector Officer"}
            options={collectOfficerOptions}
            optionFilterProp="label" 
          />
        </Flex>

         {/* Date Range  */}
        {actionType === "temporary" && 
          <Flex justify="space-between" align="center">
            <Text className={Style.text}>Select date range:</Text>

            <Flex gap={10} style={{width: "60%"}}>
              <DatePicker 
                className={Style.date_picker}
                onChange={(date)=> handleRangeChange({type:"fromDate", key:date})}
                value={rangeKey.fromDate}
                format="DD-MM-YYYY"
                placeholder="From date"
                disabledDate={(current) =>
                  current && current < dayjs().startOf("day")
                }
              />
              <DatePicker 
                className={Style.date_picker}
                onChange={(date)=> handleRangeChange({type:"toDate", key:date})}
                value={rangeKey.toDate}
                format="DD-MM-YYYY"
                placeholder="To date"
                disabledDate={(current) =>
                  current && current < dayjs(rangeKey.fromDate).startOf("day")
                }
              />
            </Flex>
          </Flex>
        }

        {/* Add */}
        <Flex justify='center' align='center'>
          <Button onClick={handleAdd} loading={loading} className={Style.add_btn}>Add</Button>
        </Flex>
      </Flex>
  </Modal>
  )
}

ReallocateModal.propTypes={
  modalStatus: PropTypes.bool, 
  handleCancel: PropTypes.func, 
  handleOK: PropTypes.func, 
  customerData: PropTypes.oneOfType([PropTypes.array, PropTypes.object]), 
  loading: PropTypes.bool
}
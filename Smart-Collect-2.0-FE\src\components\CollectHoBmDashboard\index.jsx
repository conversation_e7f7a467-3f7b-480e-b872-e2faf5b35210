import { Col, Flex, Row, Tooltip, Tour, Typography, message } from "antd";
import React, { useEffect, useState } from "react";
import AppHeader from "../AppHeader";
import CustomerDetails from "./CustomerDetails";
import { CustomerEngagementChart } from "./CustomerEngagementChart";
import { PayDetails } from "./PayDetails";
import { DateCarousel } from "./DateCarousel";
import ApplicationLoader from "../ApplicationLoader";
import { AXIOS } from "../../apis/ho-Instance";
import dayjs from "dayjs";
import { EngagementDetails } from "./EngagementDetails";
import Style from "./_style.module.scss";
import { useTour } from "../../ContextApis/TourProvider";
import TabsComponent from "../TabsComponent";
import CloseIcon from "../../assets/Images/cross-white.svg";
import {
  ALL_CACHE_NAMES,
  LOCAL_KEYS,
  SMART_COLLECT_MENU_IDS,
} from "../../constant";
import PropTypes from "prop-types";
import ServerCrash from "../ServerCrash";
import {
  getCacheKey,
  getFromCache,
  getPostCacheKey,
  storeToCache,
} from "../../utils/cacheHelper";
import { encryptBody, isSameEncryptedBody } from "../../utils/crypto";

const { Text } = Typography;

const CACHE_NAME = ALL_CACHE_NAMES.DASHBOARD_CACHE;
export default function CollectHoBmDashboard({
  role = "HO",
  title = "Dashboard",
}) {
  const [engagementData, setEngagementData] = useState([]);
  const [dashboardData, setDashboardData] = useState({});
  const [nonContactable, setNonContactable] = useState([]);
  const [loading, setLoading] = useState(false);
  const [serverDown, setServerDown] = useState(false);
  const [selectedDate, setSelectedDate] = useState({
    fromDate: null,
    toDate: null,
  });
  const storedFilter = localStorage.getItem("filterDate");
  const storedDpd = localStorage.getItem("DashboardDpdCategory");
  const { open, setOpen, steps, currentStep, setCurrentStep } = useTour();
  const [dpdCategory, setDpdCategory] = useState([]);
  const [currentDpd, setCurrentDpd] = useState(null);
  const [selectedDpd, setSelectedDpd] = useState(() => {
    return storedDpd ? JSON.parse(storedDpd) : { start: -1, end: -1 };
  });

  // Handle dpd category tab
  const handleTabChange = (key) => {
    setCurrentDpd(key);
    const selectedDpd = dpdCategory.find((cat) => cat.name === key);
    if (selectedDpd) {
      setSelectedDpd(selectedDpd?.range);
      localStorage.setItem(
        "DashboardDpdCategory",
        JSON.stringify(selectedDpd?.range)
      );
    }
  };

  // Apply dashboard data
  function applyDashboardData(data) {
    const dashboardData = Array.isArray(data.dashboard_data)
      ? data.dashboard_data[0]
      : data.dashboard_data;

    setDashboardData(dashboardData);
    setDpdCategory(dashboardData?.dpd_category_labels);
    setNonContactable(data.NonContactable || []);

    const engagementData = Array.isArray(data?.engagement_data)
      ? data.engagement_data
      : [];

    const filledData = fillMissingDates(engagementData);
    setEngagementData(filledData);

    if (filledData.length) {
      const formattedDate = dayjs(filledData[0].CreatedDate).format(
        "YYYY-MM-DD"
      );
      localStorage.setItem(
        "EngagementStartDate",
        JSON.stringify(formattedDate)
      );
    }
  }

  // Get dashboard data
  async function handleGetDashboardData() {
    setLoading(true);
    const cacheKey = getCacheKey({ endpoint: "dashboard/" });
    // Try fetching from cache first
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });

    if (cachedData) {
      applyDashboardData(cachedData);
      setLoading(false);
    }
    try {
      const response = await AXIOS.get("dashboard/");
      // Check the response
      if (response.status === 200) {
        // Store fresh data to cache
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
        applyDashboardData(response.data);
        setServerDown(false);
      }
    } catch (error) {
      console.log("Error in HO dashboard", error);
      setServerDown(true);
    } finally {
      setLoading(false);
    }
  }

  // Post the dashboard data & set
  const handleDashboardFilteredData = async ({
    fromDate,
    toDate,
    BranchName,
    dpdRange,
  }) => {
    setLoading(true);
    const body = {
      from_date: fromDate ?? "",
      to_date: toDate ?? "",
      branchls: BranchName ?? [],
      dpdfrom: dpdRange?.start ?? -1,
      dpdto: dpdRange?.end ?? -1,
    };
    // // Encrypt the body for caching
    const cacheKey = getPostCacheKey({
      endpoint: "dashboard/",
      body: body ?? {},
    });
    const encryptedOldBody = localStorage.getItem(
      LOCAL_KEYS.HO_DASHBOARD_FILTER
    );
    const sameBody =
      encryptedOldBody &&
      isSameEncryptedBody({ newBody: body, encryptedOldBody });

    if (sameBody) {
      const cachedData = await getFromCache({
        cacheName: CACHE_NAME,
        key: cacheKey,
      });
      if (cachedData) {
        applyDashboardData(cachedData);
        setLoading(false);
      }
    }
    try {
      const response = await AXIOS.post("dashboard/", body);
      // Check the response
      if (response.status === 200) {
        // Apply the dashboard data
        applyDashboardData(response.data);
        setServerDown(false);
        const encrypted = encryptBody(body);
        if (encrypted) {
          localStorage.setItem(LOCAL_KEYS.HO_DASHBOARD_FILTER, encrypted);
        }
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
      }
    } catch (error) {
      console.error("Error:", error);
      setServerDown(true);
    } finally {
      setLoading(false);
    }
  };

  // Handle filter out the dashboard data based on the selected dates
  const handleDisplaySelectedData = async ({ date, value }) => {
    const formattedDates = {
      fromDate: new Date(date).toISOString(),
      toDate: new Date(date).toISOString(),
      disbursementID: "",
    };
    if (value === 0) {
      message.warning("Filter will not apply if total engaged is 0!");
      handleGetDashboardData();
      setSelectedDate({ fromDate: null, toDate: null });
      return localStorage.removeItem("filterDate");
    } else {
      setSelectedDate({ fromDate: date, toDate: date });
      localStorage.setItem("filterDate", JSON.stringify(formattedDates));
      await handleDashboardFilteredData({
        fromDate: date,
        toDate: date,
        dpdRange: selectedDpd,
      });
    }
  };

  // Handle missing dates
  function fillMissingDates(engagementData) {
    if (!engagementData.length) return [];

    // Sort data by date
    engagementData.sort((a, b) =>
      dayjs(a.CreatedDate).diff(dayjs(b.CreatedDate))
    );

    // Get the first date and today
    const firstDate = dayjs(engagementData[0].CreatedDate);
    const currentDate = dayjs();

    // Generate all dates in range
    const allDates = [];
    let tempDate = firstDate;

    while (
      tempDate.isBefore(currentDate) ||
      tempDate.isSame(currentDate, "day")
    ) {
      allDates.push(tempDate.format("YYYY-MM-DD"));
      tempDate = tempDate.add(1, "day");
    }

    // Create a lookup for existing data
    const dataMap = new Map(
      engagementData.map((item) => [item.CreatedDate, item.total_engaged])
    );

    // Map over all dates, filling missing ones with 0
    return allDates.map((date) => ({
      CreatedDate: date,
      total_engaged: dataMap.get(date) || 0,
    }));
  }

  // Handle apply filters
  const applyFilters = async ({ fromDate, toDate, BranchName }) => {
    await handleDashboardFilteredData({
      fromDate: fromDate,
      toDate: toDate,
      BranchName,
      dpdRange: selectedDpd,
    });
  };

  // Handle clear the filter
  const ClearFilters = async () => {
    await handleGetDashboardData();
    setSelectedDate({ fromDate: null, toDate: null });
    localStorage.removeItem("filterDate");
    localStorage.removeItem(LOCAL_KEYS.HO_DASHBOARD_FILTER);
  };

  // Handle remove dpd category
  const handleRemoveDpdCategory = () => {
    setCurrentDpd(null);
    const defaultSelectedDpd = { start: -1, end: -1 };
    setSelectedDpd(defaultSelectedDpd);
    localStorage.setItem(
      "DashboardDpdCategory",
      JSON.stringify(defaultSelectedDpd)
    );
  };

  // Called the dashboard api as initial
  useEffect(() => {
    if (storedFilter || storedDpd) {
      const { fromDate, toDate, BranchName } = storedFilter
        ? JSON.parse(storedFilter)
        : { fromDate: null, toDate: null, BranchName: null };
      const parsedDpd = storedDpd ? JSON.parse(storedDpd) : selectedDpd;
      setSelectedDate({ fromDate: fromDate, toDate: toDate });
      handleDashboardFilteredData({
        fromDate,
        toDate,
        BranchName,
        dpdRange: parsedDpd,
      });
    } else {
      setSelectedDate({ fromDate: null, toDate: null });
      handleGetDashboardData();
    }
  }, [storedDpd, storedFilter]);

  // Set the current dpd tab value
  useEffect(() => {
    if (storedDpd && dpdCategory?.length) {
      const parsed = JSON.parse(storedDpd);
      const matchedTab = dpdCategory.find(
        (cate) =>
          cate.range.start === parsed.start && cate.range.end === parsed.end
      );
      if (matchedTab) {
        setCurrentDpd(matchedTab.name);
      }
    }
  }, [dpdCategory]);

  return (
    <Flex gap={30} vertical>
      <AppHeader
        title={title}
        isDashboard={true}
        applyFilters={applyFilters}
        ClearFilters={ClearFilters}
        selectedDate={selectedDate}
        pageId={SMART_COLLECT_MENU_IDS.DASHBOARD}
        isDashboardOtherPages={false}
      />

      <Flex vertical gap={10} style={{ width: "100%" }}>
        {serverDown ? (
          <ServerCrash />
        ) : (
          <Row
            gutter={[40, 20]}
            justify={"center"}
            className={Style.dashboard_row}
          >
            {dpdCategory?.length ? (
              <Col
                span={24}
                style={{ padding: "0px" }}
                className={Style.dashboard_tabs}
              >
                <TabsComponent
                  items={dpdCategory?.map((cate) => ({
                    label: (
                      <span
                        style={{
                          display: "flex",
                          alignItems: "center",
                          gap: 4,
                        }}
                      >
                        <Tooltip
                          title={
                            <div>
                              <b>DPD</b>
                              {`from: ${cate.range.start}, to: ${cate.range.end}`}
                            </div>
                          }
                          color={"#0F2050"}
                        >
                          {cate.name}
                        </Tooltip>
                        {currentDpd === cate.name && (
                          <input
                            type="image"
                            alt="dpd"
                            className={Style.selected_tab}
                            src={CloseIcon}
                            onClick={handleRemoveDpdCategory}
                          />
                        )}
                      </span>
                    ),
                    key: cate.name,
                  }))}
                  activeKey={currentDpd}
                  onChange={handleTabChange}
                />
              </Col>
            ) : null}

            {engagementData?.length ? (
              <Col span={24} style={{ padding: "0px" }}>
                <DateCarousel
                  engagementData={engagementData}
                  handleDisplaySelectedData={handleDisplaySelectedData}
                  handleGetDashboardData={handleGetDashboardData}
                  selectedDate={selectedDate}
                  setSelectedDate={setSelectedDate}
                  loading={loading}
                />
              </Col>
            ) : null}

            {loading ? (
              <Col span={24}>
                <ApplicationLoader />
              </Col>
            ) : (
              <>
                <Col xl={11} span={24} style={{ paddingLeft: "0px" }}>
                  <Flex
                    vertical
                    gap={20}
                    style={{ height: "100%" }}
                    justify="space-between"
                  >
                    <EngagementDetails dashboardData={dashboardData} />
                    <CustomerDetails dashboardData={dashboardData} />
                  </Flex>
                </Col>

                <Col
                  span={12}
                  className={Style.graph_container}
                  style={{
                    background: "#EBFDFF",
                    boxShadow: "4px 4px 4px 0px #00000040",
                    borderRadius: "8px",
                    padding: "0.5rem 20px 0",
                  }}
                >
                  {engagementData?.length ? (
                    <CustomerEngagementChart engagementData={engagementData} />
                  ) : (
                    <Text
                      style={{
                        textAlign: "center",
                        fontWeight: 500,
                        width: "100%",
                        display: "inline-block",
                      }}
                    >
                      No Data Found!
                    </Text>
                  )}
                </Col>

                <Col span={24} style={{ padding: "0px" }}>
                  <PayDetails
                    dashboardData={dashboardData}
                    nonContactable={nonContactable}
                  />
                </Col>
              </>
            )}
          </Row>
        )}
      </Flex>
      <Tour
        open={open}
        onClose={() => setOpen(false)}
        steps={steps}
        current={currentStep}
        onChange={setCurrentStep}
      />
    </Flex>
  );
}

CollectHoBmDashboard.propTypes = {
  role: PropTypes.string,
  title: PropTypes.string,
};

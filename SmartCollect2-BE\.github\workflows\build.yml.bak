name: Build

on:
  push:
    branches:
      - SmartCollect2

jobs:
  build:
    name: Build and analyze
    runs-on: self-hosted

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up PyEnv and Python
        shell: bash
        run: |
          # Check if pyenv is installed
          # echo current user
          echo "Current user: $(whoami)"

          # set $HOME as /home/<USER>
          export HOME=/home/<USER>
          export PYENV_ROOT="$HOME/.pyenv"
          export PATH="$PYENV_ROOT/bin:$PATH"
          eval "$(pyenv init --path)"
          eval "$(pyenv init -)"

          # Reload shell
          source ~/.bashrc

          if ! command -v pyenv &> /dev/null; then
              echo "PyEnv not found. Installing PyEnv..."
              # Install PyEnv dependencies
              sudo apt-get update
              sudo apt update; sudo apt install -y build-essential libssl-dev zlib1g-dev \
              libbz2-dev libreadline-dev libsqlite3-dev curl git \
              libncursesw5-dev xz-utils tk-dev libxml2-dev libxmlsec1-dev libffi-dev liblzma-dev
              
              # Install PyEnv
              curl https://pyenv.run | bash

              # Add PyEnv to PATH
              echo 'export PYENV_ROOT="$HOME/.pyenv"' >> ~/.bashrc
              echo 'export PATH="$PYENV_ROOT/bin:$PATH"' >> ~/.bashrc
              echo 'eval "$(pyenv init --path)"' >> ~/.bashrc
              echo 'eval "$(pyenv init -)"' >> ~/.bashrc
              
              # Reload shell
              source ~/.bashrc
          fi

          # Get the latest 3.12 version available via pyenv
          LATEST_312=$(pyenv install --list | grep -E "^\s*3\.12\." | tr -d ' ' | tail -1)

          # Install Python 3.12 if not already installed with name "runnerpython3.12"
          if ! pyenv versions | grep -q "$LATEST_312"; then
              echo "Installing Python $LATEST_312..."
              pyenv install $LATEST_312
          fi

          # Set alias as local version
          pyenv local $LATEST_312

          # Confirm Python version
          echo "Python version:"
          python --version

      - name: Create Virtual Environment
        shell: bash
        run: |
          python -m venv venv
          echo "Virtual environment created."

      - name: Install Dependencies
        shell: bash
        run: |
          source venv/bin/activate
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install coverage

      - name: Debug File Paths
        run: |
          echo "Current Working Directory:"
          pwd
          echo "Listing Files in Root:"
          ls -l
          echo "Checking SmartCollect2 Directory:"
          ls -l SmartCollect2/ || echo "SmartCollect2 directory not found!"

      - name: Run Tests and Generate Coverage Report
        env:
          SECRET_KEY: ${{secrets.SECRET_KEY}}
          DATABASE_NAME: ${{secrets.DATABASE_NAME}}
          DATABASE_USER: ${{secrets.DATABASE_USER}}
          DATABASE_PASSWORD: ${{secrets.DATABASE_PASSWORD}}
          DATABASE_HOST: ${{secrets.DATABASE_HOST}}
          DATABASE_PORT: ${{secrets.DATABASE_PORT}}
          DATABASE_URL: ${{secrets.DATABASE_URL}}
        run: |
          cd SmartCollect2
          coverage erase
          coverage run manage.py test
          coverage xml -o coverage.xml  # Generate coverage.xml

      - name: Run SonarQube Scan
        uses: SonarSource/sonarqube-scan-action@v4
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}

      # If you want to fail the job when the Quality Gate is red, uncomment below:
      # - name: SonarQube Quality Gate
      #   uses: SonarSource/sonarqube-quality-gate-action@v1
      #   timeout-minutes: 5
      #   env:
      #     SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

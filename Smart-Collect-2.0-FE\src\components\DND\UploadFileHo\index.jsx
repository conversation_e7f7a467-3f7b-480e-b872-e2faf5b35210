import { <PERSON><PERSON>, <PERSON>, Flex, message, Row, Typography } from "antd";
import React, { useState } from "react";
import AppHeader from "../../AppHeader";
import DOWNLOAD_IMG from "../../../assets/Images/down.svg";
import Style from "./_style.module.scss";
import { UploadComponent } from "./UploadComponent";
import { AXIOS } from "../../../apis/ho-Instance";
import { useNavigate } from "react-router";

const { Text } = Typography;

export default function UploadFile() {
  const [loadingAdd, setLoadingAdd] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState([]); // Store uploaded files
  const navigate = useNavigate();

  const handleFileUpload = (files) => {
    setSelectedFiles(files);
  };

  const handleAddDnd = async () => {
    if (selectedFiles.length === 0) {
      message.warning("Please upload a file before submitting.");
      return;
    }

    setLoadingAdd(true);

    // Using FormData for file upload
    const formData = new FormData();
    formData.append("action", "upload_file");

    // Append each selected file
    selectedFiles.forEach((file) => {
      formData.append("file", file);
    });

    try {
      await AXIOS.post("dnd/", formData, {
        headers: { "Content-Type": "multipart/form-data" },
      });

      message.success("Customer added to DND successfully!");

      navigate("/ho/dnd");
    } catch (error) {
      console.log("Error in Dnd file uploading", error?.message);
      message.error("File upload failed.");
    } finally {
      setLoadingAdd(false);
    }
  };

  return (
    <Flex vertical gap={20}>
      <AppHeader title={"Upload File"} />

      <Flex vertical gap={20} className={Style.upload_container}>
        <Row gutter={[20, 40]}>
          <Col span={24}>
            <UploadComponent onFileUpload={handleFileUpload} />
          </Col>
        </Row>

        <Flex gap={5} align="center">
          <img
            src={DOWNLOAD_IMG}
            style={{ width: "15px" }}
            alt="Download Sample"
          />
          <Text className={Style.text}>Download Sample File</Text>
        </Flex>

        <Flex justify="center">
          <Button
            className={Style.upload_button}
            loading={loadingAdd}
            onClick={handleAddDnd}
          >
            Add to DND
          </Button>
        </Flex>
      </Flex>
    </Flex>
  );
}

@import url("../../index.css");

$table-radius: 22px;
$attempts-bg: #bdd1ff;
$disable: #787777;
$body: #e4f8f9;

// Set the icon
:global(.ant-select-item-option-state) {
  :global(.anticon-check) {
    svg {
      fill: var(--dark-blue);
    }
  }
}
  .dnd_container{
    min-height: 100vh;
    .action_list{
      background-color: var(--dark-blue);
      border-radius: 50px;
      padding: 5px;
      cursor: pointer; 
      width: 31px;
      height: 31px;
      display: flex;  
      align-items: center;
      justify-content: center;
    }
    .btn{
      border: none;
      outline: none;
      background-color: transparent !important;
    }
    .bulk_btn{
      background-color: var(--dark-blue) !important;
      color:white !important;
      font-family: 'Kanit','Courier New', Courier, monospace;   
      font-size: 12px;
    }

    .customTable {
      padding-top: 0.5rem;

      .name {
        font-weight: 500;
        white-space: nowrap;
        color: var(--dark-blue);
      }

      //Global ant design classes
      // Pagination
      :global(.ant-pagination) {
        justify-content: center;
        margin: 0 !important;

        :global(.ant-pagination-prev),
        :global(.ant-pagination-next) {
          color: var(--dark-blue);
          border: 0 solid var(--blue);
          background: #e4f8f9;
          height: 25px;
          min-width: 15px;
          border-radius: 0px;
          margin: 0;
          button {
            border-radius: 0px;
          }
        }
        :global(.ant-pagination-item) {
          margin-right: 0;
          a {
            color: $disable;
            font-size: 0.9rem;
            line-height: 23px;
            font-family: 'Kanit','Courier New', Courier, monospace;
            font-weight: normal;
          }
          &:hover {
            background-color: transparent;
          }
        }
        :global(.ant-pagination-item-active) {
          border: none;
          a {
            color: var(--dark-blue);
            font-size: 1.2rem;
            padding: 0;
          }
        }
      }

      //Table container
      :global(.ant-table-container) {
        padding: 0;
        margin-bottom: 0.5rem;
        border-start-start-radius: $table-radius;
        border-start-end-radius: $table-radius;

        // Table header
        :global(.ant-table-header) {
          position: relative;
          margin: 0 auto;
          top: -21px;
          border-radius: $table-radius;

          :global(.ant-table-column-has-sorters) {
            background-color: var(--dark-blue);
            &:hover{
              background-color: var(--dark-blue);
            }

            :global(.ant-table-column-sorter-up),
            :global(.ant-table-column-sorter-down) {
              svg{
                fill: white;
              }
              &:global(.active){
                svg{
                  fill: rgb(24, 155, 249)
                }
              }
            }
          }
        }

        // Table virtual body
        :global(.ant-table-tbody-virtual) {
          margin-top: -8px;
        }

        &::before {
          box-shadow: none !important;
        }
      }

      // Table rows header
      :global(.ant-table-thead > tr) {
        th {
          border-start-end-radius: 0 !important;
          background-color: var(--dark-blue);
          border-inline-end: none !important;
          color: white;
          border-bottom: none;
          text-align: center;
          font-family: 'Kanit','Courier New', Courier, monospace;
          font-weight: normal;
          padding-left: 0;

          &:not(:last-child):not(.ant-table-selection-column):not(
              .ant-table-row-expand-icon-cell
            ):not([colspan])::before {
            width: 0;
          }
          &:global(.ant-table-cell-scrollbar) {
            box-shadow:none !important;
          }
          :global(.ant-checkbox-inner){
            background-color: #ffffff !important;
            border-color: var(--dark-blue) !important;
            &::after{
              border-color: #02951a;
              background-color: white;
            }
          }
        }
      }

      // Table body
      :global(.ant-table-tbody) {
        // Body rows
        :global(.ant-table-row) {
          &:nth-child(even) {
            background-color: var(--light-green);
            border-radius: 8px;
          }
          :global(.ant-table-cell-row-hover) {
            background-color: transparent;
          }
          // Cols
          :global(.ant-table-cell) {
            font-weight: 600;
            text-align: center;
            border-bottom: 2px solid white;

            :global(.ant-typography) {
              font-family: 'Kanit','Courier New', Courier, monospace;
              font-weight: normal;
            }

            &:global(.ant-table-cell-fix-right) {
              border-bottom: none !important;
            }
            &:global(.ant-table-cell-fix-right-first) {
              border-left: 2px solid white;
            }
            :global(.ant-checkbox-inner){
              background-color: #ffffff !important;
              border-color: var(--dark-blue) !important;
              &::after{
                border-color: #02951a;
                background-color: white;
              }
            }
          }
        }
      }

      // Fixed Cols
      :global(.ant-table-cell-fix-right) {
        background-color: $body;
      }
    }
  }
// Responsive View
@media only screen and (max-width:768px) {
  .dnd_container {
    .box {
      display: flex;
      flex-direction: column;
      align-items: start;

      .fields_container{
        flex-direction: column;
        width: 100%;
      }
      .custom_select{
        width: 100%;
        font-size: 11px;
      }
      .custom_button{
        font-size: 11px;
        height: auto;
        padding: 0.3rem .6rem;
      }
    }

    .text{
      font-size: 11px;
    }

    :global(.ant-checkbox) {
      :global(.ant-checkbox-inner) {
        width: 14px;
        height: 14px;
      }
    }
    :global(.ant-checkbox-checked) {
      :global(.ant-checkbox-inner) {
        &::after {
         height:8.142857px;
        }
      }
    }
  }

  .download_button {
    img {
      width: 15px;
    }
  }

  .customTable {
    // Table rows header
    :global(.ant-table-thead > tr) {
      th {
        font-size: 12px;
        padding: 0.5rem;
      }
    }
    :global(.ant-table-tbody) {
      :global(.ant-table-row) {
        :global(.ant-table-cell) {
          padding: 0.6rem;
          font-size: 12px !important;
          :global(.ant-typography){
            font-size: 12px !important;
          }
        }
      }
    }

    .icon_div {
      .icon_img {
        width: 13px;
        height: 13px;
      }
    }
  }
}
import { useEffect, useState } from "react";
import { Flex, Spin, Table, Typography } from "antd";
import AppHeader from "../../AppHeader";
import Style from "./_style.module.scss";
import { Link } from "react-router";
import { handleDateFormatter, ALL_CACHE_NAMES } from "../../../constant";
import { AXIOS } from "../../../apis/ho-Instance";
import {
  getCacheKey,
  getFromCache,
  storeToCache,
} from "../../../utils/cacheHelper";

const { Text } = Typography;
const CACHE_NAME = ALL_CACHE_NAMES.ADMIN_CLIENT_TABLE_CACHE;

export default function AdminClientTable() {
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch all banks details
  const handleFetchBankDetails = async () => {
    setIsLoading(true);
    // Build cache key for GET request (no params)
    const cacheKey = getCacheKey({ endpoint: "bank_details/" });
    // Try to get cached data for instant UI update
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setData(cachedData);
      setIsLoading(false);
    }
    try {
      const res = await AXIOS.get("bank_details/");
      if (res.status === 200) {
        setData(res.data?.all_banks);
        // Store the latest data in cache
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: res.data?.all_banks,
        });
      }
    } catch (error) {
      console.log("Error in fetching bank details", error);
    } finally {
      setIsLoading(false);
    }
  };

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      render: (_, data) => <Text>{data.key}.</Text>,
    },
    {
      title: "Name",
      dataIndex: "BankName",
      render: (_, { BankName }) => (
        <Link to={`/admin/client-details/${BankName}`}>
          <Text className={Style.name}>{BankName ?? "--"}</Text>
        </Link>
      ),
    },
    {
      title: "Active",
      dataIndex: "IsActive",
      render: (IsActive) => <Text>{IsActive ? "True" : "False"}</Text>,
    },
    {
      title: "Subscription Type",
      dataIndex: "SubscriptionType",
      render: (SubscriptionType) => <Text>{SubscriptionType ?? "--"}</Text>,
    },
    {
      title: "Onboarded Date",
      dataIndex: "OnBoardingDate",
      render: (_, { OnBoardingDate }) => (
        <Text className={Style.contact}>
          {OnBoardingDate ? handleDateFormatter(OnBoardingDate) : "--"}
        </Text>
      ),
    },
    {
      title: "Remarks",
      dataIndex: "Remarks",
      render: (Remarks) => <Text>{Remarks ?? "--"}</Text>,
    },
  ];

  const dataSource = Array.isArray(data)
    ? data.map((data, i) => ({
        key: i + 1,
        ...data,
      }))
    : [];

  useEffect(() => {
    handleFetchBankDetails();
  }, []);

  return (
    <Flex vertical gap={50}>
      <AppHeader title={"Client table"} />
      <Spin tip={"Loading..."} spinning={isLoading}>
        <Table
          virtual
          className={Style.customTable}
          columns={columns}
          dataSource={dataSource}
          scroll={{
            x: 1000,
            y: 460,
          }}
          pagination={{
            showSizeChanger: false,
          }}
        />
      </Spin>
    </Flex>
  );
}

import {
  But<PERSON>,
  DatePicker,
  Flex,
  InputNumber,
  Segmented,
  Typography,
  message
} from "antd";
import React, { useEffect, useState } from "react";
import Style from "./_ready.module.scss";
import dayjs from "dayjs";
import { AXIOS } from "../../../../apis/ho-Instance";
import { PaymentModal } from "./PaymentModal";
import SuccessModal from "./SuccessModal";
import PropTypes from "prop-types";

const { Text } = Typography;

export function ReadyToPay({
  overdueAmount,
  loanmstid,
  branchMstId,
  handleCross
}) {

  const [loading, setLoading] = useState(false);
  const dateFormatList = ["DD/MM/YYYY", "DD/MM/YY", "DD-MM-YYYY", "DD-MM-YY"];
  const [selectedDate, setSelectedDate] = useState(
    dayjs("01/01/25", dateFormatList[0])
  );
  const [amount, setAmount] = useState(0);
  const [payMode, setPayMode] = useState("Offline");
  const [showPayModal, setShowPayModal]  = useState(false);
  const [showSuccessModal, setShowSuccessModal]  = useState(false);

  // Handle date change
  const handleDateChange = (date) => {
    setSelectedDate(date ? dayjs(date) : null); // Ensure it's always a dayjs object or null
  };

  // Handle amount change
  const handleAmountChange = (value) => {
    if (value < 1){
      message.error("Amount never be less than 1");
      return "";
    }
    setAmount(value);
  };

  // API call to send promise data
  const handlePostPromiseData = async ({sendPayInfo}) => {
    setShowPayModal(false);
    setLoading(true);
    const formattedDate = selectedDate
      ? dayjs(selectedDate).format("YYYY-MM-DD")
      : "";

    const requestData = {
      promise_date: formattedDate,
      promise_amount: amount,
      mode_of_payment: payMode,
      BranchMstID: branchMstId,
      LoanMstID: loanmstid,
      type: "AgreedToPay",
      sendPayInfo
    };
    try {
      await AXIOS.post("feedback/", requestData);
      if (sendPayInfo) {
        // Show success modal
        setShowSuccessModal(true);
  
        // Close after 2 seconds
        setTimeout(() => {
          setShowSuccessModal(false);
          handleCross(); // your modal close handler
          window.location.reload(); // reload if needed
        }, 2000);
      } else {
        // Just close without showing success modal
        handleCross();
        window.location.reload();
      }
      // Show success message
      message.success("Promise created successfully!");

      // Close the modal after 2 seconds
      // setTimeout(() => {
      //   handleCross();
      //   window.location.reload();
      // }, 500);
    } catch (error) {
      console.log("Error in Ready to Pay:", error);
      // Show success message
      message.error("Promise not created!");
    } finally {
      setLoading(false);
    }
  };

  useEffect(()=>{ setAmount(overdueAmount) },[overdueAmount])

  return (
    <div className={Style.ready_to_pay}>
      <Flex vertical gap={20} justify="center">
        {/* date */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Date:</Text>
          <DatePicker
            className={Style.date_picker}
            placeholder="dd/mm/yy"
            onChange={handleDateChange}
            // defaultValue={selectedDate}
            format={dateFormatList}
            // value={selectedDate}
            disabledDate={(current) => {
              return current && current < dayjs().add(1, "day").startOf("day"); // Disables today and past dates, allowing selection from tomorrow
            }}
          />
        </Flex>

        {/* amount */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Amount:</Text>
          <InputNumber
            value={amount}
            min={1} 
            addonBefore={"₹"}
            formatter={(value) => {
              const num = Number(value);
              return isNaN(num) ? "" : num.toLocaleString("en-IN");
            }}
            parser={(value) => value?.replace(/[₹,]/g, "")}
            onChange={handleAmountChange}
          />
        </Flex>

        {/* mode */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Mode of Payment:</Text>

          <Segmented
            value={payMode}
            style={{
              marginBottom: 8,
            }}
            onChange={setPayMode}
            options={["Online", "Offline"]}
          />
        </Flex>

        <Flex justify="center">
          <Button
            className={Style.feedback_button}
            onClick={()=>setShowPayModal(true)}
            loading={loading}
          >
            Create Promise
          </Button>
        </Flex>
      </Flex>

      <PaymentModal modalStatus={showPayModal} handleSendPayInfo={handlePostPromiseData}/>
      <SuccessModal modalStatus={showSuccessModal}  setModalStatus={setShowSuccessModal}/>
    </div>
  );
}

ReadyToPay.propTypes={
  overdueAmount: PropTypes.oneOfType([PropTypes.string,PropTypes.number]),
  loanmstid: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  branchMstId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  handleCross: PropTypes.func
}
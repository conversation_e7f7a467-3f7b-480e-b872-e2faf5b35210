import PropTypes from 'prop-types';
import React from 'react';
import Chart from "react-apexcharts";

export function PortfolioChart({data}) {
  // Extract state names (handling null values)
  const categories= data.map((item) => item.State || "Unknown");

  // Prepare data series
  const seriesData = [
    { name: "Good", data: data.map((item) => item.good_customers || 0) },
    { name: "Arrear", data: data.map((item) => item.arrear_customers || 0) },
    { name: "NPA", data: data.map((item) => item.npa_customers || 0) },
    { name: "+NPA", data: data.map((item) => item.npaplus_customers || 0) },
  ];

  const chartOptions = {
        chart: {
          type: "bar",
          background: "#E6F7F9", // Light blue background
          toolbar: { show: false },
        },
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: "60%", // Increased bar width
            borderRadius: 1,
          },
        },
        colors: ["#198754", "#D1B000", "#F56100", "#E20613"], // Matching colors
        dataLabels: {
          enabled: true,
          formatter: (val) => `${val}%`,
          style: {
            fontSize: "10px",
            fontWeight: "bold",
          },
        },
        xaxis: {
          categories,
          title: { text: "State", style: { fontWeight: "bold" } },
        },
        yaxis: {
          title: { text: "Amount", style: { fontWeight: "bold" } },
        },
        grid: { show: false }, // Remove unnecessary grid lines
        legend: {
          position: "top",
          horizontalAlign: "right",
          fontSize: "12px",
        },
  };   
  return (
     <Chart options={chartOptions} series={seriesData} type="bar" height={400} />
  )
}

PortfolioChart.propTypes={
  data: PropTypes.array
}
import {
  <PERSON><PERSON>,
  <PERSON>,
  Flex,
  message,
  Modal,
  Row,
  Space,
  Tooltip,
  Typography,
} from "antd";
import Style from "./_style.module.scss";
import PROFILE_IMG from "../../../assets/Images/gg_profile.png";
import HISTORY_IMG from "../../../assets/Images/history-white.png";
import FEEDBACK_IMG from "../../../assets/Images/fluent-mdl2_feedback.png";
import PHONE_IMG from "../../../assets/Images/mi_call.png";
import DOWNLOAD_IMG from "../../../assets/Images/download.svg";
import { useEffect, useMemo, useRef, useState } from "react";
import {
  SMART_COLLECT_MENU_IDS,
  formatAmount,
  ALL_CACHE_NAMES,
} from "../../../constant";
import DisbursementIdSvg from "../../../assets/SVGs/DisbursementIdSvg";
import BranchNameSVG from "../../../assets/SVGs/BranchNameSVG";
import LoanTypeSVG from "../../../assets/SVGs/LoanTypeSVG";
import { Details } from "./Details";
import { DialerModal } from "../DialerModal";
import { ConversionHistory } from "../ConversionHistory";
import CommunicationResponseTable from "./CommunicationResponseTable";
import { FeedbackModal } from "../FeedbackModal";
import jsPDF from "jspdf";
import { AXIOS } from "../../../apis/ho-Instance";
import autoTable from "jspdf-autotable"; // if using tables
import PropTypes from "prop-types";
import {
  getCacheKey,
  getFromCache,
  storeToCache,
} from "../../../utils/cacheHelper";

const { Title, Text } = Typography;
const CACHE_NAME = ALL_CACHE_NAMES.HO_CUSTOMER_PROFILE_TABLE_CACHE;

// Props types
CustomerProfileModal.propTypes = {
  modalStatus: PropTypes.bool,
  handleClose: PropTypes.func.isRequired,
  name: PropTypes.string,
  branchName: PropTypes.string,
  overdueAmount: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  dpdAmount: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  promiseAmount: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  pageId: PropTypes.string,
  customerId: PropTypes.string,
  disbursementId: PropTypes.string,
  branchMstId: PropTypes.number,
  loanType: PropTypes.string,
  loanmstid: PropTypes.number,
};

export default function CustomerProfileModal({
  modalStatus,
  handleClose,
  name,
  branchName,
  overdueAmount,
  dpdAmount,
  promiseAmount,
  pageId,
  customerId,
  disbursementId,
  branchMstId,
  loanType,
  loanmstid,
}) {
  const pdfRef = useRef();
  const [modalState, setModalState] = useState({
    isDialer: false,
    isHistory: false,
    isFeedback: false,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [profileData, setProfileData] = useState({ data: [] });
  const [expended, setExpended] = useState(false);

  const handleOpenModal = (type) => {
    setModalState((prevState) => ({
      ...prevState,
      [type]: true,
    }));
  };

  const handleCloseModal = (type) => {
    setModalState((prevState) => ({
      ...prevState,
      [type]: false,
    }));
  };

  const handleDialerSubmit = async ({ number, loanmstid }) => {
    setIsLoading(true);
    message.success("Initiate call");
    try {
      await AXIOS.post("v1/dialer/", {
        loanMstId: loanmstid,
        bm_phone: number,
      });
    } catch (error) {
      console.error("Error in dialer", error?.message);
    } finally {
      setIsLoading(false);
    }
  };

  const formatAmountIn = (amount) => {
    return `Rs.${amount.toLocaleString("en-IN", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`;
  };

  const handleDownload = async () => {
    try {
      message.loading({
        content: "Generating PDF...",
        key: "pdfDownload",
        duration: 0,
      });

      const doc = new jsPDF("landscape");
      const response = await AXIOS.post("userprofile/", {
        LoanMstID: loanmstid,
      });
      if (response.status === 200) {
        const { data = [] } = response.data;
        const pageWidth = doc.internal.pageSize.getWidth();
        const pageHeight = doc.internal.pageSize.getHeight();
        doc.setFillColor(255, 255, 255); //white
        doc.rect(0, 0, pageWidth, pageHeight, "F"); // Draw a filled rectangle over the entire page

        // Define some common colors (these will draw OVER the red background)
        const primaryBlue = [0, 51, 153];
        const lightBlueBackground = [235, 248, 255];
        const darkGrayText = [50, 50, 50];
        const redText = [255, 0, 0];
        const greenText = [0, 128, 0];

        // --- Header Section (Customer Info) ---
        const headerRectX = 10;
        const headerRectY = 10;
        const headerRectWidth = pageWidth - 20;
        const headerRectHeight = 15;
        const headerCornerRadius = 8;

        doc.setFillColor(...primaryBlue);
        doc.roundedRect(
          headerRectX,
          headerRectY,
          headerRectWidth,
          headerRectHeight,
          headerCornerRadius,
          headerCornerRadius,
          "F"
        );

        doc.setTextColor(255);
        doc.setFont("helvetica", "bold");
        doc.setFontSize(10);
        doc.text(name, headerRectX + 10, headerRectY + 6);
        doc.setFontSize(8);
        doc.text(
          `Customer Id: ${customerId}`,
          headerRectX + 10,
          headerRectY + 10
        );
        // --- Info Block ---
        const infoBlockX = 10;
        const infoBlockY = headerRectY + headerRectHeight + 2;
        const infoBlockWidth = pageWidth - 20;
        // --- 2. Add bottom padding to info block by increasing its height ---
        const infoBlockHeight = 60 + 10; // Original 60 + 10 units of padding = 70
        const paddingX = 5;

        doc.setFillColor(...lightBlueBackground);
        doc.roundedRect(
          infoBlockX,
          infoBlockY,
          infoBlockWidth,
          infoBlockHeight,
          3, // horizontal corner radius
          3, // vertical corner radius
          "F"
        );

        const textBlockX = infoBlockX + paddingX;
        const textBlockWidth = infoBlockWidth - paddingX * 2;
        let singleColY = infoBlockY + 10; // Starting Y for text content

        const infoFontSize = 10;
        const getTextWidth = (text, fontSize, fontStyle) => {
          doc.setFont("helvetica", fontStyle);
          doc.setFontSize(fontSize);
          return (
            (doc.getStringUnitWidth(text) * fontSize) / doc.internal.scaleFactor
          );
        };

        const labelValuePairs = [
          ["Disbursement Id:", disbursementId],
          ["Branch Name:", branchName],
          ["Loan Type:", loanType],
          ["DPD:", dpdAmount],
          [
            "Overdue Amount:",
            `Rs.${overdueAmount.toLocaleString("en-IN", {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })}`,
          ],
          [
            "Promise Amount:",
            `Rs.${promiseAmount.toLocaleString("en-IN", {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })}`,
          ],
        ];

        labelValuePairs.forEach(([label, value], index) => {
          let fontColor = darkGrayText;

          if (label === "Overdue Amount:") {
            fontColor = redText;
          } else if (label === "Promise Amount:") {
            fontColor = greenText;
          }

          doc.setFont("helvetica", "normal");
          doc.setFontSize(infoFontSize);
          doc.setTextColor(...darkGrayText); // Set label color
          doc.text(label, textBlockX, singleColY);

          const valueText = `${value}`;
          doc.setFont("helvetica", "bold"); // Set bold for value measurement
          const valueTextWidth = getTextWidth(valueText, infoFontSize, "bold");
          const xValuePos = textBlockX + textBlockWidth - valueTextWidth;

          doc.setTextColor(...fontColor); // Set value specific color
          doc.text(valueText, xValuePos, singleColY);

          singleColY += 10; // Move to the next line for the next pair
        });

        doc.setTextColor(0); // reset

        // Add spacing before table
        const tableData = data.map((item) => [
          item.record_date ?? "",
          item.event_type ?? "",
          item.status ?? "",
          item.whatsapp_count ?? 0,
          item.blaster_count ?? 0,
          item.voicebot_count ?? 0,
          item.feedback_count ?? 0,
          item.event_source ?? "",
          item.event_date ?? "",
          formatAmountIn(item.event_amount ?? 0),
          item.dpd ?? 0,
          formatAmountIn(item.overdue_amount ?? 0),
        ]);

        autoTable(doc, {
          startY: infoBlockY + infoBlockHeight + 2,
          head: [
            [
              "Record Date",
              "Event Type",
              "Status",
              "WhatsApp",
              "Blaster",
              "Voicebot",
              "Feedback",
              "Source",
              "Event Date",
              "Amount",
              "DPD",
              "Overdue",
            ],
          ],
          body: tableData,
          styles: {
            fontSize: 7.5,
            cellPadding: 1.5,
            overflow: "linebreak",
            halign: "center",
          },
          headStyles: {
            fillColor: primaryBlue,
            textColor: 255,
            fontSize: 8.5,
          },
          alternateRowStyles: {
            fillColor: lightBlueBackground, // Added for consistency
          },
          margin: { left: 10, right: 10 }, // Minimal margin
          tableWidth: "auto", // Allow full dynamic width
        });
        doc.save(`${name}-summary.pdf`);
        message.success({
          content: "PDF downloaded successfully!",
          key: "pdfDownload",
          duration: 2,
        });
      }
    } catch (error) {
      console.error("PDF download failed:", error);
      message.error({ content: "Failed to generate PDF.", key: "pdfDownload" });
    }
  };

  const handleFetchProfileDetails = async () => {
    setLoading(true);
    // Build cache key for POST request (LoanMstID)
    const cacheKey = getCacheKey({
      endpoint: "userprofile/",
      params: { LoanMstID: loanmstid },
    });
    // Try to get cached data for instant UI update
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setProfileData({ data: cachedData });
      setLoading(false);
    }
    try {
      const response = await AXIOS.post("userprofile/", {
        LoanMstID: loanmstid,
      });
      if (response.status === 200) {
        setProfileData({
          data: response.data.data?.length ? response.data.data : [],
        });
        // Store the latest data in cache
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data.data?.length ? response.data.data : [],
        });
      }
    } catch (error) {
      console.log("Error in fetching profile details", error);
      message.error("Something went wrong, Try again!");
    } finally {
      setLoading(false);
    }
  };

  const visibleData = useMemo(() => {
    if (!Array.isArray(profileData.data)) return [];

    const isCollapsed = !expended;
    return isCollapsed ? profileData.data.slice(0, 2) : profileData.data;
  }, [profileData.data, expended]);

  // Invoking the get call
  useEffect(() => {
    if (loanmstid) handleFetchProfileDetails();
  }, [loanmstid]);

  return (
    <Modal
      centered
      className={Style.profile_modal}
      title={null}
      open={modalStatus}
      onOk={handleClose}
      onCancel={handleClose}
      footer={null}
      width={900}
    >
      <div ref={pdfRef}>
        <Flex
          justify="space-between"
          align="center"
          className={`${Style.customer_details}`}
        >
          <Flex gap={10} align="center" className={Style.profile_container}>
            {/* icon */}
            <div className={Style.profile_img}>
              <img src={PROFILE_IMG} alt={name} />
            </div>

            {/* details */}
            <Flex align="start" vertical>
              <Title level={4} className={Style.name}>
                {name ?? "N/A"}
              </Title>
              <Text className={Style.customer_id}>
                Customer Id: {customerId ?? "N/A"}
              </Text>
            </Flex>
          </Flex>

          {/* icons */}
          <Flex gap={20} align="center">
            <Tooltip title="Feedback">
              <input
                type="image"
                src={FEEDBACK_IMG}
                alt="Feedback"
                className={Style.profile_img}
                style={{ cursor: "pointer" }}
                onClick={() => handleOpenModal("isFeedback")}
              />
            </Tooltip>
            <Tooltip title="Dialer">
              <input
                type="image"
                src={PHONE_IMG}
                alt="Feedback"
                className={Style.profile_img}
                style={{ cursor: "pointer" }}
                onClick={() => handleOpenModal("isDialer")}
              />
            </Tooltip>
            <Tooltip title={"History"}>
              <input
                type="image"
                src={HISTORY_IMG}
                alt="Feedback"
                className={Style.profile_img}
                style={{ cursor: "pointer" }}
                onClick={() => handleOpenModal("isHistory")}
              />
            </Tooltip>
            <Tooltip title={"Download as PDF"}>
              <input
                type="image"
                src={DOWNLOAD_IMG}
                alt="Feedback"
                className={Style.profile_img}
                style={{ cursor: "pointer" }}
                onClick={() => handleDownload()}
              />
            </Tooltip>
          </Flex>
        </Flex>

        <Flex vertical className={Style.body_container}>
          <div
            className={`${Style.summary_section} ${
              expended ? Style.hidden : Style.visible
            }`}
          >
            {/* Amount & customer details */}
            <Row style={{ padding: "2rem 0 0" }} justify={"center"}>
              <Col span={24} md={12} className={Style.first_col}>
                <Flex vertical gap={6} className={Style.payment_details}>
                  <Details
                    title="Disbursement ID"
                    image={<DisbursementIdSvg />}
                    value={disbursementId ?? "N/A"}
                  />
                  <Details
                    title="Branch Name"
                    image={<BranchNameSVG />}
                    value={branchName ?? "N/A"}
                  />
                  <Details
                    title="Loan Type"
                    image={<LoanTypeSVG />}
                    value={loanType ?? "N/A"}
                  />
                </Flex>
              </Col>

              <Col span={24} md={12} className={Style.second_col}>
                <Flex
                  justify="space-evenly"
                  vertical
                  className={Style.amount_details}
                  gap={6}
                >
                  {dpdAmount && (
                    <Flex justify="space-between" align="center" gap={5}>
                      <Space>
                        <div className={Style.icon_img}>
                          <DisbursementIdSvg />
                        </div>
                        <Text className={Style.text}>DPD</Text>
                      </Space>
                      <Title level={4} className={`${Style.amount}`}>
                        {overdueAmount > 0 && dpdAmount === 0 ? "-" : dpdAmount}
                      </Title>
                    </Flex>
                  )}
                  {pageId !== SMART_COLLECT_MENU_IDS.ALREADY_PAY && (
                    <Flex justify="space-between" align="center" gap={5}>
                      <Space>
                        <div className={Style.icon_img}>
                          <DisbursementIdSvg />
                        </div>
                        <Text className={Style.text}>Overdue Amount</Text>
                      </Space>
                      <Title
                        level={4}
                        className={`${Style.amount} ${Style.error}`}
                      >
                        Rs.
                        {typeof overdueAmount === "string"
                          ? overdueAmount
                          : formatAmount(overdueAmount ?? 0)}
                      </Title>
                    </Flex>
                  )}
                  {(pageId === SMART_COLLECT_MENU_IDS.READY_TO_PAY ||
                    pageId === SMART_COLLECT_MENU_IDS.ALREADY_PAY) && (
                    <Flex justify="space-between" align="center" gap={5}>
                      <Space>
                        <div className={Style.icon_img}>
                          <DisbursementIdSvg />
                        </div>
                        <Text className={Style.text}>
                          {pageId === SMART_COLLECT_MENU_IDS.READY_TO_PAY
                            ? "Promise Amount"
                            : "Claim Amount"}
                        </Text>
                      </Space>
                      <Title
                        level={4}
                        className={`${Style.amount} ${
                          pageId === SMART_COLLECT_MENU_IDS.READY_TO_PAY
                            ? Style.success
                            : Style.error
                        }`}
                      >
                        Rs.
                        {formatAmount(
                          pageId === SMART_COLLECT_MENU_IDS.READY_TO_PAY
                            ? promiseAmount ?? 0
                            : overdueAmount ?? 0
                        )}
                      </Title>
                    </Flex>
                  )}
                </Flex>
              </Col>
            </Row>
          </div>

          {/*Toggle button  */}
          <Flex
            justify="end"
            className={Style.toggle_button_container}
            style={{ padding: !expended ? "0 1rem" : "2rem 1rem 0 1rem" }}
          >
            <Button
              className={Style.toggle_button}
              onClick={() => setExpended((prev) => !prev)}
            >
              {expended ? "▲ Summary" : "▼ Details"}
            </Button>
          </Flex>

          {/* Response table */}
          <div
            className={`${Style.table_section} ${
              !expended ? Style.visible : null
            }`}
          >
            <Flex className={Style.container}>
              <CommunicationResponseTable
                responseData={visibleData ?? []}
                loading={loading}
              />
            </Flex>
          </div>
        </Flex>
      </div>

      <DialerModal
        customerName={name}
        overdueAmount={overdueAmount}
        loanmstid={loanmstid}
        isLoading={isLoading}
        modalStatus={modalState.isDialer}
        handleSubmit={handleDialerSubmit}
        handleCancel={() => handleCloseModal("isDialer")}
      />

      <ConversionHistory
        customerName={name}
        loanMstId={loanmstid}
        modalStatus={modalState.isHistory}
        handleCancel={() => handleCloseModal("isHistory")}
      />

      <FeedbackModal
        pageId={pageId}
        overdueAmount={overdueAmount}
        branchMstId={branchMstId}
        loanmstid={loanmstid}
        modalStatus={modalState.isFeedback}
        handleSubmit={() => handleCloseModal("isFeedback")}
        handleCancel={() => handleCloseModal("isFeedback")}
      />
    </Modal>
  );
}

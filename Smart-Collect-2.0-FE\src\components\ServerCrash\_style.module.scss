@import url('../../index.css');

.container{
    margin-top: 2rem;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 500px; 
    height: 100%;
    .img_container{
        max-width: 200px;
        margin-bottom: 20px;
        img{
            width: 100%;
            object-fit: contain;
        }
    }
    .text_container{
        text-align: center;
        font-family: '<PERSON><PERSON><PERSON>','Courier New', Courier, monospace;
        .title{
            color: rgb(76, 76, 76);
            font-weight: 400;
            margin-bottom: 5px;
        }
        .desc{
           color: #8a8a8a;
           margin: 0;
           font-size: 13px;
        }
        button{
            border: none;
            background-color: transparent;
            color: var(--dark-blue);
            font-family: '<PERSON>ni<PERSON>','Courier New', Courier, monospace;
            text-decoration: underline;
            cursor: pointer;
        }
    }
}
//Responsive
@media screen and (max-width:768px) {
    .container{
    .text_container{
        .title{
           font-size: 16px;
        }
        .desc{
           font-size: 11px;
        }
    }
}
}

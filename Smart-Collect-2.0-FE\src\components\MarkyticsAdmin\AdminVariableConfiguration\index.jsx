import { <PERSON><PERSON>, <PERSON>, Flex, message, Row, Select, Typography } from "antd";
import React, { useEffect, useState } from "react";
import AppHeader from "../../AppHeader";
import Style from "../_global.module.scss";
import { AXIOS } from "../../../apis/ho-Instance";
const { Text } = Typography;

export default function AdminVariableConfiguration() {
  const [isLoading, setIsLoading] = useState(false);
  const [allBanks, setAllBanks] = useState([]);
  const [categories, setCategories] = useState([]);
  const [configData, setConfigData] = useState({
    bank: null,
    VariableMappingFields: [],
    CampaignCategoryColumns: [],
    BaseOnColumns: [],
  });

  // Transfer the data
  const handleTransformedArray = ({ inputData }) => {
    return Object.keys(inputData).map((key) => ({
      key: key,
      name: inputData[key].verbose_name,
      type: inputData[key].type,
    }));
  };

  // Fetch banks
  const handleFetchBanks = async () => {
    try {
      const res = await AXIOS.get("getbanks/");
      if (res.status === 200 && res.data.Banks.length > 0) {
        setAllBanks(res.data.Banks);
      } else {
        setAllBanks([]);
      }
    } catch (error) {
      console.log("Error in fetching all banks", error?.message);
      setAllBanks([]);
    }
  };

  const handleGetSelectedItems = async () => {
    try {
      const response = await AXIOS.get(`v1/bank-controls/${configData.bank}`);
      if (response.status === 200) {
        const {
          BaseOnColumns,
          CampaignCategoryColumns,
          VariableMappingFields,
        } = response.data?.Json || {};
        setConfigData((prev) => ({
          ...prev,
          VariableMappingFields: VariableMappingFields || [],
          BaseOnColumns: BaseOnColumns || [],
          CampaignCategoryColumns: CampaignCategoryColumns || [],
        }));
      }
    } catch (error) {
      console.log("Error in selected items", error);
      setConfigData((prev) => ({
        ...prev,
        VariableMappingFields: [],
        BaseOnColumns: [],
        CampaignCategoryColumns: [],
      }));
    }
  };

  // Fetch other values
  const handleFetchOtherFields = async () => {
    try {
      const res = await AXIOS.get("/v2/campaign/dropdownoptions", {
        params: {
          requesttype: "campaigncategoryall",
        },
      });
      if (res.status === 200) {
        const updated = handleTransformedArray({ inputData: res.data });
        setCategories(updated);
      }
    } catch (error) {
      console.log("Error in fetching details", error);
    }
  };

  // /Handle set bank
  const handleSet = async () => {
    if (
      !configData.bank ||
      configData.VariableMappingFields == 0 ||
      configData.CampaignCategoryColumns == 0 ||
      configData.BaseOnColumns.length === 0
    ) {
      message.warning("Please select all fields, before setting component");
      return;
    }
    setIsLoading(true);
    try {
      const res = await AXIOS.post("v1/bank-controls/", {
        BankMstID: configData.bank,
        Json: {
          CampaignCategoryColumns: configData.CampaignCategoryColumns,
          VariableMappingFields: configData.VariableMappingFields,
          BaseOnColumns: configData.BaseOnColumns,
        },
      });
      if (res.status === 201 || res.status === 200) {
        message.success("Variables created successfully!");
      }
    } catch (error) {
      console.log("Error in variable component", error);
      message.error("Something wrong ,variable not set, Please try again!");
    } finally {
      setIsLoading(false);
      setConfigData((prev) => ({
        ...prev,
        BaseOnColumns: [],
        CampaignCategoryColumns: [],
        VariableMappingFields: [],
        bank: null,
      }));
    }
  };

  useEffect(() => {
    if (configData.bank) handleGetSelectedItems();
  }, [configData.bank]);

  // Invoking the fetch banks api
  useEffect(() => {
    handleFetchBanks();
    handleFetchOtherFields();
  }, []);

  return (
    <Flex vertical gap={10}>
      <AppHeader title={"Variable Configuration"} />
      <Row
        gutter={[20, 40]}
        className={Style.add_bank}
        justify={"space-between"}
      >
        {/* bank */}
        <Col span={24}>
          <Flex gap={20} align="center">
            <Flex gap={5} align="center">
              <Text className={Style.text}>Select Bank:</Text>
            </Flex>
            <Select
              showSearch
              className={Style.custom_select}
              placeholder="Select bank"
              options={allBanks?.map((bank) => ({
                label: bank.BankName,
                value: bank.BankMstID,
              }))}
              value={configData.bank}
              onChange={(value) =>
                setConfigData((prev) => ({ ...prev, bank: value }))
              }
              filterOption={(input, option) =>
                (option?.label ?? "")
                  ?.toLowerCase()
                  .includes(input?.toLowerCase())
              }
            />
          </Flex>
        </Col>

        {/* Variable Mapping */}
        <Col md={12} xs={24}>
          <Flex gap={20} align="center">
            <Flex gap={5}>
              <Text className={Style.text} style={{ width: "250px" }}>
                Select Column for Variable Mapping:
              </Text>
            </Flex>
            <Select
              showSearch={false}
              mode="multiple"
              className={Style.custom_select}
              placeholder="Select variable mapping"
              options={categories.map((item) => ({
                label: item.name,
                value: item.key,
              }))}
              value={configData.VariableMappingFields}
              onChange={(value) =>
                setConfigData((prev) => ({
                  ...prev,
                  VariableMappingFields: value,
                }))
              }
            />
          </Flex>
        </Col>

        {/*  Campaign Category */}
        <Col md={12} xs={24}>
          <Flex gap={20} align="center">
            <Flex gap={5}>
              <Text className={Style.text} style={{ width: "250px" }}>
                Select Column for Campaign Category:
              </Text>
            </Flex>
            <Select
              showSearch={false}
              mode="multiple"
              className={Style.custom_select}
              placeholder="Select campaign category"
              options={categories.map((item) => ({
                label: item.name,
                value: item.key,
              }))}
              value={configData.CampaignCategoryColumns}
              onChange={(value) =>
                setConfigData((prev) => ({
                  ...prev,
                  CampaignCategoryColumns: value,
                }))
              }
            />
          </Flex>
        </Col>

        {/*  Based on Columns */}
        <Col span={24}>
          <Flex gap={20} align="center">
            <Flex gap={5}>
              <Text className={Style.text} style={{ width: "200px" }}>
                Select Based on Columns:
              </Text>
            </Flex>
            <Select
              showSearch={false}
              mode="multiple"
              className={Style.custom_select}
              placeholder="Select columns"
              options={categories.map((item) => ({
                label: item.name,
                value: item.key,
              }))}
              value={configData.BaseOnColumns}
              onChange={(value) =>
                setConfigData((prev) => ({ ...prev, BaseOnColumns: value }))
              }
            />
          </Flex>
        </Col>

        {/* set button */}
        <Col span={24}>
          <Flex align="center" justify="center">
            <Button
              className={Style.add_button}
              loading={isLoading}
              onClick={handleSet}
            >
              Set
            </Button>
          </Flex>
        </Col>
      </Row>
    </Flex>
  );
}

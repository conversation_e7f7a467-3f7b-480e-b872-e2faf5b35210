import React, { useState, useEffect } from "react";
import Style from "./_style.module.scss";
import { Table, Typography } from "antd";
import { formatAmount, handleDateFormatter } from "../../../constant";
import PropTypes from "prop-types";

const { Text } = Typography;

export default function Response({ responseData }) {
  const [data, setData] = useState([]);
  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      render: (_, data) => <Text>{data.key}.</Text>,
    },
    {
      title: "As On Date",
      dataIndex: "AsOnDate",
      sorter: (a, b) => a.AsOnDate?.localeCompare(b.AsOnDate),
      render: (AsOnDate ) => (
        <Text>{AsOnDate ? handleDateFormatter(AsOnDate) : "--"}</Text>
      ),
    },
    {
      title: "Branch Name",
      dataIndex: "BranchName",
      sorter: (a, b) => a.BranchName?.localeCompare(b.BranchName),
      render: (BranchName) => <Text>{BranchName || "--"}</Text>,
    },
    {
      title:"Total Promise",
      dataIndex: "TotalPromise",
      sorter: (a, b) => (a.TotalPromise || 0) - (b.TotalPromise || 0),
      render: (TotalPromise) => (
        <Text>{TotalPromise ?? "-"}</Text>
      ),
    },
    {
      title: "Total Promised Amount",
      dataIndex: "TotalPromisedAmount",
      sorter: (a, b) => (a.TotalPromisedAmount || 0) - (b.TotalPromisedAmount || 0),
      render: (TotalPromisedAmount) => (
        <Text className={Style.blue_text}>
          ₹ {formatAmount(TotalPromisedAmount || 0)}
        </Text>
      ),
    },
    {
      title: "Total Claim",
      dataIndex: "TotalDispute",
      sorter: (a, b) => (a.TotalDispute || 0) - (b.TotalDispute || 0),
      render: (TotalDispute) => <Text>{TotalDispute ?? "-"}</Text>,
    },
    {
      title:"Total Claim Amount",
      dataIndex: "TotalDisputeAmount",
      sorter: (a, b) => (a.TotalDisputeAmount || 0) - (b.TotalDisputeAmount || 0),
      render: (TotalDisputeAmount) => (
        <Text>₹ {formatAmount(TotalDisputeAmount || 0)}</Text>
      ),
    },
    {
      title:"Total Denials",
      dataIndex: "Total_Denials",
      sorter: (a, b) =>( a.Total_Denials || 0) - (b.Total_Denials || 0),
      render: (Total_Denials) => <Text>{Total_Denials}</Text>,
    },
    {
      title: "Total Already Paid",
      dataIndex: "Total_Already_Paid",
      sorter: (a, b) =>( a.Total_Already_Paid || 0) - (b.Total_Already_Paid || 0),
      render: (Total_Already_Paid) => <Text>{Total_Already_Paid}</Text>,
    },
    {
      title:"Total Already Paid Amount",
      dataIndex: "Total_Already_Paid_Amount",
      sorter: (a, b) => (a.Total_Already_Paid_Amount || 0)- (b.Total_Already_Paid_Amount ||0),
      render: (Total_Already_Paid_Amount) => (
        <Text>₹ {formatAmount(Total_Already_Paid_Amount || 0)}</Text>
      ),
    },
  ];

  const dataSource = Array.isArray(data) ? data.map((d, i) => ({key: i + 1,...d,})): [];
  
  useEffect(()=>{
    setData(responseData);
  },[responseData])
  return (
    <Table
      virtual
      className={Style.customTable}
      columns={columns}
      dataSource={dataSource}
      scroll={{
        x: 2000,
        y: 300,
      }}
      pagination={{
        showSizeChanger: false,
      }}
    />
  );
}

Response.propTypes={
  responseData : PropTypes.array
}
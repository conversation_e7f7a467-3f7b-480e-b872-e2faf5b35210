import { Table, Typography } from 'antd'
import React from 'react'
import Style from "./_style.module.scss";
import { formatAmount, formatDigits } from '../../../../constant';
import PropTypes from 'prop-types';

const {Text} = Typography;

export  function ScrubTable({
    isGraph, 
    isAccount, 
    tableData, 
    selectedState, 
    setSelectedState, 
    tableLevel, 
    setTableLevel,
    handleUpdateStateTable,
    handleGetCustomerDetails
    }) {
        
    // Handle State Click → Get Regions
    const handleStateClick = (stateId) => {
        setSelectedState(stateId);
        setTableLevel("region");
        handleUpdateStateTable({ stateId });
    };
    
    // Handle Region Click → Get Branches
    const handleRegionClick = (regionId) => {
        setTableLevel("branch");
        handleUpdateStateTable({ stateId:selectedState, regionId, tablewise:"Branch" });
    };

    const columns = [
        {
            title: 'Sr. No.',
            dataIndex: 'key',
            rowScope: 'row',
            width: 90,
            render: (_, data) => <Text>{data?.key}.</Text>
        },
        isGraph
        ?null
        :{
            title: 'State',
            dataIndex: 'State',
            render: (State, data) => (
                <Text
                className={Style.blueText}
                onClick={() =>handleStateClick(data?.State_id)}
              >
                {State || "--"}
              </Text>
            ),
        },
        ...(selectedState ?
          [{
            title: 'Region',
            dataIndex: 'Region',
            render: (Region, data) => (
                <Text
                className={Style.blueText}
                onClick={() => {
                    handleRegionClick(data?.Region_id)
                }}
              >
                {Region || "--"}
              </Text>
            ),
        }]
        : []),
        ...(tableLevel === "branch"
            ? [{
                title: "Branch",
                dataIndex: "Branch",
                render: (Branch, data) => (
                    <Text
                    className={Style.blueText}
                    // onClick={() => handleBranchClick(data?.Branch_id)}
                    >
                      {Branch || "--"}
                    </Text>
                ),
            }]
        : []),
        {
            title: 'Risk',
            children:[
            {
                title:"Very Low",
                dataIndex: "Very Low Risk",
                render: (value, record) => <Text 
                className={Style.lan} 
                style={{cursor:"pointer"}}
                onClick={()=> handleGetCustomerDetails({riskCategory:"Very Low Risk", state_id:record?.State_id, isRd:false})}>
                    {isAccount? formatDigits(value): `₹${formatAmount(value)}`}
                </Text>,
            },
            {
                title:"Low",
                dataIndex: "Low Risk",
                render: (value, record) => <Text 
                className={Style.lan} 
                style={{cursor:"pointer"}}
                onClick={()=> handleGetCustomerDetails({riskCategory:"Low Risk", state_id:record?.State_id, isRd:false})}>
                    {isAccount? formatDigits(value): `₹${formatAmount(value)}`}
                </Text>,
            },
            {
                title:"Medium",
                dataIndex: "Medium Risk",
                render: (value, record) => <Text 
                className={Style.lan} 
                style={{cursor:"pointer"}}
                onClick={()=> handleGetCustomerDetails({riskCategory:"Medium Risk", state_id:record?.State_id, isRd:false})}>
                    {isAccount? formatDigits(value): `₹${formatAmount(value)}`}
                </Text>,
            },
            {
                title:"High",
                dataIndex: "High Risk",
                render: (value, record) => <Text 
                className={Style.lan} 
                style={{cursor:"pointer"}}
                onClick={()=> handleGetCustomerDetails({riskCategory:"High Risk", state_id:record?.State_id, isRd:false})}>
                    {isAccount? formatDigits(value): `₹${formatAmount(value)}`}
                </Text>,
            },
            {
                title:"Very High",
                dataIndex: "Very High Risk",
                render: (value, record) => <Text 
                className={Style.lan} 
                style={{cursor:"pointer"}}
                onClick={()=> handleGetCustomerDetails({riskCategory:"Very High Risk", state_id:record?.State_id, isRd:false})}>
                    {isAccount? formatDigits(value): `₹${formatAmount(value)}`}
                </Text>,
            },
        ]},
        // {
        //     title: 'Total',
        //     dataIndex: 'total',
        //     render: (value) => <Text>{value}</Text>
        // },
    ].filter(Boolean);

    const dataSource = Array.isArray(tableData)
    ? tableData.map((data, i) => ({
        key: i + 1,
        ...data,
      }))
    : [];
    return (
    <Table
        bordered
        virtual
        className={Style.custom_table}
        columns={columns}
        dataSource={dataSource}
        scroll={{
            x: 1000,
            y: 460,
        }}
        pagination={{
            showSizeChanger:false
        }}
    />
  )
}
ScrubTable.propTypes={
    isGraph: PropTypes.bool, 
    isAccount: PropTypes.bool, 
    tableData: PropTypes.array, 
    selectedState: PropTypes.any, 
    setSelectedState: PropTypes.func, 
    tableLevel: PropTypes.string, 
    setTableLevel: PropTypes.func,
    handleUpdateStateTable: PropTypes.func,
    handleGetCustomerDetails: PropTypes.func
}

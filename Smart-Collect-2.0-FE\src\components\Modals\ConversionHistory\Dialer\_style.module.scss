@import url("../../../../index.css");

$dialer-bg: #fdffcf;
$dialer-color: #85aaff;
$response-bg: #db3939;

.dialer_container {
  .view_btn {
    color: var(--dark-blue) !important;
    border: none;
    outline: none;
    box-shadow: 1px 2px 2px 0px #00000040;
    font-weight: bold;
    font-size: 11px;
  }
  .summary_container {
    background-color: white;
    padding: 0 1rem;
    border-radius: 8px;
    .title {
      font-weight: bold;
      color: var(--dark-blue);
      font-family: "kanit",'Courier New', Courier, monospace;
      font-size: 13px;
    }
    .text {
      color: var(--dark-blue);
      font-family: "kanit",'Courier New', Courier, monospace;
      font-size: 12px;
    }
  }
  .date {
    color: var(--dark-blue);
    font-family: 'Kanit','Courier New', Courier, monospace;
    font-weight: 400;
  }
  .dialer_chat_box {
    background-color: $dialer-bg;
    padding: 1rem 0.5rem;
    border-radius: 10px;
  }
  .img {
    width: 11px;
  }
  .dialer_text {
    color: $dialer-color;
    font-family: 'Kanit','Courier New', Courier, monospace;
    line-height: 0;
  }
  .response_text {
    background-color: $response-bg;
    color: white;
    padding: 0.5rem 3rem;
    border-radius: 8px;
    font-family: 'Kanit','Courier New', Courier, monospace;
    font-weight: 400;
    font-size: 11px;
    text-align: center;
    width: 70%;
    margin: 0 auto;
  }

  audio {
    width: 80%;
    background-color: white;
    border-radius: 10px;
    padding: 0px;
    height: 45px;
  }

  audio::-webkit-media-controls-panel {
    background-color: white;
    border-radius: 0px;
  }

  audio::-webkit-media-controls-play-button {
    background-color: white;
    border-radius: 50%;
  }
  .belongs_text {
    font-size: 13px;
    color: var(--dark-blue);
    font-weight: 700;
  }
}

// Responsive
@media only screen and (max-width: 768px) {
  .dialer_container {
    .date {
      font-size: 11px;
    }
    .img {
      width: 10px;
    }
    .dialer_text {
      font-size: 11px;
    }
    .view_btn {
      padding: 0.5rem;
      font-size: 9px;
    }
    audio {
      width: 99%;
      height: 35px;
    }
    .audio_container {
      flex-direction: column;
    }
    .summary_container {
      .title {
        font-size: 11px;
      }
      .text {
        font-size: 9px;
      }
    }
    .response_text {
      font-size: 9px;
      width: 100%;
    }
  }
}

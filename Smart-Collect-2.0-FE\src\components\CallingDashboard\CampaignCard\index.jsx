import { Card, Flex, Typography } from "antd";
import Style from "./_style.module.scss";
import { SMART_COLLECT_MENU_IDS } from "../../../constant";
import { Link } from "react-router";
import PropTypes from "prop-types";

const {Text} = Typography;

export function CampaignCard({name, pendingTask, completedTask }) {
  return (
    <Link to={`/${SMART_COLLECT_MENU_IDS.AGENT_CALLING_DASHBOARD}/${SMART_COLLECT_MENU_IDS.AGENT_CALLING_SUMMARY}/${name}`}>
        <Card title={name} variant="borderless" className={Style.card_container}>
            <Flex justify="space-between">
                <Text className={Style.text}>Total tasks </Text>
                <Text className={Style.count}>{pendingTask + completedTask}</Text>
            </Flex>

            <Flex justify="space-between">
                <Text className={Style.text}>Pending tasks </Text>
                <Text className={Style.count}>{pendingTask}</Text>
            </Flex>

            <Flex justify="space-between">
                <Text className={Style.text}>Completed tasks </Text>
                <Text className={Style.count}>{completedTask}</Text>
            </Flex>
        </Card>
    </Link>
  )
}

// Define the prop types
CampaignCard.propTypes={
    name: PropTypes.string,
    pendingTask: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    completedTask: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
}
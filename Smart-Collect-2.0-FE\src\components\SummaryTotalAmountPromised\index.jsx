import { Flex, message, Table, Typography } from "antd";
import React, { useEffect, useState } from "react";
import AppHeader from "../AppHeader";
import {
  ALL_CACHE_NAMES,
  formatAmount,
  formattedTextToCapitalized,
  handleDateFormatter,
  handleDownloadTable,
  LOCAL_KEYS,
  SUMMARY_FILTERS,
} from "../../constant";
import DOWNLOAD_IMG from "../../assets/Images/download.svg";
import Style from "./_style.module.scss";
import { AXIOS } from "../../apis/ho-Instance";
import ApplicationLoader from "../ApplicationLoader";
import {
  getCacheKey,
  getFromCache,
  getPostCacheKey,
  storeToCache,
} from "../../utils/cacheHelper";
import { encryptBody, isSameEncryptedBody } from "../../utils/crypto";

const { Text, Title } = Typography;
const CACHE_NAME = ALL_CACHE_NAMES.CAMPAIGN_SUMMARY_DASHBOARD;

export default function SummaryTotalAmountPromised() {
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const storedFilter = localStorage.getItem(SUMMARY_FILTERS.summaryFilter);
  const storedCamp = localStorage.getItem(SUMMARY_FILTERS.selectedCampaignId);

  const handleFetchCampsAndBranchIds = async () => {
    try {
      const storedCampaigns = localStorage.getItem(
        SUMMARY_FILTERS.summaryCampaignIds
      );
      const storedBranches = localStorage.getItem(
        SUMMARY_FILTERS.summaryBranchIds
      );

      const campaignIds = storedCampaigns ? JSON.parse(storedCampaigns) : [];
      const branchIds = storedBranches ? JSON.parse(storedBranches) : [];

      return { campaignIds, branchIds };
    } catch (error) {
      console.error(
        "Error reading campaign/branch IDs from localStorage",
        error
      );
      return { campaignIds: [], branchIds: [] };
    }
  };

  // Fetch API data and set state
  const getTotalPromiseAmount = async () => {
    setIsLoading(true);
    const cacheKey = getCacheKey({
      endpoint: "campaign-total-amount-promised/",
    });
    // Check cache
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setData(cachedData.total_amount_promised);
      setIsLoading(false);
    }
    const { campaignIds, branchIds } = await handleFetchCampsAndBranchIds();
    try {
      const response = await AXIOS.get("campaign-total-amount-promised/", {
        params: {
          campaign_mstids: campaignIds,
          branch_id: branchIds,
        },
      });
      if (
        response.status === 200 &&
        response.data.total_amount_promised?.length
      ) {
        // Store data in cache
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
        setData(response.data.total_amount_promised);
      } else {
        setData([]);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // api after the Filtered aplied
  const getTotalPromiseFiltredAmount = async ({
    fromDate,
    toDate,
    BranchName,
    disbursementID,
    campaignMstIds,
  }) => {
    setIsLoading(true);
    const { campaignIds, branchIds } = await handleFetchCampsAndBranchIds();

    const body = {
      fromDate,
      toDate,
      BranchName: BranchName?.length ? BranchName : branchIds,
      dpdfrom: 0,
      dpdto: 10000,
      DisbursementID: disbursementID,
      campaign_mstids: campaignMstIds ? [campaignMstIds] : campaignIds,
    };
    const cacheKey = getPostCacheKey({
      endpoint: "campaign-total-amount-promised/",
      body,
    });
    const encryptedOldBody = localStorage.getItem(LOCAL_KEYS.HO_CHANNEL_FILTER);
    const sameBody = isSameEncryptedBody({ newBody: body, encryptedOldBody });

    if (sameBody) {
      const cachedData = await getFromCache({
        cacheName: CACHE_NAME,
        key: cacheKey,
      });
      if (cachedData) {
        setData(cachedData.total_amount_promised);
        setIsLoading(false);
      }
    }
    try {
      const response = await AXIOS.post(
        "campaign-total-amount-promised/",
        body
      );
      // Map data to match table columns
      if (
        response.data.total_amount_promised?.length &&
        response.status === 200
      ) {
        const encrypted = encryptBody(body);
        if (encrypted) {
          localStorage.setItem(LOCAL_KEYS.HO_CHANNEL_FILTER, encrypted);
        }
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
        setData(response.data.total_amount_promised);
      } else {
        setData([]);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Apply filter and fetch data
  const applyFilters = async ({
    fromDate,
    toDate,
    BranchName,
    disbursementID,
  }) => {
    await getTotalPromiseFiltredAmount({
      fromDate: fromDate,
      toDate: toDate,
      BranchName,
      disbursementID,
      campaignMstIds: parseInt(storedCamp),
    });
  };

  // Handle clear filter
  const ClearFilters = () => {
    getTotalPromiseAmount();
    localStorage.removeItem(SUMMARY_FILTERS.summaryFilter);
    localStorage.removeItem(LOCAL_KEYS.HO_CHANNEL_FILTER);
  };

  // Handle downloading
  const handleDownload = async () => {
    if (data.length === 0 || !data) {
      return message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: "Summary-Total-Amount-Promised",
        worksheetName: "Summary-Total-Amount-Promised",
        tableData: data,
      });
    }
  };

  // On mount, check localStorage for saved filters
  useEffect(() => {
    if (storedFilter || storedCamp) {
      const { fromDate, toDate, BranchName, disbursementID } = storedFilter
        ? JSON.parse(storedFilter)
        : {
            fromDate: null,
            toDate: null,
            BranchName: [],
            disbursementID: "",
          };
      const parsedCamp = storedCamp ? parseInt(storedCamp) : null;
      getTotalPromiseFiltredAmount({
        fromDate,
        toDate,
        BranchName,
        disbursementID,
        campaignMstIds: parsedCamp,
      });
    } else {
      getTotalPromiseAmount();
    }
  }, []);

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      sorter: (a, b) => (a.key ?? 0) - (b.key ?? 0),
      render: (_, { key }) => <Text>{key}.</Text>,
    },
    {
      title: "Customer Id",
      dataIndex: "CustomerID",
      sorter: (a, b) => Number(a.CustomerID ?? 0) - Number(b.CustomerID ?? 0),
      render: (CustomerId) => <Text>{CustomerId}</Text>,
    },
    {
      title: "Customer Name",
      dataIndex: "CustomerName",
      sorter: (a, b) =>
        String(a.CustomerName ?? "").localeCompare(
          String(b.CustomerName ?? "")
        ),
      render: (CustomerName) => (
        <Text className={Style.text}>
          {CustomerName ? formattedTextToCapitalized(CustomerName) : "-"}
        </Text>
      ),
    },
    {
      title: "Branch Name",
      dataIndex: "BranchName",
      sorter: (a, b) =>
        String(a.BranchName ?? "").localeCompare(String(b.BranchName ?? "")),
      render: (value) => (
        <Text className={Style.text}>
          {value ? formattedTextToCapitalized(value) : "-"}
        </Text>
      ),
    },
    {
      title: "Disbursement ID",
      dataIndex: "DisbursementID",
      sorter: (a, b) =>
        String(a.DisbursementID ?? "").localeCompare(
          String(b.DisbursementID ?? "")
        ),
      render: (value) => <Text>{value || "-"}</Text>,
    },
    {
      title: "Loan Type​",
      dataIndex: "LoanType",
      sorter: (a, b) =>
        String(a.LoanType ?? "").localeCompare(String(b.LoanType ?? "")),
      render: (value) => <Text>{value || "-"}</Text>,
    },
    {
      title: "EMI Amount​",
      dataIndex: "EMIAmount",
      sorter: (a, b) => Number(a.EMIAmount ?? 0) - Number(b.EMIAmount ?? 0),
      render: (value) => <Text>{formatAmount(value || 0)}</Text>,
    },
    {
      title: "Overdue Amount",
      dataIndex: "OverDueAmt",
      sorter: (a, b) => Number(a.OverDueAmt ?? 0) - Number(b.OverDueAmt ?? 0),
      render: (value) => <Text>{formatAmount(value || 0)}</Text>,
    },
    {
      title: "Response Date​",
      dataIndex: "ResponseDate",
      sorter: (a, b) =>
        new Date(a.ResponseDate ?? 0).getTime() -
        new Date(b.ResponseDate ?? 0).getTime(),
      render: (value) => (
        <Text>{value ? handleDateFormatter(value) : "--"}</Text>
      ),
    },
    {
      title: "Response Channel​",
      dataIndex: "ResponseChannel",
      sorter: (a, b) =>
        String(a.ResponseChannel ?? "").localeCompare(
          String(b.ResponseChannel ?? "")
        ),
      render: (value) => <Text>{value || "-"}</Text>,
    },
    {
      title: "Promise Date",
      dataIndex: "PromiseDate",
      sorter: (a, b) =>
        new Date(a.PromiseDate ?? 0).getTime() -
        new Date(b.PromiseDate ?? 0).getTime(),
      render: (value) => (
        <Text>{value ? handleDateFormatter(value) : "--"}</Text>
      ),
    },
    {
      title: "Promise Amount",
      dataIndex: "PromiseAmount",
      sorter: (a, b) =>
        Number(a.PromiseAmount ?? 0) - Number(b.PromiseAmount ?? 0),
      render: (value) => (
        <Text className={Style.blueText}>{formatAmount(value || 0)}</Text>
      ),
    },
    {
      title: "Promise Status​",
      dataIndex: "Status",
      sorter: (a, b) =>
        String(a.Status ?? "").localeCompare(String(b.Status ?? "")),
      render: (value) => <Text>{value || "-"}</Text>,
    },
  ];

  const dataSource = Array.isArray(data)
    ? data.map((data, i) => ({
        key: i + 1,
        ...data,
      }))
    : [];
  return (
    <Flex gap={20} vertical>
      <AppHeader
        title={"Total Amount Promised by Customers"}
        ClearFilters={ClearFilters}
        applyFilters={applyFilters}
        isDashboardOtherPages={true}
      />

      <Flex gap={20} vertical>
        <Flex justify="end">
          <button className={Style.download_button} onClick={handleDownload}>
            <img src={DOWNLOAD_IMG} alt="download-button" />
          </button>
        </Flex>

        {/* <Spin tip={"Loading..."} spinning={isLoading}> */}
        {isLoading ? (
          <ApplicationLoader />
        ) : (
          <Table
            bordered
            virtual
            className={Style.CustomTable}
            columns={columns}
            dataSource={dataSource}
            scroll={{
              x: 2000,
              y: 360,
            }}
            pagination={{
              showSizeChanger: false,
            }}
          />
        )}
        {/* </Spin> */}
      </Flex>
    </Flex>
  );
}

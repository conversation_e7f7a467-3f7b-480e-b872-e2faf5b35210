import axios from "axios";
import CryptoJ<PERSON> from "crypto-js";
import { ALL_CACHE_NAMES } from "../constant";
import { clearCache } from "../utils/cacheHelper";

// Get secret key from .env
const SECRET_KEY = import.meta.env.VITE_MARKYTICS_ENCRYPT_KEY;
// Get base URL from .env
const BASE_URL = import.meta.env.VITE_MARKYTICS_BASE_URL;

// Encrypt function
const encryptData = (data) => {
  const key = CryptoJS.enc.Utf8.parse(SECRET_KEY);
  const encrypted = CryptoJS.AES.encrypt(
    JSON.stringify(data),
    key,
    {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    }
  );
  return encrypted.toString();
};

// Decrypt function
const decryptData = (encryptedData) => {
  const key = CryptoJS.enc.Utf8.parse(SECRET_KEY);

  const decrypted = CryptoJS.AES.decrypt(encryptedData, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  });

  const decryptedStr = decrypted.toString(CryptoJS.enc.Utf8);
  if (!decryptedStr) throw new Error("Empty decrypted string");

  const parsed = JSON.parse(decryptedStr);
  return parsed;
};

// Create an instance with base URL and headers
export const AXIOS = axios.create({
  baseURL: BASE_URL,
});

// Add a request interceptor to attach the token
AXIOS.interceptors.request.use(
  (config) => {
    const user = JSON.parse(localStorage.getItem("user"));
    const token = user?.access;
    const contentType = config.headers['Content-Type'];
    if (token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${token}`,
      };
    }
    const url = config.url || "";
    const shouldSkipEncryption =
      url == "dashboard/" ||
      url.includes("delete-disbursement/") ||
      url.includes("accountsummaryreport/") ||
      contentType?.includes("multipart/form-data");

    if (!shouldSkipEncryption && config?.data) {
      config.data = encryptData(config.data); // Encrypt outgoing data
    }
    return config;
  },
  (error) => {
    return Promise.reject(error instanceof Error ? error : new Error(error));
  }
);

// Response Interceptor: Handle token expiration
AXIOS.interceptors.response.use(
  (response) => {
    if (response?.data) {
      const url = response?.config?.url || "";
      const contentType = response?.headers["content-type"] || "";
      const shouldSkipDecryption =
        url === "dashboard/" ||
        url.includes("delete-disbursement/") ||
        url.includes("accountsummaryreport/") ||
        contentType?.includes("multipart/form-data");
      if (!shouldSkipDecryption && response?.data) {
        try {
          const decrypted = decryptData(response.data);
          response.data = decrypted // decrypt response data
        } catch (e) {
          console.error("Decryption failed:", e);
        }
      }
    }
    return response;
  },
  (error) => {
    const currentPath = window.location.pathname;
    if (
      error.response &&
      error.response.status === 401 &&
      currentPath !== "/" &&
      currentPath !== "/forget-password"
    ) {
      // Token expired or unauthorized, trigger logout
      handleLogout();
    }
    return Promise.reject(error instanceof Error ? error : new Error(error));
  }
);

// Logout function to clear localStorage and redirect
export const handleLogout = async () => {
  localStorage.clear(); // Clear user info
  await Promise.all(Object.values(ALL_CACHE_NAMES).map((cacheName) => clearCache({ cacheName }))); // clear all cache
  window.location.href = "/"; // Redirect to login page
};

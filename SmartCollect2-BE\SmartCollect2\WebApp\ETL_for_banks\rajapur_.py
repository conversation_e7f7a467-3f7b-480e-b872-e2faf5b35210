import pandas as pd
import numpy as np
from datetime import datetime

# Read the Excel files
file_18_june = pd.read_excel(r"C:\Users\<USER>\Downloads\18062025.xlsx")
file_02_july = pd.read_excel(r"C:\Users\<USER>\Downloads\01072025.xlsx")

print("18 June file shape:", file_18_june.shape)
print("02 July file shape:", file_02_july.shape)
print("\n18 June file columns:", file_18_june.columns.tolist())
print("02 July file columns:", file_02_july.columns.tolist())

# Display first few rows to understand the data structure
print("\n18 June file sample:")
print(file_18_june.head())
print("\n02 July file sample:")
print(file_02_july.head())

# Create a composite key for matching records
file_18_june['composite_key'] = file_18_june['CUST_CODE'].astype(str) + '_' + file_18_june['DisbursementID'].astype(str)
file_02_july['composite_key'] = file_02_july['CUST_CODE'].astype(str) + '_' + file_02_july['DisbursementID'].astype(str)

# Create dictionaries for quick lookup
june_dict = file_18_june.set_index('composite_key')['TOTAL_AMT_P_I'].to_dict()
july_records = file_02_july.set_index('composite_key').to_dict('index')

# Process each record in July file
results = []

for composite_key, july_record in july_records.items():
    july_amount = july_record['TOTAL_AMT_P_I']
    
    if composite_key in june_dict:
        # Record exists in both files - calculate difference
        june_amount = june_dict[composite_key]
        amount_difference = july_amount - june_amount
        record_status = "Found in both files"
    else:
        # Record only exists in July file - keep as is
        amount_difference = july_amount
        record_status = "Only in July file"
    
    # Create result record
    result_record = {
        'CUST_CODE': july_record['CUST_CODE'],
        'DisbursementID': july_record['DisbursementID'],
        'TOTAL_AMT_P_I_July': july_amount,
        'TOTAL_AMT_P_I_June': june_dict.get(composite_key, 0),
        'Amount_Difference': amount_difference,
        'Record_Status': record_status
    }
    
    # Add CollectionDate if it exists
    if 'CollectionDate' in july_record:
        result_record['CollectionDate'] = july_record['CollectionDate']
    
    # Add any other relevant columns from July file
    for col in file_02_july.columns:
        if col not in ['composite_key', 'CUST_CODE', 'DisbursementID', 'TOTAL_AMT_P_I']:
            result_record[f'July_{col}'] = july_record.get(col)
    
    results.append(result_record)

# Create DataFrame from results
result_df = pd.DataFrame(results)

# Find the latest CollectionDate if the column exists
if 'CollectionDate' in result_df.columns:
    # Convert to datetime if it's not already
    result_df['CollectionDate'] = pd.to_datetime(result_df['CollectionDate'], errors='coerce')
    latest_date = result_df['CollectionDate'].max()
    print(f"\nLatest Collection Date found: {latest_date}")
    
    # Add a column indicating if this is the latest date
    result_df['Is_Latest_Date'] = result_df['CollectionDate'] == latest_date
else:
    print("\nCollectionDate column not found in the data")

# Display summary statistics
print(f"\nSummary Statistics:")
print(f"Total records processed: {len(result_df)}")
print(f"Records found in both files: {len(result_df[result_df['Record_Status'] == 'Found in both files'])}")
print(f"Records only in July file: {len(result_df[result_df['Record_Status'] == 'Only in July file'])}")
print(f"Total amount difference: {result_df['Amount_Difference'].sum():.2f}")

# Display sample of results
print(f"\nSample results:")
print(result_df.head(10))

# Save to CSV
output_filename = f"comparison_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
result_df.to_csv(output_filename, index=False)
print(f"\nResults saved to: {output_filename}")

# Additional analysis
print(f"\nAdditional Analysis:")
if 'Amount_Difference' in result_df.columns:
    positive_diff = result_df[result_df['Amount_Difference'] > 0]
    negative_diff = result_df[result_df['Amount_Difference'] < 0]
    zero_diff = result_df[result_df['Amount_Difference'] == 0]
    
    print(f"Records with positive difference (increase): {len(positive_diff)}")
    print(f"Records with negative difference (decrease): {len(negative_diff)}")
    print(f"Records with no change: {len(zero_diff)}")
    
    if len(positive_diff) > 0:
        print(f"Average positive difference: {positive_diff['Amount_Difference'].mean():.2f}")
    if len(negative_diff) > 0:
        print(f"Average negative difference: {negative_diff['Amount_Difference'].mean():.2f}")

# Show records with largest differences
print(f"\nTop 5 records with largest positive differences:")
top_increases = result_df.nlargest(5, 'Amount_Difference')[['CUST_CODE', 'DisbursementID', 'Amount_Difference', 'Record_Status']]
print(top_increases)

print(f"\nTop 5 records with largest negative differences:")
top_decreases = result_df.nsmallest(5, 'Amount_Difference')[['CUST_CODE', 'DisbursementID', 'Amount_Difference', 'Record_Status']]
print(top_decreases)


# import pandas as pd
# import psycopg2
 
# # Database connection
# conn = psycopg2.connect(
#     dbname="SmartCollect2",
#     user="postgres",
#     password="Markytics12345",
#     host="*************",
#     port="5432"
# )
 
# query = """
#     select
# 	distinct r.disbursementid,
# 	r.customername,
# 	r.extracolumn3 as "Original Overdue Amount",
# 	r.collectedamount as "Recovery",
# 	r.collectiondate,
# 	r.overdueamount as "Calculated Overdue"
# from
# 	rawfile r
# where
# 	r.bankmstid = 30
# 	and r.inserted_date = current_date
# """
# # query = """
# #     select * from public."AccountSummary"
# # where "BankMstID"=13
# # """
 
# # Fetch data into pandas DataFrame
# df = pd.read_sql(query, conn)
 
# # Export to Excel
# df.to_excel('sindhudhurg.xlsx', index=False)
 
# # Close connection
# conn.close()
import { Button, Flex, InputNumber, message, Modal, Switch, Typography } from 'antd';
import UPDATE_IMG from "../../../assets/Images/Frame-1.png";
import Style from "./_update.module.scss";
import React, { useEffect, useState } from 'react';
import { AXIOS } from '../../../apis/ho-Instance';
import PropTypes from 'prop-types';

const {Text, Title} = Typography;

export default function UpdateNumberModal({
  loanMstId,
  modalStatus = false, 
  handleSubmit, 
  handleCancel,
}) {
  const [messageApi, contextHolder] = message.useMessage();
  const [isUpdateNumberModalOpen, setIsUpdateNumberModalOpen] = useState(false);
  const [number, setNumber] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isValidNumber, setIsValidNumber] = useState(false);

  const formatIndianPhoneNumber = (value) => {
    if (!value) return "";

    // Convert to string
    const stringValue = String(value);

    if (stringValue.length > 10){
      messageApi.error("Please, Give Right Formatted Mobile Number");
      return "";
    }
  
    // Remove all non-numeric characters
    const cleaned = stringValue?.replace(/\D/g, "");
  
    // Ensure max length of 10 digits (excluding country code)
    const trimmed = cleaned.slice(0, 10);
  
    // Format as +91 XXXXX XXXXX
    return trimmed.replace(/(\d{5})(\d{5})/, "$1$2");
  };

  // update number
  const handleDone = async()=>{
    if(!isValidNumber && (!number || number === "" || number.toString()?.length !== 10)){
      messageApi.error("Please, Enter Valid Mobile Number");
      return;
    };
    setIsLoading(true);
    try {
      const response = await AXIOS.post('update_wrong_number/',{
        MobileNumber: Number(number),
        LoanMstID: loanMstId,
        toggle_value: isValidNumber ? "true": "false"
      })
      if(response.status ===200){
        messageApi.success("Mobile Number Updated Successfully!");
        handleCross();
        setNumber(null);
      }
    } catch (error) {
      console.log("Error in updating phone", error);
      messageApi.error("Mobile Number Not Updated!");
    }finally{
      setIsLoading(false);
    }
  }

  // hide the modal
  const handleOk = () => {
    setIsUpdateNumberModalOpen(false);
  };

  // hide the modal
  const handleCross = () => {
    setIsUpdateNumberModalOpen(false);
    handleCancel();
  };

  useEffect(()=>{
    setIsUpdateNumberModalOpen(modalStatus);
  },[modalStatus]);
  return (
  <>
  {contextHolder}  
  <Modal 
    centered
    className={Style.update_modal} 
    title={
      <Flex align='center' gap={5} justify='center'>
        <div className={Style.icon_img}>
          <img src={UPDATE_IMG} alt='icon'/>
        </div>
        <Title level={4} className={Style.update_title}>Update Mobile Number</Title>
      </Flex>
    }
    open={isUpdateNumberModalOpen} 
    onOk={handleOk} 
    onCancel={handleCross}
    footer={null}
    >
      <Flex vertical gap={10}>   
        {/* Customer  */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Customer Number is valid:</Text>
          <Switch
            value={isValidNumber}
            onChange={(value) => setIsValidNumber(value)}
          />
        </Flex>
        {/* Alternate */}
       {!isValidNumber &&
        <Flex justify='space-between' align='center' className={Style.container}>
            <Text className={Style.text}>Alternate Mobile Number:</Text>
            <InputNumber 
              min={0}
              value={number}
              placeholder="Enter" 
              onChange={(value) => {
                const formattedNumber = formatIndianPhoneNumber(value);
                setNumber(formattedNumber);
              }}
              className={Style.custom_input}/>
        </Flex> 
      }     
        <Flex justify='center'>
          <Button 
          loading={isLoading}
          className={Style.update_button} 
          onClick={handleDone}>
            Submit
          </Button>
        </Flex>
      </Flex>
  </Modal>
  </>
  )
}

UpdateNumberModal.propTypes={
  loanMstId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  modalStatus: PropTypes.bool, 
  handleSubmit: PropTypes.func, 
  handleCancel: PropTypes.func,
}
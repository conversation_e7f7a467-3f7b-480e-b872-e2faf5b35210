import pandas as pd
import numpy as np
from sqlalchemy import create_engine, update, MetaData, Table, text
from datetime import datetime, timedelta
import os
import math
import re

# Database configuration
DATABASE = "SmartCollect2"
USERNAME = "postgres"
PASSWORD = "Markytics12345"
HOST = "*************"
PORT = 5432

class DatabaseManager:
    """Database manager class to handle single engine instance and connections"""
    
    def __init__(self):
        self.engine = None
    
    def initialize_engine(self):
        """Initialize the database engine once"""
        if self.engine is None:
            # Use environment variables if available, otherwise fall back to constants
            db_user = os.environ.get('DATABASE_USER', USERNAME)
            db_password = os.environ.get('DATABASE_PASSWORD', PASSWORD)
            db_host = os.environ.get('DATABASE_HOST', HOST)
            db_port = os.environ.get('DATABASE_PORT', PORT)
            db_name = os.environ.get('DATABASE_NAME', DATABASE)
            
            connection_string = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
            self.engine = create_engine(connection_string)
            print("Database engine initialized successfully!")
        return self.engine
    
    def get_engine(self):
        """Get the engine instance, initialize if not already done"""
        if self.engine is None:
            self.initialize_engine()
        return self.engine

class CustomerMobileUpdater:
    """Class to handle customer mobile number updates"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.engine = db_manager.get_engine()
    
    def clean_mobile_number(self, mobile_number):
        """Clean mobile number by removing spaces and leading zeros"""
        if pd.isna(mobile_number) or mobile_number == '' or mobile_number is None:
            return ''
        
        # Convert to string
        mobile_str = str(mobile_number)
        
        # Remove all spaces
        mobile_str = mobile_str.replace(' ', '')
        
        # Remove any non-digit characters except + (for country codes)
        mobile_str = re.sub(r'[^\d+]', '', mobile_str)
        
        # Handle leading zeros - remove them unless it's a country code
        if mobile_str.startswith('0') and not mobile_str.startswith('+'):
            mobile_str = mobile_str.lstrip('0')
        
        # If all zeros were removed and string is empty, return empty
        if not mobile_str:
            return ''
        
        return mobile_str
    
    def read_excel_data(self, file_path):
        """Read customer data from Excel file"""
        try:
            df = pd.read_excel(file_path)
            print(f"Excel file loaded successfully with {len(df)} records")
            print(f"Columns in Excel: {list(df.columns)}")
            
            # Clean mobile numbers in Excel data
            # Handle both possible column names for mobile number
            mobile_col = None
            if 'Mobile No' in df.columns:
                mobile_col = 'Mobile No'
            elif 'MobileNumber' in df.columns:
                mobile_col = 'MobileNumber'
            
            if mobile_col:
                print(f"Cleaning mobile numbers from Excel column '{mobile_col}'...")
                original_sample = df[mobile_col].head(10).tolist()
                df[mobile_col] = df[mobile_col].apply(self.clean_mobile_number)
                cleaned_sample = df[mobile_col].head(10).tolist()
                
                print("Sample mobile number cleaning:")
                for i, (orig, clean) in enumerate(zip(original_sample, cleaned_sample)):
                    if orig != clean:
                        print(f"  {i+1}. '{orig}' -> '{clean}'")
            
            return df
        except Exception as e:
            print(f"Error reading Excel file: {e}")
            return None
    
    def get_customer_data_from_db(self):
        """Fetch customer data from DisbursementFile table"""
        try:
            query = 'SELECT "CustomerId", "Mobile No" FROM "DisbursementFile"'
            df_db = pd.read_sql(query, self.engine)
            print(f"Database records fetched: {len(df_db)}")
            
            # Clean mobile numbers from database as well for comparison
            print("Cleaning mobile numbers from database...")
            df_db['Mobile No'] = df_db['Mobile No'].apply(self.clean_mobile_number)
            
            return df_db
        except Exception as e:
            print(f"Error fetching data from database: {e}")
            return None
    
    def compare_and_prepare_updates(self, df_excel, df_db):
        """Compare Excel data with database and prepare update records"""
        
        # Determine column names for mobile number and customer ID
        # Excel file column mapping
        excel_mobile_col = 'Mobile No' if 'Mobile No' in df_excel.columns else 'MobileNumber'
        excel_customer_col = 'CustomerId' if 'CustomerId' in df_excel.columns else 'CustomerID'
        
        # Standardize column names for processing
        if excel_mobile_col != 'Mobile No':
            df_excel = df_excel.rename(columns={excel_mobile_col: 'Mobile No'})
        if excel_customer_col != 'CustomerId':
            df_excel = df_excel.rename(columns={excel_customer_col: 'CustomerId'})
        
        # Clean CustomerID in Excel data - remove leading single quote if present
        df_excel['CustomerId'] = df_excel['CustomerId'].astype(str)
        df_excel['CustomerId'] = df_excel['CustomerId'].str.lstrip("'")  # Remove leading single quote
        
        # Ensure CustomerID columns are of same type for comparison
        df_db['CustomerId'] = df_db['CustomerId'].astype(str)
        
        # Merge dataframes on CustomerId
        merged_df = pd.merge(
            df_excel, 
            df_db, 
            on='CustomerId', 
            how='inner',
            suffixes=('_excel', '_db')
        )
        
        # Identify records where mobile numbers are different
        # Handle NaN values in comparison
        merged_df['Mobile No_excel'] = merged_df['Mobile No_excel'].fillna('')
        merged_df['Mobile No_db'] = merged_df['Mobile No_db'].fillna('')
        
        # Convert to string for comparison
        merged_df['Mobile No_excel'] = merged_df['Mobile No_excel'].astype(str)
        merged_df['Mobile No_db'] = merged_df['Mobile No_db'].astype(str)
        
        # Find records that need updates (different mobile numbers and Excel has non-empty number)
        updates_needed = merged_df[
            (merged_df['Mobile No_excel'] != merged_df['Mobile No_db']) &
            (merged_df['Mobile No_excel'] != '') &
            (merged_df['Mobile No_excel'] != 'nan')
        ].copy()
        
        # Find records that match (no update needed)
        no_updates = merged_df[
            merged_df['Mobile No_excel'] == merged_df['Mobile No_db']
        ].copy()
        
        # Find records with empty mobile numbers in Excel
        empty_mobile_excel = merged_df[
            (merged_df['Mobile No_excel'] == '') |
            (merged_df['Mobile No_excel'] == 'nan')
        ].copy()
        
        # Find CustomerIDs in Excel but not in database
        not_in_db = df_excel[
            ~df_excel['CustomerId'].isin(df_db['CustomerId'])
        ].copy()
        
        print(f"Records requiring updates: {len(updates_needed)}")
        print(f"Records already matching: {len(no_updates)}")
        print(f"Records with empty mobile in Excel: {len(empty_mobile_excel)}")
        print(f"Records not found in database: {len(not_in_db)}")
        
        return updates_needed, no_updates, not_in_db, empty_mobile_excel
    
    def create_excel_report(self, updates_needed, no_updates, not_in_db, empty_mobile_excel, output_path):
        """Create Excel report with comparison results"""
        try:
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # Sheet 1: Records that need updates
                if not updates_needed.empty:
                    update_report = updates_needed[['CustomerId', 'Mobile No_excel', 'Mobile No_db']].copy()
                    update_report.columns = ['CustomerId', 'New_MobileNumber', 'Current_MobileNumber']
                    update_report.to_excel(writer, sheet_name='Updates_Required', index=False)
                
                # Sheet 2: Records that match (no update needed)
                if not no_updates.empty:
                    match_report = no_updates[['CustomerId', 'Mobile No_excel']].copy()
                    match_report.columns = ['CustomerId', 'MobileNumber']
                    match_report.to_excel(writer, sheet_name='Already_Matching', index=False)
                
                # Sheet 3: Records not found in database
                if not not_in_db.empty:
                    not_found_report = not_in_db[['CustomerId', 'Mobile No']].copy()
                    not_found_report.to_excel(writer, sheet_name='Not_In_Database', index=False)
                
                # Sheet 4: Records with empty mobile numbers in Excel
                if not empty_mobile_excel.empty:
                    empty_report = empty_mobile_excel[['CustomerId', 'Mobile No_db']].copy()
                    empty_report.columns = ['CustomerId', 'Current_MobileNumber_DB']
                    empty_report.to_excel(writer, sheet_name='Empty_Mobile_Excel', index=False)
                
                # Sheet 5: Summary
                summary_data = {
                    'Category': ['Updates Required', 'Already Matching', 'Not in Database', 'Empty Mobile in Excel'],
                    'Count': [len(updates_needed), len(no_updates), len(not_in_db), len(empty_mobile_excel)]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='Summary', index=False)
            
            print(f"Excel report created successfully: {output_path}")
            return True
        except Exception as e:
            print(f"Error creating Excel report: {e}")
            return False
    
    def validate_mobile_number(self, mobile_number):
        """Validate if mobile number format is reasonable"""
        if not mobile_number or mobile_number == '':
            return False, "Empty mobile number"
        
        # Check if it's all digits (after cleaning)
        if not mobile_number.replace('+', '').isdigit():
            return False, "Contains non-digit characters"
        
        # Check length (typically mobile numbers are 7-15 digits)
        digit_count = len(mobile_number.replace('+', ''))
        if digit_count < 7 or digit_count > 15:
            return False, f"Invalid length: {digit_count} digits"
        
        return True, "Valid"
    
    def update_mobile_numbers(self, updates_needed, batch_size=100, dry_run=True):
        """Update mobile numbers in the DisbursementFile table"""
        if updates_needed.empty:
            print("No updates required.")
            return True
        
        try:
            updated_count = 0
            failed_updates = []
            validation_failures = []
            
            # Validate mobile numbers before updating
            print("Validating mobile numbers...")
            for _, row in updates_needed.iterrows():
                is_valid, message = self.validate_mobile_number(row['Mobile No_excel'])
                if not is_valid:
                    validation_failures.append({
                        'CustomerId': row['CustomerId'],
                        'MobileNumber': row['Mobile No_excel'],
                        'Reason': message
                    })
            
            if validation_failures:
                print(f"Warning: {len(validation_failures)} mobile numbers failed validation:")
                for failure in validation_failures[:5]:  # Show first 5
                    print(f"  CustomerId {failure['CustomerId']}: '{failure['MobileNumber']}' - {failure['Reason']}")
                if len(validation_failures) > 5:
                    print(f"  ... and {len(validation_failures) - 5} more")
                
                proceed = input("Do you want to proceed with valid numbers only? (y/n): ").strip().lower()
                if proceed != 'y':
                    print("Update cancelled by user.")
                    return False
                
                # Filter out invalid numbers
                invalid_customer_ids = [f['CustomerId'] for f in validation_failures]
                updates_needed = updates_needed[~updates_needed['CustomerId'].isin(invalid_customer_ids)].copy()
                print(f"Proceeding with {len(updates_needed)} valid mobile numbers")
            
            # Process in batches
            for i in range(0, len(updates_needed), batch_size):
                batch = updates_needed.iloc[i:i+batch_size]
                
                with self.engine.begin() as conn:
                    for _, row in batch.iterrows():
                        try:
                            if not dry_run:
                                # Actual update query for DisbursementFile table
                                update_query = text("""
                                    UPDATE "DisbursementFile" 
                                    SET "Mobile No" = :mobile_number 
                                    WHERE "CustomerId" = :customer_id
                                """)
                                
                                result = conn.execute(update_query, {
                                    'mobile_number': row['Mobile No_excel'],
                                    'customer_id': row['CustomerId']
                                })
                                
                                if result.rowcount > 0:
                                    updated_count += 1
                                else:
                                    failed_updates.append(row['CustomerId'])
                            else:
                                # Dry run - just simulate
                                print(f"[DRY RUN] Would update CustomerId {row['CustomerId']}: "
                                      f"'{row['Mobile No_db']}' -> '{row['Mobile No_excel']}'")
                                updated_count += 1
                        
                        except Exception as e:
                            failed_updates.append(row['CustomerId'])
                            print(f"Failed to update CustomerId {row['CustomerId']}: {e}")
            
            if dry_run:
                print(f"\n[DRY RUN COMPLETE] Would update {updated_count} records")
                if validation_failures:
                    print(f"Validation failures: {len(validation_failures)} records would be skipped")
            else:
                print(f"\nUpdate complete: {updated_count} records updated successfully")
                if failed_updates:
                    print(f"Failed updates: {len(failed_updates)} records")
                    print(f"Failed CustomerIds: {failed_updates}")
                if validation_failures:
                    print(f"Validation failures: {len(validation_failures)} records were skipped")
            
            return len(failed_updates) == 0
            
        except Exception as e:
            print(f"Error during database update: {e}")
            return False

def main():
    """Main function to execute the mobile number update process"""
    
    # Initialize database manager
    db_manager = DatabaseManager()
    updater = CustomerMobileUpdater(db_manager)
    
    # File paths - UPDATE THESE PATHS AS NEEDED
    excel_file_path = r"C:\Users\<USER>\Downloads\VinayanaBasedata upto -28-06-2025 (1).xls"
    output_report_path = r"C:\Users\<USER>\Downloads\Customer_Mobile_Update_Report.xlsx"
    
    print("Starting Customer Mobile Number Update Process for DisbursementFile...")
    print("=" * 50)
    
    # Step 1: Read Excel data
    print("Step 1: Reading Excel file...")
    df_excel = updater.read_excel_data(excel_file_path)
    if df_excel is None:
        print("Failed to read Excel file. Exiting.")
        return
    
    # Validate required columns - check for both possible column names
    required_columns = []
    if 'CustomerId' in df_excel.columns:
        required_columns.append('CustomerId')
    elif 'CustomerID' in df_excel.columns:
        required_columns.append('CustomerID')
    else:
        print("Error: Neither 'CustomerId' nor 'CustomerID' column found in Excel")
        print(f"Available columns: {list(df_excel.columns)}")
        return
    
    if 'Mobile No' in df_excel.columns:
        required_columns.append('Mobile No')
    elif 'MobileNumber' in df_excel.columns:
        required_columns.append('MobileNumber')
    else:
        print("Error: Neither 'Mobile No' nor 'MobileNumber' column found in Excel")
        print(f"Available columns: {list(df_excel.columns)}")
        return
    
    print(f"Using columns: {required_columns}")
    
    # Clean CustomerID data - show before/after for verification
    customer_id_col = required_columns[0]  # First column is the customer ID column
    print(f"Sample CustomerIds before cleaning: {df_excel[customer_id_col].head().tolist()}")
    df_excel[customer_id_col] = df_excel[customer_id_col].astype(str).str.lstrip("'")
    print(f"Sample CustomerIds after cleaning: {df_excel[customer_id_col].head().tolist()}")
    
    # Step 2: Fetch database data
    print("\nStep 2: Fetching customer data from DisbursementFile table...")
    df_db = updater.get_customer_data_from_db()
    if df_db is None:
        print("Failed to fetch data from database. Exiting.")
        return
    
    # Step 3: Compare and prepare updates
    print("\nStep 3: Comparing data and identifying updates...")
    updates_needed, no_updates, not_in_db, empty_mobile_excel = updater.compare_and_prepare_updates(df_excel, df_db)
    
    # Step 4: Create Excel report
    print("\nStep 4: Creating Excel report...")
    report_created = updater.create_excel_report(
        updates_needed, no_updates, not_in_db, empty_mobile_excel, output_report_path
    )
    
    if not report_created:
        print("Failed to create Excel report.")
        return
    
    # Step 5: Ask user for confirmation before updating
    if not updates_needed.empty:
        print(f"\nStep 5: Ready to update {len(updates_needed)} records in DisbursementFile table")
        print("Would you like to:")
        print("1. Run a dry run (simulate updates without changing database)")
        print("2. Execute actual updates")
        print("3. Skip database updates")
        
        choice = input("Enter your choice (1/2/3): ").strip()
        
        if choice == '1':
            print("\nRunning dry run...")
            updater.update_mobile_numbers(updates_needed, dry_run=True)
        elif choice == '2':
            print("\nExecuting actual updates...")
            success = updater.update_mobile_numbers(updates_needed, dry_run=False)
            if success:
                print("All updates completed successfully!")
            else:
                print("Some updates failed. Check the logs above.")
        else:
            print("Database updates skipped.")
    else:
        print("\nNo database updates required.")
    
    print("\nProcess completed!")
    print(f"Report saved to: {output_report_path}")

if __name__ == "__main__":
    main()
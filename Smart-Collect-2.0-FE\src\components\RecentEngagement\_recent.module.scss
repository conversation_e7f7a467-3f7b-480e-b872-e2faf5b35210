.recent{
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1px;

  .indicator_box {
    border: 1px solid black;
    padding: 2px 4px;
    border-radius: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-bottom: 6px;
    margin-right: 0px;
    background-color: #e4f8f9;

  .dot {
    width: 13px;
    height: 13px;
    border-radius: 50%;
    }
  }
  .images {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px; /* Match indicator gap */
  }

  .icon_img {
    width: 14px;
    height: 14px;
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      }
  }
}

// Responsive View
@media screen and (max-width:1024px) {
  .recent{
    .indicator_box{
      margin-bottom: 0;
      .dot{
        width: 10px;
        height: 10px;
      }
    }
    .icon_img{
      width: 10px;
    }
  }
}
import { Spin, Table, Typography } from 'antd'
import React from 'react'
import Style from "./_style.module.scss";
import PropTypes from 'prop-types';

const {Text}  = Typography;

export function RegionTable({isRegionSwitchOn, data, isLoading, onRegionClick, onRangeClick}) {
    const columns = [
        {
            title: 'Region',
            dataIndex: 'Region',
            render: (Region, data) => <Text 
            style={{cursor:"pointer"}}
            onClick={()=>onRegionClick({
                regionId: data.Region_id,
                regionName: Region,
                tableShowName:"Branch",
            })}>{ Region || "-"}</Text>
        },
        {
            title: 'Total Branch',
            dataIndex: 'TotalBranch',
            render: (TotalBranch) => <Text>{TotalBranch || 0}</Text>
        },
        {
            title: 'Regular',
            dataIndex: '0',
            render: (Regular, {Region_id}) => <Text 
            style={{cursor:"pointer"}} 
            onClick={()=>{
                onRangeClick({regionId:Region_id, tablewise:"Region", range:"0" })
            }}>{Regular}</Text>
        },
        {
            title: '1 to 30',
            dataIndex: "1-30",
            render: (value, {Region_id}) => <Text 
            style={{cursor:"pointer"}} 
            onClick={()=>{
                onRangeClick({regionId:Region_id, tablewise:"Region", range:"1-30" })
            }}>{value}</Text>
           
        },
        {
            title: '31 to 60',
            dataIndex: "31-60",
            render: (value, {Region_id}) => <Text 
            style={{cursor:"pointer"}} 
            onClick={()=>{
                onRangeClick({regionId:Region_id, tablewise:"Region", range:"31-60" })
            }}>{value}</Text>
        },
        {
            title: '61 to 90',
            dataIndex: "61-90",
            render: (value, {Region_id}) => <Text 
            style={{cursor:"pointer"}} 
            onClick={()=>{
                onRangeClick({regionId:Region_id, tablewise:"Region", range:"61-90" })
            }}>{value}</Text>
        },
        {
            title: '91 to 180',
            dataIndex: "91-180",
            render: (value, {Region_id}) => <Text 
            style={{cursor:"pointer"}} 
            onClick={()=>{
                onRangeClick({regionId:Region_id, tablewise:"Region", range:"91-180" })
            }}>{value}</Text>
        },
        {
            title: '181 to 360',
            dataIndex:"181-360",
            render: (value, {Region_id}) => <Text 
            style={{cursor:"pointer"}} 
            onClick={()=>{
                onRangeClick({regionId:Region_id, tablewise:"Region", range:"181-360" })
            }}>{value}</Text>
        },
        {
            title: '360+',
            dataIndex: "360+",
            render: (value, {Region_id}) => <Text 
            style={{cursor:"pointer"}} 
            onClick={()=>{
                onRangeClick({regionId:Region_id, tablewise:"Region", range:"360+" })
            }}>{value}</Text>
        },
        {
            title: 'Total',
            dataIndex: "Total",
            render: (value) => <Text>{value}</Text>
        },
    ].filter(Boolean);

    const dataSource = Array.isArray(data)? data.filter((d)=> d.Region !== 0)?.map((data, i) => ({
        key: i+1,
        ...data
    })):[];
  return (
       <Spin tip={"Loading..."} spinning={isLoading}>
          <Table
              bordered
              virtual
              className={Style.CustomTable}
              columns={columns}
              dataSource={dataSource}
              scroll={{
                  x:800,
                  y: 360,
              }}
              pagination={false}
          />
      </Spin>
  )
}

RegionTable.propTypes={
    isRegionSwitchOn: PropTypes.bool,
    data: PropTypes.array, 
    isLoading: PropTypes.bool, 
    onRegionClick: PropTypes.func, 
    onRangeClick:PropTypes.func
}
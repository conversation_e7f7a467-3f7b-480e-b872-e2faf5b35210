@import url("../../../index.css");

$light-blue: #E3F5F6;

.card_container{
    background-color: $light-blue;
    border-top: 6.32px solid var(--dark-blue);
    border-radius: 10px;
    padding: 1rem;
    margin-top: 1rem;
    .image_container{
        width: 150px;
        height: 100px;
        img{
            object-fit: contain;
            width: 100%;
        }
    }
    .title{
        margin: 0;
        color: var(--dark-blue);
        font-weight: 400;
        font-size: 1.4rem;
        line-height: 1px;
        font-family: 'Kanit','Courier New', Courier, monospace;
    }
    .desc{
        color: #0F20508A;
        font-weight: 500;
        font-size: 16px;
        margin: 0;
        font-family: '<PERSON>ni<PERSON>','Courier New', Courier, monospace;
    }

    .icon_img{
        width: 15px;
        height: 15px;
        cursor: pointer;
        img{
            object-fit: contain;
            width: 100%;
        }
    }
}

// Responsive view
@media screen and (max-width:1024px) {
    .card_container{
      .title{
        font-size: 18px;
        white-space: pre;
      }
      .desc{
        font-size: 14px;
      }
    }
}

@media screen and (max-width:768px) {
    .card_container{
      .title{
        font-size: 14px;
        white-space: pre;
      }
      .desc{
        font-size: 12px;
      }
      .image_container{
        height: auto;
      }
    }
}
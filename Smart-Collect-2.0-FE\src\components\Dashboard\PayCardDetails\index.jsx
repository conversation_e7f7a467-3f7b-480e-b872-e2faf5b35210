import { Flex, Typography } from "antd";
import React from "react";
import Style from "./_pay.module.scss";
import { <PERSON> } from "react-router";
import READY_PAY_IMG from "../../../assets/Images/ready-pay.png";
import REFUSED_IMG from "../../../assets/Images/refused.png";
import PAID_IMG from "../../../assets/Images/paied.png";
import WRONG_NUMBER_IMG from "../../../assets/Images/wrong-number.png";
import NON_CONTACTABLE_IMG from "../../../assets/Images/non-contactable.png";
import BM_IMG from "../../../assets/Images/bm.png";
import { SMART_COLLECT_MENU_IDS } from "../../../constant";
import PropTypes from "prop-types";

const { Title, Text } = Typography;

export function PayCardDetails({ dashboardData }) {
  return (
    <div className={Style.pay_container}>
      <Flex gap={15} justify="space-between" wrap>
        <Link
          to={SMART_COLLECT_MENU_IDS.READY_TO_PAY || "#"}
          className={Style.card}
          style={{ background: `#E5FEF0` }}
        >
          <Flex align="center" justify="space-between" className={Style.box}>
            <Flex vertical gap={10} className={Style.card_details}>
              <Title level={2} style={{ color: `#1D3261` }}>
                {dashboardData?.promised_to_pay || 0}
              </Title>
              <Text>Ready to Pay</Text>
            </Flex>

            <div className={Style.pay_image}>
              <img src={READY_PAY_IMG} alt="Ready to Pay" />
            </div>
          </Flex>
        </Link>

        <Link
          to={SMART_COLLECT_MENU_IDS.REFUSED_TO_PAY || "#"}
          className={Style.card}
          style={{ background: `#FFEDED` }}
        >
          <Flex align="center" justify="space-between" className={Style.box}>
            <Flex vertical gap={10} className={Style.card_details}>
              <Title level={2} style={{ color: `#1D3261` }}>
                {dashboardData?.refuse_to_pay || 0}
              </Title>
              <Text>Refused to Pay</Text>
            </Flex>

            <div className={Style.pay_image}>
              <img src={REFUSED_IMG} alt="Refused to Pay" />
            </div>
          </Flex>
        </Link>

        <Link
          to={SMART_COLLECT_MENU_IDS.ALREADY_PAY || "#"}
          className={Style.card}
          style={{ background: `#E4F8F9` }}
        >
          <Flex align="center" justify="space-between" className={Style.box}>
            <Flex vertical gap={10} className={Style.card_details}>
              <Title level={2} style={{ color: `#02951A` }}>
                {dashboardData?.already_paid || 0}
              </Title>
              <Text>Already Pay</Text>
            </Flex>

            <div className={Style.pay_image}>
              <img src={PAID_IMG} alt="Already Pay" />
            </div>
          </Flex>
        </Link>

        <Link
          to={SMART_COLLECT_MENU_IDS.WRONG_NUMBER || "#"}
          className={Style.card}
          style={{ background: `#E6EAFF` }}
        >
          <Flex align="center" justify="space-between" className={Style.box}>
            <Flex vertical gap={10} className={Style.card_details}>
              <Title level={2} style={{ color: `#EC3939` }}>
                {dashboardData?.wrong_number || 0}
              </Title>
              <Text>Wrong Number</Text>
            </Flex>

            <div className={Style.pay_image}>
              <img src={WRONG_NUMBER_IMG} alt="Wrong Number" />
            </div>
          </Flex>
        </Link>

        <Link
          to={SMART_COLLECT_MENU_IDS.BMI_ALLOCATION || "#"}
          className={Style.card}
          style={{ background: `#F6FFC7` }}
        >
          <Flex align="center" justify="space-between" className={Style.box}>
            <Flex vertical gap={10} className={Style.card_details}>
              <Title level={2} style={{ color: `#02951A` }}>
                {dashboardData?.assigned_to_bm || 0}
              </Title>
              <Text>Non-Responsive</Text>
            </Flex>

            <div className={Style.pay_image}>
              <img src={BM_IMG} alt="BM Allocation" />
            </div>
          </Flex>
        </Link>

        <Link
          to={SMART_COLLECT_MENU_IDS.NON_CONTACTABLE || "#"}
          className={Style.card}
          style={{ background: `#FFE8C4` }}
        >
          <Flex align="center" justify="space-between" className={Style.box}>
            <Flex vertical gap={10} className={Style.card_details}>
              <Title level={2} style={{ color: `#1D3261` }}>
                {dashboardData?.assigned_to_bm || 0}
              </Title>
              <Text>Non-Contactable</Text>
            </Flex>
            <div className={Style.contact_image}>
              <img src={NON_CONTACTABLE_IMG} alt="Non-Contactable" />
            </div>
          </Flex>
        </Link>
      </Flex>
    </div>
  );
}

PayCardDetails.propTypes = {
  dashboardData: PropTypes.object,
};

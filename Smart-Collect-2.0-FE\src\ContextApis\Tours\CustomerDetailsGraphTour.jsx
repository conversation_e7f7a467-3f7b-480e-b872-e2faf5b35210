import { Button, Flex } from 'antd';
import Style from "../_style.module.scss";
import PropTypes from 'prop-types';

export function CustomerDetailsGraphTour({handleNext, handleSkip}) {
  return (
    <Flex vertical gap={30}>
        description 
        <Flex justify='end' gap={10}>
            <Button className={Style.skip_btn} onClick={handleSkip}>Skip</Button>
            <Button className={Style.next_btn} onClick={handleNext}>Next</Button>
        </Flex>
    </Flex>
  )
}

// Define props
CustomerDetailsGraphTour.propTypes = {
  handleNext: PropTypes.func.isRequired,
  handleSkip: PropTypes.func.isRequired,
};

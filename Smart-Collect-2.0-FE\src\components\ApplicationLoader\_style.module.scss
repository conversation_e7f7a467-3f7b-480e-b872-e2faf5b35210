@import url("../../index.css");

$size: 30px;
$first-block-clr: #005bba;
$second-block-clr: #87e5f0;
$clr: #111;

.container {
  width: 100%;
  height: 100%;
  background: white;
}
.spinner {
  width: 100px;
  height: 100px;
  position: relative;

  &::after,
  &::before {
    box-sizing: border-box;
    position: absolute;
    content: "";
    width: $size;
    height: $size;
    top: 50%;
    animation: up 2.4s cubic-bezier(0, 0, 0.24, 1.21) infinite;
    left: 50%;
    background: var(--dark-blue);
  }

  &::after {
    background: $second-block-clr;
    top: calc(50% - $size);
    left: calc(50% - $size);
    animation: down 2.4s cubic-bezier(0, 0, 0.24, 1.21) infinite;
  }
}

@keyframes down {
  0%,
  100% {
    transform: none;
  }

  25% {
    transform: translateX(100%);
  }

  50% {
    transform: translateX(100%) translateY(100%);
  }

  75% {
    transform: translateY(100%);
  }
}

@keyframes up {
  0%,
  100% {
    transform: none;
  }

  25% {
    transform: translateX(-100%);
  }

  50% {
    transform: translateX(-100%) translateY(-100%);
  }

  75% {
    transform: translateY(-100%);
  }
}

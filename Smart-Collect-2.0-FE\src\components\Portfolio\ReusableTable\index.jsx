import { Table, Typography } from "antd";
import React from "react";
import Style from "./_style.module.scss";
import PropTypes from "prop-types";
import { data } from "react-router";

const { Text } = Typography;

export function ReusableTable({
  isSwitch = true,
  data,
  table = "state",
  isLoading = false,
  pagination,
  onPaginationChange,
  handleFetchCustomerDetails,
}) {
  const getIdProps = (record) => {
    return {
      stateId: table === "state" ? record.State_id : "all",
      regionId: (() => {
        if (table === "region") return record.Region_id;
        if (table === "state") return "all";
        return record.Region_id;
      })(),
      branchId: table === "branch" ? record.Branch_id : "all",
      tablewise: table.charAt(0).toUpperCase() + table.slice(1),
    };
  };

  const getTableTitleAndDataIndex = () => {
    if (table === "state")
      return { title: "State", dataIndex: "State", width: 150 };
    if (table === "region") return { title: "Region", dataIndex: "Region" };
    return { title: "Branch", dataIndex: "Branch" };
  };
  const {
    title: firstTitle,
    dataIndex: firstDataIndex,
    width: firstColumnWidth,
  } = getTableTitleAndDataIndex();
  const columns = [
    {
      title: firstTitle,
      dataIndex: firstDataIndex,
      ...(firstColumnWidth && { width: firstColumnWidth }),
      render: (text) => <Text>{text || "-"}</Text>,
    },
    // Good
    {
      title: "Good",
      dataIndex: isSwitch
        ? "good_customers"
        : "good_principleoutstanding_outstanding",
      render: (value, record) => (
        <Text
          style={{ color: "#147936", cursor: "pointer" }}
          onClick={() =>
            handleFetchCustomerDetails({ ...getIdProps(record), range: "Good" })
          }
        >
          {value}
        </Text>
      ),
    },
    {
      title: "Percentage",
      dataIndex: isSwitch
        ? "good_account_percentage"
        : "good_amount_percentage",
      render: (value) => <Text>{`${Math.floor(Math.min(100, value))}%`}</Text>,
    },

    // Arrear
    {
      title: "Arrear",
      dataIndex: isSwitch
        ? "arrear_customers"
        : "arrear_principleoutstanding_outstanding",
      render: (value, record) => (
        <Text
          style={{
            background: "#F9F1C4",
            color: "#284A88",
            padding: "0.2rem 1rem",
            borderRadius: "8px",
            cursor: "pointer",
          }}
          onClick={() =>
            handleFetchCustomerDetails({
              ...getIdProps(record),
              range: "Arrear",
            })
          }
        >
          {value}
        </Text>
      ),
    },
    {
      title: "Percentage",
      dataIndex: isSwitch
        ? "arrear_account_percentage"
        : "arrear_amount_percentage",
      render: (value) => <Text>{`${Math.floor(Math.min(100, value))}%`}</Text>,
    },

    // NPA
    {
      title: "Npa",
      dataIndex: isSwitch
        ? "npa_customers"
        : "npa_principleoutstanding_outstanding",
      render: (value, record) => (
        <Text
          style={{
            background: "#FEC9B2",
            color: "#284A88",
            padding: "0.2rem 1rem",
            borderRadius: "8px",
            cursor: "pointer",
          }}
          onClick={() =>
            handleFetchCustomerDetails({ ...getIdProps(record), range: "NPA" })
          }
        >
          {value}
        </Text>
      ),
    },
    {
      title: "Percentage",
      dataIndex: isSwitch ? "npa_account_percentage" : "npa_amount_percentage",
      render: (value) => <Text>{`${Math.floor(Math.min(value))}%`}</Text>,
    },

    // NPA+
    {
      title: "Npa+",
      dataIndex: isSwitch
        ? "npaplus_customers"
        : "npaplus_principleoutstanding_outstanding",
      render: (value, record) => (
        <Text
          style={{
            background: "#FEC9B2",
            color: "#284A88",
            padding: "0.2rem 1rem",
            borderRadius: "8px",
            cursor: "pointer",
          }}
          onClick={() =>
            handleFetchCustomerDetails({
              ...getIdProps(record),
              range: "NPAPLUS",
            })
          }
        >
          {value}
        </Text>
      ),
    },
    {
      title: "Percentage",
      dataIndex: isSwitch
        ? "npaplus_account_percentage"
        : "npaplus_amount_percentage",
      render: (value) => <Text>{`${Math.floor(Math.min(value))}%`}</Text>,
    },

    // NPA 2
    {
      title: "Npa 2",
      dataIndex: isSwitch
        ? "npa2_customers"
        : "npa2_principleoutstanding_outstanding",
      render: (value, record) => (
        <Text
          style={{
            background: "#FEC9B2",
            color: "#284A88",
            padding: "0.2rem 1rem",
            borderRadius: "8px",
            cursor: "pointer",
          }}
          onClick={() =>
            handleFetchCustomerDetails({ ...getIdProps(record), range: "NPA2" })
          }
        >
          {value}
        </Text>
      ),
    },
    {
      title: "Percentage",
      dataIndex: isSwitch
        ? "npa2_account_percentage"
        : "npa2_amount_percentage",
      render: (value) => <Text>{`${Math.floor(Math.min(value))}%`}</Text>,
    },

    {
      title: "Amount",
      dataIndex: "active_overdueamt_sum",
      render: (value) => <Text>{value || "-"}</Text>,
    },
  ];

  const dataSource = Array.isArray(data)
    ? data.slice(0, -1).map((item, index) => ({ ...item, key: index }))
    : [];

  const summaryContent = ()=> <LastSummary data={data} isSwitch={isSwitch}handleFetchCustomerDetails={handleFetchCustomerDetails}/>
  return (
    <Table
      virtual
      bordered
      className={Style.reuseable_table}
      columns={columns}
      dataSource={dataSource}
      loading={isLoading}
      pagination={{
        current: pagination.current,
        pageSize: pagination.pageSize,
        total: pagination.total,
        onChange: onPaginationChange,
        showSizeChanger: false,
      }}
      scroll={{
        x: 1450,
        y: 360,
      }}
      summary={summaryContent}
    />
  );
}
function LastSummary({ data, isSwitch,handleFetchCustomerDetails }) {
  if (!data || data.length === 0) return null;

  const lastRow = data[data.length - 1];

  const summaryColumns = [
    { label: "Total" }, // Always shown

    {
      key: isSwitch
        ? "good_customers"
        : "good_principleoutstanding_outstanding",
      isClickable: true,
    },
    {
      key: isSwitch ? "good_account_percentage" : "good_amount_percentage",
      isPercentage: true,
    },

    {
      key: isSwitch
        ? "arrear_customers"
        : "arrear_principleoutstanding_outstanding",
      isClickable: true,
    },
    {
      key: isSwitch ? "arrear_account_percentage" : "arrear_amount_percentage",
      isPercentage: true,
    },

    {
      key: isSwitch ? "npa_customers" : "npa_principleoutstanding_outstanding",
      isClickable: true,
    },
    {
      key: isSwitch ? "npa_account_percentage" : "npa_amount_percentage",
      isPercentage: true,
    },

    {
      key: isSwitch
        ? "npaplus_customers"
        : "npaplus_principleoutstanding_outstanding",
      isClickable: true,
    },
    {
      key: isSwitch
        ? "npaplus_account_percentage"
        : "npaplus_amount_percentage",
      isPercentage: true,
    },

    {
      key: isSwitch
        ? "npa2_customers"
        : "npa2_principleoutstanding_outstanding",
      isClickable: true,
    },
    {
      key: isSwitch ? "npa2_account_percentage" : "npa2_amount_percentage",
      isPercentage: true,
    },
  ];

  return (
    <Table.Summary fixed>
      <Table.Summary.Row>
        {summaryColumns.map((col, index) => {
          const value = lastRow?.[col.key];
          let displayValue;
          if (col.label) {
            displayValue = col.label;
          } else if (col.isPercentage) {
            displayValue = `${(+value || 0).toFixed(2)}%`;
          } else {
            displayValue = value ?? "-";
          }

          return (
            <Table.Summary.Cell key={`${index}-${col.key}`} index={index}>
              <Text
                strong
                style={
                  col.isClickable ? { cursor: "pointer", color: "#1677ff" } : {}
                }
                onClick={
                  col.isClickable
                    ? () =>
                        handleFetchCustomerDetails({
                          range: "Summary",
                          key: col.key,
                        })
                    : undefined
                }
              >
                {displayValue}
              </Text>
            </Table.Summary.Cell>
          );
        })}
      </Table.Summary.Row>
    </Table.Summary>
  );
}
LastSummary.propTypes = {
  data: PropTypes.array,
  isSwitch: PropTypes.bool,
  handleFetchCustomerDetails: PropTypes.func
};
ReusableTable.propTypes = {
  isSwitch: PropTypes.bool,
  data: PropTypes.array,
  table: PropTypes.string,
  isLoading: PropTypes.bool,
  pagination: PropTypes.object,
  onPaginationChange: PropTypes.func,
  handleFetchCustomerDetails: PropTypes.func,
};

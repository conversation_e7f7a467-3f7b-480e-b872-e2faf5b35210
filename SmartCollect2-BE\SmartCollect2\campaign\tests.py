from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from http import HTTPStatus
import json
import WebApp.models as webmodels  # Update with the correct app name
import campaign.models as campmodels  # Update with the correct app name
from django.conf import settings
from rest_framework_simplejwt.tokens import AccessToken, RefreshToken
from django.db import connection
import sys
from datetime import datetime


class BaseTestCase(TestCase):
    @classmethod
    def setUpTestData(cls):
        """Set up test data once for all test methods"""
        cls.timestamp = datetime.now().timestamp()
        cls.bank = webmodels.BankMst.objects.create(
            BankName=f'test_bank_{cls.timestamp}',
            Remarks='test_remarks',
            SubscriptionID=1,
            SubscriptionType='test_type'
        )
        cls.user = webmodels.UserMst.objects.create_user(username='testuser', password='12345', BankMstID=cls.bank)
        languages = [
            {
                'Language': 'English',
                'BankMstID': cls.bank
            },
            {
                'Language': 'Hindi',
                'BankMstID': cls.bank
            },
            {
                'Language': 'Marathi',
                'BankMstID': cls.bank
            }
        ]
        webmodels.LanguageMst.objects.bulk_create([webmodels.LanguageMst(**lang) for lang in languages])

    def getHeaders(self):
        return {
            'HTTP_AUTHORIZATION': f'Bearer {self.token}',
            'HTTP_X_SKIP_DECRYPTION': 'true',
            'Content-Type': 'application/json'
        }

    def get_jwt_token(self):
        """Helper method to obtain JWT access token"""
        refresh = RefreshToken.for_user(self.user)
        return str(refresh.access_token)

    def setUp(self, *args, **kwargs):
        """Run before each test"""
        settings.DEBUG = True
        self.client = Client()
        self.token = self.get_jwt_token()
        self.CampaignMstID = None
        self.CampaignMstName = None
        if 'create_campaign' in kwargs and kwargs['create_campaign']:
            self.CampaignMstID = campmodels.CampaignMst.objects.create(
                Name=f'test_campaign_{self.timestamp}',
                BankMstID=self.bank,
                CampaignType='Normal',
            ).CampaignMstID
            self.CampaignMstName = f'test_campaign_{self.timestamp}'


class CreateCampaignViewTests(BaseTestCase):
    def test_01_create_campaign_mst(self):
        url = reverse('create_campaign')  # Ensure this matches your URL pattern name
        testname = f'test_campaign_{self.timestamp}'
        data = {
            'Name': testname,
            'CampaignType': 'normal',
            'Language': 'Hindi'
        }

        headers = self.getHeaders()

        response = self.client.post(url, json.dumps(data), content_type='application/json', **headers)

        print(response.json())

        # Assertions
        self.assertEqual(response.status_code, HTTPStatus.CREATED, f"Expected 201 CREATED but got {response.status_code}")
        self.assertTrue(response.json().get('status') == 'success', f"Expected 'success' in response JSON but got {response.json()}")
        self.assertTrue(campmodels.CampaignMst.objects.filter(Name=testname).exists(), "Campaign was not created in the database")
        self.CampaignMstID = response.json().get('campaign_id')
        self.CampaignMstName = testname
        self.assertTrue(self.CampaignMstID, "CampaignMstID not returned in response JSON")


class CreateFiltersViewTests(BaseTestCase):
    def setUp(self):
        super().setUp(create_campaign=True)

    def test_01_create_filters_view(self):
        """Test the create_filters API view"""
        url = reverse('create_filters')  # Ensure this matches your URL pattern name
        timestamp = datetime.now().timestamp()
        testname = f'test_filter_{timestamp}'
        data = {
            'Name': testname,
            'Filters': [
                {'Comparison': '>', 'Value': '10', 'Column': 'test_field'}
            ],
            'CampaignMstID': self.CampaignMstID
        }

        totalfilters = len(data['Filters'])

        headers = self.getHeaders()

        response = self.client.post(url, json.dumps(data), content_type='application/json', **headers)

        # Assertions
        self.assertEqual(response.status_code, HTTPStatus.CREATED, "Expected 200 OK but got {response.status_code}")
        self.assertTrue(response.json().get('status') == 'success', "Expected 'success' in response JSON but got {response.json()}")

        # Optional: Check if the filter was actually created in the database
        self.assertTrue(campmodels.FilterMst.objects.filter(Name=testname).exists(), "Master Filter was not created in the database")

        filterls = campmodels.Filters.objects.filter(filtermst__Name=testname)
        self.assertTrue(filterls.exists(), "Filter was not created in the database")
        self.assertEqual(filterls.count(), totalfilters, f"Expected {totalfilters} filters but got {filterls.count()}")


class CreateCommunicationFlowTests(BaseTestCase):
    def setUp(self):
        super().setUp(create_campaign=True)

    def test_01_create_communication_queue(self):
        url = reverse('create_commflow')
        testname = f'test_commflow_{self.timestamp}'

        data = {
            'FlowName': testname,
            'CommFlow': [{
                'CommunicationType': 'whatsapp',
                'Days': '1,2,3',
                'BeforeAfter': 'after',
                'CampaignMstID': self.CampaignMstID
            }]
        }

        totalflows = len(data['CommFlow'])
        headers = self.getHeaders()

        response = self.client.post(url, json.dumps(data), content_type='application/json', **headers)

        # Assertions
        self.assertEqual(response.status_code, HTTPStatus.CREATED, f"Expected 201 CREATED but got {response.status_code}")
        self.assertTrue(response.json().get('status') == 'success', f"Expected 'success' in response JSON but got {response.json()}")
        commst = campmodels.CommFlowMst.objects.filter(FlowName=testname)
        self.assertTrue(commst.exists(), "Communication Flow Master was not created in the database")
        self.assertEqual(commst.count(), 1, f"Expected 1 Communication Flow Master but got {commst.count()}")
        self.assertEqual(commst.first().FlowName, testname, "CommFlowMst table not linked correctly")
        flows = campmodels.CommFlow.objects.filter(CommFlowMstID__FlowName=testname)
        self.assertTrue(flows.exists(), "Communication Flow was not created in the database")
        self.assertEqual(flows.count(), totalflows, f"Expected {totalflows} communication flows but got {flows.count()}")
        self.assertEqual(flows.first().CommFlowMstID.FlowName, testname, "CommFlowMst and CommFlow table not linked correctly")

        # Optional: Check if campaignmst was linked properly
        # self.assertEqual(flows.first().CampaignMstID.id, self.CampaignMstID, "CampaignMst and CommFlowMst not linked correctly")


class CreateCommunicationTemplateTests(BaseTestCase):
    def setUp(self):
        super().setUp(create_campaign=True)

    def test_01_register_whatsapp_template(self):
        url = reverse('register_whatsapp')
        testname = f'test_whatsapp_{self.timestamp}'
        data = {
            'Name': testname,
            'TemplateText': 'This is a test template',
            'Language': 'Marathi',
            'ButtonMapping': {
                '1': {
                    'Name': 'test_button1',
                    'Response': 'test_response1'
                },
                '2': {
                    'Name': 'test_button2',
                    'Response': 'test_response2'
                },
                '3': {
                    'Name': 'test_button3',
                    'Response': 'test_response3'
                }
            },
            'SampleMapping': {
                '1': 'test_sample1',
                '2': 'test_sample2',
                '3': 'test_sample3'
            },
        }
        total_buttons = len(data['ButtonMapping'])

        headers = self.getHeaders()

        response = self.client.post(url, json.dumps(data), content_type='application/json', **headers)

        # Assertions
        self.assertEqual(response.status_code, HTTPStatus.CREATED, f"Expected 201 CREATED but got {response.status_code}")
        self.assertTrue(response.json().get('status') == 'success', f"Expected 'success' in response JSON but got {response.json()}")
        self.assertTrue(campmodels.WhatsAppFlowMst.objects.filter(FlowName=testname).exists(), "WhatsApp Flow Master was not created in the database")
        flows = campmodels.WhatsAppFlowMapping.objects.filter(FlowName=testname)
        self.assertTrue(flows.exists(), "WhatsApp Flow Mapping was not created in the database")
        self.assertEqual(flows.count(), total_buttons, f"Expected {total_buttons} buttons but got {flows.count()}")
        self.assertEqual(flows.first().FlowName, testname, "WhatsApp Flow Master and Mapping not linked correctly")


    def test_02_register_blaster_template(self):
        url = reverse('register_blaster')
        testname = f'test_blaster_{self.timestamp}'
        data = {
            'Name': testname,
            'Language': 'Marathi',
            'TemplateText': 'This is a test template',
            'VariableMapping': {
                '1': 'test_variable1',
                '2': 'test_variable2',
                '3': 'test_variable3'
            }
        }

        total_variables = len(data['VariableMapping'])

        headers = self.getHeaders()

        response = self.client.post(url, json.dumps(data), content_type='application/json', **headers)

        # Assertions
        self.assertEqual(response.status_code, HTTPStatus.CREATED, f"Expected 201 CREATED but got {response.status_code}")
        self.assertTrue(response.json().get('status') == 'success', f"Expected 'success' in response JSON but got {response.json()}")
        self.assertTrue(campmodels.BlasterTemplateMapping.objects.filter(TemplateName=testname).exists(), "Blaster Flow Master was not created in the database")
        flows = campmodels.BlasterTemplateMapping.objects.filter(TemplateName=testname)
        self.assertTrue(flows.exists(), "Blaster Flow Mapping was not created in the database")
        self.assertEqual(campmodels.BlasterUserTemplate.objects.filter(TemplateName=testname).count(), total_variables, f"Expected {total_variables} variables but got {flows.count()}")
        self.assertEqual(flows.first().TemplateName, testname, "Blaster Flow Master and Mapping not linked correctly")

    def test_03_voice_bot_template(self):
        url = reverse('register_voicebot')
        testname = f'test_voicebot_{self.timestamp}'
        data = {
            'Name': testname,
            'Language': 'Marathi',
            'TemplateText': 'This is a test template',
            'start': {
                'message': 'test_start_message',
                'VariableMapping': {
                    '1': 'test_variable1',
                    '2': 'test_variable2',
                    '3': 'test_variable3'
                },
            },
            'end': {
                'message': 'test_end_message',
                'VariableMapping': {
                    '1': 'test_variable1',
                    '2': 'test_variable2',
                    '3': 'test_variable3'
                },
            }
        }

        total_variables = len(data['start']['VariableMapping']) + len(data['end']['VariableMapping'])
        startvars = len(data['start']['VariableMapping'])
        endvars = len(data['end']['VariableMapping'])

        headers = self.getHeaders()

        response = self.client.post(url, json.dumps(data), content_type='application/json', **headers)

        # Assertions
        self.assertEqual(response.status_code, HTTPStatus.CREATED, f"Expected 201 CREATED but got {response.status_code}")
        self.assertTrue(response.json().get('status') == 'success', f"Expected 'success' in response JSON but got {response.json()}")
        self.assertTrue(campmodels.VoiceBotTemplateMapping.objects.filter(TemplateName=testname).exists(), "Voice Bot Flow Master was not created in the database")
        variablemaps = campmodels.VoiceBotUserTemplate.objects.filter(VoiceBotTemplateMappingID__TemplateName=testname)
        self.assertEqual(variablemaps.count(), total_variables, f"Expected {total_variables} variables but got {variablemaps.count()}")
        startcount = variablemaps.filter(StartEnd='Start').count()
        self.assertEqual(startcount, startvars, f"Expected {startvars} start variables but got {startcount}")
        endcount = variablemaps.filter(StartEnd='End').count()
        self.assertEqual(endcount, endvars, f"Expected {endvars} end variables but got {endcount}")
        self.assertEqual(variablemaps.first().VoiceBotTemplateMappingID.TemplateName, testname, f"Voice Bot Flow Master and Mapping not linked correctly got {variablemaps.first().TemplateName}")

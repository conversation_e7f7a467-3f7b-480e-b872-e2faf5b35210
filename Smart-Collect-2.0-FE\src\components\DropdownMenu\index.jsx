import React, { useState, useEffect } from 'react';
import { Select } from 'antd';
import styles from './_dropdown-menu.module.scss'; // Import the SCSS file
import PropTypes from 'prop-types';

const DropdownMenuExample = ({ options }) => {
    // Initialize the state with the first option's value
    const [selectedReason, setSelectedReason] = useState(options[0]?.value);

    // Ensure the state is updated if the options prop changes
    useEffect(() => {
        if (options.length > 0) {
            setSelectedReason(options[0].value);
        }
    }, [options]);

    const handleReasonChange = (value) => {
        setSelectedReason(value);
    };

    return (
        <Select
            className={styles.dropdown}
            value={selectedReason}
            onChange={handleReasonChange}
            options={options.map(option => ({ label: option.label, value: option.value }))}
        />
    );
};

DropdownMenuExample.propTypes={
    options: PropTypes.array
}

export default DropdownMenuExample;
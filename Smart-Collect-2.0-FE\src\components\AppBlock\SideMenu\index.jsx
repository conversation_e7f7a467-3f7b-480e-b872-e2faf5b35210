import { Flex, <PERSON>u, Typography } from "antd";
import React, { useEffect, useState } from "react";
import COMPANY_LOGO from "../../../assets/Images/logo.png";
import COMPANY_NAME_IMG from "../../../assets/Images/logo-title.png";
import LOGOUT_IMG from "../../../assets/Images/Frame-4.png";
import { SMART_COLLECT_MENU_IDS } from "../../../constant";
import { Link, useLocation, useNavigate } from "react-router";
import Style from "./_side.module.scss";
import { handleLogout } from "../../../apis/ho-Instance";
import PropTypes from "prop-types";

const { Title } = Typography;
export function SideMenu({ collapsedSideMenu, menuItems }) {
  const location = useLocation();
  const navigate = useNavigate();
  const [items, setItems] = useState([]);
  const [activeKey, setActiveKey] = useState(SMART_COLLECT_MENU_IDS.DASHBOARD);

  const getMenuIDFromURL = (pathname) => {
    const lastPath = pathname.split("/").pop();

    // Return the matched menuID or a default ("dashboard")
    let menuID =
      Object.values(SMART_COLLECT_MENU_IDS).find((d) => d === lastPath) ||
      SMART_COLLECT_MENU_IDS.DASHBOARD;

    // handle the client details
    if (menuID === SMART_COLLECT_MENU_IDS.CLIENT_DETAILS) {
      return SMART_COLLECT_MENU_IDS.ADMIN;
    }

    //handle the campaign child route
    if (
      [SMART_COLLECT_MENU_IDS.PERIODIC, SMART_COLLECT_MENU_IDS.NORMAL].includes(
        menuID
      )
    ) {
      return SMART_COLLECT_MENU_IDS.CAMPAIGN_MANAGEMENT;
    }

    return menuID;
  };

  useEffect(() => {
    const user = JSON.parse(localStorage.getItem("user"));
    const designation = user?.designation;

    const updatedItems = menuItems
      .filter(
        (item) =>
          !(
            item.key === SMART_COLLECT_MENU_IDS.CAMPAIGN_MANAGEMENT &&
            designation === "BM"
          )
      )
      .map((item) => ({
        key: item.key,
        icon: (
          <div
            style={{ width: "20px", height: "20px", marginInlineEnd: "0.5rem" }}
          >
            <img
              src={item.icon}
              style={{ width: "100%", objectFit: "contain" }}
              alt={item.label}
            />
          </div>
        ),
        label: (
          <Link
            to={`${item.link}`}
            style={{
              fontWeight: "400",
              fontSize: "14px",
              letterSpacing: "0.5px",
            }}
          >
            {item.label}
          </Link>
        ),
        children: item.children?.map((subItem) => ({
          key: subItem.key,
          label: (
            <Link
              to={`${subItem.link}`}
              style={{
                fontWeight: "400",
                fontSize: "14px",
                letterSpacing: "0.5px",
              }}
            >
              {subItem.label}
            </Link>
          ),
          icon: (
            <div
              style={{
                width: "20px",
                height: "20px",
                marginInlineEnd: "0.5rem",
              }}
            >
              <img
                src={subItem.icon}
                style={{ width: "100%", objectFit: "contain" }}
                alt={subItem.label}
              />
            </div>
          ),
        })),
      }));

    // Add Logout Item
    updatedItems.push({
      key: "logout",
      icon: (
        <button
          style={{
            width: "20px",
            height: "20px",
            marginInlineEnd: "0.5rem",
          }}
          onClick={handleLogout}
        >
          <img
            src={LOGOUT_IMG}
            style={{ width: "100%", objectFit: "contain" }}
            alt="logout"
          />
        </button>
      ),
      label: (
        <button
          onClick={handleLogout}
          style={{
            cursor: "pointer",
            fontWeight: "400",
            fontSize: "14px",
            letterSpacing: ".5px",
          }}
        >
          Logout
        </button>
      ),
    });

    setItems(updatedItems);
  }, [menuItems]);

  useEffect(() => {
    const active = getMenuIDFromURL(location.pathname);
    setActiveKey(active);
  }, [location.pathname]);

  const handleLogoClick = () => {
    const user = JSON.parse(localStorage.getItem("user"));
    const designation = user?.designation;

    const routes = {
      HO: "/ho",
      BM: "/ho",
      FO: "/field",
      admin: "/admin",
    };

    navigate(routes[designation]);
  };
  return (
    <>
      {/* Logo */}
      <div className={Style.logo_container}>
        <button
          className={Style.logo_image}
          onClick={handleLogoClick}
          style={{ cursor: "pointer", border: "none" }}
        >
          <img src={COMPANY_LOGO} alt="logo" />
        </button>
        {!collapsedSideMenu && (
          <div className={Style.logo_title_image}>
            <img src={COMPANY_NAME_IMG} alt="logo title" />
          </div>
        )}
      </div>

      {/* Side menus */}
      <Menu
        defaultSelectedKeys={[activeKey]}
        selectedKeys={[activeKey]}
        mode="inline"
        inlineCollapsed={collapsedSideMenu}
        items={items}
      />

      {/* Powered by */}
      <Flex
        align="center"
        justify="center"
        gap="small"
        style={{
          visibility: `${!collapsedSideMenu ? "visible" : "hidden"}`,
          padding: "1rem 0.6rem",
        }}
      >
        <a
          href="https://www.markytics.com/"
          target="_blank"
          rel="noopener noreferrer"
          style={{ textDecoration: "none", color: "white" }}
        >
          <Title
            level={4}
            style={{
              color: "white",
              margin: "0",
              fontSize: "1.3rem",
              whiteSpace: "nowrap",
              fontWeight: "275",
              cursor: "pointer",
            }}
          >
            Powered by Markytics.AI
          </Title>
        </a>
      </Flex>
    </>
  );
}

SideMenu.propTypes = {
  collapsedSideMenu: PropTypes.bool,
  menuItems: PropTypes.object,
};

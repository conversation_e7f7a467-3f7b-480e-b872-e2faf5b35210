import { Button, Flex, Modal, Typography } from 'antd'
import React from 'react'
import QUEUE_ICON_IMG from "../../../../assets/Images/queue.svg";
import Style from './_ready.module.scss';
import PropTypes from 'prop-types';

const {Text} = Typography;

export function PaymentModal({modalStatus, handleSendPayInfo}) {
  return (
<Modal
    centered
    className={Style.pay_modal}
    title={null}
    open={modalStatus}
    onOk={()=>{ handleSendPayInfo({sendPayInfo:true})}}
    onCancel={()=>{ handleSendPayInfo({sendPayInfo:false})}}
    footer={null}
    width={350}
  >
    <Flex justify='center' align='center' vertical gap={10}>
      <div className={Style.img_div}>
        <img src={QUEUE_ICON_IMG} alt="queue" />
      </div>
      <Text className={Style.queue_title}>Do you want to send payment info?</Text>
      <Flex gap={10} align='center'>
        <Button className={Style.yes_btn} onClick={()=>handleSendPayInfo({sendPayInfo:true})}>Yes</Button>
        <Button className={Style.no_btn} onClick={()=> handleSendPayInfo({sendPayInfo:false})}>No</Button>
      </Flex>
    </Flex>
</Modal>
  )
}
PaymentModal.propTypes={
  modalStatus: PropTypes.bool, 
  handleSendPayInfo: PropTypes.func
}
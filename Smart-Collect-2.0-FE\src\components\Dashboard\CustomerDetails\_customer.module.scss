@import url("../../../index.css");

$textMarginRight: 2rem;

.customer_details_container {
  .customer {
    padding: 0rem 2rem;
    height: 250px;
    border-radius: 5px 0px 0px 0px;
    position: relative;

    .gif_and_title_container{
      margin-top: 2rem;
      .gif_container {
        position: relative;
        width: 80px;
          img {
            width: 100%;
            object-fit: contain;
          }
          .dashboard_logo {
            position: absolute;
            top: 48%; // Center vertically
            left: 50%; // Center horizontally
            transform: translate(-50%, -50%); // Adjust alignment to the center
            width: 32px; // Adjust the size of the Rupee logo
            z-index: 10; // Ensure it stays above the GIF
          }
      }
    }
 
    .total_customers {
      font-size: 2.2rem;
      font-weight: 700;
      color: var(--blue);
      margin: 0;
      margin-right: $textMarginRight;
      font-family: 'Kanit','Courier New', Courier, monospace;
    }

    .heading {
      text-align: end;
      font-weight: 300;
      font-size: 1.8rem;
      margin: 2rem $textMarginRight 0 0;
      color: var(--blue);
      font-family: 'Kanit','Courier New', Courier, monospace;
    }

    .circle {
      width: 60px;
      height: 60px;
      background-color: #ffffff;
      border-radius: 50%;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }

    .left {
      left: -25px;
    }

    .right {
      right: -25px;
    }
  }

  .dropdown_container {
    position: absolute;
    bottom: 0;
    left: 0;
    overflow: hidden;
    width: 100%;
    background-color: white;
    padding: 1rem;
    box-shadow: 0px 1px 1px 0px #00000040 inset;

    // Arrow
    .arrow_container {
      width: 100%;
      display: flex;
      justify-content: end;
      cursor: pointer;
      position: relative;

      .arrow {
        border: solid #0156a6;
        border-width: 0 3px 3px 0;
        padding: 3px;
        cursor: pointer;
        transition: transform 0.3s ease-in-out;
        z-index: 1;
        background-color: white;
        &:global(.up) {
          transform: rotate(-135deg);
        }

        &:global(.down) {
          transform: rotate(45deg);
        }
      }
    }

    // Dropdown
    .list {
      :global(.ant-typography) {
        margin: 0;
        color: var(--dark-black);
        font-family: 'Kanit','Courier New', Courier, monospace;
        font-weight: 400;
      }
      span {
        font-size: 14px;
        font-weight: 500;
        letter-spacing: 0.2px;
        color: black !important;
        white-space: nowrap;
        font-family: 'Kanit','Courier New', Courier, monospace;
      }
    }
  }

  .customer_details {
    background-color: var(--light-green);
    padding: 0.8rem 0.5rem;
    border-radius: 5px;
    border-left: 5px solid var(--blue);

    .icon_image {
      width: 45px;
      height: 45px;
      border-radius: 50px;
      background-color: white;
      flex-shrink: 0;
      img {
        width: 100%;
        object-fit: contain;
        padding: 0.5rem;
      }
    }

    .amount {
      font-weight: 700;
      font-size: 1.4rem;
      color: var(--dark-black);
      margin: 0;
      flex: 1;
      text-align: center;
      font-family: 'Kanit','Courier New', Courier, monospace;
    }

    .title {
      text-align: end;
      flex: 2;
      color: var(--dark-black);
      font-size: 1.1rem;
      font-weight: 400;
      font-family: 'Kanit','Courier New', Courier, monospace;
    }
  }
}

// Responsive view
@media screen and (max-width:1224px){
  .customer_details_container{
    .customer{
      height: 300px;
    }
  }
}

@media screen and (max-width:768px) {
  .customer_details_container{
    .customer{
       height: 250px;
      .total_customers{
        font-size: 2rem;
      }
      .heading{
        font-size: 1.5rem;
        margin: 1rem 1rem 0 0;
      }
      .gif_and_title_container{
        margin-top: 1rem;
      }
      .circle{
        top: 45%;
      }
    }
  }
}

@media screen and (max-width:668px) {
  .customer_details_container{
    .customer{
      padding: 0 1rem;
      height: 190px;
      .total_customers{
        font-size: 1.2rem;
      }
      .heading{
        font-size: 1rem;
        margin: 0rem 1rem 0 0;
      }
      .gif_and_title_container{
        margin-top: 1rem;

        .gif_container{
          width: 60px;
          .rupee_logo {
            width: 25px;
          }
        }

      }
      .circle{
        top: 45%;
        width: 50px;
        height: 50px;
      }
    }

    .dropdown_container{
      padding: 0.5rem;
      .list{
        :global(.ant-typography) {
         font-size: 10px;
        }
      }
    }

    .customer_details{
      .icon_image{
        width: 35px;
        height: 35px;
      }
      .amount{
        font-size: 16px;
      }
      .title{
        font-size: 12px;
      }
    }
  }
}
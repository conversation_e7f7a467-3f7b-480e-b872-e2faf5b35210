import { Table, Typography, Flex, message } from "antd";
import React, { useEffect, useState } from "react";
import { AXIOS } from "../../../apis/ho-Instance";
import Style from "./_style.module.scss";
import {
  ALL_CACHE_NAMES,
  handleDateFormatter,
  handleDownloadTable,
} from "../../../constant";
import ApplicationLoader from "../../ApplicationLoader";
import FEEDBACK_IMG from "../../../assets/Images/iconamoon_eye-light.svg";
import { ViewFeedbackModal } from "./ViewFeedbackModal";
import DOWNLOAD_IMG from "../../../assets/Images/download.svg";
import {
  getCacheKey,
  getFromCache,
  storeToCache,
} from "../../../utils/cacheHelper";

const { Text } = Typography;
const CACHE_NAME = ALL_CACHE_NAMES.ACTIVITY_CACHE;

export function FeedbackLogTable() {
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [viewModal, setViewModal] = useState({
    status: false,
    data: null,
  });

  const handleOpenModal = (data) => {
    setViewModal({
      status: true,
      data: data,
    });
  };

  const handleViewFeedbackCancel = () => {
    setViewModal({
      status: false,
      data: null,
    });
  };

  const handleGetFeedbackLogs = async () => {
    setIsLoading(true);
    const cacheKey = getCacheKey({ endpoint: "feedback-log/" });
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setData(cachedData);
      setIsLoading(false);
    }
    try {
      const res = await AXIOS.get("feedbacklogs/");
      if (res.status === 200) {
        setData(res.data.data);
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: res.data?.data.length ? res.data.data : [],
        });
      }
    } catch (error) {
      console.log("Error in feedback logs", error?.message);
    } finally {
      setIsLoading(false);
    }
  };

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      sorter: (a, b) => a.key - b.key,
      render: (_, data) => <Text>{data?.key}.</Text>,
    },
    {
      title: "Feedback Date",
      dataIndex: "FeedbackDate",
      sorter: (a, b) => new Date(a.FeedbackDate) - new Date(b.FeedbackDate),
      render: (value) => (
        <Text className={Style.text}>
          {value ? handleDateFormatter(value) : "--"}
        </Text>
      ),
    },
    {
      title: "Customer Id",
      dataIndex: "CustomerID",
      sorter: (a, b) => a.CustomerID - b.CustomerID,
      render: (value) => <Text className={Style.text}>{value}</Text>,
    },
    {
      title: "Customer Name",
      dataIndex: "CustomerName",
      sorter: (a, b) => a.CustomerName - b.CustomerName,
      render: (value) => <Text className={Style.text}>{value || "--"}</Text>,
    },
    {
      title: "Disbursement Id",
      dataIndex: "DisbursementID",
      sorter: (a, b) => a.DisbursementID - b.DisbursementID,
      render: (value) => <Text className={Style.text}>{value}</Text>,
    },
    {
      title: "Feedback",
      dataIndex: "feedback-view",

      render: (_, data) => (
        <button
          className={Style.icon_div}
          onClick={() => handleOpenModal(data)}
        >
          <div className={Style.icon_img}>
            <img src={FEEDBACK_IMG} alt="feedback" />
          </div>
        </button>
      ),
    },
  ];

  const dataSource = Array.isArray(data)
    ? data.map((data, i) => ({
        key: i + 1,
        ...data,
      }))
    : [];

  useEffect(() => {
    handleGetFeedbackLogs();
  }, []);

  // Handle downloading
  const handleDownload = async () => {
    if (data.length === 0) {
      return message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: "Activity Feedbacklog",
        worksheetName: "activity-feedbacklog",
        tableData: data,
      });
    }
  };

  return (
    <Flex vertical gap={20}>
      {isLoading ? (
        <ApplicationLoader />
      ) : (
        <>
          <Flex justify="end">
            <button className={Style.download_button} onClick={handleDownload}>
              <img src={DOWNLOAD_IMG} alt="download-button" />
            </button>
          </Flex>
          <Table
            bordered
            virtual
            className={Style.CustomTable}
            columns={columns}
            dataSource={dataSource}
            scroll={{
              x: 700,
              y: 460,
            }}
            pagination={{
              showSizeChanger: false,
            }}
          />
        </>
      )}

      <ViewFeedbackModal
        modalStatus={viewModal.status}
        handleCancel={handleViewFeedbackCancel}
        feedbackData={viewModal.data}
      />
    </Flex>
  );
}

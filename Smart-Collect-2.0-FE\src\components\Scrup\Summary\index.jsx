import { Flex, message, Segmented, Typography } from 'antd'
import React, { useEffect, useState } from 'react'
import { CustomerRiskDistribution } from './CustomerRiskDistribution';
import Style from "./_style.module.scss";
import { handleDownloadTable } from '../../../constant';
import DOWNLOAD_IMG from "../../../assets/Images/download.svg";
import { ScrubTable } from './ScrubTable';
import { ScrubGraph } from './ScrubGraph';
import { AXIOS } from '../../../apis/ho-Instance';
import ApplicationLoader from '../../ApplicationLoader';
import { CustomerDetailsTable } from './CustomerDetailsTable';

const {Title, Text}  = Typography;

export function Summary() {
  const [stateView, setStateView]= useState("Graph");
  const [isLoading, setIsLoading] = useState(false);
  const [stateIsLoading] = useState(false);
  const [rdData, setRdData] = useState([]);
  const [tableLevel , setTableLevel ] = useState("region");
  const [banksData, setBanksData] = useState({
    account_data:[],
    amount_data:[]
  });
  const [customersData, setCustomersData] = useState({
    current: 1, 
    total: 1, 
    data:[]
  });
  const [customerPayload, setCustomerPayload] = useState({
    state_id:"all",  
    region_id: "all",    
    branch_id:"all",         
    riskCategory:"",
    dpdRange:"",
    isRd:true
  });
  const [stateTableType, setStateTableType] = useState("Account");
  const [selectedState, setSelectedState] = useState(null);
  const USER_DETAILS = JSON.parse(localStorage.getItem('user'));

  // Handle download data
  const handleDownload = async () => { 
    const selectedData = stateTableType ==="Account" ? banksData.account_data : banksData.amount_data;
    if (selectedData.length === 0) {
    return  message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: "Scrub Summary",
        worksheetName: "scrub-summary",
        tableData: selectedData,
      });
      message.success("data downloaded successfully!");
    }
  };

  // Handle customer downloading
  const handleDownloadCustomer = async () => { 
    const selectedData = customersData.data;
    if (selectedData.length === 0) {
    return  message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: "Customer Distribution",
        worksheetName: "customer-distribution",
        tableData: selectedData,
      });
      message.success("data downloaded successfully!");
    }
  };

  // Get RD & State details
  const handleGetRDAndStateData = async () => {
    const payLoad = {
      State_id: "all",  
      Region_id: "all",    
      Branch_id: "all",          
      CollectionOfficerID: "all",  
      LoanType: "all",
      BankMstID: [USER_DETAILS?.BankMstID]
    }
    setIsLoading(true);
    try {
    // Modified API call to use local development URL with skip decryption
    const rdResponse = await fetch('http://127.0.0.1:8000/backend/api/scrub_summary_risk_dpd/?HTTP_X_SKIP_DECRYPTION=true', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${JSON.parse(localStorage.getItem("user"))?.access}`
      },
      body: JSON.stringify(payLoad)
    });
    
    // Log the response
    console.log('API Response Status:', rdResponse.status);
    const responseData = await rdResponse.json();
    console.log('API Response Data:', responseData);
    
    if(rdResponse.ok){
       setRdData(responseData.final_response || []);
    }
    
    // Keep the original banks API call
    const banksResponse = await AXIOS.post('scrub_other_tables/', {...payLoad, tablewise: "State"});
    if(banksResponse.status === 200){
      const finalData = banksResponse.data.final_response;
      setBanksData({
        account_data: finalData?.account_data || [],
        amount_data: finalData?.amount_data || []
      });
    }
    }catch(error){
      console.log("Error in Summary, ", error?.message);
      message.warning(`Something is wrong, try again!`)
    }finally{
      setIsLoading(false);
    }
  };

  // Update the table data 
  const handleUpdateStateTable = async({
    stateId ="all",
    regionId = "all",
    branchId = "all",
    tablewise ="Region"
  })=>{
    const payload ={
      State_id: stateId === "all" ? "all" : [stateId],  
      Region_id: regionId ==="all"? "all" : [regionId],    
      Branch_id: branchId === "all"? "all" : [branchId],          
      BankMstID : [USER_DETAILS?.BankMstID],
      CollectionOfficerID: "all",  
      LoanType: "all",
      tablewise,
    };
    try {
      const response  = await AXIOS.post('scrub_other_tables/',
        payload,
      );      
      if(response.status === 200){
        const finalData = response.data.final_response;
        setBanksData({
          account_data: finalData?.account_data || [],
          amount_data: finalData?.amount_data || []
        });
      }else{
        setBanksData({
          account_data:[],
          amount_data: []
        });
        setTableLevel(null);
      }
    } catch (error) {
      console.log("Error in State table", error?.message);
      message.warning(`Something is wrong, try again!`)
    }
  }

  // Handle Get customer details 
  const handleGetCustomerDetails = async({
    state_id ="all",  
    region_id = "all",    
    branch_id = "all",         
    riskCategory,
    dpdRange,
    page=1,
    isRd=true,
  }) => {
    // Store customer payload
    setCustomerPayload({ state_id, region_id, branch_id, riskCategory, dpdRange, isRd });

    const payload = {
      State_id: state_id === "all" ? "all" : [state_id],
      Region_id: region_id === "all" ? "all" : [region_id],
      Branch_id: branch_id === "all" ? "all" : [branch_id],
      CollectionOfficerID: "all",
      LoanType: "all",
      BankMstID: [USER_DETAILS?.BankMstID],
      risk_category: riskCategory,
      page_limit: 10,
      offset: page-1,
      ...(isRd && { dpd_range: dpdRange }),
    };
    try {
        const response = await AXIOS.post('scrub_summary_customers/', payload);
        if(response.status === 200 && response.data.data.length){
          setCustomersData({
            current: response.data?.current_page || 1,
            total:response.data?.total_records || 1,
            data: response.data?.data
          });
            // Wait for state update to reflect in DOM
            setTimeout(() => {
              window.scrollTo({ top: document.body.scrollHeight, behavior: "smooth" });
            }, 100);
        }else{
          message.warning(`There are no customer data found!`)
          setCustomersData({
            current:1,
            total: 1,
            data: []
          })
        }
    } catch (error) {
      console.log("Error in get customer details", error?.message);
      message.warning(`Something is wrong, try again!`)
    }
  };

  // Handle customer pagination
  const handleCustomerPagination = async(page=1) =>{
    await handleGetCustomerDetails({
      state_id: customerPayload.state_id,
      region_id: customerPayload.region_id,
      dpdRange: customerPayload.dpdRange,
      riskCategory: customerPayload.riskCategory,
      branch_id: customerPayload.branch_id,
      isRd: customerPayload.isRd,
      page: page,
    })
  }
  
  // Invoking the get calls
  useEffect(()=>{
    handleGetRDAndStateData();
  },[]);

  return (
    isLoading 
    ?<ApplicationLoader/>
    :<Flex  vertical gap={10}>
        <Flex vertical gap={10} className={Style.box_container}>
            <Title level={4} className={Style.title}>Customer Risk Distribution</Title>
            <CustomerRiskDistribution data={rdData} handleGetCustomerDetails={handleGetCustomerDetails}/>
        </Flex>

        {/* State, Region,  */}
        <Flex vertical gap={10} className={Style.table_container}>
          <Flex justify='space-between' align='center' wrap>
            <Title level={4} className={Style.title}>Good in XYZ Bank & Paying to other Banks</Title>  
            <Flex gap={10} align='center' wrap>
              <Segmented
                value={stateView}
                onChange={(value) => setStateView(value)}
                options={["Graph", "Table"]}
              />
              <Segmented
                value={stateTableType}
                onChange={(value) => setStateTableType(value)}
                options={["Account", "Amount"]}
              />
              <button className={Style.download_button} onClick={handleDownload}>
                <img
                  src={DOWNLOAD_IMG}
                  alt="download-button"
                />
              </button>
            </Flex>
          </Flex>
          {stateIsLoading 
            ?<ApplicationLoader/>
            :<> 
            { stateView === "Graph" && <ScrubGraph
              isAccount ={ stateTableType === "Account"} 
              data={ stateTableType ==="Account" ? banksData.account_data : banksData.amount_data}
              handleUpdateStateTable = {handleUpdateStateTable}
              setSelectedState={setSelectedState}
              />
            }
            <ScrubTable 
              isGraph={stateView === "Graph"}
              isAccount ={ stateTableType === "Account"} 
              tableData={ stateTableType ==="Account" ? banksData.account_data : banksData.amount_data}
              selectedState={selectedState}
              setSelectedState={setSelectedState}
              tableLevel={tableLevel}
              setTableLevel={setTableLevel}
              handleUpdateStateTable = {handleUpdateStateTable}
              handleGetCustomerDetails={handleGetCustomerDetails}
              />
            </>
          }
        </Flex>

         {/* Customer Details  */}
        <Flex vertical gap={20} className={Style.table_container}>
          {customersData.data?.length 
              ?
              <>
                <Flex justify='space-between' align='center' wrap>
                  <Title level={4} className={Style.title}>
                    {`Customer Distribution for "${customerPayload.riskCategory}"`} 
                  </Title>  
                  <Flex gap={10} align='center' wrap>
                    <button className={Style.download_button} onClick={handleDownloadCustomer}>
                      <img
                        src={DOWNLOAD_IMG}
                        alt="download-button"
                      />
                    </button>
                  </Flex>
                </Flex>
                <CustomerDetailsTable 
                  customerDetails={customersData} 
                  onPaginationChange={handleCustomerPagination}/>
              </>
              :null}
        </Flex>
      </Flex>
  )
}

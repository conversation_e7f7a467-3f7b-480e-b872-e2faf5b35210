import React, { useState, useCallback } from "react";
import UPLOAD_IMG from "../../../../assets/Images/upload.png";
import { Button } from "antd";
import Style from "./_style.module.scss";
import PropTypes from "prop-types";

export function UploadComponent({ onFileUpload }) {
  const [isDragging, setIsDragging] = useState(false);
  const [files, setFiles] = useState([]);

  const handleDragEnter = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback(
    (e) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(false);

      const droppedFiles = [...e.dataTransfer.files];
      setFiles(droppedFiles);
      onFileUpload(droppedFiles); // Pass files to parent component
    },
    [onFileUpload]
  );

  const handleFileSelect = useCallback(
    (e) => {
      const selectedFiles = [...e.target.files];
      setFiles(selectedFiles);
      onFileUpload(selectedFiles); // Pass files to parent component
    },
    [onFileUpload]
  );

  return (
    <div className={Style.container}>
      <button
        className={`${Style.uploadZone} ${isDragging ? Style.dragging : ""}`}
        onDragEnter={handleDragEnter}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div className={Style.content}>
          <div className={Style.upload_img}>
            <img src={UPLOAD_IMG} alt="Upload" />
          </div>
          <div className={Style.text}>
            <span>Drag & Drop File or </span>
            <div className={Style.browseWrapper}>
              <label htmlFor="file">
               <Button type="link" className={Style.browseButton}>Browse</Button> 
              </label>
              
              <input
                type="file"
                name="file"
                id="file"
                accept=".xlsx"
                className={Style.fileInput}
                onChange={handleFileSelect}
                multiple
              />
            </div>
            {files.length > 0 && (
              <div className={Style.fileList}>
                {/* <strong>Selected Files:</strong> */}
                <ul>
                  {files.map((file, index) => (
                    <li key={`${index}-${file.name}`}>{file.name}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      </button>
    </div>
  );
}

UploadComponent.propTypes={
  onFileUpload : PropTypes.func
}
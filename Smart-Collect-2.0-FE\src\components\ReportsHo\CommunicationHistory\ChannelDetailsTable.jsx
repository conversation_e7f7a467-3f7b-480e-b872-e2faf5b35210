import { useState, useEffect } from "react";
import { Button, Flex, message, Segmented, Table, Typography } from "antd";
import { handleDownloadTable } from "../../../constant";
import ApplicationLoader from ".././../ApplicationLoader";
import { AXIOS } from "../../../apis/ho-Instance";
import Style from "./_style.module.scss";
import DOWNLOAD_IMG from "../../../assets/Images/download.svg";
import PropTypes from "prop-types";
import { LeftOutlined } from "@ant-design/icons";
import dayjs from "dayjs";

const { Text } = Typography;

export function ChannelDetailsTable({ setShowDetailsTable }) {
  const [data, setData] = useState([]);
  const [columns, setColumns] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [detailsView, setDetailsView] = useState("Date wise");
  const [hierarchyPath, setHierarchyPath] = useState([]);

  // Get BUCode and EngagementStartDate from localStorage
  const getInitialParentCode = () => {
    try {
      const user = JSON.parse(localStorage.getItem("user") || "{}") || {};
      return user.BUcode || null;
    } catch {
      return null;
    }
  };
  const getInitialFromDate = () => {
    try {
      const date = JSON.parse(localStorage.getItem("EngagementStartDate") || "null");
      return date || dayjs().format("YYYY-MM-DD");
    } catch {
      return dayjs().format("YYYY-MM-DD");
    }
  };
  const getInitialToDate = () => dayjs().format("YYYY-MM-DD");

  // Get hierarchy data
  const fetchHierarchyData = async (parentCode = null, fromDate = null, toDate = null) => {
    setIsLoading(true);
    // Use dynamic dates for all levels
    const payload = {
      parent_code: parentCode,
      from_date: fromDate,
      to_date: toDate,
    };
    console.log("[ChannelDetailsTable] API Payload:", payload);
    try {
      const response = await AXIOS.post("hierarchy/", payload);
      console.log("[ChannelDetailsTable] API Response:", response.data);
      if (response.status === 200 && response.data) {
        const result = response.data;
        if (result.parent_path) {
          setHierarchyPath(result.parent_path);
        }
        if (result.data && Array.isArray(result.data)) {
          setData(result.data);
          const tableColumns = createColumnsFromData(result);
          setColumns(tableColumns);
        } else {
          setData([]);
          setColumns([]);
        }
      } else {
        setData([]);
        setColumns([]);
        message.warning("No data found");
      }
    } catch (error) {
      console.error("Error fetching hierarchy data:", error);
      message.error("Failed to fetch data");
      setData([]);
      setColumns([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Create columns dynamically from API response
  const createColumnsFromData = (apiResult) => {
    const tableColumns = [];
    tableColumns.push({
      title: "Sr. No.",
      dataIndex: "key",
      key: "key",
      width: 80,
      render: (_, record, index) => <Text>{index + 1}</Text>,
    });
    if (apiResult.columns) {
      Object.entries(apiResult.columns).forEach(([, colName]) => {
        tableColumns.push({
          title: colName,
          dataIndex: colName,
          key: colName,
          render: (value, record) => {
            if (record.drill_down_available && isDrillDownColumn(colName)) {
              return (
                <Text
                  className={Style.blueText}
                  style={{ cursor: "pointer" }}
                  onClick={() => handleDrillDown(record, colName)}
                >
                  {value || "--"}
                </Text>
              );
            }
            return <Text>{value || "--"}</Text>;
          },
        });
      });
    }
    const communicationColumns = [
      { title: "Total Communications", dataIndex: "total_communications", key: "total_communications" },
      { title: "WhatsApp", dataIndex: "whatsapp_count", key: "whatsapp_count" },
      { title: "Blaster", dataIndex: "blaster_count", key: "blaster_count" },
      { title: "VoiceBot", dataIndex: "voicebot_count", key: "voicebot_count" },
      { title: "SMS", dataIndex: "sms_count", key: "sms_count" },
      { title: "IVR", dataIndex: "ivr_count", key: "ivr_count" },
    ];
    tableColumns.push(...communicationColumns);
    return tableColumns;
  };

  const isDrillDownColumn = (colName) => {
    return colName.includes("Code") || colName.includes("Name");
  };

  // Handle drill-down
  const handleDrillDown = (record, colName) => {
    const codeField = colName.replace("Name", "Code");
    const codeValue = record[codeField];
    if (codeValue && record.drill_down_available) {
      // Use the same date range as initial
      fetchHierarchyData(codeValue, getInitialFromDate(), getInitialToDate());
    }
  };

  // Handle back navigation
  const handleBack = () => {
    if (hierarchyPath.length > 0) {
      const parentCode = hierarchyPath[hierarchyPath.length - 1]?.code;
      if (parentCode) {
        fetchHierarchyData(parentCode, getInitialFromDate(), getInitialToDate());
      }
    } else {
      fetchHierarchyData(getInitialParentCode(), getInitialFromDate(), getInitialToDate());
    }
  };

  useEffect(() => {
    fetchHierarchyData(getInitialParentCode(), getInitialFromDate(), getInitialToDate());
  }, []);

  const dataSource = Array.isArray(data)
    ? data.map((item, i) => ({ key: i + 1, ...item }))
    : [];

  const handleDownload = async () => {
    if (data.length === 0) {
      return message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: "Hierarchy Details",
        worksheetName: "Hierarchy-Details",
        tableData: data,
      });
    }
  };

  return (
    <Flex vertical gap={20} className={Style.container}>
      <Flex vertical gap={10}>
        <Flex justify="space-between" align="center">
          <Flex gap={10} align="center">
            <Button
              type="link"
              className={Style.back_btn}
              onClick={() => setShowDetailsTable(false)}
              icon={<LeftOutlined />}
            >
              Summary
            </Button>
            {hierarchyPath.length > 0 && (
              <Button
                type="link"
                className={Style.back_btn}
                onClick={handleBack}
                icon={<LeftOutlined />}
              >
                Back
              </Button>
            )}
          </Flex>
          <Segmented
            value={detailsView}
            onChange={(value) => setDetailsView(value)}
            options={["Date wise", "As on date"]}
          />
          <button className={Style.download_button} onClick={handleDownload}>
            <img src={DOWNLOAD_IMG} alt="download-button" />
          </button>
        </Flex>

        {isLoading ? (
          <ApplicationLoader />
        ) : (
          <Table
            bordered
            virtual
            className={Style.custom_table}
            columns={columns}
            dataSource={dataSource}
            scroll={{
              x: 1600,
              y: 460,
            }}
            pagination={{
              showSizeChanger: false,
            }}
          />
        )}
      </Flex>
    </Flex>
  );
}

ChannelDetailsTable.propTypes = {
  setShowDetailsTable: PropTypes.func,
};

import { Flex } from "antd";
import React, { useState } from "react";
import BACK_IMG from "../../assets/Images/back.png";
import Style from "./_ho-lan.module.scss";
import CustomerAllCards from "../CustomerAllCards";
import { CUSTOMER_DATA, SMART_COLLECT_MENU_IDS } from "../../constant";
import PropTypes from "prop-types";
export function HoLanCards({ handleBack }) {
  const [customerData] = useState(Array(10).fill(CUSTOMER_DATA));

  return (
    <Flex vertical gap={10}>
      <button className={Style.back} onClick={handleBack}>
        <img src={BACK_IMG} alt="back" />
      </button>

      <CustomerAllCards
        pageId={SMART_COLLECT_MENU_IDS.READY_TO_PAY}
        customerData={customerData}
        modalButtonText={"Feedback"}
        isModal={true}
      />
    </Flex>
  );
}

HoLanCards.propTypes = {
  handleBack: PropTypes.func,
};

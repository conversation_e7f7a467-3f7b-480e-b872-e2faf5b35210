from django.urls import path
from rest_framework import routers

import WebApp.views as webappviews
from WebApp.view.dashboard import DashboardView
from WebApp.view.file_upload import CollectionFileUpload
from WebApp.view.delete_disbursement import DeleteDisbursementView
from WebApp.view.geography import GeographyView
from .views import OnClick
from WebApp.view.communication_reports import CommunicationSummaryReport
from WebApp.view.hierarchy_drill_down import HierarchyDrillDownView,DaywiseCommunicationCountsView

router = routers.DefaultRouter()
router.register(r"v1/languages", webappviews.LanguageMstApI, basename="LanguageMstApI")
router.register(
    r"v1/bank-controls", webappviews.BankControlViewSet, basename="BankControlViewSet"
)


urlpatterns = [
    path("dashboard/", DashboardView.as_view(), name="DashboardView"),
    path("promise/", webappviews.PromiseView.as_view(), name="PromiseView"),
    path("denials/", webappviews.DenialsView.as_view(), name="DenialsView"),
    path("claim/", webappviews.ClaimsView.as_view(), name="ClaimsView"),
    path("wrongnumber/", webappviews.WrongNumberView.as_view(), name="WrongNumberView"),
    path(
        "bmallocation/", webappviews.BMAllocationView.as_view(), name="BMAllocationView"
    ),
    path(
        "notcontactable/", webappviews.NonContactable.as_view(), name="NonContactable"
    ),
    path(
        "accountsummaryreport/",
        webappviews.AccountSummaryReport.as_view(),
        name="AccountSummaryReport",
    ),
    path(
        "communicationsummaryreport/",
        webappviews.CommunicationSummaryReport.as_view(),
        name="CommunicationSummaryReport",
    ),
    # path(
    #     "communicationsummaryreport/",
    #     CommunicationSummaryReport.as_view(),
    #     name="CommunicationSummaryReport",
    # ),
    path(
        "datewiseummaryreport/",
        webappviews.DateWiseReport.as_view(),
        name="DateWiseReport",
    ),
    path(
        "communicationsummary/",
        webappviews.CommunicationSummary.as_view(),
        name="CommunicationSummary",
    ),
    path(
        "datewiseresponse/",
        webappviews.DateWiseResponseSummary.as_view(),
        name="DateWiseResponseSummary",
    ),
    path("dpdsummary/", webappviews.DPDSummary.as_view(), name="DPDSummary"),
    path("deucollection/", webappviews.DueCollection.as_view(), name="DueCollection"),
    path("allocation/", webappviews.AllocationAnalysis.as_view(), name="allocation"),
    path("collection/", webappviews.CollectionAnalysis.as_view(), name="collection"),
    path("geomapping/", webappviews.GeoMapping.as_view(), name="GeoMapping"),
    path(
        "performance/",
        webappviews.PerformanceReport.as_view(),
        name="PerformanceReport",
    ),
    path("filter/", webappviews.FilterView.as_view(), name="FilterView"),
    path(
        "dpd_snapshot_top/",
        webappviews.DpdSnapshotTopView.as_view(),
        name="DpdSnapshotTopView",
    ),
    path(
        "dpd_snapshot_other_tables/",
        webappviews.DpdSnapshotChildView.as_view(),
        name="DpdSnapshotChildView",
    ),
    path(
        "communicationhistory/",
        webappviews.GetCustomerConversation.as_view(),
        name="GetCustomerConversation",
    ),
    path("filter/", webappviews.FilterBranch.as_view(), name="FilterBranch"),
    path("feedback/", webappviews.Feedback.as_view(), name="Feedback"),
    path("dnd/", webappviews.DNDView.as_view(), name="DND"),
    path("fieldofficer/", webappviews.FeildOfficer.as_view(), name="FeildOfficer"),
    path(
        "dpd_snapshot_customers/",
        webappviews.DpdSnapshotCustomersView.as_view(),
        name="DpdSnapshotCustomersView",
    ),
    path(
        "update_wrong_number/",
        webappviews.UpdateWrongNumber.as_view(),
        name="UpdateWrongNumber",
    ),
    path(
        "latest_feedback/", webappviews.LatestFeedback.as_view(), name="LatestFeedback"
    ),
    path("bank_details/", webappviews.BankDetails.as_view(), name="BankDetails"),
    path(
        "portfolio_cards/", webappviews.PortfolioCards.as_view(), name="PortfolioCards"
    ),
    path(
        "portfolio_dpdwise_cards/",
        webappviews.PortfolioDpdwiseCards.as_view(),
        name="PortfolioDpdwiseCards",
    ),
    path(
        "portfolio_other_tables/",
        webappviews.PortfolioFirstTable.as_view(),
        name="PortfolioFirstTable",
    ),
    path(
        "total_customers/", webappviews.TotalCustomers.as_view(), name="TotalCustomers"
    ),
    path(
        "total_customers_connected/",
        webappviews.TotalCustomersConnected.as_view(),
        name="TotalCustomersConnected",
    ),
    path(
        "total_amount_promised/",
        webappviews.TotalAmountPromised.as_view(),
        name="TotalAmountPromised",
    ),
    path(
        "total_amount_collected/",
        webappviews.TotalAmountCollected.as_view(),
        name="TotalAmountCollected",
    ),
    path(
        "whatsapp_history/",
        webappviews.WhatsappHistory.as_view(),
        name="WhatsappHistory",
    ),
    path(
        "ai_calls_history/", webappviews.AICallsHistory.as_view(), name="AICallsHistory"
    ),
    path(
        "blaster_history/", webappviews.BlasterHistory.as_view(), name="BlasterHistory"
    ),
    path("sms_history/", webappviews.SMSHistory.as_view(), name="SMSHistory"),
    path("ivr_history/", webappviews.IVRHistory.as_view(), name="IVRHistory"),
    path("dialer_history/", webappviews.DialerHistory.as_view(), name="DialerHistory"),
    path(
        "field_officer_dashboard/",
        webappviews.FieldOfficerDashboard.as_view(),
        name="FieldOfficerDashboard",
    ),
    path(
        "branch_names/", webappviews.FetchBranchNames.as_view(), name="FetchBranchNames"
    ),
    path(
        "today_allocation/",
        webappviews.TodayAllocation.as_view(),
        name="TodayAllocation",
    ),
    path(
        "pending_allocation/",
        webappviews.PendingAllocation.as_view(),
        name="PendingAllocation",
    ),
    path(
        "responseanalysis/",
        webappviews.ResponseAnalysis.as_view(),
        name="ResponseAnalysis",
    ),
    path(
        "collection_analysis/",
        webappviews.CollectionAnalysiss.as_view(),
        name="CollectionAnalysis",
    ),
    path(
        "dpdresponseanalysis/",
        webappviews.DPDResponseAnalysis.as_view(),
        name="DPDResponseAnalysis",
    ),
    path(
        "upload_collection_data/",
        CollectionFileUpload.as_view(),
        name="CollectionFileUpload",
    ),
    path(
        "upload_customer_data/",
        webappviews.UploadCustomerData.as_view(),
        name="UploadCustomerData",
    ),
    path(
        "client_statistics/",
        webappviews.ClientStatistics.as_view(),
        name="ClientStatistics",
    ),
    path(
        "user-conversations/<str:user_id>/",
        webappviews.ConversationAPI.as_view(),
        name="get_user_conversations",
    ),
    path(
        "customers/",
        webappviews.CustomerOfficerAPI.as_view(),
        name="get_loans",
    ),
    path("customers/reallocate/",
        webappviews.CustomerOfficerAPI.as_view(),
        name="get_loan_detail",
    ),
    path(
        "reallocation_details/",
        webappviews.ReAllocationDetails.as_view(),
        name="ReAllocationDetails",
    ),
    path(
        "officer_details/",
        webappviews.CollectionOfficerDetails.as_view(),
        name="CollectioOfficerDetails",
    ),
    
    path(
        "calling_details/",
        webappviews.CallingTeam.as_view(),
        name="CallingTeam",
    ),
    path(
        "calling/reallocate/",
        webappviews.CallingTeam.as_view(),
        name="CallingTeam_reallocate",
    ),
    path(
        "caller_reallocation_details/",
        webappviews.CallerReallocationDetails.as_view(),
        name="CallerReallocationDetails",
    ),
    path(
        "caller_details/",
        webappviews.CallerDetails.as_view(),
        name="CallerDetails",
    ),
    
    path(
        "total_task/",
        webappviews.CallingQueueStatsView.as_view(),
        name="CallingQueueStatsView",
        ),
     path(
        "campaign_detail/",
        webappviews.CampaignWiseCallingQueueView.as_view(),
        name="CampaignWiseCallingQueueView",
        ),
     
     path(
        "pending_task/",
        webappviews.CallingQueuePendingTasksView.as_view(),
        name="CallingQueuePendingTasksView",
        ),
     path(
        "completed_task/",
        webappviews.CallingQueueCompletedTasksView.as_view(),
        name="CallingQueueCompletedTasksView",
        ),
     
    path(
        "filter_officers/",
        webappviews.LoanSearchAPIView.as_view(),
        name="FilterOfficers",  
    ),
    path(
        "alreadypaidfeedback/",
        webappviews.AlreadyPaidFeedback.as_view(),
        name="AlreadyPaidFeedback",
    ),
    path("FO_For_Day/", webappviews.FOForDay.as_view(), name="FOForDay"),
    path("FO_For_Week/", webappviews.FOForWeek.as_view(), name="FOForWeek"),
    path("FO_For_Month/", webappviews.FOForMonth.as_view(), name="FOForMonth"),
    path(
        "datewise_communication_history/",
        webappviews.DatewiseCommunication.as_view(),
        name="DatewiseCommunication",
    ),
    path(
        "forget_password/", webappviews.ForgetPassword.as_view(), name="ForgetPassword"
    ),
    path(
        "categorywiseresponse/",
        webappviews.GetCategoryResponse.as_view(),
        name="GetCategoryResponse",
    ),
    path("customdialer/", webappviews.CustomDialer.as_view(), name="CustomDialer"),
    path("dialerlogs/", webappviews.DialerLogs.as_view(), name="DialerLogs"),
    path("feedbacklogs/", webappviews.FeedbackLogs.as_view(), name="FeedbackLogs"),
    path("dialertrigger/", webappviews.DialerTrigger.as_view(), name="DialerTrigger"),
    path("getbanks/", webappviews.GetBanks.as_view(), name="GetBanks"),
    path("setscreens/", webappviews.SetScreens.as_view(), name="SetScreens"),
    path("addcomponent/", webappviews.AddComponent.as_view(), name="AddComponent"),
    # path("commmaping/", webappviews.CommMapping.as_view(), name="CommMapping"),
    path(
        "delivery_status/", webappviews.DeliveryStatus.as_view(), name="DeliveryStatus"
    ),
    path("getcomponents/", webappviews.GetComponent.as_view(), name="GetComponent"),
    path(
        "getbrokenpromise/",
        webappviews.GetBrokenPromise.as_view(),
        name="GetBrokenPromise",
    ),
    path(
        "getbucketmovement/",
        webappviews.BucketMovementCardsTableView.as_view(),
        name="BucketMovementCardsTableView",
    ),
    path(
        "getsummarization/",
        webappviews.GetSummarization.as_view(),
        name="GetSummarization",
    ),
    path('onclick/', 
         OnClick.as_view(), 
         name='onclick'
    ),
    path(
        "scrub_summary_risk_dpd/",
        webappviews.Scrub_summary_first.as_view(),
        name="scrub_summary_risk_dpd",
    ),
    path(
        "scrub_other_tables/",
        webappviews.Scrub_summary_second.as_view(),
        name="scrub_other_tables",
    ),

    path(
        "scrub_other_tables_customers/",
        webappviews.Scrub_summary_second_customers.as_view(),
        name="scrub_other_tables_customers",
    ),


    path(
        "scrub_summary_customers/",
        webappviews.Scrub_customers_view.as_view(),
        name="Scrub_customers_view",
    ),
    path(
        "scrub_analysis_top/",
        webappviews.Scrub_Analysis_Top_view.as_view(),
        name="Scrub_Analysis_Top_view",
    ),

    path(
        "scrub_analysis_hierarchy_sp/",
        webappviews.Scrub_Analysis_Hierarchy_view.as_view(),
        name="Scrub_Analysis_Hierarchy_view_sp",
    ),
    path(
        "scrub_analysis_customer_new/",
        webappviews.Scrub_Analysis_customers.as_view(),
        name="Scrub_Analysis_customers_new",
    ),
    path(
        "retention_failure/",
        webappviews.Retention_Failure_First_Api.as_view(),
        name="Retention_Failure_First_Api",
    ),
    path(
        "retention_failure_hierarchy/",
        webappviews.Retention_Failure_hierarchy.as_view(),
        name="Retention_Failure_hierarchy",
    ),
    path(
        "allocationanalysisemidate/",
        webappviews.AllocationAnalysisNextEmiDate.as_view(),
        name="AllocationAnalysisNextEmiDate",
    ),
    path(
        "convertrecording/",
        webappviews.ConvertRecording.as_view(),
        name="ConvertRecording",
    ),
    path(
        "custlocation/", webappviews.CustomerLocation.as_view(), name="CustomerLocation"
    ),
    path("airoutes/", webappviews.AI_Routes.as_view(), name="AI_Routes"),
    path("bmcust/", webappviews.GetBMCust.as_view(), name="AI_Routes"),
    path("userprofile/", webappviews.UserProfile.as_view(), name="UserProfile"),
    path("accountsummaryanallysis/", webappviews.AccountSummaryReportAnalysis.as_view(), name="AccountSummaryReportAnalysis"),
    path("collectionofficernc/", webappviews.Collection_Officer_NC.as_view(), name="Collection_Officer_NC"),
    path("getdistinctreasons/", webappviews.GetReasons.as_view(), name="GetReasons"),
    path("delete-disbursement/", DeleteDisbursementView.as_view(), name="DeleteDisbursementView"),
    path('get_campaign_dashboard/', webappviews.CampaignDashboardView.as_view(), name='CampaignDashboardView'),#checked
    path("despute/", webappviews.Despute.as_view(), name="Despute"),
    path("getdespute/", webappviews.GetDespute.as_view(), name="GetDespute"),
    path("dailyallocation/", webappviews.DailyAllocationView.as_view(), name="DailyAllocationView"),
    path("findnumber/", webappviews.FindNumber.as_view(), name="FindNumber"),
    path("getdespute/", webappviews.GetDespute.as_view(), name="GetDespute"),
    path('campaignpromise/', webappviews.CampaignPromiseView.as_view(), name='campaignpromise'),
    path('campaigndenials/', webappviews.CampaignDenialsView.as_view(), name='campaigndenials'),
    path('campaignclaims/', webappviews.CampaignClaimsView.as_view(), name='campaignclaims'),
    path('fetchcampaign/',webappviews.CampaignDetailsView.as_view(), name ='fetchcampaign'),
    path('geography/',GeographyView.as_view(), name ='geography'),
    path('campaign-ai-calls-history/', webappviews.CampaignAICallsHistory.as_view(), name='campaign-ai-calls-history'),
    path('campaign-whatsapp-history/', webappviews.CampaignWhatsappHistory.as_view(), name='campaign-whatsapp-history'),
    path('campaign-blaster-history/', webappviews.CampaignBlasterHistory.as_view(), name='campaign-blaster-history'),
    path('campaign-sms-history/', webappviews.CampaignSMSHistory.as_view(), name='campaign-sms-history'),
    path('campaign-ivr-history/', webappviews.CampaignIVRHistory.as_view(), name='campaign-ivr-history'),
    path('campaign-total-amount-promised/', webappviews.CampaignTotalAmountPromised.as_view(), name='campaign-total-amount-promised'),
    path('campaign-total-customers-connected/', webappviews.CampaignTotalCustomersConnected.as_view(), name='campaign-total-customers-connected'),
    path('hierarchy/', HierarchyDrillDownView.as_view(), name='hierarchy-drill-down'),
    path('daywise-communication-counts/', DaywiseCommunicationCountsView.as_view(), name='daywise-communication-counts'),

]

swaggerurls = [] + router.urls

urlpatterns += router.urls

SKIP_URLS = [
    path("dashboard/", webappviews.DashboardView.as_view(), name="DashboardView"),
    path("delete-disbursement/", DeleteDisbursementView.as_view(), name="DeleteDisbursementView"),
    path(
        "accountsummaryreport/",
        webappviews.AccountSummaryReport.as_view(),
        name="AccountSummaryReport",
    )
]

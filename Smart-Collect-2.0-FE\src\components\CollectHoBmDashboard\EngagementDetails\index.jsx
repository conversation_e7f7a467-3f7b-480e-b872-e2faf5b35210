import React, { useMemo, useState } from "react";
import CUSTOMER_ENGAGEMENT_IMG from "../../../assets/Images/customer-engagement.svg";
import WHATSAPP_IMG from "../../../assets/Images/whatsapp.png";
import BLASTER_IMG from "../../../assets/Images/ai.png";
import AI_IMG from "../../../assets/Images/blaster.png";
import DIALERS_IMG from "../../../assets/Images/customer-care.png";
import SMS_IMG from "../../../assets/Images/image 79.png";
import IVR_IMG from "../../../assets/Images/IVR.png";
import Style from "../_style.module.scss";
import { Flex, Typography } from "antd";
import { Link } from "react-router";
import { SMART_COLLECT_MENU_IDS, formatDigits } from "../../../constant";
import { useTour } from "../../../ContextApis/TourProvider";
import PropTypes from "prop-types";
import classNames from "classnames";

const { Text } = Typography;

export function EngagementDetails({ dashboardData }) {
  const { customerEngagementRef } = useTour();
  const [expanded, setExpanded] = useState(false);

  const handleToggleExpansion = () => setExpanded((prevState) => !prevState);

  const allCommunicationChannel = [
    {
      title: "WhatsApp",
      count: dashboardData?.whatsapp_count,
      image: WHATSAPP_IMG,
      routeKey: SMART_COLLECT_MENU_IDS.WHATSAPP,
    },
    {
      title: "Blaster",
      count: dashboardData?.blaster_count,
      image: BLASTER_IMG,
      routeKey: SMART_COLLECT_MENU_IDS.BLASTER,
    },
    {
      title: "AI Calls",
      count: dashboardData?.aicall_count,
      image: AI_IMG,
      routeKey: SMART_COLLECT_MENU_IDS.AI_CALLS,
    },
    {
      title: "Dialers",
      count: dashboardData?.dialed,
      image: DIALERS_IMG,
      routeKey: SMART_COLLECT_MENU_IDS.DIALERS,
    },
    {
      title: "IVR",
      count: dashboardData?.ivr_count,
      image: IVR_IMG,
      routeKey: SMART_COLLECT_MENU_IDS.IVR_CALLS,
    },
    {
      title: "SMS",
      count: dashboardData?.sms_count,
      image: SMS_IMG,
      routeKey: SMART_COLLECT_MENU_IDS.SMS,
    },
  ];

  const filteredNonZeroChannel = useMemo(() => {
    return allCommunicationChannel.filter((app) => app.count)?.length;
  }, [allCommunicationChannel]);
  return (
    <Flex
      vertical
      justify="space-between"
      gap={0}
      className={Style.customer_engagement}
      ref={customerEngagementRef}
    >
      {/* Total customer engagement */}
      <Flex
        gap={4}
        className={Style.total_container}
        style={{ flexDirection: `${!expanded ? "column" : "row"}` }}
      >
        {/* <Flex gap={8}> */}
        <img
          src={CUSTOMER_ENGAGEMENT_IMG}
          alt="engagement-customer"
          className={Style.img_icon}
        />
        {/* </Flex> */}
        <Flex justify="space-between" style={{ flex: 1 }}>
          <Text className={`${Style.title} ${!expanded ? "down" : "up"}`}>
            Total Customers Engagement
          </Text>
          <Text className={Style.value}>
            {formatDigits(dashboardData?.today_engage || 0)}
          </Text>
        </Flex>
      </Flex>

      {/* details */}
      {filteredNonZeroChannel > 0 && (
        <Flex
          vertical
          className={Style.dropdown_container}
          style={{ padding: `${expanded ? "0.3rem 0.7rem" : "0.7rem"}` }}
        >
          <button
            className={Style.arrow_container}
            onClick={(event) => {
              event.stopPropagation();
              handleToggleExpansion();
            }}
          >
            <span
              className={`${Style.arrow} ${!expanded ? "down" : "up"}`}
            ></span>
          </button>
          {expanded && (
            <Flex
              gap={1}
              justify="space-between"
              align="end"
              className={classNames({
                [Style.normal]:
                  filteredNonZeroChannel === 3 || filteredNonZeroChannel === 2,
                [Style.forOneItem]: filteredNonZeroChannel === 1,
              })}
            >
              {allCommunicationChannel?.map((app, index) => {
                return app.count ? (
                  <Link to={app.routeKey} key={app.title}>
                    <Flex
                      vertical
                      align="center"
                      className={classNames({
                        [Style.sizeIncrease]: filteredNonZeroChannel <= 3,
                      })}
                    >
                      <div className={Style.img_div}>
                        <img src={app.image} alt="whatsapp" />
                      </div>
                      <Text className={Style.engagement_value}>
                        {formatDigits(app.count ?? 0)}
                      </Text>
                      <Text className={Style.engagement_text}>{app.title}</Text>
                    </Flex>
                  </Link>
                ) : null;
              })}
            </Flex>
          )}
        </Flex>
      )}
    </Flex>
  );
}

EngagementDetails.propTypes = {
  dashboardData: PropTypes.object,
};

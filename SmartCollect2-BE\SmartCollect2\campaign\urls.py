import campaign.views as campviews
from django.urls import path
from rest_framework.routers import DefaultRouter

router = DefaultRouter()
router.register('v1/getfilters', campviews.GetFilters, basename='filters')
router.register('v1/getcampaigns', campviews.GetCampaigns, basename='campaigns')
router.register('v1/getcommflows', campviews.GetCommunicationFlows, basename='commflows')
router.register('v1/getWhatsappflows', campviews.GetWhatsAppFlows, basename='whatsappflows')
router.register('v1/getBlasterflows', campviews.GetBlasterFlows, basename='blasterflows')
router.register('v1/getVoiceBotflows', campviews.GetVoiceBotFlows, basename='voicebotflows')
router.register('v1/getIVRflows', campviews.GetIVRFlows, basename='ivrflows')
router.register('v1/getSmsflows', campviews.GetSmsFlows, basename='smsflows')
router.register('v1/getCallingflows', campviews.GetCallingFlows, basename='callingflows')
router.register('v1/getCampaignCategoryOptions', campviews.CampaignTypesViewSet, basename='campaign_category_options')

urlpatterns = router.urls

urlpatterns += [
    path('v1/filters/createfilters', campviews.CreateFilters.as_view(), name='create_filters'),
    path('v1/campaign/createcampaign', campviews.CreateCampaign.as_view(), name='create_campaign'),
    path('v1/campaign/createCommFlow', campviews.CreateCommunicationFlow.as_view(), name='create_commflow'),
    path('v1/campaign/updateFlowIdCommFlow', campviews.UpdateFlowIdCommFlow.as_view(), name='update_flowid_commflow'),
    path('v1/campaign/FinalizeCampaign', campviews.FinalizeCampaign.as_view(), name='finalize_campaign'),
    path('v1/campaign/dropdownoptions', campviews.CampaignCategoryOptions.as_view(), name='campaign_category_options'),
    path('v2/campaign/dropdownoptions', campviews.CampaignCategoryOptionsV2.as_view(), name='campaign_category_options_v2'),
    path('v1/template/registerWhatsapp', campviews.RegisterWhatsappTemplate.as_view(), name='register_whatsapp'),
    path('v1/template/registerBlaster', campviews.RegisterBlasterTemplate.as_view(), name='register_blaster'),
    path('v1/template/registerVoiceBot', campviews.RegisterVoiceBotTemplate.as_view(), name='register_voicebot'),
    path('v1/template/registerIVR', campviews.RegisterIVRTemplate.as_view(), name='register_ivr'),
    path('v1/template/registerSms', campviews.RegisterSmsTemplate.as_view(), name='register_sms'),
    path('v1/template/registerCalling', campviews.RegisterCallingTemplate.as_view(), name='register_calling'),
]

exclude_urls = [
    path('v1/campaign/createCommFlow', campviews.CreateCommunicationFlow.as_view(), name='create_commflow'),
    router.urls[1],
]
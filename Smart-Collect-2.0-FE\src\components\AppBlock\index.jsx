import React, { useState } from "react";
import { Layout } from "antd";
import { Outlet } from "react-router";
import Style from "./_app.module.scss";
import CollectionHeader from "../CollectionHeader";
import { TourProvider } from "../../ContextApis/TourProvider";
import PropTypes from "prop-types";

const { Content } = Layout;

export default function AppBlock({ menuItems }) {
  const [collapsedSideMenu, setCollapsedSideMenu] = useState(true);
  const toggleCollapsedSideMenu = () => {
    setCollapsedSideMenu(!collapsedSideMenu);
  };
  return (
    <TourProvider>
      <Layout className={Style.app_container} hasSider>
        <Layout style={{ background: "white" }}>
          <CollectionHeader
            menuItems={menuItems}
            collapsedSideMenu={collapsedSideMenu}
            toggleCollapsedSideMenu={toggleCollapsedSideMenu}
            setCollapsedSideMenu={setCollapsedSideMenu}
          />

          <Content
            className={`${Style.app_container_content} ${
              !collapsedSideMenu ? Style.blur : ""
            }`}
          >
            <Outlet />
          </Content>
        </Layout>
      </Layout>
    </TourProvider>
  );
}

AppBlock.propTypes = {
  menuItems: PropTypes.array,
};

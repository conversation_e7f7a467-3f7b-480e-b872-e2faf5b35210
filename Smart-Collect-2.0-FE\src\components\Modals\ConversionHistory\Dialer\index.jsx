import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Typography } from "antd";
import DIALER_IMG from "../../../../assets/Images/image 113.svg";
import React, {
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import Style from "./_style.module.scss";
import { handleDateFormatter } from "../../../../constant";
import { AXIOS } from "../../../../apis/ho-Instance";
import PropTypes from "prop-types";

const { Text } = Typography;

export const Dialer = forwardRef(({ dialerCallData }, ref) => {
  const audioRefs = useRef([]);
  const [summaryMap, setSummaryMap] = useState({});
  const [visibleSummary, setVisibleSummary] = useState({});
  const [loadingMap, setLoadingMap] = useState({});

  // Expose a method to stop all audio when modal closes
  useImperativeHandle(ref, () => ({
    stopAllAudio: () => {
      audioRefs.current.forEach((audio) => {
        if (audio) {
          audio.pause();
          audio.currentTime = 0; // Reset to beginning
        }
      });
    },
  }));

  // Grouping AI calls by conversation_date
  const groupedCalls = dialerCallData.reduce((acc, call) => {
    const { conversation_date, person } = call;
    if (!acc[conversation_date]) {
      acc[conversation_date] = {
        calls: [],
        person: person,
      };
    }
    acc[conversation_date].calls.push(call);
    return acc;
  }, {});

  // Handle show summary related dialer
  const toggleSummary = async (recording) => {
    const isVisible = visibleSummary[recording];

    setVisibleSummary((prev) => ({
      ...prev,
      [recording]: !isVisible,
    }));

    if (!isVisible) {
      setLoadingMap((prev) => ({ ...prev, [recording]: true }));
      try {
        const res = await AXIOS.post("getsummarization/", {
          recording,
          call_type: "Dialer",
        });
        if (res.status === 200) {
          setSummaryMap((prev) => ({
            ...prev,
            [recording]: res.data.Summarization,
          }));
        }
      } catch (err) {
        console.error("Summary fetch error:", err);
      } finally {
        setLoadingMap((prev) => ({ ...prev, [recording]: false }));
      }
    }
  };

  return (
    <Flex className={Style.dialer_container} vertical gap={20}>
      {Object.entries(groupedCalls).map(([date, { calls, person }]) => {
        const modifyPersonSpell =
          person?.toLowerCase() === "Gaurantator" ? "Guarantor" : person;
        return (
          <Flex key={date} vertical gap={10} className={Style.group_container}>
            {/* Show Date Once */}
            <Flex justify="center">
              <Text className={Style.date}>{handleDateFormatter(date)}</Text>
            </Flex>

            {/* Show AI Call Heading Once */}
            <Flex className={Style.dialer_chat_box} vertical gap={10}>
              <Flex vertical>
                <Flex justify="center" gap={5} align="baseline">
                  <img src={DIALER_IMG} className={Style.img} alt="AI Icon" />
                  <Text className={Style.dialer_text}>Dialer Call</Text>
                </Flex>
                <Flex justify="center">
                  <Text className={Style.belongs_text}>
                    ({modifyPersonSpell || "Unknown"})
                  </Text>
                </Flex>
              </Flex>

              {/* List all recordings for that date */}
              <Flex className={Style.response_container} vertical gap={5}>
                {calls.map((call, index) => {
                  const isVisible = visibleSummary[call.recording];
                  const summary = summaryMap[call.recording];
                  const isLoading = loadingMap[call.recording];

                  return (
                    <Flex
                      key={`${index}-${call.recording}`}
                      justify="center"
                      vertical
                    >
                      {call.recording ? (
                        <Flex vertical gap={10}>
                          <Flex
                            justify="space-between"
                            align="center"
                            gap={10}
                            className={Style.audio_container}
                          >
                            <audio
                              ref={(el) => (audioRefs.current[index] = el)}
                              controls
                            >
                              <source src={call.recording} type="audio/ogg" />
                              <track
                                kind="captions"
                                label="No captions available"
                              />
                            </audio>
                            <Button
                              className={Style.view_btn}
                              onClick={() => toggleSummary(call.recording)}
                            >
                              {isVisible ? "Hide Summary" : "View Summary"}
                            </Button>
                          </Flex>

                          {isVisible && (
                            <Flex vertical className={Style.summary_container}>
                              <Text className={Style.title}>Summary:</Text>
                              <Spin spinning={isLoading}>
                                <p
                                  dangerouslySetInnerHTML={{
                                    __html: summary || "No summary available.",
                                  }}
                                  className={Style.text}
                                ></p>
                              </Spin>
                            </Flex>
                          )}
                        </Flex>
                      ) : (
                        <Text className={Style.response_text}>
                          Call Attempted, Customer Did Not Pick Up
                        </Text>
                      )}
                    </Flex>
                  );
                })}
              </Flex>
            </Flex>
          </Flex>
        );
      })}
    </Flex>
  );
});

Dialer.propTypes = {
  dialerCallData: PropTypes.array,
};

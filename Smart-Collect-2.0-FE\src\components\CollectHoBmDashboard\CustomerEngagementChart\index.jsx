import React, { useState } from "react";
import Chart from "react-apexcharts";
import Style from "../_style.module.scss";
import { Typography, Flex, Select } from "antd";
import { handleDateFormatter } from "../../../constant";
import { useTour } from "../../../ContextApis/TourProvider";
import PropTypes from "prop-types";

const { Text } = Typography;

export const CustomerEngagementChart = ({engagementData}) => {
  const {customerDetailsGraphRef} = useTour();
  const [selectedLastDays, setSelectedLastDays] = useState(15);
  const sortedData = engagementData?.sort((a, b) => new Date(a.CreatedDate) - new Date(b.CreatedDate));
  // Get the last 15 entries
  const last15Data = sortedData.slice(-selectedLastDays);
  const categories = last15Data?.map((item) => handleDateFormatter(item.CreatedDate));
  const seriesData = last15Data?.map((item) => item.total_engaged);
  const maxEngagement = Math.ceil(Math.max(...seriesData, 10) / 10) * 10 + 20; 

  // Change the last days 
  const handleChange =(value)=>{
    setSelectedLastDays(Number(value));
  }

  const chartOptions = {
    chart: {
      type: "bar",
      height: 350,
      toolbar: { show: false },
    },
    plotOptions: {
      bar: {
        columnWidth:"35px", // Adjust this to control bar width
        distributed: false, // Set to true if you want different colors for each bar
      },
    },
    title: {
      text: "",
      align: "left",
      style: { fontSize: "14px", color: "#407BFF", fontWeight:"normal", fontFamily:"Kanit" },
    },
    xaxis: {
      categories: categories,
      title: { 
        text: "Days",
        style: { fontSize: "16px", color: "#407BFF", fontWeight:"normal", fontFamily:"Kanit" },
       },
      labels: {
        style: {
          colors: "#407BFF", // Blue color for X-axis labels
          fontWeight: 500,
          fontSize:"9px"
        }
      },   
      axisBorder: {
        color: "#0F2050" // Blue color for X-axis border
      },
    },
    yaxis: {
      title: { text: "", style: { fontSize: "12px", color: "#407BFF" } },
      labels: {
        style: {
          colors: "#407BFF", // Blue color for Y-axis labels
          fontWeight: 500
        }
      },
      axisBorder: {
        show: true,
        color: "#0F2050" // Blue color for Y-axis border
      },
      min:0,
      max:maxEngagement
    },
    colors: ["#1E40AF"], // Dark Blue color
    dataLabels: {
      enabled: true,
      formatter: function (val) {
        return val === 0 ? '' : val; // Hide label for 0, show for others
      },
      style: {
        fontSize: "12px",
        fontWeight: "bold",
        colors: ["#FFFFFF"],
      }
    },
    // dataLabels: {
    //   enabled: true,
    //   style: {
    //     fontSize: "12px",
    //     fontWeight: "bold",
    //     colors: ["#000000"], // Black text for 0 values, white for others
    //   },
    // },
    fill: {
      opacity: 1
    },
    grid: { borderColor: "#C6D1ED" },
    legend: { position: "top", markers: { fillColors: ["#1E40AF"] } },
  };

  const series = [
    {
      name: "Total Engaged",
      data: seriesData, // Use API data for y-axis
    },
  ];

  return (
    <div className={Style.chart_container} ref={customerDetailsGraphRef}>
      <Flex justify="space-between">
        <Text className={Style.title} style={{color:"#407BFF"}}>
          Customers Engagement
        </Text> 
        <Flex justify="end" gap={10} align="center">
          <Text className={Style.day_title}>Select Days:</Text> 
          <Select
            defaultValue={selectedLastDays}
            value={selectedLastDays}
            style={{ width: 120 }}
            onChange={handleChange}
            options={[
              { value: 7, label: 'Last 7 days' },
              { value: 15, label: 'Last 15 days' },
              { value: 30, label: 'Last 30 days' },
            ]}
            className={Style.custom_select}
          />
        </Flex>
      </Flex>
      
      <Chart 
      options={chartOptions} 
      series={series} 
      type="bar" 
      height={270} />
    </div>
  );
};

CustomerEngagementChart.propTypes={
  engagementData: PropTypes.array,
}
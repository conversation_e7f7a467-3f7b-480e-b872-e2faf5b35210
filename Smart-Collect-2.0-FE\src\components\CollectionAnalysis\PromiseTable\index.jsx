import { Table, Typography, Flex, Select } from "antd";
import React, { useEffect, useState } from "react";
import { AXIOS } from "../../../apis/ho-Instance";
import { formatAmount } from "../../../constant";
import Style from './_style.module.scss';
import ApplicationLoader from "../../ApplicationLoader";

const { Text } = Typography;

export function PromiseTable() {
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedState, setSelectedState] = useState(null);
  const [selectedRegion, setSelectedRegion] = useState(null);
  const [selectedBranch, setSelectedBranch] = useState(null);
  const [selectedReason, setSelectedReason] = useState("yesterday");
  const [selectedDates, setSelectedDates] = useState({
    fromDate: new Date().toISOString().split("T")[0],
    toDate: new Date().toISOString().split("T")[0]
  });

  // Handle lan functionality
  const handleLanPromiseClick = (lanPromise) => {
        console.log('LAN Promise clicked:', lanPromise);
  };

  // Get state
  const handleGetState = async () => {
      setIsLoading(true);
        try {
        const response = await AXIOS.post("collection/", {
          from_date:selectedDates.fromDate,
          to_date:selectedDates.toDate,
          BasedOn: "PromiseDate",
        });
        if(response.status === 200){
            setData(response.data)
        }
        } catch (error) {
        console.log("Error in EMI", error);
        }finally{
          setIsLoading(false);
        }
  };

  //Get Region, branch, CO.
  const handleGetOtherColsData = async({BUType, BUName})=>{
      setIsLoading(true);
        try {
            const res = await AXIOS.post("collection/",{
              from_date:selectedDates.fromDate,
              to_date:selectedDates.toDate,
              BasedOn: "PromiseDate",
              BUType,
              BUName
            })
            if(res.status === 200){
                setData(res.data);
            }
        } catch (error) {
            console.log("Error in Emi table", error?.message);
        }finally{
          setIsLoading(false);
        }
  }

  // Handle State Click → Get Regions
  const handleStateClick = (state) => {
      setSelectedState(state);
      setSelectedRegion(null); // Reset Region when State is selected
      setSelectedBranch(null); // Reset Branch when State is selected  
      handleGetOtherColsData({ BUType: "State", BUName: state });
  };

  // Handle Region Click → Get Branches
  const handleRegionClick = (region) => {
      setSelectedRegion(region);
      setSelectedBranch(null); // Reset Branch when Region is selected
      handleGetOtherColsData({ BUType: "Region", BUName: region });
  };

  // Handle Branch Click → Get Collection Officers
  const handleBranchClick = (branch) => {
      setSelectedBranch(branch);
      handleGetOtherColsData({ BUType: "Branch", BUName: branch });
  };

  // Change Date
  const handleDateSelection = (value) => {
    setSelectedReason(value)
    if (value === "yesterday") {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      setSelectedDates({ 
        fromDate: yesterday.toISOString().split("T")[0], 
        toDate: yesterday.toISOString().split("T")[0] 
      });
    } else if (value === "last_week") {
      const lastWeekDate = new Date();
      lastWeekDate.setDate(lastWeekDate.getDate() - 7);
      setSelectedDates({ 
        fromDate: lastWeekDate.toISOString().split("T")[0], 
        toDate: lastWeekDate.toISOString().split("T")[0] 
      });
    }
  };

  const columns = [
      {
          title: 'Sr. No.',
          dataIndex: 'key',
          rowScope: 'row',
          width: 90,
          render: (_, data) => <Text>{data?.key}.</Text>
      },
      {
        title: 'State',
        dataIndex: 'State',
        render: (_, {State}) => (
            <Text
            className={Style.blueText}
            onClick={() => handleStateClick(State)}
          >
            {State || "--"}
          </Text>
        ),
      },
      ...(selectedState ?
      [{
          title: 'Region',
          dataIndex: 'region',
          render: (_, {Region}) => (
            <Text
            className={Style.blueText}
            onClick={() => handleRegionClick(Region)}
          >
            {Region || "--"}
          </Text>
          ),
      }]
      : []),
      ...(selectedRegion
        ? [
            {
              title: "Branch",
              dataIndex: "Branch",
              render: (_, { Branch }) => (
                <Text
                className={Style.blueText}
                onClick={() => handleBranchClick(Branch)}
                >
                  {Branch || "--"}
                </Text>
              ),
            },
          ]
        : []),
        ...(selectedBranch
            ? [
                {
                  title: "Collection Officer",
                  dataIndex: "CollectionOfficer",
                  render: (_, { CollectionOfficer }) => (
                    <Text>{CollectionOfficer || "--"}</Text>
                  ),
                },
              ]
        : []),
      {
          title: '#LAN',
          dataIndex: 'lan',
          render: (_, data) => (
              <Text  
              onClick={() => handleLanPromiseClick(data.lanPromise)}  
              style={{ cursor: 'pointer', color: 'black' }}>
                  {data?.lan}
              </Text>
          ),
      },
      {
          title:'Amount Collected',
          dataIndex:'amount',
          key:'amount',
          render: (_, {amount}) => (
            <Text>₹ {formatAmount(amount)}</Text>
        ),
      },
  ];

  const sortedData = selectedState
      ? [...data].sort((a, b) => (a.region === selectedState ? -1 : 1))
      : data;

  const dataSource = Array.isArray(sortedData)? sortedData.map((data, i) => ({
      key: i + 1,
      ...data
  })):[];

  useEffect(()=>{
    handleGetState();
  },[selectedDates])

  return (
    <Flex vertical gap={20}>
      <div className={Style.dropdownContainer}>
        <Select
          className={Style.dropdown}
          defaultValue={selectedReason}
          onChange={handleDateSelection}
          options={[
            { label: 'Yesterday', value: 'yesterday' },
            { label: 'Last week', value: 'last_week' },
        ]}
      />
      </div>
      {/* <Spin tip={"Loading..."} spinning={isLoading}> */}
         {isLoading
         ?<ApplicationLoader/>
         : <Table
              bordered
              virtual
              className={Style.CustomTable}
              columns={columns}
              dataSource={dataSource}
              scroll={{
                  x:800,
                  y: 460,
              }}
              pagination={{
                showSizeChanger:false
              }}
          />}
      {/* </Spin> */}
    </Flex>
  );
}
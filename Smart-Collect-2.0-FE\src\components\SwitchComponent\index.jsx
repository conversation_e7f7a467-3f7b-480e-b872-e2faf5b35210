import React, { useEffect, useId, useState } from "react";
import Style from "./_switch.module.scss";
import PropTypes from "prop-types";

export function SwitchComponent({
  onText,
  offText,
  checked,
  onToggle,
  onColor,
  offColor,
  isSnapshot=false
}) {
  const id = useId();
  const [isOn, setIsOn] = useState(false);
  // // Initial
  useEffect(() => {
    setIsOn(checked);
  }, [checked]);

  return (
    <div className={`${Style.switch} ${isSnapshot ? Style.snap : ""}`}>
      <input
        type="checkbox"
        id={id}
        checked={isOn}
        onChange={onToggle}
        className={Style.switch_input}
        style={{ display: "none" }}
        aria-checked={isOn}
        aria-label={isOn ? offText : onText}
      />
      <label
        className={`${Style.switch_label} ${isOn ? "on" : ""}`}
        htmlFor={id}
        style={{
          "--on-color": onColor,
          "--off-color": offColor,
        }}
        aria-label={isOn ? offText : onText}
      >
        <span className={`${Style.switch_track} ${isOn ? "on" : ""}`}>
          <span>{!isOn ? onText : offText}</span>
        </span>
        <span
          className={`${Style.switch_thumb} ${isOn ? "on" : ""}`}
        ></span>
      </label>
    </div>
  );
}

SwitchComponent.propTypes={
  onText: PropTypes.string,
  offText: PropTypes.string,
  checked: PropTypes.bool,
  onToggle: PropTypes.func,
  onColor: PropTypes.string,
  offColor: PropTypes.string,
  isSnapshot: PropTypes.bool,
}
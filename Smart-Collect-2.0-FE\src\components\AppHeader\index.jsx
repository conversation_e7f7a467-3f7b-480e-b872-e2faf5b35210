import React, { useState } from "react";
import { Flex, Typography } from "antd";
import { SwitchComponent } from "../SwitchComponent";
import FilterButton from "./FilterButton";
import { useNavigate, useLocation } from "react-router";
import BACK_IMG from "../../assets/Images/Vector-1.png";
import Style from "./_app-header.module.scss";
import PropTypes from "prop-types";
import { SMART_COLLECT_MENU_IDS, SUMMARY_FILTERS } from "../../constant";
import ReasonFilter from "./ReasonFilter";

const { Title, Text } = Typography;

export default function AppHeader({
  title,
  isDashboard = true,
  isDashboardOtherPages = false,
  isSwitchOn = false,
  isAccountReportsPage = false,
  isAnalysisSwitch = false,
  selectedReason = null,
  setSelectedReason = () => {},
  setIsAnalysisSwitch = () => {},
  setIsSwitchOn = () => {},
  applyFilters,
  ClearFilters,
  selectedDate,
  pageId = null,
}) {
  const location = useLocation();
  const navigate = useNavigate();
  const UserDetails = JSON.parse(localStorage.getItem("user"));
  const [filterVisible, setFilterVisible] = useState(false);
  const not_show_back_headers = ["Client table"];

  const storedFilter = localStorage.getItem("filterDate");

  const handleGoBack = () => {
    // Default back navigation if already on dashboard
    if (location.key !== "default") {
      navigate(-1);
    } else {
      navigate("/");
    }
    if (storedFilter) {
      const parsedFilter = JSON.parse(storedFilter);
      const updatedFilter = {
        ...parsedFilter,
        disbursementID: "",
        customerNames: "",
        loanTypes: "",
        conclusion: null,
      }; //reset the ids filter if they go back
      localStorage.setItem("filterDate", JSON.stringify(updatedFilter));
      localStorage.setItem(
        SUMMARY_FILTERS.summaryFilter,
        JSON.stringify(updatedFilter)
      );
    }
  };

  return (
    <Flex
      align="center"
      justify="space-between"
      className={Style.app_header}
      style={{
        borderBottom: `${title === "Dashboard" ? null : "1px solid #c3c2c252"}`,
      }}
      gap={10}
    >
      <div>
        {!not_show_back_headers.includes(title) ? (
          <Flex
            align="center"
            gap={8}
            onClick={handleGoBack}
            style={{ cursor: "pointer" }}
          >
            <div className={Style.back}>
              <img src={BACK_IMG} alt="back icon" />
            </div>
            <Title level={4} className={Style.title}>
              {title}
            </Title>
          </Flex>
        ) : (
          <Flex align="center" gap={8}>
            <Title level={4} className={Style.title}>
              {title}
            </Title>
          </Flex>
        )}
      </div>

      <div className={Style.middle_column}>
        <Text className={Style.bank_name}>{UserDetails?.BankMstName}</Text>
      </div>

      <Flex align="center" justify="end" gap={10} className={Style.end_column}>
        {pageId === SMART_COLLECT_MENU_IDS.REFUSED_TO_PAY && (
          <ReasonFilter
            selectedReason={selectedReason}
            setSelectedReason={setSelectedReason}
          />
        )}

        {!isDashboard && (
          <SwitchComponent
            onText={"Table"}
            offText={"Cards"}
            onColor={"#0F2050"}
            offColor={"#E4F8F9"}
            checked={isSwitchOn}
            onToggle={() => setIsSwitchOn((prev) => !prev)}
          />
        )}
        {isAccountReportsPage && (
          <SwitchComponent
            onText={"Analysis"}
            offText={"Table"}
            onColor={"#0F2050"}
            offColor={"#E4F8F9"}
            checked={isAnalysisSwitch}
            onToggle={() => setIsAnalysisSwitch((prev) => !prev)}
          />
        )}

        {(location.pathname.startsWith("/ho/dashboard") ||
          location.pathname.startsWith("/ho/campaign-summary")||
          location.pathname.startsWith("/ho/reports")
        ) && (
          <FilterButton
            visible={filterVisible}
            setVisible={setFilterVisible}
            applyFilters={applyFilters}
            ClearFilters={ClearFilters}
            selectedDate={selectedDate}
            isDashboardOtherPages={isDashboardOtherPages}
            pageId={pageId}
          />
        )}
      </Flex>
    </Flex>
  );
}

// Define the prop types
AppHeader.propTypes = {
  title: PropTypes.any,
  isDashboard: PropTypes.bool,
  isDashboardOtherPages: PropTypes.bool,
  isAccountReportsPage: PropTypes.bool,
  isSwitchOn: PropTypes.bool,
  isAnalysisSwitch: PropTypes.bool,
  selectedReason: PropTypes.string,
  setIsAnalysisSwitch: PropTypes.func,
  setSelectedReason: PropTypes.func,
  setIsSwitchOn: PropTypes.func,
  applyFilters: PropTypes.func,
  ClearFilters: PropTypes.func,
  selectedDate: PropTypes.object,
  pageId: PropTypes.string,
};

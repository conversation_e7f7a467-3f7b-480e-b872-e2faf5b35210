import pandas as pd
import numpy as np
from sqlalchemy import create_engine,update,MetaData, Table, text
from datetime import datetime
import os

# Load Artha sidhi data and clean data
DATABASE = "SmartCollect2"
USERNAME = "postgres"
PASSWORD = "Markytics12345"
HOST = "*************"
PORT = 5432

# Create database engine
engine = create_engine(f"postgresql://{USERNAME}:{PASSWORD}@{HOST}:{PORT}/{DATABASE}")

class DatabaseManager:
    """Database manager class to handle single engine instance and connections"""
    
    def __init__(self):
        self.engine = None
    
    def initialize_engine(self):
        """Initialize the database engine once"""
        if self.engine is None:
            connection_string = f"postgresql://{os.environ.get('DATABASE_USER')}:{os.environ.get('DATABASE_PASSWORD')}@{os.environ.get('DATABASE_HOST')}:{os.environ.get('DATABASE_PORT')}/{os.environ.get('DATABASE_NAME')}"
            self.engine = create_engine(connection_string)
            print("Database engine initialized successfully!")
        return self.engine
    
    def get_engine(self):
        """Get the engine instance, initialize if not already done"""
        if self.engine is None:
            self.initialize_engine()
        return self.engine
    
def clean_column(value):

    if pd.isna(value):
        return None
    return str(value).strip()

# Function to convert Excel serial date to Python date
def convert_excel_serial_date(serial):
    if pd.isna(serial):
        return None
    try:
        # Try to parse as regular date first
        return pd.to_datetime(serial, errors='raise').date()
    except:
        try:
            # If that fails, try as Excel serial date
            if isinstance(serial, str):
                serial = float(serial.replace(',', ''))
            return (datetime(1899, 12, 30) + pd.Timedelta(days=serial)).date()
        except:
            return None

# Function to process branch information
def process_branch(branch_text):
    if pd.isna(branch_text):
        return None, None
    
    parts = str(branch_text).strip().split('-', 1)
    if len(parts) == 2:
        return parts[0].strip(), parts[1].strip()
    return branch_text, branch_text

# Function to clean the DisbursementID column (only removing dashes)
def clean_disbursement_id(value):
    if pd.isna(value):
        return value
    value = str(value)
    # Remove only - characters
    value = value.replace("-", "")
    return value.strip()

def clean_mobile_number(mobile):
    # Check if value is missing
    if pd.isna(mobile) or str(mobile).strip() in ['', 'nan', 'None']:
        return None
    
    # Convert to string and strip whitespace
    # If it's a float (e.g., 9011100040.0), convert to int first to remove decimal
    if isinstance(mobile, float):
        mobile = str(int(mobile))
    else:
        mobile = str(mobile).strip()
    
    # Remove spaces and other non-digit characters
    mobile = ''.join(filter(str.isdigit, mobile))
    
    # Handle cases where number starts with 91 (country code) and is 12 digits
    if len(mobile) == 12 and mobile.startswith('91'):
        mobile = mobile[2:]  # Remove country code
    
    # Handle cases where the number is valid (usually 10 digits in India)
    if len(mobile) >= 10:
        return mobile[-10:]  # Return the last 10 digits
    
    # If the number has at least some digits but fewer than 10, return it as is
    if len(mobile) > 0:
        return mobile
    
    return None  # For completely invalid numbers

db_manager = DatabaseManager()

def check_existing_collections(coll_df, bank_id):
    """Check existing collections using the shared engine"""
    engine = db_manager.get_engine()
    
    with engine.connect() as connection:
        # Get all disbursement IDs and collection dates from the dataframe
        all_disbursement_ids = coll_df['DisbursementID'].tolist()
        all_collection_dates = [date.strftime('%Y-%m-%d') if not pd.isna(date) else None 
                               for date in coll_df['CollectionDate']]
        
        # Create a list of tuples (DisbursementID, CollectionDate)
        collection_keys = list(zip(all_disbursement_ids, all_collection_dates))
        
        # Prepare query to fetch all existing records in one go
        existing_collections = {}
        
        fetch_query = text("""
            SELECT "DisbursementID", "CollectionDate", "CollectedAmount" 
            FROM public."CollectionFile"
            WHERE "DisbursementID" IN :disbursement_ids
            AND "BankMstID" = :bank_id
        """)
        
        # Execute the query with all IDs at once
        result = connection.execute(fetch_query, {"disbursement_ids": tuple(all_disbursement_ids), "bank_id": bank_id}).fetchall()
        
        # Create a dictionary for quick lookup using both DisbursementID and CollectionDate as a composite key
        for row in result:
            disb_id = row[0]
            coll_date = row[1].strftime('%Y-%m-%d') if row[1] else None
            coll_amount = row[2]
            existing_collections[(disb_id, coll_date)] = coll_amount
            
        return existing_collections


def check_existing_account_summary(demand_df, bank_id):
    """Check existing account summary using the shared engine"""
    engine = db_manager.get_engine()
    
    with engine.connect() as connection:
        # Get all disbursement IDs from the dataframe
        all_disbursement_ids = demand_df['DisbursementID'].tolist()
        
        # Prepare query to fetch all existing records in one go
        fetch_query = text("""
            SELECT "DisbursementID", "NextEMIDate", "EMIAmount" FROM public."AccountSummary"
            WHERE "DisbursementID" IN :disbursement_ids
            AND "BankMstID" = :bank_id
        """)
        
        # Execute the query with all IDs at once
        result = connection.execute(fetch_query, {"disbursement_ids": tuple(all_disbursement_ids), "bank_id": bank_id}).fetchall()
        
        # Create a dictionary for quick lookup
        existing_records = {row[0]: (row[1], row[2]) for row in result}
        return existing_records


def process_arhta_sidhi_collection_data(coll_df):
    # Load CSV file

    # Clean and map columns
    coll_df['CustomerID'] = coll_df['Member No'].apply(clean_column)
    coll_df['DisbursementID'] = coll_df['Loan No'].apply(clean_disbursement_id)
    coll_df['CustomerName'] = coll_df['Member'].apply(clean_column)
    coll_df['LoanType'] = coll_df['Loan Type'].apply(clean_column)
    coll_df['DisbursementAmt'] = coll_df['Loan Amount']
    coll_df['EMIAmount'] = coll_df['EMI Amount']
    coll_df['DisbursementDate'] = pd.to_datetime(coll_df['Loan Date'], errors='coerce')
    coll_df['CollectedAmount'] = coll_df['Collection Amount']
    coll_df['POS'] = coll_df['POS']
    coll_df['CollectionDate'] = pd.to_datetime(coll_df['Received Date'], errors='coerce')
    coll_df['PaymentFrequency'] = coll_df['Repayment Frequency']
    coll_df['PrincipleCollected'] = coll_df['Principle Collection Amount']
    coll_df['InterestCollected'] = coll_df['Interest Collection Amount']
    coll_df['TransactionNumber'] = coll_df['Receipt No']
    
    # Process branch information
    coll_df['BranchCode'], coll_df['BranchName'] = zip(*coll_df['Branch Name'].apply(process_branch))
    return coll_df


def get_arthasidhi_recovery_columns(self):
    """Get required columns for Arthasidhi recovery file type"""
    return [
        'Member No', 'Loan No', 'Member', 'Loan Type', 'Loan Amount', 
        'EMI Amount', 'Loan Date', 'Collection Amount', 'POS', 
        'Received Date', 'Repayment Frequency', 'Principle Collection Amount', 
        'Interest Collection Amount', 'Receipt No', 'Branch Name'
    ]


def find_header_row(file_path, required_columns):
    """Find the row index where the required columns are located"""
    try:
        # Read the first few rows to find headers
        temp_df = pd.read_excel(file_path, header=None, nrows=20)
        
        for row_idx in range(len(temp_df)):
            row_values = temp_df.iloc[row_idx].astype(str).str.strip().tolist()
            # Check if required columns are present in this row
            matches = sum(1 for col in required_columns if col in row_values)
            if matches >= len(required_columns) * 0.7:  # At least 70% columns match
                return row_idx
        
        return 0  # Default to first row if no clear header found
    except Exception as e:
        print(f"Error finding header row: {e}")
        return 0


def read_and_process_xls_file(file_path, bank_id):
    """Read XLS file, find column headers, and process the data"""
    try:
        required_columns = [
            'Member No', 'Loan No', 'Member', 'Loan Type', 'Loan Amount', 
            'EMI Amount', 'Loan Date', 'Collection Amount', 'POS', 
            'Received Date', 'Repayment Frequency', 'Principle Collection Amount', 
            'Interest Collection Amount', 'Receipt No', 'Branch Name'
        ]
        
        # Find the header row
        header_row = find_header_row(file_path, required_columns)
        
        # Read the complete file with proper header
        df = pd.read_excel(file_path, header=header_row)
        
        # Reset index to ensure clean indexing
        df = df.reset_index(drop=True)
        
        # Remove any completely empty rows
        df = df.dropna(how='all').reset_index(drop=True)
        
        print(f"File loaded successfully. Total rows: {len(df)}")
        print(f"Columns found: {list(df.columns)}")
        
        # Now process the data
        return process_arthasidhi_recovery_data(df, bank_id)
        
    except Exception as e:
        raise Exception(f"Error reading and processing XLS file: {str(e)}")
def insert_collection_data(new_records, bank_id=13):
    engine = db_manager.get_engine()

    # Step 1: Fetch branch name → BranchMstID mapping for the given bank
    with engine.connect() as connection:
        branch_map_result = connection.execute(
        text("""
            SELECT "BranchName", "BranchMstID"
            FROM "BranchMst"
            WHERE "BankMstID" = :bank_id
        """),
        {'bank_id': bank_id}
    ).mappings()  # <-- This is key!

        branch_map = {row['BranchName']: row['BranchMstID'] for row in branch_map_result}


    # Step 2: Prepare insert data with BranchMstID pre-filled
    insert_data = []
    for row in new_records:
        branch_name = row['BranchName']
        branch_mst_id = branch_map.get(branch_name)  # None if not found

        insert_data.append({
            'DisbursementID': row['DisbursementID'],
            'PrincipleCollected': row['PrincipleCollected'],
            'InterestCollected': row['InterestCollected'],
            'InstStartDate': row['DisbursementDate'],
            'CollectedAmount': row['CollectedAmount'],
            'CollectionDate': row['CollectionDate'],
            'BMID': None,
            'POS': row['POS'],
            'ExtraColumn1': None,
            'ExtraColumn2': None,
            'ExtraColumn3': None,
            'ExtraColumn4': None,
            'LoanType': row['LoanType'],
            'BankMstID': bank_id,
            'BranchMstID': branch_mst_id,
            'CreatedDate': datetime.now().date(),
            'BranchName': branch_name,
            'CustomerId': row['CustomerID'],
            'TransactionNumber': row['TransactionNumber']
        })

    # # # Step 3: Insert all at once
    with engine.begin() as connection:
        connection.execute(
            text("""
                INSERT INTO public."CollectionFile" (
                    "DisbursementID", "PrincipleCollected", "InterestCollected", "InstStartDate",
                    "CollectedAmount", "CollectionDate", "BMID", "POS", "ExtraColumn1", "ExtraColumn2",
                    "ExtraColumn3", "ExtraColumn4", "LoanType", "BankMstID", "BranchMstID",
                    "CreatedDate", "BranchName", "CustomerId", "TransactionNumber"
                ) VALUES (
                    :DisbursementID, :PrincipleCollected, :InterestCollected, :InstStartDate,
                    :CollectedAmount, :CollectionDate, :BMID, :POS, :ExtraColumn1, :ExtraColumn2,
                    :ExtraColumn3, :ExtraColumn4, :LoanType, :BankMstID, :BranchMstID,
                    :CreatedDate, :BranchName, :CustomerId, :TransactionNumber
                )
            """),
            insert_data
        )
    print(f"Inserted {len(insert_data)} new records into the CollectionFile table with BranchMstID pre-filled.")

def process_arthasidhi_recovery_data(df, bank_id):
    """Process Arthasidhi recovery data and generate summary"""
    try:
        print(f"Processing data for bank_id: {bank_id}")
        coll_df = process_arhta_sidhi_collection_data(df)
        existing_collections = check_existing_collections(coll_df, bank_id=bank_id)
        
        summary_data = []
        new_records = []  # This will store actual new records (inserts only)
        counters = {'update': 0, 'insert': 0, 'no_change': 0, 'error': 0}
        db_operation_success = False
        db_error_message = None
        
        print(f"Processing {len(coll_df)} collection records...")
        
        for index, row in coll_df.iterrows():
            try:
                disbursement_id = row['DisbursementID']
                customer_id = row['CustomerID']
                collection_date = row['CollectionDate']
                collection_date_str = collection_date.strftime('%Y-%m-%d') if not pd.isna(collection_date) else None
                new_amount = row['CollectedAmount']
                
                # Skip records with missing essential data
                if pd.isna(disbursement_id) or pd.isna(collection_date):
                    counters['error'] += 1
                    continue
                
                # Check if this collection already exists
                collection_key = (disbursement_id, collection_date_str)
                
                if collection_key in existing_collections:
                    current_amount = existing_collections[collection_key]
                    if current_amount != new_amount:
                        action = 'Update'
                        counters['update'] += 1
                    else:
                        action = 'No Change'
                        counters['no_change'] += 1
                    
                    summary_data.append({
                        'CustomerID': customer_id,
                        'DisbursementID': disbursement_id,
                        'CollectionDate': collection_date,
                        'Action': action,
                        'CurrentCollectedAmount': current_amount,
                        'NewCollectedAmount': new_amount
                    })
                else:
                    # This is a new record - add to new_records for insertion
                    action = 'Insert'
                    counters['insert'] += 1
                    new_records.append(row)  # Add the actual row data for new records
                    
                    summary_data.append({
                        'CustomerID': customer_id,
                        'DisbursementID': disbursement_id,
                        'CollectionDate': collection_date,
                        'Action': action,
                        'CurrentCollectedAmount': None,
                        'NewCollectedAmount': new_amount
                    })
            except Exception as e:
                print(f"Error processing row {index}: {e}")
                counters['error'] += 1
        
        summary_df = pd.DataFrame(summary_data)
        total_records = len(coll_df)
        
        print(f"Processing complete. Summary: {counters}")
        
        # Ask user confirmation before database insertion
        if new_records:
            print(f"\nFound {len(new_records)} new records to insert into database.")
            print("Summary of operations:")
            print(f"  - New records to insert: {counters['insert']}")
            print(f"  - Records to update: {counters['update']}")
            print(f"  - No change needed: {counters['no_change']}")
            print(f"  - Errors: {counters['error']}")
            
            user_choice = input("\nDo you want to proceed with database insertion? (y/n): ").strip().lower()
            
            if user_choice in ['y', 'yes']:
                try:
                    insert_collection_data(new_records, bank_id=bank_id)
                    db_operation_success = True
                    print(f"Successfully inserted {len(new_records)} new records")
                except Exception as e:
                    db_operation_success = False
                    db_error_message = str(e)
                    print(f"Database insertion failed for Loan Recovery data: {e}")
            else:
                print("Database insertion cancelled by user.")
                db_operation_success = False
                db_error_message = "User cancelled insertion"
        else:
            print("No new records to insert")
        
        return {
            'summary_df': summary_df,
            'new_records': new_records,
            'counters': counters,
            'total_records': total_records,
            'db_operation_success': db_operation_success,
            'db_error_message': db_error_message
        }
        
    except Exception as e:
        raise Exception(f"Error processing recovery data: {str(e)}")


def main():
    """Main function to run the process standalone"""
    try:
        # Get file path and bank ID from user
        file_path = input("Enter the path to the XLS file: ").strip()
        bank_id = input("Enter the bank ID (default: 13): ").strip()
        
        if not bank_id:
            bank_id = 13
        else:
            bank_id = int(bank_id)
        
        print(f"\nProcessing file: {file_path}")
        print(f"Bank ID: {bank_id}")
        print("-" * 50)
        
        # Process the file
        result = read_and_process_xls_file(file_path, bank_id)
        
        return result
        
    except FileNotFoundError:
        print(f"Error: File '{file_path}' not found.")
        return None
    except ValueError as e:
        print(f"Error: Invalid input - {e}")
        return None
    except Exception as e:
        print(f"Error: {e}")
        return None


if __name__ == "__main__":
    result = main()
    if result:
        print("\nProcess completed successfully!")
        print(f"Total records processed: {result['total_records']}")
        print(f"Summary: {result['counters']}")
    else:
        print("Process failed!")
@import url("../../../index.css");

$light-blue: #E3F5F6;
$sky-blue: #407BFF;

.detail_container{
    background-color: $light-blue;
    padding: 1rem;
    border-radius: 10px;
    box-shadow: 0px 4px 4px 0px #00000040;

    .name{
        margin: 0;
        color: var(--dark-blue);
        font-weight: 500;
        font-size: 1.6rem;
        font-family: 'Kanit','Courier New', Courier, monospace;
    }
    .amount_text{
        color: #0F2050B8;
        font-size: 1.1rem;
        font-weight: 500;
        font-family: 'Kanit','Courier New', Courier, monospace;
    }
    .amount{
        color:$sky-blue;
        font-weight: 600;
        margin: 0 1rem;
        font-size: 1.4rem;
    }

    .amount_details{
        width: 79%;
        .amount{
            margin: 0;
            font-family: 'Kanit','Courier New', Courier, monospace;
            font-weight: normal;
        }
    }

    .image_container{
        width: 40px;
        height:40px;
        img{
            width: 100%;
            object-fit: contain;
        }
    }
}

// Responsive view
@media screen and (max-width:1024px) {
    .detail_container{
        padding: 1rem 0.5rem;
        .name{
            font-size: 1.2rem;
        }
        .amount_text{
            font-size: 14px;
        }
        .amount{
            font-size: 16px;
        }
        .amount_details{
            width: 100%;
            .amount{
               font-size: 12px;
            }
        }
        .image_container{
            width: 20px;
            height: 20px;
        }
    }
}

@media screen and (max-width:568px) {
    .detail_container{
        padding: 1rem 0.5rem;
        .name{
            font-size: 14px;
        }
        .amount_text{
            font-size: 12px;
        }
        .amount{
            font-size: 14px;
        }
    }
}
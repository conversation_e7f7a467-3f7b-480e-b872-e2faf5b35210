import { Button, <PERSON>lex, Modal, Typography } from "antd";
import React from "react";
import Style from "./_style.module.scss";
import PropTypes from "prop-types";

const { Text } = Typography;

export function CampaignDeleteModal({
  desc,
  modalStatus,
  setModalStatus,
  isLoading,
  handleDeleteCampaign,
}) {
  return (
    <Modal
      centered
      className={Style.modal_container}
      title={null}
      open={modalStatus}
      onOk={() => {
        setModalStatus(false);
      }}
      onCancel={() => {
        setModalStatus(false);
      }}
      footer={null}
      width={350}
    >
      <Flex justify="center" align="center" vertical gap={15}>
        <div className={Style.img_div}>
          <span>&#10006;</span>
        </div>
        <Flex vertical gap={1}>
          <Text className={Style.queue_title}>Are you sure?</Text>
          <Text
            className={Style.queue_desc}
          >{`Do you really want to delete ${desc} This process cannot be undone?`}</Text>
        </Flex>
        <Flex gap={10} align="center" style={{ marginBlockStart: "0.5rem" }}>
          <Button
            className={Style.no_btn}
            onClick={() => setModalStatus(false)}
          >
            Cancel
          </Button>
          <Button
            className={Style.yes_btn}
            loading={isLoading}
            onClick={handleDeleteCampaign}
          >
            Delete
          </Button>
        </Flex>
      </Flex>
    </Modal>
  );
}

CampaignDeleteModal.propTypes = {
  desc: PropTypes.string,
  modalStatus: PropTypes.bool,
  setModalStatus: PropTypes.func,
  isLoading: PropTypes.bool,
  handleDeleteCampaign: PropTypes.func,
};

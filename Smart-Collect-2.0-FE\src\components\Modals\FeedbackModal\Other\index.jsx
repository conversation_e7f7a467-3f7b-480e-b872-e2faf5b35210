import React, { useState } from "react";
import Style from "./_other.module.scss";
import { Button, Flex, Input, InputNumber, message, Switch, Typography } from "antd";
import { AXIOS } from "../../../../apis/ho-Instance";
import PropTypes from "prop-types";

const { Text } = Typography;

export function Other({ loanmstid, branchMstId, handleCross }) {
  const [otherSwitch, setOtherSwitch] = useState({
    isReply: false,
    isWrongNumber: false,
  });

  const [loading, setLoading] = useState(false);
  const [wrongNumber, setWrongNumber] = useState(0);
  const [remark, setRemark] = useState();

  const handleSwitchChange = ({ key, value }) => {
    setOtherSwitch((prev) => ({ ...prev, [key]: value }));
  };

  const handleRemark = (value) => {
    setRemark(value);
  };
  
  const handlePostOtherData = async () => {
    const requestData = {
      BranchMstID: branchMstId,
      LoanMstID: loanmstid,
      type: "Other",
      customer_not_reply: otherSwitch.isReply,
      wrongnumber: otherSwitch.isWrongNumber,
      number:otherSwitch.isWrongNumber
        ? wrongNumber
        : otherSwitch.isWrongNumber,
      remark: remark,
    };
    setLoading(true);
    try {
      await AXIOS.post("feedback/", requestData);
      message.success("Promise created successfully!");

      // Close the modal after 2 seconds
      setTimeout(() => {
        handleCross();
        window.location.reload();
      }, 500);
    } catch (error) {
      console.log("Error in Ready to Pay:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={Style.other}>
      <Flex vertical gap={20} justify="center">
        {/* reply */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Customer Not Reply:</Text>
          <Switch
            value={otherSwitch.isReply}
            onChange={(value) => handleSwitchChange({ key: "isReply", value })}
          />
        </Flex>

        {/* wrong number */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Wrong Number:</Text>
          <Switch
            className={Style.switch}
            value={otherSwitch.isWrongNumber}
            onChange={(value) =>
              handleSwitchChange({ key: "isWrongNumber", value })
            }
          />
        </Flex>

        {/* wrong number */}
        {otherSwitch.isWrongNumber && (
          <Flex justify="space-between" align="center">
            <Text className={Style.text}>Alternate number:</Text>
            <InputNumber
              value={wrongNumber}
              onChange={setWrongNumber}
              placeholder="Optional"
              className={Style.custom_number}
            />
          </Flex>
        )}

        {/* /remark */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Remark:</Text>
          <Input
            placeholder="Enter"
            onChange={(e) => handleRemark(e.target.value)}
            className={Style.custom_input}
          />
        </Flex>

        <Flex justify="center">
          <Button
            loading={loading}
            className={Style.feedback_button}
            onClick={handlePostOtherData}
          >
            Submit
          </Button>
        </Flex>
      </Flex>
    </div>
  );
}

Other.propTypes={
  loanmstid: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  branchMstId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  handleCross: PropTypes.func
}
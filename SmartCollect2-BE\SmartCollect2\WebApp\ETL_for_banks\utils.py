import pandas as pd
import re
import numpy as np
from datetime import datetime
from WebApp.ETL_for_banks.required_columns import *
from WebApp.ETL_for_banks.custom_expection import *
import io
import xlwings as xw
from dateutil import parser
import warnings

# Optional utility functions for additional analysis
def analyze_mobile_patterns(df, mobile_col='MobileNo'):
    """Analyze mobile number patterns"""
    if mobile_col in df.columns:
        mobiles = df[mobile_col].astype(str)
        print("Mobile number analysis:")
        print(f"- Starting with 9: {mobiles.str.startswith('9').sum()}")
        print(f"- Starting with 8: {mobiles.str.startswith('8').sum()}")
        print(f"- Starting with 7: {mobiles.str.startswith('7').sum()}")
        print(f"- Starting with 6: {mobiles.str.startswith('6').sum()}")
        return mobiles.str[:1].value_counts()
    return None

def check_data_quality(df):
    """Check overall data quality"""
    print("Data Quality Report:")
    print(f"- Total rows: {len(df)}")
    print(f"- Duplicate rows: {df.duplicated().sum()}")
    print(f"- Completely empty rows: {df.isnull().all(axis=1).sum()}")
    
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    if len(numeric_cols) > 0:
        print(f"- Negative values in numeric columns:")
        for col in numeric_cols:
            neg_count = (df[col] < 0).sum()
            if neg_count > 0:
                print(f"  - {col}: {neg_count} negative values")

def create_branch_mapping(result_df):
    """
    Create a branch mapping dictionary from existing branch data
    
    Args:
        result_df: DataFrame containing branch information
        
    Returns:
        dict: Mapping of branch names to formatted branch codes
    """
    branch_mapping = {}
    unmapped_branches = set()
    
    # Extract branch information from existing 'Branch' column
    if 'Branch' in result_df.columns:
        unique_branches = result_df['Branch'].dropna().unique()
        
        for branch_info in unique_branches:
            branch_str = str(branch_info).strip()
            
            # Expected format: "001 - SOLAPUR" or "001-SOLAPUR"
            if ' - ' in branch_str:
                parts = branch_str.split(' - ', 1)
                if len(parts) == 2:
                    code = parts[0].strip()
                    name = parts[1].strip().upper()
                    branch_mapping[name] = branch_str
                    # Also map the code to the full string
                    branch_mapping[code] = branch_str
            elif '-' in branch_str and len(branch_str) > 3:
                parts = branch_str.split('-', 1)
                if len(parts) == 2:
                    code = parts[0].strip()
                    name = parts[1].strip().upper()
                    formatted = f"{code} - {name}"
                    branch_mapping[name] = formatted
                    branch_mapping[code] = formatted
    
    print(f"📋 Created branch mapping with {len(branch_mapping)} entries")
    return branch_mapping

def map_branch_demand(branch_demand_value, branch_mapping):
    """
    Map branch_demand value to proper format using branch mapping
    
    Args:
        branch_demand_value: Value from branch_demand column
        branch_mapping: Dictionary mapping branch names/codes to formatted strings
        
    Returns:
        str: Properly formatted branch string or original value if not found
    """
    if pd.isna(branch_demand_value) or branch_demand_value == '':
        return branch_demand_value
    
    branch_str = str(branch_demand_value).strip().upper()
    
    # Direct mapping check
    if branch_str in branch_mapping:
        return branch_mapping[branch_str]
    
    # Check if it's already in the correct format
    if ' - ' in branch_str and len(branch_str.split(' - ')) == 2:
        return branch_demand_value
    
    # Try partial matching for branch names
    for key, value in branch_mapping.items():
        if branch_str in key.upper() or key.upper() in branch_str:
            return value
    
    # If no mapping found, return original value
    return branch_demand_value

def clean_name(name):
    """Enhanced name cleaning function"""
    if pd.isna(name):
        return ''
    
    name = str(name).upper().strip()
    
    # Remove ID patterns (Aadhaar, PAN, etc.)
    name = re.sub(r'\s*[-_/]\s*(AADHAAR|UID|PAN|VOTER)\s*\w*', '', name, flags=re.IGNORECASE)
    
    # Standardize separators and clean special characters
    name = re.sub(r'\s*[-_/]\s*', ' ', name)  # Replace separators with single space
    name = re.sub(r'[^A-Z\s]', '', name)  # Remove non-alphabetic characters
    name = re.sub(r'\s+', ' ', name).strip()  # Collapse multiple spaces
    
    # Convert to Title Case (optional)
    name = name.title()
    
    return name

def clean_for_merge(series):
    """Clean series for merging"""
    return series.astype(str).str.strip().str.replace(r'[^a-zA-Z0-9]', '', regex=True)
# Function to clean column values
def clean_column(value):
    if pd.isna(value):
        return None
    return str(value).strip()

# Function to convert Excel serial date to Python date
# def convert_excel_serial_date(serial):
#     if pd.isna(serial):
#         return None
    
#     # Handle datetime objects directly
#     if isinstance(serial, (pd.Timestamp, datetime)):
#         return serial.date()
    
#     # Handle string datetime representations
#     if isinstance(serial, str):
#         # Try to parse as datetime first
#         try:
#             return pd.to_datetime(serial, errors='raise').date()
#         except:
#             pass
        
#         # List of common date formats to try
#         date_formats = ['%d/%m/%Y', '%d-%m-%Y', '%Y-%m-%d', '%m/%d/%Y']
        
#         for fmt in date_formats:
#             try:
#                 return pd.to_datetime(serial, format=fmt, errors='raise').date()
#             except:
#                 continue
    
#     # If all string formats fail, try as Excel serial date
#     try:
#         if isinstance(serial, str):
#             serial = float(serial.replace(',', ''))
#         return (datetime(1899, 12, 30) + pd.Timedelta(days=serial)).date()
#     except:
#         return None

def convert_excel_serial_date(serial):
    """
    Convert Excel date using the best automatic detection methods.
    Handles all date patterns automatically without manual pattern matching.
    
    This function combines multiple approaches for maximum compatibility:
    1. dateutil parser (most flexible)
    2. pandas smart detection
    3. Excel serial date handling
    
    Args:
        serial: Date value from Excel (can be string, number, datetime, etc.)
    
    Returns:
        datetime.date object or None if conversion fails
    """
    if pd.isna(serial):
        return None
    
    # Handle datetime objects directly
    if isinstance(serial, (pd.Timestamp, datetime)):
        return serial.date()
    
    # Handle string datetime representations
    if isinstance(serial, str):
        serial = serial.strip()  # Remove any whitespace
        
        # Method 1: dateutil parser (best for handling various formats automatically)
        try:
            # dateutil is excellent at detecting formats like:
            # "05-07-2024", "July 5, 2024", "5/7/24", "2024-05-07", etc.
            return parser.parse(serial, fuzzy=False).date()
        except:
            pass
        
        # Method 2: pandas with smart detection
        try:
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                return pd.to_datetime(serial, infer_datetime_format=True, errors='raise').date()
        except:
            pass
        
        # Method 3: pandas default behavior (also quite intelligent)
        try:
            return pd.to_datetime(serial, errors='raise').date()
        except:
            pass
    
    # Handle numeric values (Excel serial dates)
    try:
        # Convert string to float if needed
        if isinstance(serial, str):
            # Remove commas and convert to float
            serial = float(serial.replace(',', ''))
        
        # Handle Excel serial date (days since 1899-12-30)
        if isinstance(serial, (int, float)):
            return (datetime(1899, 12, 30) + pd.Timedelta(days=serial)).date()
    except:
        pass
    
    # If all methods fail, return None
    return None
# Function to process branch information
def process_branch(branch_text):
    if pd.isna(branch_text):
        return None, None
    
    parts = str(branch_text).strip().split('-', 1)
    if len(parts) == 2:
        return parts[0].strip(), parts[1].strip()
    return branch_text, branch_text

# Function to clean the DisbursementID column (only removing dashes)
def clean_disbursement_id(value):
    if pd.isna(value):
        return value
    value = str(value)
    # Remove only - characters
    value = value.replace("-", "")
    return value.strip()

def clean_mobile_number(mobile):
    # Check if value is missing
    if pd.isna(mobile) or str(mobile).strip() in ['', 'nan', 'None']:
        return None
    
    # Convert to string and strip whitespace
    # If it's a float (e.g., 9011100040.0), convert to int first to remove decimal
    if isinstance(mobile, float):
        mobile = str(int(mobile))
    else:
        mobile = str(mobile).strip()
    
    # Remove spaces and other non-digit characters
    mobile = ''.join(filter(str.isdigit, mobile))
    
    # Handle cases where number starts with 91 (country code) and is 12 digits
    if len(mobile) == 12 and mobile.startswith('91'):
        mobile = mobile[2:]  # Remove country code
    
    # Handle cases where the number is valid (usually 10 digits in India)
    if len(mobile) >= 10:
        return mobile[-10:]  # Return the last 10 digits
    
    # If the number has at least some digits but fewer than 10, return it as is
    if len(mobile) > 0:
        return mobile
    
    return None  # For completely invalid numbers


def sanitize_filename(filename):
    """
    Sanitize filename by removing or replacing invalid characters
    
    Args:
        filename (str): Original filename
        
    Returns:
        str: Sanitized filename safe for Windows/Unix systems
    """
    # Characters that are invalid in Windows filenames
    invalid_chars = r'[<>:"/\\|?*]'
    
    # Replace invalid characters with underscores
    sanitized = re.sub(invalid_chars, '_', filename)
    
    # Remove any trailing dots or spaces (invalid in Windows)
    sanitized = sanitized.rstrip('. ')
    
    # Ensure filename is not empty after sanitization
    if not sanitized:
        sanitized = "file"
    
    return sanitized

def extract_datetime_from_filename(filename):
    """
    Extract datetime from filename pattern like '0012_DemandData_25_05_2025_14_30_00'
    
    Args:
        filename (str): The filename to parse
        
    Returns:
        datetime or None: Parsed datetime object or None if not found
    """
    # Common datetime patterns in filenames
    patterns = [
        # Your specific format: DD_MM_YYYY_HH_MM_SS (most likely pattern)
        r'(\d{2}_\d{2}_\d{4}_\d{2}_\d{2}_\d{2})',  # 25_05_2025_14_30_00
        # Alternative formats with underscores
        r'(\d{4}_\d{2}_\d{2}_\d{2}_\d{2}_\d{2})',  # YYYY_MM_DD_HH_MM_SS
        # Formats with hyphens and underscores
        r'(\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2})',  # YYYY-MM-DD_HH-MM-SS
        r'(\d{2}-\d{2}-\d{4}_\d{2}-\d{2}-\d{2})',  # DD-MM-YYYY_HH-MM-SS
        # Compact formats
        r'(\d{4}\d{2}\d{2}_\d{2}\d{2}\d{2})',      # YYYYMMDD_HHMMSS
        r'(\d{2}\d{2}\d{4}_\d{2}\d{2}\d{2})',      # DDMMYYYY_HHMMSS
        # Formats with spaces
        r'(\d{4}-\d{2}-\d{2} \d{2}-\d{2}-\d{2})',  # YYYY-MM-DD HH-MM-SS
        r'(\d{2} \d{2} \d{4} \d{2} \d{2} \d{2})',  # DD MM YYYY HH MM SS
    ]
    
    datetime_formats = [
        '%d_%m_%Y_%H_%M_%S',  # 25_05_2025_14_30_00
        '%Y_%m_%d_%H_%M_%S',  # 2025_05_25_14_30_00
        '%Y-%m-%d_%H-%M-%S',  # 2025-05-25_14-30-00
        '%d-%m-%Y_%H-%M-%S',  # 25-05-2025_14-30-00
        '%Y%m%d_%H%M%S',      # 20250525_143000
        '%d%m%Y_%H%M%S',      # 25052025_143000
        '%Y-%m-%d %H-%M-%S',  # 2025-05-25 14-30-00
        '%d %m %Y %H %M %S',  # 25 05 2025 14 30 00
    ]
    
    for i, pattern in enumerate(patterns):
        match = re.search(pattern, filename)
        if match:
            datetime_str = match.group(1)
            try:
                parsed_dt = datetime.strptime(datetime_str, datetime_formats[i])
                return parsed_dt
            except ValueError as e:
                print(f"Failed to parse with format {datetime_formats[i]}: {e}")
                continue
    
    print("No datetime pattern matched")
    return None


def determine_file_type_from_filename(filename):
    """
    Determine file type based on filename keywords
    
    Args:
        filename (str): The filename to analyze
        
    Returns:
        str: Detected file type or 'unknown'
    """
    filename_lower = filename.lower()
    
    # Define keyword mappings for file type detection
    file_type_keywords = {
        'loan recovery': ['recovery', 'collection'],
        'over due': ['overdue', 'od', 'over due'],
        'demand': ['demand'],
        '2pm demand': ['2pm', '2bj'],
        'basedata': ['baseddata', 'basedata', 'based_data'],
        'loan': ['loan'],
        'customer':['customer details'],
        'guarantor':['guarantor'],


    }
    
    # Check for 2pm demand first (more specific)
    if any(keyword in filename_lower for keyword in file_type_keywords['2pm demand']):
        if any(keyword in filename_lower for keyword in file_type_keywords['demand']):
            return '2pm demand'
    
    # Check other file types
    for file_type, keywords in file_type_keywords.items():
        if file_type == '2pm demand':  # Already checked above
            continue
        if any(keyword in filename_lower for keyword in keywords):
            return file_type
    
    return 'unknown'

def get_required_columns(bank_id, file_type):
    """
    Get required columns based on bank ID and file type
    
    Args:
        bank_id (int): Bank master ID
        file_type (str): Type of file (recovery, od, over due, demand, loan, etc.)
        
    Returns:
        list: List of required column names
        
    Raises:
        UnsupportedFileTypeError: If bank_id and file_type combination is not supported
    """
    file_type = file_type.lower()
    
    try:
        # Arthasidhi Bank (ID: 9 or 13)
        if bank_id == 13:
            if file_type == 'loan recovery':
                return get_arthasidhi_recovery_columns()
            elif file_type in ['od', 'over due', "overdue"]:
                return get_arthasidhi_od_columns()
            elif file_type == 'demand' or file_type == '2pm demand':
                return get_arthasidhi_demand_columns()
        
        # Pavana Bank (ID: 21)
        elif bank_id == 21:
            if file_type in ['over due', 'overdue']:
                return get_pavan_over_due_columns()
        
        # Vinayana Bank (ID: 23)
        elif bank_id == 23:
            if file_type in ['baseddata', 'basedata']:
                return get_vinayana_based_data_columns()
        
        # Warna Bank
        elif bank_id == 36:
            if file_type == 'loan':
                return get_warna_loan_data_columns()
            elif file_type == 'customer details':
                return get_warna_customer_columns()
            elif file_type == 'guarantor':
                return get_warna_gurantor_columns()
            elif file_type == 'negative':
                return get_warna_negative_columns()
       
        elif bank_id == 27:
            if file_type == 'loan':
                return get_ghoti_loan_data_columns()
            elif file_type == 'over due':
                return get_ghoti_od_data_columns()
        
        elif bank_id == 28:
            return get_shakti_finance_data_columns()
        
        elif bank_id == 29:
            return get_rajapur_collection_data_columns()
        
        elif bank_id == 30:
            return get_sindhudhurg_data_columns()
        
        elif bank_id == 32:
            if file_type == 'over due':
                return get_bss_bihar_data_columns()
            if file_type == 'collection':
                return get_bss_bihar_collection_data_columns()
        elif bank_id == 35:
            return get_shareindia_data_columns()
        elif bank_id == 37:
            return get_shrimantmalojiraje_data_columns()
        elif bank_id == 369:
            if file_type == 'base':
                return get_swami_smarth_base_data_columns()
            elif file_type == 'daily':
                return get_swami_smarth_daily_data_columns()
        elif bank_id == 378:
                return get_tulja_data_columns()        
        # If no matching configuration is found
        available_types = get_available_file_types(bank_id)
        if available_types:
            if isinstance(available_types, dict):
                types_list = available_types.get('file_types', available_types.get('file_type', []))
            else:
                types_list = available_types
        else:
            types_list = []
            
        raise UnsupportedFileTypeError(file_type, bank_id, types_list)
    
    except UnsupportedFileTypeError:
        raise
    except Exception as e:
        raise UnsupportedFileTypeError(file_type, bank_id, [])

def preprocess_dataframe(df, bank_id, file_type, filename=None):
    """
    Preprocess dataframe based on bank and file type requirements
    
    Args:
        df (pandas.DataFrame): Raw dataframe
        bank_id (int): Bank master ID
        file_type (str): Type of file
        filename (str): Original filename for time-based logic
        
    Returns:
        pandas.DataFrame: Preprocessed dataframe
        
    Raises:
        EmptyFileError: If dataframe is empty
        ColumnMissingError: If required columns not found
        DataProcessingError: If preprocessing fails
    """
    if df is None:
        raise EmptyFileError(filename or "Unknown file")
    
    if bank_id == 27 and file_type == 'loan':
        # df should be a dictionary with 'loan_types' and 'branch_names'
        if isinstance(df, dict):
            # Validate the dictionary structure
            if 'loan_types' not in df or 'branch_names' not in df:
                raise DataProcessingError("Bank 27 loan file must contain 'loan_types' and 'branch_names' data")
            
            # Validate that the dataframes are not empty
            if df['loan_types'].empty or df['branch_names'].empty:
                raise EmptyFileError(filename or "Bank 27 loan file")
            
            return df  # Already in the correct format
        else:
            # If somehow df is a regular dataframe, raise exception instead of warning
            raise DataProcessingError("Expected dictionary for bank 27 loan file, got dataframe")
    
    # Check if dataframe is empty
    if df.empty:
        raise EmptyFileError(filename or "Unknown file")
    
    file_type = file_type.lower()
    
    # Get required columns for validation
    try:
        required_columns = get_required_columns(bank_id, file_type)
    except UnsupportedFileTypeError:
        # FIXED: Pass bank_id as a parameter instead of in the message
        raise UnsupportedFileTypeError(bank_id, file_type, f"No column validation available for bank {bank_id}, file type '{file_type}'")
    except Exception as e:
        raise ColumnMissingError(f"Error getting required columns: {str(e)}")
    
    # Function to find header row by looking for required columns
    def find_header_row(df, required_columns, max_rows=15):
        """
        Search for the header row within the first max_rows (0 to max_rows-1)
        Returns the row index if found, None otherwise
        Special return value -1 means headers are already properly set
        """
        print(df.head(5), "DataFrame preview")
        
        if df.empty or len(required_columns) == 0:
            return None
        
        # Clean required columns by removing extra whitespace and convert to lowercase
        cleaned_required_columns = [col.strip().lower() for col in required_columns if col and col.strip()]
        
        if not cleaned_required_columns:
            return None
        
        print(f"Searching for header row with required columns: {cleaned_required_columns}")
        
        # First check if DataFrame already has proper column headers
        if hasattr(df, 'columns') and len(df.columns) > 0:
            # Check if current column headers match required columns
            current_headers = [str(col).strip().lower() for col in df.columns if str(col).strip()]
            current_headers_set = set(current_headers)
            
            print(f"Current DataFrame headers: {current_headers}")
            
            # Check exact matches for required columns
            matches = 0
            matched_columns = []
            for req_col in cleaned_required_columns:
                if req_col in current_headers_set:
                    matches += 1
                    matched_columns.append(req_col)
            
            print(f"DataFrame headers: Found {matches}/{len(cleaned_required_columns)} matches: {matched_columns}")
            
            # Calculate minimum matches needed (at least 70% or minimum 2)
            min_matches = max(2, int(len(cleaned_required_columns) * 0.7))
            
            # If we find sufficient required columns in the headers
            if matches >= min_matches:
                print(f"Headers already present in DataFrame with {matches} matches")
                return -1  # Special value indicating headers are already set
        
        # If headers don't match, search through data rows for potential header row
        for row_idx in range(min(max_rows, len(df))):
            try:
                # Get the row as potential headers
                potential_headers = df.iloc[row_idx]
                
                # Convert to string and clean headers
                header_list = []
                for header in potential_headers:
                    if pd.notna(header) and str(header).strip():
                        header_clean = str(header).strip().lower()
                        if header_clean != 'nan' and header_clean:
                            header_list.append(header_clean)
                
                # Create a set of cleaned headers for faster lookup
                header_set = set(header_list)
                
                print(f"Row {row_idx} headers: {header_list}")
                
                # Check exact matches for required columns
                matches = 0
                matched_columns = []
                for req_col in cleaned_required_columns:
                    if req_col in header_set:
                        matches += 1
                        matched_columns.append(req_col)
                
                print(f"Row {row_idx}: Found {matches}/{len(cleaned_required_columns)} matches: {matched_columns}")
                
                # Calculate minimum matches needed (at least 70% or minimum 2)
                min_matches = max(2, int(len(cleaned_required_columns) * 0.7))
                
                # If we find sufficient required columns
                if matches >= min_matches:
                    print(f"Header row found at index {row_idx} with {matches} matches")
                    return row_idx
                    
            except Exception as e:
                print(f"Error processing row {row_idx}: {str(e)}")
                continue
        
        print(f"No header row found in first {max_rows} rows")
        return None
    
    # Find header row
    header_row_idx = find_header_row(df, required_columns)
    
    if header_row_idx is None:
        # No header row found, check if we can proceed with existing structure
        print("No suitable header row found, checking existing columns...")
        
        # Get current column names
        current_columns = [str(col).strip().lower() for col in df.columns]
        required_columns_lower = [col.strip().lower() for col in required_columns]
        
        # Check how many required columns exist
        matches = sum(1 for req_col in required_columns_lower if req_col in current_columns)
        min_matches = max(2, int(len(required_columns_lower) * 0.5))  # Lower threshold for existing columns
        
        if matches >= min_matches:
            print(f"Using existing column structure with {matches} matches")
            # Continue with existing structure
        else:
            # Create a more detailed error message
            missing_columns = [col for col in required_columns if col.strip().lower() not in current_columns]
            available_columns = list(df.columns)
            
            raise ColumnMissingError(
                f"Missing required columns: {', '.join(missing_columns)}. "
                f"Found columns: {', '.join(available_columns)}"
            )
    
    elif header_row_idx == -1:
        # Headers already properly set, no need to modify
        print("Using existing DataFrame headers")
        
        # Still validate that we have sufficient columns
        current_columns = [str(col).strip().lower() for col in df.columns]
        required_columns_lower = [col.strip().lower() for col in required_columns]
        
        # Check how many required columns exist
        matches = sum(1 for req_col in required_columns_lower if req_col in current_columns)
        min_matches = max(2, int(len(required_columns_lower) * 0.5))  # Lower threshold for validation
        
        if matches < min_matches:
            # Create a more detailed error message
            missing_columns = [col for col in required_columns if col.strip().lower() not in current_columns]
            available_columns = list(df.columns)
            
            print(f"Warning: Only {matches}/{len(required_columns_lower)} required columns found")
            print(f"Missing: {missing_columns}")
            print(f"Available: {available_columns}")
            
            # For now, continue with a warning instead of failing
            # You can change this to raise an error if needed
            print("Proceeding with available columns...")
    
    else:
        # Header row found in data, need to restructure DataFrame
        print(f"Restructuring DataFrame using header row at index {header_row_idx}")
        
        try:
            # Extract headers from the found row
            new_headers = df.iloc[header_row_idx].tolist()
            
            # Clean headers
            cleaned_headers = []
            for header in new_headers:
                if pd.notna(header) and str(header).strip():
                    cleaned_headers.append(str(header).strip())
                else:
                    cleaned_headers.append(f"col_{len(cleaned_headers)}")
            
            # Create new dataframe with proper headers
            df_data = df.iloc[header_row_idx + 1:].reset_index(drop=True)
            df_data.columns = cleaned_headers
            
            # Update df reference
            df = df_data
            
            print(f"DataFrame restructured with headers: {cleaned_headers}")
            
        except Exception as e:
            raise DataProcessingError(f"Failed to restructure DataFrame: {str(e)}")
    
    # Final validation and cleanup
    try:
        # Remove completely empty rows
        df = df.dropna(how='all')
        
        # Reset index
        df = df.reset_index(drop=True)
        
        print(f"Final DataFrame shape: {df.shape}")
        print(f"Final columns: {list(df.columns)}")
        
        return df
        
    except Exception as e:
        raise DataProcessingError(f"Error in final DataFrame processing: {str(e)}")

def validate_columns(df, required_columns):
    """
    Validate that all required columns are present and not empty
    
    Args:
        df (pandas.DataFrame): The dataframe to validate
        required_columns (list): List of required column names
        
    Returns:
        tuple: (is_valid, error_response_data)
        
    Raises:
        ColumnMissingError: If required columns are missing
        EmptyFileError: If dataframe is empty
    """
    if df is None or df.empty:
        raise EmptyFileError("Unknown file", error_code="EMPTY_DATAFRAME_VALIDATION")
    
    if not required_columns:
        # If no required columns specified, consider it valid
        return True, None
    
    # Get current columns and clean them thoroughly
    current_columns = []
    for col in df.columns:
        # Clean column names by removing extra whitespace and handling NaN/None values
        cleaned_col = str(col).strip()
        if cleaned_col and not cleaned_col.startswith('Unnamed:'):
            current_columns.append(cleaned_col)
    
    required_columns_clean = [str(col).strip() for col in required_columns]
    
    # Create a set of current columns for faster lookup (case-insensitive)
    current_columns_set = set(col.lower().strip() for col in current_columns)
    
    # Check if all required columns are present
    missing_columns = []
    for req_col in required_columns_clean:
        req_col_normalized = req_col.lower().strip()
        
        # Check if required column exists in current columns
        if req_col_normalized not in current_columns_set:
            # Try flexible matching for common variations
            found_match = False
            for curr_col in current_columns:
                curr_col_normalized = curr_col.lower().strip()
                # Handle cases with extra spaces
                if req_col_normalized.replace(' ', '') == curr_col_normalized.replace(' ', ''):
                    found_match = True
                    break
            
            if not found_match:
                missing_columns.append(req_col)
    
    if missing_columns:
        raise ColumnMissingError(missing_columns, current_columns)
    
    return True, None

def get_available_file_types(bank_id):
    """
    Get available file types based on bank ID
    
    Args:
        bank_id (int): Bank master ID
        
    Returns:
        list: List of available file types for the bank
    """
    # Arthasidhi Bank (ID: 9 or 13)
    if bank_id == 13:
        return {"file_type": ["combined", "2pm demand"]}
    
    # Pavana Bank (ID: 21)
    elif bank_id == 21:
        return {
            "file_type": ["over due"]
        }
    elif bank_id == 23:
        return {
            "file_type": ["basedata"]
        }
    elif bank_id == 28:
        return {
            "file_type":['over due']
        }
    elif bank_id in (27,30,36,32):
        return {
            "file_type":['combined']
        }        
    elif bank_id in (29,34,35,37,378):
        return {
            "file_type":['Default']
        }
    elif bank_id == 369:
        return {
            "file_type":['base','daily']
        }
    # Return empty list for unsupported banks
    return []



def read_csv_file_safely(file):
    """
    Safely read CSV file with multiple encoding attempts and format detection
    Now also handles HTML files disguised as XLS files
    
    Args:
        file: Django uploaded file object
        
    Returns:
        pandas.DataFrame: The loaded dataframe
        
    Raises:
        FileFormatError: If file format is not supported
        EmptyFileError: If file is empty
        DataProcessingError: If file cannot be read
    """
    import pandas as pd
    import io
    
    if not hasattr(file, 'read') or not hasattr(file, 'seek'):
        raise FileFormatError(getattr(file, 'name', 'Unknown'), ['Valid file object'])
    
    # Reset file pointer to beginning
    file.seek(0)
    
    try:
        # First, read the file content into memory
        file_content = file.read()
        
        if len(file_content) == 0:
            raise EmptyFileError(getattr(file, 'name', 'Unknown file'))
        
        # Check if file starts with HTML content (common for fake XLS files)
        content_preview = file_content[:100].decode('utf-8', errors='ignore').lower()
        
        if '<table' in content_preview or '<html' in content_preview or '<!doctype' in content_preview:
            print("Detected HTML content in file, treating as HTML table...")
            try:
                # Try to read as HTML table
                content_str = file_content.decode('utf-8')
                dfs = pd.read_html(io.StringIO(content_str))
                if dfs and len(dfs) > 0 and not dfs[0].empty:
                    df = dfs[0]  # Take the first table
                    print(f"Successfully read HTML table. Shape: {df.shape}")
                    return df
                else:
                    raise EmptyFileError(getattr(file, 'name', 'Unknown file'), 
                                       error_code="EMPTY_HTML_TABLE")
            except Exception as html_error:
                print(f"HTML parsing failed: {html_error}")
                # Fall through to CSV reading attempts
        
        # List of encodings to try
        encodings_to_try = ['utf-8', 'latin1', 'ISO-8859-1', 'cp1252']
        
        for encoding in encodings_to_try:
            try:
                content_str = file_content.decode(encoding)
                df = pd.read_csv(io.StringIO(content_str))
                
                if df.empty:
                    continue  # Try next encoding
                    
                print(f"Successfully read CSV with {encoding} encoding. Shape: {df.shape}")
                return df
                
            except UnicodeDecodeError:
                continue  # Try next encoding
            except Exception as e:
                print(f"{encoding} encoding failed with error: {e}")
                continue
        
        # If all encodings failed, raise error
        raise DataProcessingError(f"Failed to read CSV file with any supported encoding: {encodings_to_try}")
                        
    except EmptyFileError:
        raise  # Re-raise empty file errors
    except Exception as e:
        print(f"Error reading file content: {e}")
        # Fallback: try direct pandas reading
        file.seek(0)
        
        try:
            df = pd.read_csv(file)
            if df.empty:
                raise EmptyFileError(getattr(file, 'name', 'Unknown file'))
            print(f"Direct pandas read successful. Shape: {df.shape}")
            return df
        except Exception as fallback_error:
            # Last resort: try reading as HTML directly from file object
            try:
                file.seek(0)
                dfs = pd.read_html(file)
                if dfs and len(dfs) > 0 and not dfs[0].empty:
                    df = dfs[0]
                    print(f"Fallback HTML read successful. Shape: {df.shape}")
                    return df
                else:
                    raise EmptyFileError(getattr(file, 'name', 'Unknown file'))
            except Exception as html_fallback_error:
                print(f"HTML fallback also failed: {html_fallback_error}")
            
            raise DataProcessingError(f"All file reading methods failed. File may be corrupted or in unsupported format. Last error: {fallback_error}")
        

def read_single_file(file):
    """
    Read a single file based on its extension
    
    Args:
        file: Django uploaded file object
        
    Returns:
        pandas.DataFrame or dict: The loaded dataframe or dictionary with multiple dataframes for bank_id=27 loan files
        
    Raises:
        FileFormatError: If file format is not supported
        EmptyFileError: If file is empty
        DataProcessingError: If file cannot be read
    """
    if not hasattr(file, 'name'):
        raise FileFormatError('Unknown file', ['Valid file with name attribute'])
    
    filename = file.name.lower()
    original_filename = file.name
    
    # Validate file extension
    supported_extensions = ('.csv', '.xlsx', '.xls')
    if not filename.endswith(supported_extensions):
        raise FileFormatError(original_filename, [ext[1:].upper() for ext in supported_extensions])
    
    try:
        if filename.endswith('.csv'):
            df = read_csv_file_safely(file)
            print(f"CSV loaded successfully. Shape: {df.shape}")
            
        elif filename.endswith('.xlsx'):
            # Use openpyxl engine for .xlsx files
            try:
                df = pd.read_excel(file, engine='openpyxl', dtype=str)
                if df.empty:
                    raise EmptyFileError(original_filename)
                print(f"XLSX loaded successfully. Shape: {df.shape}")
            except Exception as e:
                if 'xlsxreader' in str(e).lower() or 'openpyxl' in str(e).lower():
                    raise FileFormatError(original_filename, ['Valid XLSX file'])
                raise DataProcessingError(f"Error reading XLSX file: {str(e)}")
            
        elif filename.endswith('.xls'):
            # Handle XLS files using xlwings
            print("DEBUG: Processing XLS file")
            
            try:
                # Save the uploaded file temporarily
                import tempfile
                import os
                
                with tempfile.NamedTemporaryFile(delete=False, suffix='.xls') as temp_file:
                    file.seek(0)
                    temp_file.write(file.read())
                    temp_file_path = temp_file.name
                
                # Use xlwings to read the file
                app = xw.App(visible=False)
                try:
                    wb = app.books.open(temp_file_path)
                    sheet = wb.sheets[0]  # Get first sheet
                    
                    # Get all data from the used range
                    data = sheet.used_range.value
                    
                    if data is None or len(data) == 0:
                        raise EmptyFileError(original_filename)
                    
                    # Convert to DataFrame
                    if isinstance(data[0], list):
                        # Multiple rows - first row as headers
                        df = pd.DataFrame(data[1:], columns=data[0])
                    else:
                        # Single row
                        df = pd.DataFrame([data])
                    
                    print(f"Successfully read XLS with xlwings. Shape: {df.shape}")
                    
                finally:
                    wb.close()
                    app.quit()
                    # Clean up temporary file
                    os.unlink(temp_file_path)
                    
            except Exception as e:
                if 'xlwings' in str(e).lower():
                    raise DataProcessingError(f"xlwings error reading XLS file: {str(e)}")
                else:
                    raise DataProcessingError(f"Error reading XLS file: {str(e)}")

        else:
            # Generic excel reading for other formats
            try:
                df = pd.read_excel(file, dtype=str)
                if df.empty:
                    raise EmptyFileError(original_filename)
                print(f"Excel file loaded successfully. Shape: {df.shape}")
            except Exception as e:
                raise DataProcessingError(f"Error reading Excel file: {str(e)}")
        
        # Clean column names by removing leading/trailing whitespace
        if hasattr(df, 'columns'):
            df.columns = df.columns.str.strip()
        
        return df
        
    except (FileFormatError, EmptyFileError, DataProcessingError):
        raise  # Re-raise these specific exceptions
    except ImportError as ie:
        if 'xlrd' in str(ie):
            raise DataProcessingError("Missing required dependency for Excel file reading. Please contact system administrator.")
        else:
            raise DataProcessingError(f"Missing required dependency for file reading: {str(ie)}")
    except Exception as e:
        print(f"Error reading file {filename}: {str(e)}")
        raise DataProcessingError(f"Unexpected error reading file. Please ensure it's a valid Excel/CSV file: {str(e)}")



def read_bank_27_loan_file(file):
    """
    Special method to read bank 27 loan files with specific structure
    
    Args:
        file: Django uploaded file object
        
    Returns:
        dict: Dictionary containing loan_types and branch_names dataframes
        
    Raises:
        DataProcessingError: If file structure is invalid
        EmptyFileError: If file is empty or tables are empty
    """
    try:
        # Read the entire file first to understand structure
        df_full = pd.read_excel(file, header=None)
        
        if df_full.empty:
            raise EmptyFileError(getattr(file, 'name', 'Bank 27 loan file'))
        
        print(f"Full file shape: {df_full.shape}")
        print(f"First few rows:\n{df_full.head(25)}")
        
        # Reset file pointer
        file.seek(0)
        
        # Find where loan types table starts (look for "LOAN CODE" header)
        loan_header_row = None
        for i in range(min(10, len(df_full))):
            if any('LOAN CODE' in str(cell).upper() for cell in df_full.iloc[i] if pd.notna(cell)):
                loan_header_row = i
                break
        
        if loan_header_row is None:
            loan_header_row = 1  # Default to row 1
            print("Warning: LOAN CODE header not found, using default row 1")
        
        # Read loan types table
        try:
            loan_types = pd.read_excel(file, header=loan_header_row, nrows=11, usecols="A:B")
            loan_types.columns = ['LOAN CODE', 'LOAN TYPE NAME']
            loan_types = loan_types.dropna(subset=['LOAN CODE'])  # Remove rows where LOAN CODE is empty
            
            if loan_types.empty:
                raise EmptyFileError(getattr(file, 'name', 'Bank 27 loan file'), 
                                   error_code="EMPTY_LOAN_TYPES_TABLE")
        except Exception as e:
            raise DataProcessingError(f"Error reading loan types table: {str(e)}")
        
        # Reset file pointer
        file.seek(0)
        
        # Find where branch names table starts (look for "BR CODE" header)
        branch_header_row = None
        for i in range(loan_header_row + 12, min(loan_header_row + 25, len(df_full))):
            if any('BR CODE' in str(cell).upper() for cell in df_full.iloc[i] if pd.notna(cell)):
                branch_header_row = i
                break
        
        if branch_header_row is None:
            branch_header_row = 19  # Default fallback
            print("Warning: BR CODE header not found, using default row 19")
        
        # Read branch names table
        try:
            branch_names = pd.read_excel(file, header=branch_header_row, nrows=10, usecols="A:B")
            branch_names.columns = ['BR CODE', 'BR NAME']
            branch_names = branch_names.dropna(subset=['BR CODE'])  # Remove rows where BR CODE is empty
            
            if branch_names.empty:
                raise EmptyFileError(getattr(file, 'name', 'Bank 27 loan file'), 
                                   error_code="EMPTY_BRANCH_NAMES_TABLE")
        except Exception as e:
            raise DataProcessingError(f"Error reading branch names table: {str(e)}")
        
        print(f"Loan types extracted: {loan_types.shape}")
        print(f"Branch names extracted: {branch_names.shape}")
        
        return {
            'loan_types': loan_types,
            'branch_names': branch_names
        }
        
    except (DataProcessingError, EmptyFileError):
        raise  # Re-raise these specific exceptions
    except Exception as e:
        print(f"Error reading bank 27 loan file: {str(e)}")
        raise DataProcessingError(f"Unexpected error reading bank 27 loan file structure: {str(e)}")


def process_single_file(file, bank_id, file_type):
    """
    Process a single file: read, preprocess, and validate
    
    Args:
        file: Django uploaded file object
        bank_id (int): Bank master ID
        file_type (str): Type of file (provided by user)
        
    Returns:
        dict: Processing result with dataframe and metadata
    """
    original_filename = file.name
    sanitized_filename = sanitize_filename(original_filename)
    
    print(f"Processing file: {original_filename} as {file_type}")

    # Step 1: Read the file
    try:
        # Check file format
        if not original_filename.lower().endswith(('.xls', '.xlsx', '.csv')):
            raise FileFormatError(original_filename, ['XLS', 'XLSX', 'CSV'])
        
        # Special handling for bank_id=27 and file_type='loan'
        if bank_id == 27 and file_type.lower() == 'loan':
            df = read_bank_27_loan_file(file)
            print(f"Bank 27 loan file processed successfully. Loan types: {df['loan_types'].shape}, Branch names: {df['branch_names'].shape}")
        else:
            df = read_single_file(file)
            if df.empty:
                raise EmptyFileError(original_filename)
                
    except (FileFormatError, EmptyFileError, DataProcessingError) as e:
        return {
            'success': False,
            'error': str(e),
            'error_code': e.error_code,
            'status_code': e.status_code,
            'filename': original_filename
        }
    except Exception as e:
        return {
            'success': False,
            'error': f"Unexpected error reading file: {str(e)}",
            'error_code': "UNEXPECTED_FILE_ERROR",
            'status_code': 500,
            'filename': original_filename
        }
    
    # Step 2: Preprocess dataframe (skip for bank_id=27 and file_type='loan')
    if not (bank_id == 27 and file_type.lower() == 'loan'):
        try:
            print("going inside process dataframe with bankid ", bank_id, "and filename", file_type)
            
            df = preprocess_dataframe(df, bank_id, file_type, sanitized_filename)
            print(f"DataFrame preprocessed successfully for {original_filename}")
        except (ColumnMissingError, EmptyFileError, UnsupportedFileTypeError, DataProcessingError) as e:
            return {
                'success': False,
                'error': str(e),
                'error_code': e.error_code,
                'status_code': e.status_code,
                'filename': original_filename
            }
        except Exception as e:
            print(f"Error preprocessing dataframe for {original_filename}: {str(e)}")
            # Raise the error instead of continuing
            raise e
    
    # Step 3: Return success response - THIS WAS MISSING!
    return {
        'success': True,
        'dataframe': df,
        'filename': original_filename,
        'sanitized_filename': sanitized_filename,
        'bank_id': bank_id,
        'file_type': file_type,
        'message': f"File {original_filename} processed successfully"
    }
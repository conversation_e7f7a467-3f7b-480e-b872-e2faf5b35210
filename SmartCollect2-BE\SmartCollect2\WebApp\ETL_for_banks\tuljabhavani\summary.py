import pandas as pd
from datetime import datetime, timedelta
from dateutil import parser
import numpy as np

# Example user utilities (define or import these in your project)
from ..utils import clean_mobile_number
from ..queries import insert_data_in_raw_table
from ..sp import execute_post_processing_procedures

def run_post_processing_in_background(bank_id):
    try:
        print(f"Starting background post-processing for bank_id: {bank_id}")
        execute_post_processing_procedures(bank_id)
        print(f"Background post-processing initiated for bank_id: {bank_id}")
    except Exception as e:
        print(f"Error starting background post-processing for bank_id {bank_id}: {str(e)}")

def convert_date(val, colname='unknown'):
    """
    Improved date converter: supports dmyyyy, ddmmyyyy, dmmyyyy and Excel serials.
    Returns date in YYYY-MM-DD string format for database compatibility.
    Prints a message for unparseable values.
    """
    if pd.isnull(val) or (isinstance(val, str) and str(val).strip().lower() in {'', 'nan', 'none', 'null'}):
        return None

    # If already a datetime or pandas timestamp, convert to date string
    if isinstance(val, (datetime, pd.Timestamp)):
        return val.strftime('%Y-%m-%d')

    try:
        # Handle Excel serial numbers
        if isinstance(val, (float, int)) and val > 30000 and val < 70000:
            excel_origin = datetime(1899, 12, 30)
            # Excel's leap year bug correction
            days = int(val)
            if days > 59:
                days -= 1
            dt = excel_origin + timedelta(days=days)
            return dt.strftime('%Y-%m-%d')
    except Exception as e:
        print(f"[{colname}] Could not parse as Excel serial: {val} ({str(e)})")

    val_s = str(val).strip()
    
    # Handle datetime strings that might have time components
    if ' ' in val_s and ':' in val_s:
        try:
            # Remove time component and parse date part
            date_part = val_s.split(' ')[0]
            
            # Try common date formats explicitly
            date_formats = [
                '%d-%m-%Y',    # DD-MM-YYYY
                '%m-%d-%Y',    # MM-DD-YYYY  
                '%Y-%m-%d',    # YYYY-MM-DD
                '%d/%m/%Y',    # DD/MM/YYYY
                '%m/%d/%Y',    # MM/DD/YYYY
                '%Y/%m/%d'     # YYYY/MM/DD
            ]
            
            for fmt in date_formats:
                try:
                    dt = datetime.strptime(date_part, fmt)
                    return dt.strftime('%Y-%m-%d')
                except ValueError:
                    continue
            
            # If explicit formats fail, try dateutil parser
            # IMPORTANT: Determine if your dates are DD-MM-YYYY or MM-DD-YYYY format
            # For MM-DD-YYYY (Month-Day-Year), use dayfirst=False
            # For DD-MM-YYYY (Day-Month-Year), use dayfirst=True
            
            # Based on your example "10-09-2025" going to database as "09-10-2025"
            # it seems you want MM-DD-YYYY format, so use dayfirst=False:
            dt = parser.parse(date_part, dayfirst=False, yearfirst=False)
            return dt.strftime('%Y-%m-%d')
            
        except Exception as e:
            print(f"[{colname}] Could not parse datetime string: {val} ({e})")
    
    # If all digits, possibly ddmmyyyy/dmmyyyy/dmmmyyyy
    if val_s.isdigit():
        try:
            if len(val_s) == 8:
                # ddmmyyyy (21062022)
                dt = datetime.strptime(val_s, "%d%m%Y")
                return dt.strftime('%Y-%m-%d')
            elif len(val_s) == 7:
                # dmmmyyyy (e.g. 3072025 = 03-07-2025)
                # day: 1 or 2 digits, month: always 2 digits (max 12), year: always 4 digits
                # Try as dmmYYYY (1-2 day, 2 month, 4 year)
                day = int(val_s[:-6])
                month = int(val_s[-6:-4])
                year = int(val_s[-4:])
                if 1 <= day <= 31 and 1 <= month <= 12 and 1900 <= year <= 2100:
                    dt = datetime(year, month, day)
                    return dt.strftime('%Y-%m-%d')
                else:
                    # Try as ddmYYYY (for dates like 1122024, meaning 11-2-2024)
                    day = int(val_s[:-5])
                    month = int(val_s[-5:-4])
                    year = int(val_s[-4:])
                    if 1 <= day <= 31 and 1 <= month <= 12 and 1900 <= year <= 2100:
                        dt = datetime(year, month, day)
                        return dt.strftime('%Y-%m-%d')
            elif len(val_s) == 6:
                # dmyyyy (e.g. 120202: 1-2-2022)
                day = int(val_s[:-4])
                month = int(val_s[-4:-2])
                year = int(val_s[-2:])
                if year < 100:
                    year += 2000
                if 1 <= day <= 31 and 1 <= month <= 12 and 1900 <= year <= 2100:
                    dt = datetime(year, month, day)
                    return dt.strftime('%Y-%m-%d')
        except Exception as e:
            pass  # We'll try dateutil parser below

    # Try with dateutil parser for other cases
    try:
        # CRITICAL FIX: Change dayfirst parameter based on your date format
        # If your dates are MM-DD-YYYY format, use dayfirst=False
        # If your dates are DD-MM-YYYY format, use dayfirst=True
        dt = parser.parse(val_s, dayfirst=False, yearfirst=False)  # Changed to False
        return dt.strftime('%Y-%m-%d')
    except Exception as e:
        print(f"[{colname}] Could not parse as date: {val} ({e})")
        return None

def safe_date_convert(val, colname='unknown'):
    """
    Safe wrapper for date conversion with additional error handling
    """
    try:
        result = convert_date(val, colname)
        # Additional validation - ensure result is in correct format if not None
        if result is not None:
            # Validate the date string format
            datetime.strptime(result, '%Y-%m-%d')
        return result
    except Exception as e:
        print(f"[{colname}] Safe date conversion failed for value: {val} ({e})")
        return None

def process_guarantor_data(row):
    guarantors = []
    # column mappings (as in your code)
    guarantor_columns = [
        {'id_col': 'Guarantor_ID', 'name_col': 'Guarantor_name', 'mobile_col': 'Guarantor_mobile'},
        {'id_col': None, 'name_col': 'Gurantor_2 Name', 'mobile_col': None},
        {'id_col': None, 'name_col': 'Gurantor_3 Name', 'mobile_col': None}
    ]
    co_applicant_columns = [
        {'id_col': 'Co-Applicant_Customar_ID', 'name_col': 'Co-Applicant Name', 'mobile_col': 'Co-Applicant Mobile'}
    ]
    all_guarantor_columns = guarantor_columns + co_applicant_columns
    
    # Process guarantors first
    for i, cols in enumerate(guarantor_columns, 1):
        guarantor_info = {}
        guarantor_name = row.get(cols['name_col'], None)
        if guarantor_name and str(guarantor_name).strip() and str(guarantor_name).lower() not in ['nan', 'none', 'null', '']:
            guarantor_info['guarantor_number'] = i
            guarantor_info['guarantor_name'] = str(guarantor_name).strip()
            # Add id
            if cols['id_col']:
                guarantor_id = row.get(cols['id_col'], '')
                guarantor_info['guarantor_id'] = (str(guarantor_id).strip() 
                        if guarantor_id and str(guarantor_id).lower() not in ['nan', 'none', 'null'] else '')
            else:
                guarantor_info['guarantor_id'] = ''
            # Add mobile
            if cols['mobile_col']:
                guarantor_mobile = row.get(cols['mobile_col'], '')
                if guarantor_mobile and str(guarantor_mobile).lower() not in ['nan', 'none', 'null', '']:
                    guarantor_info['guarantor_mobile'] = clean_mobile_number(str(guarantor_mobile))
                else:
                    guarantor_info['guarantor_mobile'] = ''
            else:
                guarantor_info['guarantor_mobile'] = ''
            
            guarantor_info['guarantor_type'] = 'guarantor'
            guarantors.append(guarantor_info)
    
    # Process co-applicants separately
    for i, cols in enumerate(co_applicant_columns, 1):
        guarantor_info = {}
        guarantor_name = row.get(cols['name_col'], None)
        
        print(f"DEBUG Co-Applicant {i}: Name='{guarantor_name}', ID='{row.get(cols['id_col'], '')}', Mobile='{row.get(cols['mobile_col'], '')}'")
        
        if guarantor_name and str(guarantor_name).strip() and str(guarantor_name).lower() not in ['nan', 'none', 'null', '']:
            guarantor_info['guarantor_number'] = len(guarantor_columns) + i  # Continue numbering after guarantors
            guarantor_info['guarantor_name'] = str(guarantor_name).strip()
            # Add id
            if cols['id_col']:
                guarantor_id = row.get(cols['id_col'], '')
                guarantor_info['guarantor_id'] = (str(guarantor_id).strip() 
                        if guarantor_id and str(guarantor_id).lower() not in ['nan', 'none', 'null'] else '')
            else:
                guarantor_info['guarantor_id'] = ''
            # Add mobile
            if cols['mobile_col']:
                guarantor_mobile = row.get(cols['mobile_col'], '')
                if guarantor_mobile and str(guarantor_mobile).lower() not in ['nan', 'none', 'null', '']:
                    guarantor_info['guarantor_mobile'] = clean_mobile_number(str(guarantor_mobile))
                else:
                    guarantor_info['guarantor_mobile'] = ''
            else:
                guarantor_info['guarantor_mobile'] = ''
            
            # This is the key fix - mark as co_applicant
            guarantor_info['guarantor_type'] = 'co_applicant'
            guarantors.append(guarantor_info)
            print(f"DEBUG Added Co-Applicant: {guarantor_info}")
    
    return guarantors

def process_tulja_raw_data(df1):
    db_operation_success = False
    db_error_message = None
    records_with_guarantors = 0
    records_without_guarantors = 0
    co_applicant_records = 0  # Track co-applicant records

    # Dataframe construction:
    df = pd.DataFrame()
    df['customerid'] = df1['CustomerId']
    df['disbursementid'] = df1['LoanType'] + '-' + df1['DisbursementID'].astype(str) + '-' + df1['branch_id'].astype(str)
    df['customername'] = df1['Customer Name']
    df['loantype'] = df1['LoanType']
    df['mobileno'] = df1['Mobile No'].apply(clean_mobile_number)
    df['disbursementamount'] = df1['DisburseAmount']
    df['rateofinterest'] = df1['RateOfInterest']
    df['repaymenttenure'] = df1['RepaymentTenure']
    df['emiamount'] = df1['EMIAMOUNT']
    df['paymentfrequency'] = df1['Payment Frequency']
    df['numberofdayspastdue'] = df1['NumberOfDaysPastDue']
    df['branchname'] = df1['Branch Nsme']
    
    # Handle nextemidate with safe conversion
    df['nextemidate'] = df1['NextEMIDate'].apply(lambda x: safe_date_convert(x, colname='NextEMIDate'))

    # Convert all relevant dates using the safe conversion function
    date_columns = {
        'InstStartDate': 'inststartdate',
        'DateofLastPayment': 'collectiondate',
        'EndDate': 'closingdate',
        'DateOpen': 'disbursementdate'
    }
    for src_col, tgt_col in date_columns.items():
        print(f"Processing date column: {src_col} -> {tgt_col}")
        df[tgt_col] = df1[src_col].apply(lambda x: safe_date_convert(x, colname=src_col))

    # Handle inserted_date - ensure it's in correct format
    df['inserted_date'] = datetime.now().strftime('%Y-%m-%d')

    df['extracolumn2'] = df1['NO of Dues Installment']
    df['extracolumn3'] = df1['Account Status']
    df['extracolumn5'] = 'All'
    df['extracolumn1'] = df1['GLHead']
    df['originaldisbursementid'] = df1['GLHead']+ "/" + df1['DisbursementID']
    df['bankmstid'] = 378
    df['collectionofficerid'] = df1['CollectionOfficerID']
    df['collectionofficername'] = df1['CollectionOfficerName']
    df['branchcode'] = df1['branch_id']
    df['overdueamount'] = df1['AmountOverdue']
    df['currentbalance'] = df1['CurrentBalance']
    df['collectedamount'] = df1['LastesCollectedAmount']

    # Debug: Print sample of converted dates
    print("\n=== DATE CONVERSION DEBUG ===")
    date_cols_to_check = ['inststartdate', 'collectiondate', 'closingdate', 'disbursementdate', 'nextemidate', 'inserted_date']
    for col in date_cols_to_check:
        if col in df.columns:
            sample_values = df[col].dropna().head(3).tolist()
            print(f"{col}: {sample_values}")
    print("=============================\n")

    # Prepare guarantor output
    all_guarantor_records = []
    for index, row in df1.iterrows():
        main_record = df.iloc[index].to_dict()
        guarantors = process_guarantor_data(row)
        if guarantors:
            records_with_guarantors += 1
            for guarantor in guarantors:
                guarantor_record = main_record.copy()
                
                # Fixed logic for secondary_user_type
                if guarantor.get('guarantor_type') == 'co_applicant':
                    secondary_user_type = 'co-applicant'
                    co_applicant_records += 1
                    print(f"DEBUG Processing Co-Applicant Record: {guarantor.get('guarantor_name')} - Type: {secondary_user_type}")
                else:
                    secondary_user_type = 'guarantor'
                
                guarantor_record.update({
                    'guarantorid': guarantor.get('guarantor_id', ''),
                    'guarantor': guarantor.get('guarantor_name', ''),
                    'guarantor_mobile': guarantor.get('guarantor_mobile', ''),  # Fixed this line
                    'secondary_user_type': secondary_user_type
                })
                all_guarantor_records.append(guarantor_record)
        else:
            records_without_guarantors += 1
            guarantor_record = main_record.copy()
            guarantor_record.update({
                'guarantorid': '',
                'guarantor': '',
                'guarantor_mobile': '',
                'secondary_user_type': ''
            })
            all_guarantor_records.append(guarantor_record)

    final_df = pd.DataFrame(all_guarantor_records)
    
    # Final validation of date columns in the final dataframe
    print("\n=== FINAL DATE VALIDATION ===")
    for col in date_cols_to_check:
        if col in final_df.columns:
            # Check for any invalid date formats
            invalid_dates = []
            for idx, val in final_df[col].items():
                if val is not None and val != '':
                    try:
                        # Try to parse the date to validate format
                        datetime.strptime(str(val), '%Y-%m-%d')
                    except ValueError:
                        invalid_dates.append((idx, val))
            
            if invalid_dates:
                print(f"WARNING: Found invalid dates in {col}: {invalid_dates[:5]}")  # Show first 5
                # Fix invalid dates
                for idx, val in invalid_dates:
                    final_df.at[idx, col] = safe_date_convert(val, col)
            else:
                print(f"{col}: All dates are in correct format")
    print("=============================\n")
    
    # Print co-applicant data for debugging
    co_applicant_data = final_df[final_df['secondary_user_type'] == 'co-applicant']
    print("\n=== CO-APPLICANT DATA ===")
    print(f"Total Co-Applicant records: {len(co_applicant_data)}")
    if len(co_applicant_data) > 0:
        print("Co-Applicant Details:")
        for idx, record in co_applicant_data.iterrows():
            print(f"  Customer: {record['customername']}")
            print(f"  Co-Applicant: {record['guarantor']}")
            print(f"  Co-Applicant ID: {record['guarantorid']}")
            print(f"  Co-Applicant Mobile: {record['guarantor_mobile']}")
            print(f"  Secondary User Type: {record['secondary_user_type']}")
            print("  ---")
    else:
        print("No co-applicant records found!")
    print("=========================\n")
    
    try:
        result = insert_data_in_raw_table(final_df, bank_id=378)
        db_operation_success = result['success']
        if not result['success']:
            db_error_message = result['message']
    except Exception as e:
        db_operation_success = False
        db_error_message = str(e)
        print(f"Database update failed for tulja finance data: {e}")
        result = {'inserted_count': 0, 'duplicate_count': 0, 'message': str(e)}

    return {
        "Total records processed": len(df),
        "Total final records (with guarantor expansion)": len(final_df),
        "New records inserted": result.get('inserted_count', 0),
        "Duplicate records found": result.get('duplicate_count', 0),
        "Records with guarantors": records_with_guarantors,
        "Records without guarantors": records_without_guarantors,
        "Co-applicant records": co_applicant_records,  # Added this
        "db_operation_success": db_operation_success,
        "db_error_message": db_error_message,
        "message": result.get('message', ''),
    }
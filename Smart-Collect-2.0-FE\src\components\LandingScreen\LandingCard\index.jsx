import { Flex,Typography } from 'antd';
import React from 'react';
import { Link } from 'react-router';
import Style from "../_landing-screen.module.scss";
import PropTypes from 'prop-types';

const {Text} = Typography;
export function LandingCard({title, link, image}) {
  return (
    <Link to={link}>  
    <Flex vertical justify='center' className={Style.card_container}>
        <Flex className={Style.img_container} justify='center'>
            <div className={Style.img}>
                <img src={image} alt={title}/>
            </div>
        </Flex>

        <Text className={Style.title}>{title}</Text>
    </Flex>
    </Link>
  
  )
}
LandingCard.propTypes={
  title: PropTypes.string,
  image: PropTypes.string,
  link: PropTypes.string
}

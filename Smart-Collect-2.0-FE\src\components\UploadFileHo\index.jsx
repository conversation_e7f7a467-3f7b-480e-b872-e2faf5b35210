import { <PERSON><PERSON>, <PERSON>, Flex, message, Row, Select, Typography } from "antd";
import React, { useEffect, useState } from "react";
import AppHeader from "../AppHeader";
import Style from "./_style.module.scss";
import { UploadComponent } from "./UploadComponent";
import { AXIOS } from "../../apis/ho-Instance";

const { Text } = Typography;

export default function UploadFileHo() {
  const [errorMessage, setErrorMessage] = useState(null);
  const [showError, setShowError] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState([]); // Store uploaded files
  const [fileType, setFileType] = useState(null); // Store selected file type
  const [fileTypeOptions, setFileTypeOptions] = useState([]); // Store file type options
  const [isUpload, setIsUpload] = useState(false); // State to control upload component
  const handleFileChange = (value) => setFileType(value); // Handle file type selection
  const handleFileUpload = (files) => setSelectedFiles(files); // Handle file upload

  // Function to handle file upload
  const handleUploadFile = async () => {
    if (selectedFiles.length === 0) {
      message.warning("Please select a file before uploading.", 2);
      return;
    }
    setIsLoading(true);

    // Using FormData for file upload
    const formData = new FormData();

    // Append each selected file
    selectedFiles.forEach((file) => {
      formData.append("file", file);
    });

    // Append the file type
    formData.append("type_of_file", fileType);

    console.log("Form data", formData);
    try {
      const response = await AXIOS.post("upload_collection_data/", formData, {
        headers: { "Content-Type": "multipart/form-data" },
        validateStatus: () => true, // <-- Keeps 400 inside try block
      });
      if (response.status === 200) {
        message.success("File uploaded successfully.", 5);
        setFileType(null);
        setSelectedFiles([]);
        setIsUpload(true); // Reset upload component state
        setShowError(false);
      } else {
        setErrorMessage(
          response.data?.message || "File upload failed. Please try again."
        );
        setShowError(true);
      }
    } catch (error) {
      console.log(error?.config);
      setErrorMessage(
        "Server error occurred while uploading the file. Please try again."
      );
      setShowError(true);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch file type options
  const handleGetFileTypeOptions = async () => {
    try {
      const response = await AXIOS.get("upload_collection_data/");
      if (
        response.status === 200 &&
        response.data?.data?.file_type?.length > 0
      ) {
        const updateFileTypeOptions = response.data.data.file_type.map(
          (type) => ({
            label: type,
            value: type,
          })
        );
        setFileTypeOptions(updateFileTypeOptions);
      } else {
        setFileTypeOptions([]);
        message.info(
          response.data?.message || "Failed to retrieve file type.",
          3
        );
      }
    } catch (error) {
      console.log("Error in getting fileType options:", error);
      message.error("Failed to get file type options.", 2);
    }
  };

  useEffect(() => {
    handleGetFileTypeOptions();
  }, []);

  return (
    <Flex vertical gap={20}>
      <AppHeader title={"Upload File"} />

      <Flex vertical gap={20} className={Style.upload_container}>
        <Row gutter={[20, 10]}>
          <Col span={24}>
            <UploadComponent
              onFileUpload={handleFileUpload}
              isUpload={isUpload}
              acceptFileTypes={fileType}
            />
          </Col>
          <Col span={24}>
            {showError && <Text className={Style.error}>{errorMessage}</Text>}
          </Col>
        </Row>
        <Flex gap={10} className={Style.upload_text} align="center">
          <Text className={Style.text}>Select file type : </Text>
          <Select
            className={Style.select}
            placeholder="excel/csv/json"
            onChange={handleFileChange}
            value={fileType}
            options={fileTypeOptions}
            allowClear
            style={{ width: 200 }}
          />
        </Flex>
        <Flex vertical gap={5}>
          {!fileType && (
            <Text className={Style.error}>
              Please select file type to upload the sample.
            </Text>
          )}
          <Text className={Style.text}>
            Add data to an excel file with same format as sample file
          </Text>
        </Flex>

        <Flex justify="center">
          <Button
            loading={isLoading}
            disabled={!fileType}
            className={Style.upload_button}
            style={{ opacity: fileType ? 1 : 0.6 }}
            onClick={handleUploadFile}
          >
            Upload File
          </Button>
        </Flex>
      </Flex>
    </Flex>
  );
}

import React from "react";
import Style from "./_other.module.scss";
import { Flex, Input, Switch, Typography } from "antd";
import PropTypes from "prop-types";

const { Text } = Typography;

export function Other({ feedbackData }) {
  return (
    <div className={Style.other}>
      <Flex vertical gap={20} justify="center">
        {/* reply */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Customer Not Reply:</Text>
          <Switch value={feedbackData?.CustomerNotReply} />
        </Flex>

        {/* wrong number */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Wrong Number:</Text>
          <Switch
            className={Style.switch}
            value={feedbackData?.WrongNumber}
          />
        </Flex>

        {/* wrong number */}
        {feedbackData?.WrongNumber && (
          <Flex justify="space-between" align="center">
            <Text className={Style.text}>Alternate number:</Text>
            <Input
              placeholder="Optional"
              className={Style.custom_input}
              value={feedbackData?.WrongNumber}
              disabled
            />
          </Flex>
        )}

        {/* /remark */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Remark:</Text>
          <Input
            placeholder="Enter"
            className={Style.custom_input}
            value={feedbackData?.Remark}
            disabled
          />
        </Flex>
      </Flex>
    </div>
  );
}

Other.propTypes={
  feedbackData: PropTypes.object
}
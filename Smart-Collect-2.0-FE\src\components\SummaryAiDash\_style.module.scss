@import url("../../index.css");

$table-radius:22px;
$attempts-bg:#BDD1FF;
$disable:#787777;
$body:#E4F8F9;

.title{
    flex-direction: column;
    :global(.ant-typography){
        margin: 0 !important;
        color:#407BFF !important;
        font-family: '<PERSON><PERSON><PERSON>','Courier New', Courier, monospace;
        font-weight: 400 !important;
        font-size: 16px !important;
    }
}

.details{
    margin:0.5rem 0;
}

.download_button{
 background-color: var(--dark-blue);
 padding: 0.2rem 1rem;
 border-radius: 10px;
 cursor: pointer;
 border:none;
 img{
    width: 20px;
 }
}

// Responsive 
@media only screen and (max-width:768px) {
    .download_button{
        margin-top: 1rem;
        padding: 0rem 0.5rem;
        border-radius: 4px;
        img{
          width: 15px;
        }
    }
    .title{
        flex-direction: row;
        :global(.ant-typography){
           font-size: 12px  !important; 
        } 
    }
}
import React, { useEffect, useState } from "react";
import Style from "./_circular-progress.module.scss";
import PropTypes from "prop-types";

const CircularProgress = ({ percentage, duration = 1000 }) => {
  const [progress, setProgress] = useState(0);

  // Calculate the circle's properties
  const radius = 45;
  const circumference = 2 * Math.PI * radius;
  const strokeWidth = 8;
  const viewBoxSize = 100;

  // Calculate the stroke-dashoffset based on current progress
  const strokeDashoffset = circumference - (progress / 100) * circumference;

  useEffect(() => {
    // Reset progress when percentage prop changes
    setProgress(0);

    // Animate from 0 to target percentage
    const startTime = Date.now();
    const animate = () => {
      const currentTime = Date.now();
      const elapsedTime = currentTime - startTime;

      if (elapsedTime < duration) {
        const nextProgress = (elapsedTime / duration) * percentage;
        setProgress(nextProgress);
        requestAnimationFrame(animate);
      } else {
        setProgress(percentage);
      }
    };

    requestAnimationFrame(animate);
  }, [percentage, duration]);

  return (
    <div className={Style.circular_progress}>
      <svg
        className={Style.circular_progress_svg}
        viewBox={`0 0 ${viewBoxSize} ${viewBoxSize}`}
      >
        <circle
          className={Style.circular_progress_background}
          cx={viewBoxSize / 2}
          cy={viewBoxSize / 2}
          r={radius}
          strokeWidth={strokeWidth}
        />

        <circle
          className={Style.circular_progress_progress}
          cx={viewBoxSize / 2}
          cy={viewBoxSize / 2}
          r={radius}
          strokeWidth={strokeWidth}
          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset}
        />
      </svg>

      <div className={Style.circular_progress_text}>
        <span>{Math.round(progress)}%</span>
      </div>
    </div>
  );
};

// Define the types
CircularProgress.propTypes={
  percentage: PropTypes.number,
  duration: PropTypes.number
}

export default CircularProgress;

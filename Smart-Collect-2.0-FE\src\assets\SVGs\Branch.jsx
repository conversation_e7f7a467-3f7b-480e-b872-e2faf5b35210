import PropTypes from "prop-types";
import React from "react";

export default function BranchSVG({ width = 15, height = 15 }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 48 48"
    >
      <g
        fill="none"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="4"
      >
        <circle cx="24" cy="11" r="7" />
        <path d="M4 41c0-8.837 8.059-16 18-16m9 17l10-10l-4-4l-10 10v4z" />
      </g>
    </svg>
  );
}

BranchSVG.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
};

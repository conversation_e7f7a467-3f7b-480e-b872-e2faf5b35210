import { Flex, Table, Typography } from "antd";
import {
  SMART_COLLECT_MENU_IDS,
  formatAmount,
  formattedTextToCapitalized,
  handleDateFormatter,
  ALL_CACHE_NAMES,
} from "../../constant";
import HISTORY_IMG from "../../assets/Images/history_icon.png";
import AppHeader from "../AppHeader";
import Style from "./_style.module.scss";
import ApplicationLoader from "../ApplicationLoader";
import { AXIOS } from "../../apis/ho-Instance";
import { FeedbackModal } from "../Modals/FeedbackModal";
import FoAllocationCustomerModal from "../Modals/FoAllocationCustomerModal";
import {
  getCacheKey,
  getFromCache,
  storeToCache,
} from "../../utils/cacheHelper";
import { useState, useEffect } from "react";

const { Text } = Typography;
const CACHE_NAME = ALL_CACHE_NAMES.FIELD_PENDING_ALLOCATION_CACHE;

export default function FieldPendingAllocation() {
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [modalState, setModalState] = useState({
    feedback: { isOpen: false, data: null },
    customer: { isOpen: false, data: null },
  });

  const openModal = ({ type, data }) => {
    setModalState((prev) => ({
      ...prev,
      [type]: { isOpen: true, data },
    }));
  };

  const closeModal = (type) => {
    setModalState((prev) => ({
      ...prev,
      [type]: { isOpen: false, data: null },
    }));
  };

  // Handle Fetch the API Data
  const handleFetchData = async () => {
    setIsLoading(true);
    // Build cache key for GET request (no params)
    const cacheKey = getCacheKey({ endpoint: "pending_allocation/" });
    // Try to get cached data for instant UI update
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setData(cachedData);
      setIsLoading(false);
    }
    try {
      const res = await AXIOS.get("pending_allocation/");
      if (res.status === 200) {
        setData(res.data.PendingAllocation);
        // Store the latest data in cache
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: res.data.PendingAllocation,
        });
      }
    } catch (error) {
      console.log("Error in pending allocation", error?.message);
    } finally {
      setIsLoading(false);
    }
  };

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      render: (_, data) => <Text>{data.key}.</Text>,
    },
    {
      title: "Name",
      dataIndex: "Name",
      render: (value, data) => (
        <Text
          className={Style.name}
          onClick={() => {
            openModal({ type: "customer", data });
          }}
        >
          {value ? formattedTextToCapitalized(value) : "--"}
        </Text>
      ),
    },
    {
      title: "Contact",
      dataIndex: "Contact",
      render: (value) => <Text className={Style.contact}>{value || "--"}</Text>,
    },
    {
      title: "Overdue Amount",
      dataIndex: "Overdue_Amount",
      render: (value) => (
        <Text style={{ color: "#407BFF", fontWeight: 700 }}>
          Rs. {formatAmount(value || 0)}
        </Text>
      ),
    },
    {
      title: "Promise Amount",
      dataIndex: "PromiseAmount",
      render: (value) => (
        <Text style={{ color: "#407BFF", fontWeight: 700 }}>
          Rs. {formatAmount(value || 0)}
        </Text>
      ),
    },
    {
      title: "Promise Date",
      dataIndex: "PromiseDate",
      render: (value) => (
        <Text>{value ? handleDateFormatter(value) : "--"}</Text>
      ),
    },
    {
      title: "Pending Scene",
      dataIndex: "PendingScene",
      render: (value) => (
        <Text>{value ? handleDateFormatter(value.split("T")[0]) : "--"}</Text>
      ),
    },
    {
      title: "Feedback",
      dataIndex: "feedback",
      width: 100,
      render: (_, data) => (
        <button
          className={Style.icon_div}
          onClick={() => openModal({ type: "feedback", data })}
        >
          <div className={Style.icon_img}>
            <img src={HISTORY_IMG} alt="feedback" />
          </div>
        </button>
      ),
    },
    {
      title: "Status",
      dataIndex: "Status",
      render: (value) => (
        <Text style={{ color: !value == "Pending" ? "#1D9C58" : "#D01B1B" }}>
          {value}
        </Text>
      ),
    },
  ];

  const dataSource = Array.isArray(data)
    ? data.map((d, i) => ({ key: i + 1, ...d }))
    : [];

  useEffect(() => {
    handleFetchData();
  }, []);
  return (
    <Flex vertical gap={40}>
      <AppHeader title={"Pending Allocation"} />
      <div>
        {isLoading ? (
          <ApplicationLoader />
        ) : (
          <Table
            virtual
            className={Style.customTable}
            columns={columns}
            dataSource={dataSource}
            scroll={{
              x: 1000,
              y: 360,
            }}
            pagination={{
              showSizeChanger: false,
            }}
          />
        )}
      </div>

      <FeedbackModal
        pageId={SMART_COLLECT_MENU_IDS.READY_TO_PAY}
        overdueAmount={modalState.feedback.data?.Overdue_Amount}
        branchMstId={modalState.feedback.data?.BranchMstID}
        loanmstid={modalState.feedback.data?.LoanMstID}
        modalStatus={modalState.feedback.isOpen}
        handleSubmit={() => closeModal("feedback")}
        handleCancel={() => closeModal("feedback")}
      />
      <FoAllocationCustomerModal
        data={modalState.customer.data}
        modalStatus={modalState.customer.isOpen}
        handleCancel={() => closeModal("customer")}
      />
    </Flex>
  );
}

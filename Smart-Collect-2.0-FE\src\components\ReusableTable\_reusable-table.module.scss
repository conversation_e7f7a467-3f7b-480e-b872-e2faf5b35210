@import url("../../index.css");

$table-radius:22px;
$attempts-bg:#BDD1FF;
$disable:#787777;
$body:#E4F8F9;

.CustomTable {
    padding-top: 1rem;

    .text {
        font-weight: 500;
        white-space: nowrap;
        color: var(--dark-blue);
    }

    .blueText {
        font-weight: 700;
        color: var(--blue);
        cursor: pointer;
    }

    .button {
        border: none;
        background-color: transparent;
        color: var(--blue);
        font-weight: 700;
        cursor: pointer;
    }

    // Global ant design classes

    // Pagination
    :global(.ant-pagination) {
        justify-content: center;
        margin: 0 !important;

        :global(.ant-pagination-prev),
        :global(.ant-pagination-next) {
            color: var(--dark-blue);
            border: 0 solid var(--blue);
            background: #E4F8F9;
            height: 25px;
            min-width: 15px;
            border-radius: 0px;
            margin: 0;

            button {
                border-radius: 0px;
            }
        }

        :global(.ant-pagination-item) {
            margin-right: 0;
            height: 0;

            a {
                color: $disable;
                font-size: 0.9rem;
                line-height: 23px;
            }

            &:hover {
                background-color: transparent;
            }
        }

        :global(.ant-pagination-item-active) {
            border: none;

            a {
                color: var(--dark-blue);
                font-size: 1.2rem;
                padding: 0;
            }
        }
    }

    // Table container
    :global(.ant-table-container) {
        padding: 0 1rem;
        margin-bottom: 0.5rem;
        background: var(--light-green);
        border-start-start-radius: $table-radius;
        border-start-end-radius: $table-radius;

        // Table header
        :global(.ant-table-header) {
            position: relative;
            margin: 0 auto;
            top: -21px;
            border-radius: $table-radius;
        }

        // Table virtual body
        :global(.ant-table-tbody-virtual) {
            margin-top: -8px;
        }

        &::before {
            box-shadow: none !important;
        }
    }

    // Table rows header 
    :global(.ant-table-thead > tr) {
        th {
            border-start-end-radius: 0 !important;
            background-color: var(--dark-blue);
            border-inline-end: none !important;
            color: white;
            border-bottom: none;
            white-space: nowrap;
            text-align: center;
            font-family: 'Kanit','Courier New', Courier, monospace;
            font-weight: normal;
            &:nth-child(2) {
                padding-right: 150px; // Shift the second column header to the right
            }

            &:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
                width: 0;
            }
        }
    }

    // Table body
    :global(.ant-table-tbody) {
        // Body rows
        :global(.ant-table-row) {
            // Cols
            :global(.ant-table-cell) {
                text-align: center;
                border-bottom: 2px solid white;
                padding: 10px; 
                font-family: 'Kanit','Courier New', Courier, monospace;
                font-weight: normal;
                
                &:first-child {
                    border-right: none;
                    padding-right: 20px; // Reduce padding on the right of the first column
                }

                &:nth-child(2) {
                    border-right: none;
                    padding-right: 150px; // Shift the second column data to the right
                }

                &:global(.ant-table-cell-row-hover) {
                    background-color: $body;
                }
            }
        }
    }

    // Fixed Cols
    :global(.ant-table-cell-fix-right) {
        background-color: $body;
    }
}
$table-radius:22px;
$attempts-bg:#BDD1FF;
$disable:#787777;
$body:#E4F8F9;

.customTable{
    padding-top: 1.5rem;
    .blue_text{
        font-weight: 500;
        color: #407BFF;
    }

    .icon_div{
        display: flex;
        justify-content: center;
        width: 100%;  
        .icon_img{
        width: 20px;
        height: 20px;
        img{
            width: 100%;
            object-fit: contain;
            cursor: pointer;
        }
      }
    }

    .icon{
        display: flex;
        align-items: center;
        cursor: pointer;
        img{
            width: 3.5px;
        }
    }

    .text{
        color: white;
        font-family: 'Kanit','Courier New', Courier, monospace;

    }
   
    //Global ant design classes

    // Pagination
    :global(.ant-pagination){
        justify-content: center;
        margin: 0 !important;
        
        :global(.ant-pagination-prev),
        :global(.ant-pagination-next){
            color: var(--dark-blue);
            border: 0 solid var(--blue);
            background: #E4F8F9;
            height: 25px;
            min-width: 15px;
            border-radius: 0px;
            margin: 0;
            button{
                border-radius: 0px;
            }
        }
        :global(.ant-pagination-item){
            margin-right: 0;
            height: 0;
            a{
                color:$disable;
                font-size: 0.9rem;
                line-height: 23px;
                font-family: 'Kanit','Courier New', Courier, monospace;
                font-weight: normal;
            }
            &:hover{
                background-color: transparent;
            }
        }
        :global(.ant-pagination-item-active){
            border:none;
           a{
                color:var(--dark-blue);
                font-size:1.2rem;
                padding: 0;
           }
        }
    }

    //Table container
    :global(.ant-table-container){
        padding: 0;
        margin-bottom: 0.5rem;
        border-start-start-radius: $table-radius;
        border-start-end-radius: $table-radius;

        // Table header
        :global(.ant-table-header) {
            position: relative;
            margin: 0 auto;
            top: -21px;
            border-radius: $table-radius;
            :global(.ant-table-column-has-sorters) {
            background-color: var(--dark-blue);
            &:hover{
                background-color: var(--dark-blue);
            }
        
            :global(.ant-table-column-sorter-up),
            :global(.ant-table-column-sorter-down) {
                svg{
                fill: white;
                }
                &:global(.active){
                svg{
                    fill: rgb(24, 155, 249)
                    }
                    }
                }
                }
        }

        // Table virtual body
        :global(.ant-table-tbody-virtual){
            margin-top: -8px;
        }

        &::before{
            box-shadow: none !important;
        }
    }

    // Table rows header 
    :global(.ant-table-thead >tr){
        th{
            border-start-end-radius:0 !important;
            background-color: var(--dark-blue);
            border-inline-end: none !important;
            color: white;
            border-bottom:none;
            text-align: center;
            font-family: 'Kanit','Courier New', Courier, monospace;
            font-weight: normal;
            &:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before{
                width: 0;
            }
            &:global(.ant-table-column-sort),
            &:global(.ant-table-column-has-sorters){
              &:hover{
                background-color: var(--dark-blue);
              }
              background-color: var(--dark-blue);
            }
        }
    }

    // Table body
    :global(.ant-table-tbody){
        // Body rows
        :global(.ant-table-row){  
            &:nth-child(even){
                background-color: var(--light-green);
            }  
        
        :global(.ant-table-cell-row-hover){
            background: transparent;
        }
        // Cols
        :global(.ant-table-cell){
            font-family: 'Kanit','Courier New', Courier, monospace;
            font-weight: normal;
            text-align: center;
            border-bottom: 2px solid white; 
            font-size: 11px;
            padding: 0.8rem;

            :global(.ant-typography){
                font-family: 'Kanit','Courier New', Courier, monospace;
            }

            &:global(.ant-table-cell-fix-right){
                border-bottom: none !important; 
           }
            &:global(.ant-table-cell-fix-right-first){
                border-left: 2px solid white;
            }
        }
        }
    }

    // Fixed Cols
    :global(.ant-table-cell-fix-right){
        background-color: $body;
   }

    //Sorting
    :global(.ant-table-column-sorter){
        :global(.ant-table-column-sorter-inner){
            :global(.ant-table-column-sorter-up){
                background-color: red;
                position: relative;

                &::after{
                  content: '';
                  position: absolute;
                  width: 1px;
                  height: 10px;
                  background-color: white;
                  left: 1px;
                  clip-path: polygon(35% 25%, 35% 25%, 35% 0%, 39% 0, 39% 26%, 38% 0, 83% 0, 41% 26%, 42% 100%, 35% 100%, 35% 50%, 35% 76%);
                }

                svg{
                    display: none;
                }
            }
        }
    }

}
// Responsive view
@media screen and (max-width:768px) {
    .customTable{
        :global(.ant-table-thead >tr){
            th{
                padding: 0.5rem;
            }
        } 
        :global(.ant-table-tbody){
            :global(.ant-table-row){
                :global(.ant-table-cell){
                    padding: 0.5rem;
                }
            }
        }
    }
}
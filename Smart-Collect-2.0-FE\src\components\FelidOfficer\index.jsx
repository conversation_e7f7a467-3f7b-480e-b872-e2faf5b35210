import { Flex, message, Spin, Table, Typography } from "antd";
import React, { useEffect, useState } from "react";
import AppHeader from "../AppHeader";
import {
  ALL_CACHE_NAMES,
  formatAmount,
  handleDownloadTable,
} from "../../constant";
import { AXIOS } from "../../apis/ho-Instance";
import Style from "./_field.module.scss";
import DOWNLOAD_IMG from "../../assets/Images/download.svg";
import {
  getFromCache,
  getPostCacheKey,
  storeToCache,
} from "../../utils/cacheHelper";

const CACHE_NAME = ALL_CACHE_NAMES.H_FIELD_CACHE;

const { Text } = Typography;
export default function FelidOfficer() {
  const [data, setData] = useState([]);
  const [prevData, setPrevData] = useState([]);
  const [selectedState, setSelectedState] = useState(null);
  const [selectedRegion, setSelectedRegion] = useState(null);
  const [selectedBranch, setSelectedBranch] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const From_Date = new Date().toISOString().split("T")[0];
  const To_Date = new Date().toISOString().split("T")[0];

  // Get state
  const handleGetState = async () => {
    setIsLoading(true);
    const body = {
      from_date: From_Date,
      to_date: To_Date,
    };
    const cacheKey = getPostCacheKey({ endpoint: "ho-field-officer/", body });
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setData(cachedData);
      setPrevData(cachedData);
      setIsLoading(false);
    }
    try {
      const response = await AXIOS.post("fieldofficer/", {
        from_date: From_Date,
        to_date: To_Date,
      });
      if (
        response.status === 200 &&
        Array.isArray(response.data) &&
        response.data?.length
      ) {
        setData(response.data);
        setPrevData(response.data);
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
      } else {
        setData([]);
        setPrevData([]);
      }
    } catch (error) {
      console.log("Error in Field officer", error);
      setPrevData([]);
      setData([]);
    } finally {
      setIsLoading(false);
    }
  };

  //Get Region, branch, CO.
  const handleGetOtherColsData = async ({ BUType, BUName }) => {
    setIsLoading(true);
    const body = {
      from_date: From_Date,
      to_date: To_Date,
      BUType,
      BUName,
    };
    const cacheKey = getPostCacheKey({ endpoint: "ho-field-officer/", body });
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setData(cachedData);
      setPrevData(cachedData);
      setIsLoading(false);
    }
    try {
      const res = await AXIOS.post("fieldofficer/", {
        from_date: From_Date,
        to_date: To_Date,
        BUType,
        BUName,
      });

      if (res.status === 200 && res.data?.length) {
        setData(res.data);
        setPrevData(res.data);
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: res.data,
        });
      } else {
        message.warning(`Sorry, there is not data related to ${BUName}`);
        setData(prevData);
      }
    } catch (error) {
      console.log("Error in Promise table", error?.message);
      message.warning(`No data found for ${BUName}`);
      setData(prevData);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle State Click → Get Regions
  const handleStateClick = (state) => {
    setSelectedState(state);
    setSelectedRegion(null); // Reset Region when State is selected
    setSelectedBranch(null); // Reset Branch when State is selected
    handleGetOtherColsData({ BUType: "State", BUName: state });
  };

  // Handle Region Click → Get Branches
  const handleRegionClick = (region) => {
    setSelectedRegion(region);
    setSelectedBranch(null); // Reset Branch when Region is selected
    handleGetOtherColsData({ BUType: "Region", BUName: region });
  };

  // Handle Branch Click → Get Collection Officers
  const handleBranchClick = (branch) => {
    setSelectedBranch(branch);
    handleGetOtherColsData({ BUType: "Branch", BUName: branch });
  };

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      sorter: (a, b) => a.key - b.key,
      render: (_, data) => <Text className={Style.amtText}>{data?.key}.</Text>,
    },
    {
      title: "State",
      dataIndex: "State",
      sorter: (a, b) => a.State?.localeCompare(b.State),
      render: (_, { State }) => (
        <Text
          className={Style.blueText}
          onClick={() => handleStateClick(State)}
        >
          {State || "-"}
        </Text>
      ),
    },
    ...(selectedState
      ? [
          {
            title: "Region",
            dataIndex: "region",
            sorter: (a, b) => a.Region?.localeCompare(b.Region),
            render: (_, { Region }) => (
              <Text
                className={Style.blueText}
                onClick={() => handleRegionClick(Region)}
              >
                {Region || "-"}
              </Text>
            ),
          },
        ]
      : []),
    ...(selectedRegion
      ? [
          {
            title: "Branch",
            dataIndex: "Branch",
            sorter: (a, b) => a.Branch?.localeCompare(b.Branch),
            render: (_, { Branch }) => (
              <Text
                className={Style.blueText}
                onClick={() => handleBranchClick(Branch)}
              >
                {Branch || "-"}
              </Text>
            ),
          },
        ]
      : []),
    ...(selectedBranch
      ? [
          {
            title: "Collection Officer",
            dataIndex: "CollectionOfficerName",
            sorter: (a, b) =>
              a.CollectionOfficerName?.localeCompare(b.CollectionOfficerName),
            render: (_, { CollectionOfficerName }) => (
              <Text>{CollectionOfficerName || "-"}</Text>
            ),
          },
        ]
      : []),
    {
      title: "Total",
      dataIndex: "total",
      children: [
        {
          title: "#Lan",
          dataIndex: "TotalLoans",
          sorter: (a, b) => a.TotalLoans - b.TotalLoans,
          render: (_, { TotalLoans }) => (
            <Text className={Style.lan}>{TotalLoans || 0}</Text>
          ),
        },
        {
          title: "Amount",
          dataIndex: "TotalAmount",
          sorter: (a, b) => a.TotalAmount - b.TotalAmount,
          render: (_, { TotalAmount }) => (
            <Text className={Style.amtText}>
              ₹{formatAmount(TotalAmount) || 0}
            </Text>
          ),
        },
      ],
    },
    {
      title: "Pending",
      dataIndex: "pending",

      children: [
        {
          title: "#Lan",
          dataIndex: "PendingLoans",
          sorter: (a, b) => a.PendingLoans - b.PendingLoans,
          render: (_, { PendingLoans }) => (
            <Text className={Style.lan}>{PendingLoans || 0}</Text>
          ),
        },
        {
          title: "Amount",
          dataIndex: "PendingAmount",
          sorter: (a, b) => a.PendingAmount - b.PendingAmount,
          render: (_, { PendingAmount }) => (
            <Text className={Style.amtText}>
              ₹{formatAmount(PendingAmount) || 0}
            </Text>
          ),
        },
      ],
    },
    {
      title: "Complete",
      dataIndex: "complete",
      children: [
        {
          title: "#Lan",
          dataIndex: "CollectedLoans",
          sorter: (a, b) => a.CollectedLoans - b.CollectedLoans,
          render: (_, { CollectedLoans }) => (
            <Text className={Style.lan}>{CollectedLoans || 0}</Text>
          ),
        },
        {
          title: "Amount",
          dataIndex: "CollectedAmount",
          sorter: (a, b) => a.CollectedAmount - b.CollectedAmount,
          render: (_, { CollectedAmount }) => (
            <Text className={Style.amtText}>
              ₹{formatAmount(CollectedAmount) || 0}
            </Text>
          ),
        },
      ],
    },
  ];

  const sortedData = selectedState
    ? [...data].sort((a, b) => (a.region === selectedState ? -1 : 1))
    : data;

  const dataSource = Array.isArray(sortedData)
    ? sortedData.map((data, i) => ({
        key: i + 1,
        ...data,
      }))
    : [];

  useEffect(() => {
    handleGetState();
  }, []);

  // Handle downloading
  const handleDownload = async () => {
    if (data.length === 0) {
      return message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: "Field Officer",
        worksheetName: "field-officer",
        tableData: data,
      });
    }
  };
  return (
    <Flex vertical gap={20}>
      <AppHeader title={"Field Officer"} />
      <Spin tip={"Loading..."} spinning={isLoading}>
        <Flex justify="end">
          <button className={Style.download_button} onClick={handleDownload}>
            <img src={DOWNLOAD_IMG} alt="download-button" />
          </button>
        </Flex>
        <Table
          bordered
          virtual
          className={Style.field_officer}
          columns={columns}
          dataSource={dataSource}
          scroll={{
            // x: 2000,
            y: 460,
          }}
          pagination={{
            showSizeChanger: false,
          }}
        />
      </Spin>
    </Flex>
  );
}

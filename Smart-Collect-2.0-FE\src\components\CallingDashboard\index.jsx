import { Col, DatePicker, Flex, Row, Typography } from "antd";
import AppHeader from "../AppHeader";
import Style from "./_style.module.scss";
import { useState,useEffect } from "react";
import dayjs from "dayjs";
import { DetailsCard } from "./DetailsCard";
import READY_IMG from "../../assets/Images/ready.png";
import { CampaignCard } from "./CampaignCard";
import {AXIOS } from "../../apis/ho-Instance";
const { Text, Title } = Typography;

export default function CallingDashboard() {
  const [selectedDate, setSelectedDate] = useState(dayjs());
  const [taskData, setTaskData] = useState({
    summary: {
      total_distinct_loans: 0,
      total_overdue_amount: 0,
      pending_tasks_count: 0,
      completed_tasks_count: 0,
    },
    loan_details: [],
  });
  const [pendingTasksData, setPendingTasksData] = useState({
    summary: {
      total_distinct_loans: 0,
      total_overdue_amount: 0,
    },
    loan_details: [],
  });
  const [completedTasksData, setCompletedTasksData] = useState({
    summary: {
      total_distinct_loans: 0,
      total_overdue_amount: 0,
    },
    loan_details: [],
  });
  const [campaignData, setCampaignData] = useState([]);

  
  useEffect(() => {
    const fetchTaskData = async () => {
      try {
        const response = await AXIOS.get('total_task/');
        if (response.status === 200) {
          setTaskData(response.data);
        }
      } catch (error) {
        console.error("Error fetching task data:", error);
      }
    };
    const fetchPendingData = async () => {
      try {
        const response = await AXIOS.get('pending_task/');
        if (response.status === 200) {
          setPendingTasksData(response.data);
        }
      } catch (error) {
        console.error("Error fetching Pending task data:", error);
      }
    };
    const fetchCompletedData = async () => {
      try {
        const response = await AXIOS.get('completed_task/');
        if (response.status === 200) {
          setCompletedTasksData(response.data);
        }
      } catch (error) {
        console.error("Error fetching Completed task data:", error);
      }
    };
    const fetchCampaignData = async () => {
      try {
        const response = await AXIOS.get('campaign_detail/');
        if (response.status === 200) {
          // Transform campaigns object to array
          const campaigns = Object.values(response.data.campaigns).map(campaign => ({
            name: campaign.campaign_info.campaign_name,
            pendingTask: campaign.campaign_info.pending_tasks,
            completedTask: campaign.campaign_info.completed_tasks
          }));
          setCampaignData(campaigns);
        }
      } catch (error) {
        console.error("Error fetching campaign data:", error);
      }
    };

    fetchTaskData();
    fetchPendingData();
    fetchCompletedData();
    fetchCampaignData();
  },[])


    // Calculate total tasks and percentages
    const totalTasks = taskData.summary.total_distinct_loans;
    const pendingTasks = pendingTasksData.summary.total_distinct_loans;
    const completedTasks = completedTasksData.summary.total_distinct_loans;
    const totalAmount = taskData.summary.total_overdue_amount;
    
    const pendingAmount = pendingTasksData.summary.total_overdue_amount;
    const completedAmount = completedTasksData.summary.total_overdue_amount;
  
    const pendingPercentage = totalTasks > 0 ? Math.round((pendingTasks / totalTasks) * 100) : 0;
    const completedPercentage = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
  

  return (
    <Flex vertical gap={20} className={Style.container}>
      <AppHeader title={"Dashboard"} />

      <Flex vertical gap={15}>
        {/* Date */}
        <Flex justify="end" align="center" gap={10}>
          <Text className={Style.date_text}>Date:</Text>
          <DatePicker
            className={Style.date_picker}
            onChange={(date) => setSelectedDate(date)}
            value={selectedDate}
            format="DD-MM-YYYY"
            placeholder="dd-mm-yyyy"
            disabledDate={(current) => {
              const today = dayjs().startOf("day");
              return !current.isSame(today, "day");
            }}
            disabled
          />
        </Flex>

        {/*Task details  */}
        <Row gutter={[20, 20]} justify="space-between" className={Style.details_row}>
          <Col span={24} md={8}>
            <DetailsCard
              cardName="Total Tasks"
              percentValue={ 100}
              customerCount={ totalTasks}
              totalAmount={ totalAmount}
              amountCollected={ 0}
              image={READY_IMG}
            />
          </Col>

          <Col span={24} md={8}>
            <DetailsCard
              cardName={"Pending Tasks"}
              percentValue={pendingPercentage}
              customerCount={ pendingTasks}
              totalAmount={ pendingAmount}
              amountCollected={0}
              image={READY_IMG}
            />
          </Col>

          <Col span={24} md={8}>
            <DetailsCard
              cardName={"Completed Tasks"}
              percentValue={completedPercentage}
              customerCount={completedTasks}
              totalAmount={completedAmount}
              amountCollected={0}
              image={READY_IMG}
            />
          </Col>
        </Row>

        {/* Campaign wise task */}
        <Row gutter={[20,20]} className={Style.details_row}>
          <Col span={24}>
              <Text className={Style.title}>Campaign Wise Tasks:</Text>
          </Col>
          {campaignData.map((campaign, index)=>(
              <Col span={24} md={6} key={`${index}-${campaign.name}`}>
                <CampaignCard 
                  name={campaign.name} 
                  pendingTask={campaign.pendingTask} 
                  completedTask={campaign.completedTask}
                  />
              </Col>
            ))
          }
        </Row>
      </Flex>
    </Flex>
  );
}

from ..utils import clean_mobile_number
import pandas as pd
import datetime as datetime
from ..queries import insert_data_in_raw_table
from .process_data import create_rawfile_records
import threading
from ..sp import execute_post_processing_procedures

def run_post_processing_in_background(bank_id):
    """
    Run post-processing procedures in a background thread
    """
    try:
        print(f"Starting background post-processing for bank_id: {bank_id}")
        execute_post_processing_procedures(bank_id)
        print(f"Background post-processing initiated for bank_id: {bank_id}")
    except Exception as e:
        print(f"Error starting background post-processing for bank_id {bank_id}: {str(e)}")

def process_pavana_overdue_data(df, bank_id=21):
    db_operation_success = False
    db_error_message = None
    
    # Data type conversions
    df["Main Cust No"] = df["Main Cust No"].astype(str)
    df["Ac No"] = df["Ac No"].astype(str)
    df["Prd Cd"] = df["Prd Cd"].astype(str)

    # Date conversions
    df["Disbursed Date"] = pd.to_datetime(df["Disbursed Date"], format='%d-%m-%Y', errors='coerce')
    df["Expiry Date"] = pd.to_datetime(df["Expiry Date"], format='%d-%m-%Y', errors='coerce')
    df["Instal Start Date"] = pd.to_datetime(df["Instal Start Date"], format='%d-%m-%Y', errors='coerce')
    df["Report Date"] = pd.to_datetime(df["Report Date"], format='%d-%m-%Y', errors='coerce')

    # Handle branch information
    df[['BranchCode', 'BranchName']] = df["Br Name"].str.split('-', n=1, expand=True)
    df['BranchName'] = df['BranchName'].str.strip()
    df['BranchCode'] = df['BranchCode'].astype(str)

    # Clean contact info
    df["Mobile No"] = df["Mobile No"].apply(clean_mobile_number)
    df["Mobile No2"] = df["Mobile No2"].apply(clean_mobile_number)

    # Clean guarantor mobile numbers
    df["Guarantor Tel No1"] = df["Guarantor Tel No1"].apply(clean_mobile_number)
    df["Guarantor Tel No2"] = df["Guarantor Tel No2"].apply(clean_mobile_number)
    df["Guarantor Tel No3"] = df["Guarantor Tel No3"].apply(clean_mobile_number)

    # Numeric columns
    df["Sanction Amount"] = pd.to_numeric(df["Sanction Amount"], errors='coerce')
    df["Interest Rate"] = pd.to_numeric(df["Interest Rate"], errors='coerce')
    df["Instal Amount"] = pd.to_numeric(df["Instal Amount"], errors='coerce')
    df["Int Overdue"] = pd.to_numeric(df["Int Overdue"], errors='coerce').fillna(0)
    df["Total OVerdue"] = pd.to_numeric(df["Total OVerdue"], errors='coerce').fillna(0)
    df['collectedamount'] = pd.to_numeric(df['Recovery Amt'], errors='coerce').fillna(0)
    
    # FIXED: Handle Recovery Date conversion properly
    def parse_recovery_date(date_str):
        """
        Parse recovery date and return pandas datetime object or NaT
        Input can be in multiple formats: DD-MM-YYYY or YYYY-MM-DD HH:MM:SS
        """
        if pd.isna(date_str) or date_str == '':
            return pd.NaT
        
        try:
            # Convert to string first to handle any numeric inputs
            date_str = str(date_str).strip()
            
            # If it's already in YYYY-MM-DD format (with or without time)
            if len(date_str) >= 10 and date_str[4] == '-' and date_str[7] == '-':
                # Extract just the date part if it has time
                date_part = date_str.split(' ')[0]
                return pd.to_datetime(date_part, format='%Y-%m-%d')
            
            # If it's in DD-MM-YYYY format
            elif len(date_str) == 10 and date_str[2] == '-' and date_str[5] == '-':
                return pd.to_datetime(date_str, format='%d-%m-%Y')
            
            # Generic fallback
            else:
                return pd.to_datetime(date_str, errors='coerce')
                
        except:
            try:
                # Final fallback
                return pd.to_datetime(date_str, errors='coerce')
            except:
                return pd.NaT
    
    # Apply the date parsing - this returns datetime objects, not strings
    df['collectiondate'] = df['Recovery Date'].apply(parse_recovery_date)

    # Calculate tenure in days
    df["RepaymentTenure"] = (df["Expiry Date"] - df["Disbursed Date"]).dt.days

    # Handle frequency codes (now in days)
    frequency_mapping = {
        'M': 30,      # Monthly
        'Q': 90,      # Quarterly
        'H': 180,     # Half-yearly
        'Y': 365,     # Yearly
        'U': 0,       # Unknown
        '': 0         # Empty
    }
    df["PaymentFrequency"] = df["Ln Inst Freq"].str.upper().map(frequency_mapping).fillna(0).astype(int)

    # Overdue days
    df["NumberOfDaysPastDue"] = pd.to_numeric(df["Over Due Age"], errors='coerce').fillna(0).astype(int)

    # Convert datetime to date objects - NOW THIS WILL WORK
    date_cols = ["Disbursed Date", "Expiry Date", "Instal Start Date", "Report Date"]
    for col in date_cols:
        if col in df.columns:
            # Handle NaT values properly
            df[col] = df[col].dt.date.where(df[col].notna(), None)
    
    # Handle collectiondate separately to ensure proper format
    if 'collectiondate' in df.columns:
        # Convert to date objects, handling NaT values
        df['collectiondate'] = df['collectiondate'].dt.date.where(df['collectiondate'].notna(), None)

    # Create DisbursementID by combining BranchCode + LoanType + AccountNo
    df['DisbursementID'] = (df['BranchCode'].astype(str).str.strip() + 
                           df['Prd Cd'].astype(str).str.strip() + 
                           df['Ac No'].astype(str).str.strip())
    df['originaldisbursementid'] = df["Ac No"].astype(str).str.strip()
    raw_file_df = create_rawfile_records(df, bank_id)
    
    try:
        result = insert_data_in_raw_table(raw_file_df, bank_id=21)
        db_operation_success = result['success']
        if not result['success']:
            db_error_message = result['message']
    except Exception as e:
        db_operation_success = False
        db_error_message = str(e)
        print(f"Database update failed for pavana data: {e}")
    
    # # ========== START BACKGROUND POST-PROCESSING ==========
    if db_operation_success:
        # Start post-processing in background thread
        background_thread = threading.Thread(
            target=run_post_processing_in_background,
            args=(bank_id,),
            daemon=True
        )
        background_thread.start()
        print(f"Post-processing started in background thread for bank_id: {bank_id}")
    else:
        print(f"Skipping post-processing due to database operation failure: {db_error_message}")
    return {
        "Total records processed": len(raw_file_df),
        "Records with guarantors": len(raw_file_df[raw_file_df['guarantor'].notna()]),
        "Records without guarantors": len(raw_file_df[raw_file_df['guarantor'].isna()]),
        "New records inserted": result.get('inserted_count', 0) if db_operation_success else 0,
        "Duplicate records found": result.get('duplicate_count', 0) if db_operation_success else 0,
        "db_operation_success": db_operation_success,
        "db_error_message": db_error_message,
        "message": result.get('message', '') if db_operation_success else db_error_message
    }
import { Modal, Flex, Typography, Input, Button, InputNumber, message } from 'antd'
import React, { useState } from 'react';
import PHONE_IMG from "../../../assets/Images/solar_phone-linear.png"
import Style from "./_style.module.scss";
import PropTypes from 'prop-types';

const {Text, Title} = Typography;

export function DialerModal({ 
  branchMstId = null,
  modalStatus = false,
  customerName,
  overdueAmount,
  loanmstid, 
  isLoading,
  handleSubmit, 
  handleCancel,}) {

 const [messageApi, contextHolder] = message.useMessage();
 const[number, setNumber] = useState("");

  const isValidIndianPhoneNumber = (num) => {
    const indianPhoneRegex = /^[6-9]\d{9}$/;
    return indianPhoneRegex.test(num);
  };

 const handleCross = () =>{
    handleCancel();
 }
 
 const handleOk = ()=>{
  if (!isValidIndianPhoneNumber(number)) {
    messageApi.error("Please enter a valid Indian phone number.");
    return;
  }
  handleSubmit({number, loanmstid, overdueAmount, branchMstId});
 }

return (
<>
  {contextHolder}
  <Modal 
    centered
    className={Style.dialer_modal} 
    title={
      <Flex align='center' gap={5} justify='center'>
        <Title level={4} className={Style.feedback_title}>
            Dialer
        </Title>
      </Flex>
    }
    open={modalStatus} 
    onOk={handleOk} 
    onCancel={handleCross}
    footer={null}
    >
      <Flex vertical gap={20}>
          <Flex gap={20} justify='space-between'>
            <Text className={Style.text}>Customer Name:</Text>
            <Input className={Style.custom_input} value={customerName} disabled={true}/>
          </Flex>

          <Flex gap={20} justify='space-between'>
            <Text className={Style.text}>Overdue Amount:</Text>
            <Input className={Style.custom_input} value={overdueAmount} disabled={true}/>
          </Flex>

          <Text strong className={Style.message_text}>*Verify your number before proceeding. If needed, update it below and Initiate Call to Continue.*</Text>

          <Flex gap={20} justify='space-between'>
            <Text className={Style.text}>Initiating Number:</Text>
            <InputNumber
            className={Style.custom_number} 
            value={number} 
            onChange={(value)=> setNumber(value)}/>
          </Flex>

          <Flex justify='center'>
            <Button loading={isLoading} 
            className={Style.custom_button} 
            onClick={handleOk}>
              <Flex gap={5} align='center'>
                <div className={Style.img}>
                 <img src={PHONE_IMG} alt='phone'/> 
                </div>
                
                <Text>Initiate Call</Text>
              </Flex>
            </Button>
          </Flex>
      </Flex>
  </Modal>
</>
  )
}

DialerModal.propTypes={
  branchMstId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  modalStatus: PropTypes.bool,
  customerName: PropTypes.string,
  overdueAmount: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  loanmstid: PropTypes.oneOfType([PropTypes.string, PropTypes.number]), 
  isLoading: PropTypes.bool,
  handleSubmit: PropTypes.func, 
  handleCancel: PropTypes.func
}
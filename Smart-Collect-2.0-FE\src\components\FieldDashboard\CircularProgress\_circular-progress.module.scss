@import url("../../../index.css");

.circular_progress {
    position: relative;
    width: 120px;
    height: 120px;
    
    .circular_progress_svg {
      width: 100%;
      height: 100%;
      transform: rotate(-90deg);
      filter: drop-shadow(3px 5px 2px rgba(0, 0, 0, 0.166));

    }
    
    .circular_progress_background {
      stroke: white;
      fill: none;
      stroke-width: 10px;
      box-shadow: 5.53px 5.53px 5.53px 0px #00000040 inset;
    }
    
    .circular_progress_progress {
      stroke: var(--dark-blue);
      fill: none;
      stroke-linecap: square;
      stroke-width: 10px;
      transition: stroke-dashoffset 0.1s ease;
    }
    
    .circular_progress_text {
      position: absolute;
      inset: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      
      span {
        font-size: 24px;
        font-weight: 600;
        color: var(--dark-blue);
        font-family: 'Kanit','Courier New', Courier, monospace;
      }
    }
}

// Responsive view
@media screen and (max-width:1024px) {
  .circular_progress{
    width: 80px;
    height: 80px;

    .circular_progress_text{
      span{
        font-size: 16px;  
      }
    }
  }
}

@media screen and (max-width:1024px) {
  .circular_progress{
    width: 60px;
    height: 60px;

    .circular_progress_text{
      span{
        font-size: 12px;  
      }
    }
  }
}
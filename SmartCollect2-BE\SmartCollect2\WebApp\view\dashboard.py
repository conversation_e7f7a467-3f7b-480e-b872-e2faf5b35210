import logging
import time
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from WebApp.view.common_filters import get_common_filters
from DB.db_manager import db_manager
import json

logger = logging.getLogger(__name__)

class DashboardView(APIView):
    permission_classes = (IsAuthenticated,)

    def getdata(self, from_date, to_date, branchls, BankMstID, dpdfrom, dpdto, fo_id):
        print(from_date, to_date, "***********************")
        print(branchls)
        
        try:
            # Format branchls for PostgreSQL array format
            if isinstance(branchls, list):
                branch_array = ','.join(map(str, branchls))
            else:
                branch_array = str(branchls) if branchls else ''
            
            # Construct the query with proper parameter binding
            query = """
                SELECT * FROM get_communication_summaryv2(
                    :from_date, 
                    :to_date, 
                    ARRAY[{}]::integer[], 
                    :bank_mst_id, 
                    :dpd_from, 
                    :dpd_to, 
                    :fo_id
                );
            """.format(branch_array)
            
            # Parameters for the query
            params = {
                'from_date': from_date,
                'to_date': to_date,
                'bank_mst_id': BankMstID,
                'dpd_from': dpdfrom,
                'dpd_to': dpdto,
                'fo_id': fo_id
            }
            
            print(f"Executing query with params: {params}")
            
            # Execute query using db_manager with retry logic
            rows = db_manager.execute_query_with_retry(query, params)
            
            if rows and len(rows) > 0:
                # Assuming the stored procedure returns JSON data in the first column
                data = rows[0][0]
                
                # Handle if data is a string that needs to be parsed as JSON
                if isinstance(data, str):
                    try:
                        data = json.loads(data)
                    except json.JSONDecodeError:
                        # If it's not JSON, wrap it in a dict
                        data = {"result": data}
                elif not isinstance(data, dict):
                    # If it's not a dict, wrap it
                    data = {"result": data}
                
                # Add metadata
                data.update({
                    "from_date": str(from_date), 
                    "to_date": str(to_date),
                    "message": "Data fetched successfully!", 
                    "status": "success"
                })
                
                return Response(data)
            else:
                return Response({
                    "message": "No data found for the given criteria",
                    "status": "success",
                    "from_date": str(from_date),
                    "to_date": str(to_date),
                    "data": {}
                })
                
        except Exception as e:
            print(f"Error occurred in getdata: {e}")
            
            # Log pool status for debugging
            pool_status = db_manager.get_detailed_pool_status()
            if pool_status:
                print(f"Database pool status: {pool_status}")
            
            return Response(
                {
                    "message": "An error occurred while fetching data!",
                    "error": str(e),
                    "status": "error",
                    "branchls": branchls,
                    "from_date": str(from_date),
                    "to_date": str(to_date)
                }
            )

    def get(self, request):
        try:
            from_date, to_date, branchls = get_common_filters(request.user)
            BankMstID = request.user.BankMstID_id
            
            if request.user.Designation == "FO":
                fo_id = request.user.FO_id
                print(f"FO user detected, fo_id: {fo_id}")
            else:
                fo_id = 0
                
            dpdfrom = -1
            dpdto = -1
            
            return self.getdata(
                from_date, to_date, branchls, BankMstID, dpdfrom, dpdto, fo_id
            )
            
        except Exception as e:
            print(f"Error in GET request: {e}")
            return Response({
                "message": "Error processing GET request",
                "error": str(e),
                "status": "error"
            })

    def post(self, request):
        try:
            from_date, to_date, branchls = get_common_filters(request.user, request)
            print(from_date, to_date, branchls, "POST DATA")
            
            BankMstID = request.user.BankMstID_id
            dpdfrom = request.data.get("dpdfrom", -1)  # Default to -1 if not provided
            dpdto = request.data.get("dpdto", -1)      # Default to -1 if not provided
            
            if request.user.Designation == "FO":
                fo_id = request.user.FO_id
                print(f"FO user detected, fo_id: {fo_id}")
            else:
                fo_id = 0
                
            print(branchls, "BNNNNNNNNNNNNNNNNNNNNNN")
            
            return self.getdata(
                from_date, to_date, branchls, BankMstID, dpdfrom, dpdto, fo_id
            )
            
        except Exception as e:
            print(f"Error in POST request: {e}")
            return Response({
                "message": "Error processing POST request",
                "error": str(e),
                "status": "error"
            })
import logging
import time
import json
import os
from django.utils import timezone
from django.contrib.auth.hashers import make_password
from django.db import connection, transaction
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from rest_framework.response import Response
from DB.db_manager import db_manager

engine = db_manager.get_engine()

class GeographyView(APIView):
    permission_classes = [IsAuthenticated,]
    
    def get(self, request):
        """Fetch all geography data with bank details"""
        try:
            with connection.cursor() as dbconnection:
                dbconnection.execute("""
                    SELECT g.*, b."BankName", b."SubscriptionType", b."IsActive" as "BankIsActive"
                    FROM public."GeographyMst" g
                    LEFT JOIN public."BankMst" b ON g."BankMstID" = b."BankMstID"
                    ORDER BY g."BUCode" ASC;                      
                """)
                data = dbconnection.fetchall()
                columns = [col[0] for col in dbconnection.description]
                geography_data = [dict(zip(columns, row)) for row in data]
                return Response(
                    {
                        "message": "Geography data fetched successfully!",
                        "status": "success",
                        "geography_data": geography_data,
                    }
                )
        except Exception as e:
            logging.error(f"Error occurred in GeographyView GET: {e}")
            return Response(
                {"message": "An error occurred!", "error": str(e), "status": "error"}
            )

    def post(self, request):
        """Create new geography entry with user and bank details"""
        # Extract data from request
        # Geography fields
        bu_code = request.data.get("bu_code")
        bu_name = request.data.get("bu_name")
        bu_type = request.data.get("bu_type")
        reporting_bu_id = request.data.get("reporting_bu_id")
        reporting_bu_type = request.data.get("reporting_bu_type")
        bu_mobile = request.data.get("bu_mobile")
        bu_email = request.data.get("bu_email")
        geography_is_active = request.data.get("geography_is_active", True)
        
        # Bank fields
        bank_name = request.data.get("bank_name")
        username = request.data.get("username")
        bank_status = request.data.get("bank_status")
        email = request.data.get("email")
        first_name = request.data.get("first_name")
        last_name = request.data.get("last_name")
        subscription_type = request.data.get("subscription_type")
        mobile_number = request.data.get("mobile_number")
        comm_channel = request.data.get("comm_channel", [])
        remark = request.data.get("remark", "")
        components = request.data.get("components", [])
        claim_period = request.data.get("claim_period")
        
        # Branch fields (optional)
        branch_code = request.data.get("branch_code")
        branch_name = request.data.get("branch_name")
        lngmstid = request.data.get("lngmstid")  # Get lngmstid from user input
        
        # User password field
        user_password = request.data.get("password")
        
        # Validation: Check if password is provided
        if not user_password:
            return Response(
                {
                    "message": "Password is required for user creation!",
                    "status": "error",
                },
                status=400,
            )
        
        # Validation: Check if lngmstid is provided when branch details are provided
        if (branch_code or branch_name) and not lngmstid:
            return Response(
                {
                    "message": "lngmstid is required when creating branch!",
                    "status": "error",
                },
                status=400,
            )
        
        current_date = timezone.now().date()
        
        try:
            with transaction.atomic():  # This ensures rollback on any failure
                with connection.cursor() as dbconnection:
                    # Handle subscription type - subscription_type is expected to be an integer ID
                    subscription_ID = subscription_type

                    # Prepare bank data
                    bank_data = {
                        "OnBoardingDate": current_date,
                        "IsActive": True if bank_status == "Active" else False,
                        "UpdatedDate": current_date,
                        "Remarks": remark,
                        "SubscriptionID": subscription_ID,
                        "SubscriptionType": subscription_type,  # Keep original value
                        "BankName": bank_name,
                        "claim_period": claim_period,
                        **{
                            channel: channel in comm_channel
                            for channel in ["WhatsApp", "VoiceBot", "Blaster", "IVR", "SMS"]
                        },
                    }

                    # Insert bank data first
                    bank_columns = ", ".join([f'"{key}"' for key in bank_data.keys()])
                    bank_values = [bank_data[key] for key in bank_data.keys()]
                    bank_placeholders = ", ".join(["%s"] * len(bank_values))
                    
                    dbconnection.execute(
                        f"""
                        INSERT INTO public."BankMst" ({bank_columns})
                        VALUES ({bank_placeholders})
                        RETURNING "BankMstID";
                        """,
                        bank_values,
                    )
                    bank_result = dbconnection.fetchone()
                    bank_id = bank_result[0] if bank_result else None

                    if not bank_id:
                        raise Exception("Failed to create bank record")

                    # Create branch record if branch details are provided
                    branch_mst_id = None
                    if branch_code and branch_name and lngmstid:
                        branch_data = {
                            "BankMstID": bank_id,
                            "BranchCode": branch_code,
                            "BranchName": branch_name,
                            "IsActive": True,
                            "CreatedDate": current_date,
                            "UpdatedDate": current_date,
                            "LngMstID": lngmstid  # Use lngmstid from user input
                        }
                        
                        branch_columns = ", ".join([f'"{key}"' for key in branch_data.keys()])
                        branch_values = [branch_data[key] for key in branch_data.keys()]
                        branch_placeholders = ", ".join(["%s"] * len(branch_values))
                        
                        try:
                            dbconnection.execute(
                                f"""
                                INSERT INTO public."BranchMst" ({branch_columns})
                                VALUES ({branch_placeholders})
                                RETURNING "BranchMstID";
                                """,
                                branch_values,
                            )
                            branch_result = dbconnection.fetchone()
                            branch_mst_id = branch_result[0] if branch_result else None
                            
                            if not branch_mst_id:
                                raise Exception("Failed to create branch record - no ID returned")
                                
                            logging.info(f"Branch created successfully with ID: {branch_mst_id}")
                            
                        except Exception as branch_error:
                            logging.error(f"Error creating branch: {branch_error}")
                            raise Exception(f"Failed to create branch record: {str(branch_error)}")

                    # Prepare geography data
                    geography_data = {
                        "BUCode": bu_code,
                        "BUName": bu_name,
                        "BUType": bu_type,
                        "ReportingBUID": reporting_bu_id,
                        "ReportingBUType": reporting_bu_type,
                        "BUMobile": bu_mobile,
                        "BUEmail": bu_email,
                        "IsActive": geography_is_active,
                        "CreatedDate": current_date,
                        "BankMstID": bank_id,  # Auto-assigned from bank creation
                    }

                    # Insert geography data
                    geography_columns = ", ".join([f'"{key}"' for key in geography_data.keys()])
                    geography_values = [geography_data[key] for key in geography_data.keys()]
                    geography_placeholders = ", ".join(["%s"] * len(geography_values))
                    
                    dbconnection.execute(
                        f"""
                        INSERT INTO public."GeographyMst" ({geography_columns})
                        VALUES ({geography_placeholders})
                        RETURNING "BUCode";
                        """,
                        geography_values,
                    )
                    geography_result = dbconnection.fetchone()
                    geography_bu_code = geography_result[0] if geography_result else None

                    if not geography_bu_code:
                        raise Exception("Failed to create geography record")

                    # Prepare user data - FIXED: Added BUCode to user data
                    user_data = {
                        "password": make_password(user_password),  # Use the password from user input
                        "is_superuser": True,
                        "username": username,
                        "first_name": first_name,
                        "last_name": last_name,
                        "email": email,
                        "MobileNumber": mobile_number,
                        "is_staff": True,
                        "is_active": True,
                        "is_admin": False,
                        "BankMaster": False,
                        "UpdatedDate": current_date,
                        "date_joined": current_date,
                        "BUcode": bu_code,  # FIXED: Added BUCode field
                        "BranchID": request.user.BranchID
                        if hasattr(request.user, "BranchID")
                        else None,
                        "BankMstID_id": bank_id,  # Link to the newly created bank
                        "BranchMstID_id": branch_mst_id,  # FIXED: Use created branch ID or None
                        "FO_id": request.user.FO_id if hasattr(request.user, "FO_id") else None,
                        "Designation": request.user.Designation
                        if hasattr(request.user, "Designation")
                        else None,
                    }

                    # Insert user data - no need to return user ID, just insert
                    user_columns = ", ".join([f'"{key}"' for key in user_data.keys()])
                    user_values = [user_data[key] for key in user_data.keys()]
                    user_placeholders = ", ".join(["%s"] * len(user_values))

                    try:
                        dbconnection.execute(
                            f"""
                            INSERT INTO public."UserMst" ({user_columns})
                            VALUES ({user_placeholders});
                            """,
                            user_values,
                        )
                        logging.info("User created successfully")
                    except Exception as user_error:
                        logging.error(f"Error creating user: {user_error}")
                        raise Exception(f"Failed to create user record: {str(user_error)}")
                    
                    # Since we're returning bank_id instead, no need to fetch user result
                    user_id = bank_id  # Return bank_id as user_id for consistency

                    return Response(
                        {
                            "message": "Geography entry with Bank, Branch (if provided), and User details created successfully!",
                            "status": "success",
                            "bank_id": bank_id,
                            "branch_id": branch_mst_id,
                            "geography_bu_code": geography_bu_code,
                            "user_id": user_id,
                        },
                        status=201,
                    )

        except Exception as e:
            logging.error(f"Error occurred in GeographyView POST: {e}")
            return Response(
                {
                    "message": "Failed to create geography entry. All changes rolled back.",
                    "error": str(e),
                    "status": "error",
                },
                status=500,
            )


class BankDetails(APIView):
    """Simplified BankDetails class - functionality moved to GeographyView"""
    permission_classes = (IsAuthenticated,)

    def get(self, request, BankMstID=None):
        """Get specific bank details by ID"""
        try:
            with connection.cursor() as dbconnection:
                if BankMstID:
                    dbconnection.execute("""
                        SELECT * FROM public."BankMst"
                        WHERE "BankMstID" = %s;                      
                    """, [BankMstID])
                else:
                    dbconnection.execute("""
                        SELECT * FROM public."BankMst"
                        ORDER BY "BankMstID" ASC;                      
                    """)
                
                data = dbconnection.fetchall()
                columns = [col[0] for col in dbconnection.description]
                bank_details = [dict(zip(columns, row)) for row in data]
                
                return Response(
                    {
                        "message": "Bank details fetched successfully!",
                        "status": "success",
                        "bank_details": bank_details,
                    }
                )
        except Exception as e:
            logging.error(f"Error occurred in BankDetails GET: {e}")
            return Response(
                {"message": "An error occurred!", "error": str(e), "status": "error"}
            )
import pandas as pd
from WebApp.ETL_for_banks.utils import clean_column,process_branch,clean_disbursement_id,convert_excel_serial_date
# Load Artha sidhi data and clean data
def process_arhta_sidhi_collection_data(coll_df):
    # Load CSV file

    # Clean and map columns
    coll_df['CustomerID'] = coll_df['Member No'].apply(clean_column)
    coll_df['DisbursementID'] = coll_df['Loan No'].apply(clean_disbursement_id)
    coll_df['CustomerName'] = coll_df['Member'].apply(clean_column)
    coll_df['LoanType'] = coll_df['Loan Type'].apply(clean_column)
    coll_df['DisbursementAmt'] = coll_df['Loan Amount']
    coll_df['EMIAmount'] = coll_df['EMI Amount']
    coll_df['DisbursementDate'] = pd.to_datetime(coll_df['Loan Date'], errors='coerce')
    coll_df['CollectedAmount'] = coll_df['Collection Amount']
    coll_df['POS'] = coll_df['POS']
    coll_df['CollectionDate'] = pd.to_datetime(coll_df['Received Date'], errors='coerce')
    coll_df['PaymentFrequency'] = coll_df['Repayment Frequency']
    coll_df['PrincipleCollected'] = coll_df['Principle Collection Amount']
    coll_df['InterestCollected'] = coll_df['Interest Collection Amount']
    coll_df['TransactionNumber'] = coll_df['Receipt No']
    
    # Process branch information
    coll_df['BranchCode'], coll_df['BranchName'] = zip(*coll_df['Branch Name'].apply(process_branch))
    return coll_df

def process_artha_sidhi_demand_data(demand_df):
    # Map columns from the demand file to our database columns
    demand_df['CustomerID'] = demand_df['Member No']
    demand_df['DisbursementID'] = demand_df['Loan No'].apply(clean_disbursement_id)
    demand_df['CustomerName'] = demand_df['Member']
    demand_df['MobileNumber'] = demand_df['Mobile Number']
    demand_df['DisbursementDate'] = demand_df['Loan Date'].apply(convert_excel_serial_date)
    demand_df['DisbursementAmt'] = demand_df['Loan Amount']
    demand_df['LoanType'] = demand_df['Loan Scheme']
    demand_df['BranchName'] = demand_df['Branch']
    demand_df['NextEMIDate'] = demand_df['Demand Date'].apply(convert_excel_serial_date)
    if 'Savings Demand Amount' in demand_df.columns:
        demand_df['Savings Demand Amount'] = demand_df['Savings Demand Amount'].fillna(0)
    else:
        demand_df['Savings Demand Amount'] = 0
    demand_df['EMIAmount'] = demand_df.apply(
    lambda row: max(
        row.get('EMI Amount',0) + row.get('Savings Demand Amount', 0), 
        row.get('Total All Demand', 0)
    ),
    axis=1
)
    print(demand_df[['EMI Amount', 'Savings Demand Amount', 'Total All Demand']].isna().sum())
    return demand_df


def process_artha_sidhi_over_due_data(od_df):
    #Adding Clean Disursement_id as same code
    od_df['CustomerID'] = od_df['MemberNo'].apply(clean_disbursement_id)
    od_df['DisbursementID'] = od_df['LoanNo'].apply(clean_disbursement_id)
    od_df['MobileNumber'] = od_df['Mobile No.']
    od_df['CustomerName'] = od_df['MemberName']
    od_df['DPD'] = od_df['OdDays']
    od_df['DisbursementDate'] = od_df['LoanDt'].apply(convert_excel_serial_date)
    od_df['DisbursementAmt'] = od_df['LoanAmt']
    od_df['LoanType'] = od_df['Scheme']
    od_df['LastCollectedDate'] = od_df['LastCollDt'].apply(convert_excel_serial_date)
    od_df['LastCollectedAmount'] = od_df['LastCollAmt']
    od_df['OverDueAmt'] = od_df['ODAmount']
    od_df['EMIAmount'] = od_df['EMI Amount']
    
    # Process branch information
    od_df['BranchCode'], od_df['BranchName'] = zip(*od_df['Branch'].apply(process_branch))
    return od_df

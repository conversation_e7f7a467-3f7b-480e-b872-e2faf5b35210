# ===============================================
# Enhanced HierarchyDrillDown with Date Filter and Communication Metrics
# ===============================================

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db import connection
from typing import List, Dict, Optional
from datetime import datetime, date
import logging

logger = logging.getLogger(__name__)



class HierarchyDrillDownView(APIView):
    """
    Enhanced Hierarchy Drill-Down API with Communication Metrics, Date Filtering, and Date Column
    GET /api/hierarchy/ - Top level with communication stats
    GET /api/hierarchy/?parent_code=BU12HO&from_date=2024-01-01&to_date=2024-12-31 - Children of parent with communication stats and date filter
    POST /api/hierarchy/ - With date filters in request body
    """
    permission_classes = (IsAuthenticated,)

    def parse_date(self, date_str: str) -> Optional[str]:
        """Parse date string to ensure proper format"""
        if not date_str:
            return None
            
        try:
            # Try different date formats
            for fmt in ['%Y-%m-%d', '%d-%m-%Y', '%m/%d/%Y', '%Y/%m/%d']:
                try:
                    parsed_date = datetime.strptime(date_str, fmt)
                    return parsed_date.strftime('%Y-%m-%d')
                except ValueError:
                    continue
            
            # If no format worked, return None
            logger.warning(f"Unable to parse date: {date_str}")
            return None
            
        except Exception as e:
            logger.error(f"Error parsing date {date_str}: {str(e)}")
            return None

    def get_date_filter_condition(self, from_date: str = None, to_date: str = None) -> tuple:
        """Get date filter condition and parameters for SQL query using BETWEEN"""
        if from_date and to_date:
            parsed_from = self.parse_date(from_date)
            parsed_to = self.parse_date(to_date)
            if parsed_from and parsed_to:
                return ('AND cq."CreatedDate" BETWEEN %s AND %s', [parsed_from, parsed_to])
        elif from_date:
            parsed_from = self.parse_date(from_date)
            if parsed_from:
                return ('AND cq."CreatedDate" >= %s', [parsed_from])
        elif to_date:
            parsed_to = self.parse_date(to_date)
            if parsed_to:
                return ('AND cq."CreatedDate" <= %s', [parsed_to])
        
        return ("", [])

    def get_hierarchy_levels(self, bank_id: int) -> Dict[str, str]:
        """Get the hierarchy levels dynamically from actual hierarchy structure"""
        
        # First, find the root node (node with no parent or parent not in system)
        root_query = """
            WITH root_candidates AS (
                SELECT "BUCode", "BUName", "BUType", "ReportingBUID"
                FROM "GeographyMst" 
                WHERE "BankMstID" = %s AND "IsActive" = true
                AND ("ReportingBUID" IS NULL 
                     OR "ReportingBUID" NOT IN (
                         SELECT "BUCode" FROM "GeographyMst" 
                         WHERE "BankMstID" = %s AND "IsActive" = true
                     ))
            )
            SELECT "BUCode", "BUType" FROM root_candidates LIMIT 1
        """
        
        # Then build the hierarchy dynamically
        hierarchy_query = """
            WITH RECURSIVE hierarchy_levels AS (
                -- Start from root
                SELECT 
                    "BUCode", "BUName", "BUType", "ReportingBUID", 
                    0 as level,
                    "BUType" as level_type
                FROM "GeographyMst"
                WHERE "BUCode" = %s AND "BankMstID" = %s AND "IsActive" = true
                
                UNION ALL
                
                -- Recursively get children
                SELECT 
                    child."BUCode", child."BUName", child."BUType", child."ReportingBUID",
                    h.level + 1,
                    child."BUType"
                FROM hierarchy_levels h
                INNER JOIN "GeographyMst" child ON child."ReportingBUID" = h."BUCode"
                WHERE child."BankMstID" = %s AND child."IsActive" = true
            )
            SELECT DISTINCT level, level_type
            FROM hierarchy_levels
            ORDER BY level
        """
        
        try:
            with connection.cursor() as cursor:
                # Get root node
                cursor.execute(root_query, [bank_id, bank_id])
                root_result = cursor.fetchone()
                
                if not root_result:
                    # Fallback: just get any node and use it
                    fallback_query = """
                        SELECT "BUCode", "BUType" FROM "GeographyMst" 
                        WHERE "BankMstID" = %s AND "IsActive" = true LIMIT 1
                    """
                    cursor.execute(fallback_query, [bank_id])
                    root_result = cursor.fetchone()
                
                if not root_result:
                    return {'current': 'Level0', 'next': 'Level1', 'after_next': 'Level2'}
                
                root_code = root_result[0]
                
                # Get hierarchy levels
                cursor.execute(hierarchy_query, [root_code, bank_id, bank_id])
                level_results = cursor.fetchall()
                
            # Build level mapping
            levels = [row[1] for row in level_results]  # row[1] is level_type
            
            return {
                'levels': levels,
                'current': levels[0] if levels else 'Level0',
                'next': levels[1] if len(levels) > 1 else 'Level1', 
                'after_next': levels[2] if len(levels) > 2 else 'Level2',
                'root_code': root_code
            }
            
        except Exception as e:
            logger.error(f"Error getting hierarchy levels: {str(e)}")
            return {'current': 'Level0', 'next': 'Level1', 'after_next': 'Level2'}

    def get_hierarchy_path(self, node_code: str, bank_id: int) -> List[Dict]:
        """Get the complete hierarchy path from root to given node"""
        path_query = """
            WITH RECURSIVE hierarchy_path AS (
                -- Start from the given node
                SELECT 
                    "BUCode", "BUName", "BUType", "ReportingBUID", 
                    0 as distance_from_target,
                    ARRAY["BUCode"::text] as path_codes,
                    ARRAY["BUName"::text] as path_names,
                    ARRAY["BUType"::text] as path_types
                FROM "GeographyMst"
                WHERE "BUCode" = %s AND "BankMstID" = %s AND "IsActive" = true
                
                UNION ALL
                
                -- Go up to parent
                SELECT 
                    parent."BUCode", parent."BUName", parent."BUType", parent."ReportingBUID",
                    h.distance_from_target + 1,
                    ARRAY[parent."BUCode"::text] || h.path_codes,
                    ARRAY[parent."BUName"::text] || h.path_names,
                    ARRAY[parent."BUType"::text] || h.path_types
                FROM hierarchy_path h
                INNER JOIN "GeographyMst" parent ON parent."BUCode" = h."ReportingBUID"
                WHERE parent."BankMstID" = %s AND parent."IsActive" = true
            )
            SELECT path_codes, path_names, path_types
            FROM hierarchy_path
            ORDER BY distance_from_target DESC
            LIMIT 1
        """
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(path_query, [node_code, bank_id, bank_id])
                result = cursor.fetchone()
                
                if result:
                    codes, names, types = result
                    path = []
                    for i, (code, name, type_) in enumerate(zip(codes, names, types)):
                        path.append({
                            'level': i,
                            'code': code,
                            'name': name,
                            'type': type_
                        })
                    return path
                
                return []
                
        except Exception as e:
            logger.error(f"Error getting hierarchy path: {str(e)}")
            return []

    def get_communication_stats(self, bu_code: str, bank_id: int, from_date: str = None, to_date: str = None) -> Dict:
        """Get communication statistics for a hierarchy node with date filtering"""
        
        # Build date filter condition using BETWEEN
        date_condition = ""
        date_params = []
        
        if from_date and to_date:
            parsed_from = self.parse_date(from_date)
            parsed_to = self.parse_date(to_date)
            if parsed_from and parsed_to:
                date_condition = 'AND cq."CreatedDate" BETWEEN %s AND %s'
                date_params = [parsed_from, parsed_to]
        elif from_date:
            parsed_from = self.parse_date(from_date)
            if parsed_from:
                date_condition = 'AND cq."CreatedDate" >= %s'
                date_params = [parsed_from]
        elif to_date:
            parsed_to = self.parse_date(to_date)
            if parsed_to:
                date_condition = 'AND cq."CreatedDate" <= %s'
                date_params = [parsed_to]
        
        comm_stats_query = f"""
            WITH branch_ids AS (
                SELECT "BranchMstID" 
                FROM hierarchy_based_access_control_system(%s, %s)
            ),
            communication_stats AS (
                SELECT 
                    COUNT(CASE WHEN "IsWhatsApp" = true THEN 1 END) as whatsapp_count,
                    COUNT(CASE WHEN "IsBlaster" = true THEN 1 END) as blaster_count,
                    COUNT(CASE WHEN "IsVoiceBot" = true THEN 1 END) as voicebot_count,
                    COUNT(CASE WHEN "IsSMS" = true THEN 1 END) as sms_count,
                    COUNT(CASE WHEN "IsIVR" = true THEN 1 END) as ivr_count,
                    COUNT(*) as total_communications
                FROM "CommunicationQueue" cq
                INNER JOIN branch_ids bi ON cq."BranchMstID" = bi."BranchMstID"
                WHERE cq."BankMstID" = %s
                {date_condition}
            )
            SELECT * FROM communication_stats
        """
        
        # Build parameters list
        query_params = [bu_code, bank_id, bank_id] + date_params
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(comm_stats_query, query_params)
                result = cursor.fetchone()
                
                if result:
                    return {
                        'whatsapp_count': result[0] or 0,
                        'blaster_count': result[1] or 0,
                        'voicebot_count': result[2] or 0,
                        'sms_count': result[3] or 0,
                        'ivr_count': result[4] or 0,
                        'total_communications': result[5] or 0,
                        'date_range_applied': bool(from_date or to_date),
                        'from_date_filter': from_date,
                        'to_date_filter': to_date
                    }
                
                return {
                    'whatsapp_count': 0,
                    'blaster_count': 0,
                    'voicebot_count': 0,
                    'sms_count': 0,
                    'ivr_count': 0,
                    'total_communications': 0,
                    'date_range_applied': bool(from_date or to_date),
                    'from_date_filter': from_date,
                    'to_date_filter': to_date
                }
                
        except Exception as e:
            logger.error(f"Error getting communication stats: {str(e)}")
            return {
                'whatsapp_count': 0,
                'blaster_count': 0,
                'voicebot_count': 0,
                'sms_count': 0,
                'ivr_count': 0,
                'total_communications': 0,
                'date_range_applied': bool(from_date or to_date),
                'from_date_filter': from_date,
                'to_date_filter': to_date,
                'error': str(e)
            }

    def get_datewise_communication_data(self, bu_code: str, bank_id: int, from_date: str = None, to_date: str = None) -> List[Dict]:
        """Get datewise communication data for a hierarchy node similar to DaywiseCommunicationCountsView"""
        
        # Build date filter condition
        date_condition = ""
        date_params = []
        
        if from_date and to_date:
            parsed_from = self.parse_date(from_date)
            parsed_to = self.parse_date(to_date)
            if parsed_from and parsed_to:
                date_condition = 'AND DATE(cq."CreatedDate") BETWEEN %s AND %s'
                date_params = [parsed_from, parsed_to]
        elif from_date:
            parsed_from = self.parse_date(from_date)
            if parsed_from:
                date_condition = 'AND DATE(cq."CreatedDate") >= %s'
                date_params = [parsed_from]
        elif to_date:
            parsed_to = self.parse_date(to_date)
            if parsed_to:
                date_condition = 'AND DATE(cq."CreatedDate") <= %s'
                date_params = [parsed_to]
        else:
            # If no date filter, get last 30 days by default
            date_condition = 'AND cq."CreatedDate" >= CURRENT_DATE - INTERVAL \'30 days\''
        
        datewise_query = f"""
            WITH branch_ids AS (
                SELECT "BranchMstID" 
                FROM hierarchy_based_access_control_system(%s, %s)
            ),
            datewise_stats AS (
                SELECT 
                    DATE(cq."CreatedDate") as communication_date,
                    COUNT(CASE WHEN "IsWhatsApp" = true THEN 1 END) as whatsapp_count,
                    COUNT(CASE WHEN "IsBlaster" = true THEN 1 END) as blaster_count,
                    COUNT(CASE WHEN "IsVoiceBot" = true THEN 1 END) as voicebot_count,
                    COUNT(CASE WHEN "IsSMS" = true THEN 1 END) as sms_count,
                    COUNT(CASE WHEN "IsIVR" = true THEN 1 END) as ivr_count,
                    COUNT(*) as total_communications
                FROM "CommunicationQueue" cq
                INNER JOIN branch_ids bi ON cq."BranchMstID" = bi."BranchMstID"
                WHERE cq."BankMstID" = %s
                {date_condition}
                GROUP BY DATE(cq."CreatedDate")
                ORDER BY communication_date DESC
            )
            SELECT * FROM datewise_stats
        """
        
        # Build parameters list
        query_params = [bu_code, bank_id, bank_id] + date_params
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(datewise_query, query_params)
                results = cursor.fetchall()
                
                datewise_data = []
                for row in results:
                    datewise_data.append({
                        'date': row[0].strftime('%Y-%m-%d') if row[0] else None,
                        'whatsapp_count': row[1] or 0,
                        'blaster_count': row[2] or 0,
                        'voicebot_count': row[3] or 0,
                        'sms_count': row[4] or 0,
                        'ivr_count': row[5] or 0,
                        'total_communications': row[6] or 0
                    })
                
                return datewise_data
                
        except Exception as e:
            logger.error(f"Error getting datewise communication data: {str(e)}")
            return []

    def get_top_level_data(self, bank_id: int, from_date: str = None, to_date: str = None):
        """Get top level hierarchy with communication metrics and date filtering"""
        try:
            # Get hierarchy levels dynamically
            hierarchy_info = self.get_hierarchy_levels(bank_id)
            levels = hierarchy_info['levels']
            root_code = hierarchy_info['root_code']
            
            query = """
                WITH RECURSIVE hierarchy AS (
                    SELECT
                        g."BUCode", g."BUName", g."BUType", g."ReportingBUID", 0 as level
                    FROM "GeographyMst" g
                    WHERE g."BUCode" = %s 
                    AND g."BankMstID" = %s 
                    AND g."IsActive" = true
                    
                    UNION ALL
                    
                    SELECT
                        child."BUCode", child."BUName", child."BUType", 
                        child."ReportingBUID", h.level + 1
                    FROM hierarchy h
                    INNER JOIN "GeographyMst" child ON child."ReportingBUID" = h."BUCode"
                    WHERE child."BankMstID" = %s 
                    AND child."IsActive" = true
                )
                SELECT 
                    h0."BUCode",
                    h0."BUName",
                    h0."BUType",
                    COUNT(DISTINCT h1."BUCode") as next_level_count,
                    COUNT(DISTINCT h2."BUCode") as after_next_count
                FROM hierarchy h0
                LEFT JOIN hierarchy h1 ON h1.level = 1 AND h1."ReportingBUID" = h0."BUCode"
                LEFT JOIN hierarchy h2 ON h2.level >= 2
                WHERE h0.level = 0
                GROUP BY h0."BUCode", h0."BUName", h0."BUType"
            """
            
            with connection.cursor() as cursor:
                cursor.execute(query, [root_code, bank_id, bank_id])
                result = cursor.fetchone()
            
            if result:
                current_level = levels[0] if levels else 'Level0'
                next_level = levels[1] if len(levels) > 1 else 'Level1'
                after_next_level = levels[2] if len(levels) > 2 else 'Level2'
                
                # Get communication stats for the root node with date filtering
                comm_stats = self.get_communication_stats(root_code, bank_id, from_date, to_date)
                
                # Get datewise communication data
                datewise_data = self.get_datewise_communication_data(root_code, bank_id, from_date, to_date)
                
                # Dynamic column structure with communication metrics
                data = {
                    f"{current_level}Code": result[0],
                    f"{current_level}Name": result[1], 
                    f"{current_level}Type": result[2],
                    f"{next_level}Count": result[3],
                    f"{after_next_level}Count": result[4],
                    'drill_down_available': result[3] > 0,
                    # Communication metrics
                    'total_communications': comm_stats['total_communications'],
                    'whatsapp_count': comm_stats['whatsapp_count'],
                    'blaster_count': comm_stats['blaster_count'],
                    'voicebot_count': comm_stats['voicebot_count'],
                    'sms_count': comm_stats['sms_count'],
                    'ivr_count': comm_stats['ivr_count']
                }
                
                # Column headers for frontend with communication columns
                columns = {
                    'col1': f"{current_level}Code",
                    'col2': f"{current_level}Name",
                    'col3': f"{next_level}Count", 
                    'col4': f"{after_next_level}Count",
                    'col5': 'TotalCommunications',
                    'col6': 'WhatsAppCount',
                    'col7': 'BlasterCount',
                    'col8': 'VoiceBotCount',
                    'col9': 'SMSCount',
                    'col10': 'IVRCount'
                }
                
                return {
                    'level_type': 'top',
                    'current_level': current_level,
                    'hierarchy_levels': levels,
                    'columns': columns,
                    'data': [data],  # Always return as list for consistency
                    'communication_summary': comm_stats,
                    'datewise_data': datewise_data,  # New: datewise breakdown
                    'date_filters': {
                        'from_date': from_date,
                        'to_date': to_date,
                        'date_range_applied': bool(from_date or to_date)
                    }
                }
            
            return {'data': [], 'message': 'No data found'}
            
        except Exception as e:
            logger.error(f"Error in get_top_level_data: {str(e)}")
            raise

    def get_children_data(self, parent_code: str, bank_id: int, from_date: str = None, to_date: str = None):
        """Get children of parent with communication statistics and date filtering"""
        try:
            # Get the hierarchy path to the parent
            parent_path = self.get_hierarchy_path(parent_code, bank_id)
            
            if not parent_path:
                return {'error': 'Parent code not found', 'status': 404}

            # Get all children with their communication stats
            children_query = """
                WITH children AS (
                    SELECT 
                        g."BUCode",
                        g."BUName", 
                        g."BUType",
                        (
                            SELECT COUNT(*) FROM "GeographyMst" 
                            WHERE "ReportingBUID" = g."BUCode" 
                            AND "BankMstID" = %s 
                            AND "IsActive" = true
                        ) as child_count
                    FROM "GeographyMst" g
                    WHERE g."ReportingBUID" = %s 
                    AND g."BankMstID" = %s 
                    AND g."IsActive" = true
                ),
                child_types_summary AS (
                    SELECT jsonb_object_agg(child_type, child_count) as summary
                    FROM (
                        SELECT "BUType" as child_type, COUNT(*) as child_count
                        FROM children
                        GROUP BY "BUType"
                    ) t
                )
                SELECT 
                    c.*,
                    cts.summary as child_types_summary
                FROM children c
                CROSS JOIN child_types_summary cts
                ORDER BY c."BUCode"
            """
            
            with connection.cursor() as cursor:
                cursor.execute(children_query, [bank_id, parent_code, bank_id])
                children_results = cursor.fetchall()

            if not children_results:
                return {
                    'level_type': 'children',
                    'parent_path': parent_path,
                    'data': [],
                    'datewise_data': [],
                    'message': 'No children found'
                }

            # Process children data with communication stats
            children_data = []
            child_types = set()
            total_comm_summary = {
                'total_communications': 0,
                'whatsapp_count': 0,
                'blaster_count': 0,
                'voicebot_count': 0,
                'sms_count': 0,
                'ivr_count': 0
            }
            
            # Collect all datewise data
            all_datewise_data = []
            
            for row in children_results:
                child_code = row[0]
                child_name = row[1]
                child_type = row[2]
                
                # Get communication stats for this child with date filtering
                comm_stats = self.get_communication_stats(child_code, bank_id, from_date, to_date)
                
                # Get datewise data for this child
                child_datewise_data = self.get_datewise_communication_data(child_code, bank_id, from_date, to_date)
                
                # Add child info to each datewise record using actual child type
                for date_record in child_datewise_data:
                    # Use actual child type for field names instead of generic names
                    date_record[f'{child_type}_code'] = child_code
                    date_record[f'{child_type}_name'] = child_name
                    date_record[f'{child_type}_type'] = child_type
                    
                    # Add parent hierarchy info
                    for level_info in parent_path:
                        date_record[f"{level_info['type']}_code"] = level_info['code']
                        date_record[f"{level_info['type']}_name"] = level_info['name']
                    
                all_datewise_data.extend(child_datewise_data)
                
                # Build data with parent hierarchy path
                child_data = {
                    'code': child_code,
                    'name': child_name,
                    'type': child_type,
                    f'{child_type}_childcount': row[3],
                    'drill_down_available': row[3] > 0
                }
            
                
                # Add parent hierarchy data to each row
                for level_info in parent_path:
                    child_data[f"{level_info['type']}Code"] = level_info['code']
                    child_data[f"{level_info['type']}Name"] = level_info['name']
                
                # Add current child level data using actual child type
                child_data[f'{child_type}Code'] = child_code
                child_data[f'{child_type}Name'] = child_name
                child_data[f'{child_type}Type'] = child_type
                child_data[f'{child_type}Count'] = row[3]
                
                # Add communication metrics
                child_data.update({
                    'total_communications': comm_stats['total_communications'],
                    'whatsapp_count': comm_stats['whatsapp_count'],
                    'blaster_count': comm_stats['blaster_count'],
                    'voicebot_count': comm_stats['voicebot_count'],
                    'sms_count': comm_stats['sms_count'],
                    'ivr_count': comm_stats['ivr_count']
                })
                
                # Aggregate totals
                for key in ['total_communications', 'whatsapp_count', 'blaster_count', 
                        'voicebot_count', 'sms_count', 'ivr_count']:
                    total_comm_summary[key] += comm_stats[key]
                
                children_data.append(child_data)
                child_types.add(child_type)

            # Sort datewise data by date descending
            all_datewise_data.sort(key=lambda x: x['date'], reverse=True)

            # Build dynamic columns based on actual hierarchy path + child types + communication
            columns = {}
            col_index = 1

            # Add all parent levels to columns
            for level_info in parent_path:
                columns[f'col{col_index}'] = f"{level_info['type']}Code"
                columns[f'col{col_index + 1}'] = f"{level_info['type']}Name"
                col_index += 2

            # Add current child level columns using actual child type names
            # Get the first child type (assuming all children are of same type in most cases)
            child_type_for_columns = next(iter(child_types)) if child_types else "Child"
            
            columns[f'col{col_index}'] = f"{child_type_for_columns}Code"
            columns[f'col{col_index + 1}'] = f"{child_type_for_columns}Name"
            columns[f'col{col_index + 2}'] = f"{child_type_for_columns}Type"
            columns[f'col{col_index + 3}'] = f"{child_type_for_columns}Count"
            col_index += 4
            
            # Add communication columns
            columns[f'col{col_index}'] = "TotalCommunications"
            columns[f'col{col_index + 1}'] = "WhatsAppCount"
            columns[f'col{col_index + 2}'] = "BlasterCount"
            columns[f'col{col_index + 3}'] = "VoiceBotCount"
            columns[f'col{col_index + 4}'] = "SMSCount"
            columns[f'col{col_index + 5}'] = "IVRCount"

            # Get child type distribution from first row
            child_types_summary = children_results[0][4] if children_results else {}

            return {
                'level_type': 'children',
                'parent_path': parent_path,
                'current_levels': list(child_types),
                'child_types_summary': child_types_summary,
                'hierarchy_depth': len(parent_path) + 1,
                'columns': columns,
                'data': children_data,
                'communication_summary': total_comm_summary,
                'datewise_data': all_datewise_data,  # New: datewise breakdown for all children
                'date_filters': {
                    'from_date': from_date,
                    'to_date': to_date,
                    'date_range_applied': bool(from_date or to_date)
                }
            }

        except Exception as e:
            logger.error(f"Error in get_children_data: {str(e)}")
            raise

    def get(self, request):
        """Handle GET requests for hierarchy data with communication metrics and date filtering"""
        try:
            bank_id = getattr(request.user, 'BankMstID', 13)  # Get from user or default
            parent_code = request.query_params.get('parent_code')
            from_date = request.query_params.get('from_date')
            to_date = request.query_params.get('to_date')
            
            if parent_code:
                # Get children of parent with communication stats and date filtering
                result = self.get_children_data(parent_code, bank_id, from_date, to_date)
                if 'error' in result:
                    return Response(result, status=result.get('status', 400))
            else:
                # Get top level with communication stats and date filtering
                result = self.get_top_level_data(bank_id, from_date, to_date)
            
            return Response(result)
            
        except Exception as e:
            logger.error(f"Error in HierarchyDrillDownView GET: {str(e)}")
            return Response({
                'error': 'An error occurred while fetching hierarchy data',
                'message': str(e)
            }, status=500)

    def post(self, request):
        """Handle POST requests for hierarchy data with filters, communication metrics, and date filtering"""
        try:
            # Get bank_id from request body or user object
            bank_id = request.data.get('bank_mst_id')
            if bank_id is None:
                bank_id = getattr(request.user, 'BankMstID', 13)
                if hasattr(bank_id, 'id'):  # If it's a model instance
                    bank_id = bank_id.id
            
            parent_code = request.data.get('parent_code')
            from_date = request.data.get('from_date')
            to_date = request.data.get('to_date')
            
            if parent_code:
                result = self.get_children_data(parent_code, bank_id, from_date, to_date)
                if 'error' in result:
                    return Response(result, status=result.get('status', 400))
            else:
                result = self.get_top_level_data(bank_id, from_date, to_date)
            
            return Response(result)
            
        except Exception as e:
            logger.error(f"Error in HierarchyDrillDownView POST: {str(e)}")
            return Response({
                'error': 'An error occurred while fetching hierarchy data',
                'message': str(e)
            }, status=500)
        




class DaywiseCommunicationCountsView(APIView):
    """
    Daywise Communication Counts API with Date Filtering
    POST /api/daywise-communication-counts/ - Get daywise counts with all parameters in request body
    
    Request Body Example:
    {
        "from_date": "2024-01-01",
        "to_date": "2024-12-31",
        "branch_ids": [453, 454, 455, 456, 457] // required - must provide branch IDs
    }
    """
    permission_classes = (IsAuthenticated,)

    def parse_date(self, date_str: str) -> Optional[str]:
        """Parse date string to ensure proper format"""
        if not date_str:
            return None
            
        try:
            # Try different date formats
            for fmt in ['%Y-%m-%d', '%d-%m-%Y', '%m/%d/%Y', '%Y/%m/%d']:
                try:
                    parsed_date = datetime.strptime(date_str, fmt)
                    return parsed_date.strftime('%Y-%m-%d')
                except ValueError:
                    continue
            
            # If no format worked, return None
            logger.warning(f"Unable to parse date: {date_str}")
            return None
            
        except Exception as e:
            logger.error(f"Error parsing date {date_str}: {str(e)}")
            return None



    def get_daywise_communication_counts(self, branches: List[int], from_date: str, to_date: str) -> Dict:
        """Get daywise communication counts using the stored procedure"""
        try:
            # Validate inputs
            if not branches:
                logger.warning("No branches provided for communication counts")
                return {
                    'data': [],
                    'summary': {
                        'total_days': 0,
                        'total_branches': 0,
                        'total_communications': 0
                    },
                    'message': 'No accessible branches found'
                }
            
            # Parse and validate dates
            parsed_from_date = self.parse_date(from_date)
            parsed_to_date = self.parse_date(to_date)
            
            if not parsed_from_date or not parsed_to_date:
                raise ValueError("Invalid date format. Please use YYYY-MM-DD format.")
            
            # Ensure from_date is not after to_date
            if parsed_from_date > parsed_to_date:
                raise ValueError("From date cannot be after to date")
            
            logger.info(f"Calling daywise_communication_counts with branches: {branches}, "
                       f"from_date: {parsed_from_date}, to_date: {parsed_to_date}")
            
            with connection.cursor() as cursor:
                # Call the stored procedure
                cursor.execute(
                    "SELECT public.daywise_communication_counts(%s, %s, %s);",
                    [branches, parsed_from_date, parsed_to_date]
                )
                result = cursor.fetchone()
                
                if result and result[0]:
                    # The stored procedure returns JSON, so parse it
                    communication_data = result[0]
                    
                    # If it's already a Python object (dict/list), use it directly
                    # If it's a JSON string, parse it
                    if isinstance(communication_data, str):
                        communication_data = json.loads(communication_data)
                    
                    # Calculate summary statistics
                    summary = self.calculate_summary(communication_data, branches, parsed_from_date, parsed_to_date)
                    
                    return {
                        'data': communication_data,
                        'summary': summary,
                        'filters': {
                            'from_date': parsed_from_date,
                            'to_date': parsed_to_date,
                            'total_branches_queried': len(branches),
                            'branch_ids': branches
                        },
                        'status': 'success'
                    }
                else:
                    logger.info("No communication data found for the given criteria")
                    return {
                        'data': [],
                        'summary': {
                            'total_days': 0,
                            'total_branches': len(set(branches)),
                            'total_communications': 0,
                            'whatsapp_total': 0,
                            'ivr_total': 0,
                            'sms_total': 0,
                            'blaster_total': 0,
                            'voicebot_total': 0
                        },
                        'filters': {
                            'from_date': parsed_from_date,
                            'to_date': parsed_to_date,
                            'total_branches_queried': len(branches),
                            'branch_ids': branches
                        },
                        'message': 'No communication data found for the specified date range',
                        'status': 'success'
                    }
                    
        except ValueError as ve:
            logger.error(f"Validation error in get_daywise_communication_counts: {str(ve)}")
            raise ve
        except Exception as e:
            logger.error(f"Error in get_daywise_communication_counts: {str(e)}")
            raise Exception(f"Database error while fetching communication counts: {str(e)}")

    def calculate_summary(self, data: List[Dict], branches: List[int], from_date: str, to_date: str) -> Dict:
        """Calculate summary statistics from the communication data"""
        try:
            if not data:
                return {
                    'total_days': 0,
                    'total_branches': len(set(branches)),
                    'total_communications': 0,
                    'whatsapp_total': 0,
                    'ivr_total': 0,
                    'sms_total': 0,
                    'blaster_total': 0,
                    'voicebot_total': 0
                }
            
            # Calculate date range
            start_date = datetime.strptime(from_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(to_date, '%Y-%m-%d').date()
            total_days = (end_date - start_date).days + 1
            
            # Get unique branches from data
            unique_branches = set(row.get('BranchName', '') for row in data)
            
            # Calculate totals
            totals = {
                'total_communications': 0,
                'whatsapp_total': 0,
                'ivr_total': 0,
                'sms_total': 0,
                'blaster_total': 0,
                'voicebot_total': 0
            }
            
            for row in data:
                totals['total_communications'] += row.get('TotalCommunications', 0)
                totals['whatsapp_total'] += row.get('WhatsAppCount', 0)
                totals['ivr_total'] += row.get('IVRCount', 0)
                totals['sms_total'] += row.get('SMSCount', 0)
                totals['blaster_total'] += row.get('BlasterCount', 0)
                totals['voicebot_total'] += row.get('VoicebotCount', 0)
            
            return {
                'total_days': total_days,
                'total_branches': len(unique_branches),
                'date_range_days': total_days,
                **totals
            }
            
        except Exception as e:
            logger.error(f"Error calculating summary: {str(e)}")
            return {
                'total_days': 0,
                'total_branches': 0,
                'total_communications': 0,
                'error': str(e)
            }

    def post(self, request):
        """Handle POST requests for daywise communication counts with all parameters in request body"""
        try:
            # Get date parameters from request body (required)
            from_date = request.data.get('from_date')
            to_date = request.data.get('to_date')
            
            # Validate that dates are provided
            if not from_date or not to_date:
                return Response({
                    'error': 'Missing required parameters',
                    'message': 'Both from_date and to_date are required in request body',
                    'status': 'error'
                }, status=400)
            
            # Get branches from request body (required)
            branch_ids = request.data.get('branch_ids', [])
            
            if not branch_ids:
                return Response({
                    'error': 'Missing required parameters',
                    'message': 'branch_ids array is required in request body',
                    'status': 'error'
                }, status=400)
            
            if not isinstance(branch_ids, list):
                return Response({
                    'error': 'Invalid parameter format',
                    'message': 'branch_ids must be an array of integers',
                    'status': 'error'
                }, status=400)
            
            if len(branch_ids) == 0:
                return Response({
                    'error': 'Invalid parameter value',
                    'message': 'branch_ids array cannot be empty',
                    'status': 'error'
                }, status=400)
            
            # Validate that all branch_ids are integers
            try:
                branch_ids = [int(bid) for bid in branch_ids]
            except (ValueError, TypeError):
                return Response({
                    'error': 'Invalid parameter format',
                    'message': 'All branch_ids must be valid integers',
                    'status': 'error'
                }, status=400)
            
            logger.info(f"Processing request with {len(branch_ids)} branches and date range {from_date} to {to_date}")
            
            # Get communication counts using the provided branches
            result = self.get_daywise_communication_counts(branch_ids, from_date, to_date)
            
            # Add request info to response
            result['request_info'] = {
                'total_branches_requested': len(branch_ids),
                'branch_ids_used': branch_ids,
                'total' : len(result['data']),
                'request_method': 'POST'
            }
            
            return Response(result)
            
        except ValueError as ve:
            return Response({
                'error': 'Invalid input parameters',
                'message': str(ve),
                'status': 'error'
            }, status=400)
        except Exception as e:
            logger.error(f"Error in DaywiseCommunicationCountsView POST: {str(e)}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return Response({
                'error': 'An error occurred while fetching daywise communication counts',
                'message': str(e),
                'status': 'error'
            }, status=500)
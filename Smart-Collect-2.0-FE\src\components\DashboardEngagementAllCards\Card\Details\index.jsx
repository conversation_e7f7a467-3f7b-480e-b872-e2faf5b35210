import React from "react";
import { Flex, Typography } from "antd";
import { formattedTextToCapitalized } from "../../../../constant";
import Style from "../_style.module.scss";
import PropTypes from "prop-types";

const { Text } = Typography;
export function Details({ title, image, value }) {
  return (
    <Flex justify="space-between">
      <Flex gap={10} align="center">
        {typeof image === "string" ? (
          <div className={Style.icon_img}>
            <img src={image} alt={title} />
          </div>
        ) : (
          image
        )}
        <Text className={Style.title}>
          {formattedTextToCapitalized(title)}:
        </Text>
      </Flex>
      <Text className={Style.value}>{value ?? "-"}</Text>
    </Flex>
  );
}

Details.propTypes = {
  title: PropTypes.string,
  image: PropTypes.oneOfType[(PropTypes.string, PropTypes.any)],
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
};

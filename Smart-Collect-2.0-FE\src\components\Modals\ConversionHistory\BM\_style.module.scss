@import url("../../../../index.css");

$bm-bg: #ffefc4;
$bm-color: #85aaff;

:global(.ant-modal-wrap):has(.feedback_modal) {
  z-index: 1200 !important;
}

.bm_container {
  .date {
    color: var(--dark-blue);
    font-family: 'Kanit','Courier New', Courier, monospace;
    font-weight: 400;
  }
  .bm_chat_box {
    background-color: $bm-bg;
    padding: 1rem 0.5rem;
    border-radius: 10px;
  }
  .img {
    width: 11px;
  }
  .bm_text {
    color: $bm-color;
    font-family: 'Kanit','Courier New', Courier, monospace;
    line-height: 0;
  }
  .response_text {
    background-color: var(--dark-blue);
    color: white;
    padding: 0.5rem 2rem;
    border-radius: 8px;
    font-family: 'Kanit','Courier New', Courier, monospace;
    font-weight: 400;
    font-size: 11px;
    cursor: pointer;
  }

  audio {
    width: 80%;
    background-color: white;
    border-radius: 10px;
    padding: 0px;
  }

  audio::-webkit-media-controls-panel {
    background-color: white;
    border-radius: 0px;
  }

  audio::-webkit-media-controls-play-button {
    background-color: white;
    border-radius: 50%;
  }
  .belongs_text {
    font-size: 13px;
    color: var(--dark-blue);
    font-weight: 700;
  }
}

// Responsive
@media only screen and (max-width: 768px) {
  .bm_container {
    .date {
      font-size: 11px;
    }
    .img {
      width: 10px;
    }
    .bm_text {
      font-size: 11px;
    }
    .response_text {
      font-size: 9px;
      padding: 0.4rem 1rem;
    }
    audio {
      width: 99%;
      height: 35px;
    }
    .audio_container {
      flex-direction: column;
    }
    .summary_container {
      .title {
        font-size: 11px;
      }
      .text {
        font-size: 9px;
      }
    }
  }
}

import { Select } from "antd";
import React, { useEffect, useState } from "react";
import Style from "./_app-header.module.scss";
import PropTypes from "prop-types";
import { AXIOS } from "../../apis/ho-Instance";
import { DownOutlined } from "@ant-design/icons";

export default function ReasonFilter({ selectedReason, setSelectedReason }) {
  const [isLoading, setIsLoading] = useState();
  const [reasonList, setReasonList] = useState([]);

  const handleFetchReasons = async () => {
    setIsLoading(true);
    try {
      const res = await AXIOS.get("getdistinctreasons/");
      let reasons = res.data?.distinct_reasons;
      // Filter out null or undefined values
      reasons = Array.isArray(reasons)
        ? reasons.filter((item) => item !== null && item !== undefined)
        : [];

      if (reasons.length > 0) {
        const uniqueReasons = [...new Set(reasons)];
        setReasonList(
          uniqueReasons.map((item) => ({
            label: item,
            value: item,
          }))
        );
      } else {
        setReasonList([]);
      }
    } catch (error) {
      console.warn("Error in reasons fetching", error?.message);
    } finally {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    handleFetchReasons();
  }, []);
  return reasonList.length > 0 ? (
    <Select
      className={Style.reason_select}
      options={reasonList ?? []}
      value={selectedReason}
      onChange={setSelectedReason}
      placeholder={"Select Reason"}
      loading={isLoading}
      allowClear
      showSearch
    />
  ) : null;
}

ReasonFilter.propTypes = {
  selectedReason: PropTypes.string,
  setSelectedReason: PropTypes.func,
};

import React from 'react'
import {Flex, Typography} from "antd";
import Style from "../_style.module.scss";
import PropTypes from 'prop-types';

const {Text}  = Typography;

export function Card({imgSrc, title, value}) {
  return (
    <Flex justify='space-between' align='center' className={Style.card}>
        <div className={Style.img_div}>
            <img src={imgSrc} alt={title}/>
        </div>
        <Flex vertical gap={10} align='center' style={{flex:1}}>
            <Text className={Style.title}>{title}</Text>
            <Text className={Style.value}>{value}</Text>
        </Flex>
    </Flex>
  )
}

Card.propTypes={
  imgSrc: PropTypes.string,
  title: PropTypes.string,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
}
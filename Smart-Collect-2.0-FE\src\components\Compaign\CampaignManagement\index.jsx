import { Flex, message, Spin, Table, Typography } from "antd";
import React, { useEffect, useState } from "react";
import AppHeader from "../../AppHeader";
import DONE_IMG from "../../../assets/Images/delete-camp.svg";
import ADD_ICON_IMG from "../../../assets/Images/add-icon.png";
import {
  ALL_CACHE_NAMES,
  handleDateFormatter,
  handleDownloadTable,
  SMART_COLLECT_MENU_IDS,
} from "../../../constant";
import { CategoriesModal } from "../../Modals/CategoriesModal";
import Style from "./_style.module.scss";
import { Link, Outlet, useLocation } from "react-router";
import { AXIOS } from "../../../apis/ho-Instance";
import DOWNLOAD_IMG from "../../../assets/Images/download.svg";
import { CampaignDeleteModal } from "../../Modals/CampaignDeleteModal";
import {
  getCacheKey,
  getFromCache,
  storeToCache,
} from "../../../utils/cacheHelper";

const { Text } = Typography;
const CACHE_NAME = ALL_CACHE_NAMES.CAMPAIGN_MANAGEMENT_CACHE;

export default function CampaignManagement() {
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleteLoading, setIsDeleteLoading] = useState(false);
  const [hovered, setHovered] = useState(false);
  const [isCategoriesOpen, setIsCategoriesOpen] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedCampaign, setSelectedCampaign] = useState({
    name: "",
    id: null,
  });
  const [campaigns, setCampaigns] = useState([]);
  const location = useLocation();
  const statusColorMap = {
    Saved: "#02951A",
    "Pending Approval": "#EC3939",
    Active: "#1743BE",
  };

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      sorter: (a, b) => a.key - b.key,
      render: (_, { key }) => <Text>{key}.</Text>,
    },
    {
      title: "Name",
      dataIndex: "Name",
      sorter: (a, b) => a.Name.localeCompare(b.Name),
      render: (_, { Name, CampaignMstID, Status }) => {
        const updatedStatus = Status === "Saved" ? "saved" : "view";
        return (
          <Link
            to={`/ho/${SMART_COLLECT_MENU_IDS.CAMPAIGN_MANAGEMENT}/${
              SMART_COLLECT_MENU_IDS.NORMAL
            }/${Name}/${updatedStatus?.toLowerCase()}`}
            onClick={() => {
              localStorage.setItem("campaignId", CampaignMstID);
            }}
          >
            <Text style={{ textTransform: "capitalize" }}>{Name}</Text>
          </Link>
        );
      },
    },
    {
      title: "Create Last Update Date",
      dataIndex: "CreatedDate",
      sorter: (a, b) => new Date(a.CreatedDate) - new Date(b.CreatedDate),
      render: (_, { CreatedDate }) => (
        <Text>{CreatedDate ? handleDateFormatter(CreatedDate) : "--"}</Text>
      ),
    },
    {
      title: "Status",
      dataIndex: "Status",
      sorter: (a, b) => a.Status.localeCompare(b.Status),
      render: (_, { Status }) => (
        <Text
          className={Style.status}
          style={{ color: statusColorMap[Status] || "#0F2050" }}
        >
          {Status}
        </Text>
      ),
    },
    {
      title: "Action",
      dataIndex: "action",
      render: (_, { Name, Status, CampaignMstID }, index) => (
        <Flex className={Style.icon_div} gap={10}>
          {Status?.toLowerCase() === "saved" ? (
            <div className={Style.icon_img}>
              <input
                type="image"
                src={DONE_IMG}
                style={{ width: "20px" }}
                alt="delete"
                onClick={() => {
                  setSelectedCampaign({ name: Name, id: CampaignMstID });
                  setShowDeleteModal(true);
                }}
              />
            </div>
          ) : (
            "--"
          )}
        </Flex>
      ),
    },
  ];

  // handle ok
  const handleOk = () => {
    setIsCategoriesOpen(false);
  };

  // handle cancel
  const handleCancel = () => {
    setIsCategoriesOpen(false);
  };

  // Fetch campaign data
  const handleGetCampaignData = async () => {
    setIsLoading(true);
    const cacheKey = getCacheKey({ endpoint: "all-campaign-details/" });
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setCampaigns(cachedData);
      setIsLoading(false);
    }
    try {
      const response = await AXIOS.get("v1/getcampaigns/");
      if (response.status === 200) {
        setCampaigns(response.data);
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data ?? [],
        });
      }
    } catch (error) {
      console.log("Error in campaigns", error?.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteCampaign = async () => {
    setIsDeleteLoading(true);
    try {
      const res = await AXIOS.delete(
        `/v1/getcampaigns/${selectedCampaign.id}/`
      );
      if (res.status === 200 || res.status === 202) {
        setCampaigns((prev) =>
          prev.filter((c) => c.CampaignMstID !== selectedCampaign.id)
        );
        message.success("Campaign deleted successfully!");
      }
    } catch (error) {
      console.log("Error in delete campaign", error?.message);
      message.error("Campaign is not deleted, try again");
    } finally {
      setIsDeleteLoading(false);
      setShowDeleteModal(false);
    }
  };

  const dataSource = Array.isArray(campaigns)
    ? campaigns.map((data, i) => ({ key: i + 1, ...data }))
    : [];

  useEffect(() => {
    handleGetCampaignData();
  }, [location]);

  // Handle downloading
  const handleDownload = async () => {
    if (campaigns.length === 0) {
      return message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: "Campaign Management",
        worksheetName: "Campaign-Management",
        tableData: campaigns,
      });
    }
  };
  return location.pathname === "/ho/campaign-management" ? (
    <Flex vertical gap={20}>
      <AppHeader title={"Campaign Management"} />

      <div>
        <Spin tip={"Loading..."} spinning={isLoading}>
          <Flex justify="end">
            <button className={Style.download_button} onClick={handleDownload}>
              <img src={DOWNLOAD_IMG} alt="download-button" />
            </button>
          </Flex>
          <Table
            virtual
            className={Style.customTable}
            columns={columns}
            dataSource={dataSource}
            scroll={{
              x: 1000,
              y: 360,
            }}
            pagination={{
              showSizeChanger: false,
            }}
          />
        </Spin>

        {/* Add campaign button */}
        <Flex justify="end">
          <button
            onMouseEnter={() => setHovered(true)}
            onMouseLeave={() => setHovered(false)}
            onClick={() => setIsCategoriesOpen(true)}
            className={Style.add_button}
          >
            <span
              className={`${Style.button_text} ${
                hovered ? `${Style.show}` : `${Style.hide}`
              }`}
            >
              Create New Campaign
            </span>

            <div className={Style.icon_img}>
              <img src={ADD_ICON_IMG} alt="add-icon" />
            </div>
          </button>
        </Flex>
      </div>

      {/* Categories modal */}
      <CategoriesModal
        open={isCategoriesOpen}
        handleCancel={handleCancel}
        handleOk={handleOk}
      />

      {/* Campaign Delete Modal */}
      <CampaignDeleteModal
        modalStatus={showDeleteModal}
        setModalStatus={setShowDeleteModal}
        handleDeleteCampaign={handleDeleteCampaign}
        desc={`campaign "${selectedCampaign.name}"`}
        isLoading={isDeleteLoading}
      />
    </Flex>
  ) : (
    <Outlet />
  );
}

import { Flex, Typography } from "antd";
import React, { useState } from "react";
import { formattedTextToCapitalized } from "../../../constant";
import { Link } from "react-router";
import { Details } from "./Details";
import Style from "./_style.module.scss";
import { ConversionHistory } from "../../Modals/ConversionHistory";
import PropTypes from "prop-types";
import CustomerProfileModal from "../../Modals/CustomerProfileModal";
import ProfileSVG from "../../../assets/SVGs/Profile";
import ChatHistorySVG from "../../../assets/SVGs/ChatHistory";
import AppIdSVG from "../../../assets/SVGs/AppId";
import BranchSVG from "../../../assets/SVGs/Branch";
import LoanTypeSVG from "../../../assets/SVGs/LoanTypeSVG";

const { Title, Text } = Typography;

export function Card({
  name,
  customerId,
  disbursementId,
  branchName,
  loanType,
  overdueAmount,
  loanMstId,
}) {
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const [showCustomerProfile, setShowCustomerProfile] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState(null);

  const handleHistoryCancel = () => {
    setIsHistoryModalOpen(false);
  };
  return (
    <>
      <Flex vertical className={Style.card_container}>
        {/* customer details */}
        <Flex
          justify="space-between"
          align="center"
          className={Style.customer_details}
        >
          <Flex
            gap={10}
            align="center"
            className={Style.profile_container}
            style={{ cursor: "pointer" }}
            onClick={() => {
              setSelectedCustomer({
                name,
                customerId,
                disbursementId,
                branchName,
                loanType,
                overdueAmount,
                loanMstId,
              });
              setShowCustomerProfile(true);
            }}
          >
            {/* profile icon */}
            <ProfileSVG />

            {/* details */}
            <Flex vertical gap={5}>
              <Title level={4} className={Style.name}>
                {name}
              </Title>
              <Text className={Style.customer_id}>
                Customer Id: {customerId}
              </Text>
            </Flex>
          </Flex>

          {/* History icons */}
          <Flex
            gap={20}
            onClick={() => setIsHistoryModalOpen(true)}
            style={{ cursor: "pointer" }}
          >
            <ChatHistorySVG width={20} height={20} />
          </Flex>
        </Flex>

        {/* details card */}
        <Flex vertical gap={2} className={Style.payment_details}>
          <Details
            image={<AppIdSVG />}
            title={"Disbursement Id"}
            value={disbursementId}
          />
          <Details
            image={<BranchSVG />}
            title={"Branch Name"}
            value={formattedTextToCapitalized(branchName)}
          />
          <Details
            image={<LoanTypeSVG />}
            title={"Loan Type"}
            value={formattedTextToCapitalized(loanType)}
          />
        </Flex>

        {/* amount details */}
        <Flex
          justify="space-between"
          align="center"
          gap={20}
          className={Style.amount_details}
        >
          <Link
            to="#"
            className={Style.text}
            style={{ textDecoration: "underline" }}
          >
            Overdue Amount
          </Link>
          <Title level={4} className={`${Style.amount} ${Style.error}`}>
            Rs.{overdueAmount}
          </Title>
        </Flex>
      </Flex>

      <ConversionHistory
        customerName={name}
        loanMstId={loanMstId}
        modalStatus={isHistoryModalOpen}
        handleCancel={handleHistoryCancel}
      />
      {selectedCustomer && (
        <CustomerProfileModal
          modalStatus={showCustomerProfile}
          name={selectedCustomer.name}
          customerId={selectedCustomer.customerId}
          disbursementId={selectedCustomer.disbursementId}
          branchName={selectedCustomer.branchName}
          loanType={selectedCustomer.loanType}
          overdueAmount={selectedCustomer.overdueAmount}
          loanmstid={selectedCustomer.loanMstId}
          handleClose={() => {
            setSelectedCustomer(null);
            setShowCustomerProfile(false);
          }}
        />
      )}
    </>
  );
}

Card.propTypes = {
  name: PropTypes.string,
  customerId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  disbursementId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  branchName: PropTypes.string,
  loanType: PropTypes.string,
  overdueAmount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  loanMstId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
};

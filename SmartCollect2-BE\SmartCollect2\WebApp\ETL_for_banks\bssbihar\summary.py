import pandas as pd
import numpy as np
from datetime import datetime
from ..queries import *
from ..utils import *
from ..sp import execute_post_processing_procedures
import threading

def convert_to_date_format(date_value):
    """
    Convert various date formats to YYYY-MM-DD format
    Handles: datetime objects, strings with time, Excel serial dates, etc.
    """
    if pd.isna(date_value) or date_value is None or date_value == '':
        return None
    
    try:
        # If it's already a datetime object
        if isinstance(date_value, datetime):
            return date_value.strftime('%Y-%m-%d')
        
        # If it's a string
        if isinstance(date_value, str):
            # Remove extra whitespace
            date_value = date_value.strip()
            
            # Handle common date formats
            for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', 
                       '%d-%m-%Y', '%m-%d-%Y', '%Y/%m/%d']:
                try:
                    parsed_date = datetime.strptime(date_value, fmt)
                    return parsed_date.strftime('%Y-%m-%d')
                except ValueError:
                    continue
        
        # If it's a numeric value (Excel serial date)
        if isinstance(date_value, (int, float)):
            try:
                # Convert Excel serial date to datetime
                parsed_date = convert_excel_serial_date(date_value)
                if parsed_date and not pd.isna(parsed_date):
                    return parsed_date.strftime('%Y-%m-%d')
            except:
                pass
        
        # Try pandas to_datetime as last resort
        parsed_date = pd.to_datetime(date_value, errors='coerce')
        if not pd.isna(parsed_date):
            return parsed_date.strftime('%Y-%m-%d')
        
        print(f"Warning: Could not parse date value: {date_value}")
        return None
        
    except Exception as e:
        print(f"Error converting date {date_value}: {str(e)}")
        return None

def run_post_processing_in_background(bank_id):
    """
    Run post-processing procedures in a background thread
    """
    try:
        print(f"Starting background post-processing for bank_id: {bank_id}")
        execute_post_processing_procedures(bank_id)
        print(f"Background post-processing initiated for bank_id: {bank_id}")
    except Exception as e:
        print(f"Error starting background post-processing for bank_id {bank_id}: {str(e)}")

def process_bssbihar_disbursement_data(df1):
    df = pd.DataFrame()
    df1.columns = df1.columns.str.strip().str.replace(r'\s+', ' ', regex=True)
    
    # Check for missing disbursementid values before processing
    print(f"[DISBURSEMENT] Checking disbursementid column for null/missing values...")
    disbursementid_null_count = df1['disbursementid'].isnull().sum()
    disbursementid_empty_count = (df1['disbursementid'] == '').sum()
    disbursementid_na_count = df1['disbursementid'].isna().sum()
    
    total_missing = disbursementid_null_count + disbursementid_empty_count
    print(f"[DISBURSEMENT] Total records: {len(df1)}")
    print(f"[DISBURSEMENT] Null disbursementid values: {disbursementid_null_count}")
    print(f"[DISBURSEMENT] Empty disbursementid values: {disbursementid_empty_count}")
    print(f"[DISBURSEMENT] NA disbursementid values: {disbursementid_na_count}")
    print(f"[DISBURSEMENT] Total missing disbursementid values: {total_missing}")
    
    if total_missing > 0:
        error_message = f"[DISBURSEMENT] CRITICAL ERROR: Found {total_missing} records with missing disbursementid values out of {len(df1)} total records. Processing stopped."
        print(error_message)
        
        # Print sample records with missing disbursementid for debugging
        missing_records = df1[df1['disbursementid'].isnull() | (df1['disbursementid'] == '')]
        if not missing_records.empty:
            print("[DISBURSEMENT] Sample records with missing disbursementid:")
            print(missing_records[['customerid', 'disbursementid', 'customername']].head(5))
        
        raise ValueError(error_message)
    
    print("[DISBURSEMENT] ✓ All disbursementid values are present. Proceeding with data processing...")
    
    df['customerid'] = df1['customerid']
    df['disbursementid'] = df1['disbursementid']
    df['loantype'] = df1['loantype']
    df['customername'] = df1['customername'].astype(str).str.upper().str.strip()
    df['disbursementamount'] = df1['disbursementamount']
    
    # Apply date formatting
    df['disbursementdate'] = df1['disbursementdate'].apply(convert_to_date_format)
    
    df['emiamount'] = df1['emiamount']
    df['mobileno'] = df1['mobileno']
    df['numberofdayspastdue'] = df1['numberofdayspastdue']
    df['branchname'] = df1['branchname'].astype(str).str.upper().str.strip()
    df['branchcode'] = df1['branchcode']
    
    # Apply date formatting to all date columns
    df['lastpaymentdate'] = df1['lastpaymentdate'].apply(convert_to_date_format)
    df['nextemidate'] = df1['nextemidate'].apply(convert_to_date_format)
    df['closingdate'] = df1['closingdate'].apply(convert_to_date_format)
    
    df['loanclassification'] = df1['loanclassification']
    df['overdueamount'] = df1['overdueamount']
    df['originaldisbursementid'] = df1['originaldisbursementid']
    df['pending_emis'] = df1['pending_emis']
    df['extracolumn1'] = df1['extracolumn1']
    df['centername'] = df1['centername']
    df['extracolumn2'] = df1['extracolumn2']

    return df

def process_bssbihar_collection_data(df1):
    df = pd.DataFrame()
    df1.columns = df1.columns.str.strip().str.replace(r'\s+', ' ', regex=True)

    # Check for missing disbursementid values before processing
    print(f"[COLLECTION] Checking DisbursementID column for null/missing values...")
    disbursementid_null_count = df1['DisbursementID'].isnull().sum()
    disbursementid_empty_count = (df1['DisbursementID'] == '').sum()
    disbursementid_na_count = df1['DisbursementID'].isna().sum()
    
    total_missing = disbursementid_null_count + disbursementid_empty_count
    print(f"[COLLECTION] Total records: {len(df1)}")
    print(f"[COLLECTION] Null DisbursementID values: {disbursementid_null_count}")
    print(f"[COLLECTION] Empty DisbursementID values: {disbursementid_empty_count}")
    print(f"[COLLECTION] NA DisbursementID values: {disbursementid_na_count}")
    print(f"[COLLECTION] Total missing DisbursementID values: {total_missing}")
    
    if total_missing > 0:
        error_message = f"[COLLECTION] CRITICAL ERROR: Found {total_missing} records with missing DisbursementID values out of {len(df1)} total records. Processing stopped."
        print(error_message)
        
        # Print sample records with missing DisbursementID for debugging
        missing_records = df1[df1['DisbursementID'].isnull() | (df1['DisbursementID'] == '')]
        if not missing_records.empty:
            print("[COLLECTION] Sample records with missing DisbursementID:")
            available_cols = [col for col in ['customerid', 'DisbursementID', 'CollectedAmount'] if col in df1.columns]
            print(missing_records[available_cols].head(5))
        
        raise ValueError(error_message)
    
    print("[COLLECTION] ✓ All DisbursementID values are present. Proceeding with data processing...")

    df['customerid']= df1['customerid']
    df['disbursementid'] = df1['DisbursementID']
    df['principlecollected'] = df1['PrincipleCollected']
    df['interestcollected'] = df1['InterestCollected']
    df['collectedamount'] = df1['CollectedAmount']
    
    # Apply date formatting
    df['scheduledate'] = df1['ScheduleDate'].apply(convert_to_date_format)
    df['collectiondate'] = df1['CollectionDate'].apply(convert_to_date_format)
    
    df['pos'] = df1['pos']

    return df

def merged_bssbihar_data(disbursement_df, collection_df):
    """
    Merge disbursement and collection data for BSS Bihar.
    
    Parameters:
    disbursement_df: DataFrame from process_bssbihar_disbursement_data (can be None)
    collection_df: DataFrame from process_bssbihar_collection_data (can be None)
    
    Returns:
    DataFrame: Merged data with collection details updated where matches found
    """
    import pandas as pd
    
    # Handle cases where one or both DataFrames are None
    if disbursement_df is None and collection_df is None:
        print("Warning: Both disbursement and collection data are None. Returning empty DataFrame.")
        return pd.DataFrame()
    
    if disbursement_df is None:
        print("Warning: No disbursement data provided. Returning collection data only.")
        return collection_df.copy()
    
    if collection_df is None:
        print("Warning: No collection data provided. Returning disbursement data only.")
        return disbursement_df.copy()
    
    # Start with a copy of disbursement data
    merged_df = disbursement_df.copy()
    
    # Create a composite key for matching
    disbursement_keys = merged_df[['customerid', 'disbursementid']].copy()
    collection_keys = collection_df[['customerid', 'disbursementid']].copy()
    
    # Track which collection records have been matched
    matched_collection_indices = set()
    
    # Update existing disbursement records with collection data
    for idx, disburse_row in disbursement_keys.iterrows():
        # Find matching collection records
        matches = collection_df[
            (collection_df['customerid'] == disburse_row['customerid']) & 
            (collection_df['disbursementid'] == disburse_row['disbursementid'])
        ]
        
        if not matches.empty:
            # Update with the latest collection record (in case multiple matches)
            latest_collection = matches.iloc[-1]  # Take the last record
            matched_collection_indices.update(matches.index.tolist())
    
    # Add unmatched collection records as new entries
    unmatched_collection = collection_df[~collection_df.index.isin(matched_collection_indices)]
    
    if not unmatched_collection.empty:
        print(f"Adding {len(unmatched_collection)} unmatched collection records as new entries.")
        
        # Create new rows for unmatched collection data
        new_rows = []
        for _, collection_row in unmatched_collection.iterrows():
            new_row = {}
            
            # Map collection fields to disbursement structure
            new_row['customerid'] = collection_row['customerid']
            new_row['disbursementid'] = collection_row['disbursementid']
            new_row['interestcollected'] = collection_row['interestcollected']
            new_row['collectedamount'] = collection_row['collectedamount']
            new_row['collectiondate'] = collection_row['collectiondate']
            
            if 'scheduledate' in collection_row.index:
                new_row['scheduledate'] = collection_row['scheduledate']
            
            # Fill other disbursement fields with default/null values
            for col in merged_df.columns:
                if col not in new_row:
                    if col in ['disbursementamount', 
                              'emiamount', 'numberofdayspastdue', 'overdueamount']:
                        new_row[col] = 0.0
                    elif col in ['customername', 'loantype', 'mobileno', 
                                  'branchname', 
                                'branchcode', 'pos']:
                        new_row[col] = None
                    elif col in ['disbursementdate', 'lastpaymentdate', 'nextemidate', 'closingdate']:
                        new_row[col] = None
                    else:
                        new_row[col] = None
            
            new_rows.append(new_row)
        
        # Append new rows to merged DataFrame
        if new_rows:
            new_df = pd.DataFrame(new_rows)
            merged_df = pd.concat([merged_df, new_df], ignore_index=True)
    
    print(f"Merge completed. Total records: {len(merged_df)}")
    print(f"Original disbursement records: {len(disbursement_df) if disbursement_df is not None else 0}")
    print(f"Collection records processed: {len(collection_df) if collection_df is not None else 0}")
    print(f"Matched collection records: {len(matched_collection_indices)}")
    print(f"New records added from collection: {len(unmatched_collection)}")
    
    return merged_df

def process_raw_data_for_bssbihar(df1):
    db_operation_success = False
    db_error_message = None
    df = pd.DataFrame()
    df1.columns = df1.columns.str.strip().str.replace(r'\s+', ' ', regex=True)
    print(df1.head(),"Dfdkfndkf")
    
    # Check for missing disbursementid values before processing
    print(f"Checking disbursementid column for null/missing values...")
    disbursementid_null_count = df1['disbursementid'].isnull().sum()
    disbursementid_empty_count = (df1['disbursementid'] == '').sum()
    disbursementid_na_count = df1['disbursementid'].isna().sum()
    
    total_missing = disbursementid_null_count + disbursementid_empty_count
    print(f"Total records: {len(df1)}")
    print(f"Null disbursementid values: {disbursementid_null_count}")
    print(f"Empty disbursementid values: {disbursementid_empty_count}")
    print(f"NA disbursementid values: {disbursementid_na_count}")
    print(f"Total missing disbursementid values: {total_missing}")
    
    if total_missing > 0:
        error_message = f"CRITICAL ERROR: Found {total_missing} records with missing disbursementid values out of {len(df1)} total records. Processing stopped."
        print(error_message)
        
        # Print sample records with missing disbursementid for debugging
        missing_records = df1[df1['disbursementid'].isnull() | (df1['disbursementid'] == '')]
        if not missing_records.empty:
            print("Sample records with missing disbursementid:")
            print(missing_records[['customerid', 'disbursementid', 'customername']].head(5))
        
        raise ValueError(error_message)
    
    print("✓ All disbursementid values are present. Proceeding with data processing...")
    
    df['customerid'] = df1['customerid']
    df['disbursementid'] = df1['disbursementid']
    df['loantype'] = df1['loantype']
    df['customername'] = df1['customername'].astype(str).str.upper().str.strip()
    df['disbursementamount'] = df1['disbursementamount']
    
    # Apply proper date formatting to all date columns
    df['disbursementdate'] = df1['disbursementdate'].apply(convert_to_date_format)
    
    df['emiamount'] = df1['emiamount']
    df['numberofdayspastdue'] = df1['numberofdayspastdue']
    df['mobileno'] = df1['mobileno']
    df['branchname'] = df1['branchname'].astype(str).str.upper().str.strip()
    df['branchcode'] = df1['branchcode']
    
    # Apply date formatting to all date columns
    df['lastpaymentdate'] = df1['lastpaymentdate'].apply(convert_to_date_format)
    df['nextemidate'] = df1['nextemidate'].apply(convert_to_date_format)
    df['closingdate'] = df1['closingdate'].apply(convert_to_date_format)
    
    df['loanclassification'] = df1['loanclassification']
    df['overdueamount'] = df1['overdueamount']
    df['originaldisbursementid'] = df1['originaldisbursementid']
    df['pending_emis'] = df1['pending_emis']
    df['extracolumn1'] = df1['extracolumn1'] # it is pending EMI 
    df['centername'] = df1['centername']
    df['bankmstid'] = 32
    
    # Format inserted_date properly
    df['inserted_date'] = datetime.now().strftime('%Y-%m-%d')
    
    df['extracolumn2'] = df1['extracolumn2']
    
    # Debug: Print sample date values before database insertion
    print("Sample date values after formatting:")
    date_columns = ['disbursementdate', 'lastpaymentdate', 'nextemidate', 'closingdate', 'inserted_date']
    for col in date_columns:
        if col in df.columns:
            sample_values = df[col].dropna().head(3).tolist()
            print(f"{col}: {sample_values}")
    
    # Database operation
    try:
        result = insert_data_in_raw_table(df, bank_id=32)
        db_operation_success = result['success']
        if not result['success']:
            db_error_message = result['message']
    except Exception as e:
        db_operation_success = False
        db_error_message = str(e)
        print(f"Database update failed for bssbihar data: {e}")
    
    # Start background post-processing if database operation was successful
    if db_operation_success:
        bank_id = 32
        background_thread = threading.Thread(
            target=run_post_processing_in_background,
            args=(bank_id,),
            daemon=True
        )
        background_thread.start()
        print(f"Post-processing started in background thread for bank_id: {bank_id}")
    else:
        print(f"Skipping post-processing due to database operation failure: {db_error_message}")

    return {
        "Total records processed": len(df),
        "New records inserted": result.get('inserted_count', 0) if db_operation_success else 0,
        "Duplicate records found": result.get('duplicate_count', 0) if db_operation_success else 0,
        "db_operation_success": db_operation_success,
        "db_error_message": db_error_message,
        "message": result.get('message', '') if db_operation_success else db_error_message,
    }
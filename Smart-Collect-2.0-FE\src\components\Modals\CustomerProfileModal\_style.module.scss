@import url("../../../index.css");

$light-gray: #d9d9d9;
$tab-height: 7px;
$blue: #3b67c7;
$light-blue: #e4f8f9;
$border-color: #acc5fc;
$bg-error: #ec393929;
$error: #ec3939;
$success: #087b1a;
$sky-blue: #e8efff;
$top-gap: 30px;
$table-radius: 22px;
$attempts-bg: #bdd1ff;
$disable: #787777;
$body: #e4f8f9;

.profile_modal {
  padding-top: 3rem;
  background-color: "red";

  .payment_details {
    padding: 0 1rem;

    .icon_img {
      width: 15px;
      img {
        width: 100%;
        object-fit: contain;
      }
    }
    .title {
      font-size: 14px;
      font-family:'Kanit','Courier New', Courier, monospace;
      text-transform: capitalize;
    }
    .value {
      font-size: 14px;
      font-weight: 400;
      font-family:'Kanit','Courier New', Courier, monospace;
      text-transform: capitalize;
    }
  }

  .amount_details {
    padding-bottom: 1rem;
    .text {
      font-size: 15px;
      font-weight: 400;
      font-family:'Kanit','Courier New', Courier, monospace;
    }
    .amount_button {
      margin: 0;
      cursor: pointer;
    }
    .amount {
      margin: 0;
      font-weight: 600;
      color: #0f2050;
      font-family:'Kanit','Courier New', Courier, monospace;
      font-size: 15px;
    }
    .error {
      color: $error;
    }
    .success {
      color: $success;
    }
  }

  .attempts {
    background-color: white;
    margin: 1rem;
    padding: 0.2rem;
    border-radius: 4px;
  }

  .recent {
    .value {
      font-family:'Kanit','Courier New', Courier, monospace;
      color: var(--dark-blue);
      font-weight: 500;
      font-size: 16px;
    }
    .channel {
      font-family:'Kanit','Courier New', Courier, monospace;
      color: var(--dark-blue);
      font-size: 11px;
    }
    .icon_img {
      width: 20px;
      height: 20px;
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
  }

  .first_col {
    border-right: 1px solid #acc5fc;
  }

  .second_col {
    padding: 0 1rem;
  }

  .container {
    padding: 0 1rem;
    transition: all 0.3s ease;
  }

  .customTable {
    padding-top: 1.5rem;
    .blue_text {
      font-weight: 500;
      color: #407bff;
    }

    .icon_div {
      display: flex;
      justify-content: center;
      width: 100%;
      .icon_img {
        width: 20px;
        height: 20px;
        img {
          width: 100%;
          object-fit: contain;
          cursor: pointer;
        }
      }
    }

    .icon {
      display: flex;
      align-items: center;
      cursor: pointer;
      img {
        width: 3.5px;
      }
    }

    .text {
      color: white;
      font-family:'Kanit','Courier New', Courier, monospace;
    }

    //Global ant design classes
    // Pagination
    :global(.ant-pagination) {
      justify-content: center;
      margin: 0 !important;

      :global(.ant-pagination-prev),
      :global(.ant-pagination-next) {
        color: var(--dark-blue);
        border: 0 solid var(--blue);
        background: #e4f8f9;
        height: 25px;
        min-width: 15px;
        border-radius: 0px;
        margin: 0;
        button {
          border-radius: 0px;
        }
      }
      :global(.ant-pagination-item) {
        margin-right: 0;
        height: 0;
        a {
          color: $disable;
          font-size: 0.9rem;
          line-height: 23px;
          font-family:'Kanit','Courier New', Courier, monospace;
          font-weight: normal;
        }
        &:hover {
          background-color: transparent;
        }
      }
      :global(.ant-pagination-item-active) {
        border: none;
        a {
          color: var(--dark-blue);
          font-size: 1.2rem;
          padding: 0;
        }
      }
    }

    //Table container
    :global(.ant-table-container) {
      padding: 0.5rem;
      margin-bottom: 0.5rem;
      border-start-start-radius: $table-radius;
      border-start-end-radius: $table-radius;

      // Table header
      :global(.ant-table-header) {
        position: relative;
        margin: 0 auto;
        top: -21px;
        border-radius: $table-radius;
        :global(.ant-table-column-has-sorters) {
          background-color: var(--dark-blue);
          &:hover {
            background-color: var(--dark-blue);
          }

          :global(.ant-table-column-sorter-up),
          :global(.ant-table-column-sorter-down) {
            svg {
              fill: white;
            }
            &:global(.active) {
              svg {
                fill: rgb(24, 155, 249);
              }
            }
          }
        }
      }

      // Table virtual body
      :global(.ant-table-tbody-virtual) {
        margin-top: -8px;
      }

      &::before {
        box-shadow: none !important;
      }
    }

    // Table rows header
    :global(.ant-table-thead > tr) {
      th {
        border-start-end-radius: 0 !important;
        background-color: var(--dark-blue);
        color: white;
        border-bottom: none;
        text-align: center;
        font-family:'Kanit','Courier New', Courier, monospace;
        font-weight: normal;
        padding: 2px;
        font-size: 12px;
        &:not(:last-child):not(.ant-table-selection-column):not(
            .ant-table-row-expand-icon-cell
          ):not([colspan])::before {
          width: 0;
        }
      }
    }

    // Table body
    :global(.ant-table-tbody) {
      // Body rows
      :global(.ant-table-row) {
        &:nth-child(even) {
          background-color: var(--light-green);
        }

        :global(.ant-table-cell-row-hover) {
          background: transparent;
        }
        // Cols
        :global(.ant-table-cell) {
          font-family:'Kanit','Courier New', Courier, monospace;
          font-weight: normal;
          text-align: center;
          border-bottom: 2px solid white;
          font-size: 10px;
          padding: 5px;

          :global(.ant-typography) {
            font-family:'Kanit','Courier New', Courier, monospace;
            font-size: 11px;
          }

          &:global(.ant-table-cell-fix-right) {
            border-bottom: none !important;
          }
          &:global(.ant-table-cell-fix-right-first) {
            border-left: 2px solid white;
          }
        }
      }
      :global(.ant-table-tbody-virtual-scrollbar-horizontal){
        :global(.ant-table-tbody-virtual-scrollbar-thumb){
          height: 2.3px !important;
          bottom: 0px !important;
        }
      }
    }

    // Fixed Cols
    :global(.ant-table-cell-fix-right) {
      background-color: $body;
    }
  }

  .customer_details {
    padding: 0.3rem 1rem;
    background: var(--dark-blue);
    color: white;
    border-radius: 50px;
    width: 91%;
    position: relative;
    z-index: 11;
    margin: 0 auto;
    .profile_img {
      width: 16px;
      height: 16px;
      overflow: hidden;
      cursor: pointer;
      img {
        width: 100%;
        object-fit: contain;
      }
    }
    .name {
      font-weight: 400;
      margin: 0;
      color: white;
      font-family:'Kanit','Courier New', Courier, monospace;
      font-size: 13px;
    }
    .customer_id {
      font-size: 12px;
      color: white;
      font-weight: 300;
      font-family:'Kanit','Courier New', Courier, monospace;
    }
  }

  .body_container {
    margin-top: -20px;
    background-color: $light-blue;
    border-radius: 10px;
    padding-bottom: 1rem;
  }

  .toggle_button_container {
    padding: 1rem;
    display: flex;
    justify-content: end;
    position: sticky;
    bottom: 0;
    z-index: 10;
  }

  // Toggle button
  .toggle_button {
    background-color: var(--blue);
    border-color: var(--blue);
    color: white;
    font-family:'Kanit','Courier New', Courier, monospace;
    font-weight: 500;
    padding: 0 1rem;
    height: 28px;
    border-radius: 16px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    &:hover {
      opacity: 0.9;
    }

    &:active {
      transform: scale(0.98);
    }
  }

  // Transition styles
  .summary_section,
  .table_section {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); // Faster timing
    overflow: hidden;
    will-change: transform, opacity; 
    &.hidden {
      max-height: 0;
      opacity: 0;
      transform: translateY(-50px);
      margin: 0;
      padding: 0;
      pointer-events: none;
    }

    &.visible {
      opacity: 1;
      transform: translateY(0);
      transition-delay: 0.1s; 
    }
  }

  .table_section {
     &.visible{
      transform: translateY(10px);
     }
  }

  :global(.ant-modal-close) {
    display: none;
  }

  :global(.ant-modal-header) {
    padding: 0.5rem 0px;
    text-align: center;
    background-color: $light-blue;
    :global(.ant-modal-title) {
      color: white;
      font-weight: 500;
    }
  }

  :global(.ant-modal-content) {
    padding: 0px;
    background-color: transparent;
    box-shadow: none;
  }
}

// Responsive View
@media screen and (max-width: 768px) {
  .profile_modal {
    .first_col {
      border: none;
      border-bottom: 1px solid #d9d9d9;
      padding-bottom: 1rem;
      margin-bottom: 1rem;
    }

    .second_col,
    .container,
    .payment_details {
      padding: 0;
    }

    .payment_details,
    .amount_details {
      .icon_img {
        svg {
          width: 12px;
          height: 12px;
        }
      }
      .title,
      .text,
      .amount {
        font-size: 12px;
      }
      .value {
        font-size: 12px;
      }
    }

    .recent,
    .customer_details {
      flex-wrap: wrap;
    }

    .recent {
      .value {
        font-size: 12px;
      }
      .icon_img {
        width: 12px;
      }
      .channel {
        font-size: 10px;
      }
    }

    .attempts {
      margin: 0;
    }

    .customTable {
      :global(.ant-table-thead > tr) {
        th {
          padding: 0.5rem;
        }
      }
      :global(.ant-table-tbody) {
        margin-top: 2rem;
        :global(.ant-table-row) {
          :global(.ant-table-cell) {
            padding: 0.5rem;
          }
        }
      }
    }

    .customer_details {
      padding: 0.5rem 1rem;
      .profile_img {
        width: 14px !important;
      }
      .name {
        font-size: 12px;
      }
      .customer_id {
        font-size: 10px;
      }
    }
    :global(.ant-modal-header) {
      margin-bottom: 1rem;
    }
  }
}
@media screen and (max-width: 468px) {
  .profile_modal {
    .customer_details {
      .profile_img {
        width: 12px !important;
      }
      .name {
        font-size: 10px;
      }
      .customer_id {
        font-size: 8px;
      }
    }
  }
}

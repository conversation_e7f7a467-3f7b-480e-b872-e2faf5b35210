import { Col, Flex, Input, message, Row, Select, Typography } from "antd";
import ADD_ICON_IMG from "../../../../../assets/Images/add-icon.png";
import { useEffect, useState } from "react";
import DELETE_IMG from "../../../../../assets/Images/delete.svg";
import Style from "./_style.module.scss";
import { useParams } from "react-router";
import { AXIOS } from "../../../../../apis/ho-Instance";
import { CampaignDeleteModal } from "../../../../Modals/CampaignDeleteModal";
import PropTypes from "prop-types";
import { FilterValueInput } from "./FilterValueInput";
import { CAMPAIGN_DATA_PROP_TYPES } from "../../../../../constant";

const { Text } = Typography;

export function CategoriesComponent({ campaignData, setCampaignData }) {
  const params = useParams();
  const [parameterOptions, setParameterOptions] = useState([]);
  const [parameterTypes, setParameterTypes] = useState({});
  const [columnOptionsMap, setColumnOptionsMap] = useState({});
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isDeleteLoading, setIsDeleteLoading] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState({
    FiltersID: null,
    index: null,
  });

  // Handle Fetch parameter
  const handleFetchParameters = async () => {
    try {
      const res = await AXIOS.get("v2/campaign/dropdownoptions", {
        params: {
          requesttype: "campaigncategory",
        },
      });
      if (res.status === 200) {
        const rawParams = res.data;
        const options = Object.keys(rawParams).map((key) => ({
          value: key, // e.g., "BankMstID"
          label: rawParams[key].verbose_name, // e.g., "Bank Name"
          type: rawParams[key].type, // e.g., "dropdown"
        }));
        setParameterOptions(options);
        setParameterTypes(rawParams);
      }
    } catch (error) {
      console.log("Error in parameters,", error?.message);
    }
  };

  // Handle Fetch parameter's value
  const handleFetchParameterValues = async ({ index, columnname }) => {
    try {
      const res = await AXIOS.get("v2/campaign/dropdownoptions", {
        params: {
          requesttype: "campaigncategorydata",
          columnname,
        },
      });
      if (res.status === 200) {
        const rawParams = res.data.values;
        const options = rawParams.map((key) => ({
          label: String(key),
          value: key,
        }));
        setColumnOptionsMap((prev) => ({
          ...prev,
          [index]: options,
        }));
      }
    } catch (error) {
      console.log("Error in column options,", error?.message);
    }
  };

  // Handle parameter name
  const handleParameterChange = async ({ index, value }) => {
    setCampaignData((prevData) => {
      const updatedFilters = prevData.categories.filters.map((filter, i) =>
        i === index
          ? {
              ...filter,
              parameterName: value,
              comparison: null,
              comparisonType: "value",
              value: "",
              list: "",
              rangeFrom: 0,
              rangeTo: 1,
            }
          : filter
      );

      return {
        ...prevData,
        categories: {
          ...prevData.categories,
          filters: updatedFilters,
        },
      };
    });
    const paramType = parameterTypes[value]?.type;
    if (paramType === "dropdown") {
      await handleFetchParameterValues({ columnname: value, index });
    }
  };

  // Handle comparison value
  const handleComparisonChange = ({ index, value }) => {
    setCampaignData((prevData) => {
      const updatedFilters = prevData.categories.filters.map((filter, i) =>
        i === index
          ? {
              ...filter,
              comparison: value,
              comparisonType: "value",
              value: "",
              list: "",
              rangeFrom: 0,
              rangeTo: 1,
            }
          : filter
      );
      return {
        ...prevData,
        categories: { ...prevData.categories, filters: updatedFilters },
      };
    });
  };

  // Handle comparison type
  const handleComparisonTypeChange = ({ index, value }) => {
    setCampaignData((prevData) => {
      const updatedFilters = prevData.categories.filters.map((filter, i) =>
        i === index
          ? {
              ...filter,
              comparisonType: value,
              value: "",
              list: "",
              rangeFrom: 0,
              rangeTo: 1,
            }
          : filter
      );
      return {
        ...prevData,
        categories: { ...prevData.categories, filters: updatedFilters },
      };
    });
  };

  // Handle value, list, range, and name
  const handleInputChange = ({ index, field, value }) => {
    setCampaignData((prevData) => {
      if (field === "name") {
        return {
          ...prevData,
          categories: { ...prevData.categories, name: value },
        };
      } else {
        const updatedFilters = [...prevData.categories.filters];
        updatedFilters[index][field] = value;
        return {
          ...prevData,
          categories: { ...prevData.categories, filters: updatedFilters },
        };
      }
    });
  };

  // Handle new filters
  const handleAddFilter = () => {
    setCampaignData((prevData) => {
      // Check if any existing filter has missing required fields
      const incompleteFilter = prevData.categories.filters.some(
        (filter) =>
          !filter.parameterName ||
          !filter.comparisonType ||
          !filter.comparison ||
          (!["list", "range"].includes(filter.comparison) &&
            filter.value === "") ||
          (filter.comparison === "list" && filter.list === "") ||
          (filter.comparison === "range" && filter.rangeFrom === null) ||
          (filter.comparison === "range" && filter.rangeTo === null)
      );

      if (prevData.categories.name === "" || !prevData.categories.name) {
        message.error("Please provide a name for the categories.");
        return prevData; // No changes applied
      }

      // If any required field is missing, show an alert and block new filter addition
      if (incompleteFilter) {
        message.error("Please complete all fields, before adding a new one.");
        return prevData; // No changes applied
      }

      // All fields are filled, add the new filter
      return {
        ...prevData,
        categories: {
          ...prevData.categories,
          filters: [
            ...prevData.categories.filters,
            {
              parameterName: null,
              comparison: null,
              value: "",
              list: "",
              rangeFrom: 0,
              rangeTo: 1,
            },
          ],
        },
      };
    });
  };

  //Handle delete exits filters
  const handleDeleteExitsFilter = async (id) => {
    try {
      await AXIOS.delete(`v1/getfilters/${id}/`);
    } catch (error) {
      console.log("Error in filter deleting,", error?.message);
    }
  };

  const handleDeleteFilter = async () => {
    setIsDeleteLoading(true);
    try {
      if (params.status === "saved" || params.status === "Saved") {
        if (
          selectedCategory.FiltersID !== undefined &&
          selectedCategory.FiltersID !== null
        )
          await handleDeleteExitsFilter(selectedCategory.FiltersID);
      }
      setCampaignData((prevData) => ({
        ...prevData,
        categories: {
          ...prevData.categories,
          filters: prevData.categories.filters.filter(
            (_, i) => i !== selectedCategory.index
          ),
        },
      }));

      // Adjust the options map to reflect updated filter indexes
      setColumnOptionsMap((prevMap) => {
        const newMap = {};
        Object.keys(prevMap).forEach((key) => {
          const keyNum = parseInt(key, 10);
          if (keyNum < selectedCategory.index) {
            newMap[keyNum] = prevMap[keyNum];
          } else if (keyNum > selectedCategory.index) {
            newMap[keyNum - 1] = prevMap[keyNum];
          }
          // skip indexToRemove
        });
        return newMap;
      });
      message.success("Filter deleted successfully!");
    } catch (error) {
      console.log("Error in delete campaign", error);
      message.error("Category is not deleted, try again");
    } finally {
      setIsDeleteLoading(false);
      setShowDeleteModal(false);
    }
  };

  // Filter out the Comparison options
  const getComparisonOptions = ({ filter, parameterTypes }) => {
    const type = parameterTypes[filter.parameterName]?.type;

    if (type === "dropdown") {
      return [
        { value: "in", label: "in" },
        { value: "not in", label: "not in" },
      ];
    }

    const baseOptions = [
      { value: "=", label: "= (equals)" },
      { value: "<", label: "< (Less than)" },
      { value: ">", label: "> (Greater than)" },
      { value: ">=", label: ">= (Greater than equals)" },
      { value: "<=", label: "<= (Less than equals)" },
    ];

    if (
      typeof filter?.parameterName === "string" &&
      filter.parameterName.includes("DPD")
    ) {
      baseOptions.push({ value: "range", label: "Range" });
    }

    return baseOptions;
  };

  //Invoking get methods
  useEffect(() => {
    handleFetchParameters();
  }, []);

  return (
    <Flex vertical className={Style.filter} gap={10}>
      <Text className={Style.text}>Select Parameter</Text>
      <Flex align="center" gap={10} className={Style.filters_container}>
        <Text className={Style.input_text}>Category Name:</Text>
        <Input
          className={Style.custom_input}
          placeholder="Enter"
          value={campaignData?.categories.name}
          onChange={(e) => {
            handleInputChange({
              field: "name",
              value: e.target.value,
            });
          }}
        />
      </Flex>
      <Flex vertical className={Style.filters_container} gap={20}>
        {campaignData?.categories?.filters?.map((filter, index) => {
          const isComparisonTypeVisible = !["in", "not in"].includes(
            filter.comparison
          );
          const columnWidth = isComparisonTypeVisible ? 6 : 8;
          return (
            <Row
              gutter={[30, 30]}
              key={`${index}-${filter.parameterName}`}
              justify={"space-between"}
            >
              {campaignData?.categories?.filters?.length > 1 && (
                <Col span={24}>
                  <Flex justify="end">
                    <input
                      type="image"
                      src={DELETE_IMG}
                      alt="delete"
                      style={{ cursor: "pointer", width: "9px" }}
                      onClick={() => {
                        setShowDeleteModal(true);
                        setSelectedCategory({
                          FiltersID: filter.FiltersID,
                          index: index,
                        });
                      }}
                    />
                  </Flex>
                </Col>
              )}

              <Col sm={columnWidth} xs={24}>
                <Flex align="center" gap={10}>
                  <Text className={Style.input_text}>Parameter Name:</Text>
                  <Select
                    showSearch
                    value={filter.parameterName}
                    className={Style.custom_select}
                    placeholder={"Select"}
                    onChange={(value) =>
                      handleParameterChange({ index, value })
                    }
                    options={[...parameterOptions].sort(
                      (a, b) => a.label.localeCompare(b.label) // Sort alphabetically by 'label'
                    )}
                    disabled={filter?.isNonEditable}
                  />
                </Flex>
              </Col>

              <Col sm={columnWidth} xs={24}>
                <Flex align="center" gap={10}>
                  <Text className={Style.input_text}>Comparison:</Text>
                  <Select
                    value={filter.comparison}
                    className={Style.custom_select}
                    placeholder={"Select"}
                    onChange={(value) =>
                      handleComparisonChange({ index, value })
                    }
                    options={getComparisonOptions({ filter, parameterTypes })}
                    disabled={filter?.isNonEditable}
                  />
                </Flex>
              </Col>

              {isComparisonTypeVisible && (
                <Col sm={columnWidth} xs={24}>
                  <Flex align="center" gap={10}>
                    <Text className={Style.input_text}>Comparison Type:</Text>
                    <Select
                      value={filter.comparisonType}
                      className={Style.custom_select}
                      placeholder={"Select"}
                      onChange={(value) =>
                        handleComparisonTypeChange({ index, value })
                      }
                      options={[
                        { label: "Value", value: "value" },
                        { label: "Column", value: "column" },
                      ]}
                      disabled={filter?.isNonEditable}
                    />
                  </Flex>
                </Col>
              )}

              <Col sm={columnWidth} xs={24}>
                <FilterValueInput
                  filter={filter}
                  columnOptionsMap={columnOptionsMap}
                  handleInputChange={handleInputChange}
                  parameterTypes={parameterTypes}
                  index={index}
                />
              </Col>
            </Row>
          );
        })}

        {/* Add campaign button */}
        <Flex justify="end">
          <button className={Style.add_button} onClick={handleAddFilter}>
            <div className={Style.icon_img}>
              <img src={ADD_ICON_IMG} alt="add-icon" />
            </div>
          </button>
        </Flex>
      </Flex>

      <CampaignDeleteModal
        modalStatus={showDeleteModal}
        setModalStatus={setShowDeleteModal}
        handleDeleteCampaign={handleDeleteFilter}
        desc={`category`}
        isLoading={isDeleteLoading}
      />
    </Flex>
  );
}
CategoriesComponent.propTypes = {
  campaignData: CAMPAIGN_DATA_PROP_TYPES,
  setCampaignData: PropTypes.func,
};

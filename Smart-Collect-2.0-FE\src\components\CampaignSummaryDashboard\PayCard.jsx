import React from "react";
import Style from "./_style.module.scss";
import { Flex, Typography } from "antd";
import PropTypes from "prop-types";
import classNames from "classnames";

const { Text } = Typography;

export function PayCard({
  accounts,
  amount,
  icon,
  title,
  color,
  isVisible = false,
  onlyName = false,
}) {
  return !onlyName ? (
    <div className={Style.card}>
      <Flex
        justify="space-between"
        align="center"
        style={{ borderBottom: "1px solid black" }}
      >
        <Text className={Style.title} style={{ color }}>
          <span className={`text-${color}-500 mr-2`}>●</span> {title}
        </Text>

        <div className={Style.img_div}>
          <img src={icon} alt={title} />
        </div>
      </Flex>
      <p className={Style.text}>
        Account:{" "}
        <span
          style={{
            color: "#1D3261",
            fontSize: "16px",
            marginInlineStart: "1rem",
          }}
        >
          {accounts}
        </span>
      </p>
      {!isVisible && (
        <p className={Style.text}>
          Amount:{" "}
          <span
            style={{
              color: "#1D3261",
              fontSize: "16px",
              marginInlineStart: "1rem",
            }}
          >
            {amount}
          </span>
        </p>
      )}
    </div>
  ) : (
    <div className={classNames(Style.card, Style.no_content_card)}>
      <Flex
        justify="space-between"
        align="center"
        style={{ padding: "0 1rem" }}
      >
        <div className={Style.img_div}>
          <img src={icon} alt={title} />
        </div>
        <Text className={Style.title} style={{ color }}>
          <span className={`text-${color}-500 mr-2`}>●</span> {title}
        </Text>
      </Flex>
    </div>
  );
}

// Define the types
PayCard.propTypes = {
  accounts: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  amount: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  icon: PropTypes.string,
  title: PropTypes.string,
  color: PropTypes.string,
  isVisible: PropTypes.bool,
  onlyName: PropTypes.bool,
};

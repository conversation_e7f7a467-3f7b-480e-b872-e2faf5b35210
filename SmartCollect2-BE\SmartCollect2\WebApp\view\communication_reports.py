from django.db import connection
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from DB.db_manager import db_manager
from WebApp import models as webmodels

class CommunicationSummaryReport(APIView):
    permission_classes = (IsAuthenticated,)

    def getdata(self, branchls):
        try:
            result_rows, _ = db_manager.execute_query_with_positional_params(
                f"""
                    select * from communication_summary_report(array[{branchls}]);
                """,
                (branchls,)
            )
            
            # Calculate totals
            total_whatsapp = 0
            total_ivr = 0
            total_sms = 0
            total_blaster = 0
            total_communications = 0
            total_voicebot = 0
            
            # Function to recursively find and sum data
            def extract_and_sum_data(data):
                nonlocal total_whatsapp, total_ivr, total_sms, total_blaster, total_communications,total_voicebot
                
                if isinstance(data, dict):
                    # If it's a dictionary with the data we need
                    if 'WhatsAppCount' in data:
                        total_whatsapp += data.get('WhatsAppCount', 0)
                        total_ivr += data.get('IVRCount', 0)
                        total_sms += data.get('SMSCount', 0)
                        total_blaster += data.get('BlasterCount', 0)
                        total_voicebot += data.get("VoicebotCount",0)
                        total_communications += data.get('TotalCommunications', 0)
                elif isinstance(data, (list, tuple)):
                    # If it's a list or tuple, recursively process each item
                    for item in data:
                        extract_and_sum_data(item)
            
            # Process the result_rows
            extract_and_sum_data(result_rows)
            
            # Create totals dictionary
            totals = {
                'TotalWhatsApp': total_whatsapp,
                'TotalIVR': total_ivr,
                'TotalSMS': total_sms,
                'TotalBlaster': total_blaster,
                'Total Voice': total_voicebot,
                'TotalCommunications': total_communications
            }
            
            # Return both original data and totals
            return Response({
                'totals': totals,
                'data': result_rows
            })
            
        except Exception as e:
            print(f"Error occurred: {e}")
            return Response(
                {
                    "message": "An error occurred!",
                    "error": str(e),
                    "status": "error",
                    "branchls": branchls,
                }
            )

    def get(self, request):
        branchls = []
        if request.user.Designation == "HO":
            branchls = list(
                webmodels.BranchMst.objects.filter(BankMstID=request.user.BankMstID)
                .values_list("BranchMstID", flat=True)
                .distinct()
            )
        else:
            branchls = list(
                webmodels.BranchMst.objects.filter(
                    BankMstID=request.user.BankMstID,
                    BranchMstID=request.user.BranchMstID_id,
                ).values_list("BranchMstID", flat=True)
            )
        return self.getdata(branchls)

    def post(self, request):
        branchls = request.data.get("branch_id")
        if branchls:
            return self.getdata(branchls)
        branchls = []
        if request.user.Designation == "HO":
            branchls = list(
                webmodels.BranchMst.objects.filter(BankMstID=request.user.BankMstID)
                .values_list("BranchMstID", flat=True)
                .distinct()
            )
        else:
            branchls = list(
                webmodels.BranchMst.objects.filter(
                    BankMstID=request.user.BankMstID,
                    BranchMstID=request.user.BranchMstID_id,
                ).values_list("BranchMstID", flat=True)
            )
        return self.getdata(branchls)
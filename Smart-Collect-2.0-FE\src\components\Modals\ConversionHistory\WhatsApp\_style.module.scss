@import url("../../../../index.css");

$what-bg:#E9FFED;
$what-color:#60D669;
$response-bg:#DB3939;

.what_container{  
    .date{
      color: var(--dark-blue);
      font-family: 'Kanit','Courier New', Courier, monospace;
      font-weight: 400;
    }
    .what_chat_box{
        background-color: $what-bg;
        padding: 1rem;
        border-radius: 10px;
    }
    .img{
        width: 13px;
    }
    .what_text{
        color: $what-color;
        font-family: 'Kanit','Courier New', Courier, monospace;
    }
    .response_text{
       background-color: $response-bg;
       color: white;
       padding: 0.5rem 3rem;
       border-radius: 8px;
       font-family: 'Kanit','Courier New', Courier, monospace;
       font-weight: 400;
       font-size: 11px;
    }
    .belongs_text{
        font-size: 13px;
        color: var(--dark-blue);
        font-weight: 700;
    }
}

// Responsive 
@media only screen and (max-width:768px) {
    .what_container{
        .date{
            font-size: 11px;
        }
        .what_chat_box{
            padding:0.5rem 1rem;
        }
        .img{
            width: 11px;
        }
        .what_text{
            font-size: 11px;
        }
    }
}
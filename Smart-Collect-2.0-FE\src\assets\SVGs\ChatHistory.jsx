import PropTypes from "prop-types";
import React from "react";

export default function ChatHistorySVG({ width = 24, height = 24 }) {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 25 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2.212 22V4c0-.55.196-1.02.588-1.412A1.93 1.93 0 014.212 2h16c.55 0 1.02.196 1.413.588.392.392.588.863.587 1.412v12c0 .55-.196 1.021-.587 1.413a1.92 1.92 0 01-1.413.587h-14l-4 4zm3.15-6h14.85V4h-16v13.125L5.362 16z"
        fill="#fff"
      />
      <path
        d="M12.695 14.166c-.931 0-1.742-.304-2.435-.912a3.502 3.502 0 01-1.2-2.296h.463a3.21 3.21 0 001.078 1.966 3.068 3.068 0 002.094.784c.893 0 1.651-.311 2.274-.934.622-.622.934-1.38.934-2.274 0-.894-.311-1.653-.934-2.275-.623-.622-1.381-.934-2.274-.934-.475 0-.92.1-1.338.301-.417.2-.785.476-1.104.827h1.137v.459H9.486V6.975h.459v1.094c.354-.389.77-.692 1.245-.91a3.592 3.592 0 011.505-.326c.508 0 .984.096 1.429.287a3.693 3.693 0 011.95 1.95c.192.445.288.921.287 1.43 0 .508-.096.985-.287 1.429a3.69 3.69 0 01-1.95 1.95c-.444.192-.92.287-1.43.287zm1.468-1.9l-1.671-1.671V8.208h.458v2.196l1.537 1.538-.324.324z"
        fill="#fff"
      />
    </svg>
  );
}
ChatHistorySVG.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  color: PropTypes.string,
};

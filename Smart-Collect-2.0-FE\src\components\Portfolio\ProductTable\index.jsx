import { Table, Typography } from 'antd'
import React from 'react'
import Style from "../_style.module.scss";
import PropTypes from 'prop-types';

const {Text} = Typography;

export function ProductTable({data, activePortfolioType, pagination, onPaginationChange}) {
    const columnMap = {
        LoanType: "Loan Type",
        [`${activePortfolioType}_customers`]: "Active Count",
        [`${activePortfolioType}_account_percentage`]: "Active Count (Contribution)",
        [`${activePortfolioType}_principleoutstanding_outstanding`]: "POS Count",
        [`${activePortfolioType}_amount_percentage`]: "POS Count (Contribution)"
    };
    const columns = Object.entries(columnMap).map(([key, title]) => ({
        title,
        dataIndex: key,
        render: (value) => (
          <Text style={title.includes("Contribution") ? { color: "#0D7430" } : {}}>
            {value}
          </Text>
        )
      }));
    const dataSource = Array.isArray(data)? data?.map((data, i) => ({
        key: i+1,
        ...data
    })):[];

  return (
    <Table
            bordered
            virtual
            className={Style.CustomTable}
            columns={columns}
            dataSource={dataSource}
            scroll={{
                x:800,
                y: 360,
            }}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              onChange: onPaginationChange,
              showSizeChanger:false
            }}
        />
  )
}

ProductTable.propTypes={
  data: PropTypes.array,
  activePortfolioType: PropTypes.string,
  pagination: PropTypes.object,
  onPaginationChange: PropTypes.func
}
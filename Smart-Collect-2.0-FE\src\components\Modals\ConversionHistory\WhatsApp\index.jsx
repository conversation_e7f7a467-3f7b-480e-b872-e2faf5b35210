import React from "react";
import { Flex, Typography } from "antd";
import WHAT_IMG from "../../../../assets/Images/what.svg";
import Style from "./_style.module.scss";
import { ChatComponent } from "./ChatComponent";
import { handleDateFormatter } from "../../../../constant";
import PropTypes from "prop-types";

const { Text } = Typography;

export function WhatsApp({ whatsappData }) {
  // Group messages by conversation_date
  const groupedData = (whatsappData ?? []).reduce((acc, conversation) => {
    const { conversation_date, conversation_json, person } = conversation;
    // Skip if conversation_json is null or not an array
    if (!conversation_json) return acc;

    if (!acc[conversation_date]) {
      acc[conversation_date] = {
        messages: [],
        person,
      };
    }
    acc[conversation_date].messages.push(...conversation_json);
    return acc;
  }, {});
  return (
    <Flex className={Style.what_container} vertical gap={10}>
      {Object.entries(groupedData).map(([date, data], index) => {
        const { messages, person } = data;
        console.log(person);
        const modifyPersonSpell =
          person?.toLowerCase() === "gaurantator" ? "Guarantor" : person;
        return (
          <Flex key={`${index}-${date}`} vertical gap={10}>
            {/* Display conversation date once */}
            <Flex justify="center">
              <Text className={Style.date}>{handleDateFormatter(date)}</Text>
            </Flex>

            {/* AI Chats */}
            <Flex className={Style.what_chat_box} vertical gap={10}>
              <Flex vertical>
                <Flex justify="center" gap={5} align="center">
                  <img
                    src={WHAT_IMG}
                    className={Style.img}
                    alt="WhatsApp Icon"
                  />
                  <Text className={Style.what_text}>WhatsApp Message</Text>
                </Flex>
                <Flex justify="center">
                  <Text className={Style.belongs_text}>
                    ({modifyPersonSpell || "Unknown"})
                  </Text>
                </Flex>
              </Flex>

              <Flex className={Style.response_container} justify="center">
                {/* Pass combined messages to ChatComponent */}
                <ChatComponent messages={messages} />
              </Flex>
            </Flex>
          </Flex>
        );
      })}
    </Flex>
  );
}

WhatsApp.propTypes = {
  whatsappData: PropTypes.array,
};

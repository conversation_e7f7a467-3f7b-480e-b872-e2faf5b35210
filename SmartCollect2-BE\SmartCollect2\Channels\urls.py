from django.urls import path
from rest_framework.routers import Default<PERSON><PERSON><PERSON>

import Channels.views as channelviews
from Channels.views import create_payment_view, razorpay_webhook

router = DefaultRouter()
router.register(
    r"v1/tbldialerhistory",
    channelviews.TBLDialerHistViewSet,
    basename="tbldialerhistory",
)

unencrypted_urls = [
    path(
        "v1/WhatsAppWebHook/",
        channelviews.WhatsAppWebHook.as_view(),
        name="WhatsAppWebHook",
    ),
    path(
        "v1/WhatsAppStatus/",
        channelviews.WhatsAppStatus.as_view(),
        name="WhatsAppStatus",
    ),
    path(
        "v1/WhatsAppError/", channelviews.WhatsAppError.as_view(), name="WhatsAppError"
    ),
    path(
        "v1/wh_bot_webhook_smart_ping/",
        channelviews.WhatsAppWebhookHandler.as_view(),
        name="wh_bot_webhook_smart_ping",
    ),
    path(
        "v1/wh_bot_webhook_gapshap/",
        channelviews.WhatsAppWebhookGapShap().as_view(),
        name="wh_bot_webhook_gapshap",
    ),
    path(
        "v1/gapshap_message_status/",
        channelviews.WhatsAppStatusGupshap().as_view(),
        name="WhatsAppStatusGupshap",
    ),
    path(
        "v1/WhatsAppResponseSummary",
        channelviews.WhatsAppResponseSummary.as_view(),
        name="WhatsAppResponseSummary",
    ),
    path(
        "v1/wh_bot_webhook_pinnacle/",
        channelviews.WhatsAppWebhookPinnacle().as_view(),
        name="wh_bot_webhook_pinnacle",
    ),
]

urlpatterns = [
    path(
        "v1/WhatsAppEngine/",
        channelviews.WhatsAppEngine.as_view(),
        name="WhatsAppEngine",
    ),
    # path("v2/WhatsAppWebHook/", channelviews.WhatsAppWebHookNew.as_view(), name="WhatsAppWebHook"),
    path("v1/dialer/", channelviews.DialerView.as_view(), name="dialer"),
    path(
        "v1/ptpreminder/",
        channelviews.SendPtpReminder.as_view(),
        name="SendPtpReminder",
    ),
    path("v1/create-payment/", create_payment_view, name="create-payment"),
    path("v1/razorpaywebhook/", razorpay_webhook, name="razorpay_webhook"),
]

urlpatterns += unencrypted_urls

urlpatterns += router.urls

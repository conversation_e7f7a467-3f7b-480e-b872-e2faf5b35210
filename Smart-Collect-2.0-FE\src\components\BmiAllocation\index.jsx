import React, { useEffect, useState } from "react";
import {
  ALL_CACHE_NAMES,
  LOCAL_KEYS,
  SMART_COLLECT_MENU_IDS,
} from "../../constant";
import { Flex } from "antd";
import AppHeader from "../AppHeader";
import AppEngagements from "../AppEngagements";
import CustomerAllCards from "../CustomerAllCards";
import CustomersTable from "../CustomersTable";
import Style from "./_bmi-allocation.module.scss";
import { AXIOS } from "../../apis/ho-Instance";
import ApplicationLoader from "../ApplicationLoader";
import { encryptBody, isSameEncryptedBody } from "../../utils/crypto";
import {
  getCacheKey,
  getFromCache,
  getPostCacheKey,
  storeToCache,
} from "../../utils/cacheHelper";

const CACHE_NAME = ALL_CACHE_NAMES.DASHBOARD_CACHE;

export default function BmiAllocation() {
  const [isSwitchOn, setIsSwitchOn] = useState(false);
  const [customerData, setCustomerData] = useState([]);
  const [loading, setLoading] = useState(false);
  // Retrieve dates from localStorage
  const storedFilter = localStorage.getItem("filterDate");
  const storedDpd = localStorage.getItem("DashboardDpdCategory");

  const handleGetBMData = async () => {
    setLoading(true);
    const cacheKey = getCacheKey({ endpoint: "bmallocation/" });
    // Check cache
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setCustomerData(cachedData);
      setLoading(false);
    }
    try {
      const response = await AXIOS.get("bmallocation/");
      if (response.status === 200) {
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
        setCustomerData(response.data);
      }
    } catch (error) {
      console.log("Error in BM allocation", error?.message);
    } finally {
      setLoading(false);
    }
  };

  const handleNonResponsible = async ({
    fromDate,
    toDate,
    BranchName,
    dpdRange,
    disbursementID,
  }) => {
    setLoading(true);
    try {
      const body = {
        from_date: fromDate,
        to_date: toDate,
        branch_id: BranchName,
        dpdfrom: dpdRange?.start ?? -1,
        dpdto: dpdRange?.end ?? -1,
        disbursementIDs: disbursementID,
      };
      const cacheKey = getPostCacheKey({ endpoint: "bmallocation/", body });
      const encryptedOldBody = localStorage.getItem(LOCAL_KEYS.HO_PAY_FILTER);
      const sameBody = isSameEncryptedBody({
        newBody: body,
        encryptedOldBody,
      });

      if (sameBody) {
        const cachedData = await getFromCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
        });
        if (cachedData) {
          setCustomerData(cachedData);
          setLoading(false);
        }
      }
      const response = await AXIOS.post("bmallocation/", body);
      // Check the response
      if (response.status === 200) {
        const encrypted = encryptBody(body);
        if (encrypted) {
          localStorage.setItem(LOCAL_KEYS.HO_PAY_FILTER, encrypted);
        }
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
        setCustomerData(response.data);
      }
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoading(false);
    }
  };

  // Apply filter and fetch data
  const applyFilters = async ({
    fromDate,
    toDate,
    BranchName,
    disbursementID,
  }) => {
    await handleNonResponsible({
      fromDate: fromDate,
      toDate: toDate,
      BranchName,
      dpdRange: JSON.parse(storedDpd),
      disbursementID,
    });
  };

  // Clear filters and fetch data
  const ClearFilters = () => {
    handleGetBMData();
    localStorage.removeItem("filterDate");
    localStorage.removeItem(LOCAL_KEYS.HO_PAY_FILTER);
  };

  useEffect(() => {
    if (storedFilter || storedDpd) {
      const { fromDate, toDate, BranchName, disbursementID } = storedFilter
        ? JSON.parse(storedFilter)
        : { fromDate: null, toDate: null, BranchName: null };
      const parsedDpd = storedDpd
        ? JSON.parse(storedDpd)
        : { start: -1, end: -1 };
      handleNonResponsible({
        fromDate,
        toDate,
        BranchName,
        dpdRange: parsedDpd,
        disbursementID,
      });
    } else {
      handleGetBMData();
    }
  }, []);
  return (
    <Flex gap={15} vertical className={Style.bmi_allocation}>
      {/* Header */}
      <AppHeader
        title={`Non-Responsive: ${customerData.length}`}
        isDashboard={false}
        isSwitchOn={isSwitchOn}
        setIsSwitchOn={setIsSwitchOn}
        isDashboardOtherPages={true}
        applyFilters={applyFilters}
        ClearFilters={ClearFilters}
      />

      <Flex gap={15} vertical>
        {/* app engagements */}
        <AppEngagements />

        {/* Details */}
        {loading ? (
          <ApplicationLoader />
        ) : (
          <div className={Style.details}>
            {!isSwitchOn ? (
              <CustomerAllCards
                pageId={SMART_COLLECT_MENU_IDS.BMI_ALLOCATION}
                customerData={customerData}
                modalButtonText={"Feedback"}
                isModal={true}
              />
            ) : (
              <CustomersTable
                title={"Non-Responsive"}
                customerData={customerData}
                pageId={SMART_COLLECT_MENU_IDS.BMI_ALLOCATION}
                modalButtonText={"Feedback"}
              />
            )}
          </div>
        )}
      </Flex>
    </Flex>
  );
}

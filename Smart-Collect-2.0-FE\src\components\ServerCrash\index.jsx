import { Flex } from 'antd'
import React from 'react'
import SERVER_DOWN from "../../assets/Images/server-crash.jpg";
import Style from "./_style.module.scss";

export default function ServerCrash() {
  return (
    <Flex justify='center' align='center' vertical className={Style.container}>
        <div className={Style.img_container}><img src={SERVER_DOWN} alt='server-down'/></div>
        <div className={Style.text_container}>
            <h2 className={Style.title}>Server is temporarily unavailable</h2>
            <Flex align='center' wrap justify='center'>
                <p className={Style.desc}>Please, try again after sometime.</p> 
                <button type="primary" onClick={() => window.location.reload()}>Refresh</button>
            </Flex>
        </div>
    </Flex>
  )
}

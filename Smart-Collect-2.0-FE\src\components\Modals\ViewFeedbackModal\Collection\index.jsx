import React from "react";
import Style from "./_collection.module.scss";
import {
  Flex,
  InputNumber,
  Typography,
  DatePicker,
  Segmented,
} from "antd";
import dayjs from "dayjs";
import PropTypes from "prop-types";
const { Text } = Typography;

export function Collection({ latestFeedBack }) {
  return (
    <div className={Style.collection}>
      <Flex vertical gap={20} justify="center">
        {/* date */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Date:</Text>
          <DatePicker
            className={Style.date_picker}
            placeholder="dd/mm/yy"
            value={
              latestFeedBack?.CollectionDate
                ? dayjs(latestFeedBack.CollectionDate)
                : null
            }
            disabled
          />
        </Flex>

        {/* amount */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Amount:</Text>
          <InputNumber 
          value={latestFeedBack.CollectionAmount} 
          disabled 
          formatter={(value) => {
            const num = Number(value);
            return isNaN(num) ? "" : num.toLocaleString("en-IN");
          }}
          parser={(value) => value?.replace(/[₹,]/g, "")}/>
        </Flex>
        {latestFeedBack?.promise_amount && (
          <>
            {/* promise date */}
            <Flex justify="space-between" align="center">
              <Text className={Style.text}>Promise Date:</Text>
              <DatePicker
                className={Style.date_picker}
                placeholder="dd/mm/yy"
                value={
                  latestFeedBack?.promise_date
                    ? dayjs(latestFeedBack.promise_amount)
                    : null
                }
                disabled
              />
            </Flex>

            {/* amount */}
            <Flex justify="space-between" align="center">
              <Text className={Style.text}>Promise Amount:</Text>
              <InputNumber 
                value={latestFeedBack.promise_amount} 
                disabled 
                formatter={(value) => {
                  const num = Number(value);
                  return isNaN(num) ? "" : num.toLocaleString("en-IN");
                }}
                parser={(value) => value?.replace(/[₹,]/g, "")}/>
            </Flex>
          </>
        )}

        {/* mode */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Mode of Payment:</Text>

          <Segmented
            value={latestFeedBack.mode_of_payment}
            style={{
              marginBottom: 8,
            }}
            options={["Online", "Offline"]}
            disabled
          />
        </Flex>
      </Flex>
    </div>
  );
}

Collection.propTypes={
  latestFeedBack: PropTypes.object
}
# Generated by Django 5.1.5 on 2025-02-18 06:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("WebApp", "0002_update_usermst_loginhistory"),
    ]

    operations = [
        migrations.CreateModel(
            name="AccountSummary",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "CustomerInfoID",
                    models.CharField(blank=True, max_length=500, null=True),
                ),
                (
                    "CustomerCode",
                    models.CharField(blank=True, max_length=500, null=True),
                ),
                (
                    "CustomerName",
                    models.CharField(blank=True, max_length=500, null=True),
                ),
                (
                    "MobileNumber",
                    models.CharField(blank=True, max_length=500, null=True),
                ),
                ("Gender", models.CharField(blank=True, max_length=500, null=True)),
                ("DateOfBirth", models.Date<PERSON>ield(blank=True, null=True)),
                (
                    "DisbursementID",
                    models.Char<PERSON>ield(blank=True, max_length=500, null=True),
                ),
                ("DisbursementDate", models.DateField(blank=True, null=True)),
                (
                    "DisbursementAmt",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0.0,
                        max_digits=15,
                        null=True,
                    ),
                ),
                ("LoanType", models.CharField(blank=True, max_length=50, null=True)),
                ("DaysPastDue", models.IntegerField(blank=True, null=True)),
                (
                    "LoanClassification",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                ("LastPaymentDate", models.DateField(blank=True, null=True)),
                (
                    "LastCollectedAmount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0.0,
                        max_digits=15,
                        null=True,
                    ),
                ),
                (
                    "CurrentBalance",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0.0,
                        max_digits=15,
                        null=True,
                    ),
                ),
                (
                    "OverDueAmt",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0.0,
                        max_digits=15,
                        null=True,
                    ),
                ),
                (
                    "PrincipleOutstanding",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0.0,
                        max_digits=15,
                        null=True,
                    ),
                ),
                (
                    "InterestOutstanding",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0.0,
                        max_digits=15,
                        null=True,
                    ),
                ),
                (
                    "TotalOutstanding",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0.0,
                        max_digits=15,
                        null=True,
                    ),
                ),
                (
                    "PrincipalPending",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0.0,
                        max_digits=15,
                        null=True,
                    ),
                ),
                (
                    "InterestPending",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0.0,
                        max_digits=15,
                        null=True,
                    ),
                ),
                (
                    "TotalPending",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0.0,
                        max_digits=15,
                        null=True,
                    ),
                ),
                ("ClosingDate", models.DateField(blank=True, null=True)),
                ("IsActive", models.BooleanField(default=True)),
                (
                    "EMIAmount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0.0,
                        max_digits=15,
                        null=True,
                    ),
                ),
                ("NextEMIDate", models.DateField(blank=True, null=True)),
                ("LoanMstID", models.IntegerField()),
                (
                    "Extra_Column1",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "Extra_Column2",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "Extra_Column3",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "Extra_Column4",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "Extra_Column5",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("As_On_Date", models.DateField(blank=True, null=True)),
                ("Branch_id", models.CharField(blank=True, max_length=255, null=True)),
                ("Branch", models.CharField(blank=True, max_length=255, null=True)),
                ("Region_id", models.CharField(blank=True, max_length=255, null=True)),
                ("State_id", models.CharField(blank=True, max_length=255, null=True)),
                ("Region", models.CharField(blank=True, max_length=255, null=True)),
                ("State", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "CollectionOfficerID",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "CollectionOfficerName",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
            ],
            options={
                "db_table": "AccountSummary",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="CustomerMst",
            fields=[
                ("CustomerMstID", models.AutoField(primary_key=True, serialize=False)),
                ("CustomerID", models.CharField(blank=True, max_length=50, null=True)),
                ("CustomerCode", models.IntegerField(blank=True, null=True)),
                ("CustomerName", models.CharField(max_length=100)),
                (
                    "MobileNumber",
                    models.CharField(blank=True, max_length=15, null=True),
                ),
                ("Gender", models.CharField(blank=True, max_length=50, null=True)),
                ("DateOfBirth", models.DateField(blank=True, null=True)),
                ("IsDeath", models.BooleanField(blank=True, null=True)),
                ("CreatedDate", models.DateField(auto_now_add=True)),
                ("UpdatedDate", models.DateField(auto_now=True)),
                ("EmailID", models.CharField(blank=True, max_length=100, null=True)),
                ("Latitude", models.CharField(blank=True, max_length=100, null=True)),
                ("Longitude", models.CharField(blank=True, max_length=100, null=True)),
                ("PanNo", models.CharField(blank=True, max_length=100, null=True)),
                ("Pincode", models.IntegerField(blank=True, null=True)),
                (
                    "CustomerAddress",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "ExtraColumn1",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn2",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn3",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn4",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn5",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
            ],
            options={
                "db_table": "CustomerMst",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="LanguageMst",
            fields=[
                (
                    "LngMstID",
                    models.AutoField(
                        db_column="LngMstID", primary_key=True, serialize=False
                    ),
                ),
                ("Language", models.CharField(max_length=255)),
                ("IsActive", models.BooleanField(default=True)),
                ("CreatedDate", models.DateField(auto_now_add=True)),
                ("UpdatedDate", models.DateField(auto_now=True)),
                (
                    "ExtraColumn1",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn2",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn3",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn4",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn5",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
            ],
            options={
                "db_table": "LanguageMst",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="LoanMst",
            fields=[
                ("LoanMstID", models.AutoField(primary_key=True, serialize=False)),
                (
                    "DisbursementID",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                ("DisbursementDate", models.DateField(blank=True, null=True)),
                (
                    "DisbursementAmt",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0.0,
                        max_digits=15,
                        null=True,
                    ),
                ),
                ("LoanType", models.CharField(blank=True, max_length=50, null=True)),
                ("InstallmentStartDate", models.DateField(blank=True, null=True)),
                (
                    "RateOfInterest",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0.0,
                        max_digits=15,
                        null=True,
                    ),
                ),
                ("RepaymentTenure", models.IntegerField(blank=True, null=True)),
                (
                    "EMIAmount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0.0,
                        max_digits=15,
                        null=True,
                    ),
                ),
                ("PaymentFrequency", models.IntegerField(blank=True, null=True)),
                ("LastModifiedDate", models.DateField(blank=True, null=True)),
                ("IsActive", models.BooleanField(blank=True, null=True)),
                ("ClosingDate", models.DateField(blank=True, null=True)),
                (
                    "ExtraColumn1",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn2",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn3",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn4",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn5",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "CollectionOfficerID",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "CollectionOfficerName",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("BMID", models.IntegerField(blank=True, null=True)),
            ],
            options={
                "db_table": "LoanMst",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Response",
            fields=[
                (
                    "ResponseID",
                    models.AutoField(
                        db_column="ResponseID", primary_key=True, serialize=False
                    ),
                ),
                ("LoanMstID", models.IntegerField(db_column="LoanMstID")),
                ("BlasterQueueID", models.IntegerField(db_column="BlasterQueueID")),
                ("IVRQueueID", models.IntegerField(db_column="IVRQueueID")),
                ("VoiceBotQueueID", models.IntegerField(db_column="VoiceBotQueueID")),
                ("WhatsappQueueID", models.IntegerField(db_column="WhatsappQueueID")),
                ("AllocationID", models.IntegerField(db_column="AllocationID")),
                ("FeedbackID", models.IntegerField(db_column="FeedbackID")),
                (
                    "ModeOfPayment",
                    models.CharField(db_column="ModeOfPayment", max_length=255),
                ),
                (
                    "ExtraColumn1",
                    models.CharField(
                        blank=True, db_column="ExtraColumn1", max_length=255, null=True
                    ),
                ),
                (
                    "ExtraColumn2",
                    models.CharField(
                        blank=True, db_column="ExtraColumn2", max_length=255, null=True
                    ),
                ),
                (
                    "ExtraColumn3",
                    models.CharField(
                        blank=True, db_column="ExtraColumn3", max_length=255, null=True
                    ),
                ),
                (
                    "ExtraColumn4",
                    models.CharField(
                        blank=True, db_column="ExtraColumn4", max_length=255, null=True
                    ),
                ),
                (
                    "ExtraColumn5",
                    models.CharField(
                        blank=True, db_column="ExtraColumn5", max_length=255, null=True
                    ),
                ),
                ("PromiseDateTime", models.DateTimeField(db_column="PromiseDateTime")),
                ("Status", models.CharField(db_column="Status", max_length=255)),
                (
                    "Amount",
                    models.DecimalField(
                        db_column="Amount", decimal_places=2, max_digits=15
                    ),
                ),
                (
                    "ResponseDateTime",
                    models.DateTimeField(db_column="ResponseDateTime"),
                ),
            ],
            options={
                "db_table": "Response",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="UserFeedback",
            fields=[
                ("UserFeedbackID", models.AutoField(primary_key=True, serialize=False)),
                ("FeedbackDate", models.DateField()),
                ("AgreedToPay", models.BooleanField()),
                ("RefusedToPay", models.BooleanField()),
                ("WrongNumber", models.BooleanField()),
                ("CustomerBusy", models.BooleanField()),
                ("AgreedToDate", models.DateField(blank=True, null=True)),
                (
                    "AgreedToAmount",
                    models.DecimalField(decimal_places=2, max_digits=10),
                ),
                ("ReasonOfRefusal", models.TextField(blank=True, null=True)),
                (
                    "ExtraColumn1",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn2",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn3",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn4",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn5",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("BankMstID", models.IntegerField()),
            ],
            options={
                "db_table": "UserFeedback",
                "managed": False,
            },
        ),
        migrations.AlterModelOptions(
            name="loginhistory",
            options={"managed": False, "ordering": ["-created_at"]},
        ),
        migrations.AlterModelOptions(
            name="usermst",
            options={
                "managed": False,
                "ordering": ["BankMstID", "BranchMstID", "UserID"],
            },
        ),
    ]

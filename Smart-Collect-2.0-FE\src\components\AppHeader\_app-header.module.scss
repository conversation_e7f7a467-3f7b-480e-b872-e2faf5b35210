@import url("../../index.css");

$light-blue: #e3f5f6;

.app_header {
  // padding: 0.8rem 0;
  padding: 0.8rem 2rem;
  position: fixed;
  top: 64px;
  z-index: 108;
  background-color: white;
  width: 100%;
  // width: calc(100% - 4rem);
  // margin: 0 2rem;

  .img_container {
    width: 20px;
    height: 20px;
  }
  .title {
    margin: 0;
    color: #407bff;
    font-family: "Kanit", "Courier New", Courier, monospace;
    font-weight: 400;
    font-size: 18px;
  }
  .back {
    width: 8px;
    cursor: pointer;
    display: flex;
    img {
      width: 100%;
      object-fit: "contain";
    }
  }
  .bank_name {
    color: #1651d7;
    font-weight: 500;
    font-family: "Kanit", "Courier New", Courier, monospace;
    text-transform: uppercase;
    font-size: 20px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
  .custom_select {
    width: 100%;
    :global(.ant-select-selector) {
      background-color: $light-blue !important;
      box-shadow: none !important;
      border: none !important;
      outline: none;
      font-family: "Kanit", "Courier New", Courier, monospace;
    }
    :global(.ant-select-selection-item) {
      font-weight: 400;
      color: var(--dark-blue);
      font-family: "Kanit", "Courier New", Courier, monospace;
    }
  }
  .reason_select {
    width: 100%;
    max-width: 150px;

    :global(.ant-select-selector) {
      background-color: var(--dark-blue) !important;
      box-shadow: none !important;
      border: none !important;
      outline: none;
      font-family: "Kanit", "Courier New", Courier, monospace;
    }
    :global(.ant-select-selection-item) {
      font-weight: 400;
      color: white;
      font-family: "Kanit", "Courier New", Courier, monospace;
    }
    :global(.ant-select-selection-placeholder) {
      color: lightgray;
    }
    :global(.ant-select-selection-search-input) {
      color: white !important;
    }
    :global(.ant-select-arrow) {
      color: white;
    }
  }
  .end_column {
    width: 450px;
  }
}

.container {
  position: relative;

  .button_container {
    background-color: white;
    color: white;
    cursor: pointer;
    padding: 0.5rem;
    width: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 20px;
      object-fit: contain;
    }
  }

  .modal {
    position: absolute;
    right: 0;
    top: 0;
    background-color: $light-blue;
    box-shadow: 4px 4px 4px 0px #00000040;
    width: 300px;
    padding: 0.4rem 1rem;
    z-index: 1;
    border-radius: 10px;

    .text {
      text-align: center;
      color: var(--dark-blue);
      font-family: "Kanit", "Courier New", Courier, monospace;
      font-size: 16px;
      font-weight: bold;
    }

    .fields {
      color: var(--dark-blue);
      font-family: "Kanit", "Courier New", Courier, monospace;
    }

    .apply {
      background-color: var(--dark-blue) !important;
      color: white !important;
      border: none;
      outline: none;
      box-shadow: none;
      font-family: "Kanit", "Courier New", Courier, monospace;
      padding: 1rem 2rem;
      margin: 1rem 0;
    }

    .clear {
      background-color: $light-blue !important;
      color: var(--dark-blue) !important;
      outline: none;
      box-shadow: none;
      font-family: "Kanit", "Courier New", Courier, monospace;
      border: 1px solid #0f2050 !important;
      padding: 1rem 2rem;
      margin: 1rem 0;
    }

    .custom_select {
      :global(.ant-select-selector) {
        background-color: $light-blue;
        box-shadow: none !important;
        border: none;
        outline: none;
        border-bottom: 1px solid var(--dark-blue) !important;
        font-family: "Kanit", "Courier New", Courier, monospace;
      }
      :global(.ant-select-selection-item) {
        font-weight: 400;
        color: var(--dark-blue);
        font-family: "Kanit", "Courier New", Courier, monospace;
      }
    }

    .date_picker {
      border: none;
      outline: none;
      box-shadow: none;
      border-bottom: 1px solid var(--dark-blue);
      background-color: transparent;
      :global(.ant-picker-input > input) {
        font-weight: 300;
        color: var(--dark-blue);
        font-family: "Kanit", "Courier New", Courier, monospace;

        &::placeholder {
          font-weight: 600;
        }
      }
      &::placeholder {
        color: #0f20504a;
        font-weight: 600;
      }
    }

    .custom_input {
      background-color: $light-blue;
      caret-color: #407bff;
      padding: 0.3rem 0.5rem;
      width: 100%;
      text-transform: capitalize;
      border-bottom: 1px solid var(--dark-blue) !important;

      &:global(.ant-input-outlined) {
        border: none;
        box-shadow: none;
        color: var(--dark-blue);
        font-weight: 400;
        border-radius: 6px;
        font-family: "Kanit", "Courier New", Courier, monospace;
      }
    }
  }
}

// Responsive view
@media screen and (max-width: 1024px) {
  .container {
    .button_container {
      padding: 0;
      img {
        width: 14px;
      }
    }
  }
}
// Responsive view
@media screen and (max-width: 768px) {
  .app_header {
    padding: 0.5rem;
    flex-direction: column;
    align-items: start;
    gap: 0 !important;

    .title {
      font-size: 13px;
    }

    .back {
      width: 5px;
    }

    .bank_name {
      font-size: 12px;
      position: static;
      display: block;
      text-align: center;
      transform: translate(0);
    }

    .img_container {
      width: 12px;
    }
    .end_column,
    .middle_column {
      width: 100%;
    }
    .end_column {
      justify-content: end;
    }
    .custom_select {
      max-width: 200px;
      padding: 0px 4px;
      height: 19px;

      :global(.ant-select-selection-placeholder) {
        padding-inline-end: 0px;
        font-size: 10px;
      }
    }
  }
}

import React, { useEffect, useState } from "react";
import AppHeader from "../AppHeader";
import { Flex, message, Table, Typography } from "antd";
import Style from "./_style.module.scss";
import {
  ALL_CACHE_NAMES,
  SMART_COLLECT_MENU_IDS,
  handleDownloadTable,
} from "../../constant";
import ApplicationLoader from "../ApplicationLoader";
import { Link } from "react-router";
import { AXIOS } from "../../apis/ho-Instance";
import DOWNLOAD_IMG from "../../assets/Images/download.svg";
import {
  getCacheKey,
  getFromCache,
  storeToCache,
} from "../../utils/cacheHelper";

const { Text } = Typography;
const CACHE_NAME = ALL_CACHE_NAMES.ANALYSIS_CACHE;

export default function DPDWiseResponse() {
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const handleFetchData = async () => {
    setIsLoading(true);
    const cacheKey = getCacheKey({ endpoint: "dpdwise-response-analysis/" });
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setData(cachedData);
      setIsLoading(false);
    }
    try {
      const response = await AXIOS.get("dpdresponseanalysis/");
      if (response.status === 200) {
        setData(response.data);
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
      }
    } catch (error) {
      console.log("Error in dpd response", error);
    } finally {
      setIsLoading(false);
    }
  };

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      sorter: (a, b) => a.key - b.key,
      width: 90,
      render: (_, data) => <Text>{data?.key}.</Text>,
    },
    {
      title: "DPD Categories",
      dataIndex: "DPD_Range",
      sorter: (a, b) => a.DPD_Range - b.DPD_Range,
      render: (State) => (
        <Text className={Style.blueText}>{State || "--"}</Text>
      ),
    },
    {
      title: "Response Categories",
      dataIndex: "ResponseCategories",
      children: [
        {
          title: "Promised to pay",
          dataIndex: "Ready_to_pay",
          sorter: (a, b) => a.Ready_to_pay - b.Ready_to_pay,
          render: (value) => (
            <Link
              to={`/ho/${SMART_COLLECT_MENU_IDS.DASHBOARD}/${SMART_COLLECT_MENU_IDS.READY_TO_PAY}`}
            >
              <Text className={Style.lan}>{value || 0}</Text>
            </Link>
          ),
        },
        {
          title: "Refused to pay",
          dataIndex: "RefusedToPay",
          sorter: (a, b) => a.RefusedToPay - b.RefusedToPay,
          render: (value) => (
            <Link
              to={`/ho/${SMART_COLLECT_MENU_IDS.DASHBOARD}/${SMART_COLLECT_MENU_IDS.REFUSED_TO_PAY}`}
            >
              <Text className={Style.lan}>{value || 0}</Text>
            </Link>
          ),
        },
        {
          title: "Already paid",
          dataIndex: "AlreadyPaid",
          sorter: (a, b) => a.AlreadyPaid - b.AlreadyPaid,
          render: (value) => (
            <Link
              to={`/ho/${SMART_COLLECT_MENU_IDS.DASHBOARD}/${SMART_COLLECT_MENU_IDS.ALREADY_PAY}`}
            >
              <Text className={Style.lan}>{value || 0}</Text>
            </Link>
          ),
        },
        {
          title: "Wrong Number",
          dataIndex: "WrongNumber",
          sorter: (a, b) => a.WrongNumber - b.WrongNumber,
          render: (value) => (
            <Link
              to={`/ho/${SMART_COLLECT_MENU_IDS.DASHBOARD}/${SMART_COLLECT_MENU_IDS.WRONG_NUMBER}`}
            >
              <Text className={Style.lan}>{value || 0}</Text>
            </Link>
          ),
        },
        {
          title: "Non-Responsive",
          dataIndex: "BMAllocationCount",
          sorter: (a, b) => a.BMAllocationCount - b.BMAllocationCount,
          render: (value) => (
            <Link
              to={`/ho/${SMART_COLLECT_MENU_IDS.DASHBOARD}/${SMART_COLLECT_MENU_IDS.BMI_ALLOCATION}`}
            >
              <Text className={Style.lan}>{value || 0}</Text>
            </Link>
          ),
        },
        {
          title: "Non-Contactableee",
          dataIndex: "NonContactable",
          sorter: (a, b) => a.NonContactable - b.NonContactable,
          render: (value) => (
            <Link
              to={`/ho/${SMART_COLLECT_MENU_IDS.DASHBOARD}/${SMART_COLLECT_MENU_IDS.NON_CONTACTABLE}`}
            >
              <Text className={Style.lan}>{value || 0}</Text>
            </Link>
          ),
        },
      ],
    },
  ];

  const dataSource = Array.isArray(data)
    ? data?.map((data, i) => ({
        key: i + 1,
        ...data,
      }))
    : [];

  useEffect(() => {
    handleFetchData();
  }, []);
  // Handle downloading
  const handleDownload = async () => {
    if (data.length === 0) {
      return message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: "DPD Response Analysis",
        worksheetName: "DPD-Response-Analysis",
        tableData: data,
      });
    }
  };
  return (
    <Flex vertical gap={20} className={Style.container}>
      <AppHeader title={"DPD Response Analysis"} />
      <div>
        {isLoading ? (
          <ApplicationLoader />
        ) : (
          <>
            <Flex justify="end">
              <button
                className={Style.download_button}
                onClick={handleDownload}
              >
                <img src={DOWNLOAD_IMG} alt="download-button" />
              </button>
            </Flex>
            <Table
              bordered
              virtual
              className={Style.custom_table}
              columns={columns}
              dataSource={dataSource}
              scroll={{
                x: 1600,
                y: 460,
              }}
              pagination={{
                showSizeChanger: false,
              }}
            />
          </>
        )}
      </div>
    </Flex>
  );
}

import React, { useCallback } from 'react'
import { Table,Typography } from 'antd';
import Style from "./_style.module.scss";
import PropTypes from 'prop-types';

const {Text} = Typography;

export function CustomerRiskDistribution({data, handleGetCustomerDetails}) {
  const columns = [
    {
      title: 'Previous month',
      dataIndex: 'dpd_category',
      render:(value)=> <Text className={Style.key}>{value}</Text>
    },
    {
      title: 'Risk',
      dataIndex: 'currentMonth',
      children:[
        {
          title:"High",
          dataIndex:"HIGH",
          render:(value,record)=> <Text style={{cursor:"pointer"}} onClick={()=>{
            handleGetCustomerDetails({riskCategory:"HIGH", dpdRange: record?.dpd_category})
          }}>{value}</Text>
        },
        {
          title:"Low",
          dataIndex:"LOW",
          render:(value, record)=> <Text style={{cursor:"pointer"}} onClick={()=>{
            handleGetCustomerDetails({riskCategory:"LOW", dpdRange: record?.dpd_category})
          }}>{value}</Text>
        },
        {
          title:"Low - Medium",
          dataIndex:"LOW - MEDIUM",
          render:(value, record)=> <Text style={{cursor:"pointer"}} onClick={()=>{
            handleGetCustomerDetails({riskCategory:"LOW - MEDIUM", dpdRange: record?.dpd_category})
          }}>{value}</Text>
        },
        {
          title:"Medium",
          dataIndex:"MEDIUM",
          render:(value, record)=> <Text style={{cursor:"pointer"}} onClick={()=>{
            handleGetCustomerDetails({riskCategory:"MEDIUM", dpdRange: record?.dpd_category})
          }}>{value}</Text>
        },
        {
          title:"Total",
          dataIndex:"Total",
          render:(value, record)=> <Text style={{cursor:"pointer"}} onClick={()=>{
            handleGetCustomerDetails({riskCategory:"Total", dpdRange: record?.dpd_category})
          }}>{value}</Text>
        },
      ]
    },
  ];
    
  const dataSource = Array.isArray(data)
    ? data.filter((item)=> item.dpd_category !== "Total")
    :[];

  // Get the Total row for summary
  const totalRow = Array.isArray(data)
  ? data.find((item) => item.dpd_category === "Total")
  : null;

  const summaryContent = useCallback(
    () => <SummaryComp totalRow={totalRow} columns={columns}/>,
    [totalRow]
  );
  return (
     <Table
        // virtual
        bordered
        rowKey={"dpd_category"}
        className={Style.customTable}
        columns={columns}
        dataSource={dataSource}
        pagination={false}
        summary={summaryContent}/>
  )
}

function SummaryComp({totalRow, columns}){
  if(!totalRow) return null;
  const flattenColumns = (cols) => cols.reduce((acc, col) => {
      if (col.children) {
        acc.push(...flattenColumns(col.children));
      } else {
        acc.push(col);
      }
      return acc;
  }, []);
          return(
            <Table.Summary.Row>
              {flattenColumns(columns).map((col, index) => (
                <Table.Summary.Cell index={index} key={col.dataIndex}>
                  <b>{totalRow[col.dataIndex] ?? ''}</b>
                </Table.Summary.Cell>
              ))}
          </Table.Summary.Row>
          )
}

SummaryComp.propTypes={
  totalRow: PropTypes.object,
  columns: PropTypes.array
}
CustomerRiskDistribution.propTypes={
  data: PropTypes.array, 
  handleGetCustomerDetails: PropTypes.func
}
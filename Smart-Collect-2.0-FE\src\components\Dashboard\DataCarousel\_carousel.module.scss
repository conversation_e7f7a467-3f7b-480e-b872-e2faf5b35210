@import url("../../../index.css");

.slider_container {
  background-color: var(--light-green);
  padding: 0.4rem 0;
  border-radius: 5px;

  .value {
    color: var(--blue);
    margin: 0;
    font-family: 'Kanit','Courier New', Courier, monospace;
    font-weight: 400;

    &.selectedText {
      color: #ffffff !important; 
    }
  }
  .date {
    font-size: 14px;
    font-weight: 500;
    font-family: 'Kanit','Courier New', Courier, monospace;
        
    &.selectedText {
      color: #ffffff !important;
    }
  }
  // Carousel card
  .d_card {
    border-right: 1.66px solid var(--slate);
    cursor: pointer;
    justify-content: center;
    height: 80px;
  }
  
  .card{
    padding:0.5rem;
    &.selected{
    background-color: var(--dark-blue);
    width: 150px;
    margin: 0 auto;
    border-radius: 4px;
    box-shadow: 4px 4px 0px 0px #00000026;
   }
  }

  // ant typography
  :global(.ant-typography) {
    text-align: center;
    display: block;
    width: 100%;
  }

  // slick arrow
  :global(.slick-arrow) {
    background: var(--light-blue);
    height: 25px;
    width: 15px;
    &::before {
      color: var(--blue);
      position: absolute;
      content: "";
      display: inline-block;
      width: 8px;
      height: 8px;
      border: 0 solid var(--blue);
      border-inline-width: 3px 0;
      border-block-width: 3px 0;
      border-radius: 2px !important;
      top: 28%;
    }
  }

  // prev
  :global(.slick-prev) {
    left: 1px;
    z-index: 1;

    &::before {
      transform: rotate(-45deg);
      left: 4px;
    }
  }

  // next
  :global(.slick-next) {
    right: 1px;

    &::before {
      transform: rotate(135deg);
      right: 3px;
    }
  }
}

// Responsive view
@media screen and (max-width:1024px) {
  .slider_container{

    .value{
      font-size: 18px;
    }

    .date{
       font-size: 12px;
    }
  }
}

@media screen and (max-width:568px) {
  .slider_container{
    .d_card{
      border: none;
    }
  }
}
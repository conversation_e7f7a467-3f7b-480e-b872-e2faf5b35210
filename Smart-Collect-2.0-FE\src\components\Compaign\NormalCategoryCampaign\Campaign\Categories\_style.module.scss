@import url("../../../../../index.css");

$light-blue:#E3F5F6;

.filter{
    margin-top: 1rem;
    .text{
        font-size: 18px;
        color: var(--dark-blue);
        font-family: 'Kanit','Courier New', Courier, monospace;
    }
    .filters_container{
        background-color:$light-blue;
        border-radius: 8px;
         padding: 1rem 2rem;

        .input_text{
            font-family: 'Kanit','Courier New', Courier, monospace;
            font-size: 14px;
            color: var(--dark-blue);
            white-space: nowrap;
        }

        .custom_select{
            width: 100%;
            border-radius: 10px;
            box-shadow: 0px 4px 4px 0px #00000040;

            :global(.ant-select-selector){
                color: var(--dark-blue);
                border:none !important;
                box-shadow: none !important;
                font-family: 'Kanit','Courier New', Courier, monospace;
            }     
            :global(.ant-select-selection-item){
                font-weight: 400;
                color: var(--dark-blue);
            }
            :global(.ant-select-selection-placeholder){
                color: #0F205052;
                font-weight: 400;
            }
        }

        .custom_input{
            caret-color: #407BFF;
            padding:0.3rem 0.5rem;
    
            &:global(.ant-input-outlined){
                border:none;
                color: var(--dark-blue);
                font-weight: 400;
                border-radius: 6px;
                box-shadow: 0px 2px 2px 0px #00000040 inset;
                font-family: 'Kanit','Courier New', Courier, monospace;
            }
        }

        .custom_input_number{
            width: 300px;
            :global(.ant-picker-outlined),
            :global(.ant-input-number),
            :global(.ant-input-number-group-addon){
                background-color: #E4F8F9;
                border:none !important;
                width: 0;
                padding: 0 0 0 5px;
            }
            :global(.ant-input-number){
                width: 186px;
            }
            :global(.ant-input-number-input){
               color: var(--dark-blue);
               font-weight: 500;
               font-family: 'Kanit','Courier New', Courier, monospace;
            }
            
            :global(.ant-input-number-outlined){
                &:focus-within{
                   box-shadow:none !important;
                }
            }
        
            :global(.ant-input-number-handler-wrap){
                display: none;
            }
        }

        .date_picker{
            width: 100%;
            border:none;
            outline: none;
            box-shadow: 0px 2px 2px 0px #0000001f;  
            padding: 0.4rem;            
            :global(.ant-picker-input>input){
                font-weight: 400;
                color:var(--dark-blue);
                font-family: 'Kanit','Courier New', Courier, monospace;

                &::placeholder{
                font-weight: 600;  
                }
                
            }
            &::placeholder{
                color: #0F20504A;
                font-weight: 600;
            }
        }

        .add_button{
            background-color: var(--dark-blue);
            color: white;
            border-radius: 50px;
            padding: 0.1rem;
            cursor: pointer;
            height: 30px;
            width: 30px;
            outline: none;
            border: none;
            .icon_img{
                img{
                    width: 100%;
                    object-fit: contain;
                }
            }
        }
    }
}

// Responsive View
@media screen and (max-width:768px) {
    .filter{

        .text{
            font-size: 14px;
        }
      .filters_container{  
         padding: 0.5rem;
         flex-direction: column;
        
        .input_text{
            font-size: 11px;
            width: 100%;
        }
        .text{
            font-size: 11px;
        }
        .custom_input{
            font-size: 10px;
        }
        .custom_select{   
            :global(.ant-select-selection-item){
             font-size: 10px;
            }
            :global(.ant-select-selection-placeholder){
                font-size: 10px;
            }
        }}
    }
    :global(.ant-select-dropdown){
        :global(.ant-select-item-option){
          font-size: 10px;
        }
    }
}
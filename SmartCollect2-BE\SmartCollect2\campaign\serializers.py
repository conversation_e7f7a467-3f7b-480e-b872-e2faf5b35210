from rest_framework.serializers import ModelSerializer
from rest_framework import serializers
import campaign.models as campmodels
import logging
from dateutil import rrule
from datetime import datetime, date


class FilterMstSerializer(ModelSerializer):
    class Meta:
        model = campmodels.FilterMst
        fields = '__all__'


class FiltersSerializer(ModelSerializer):
    class Meta:
        model = campmodels.Filters
        fields = '__all__'


# ### Campaign Flow ####
class CommFlowSerializer(ModelSerializer):
    FlowName = serializers.CharField(source='CommFlowMstID.FlowName', read_only=True)

    class Meta:
        model = campmodels.CommFlow
        fields = '__all__'

    def to_representation(self, instance):
        """
        This function is used to add additional data to the serialized representation of a CommFlow instance.
        Depending on the CommunicationType, it retrieves related data from the respective models and adds it to the instance.

        Parameters:
        instance (CommFlow): The instance of CommFlow to be serialized.

        Returns:
        dict: The serialized representation of the CommFlow instance with additional data.
        """
        instance = super().to_representation(instance)

        if instance.get('CommunicationType') == 'whatsapp':
            obj = campmodels.WhatsAppFlowMst.objects.filter(WhatsAppFlowMstID=instance.get('FlowID')).first()
            if obj:
                instance['FlowID_data'] = WhatsAppFlowMstDetailsSerializer(obj).data
        elif instance.get('CommunicationType') == 'ai':
            obj = campmodels.VoiceBotTemplateMapping.objects.filter(VoiceBotTemplateMappingID=instance.get('FlowID')).first()
            if obj:
                instance['FlowID_data'] = VoiceBotTemplateMappingDetailsSerializer(obj).data
        elif instance.get('CommunicationType') == 'ivr':
            obj = campmodels.IVRFlowMst.objects.filter(IVRFlowMstID=instance.get('FlowID')).first()
            if obj:
                instance['FlowID_data'] = IVRFlowMappingDetailsSerializer(obj).data
        elif instance.get('CommunicationType') == 'blaster':
            obj = campmodels.BlasterTemplateMapping.objects.filter(BlasterTemplateMappingID=instance.get('FlowID')).first()
            if obj:
                instance['FlowID_data'] = BlasterTemplateMappingDetailsSerializer(obj).data
        elif instance.get('CommunicationType') == 'calling':  # Add this condition
            obj = campmodels.CallingTemplateMapping.objects.filter(CallingTemplateMappingID=instance.get('FlowID')).first()
            if obj:
                instance['FlowID_data'] = CallingTemplateMappingDetailsSerializer(obj).data
        return instance


class FilterMstDetailsSerializer(ModelSerializer):
    FiltersID = FiltersSerializer(read_only=True)

    class Meta:
        model = campmodels.FilterMst
        fields = '__all__'


class CampaignMstDetailsSerializer(ModelSerializer):
    commflow_set = CommFlowSerializer(many=True, read_only=True)
    filtermst_set = FilterMstDetailsSerializer(many=True, read_only=True)

    class Meta:
        model = campmodels.CampaignMst
        fields = '__all__'
        read_only_fields = ['commflow_set', 'filtermst_set']


class CampaignMstSerializer(ModelSerializer):
    class Meta:
        model = campmodels.CampaignMst
        fields = '__all__'


class CommunicationMstSerializer(ModelSerializer):
    class Meta:
        model = campmodels.CommFlowMst
        fields = '__all__'

    def get_fields(self):
        fields = super().get_fields()
        if self.instance is not None:
            read_only_fields = ['CampaignMstID', 'BankMstID', 'CreatedDate']
            for field_name in read_only_fields:
                if field_name in fields:
                    fields[field_name].read_only = True

        return fields


class CommunicationDetailsSerializer(ModelSerializer):
    class Meta:
        model = campmodels.CommFlow
        fields = '__all__'

    def get_fields(self):
        # make fields read_only if the request in create request
        if self.instance is not None:
            read_only_fields = ['CommFlowID', 'BankMstID', 'CampaignMstID', 'CommFlowMstID']
            for field_name in read_only_fields:
                self.fields[field_name].read_only = True
        return super().get_fields()

    def convert_days_to_negative(self, validated_data):
        # if BeforeAfter is 'before' then change Days to -ve comma seperated values
        # eg change 1,2,3 to -1,-2,-3
        if validated_data['BeforeAfter'] == 'before':
            validated_data['Days'] = ','.join([str(-int(x)) for x in validated_data['Days'].split(',')])
        self._validate_days(validated_data['Days'])
        return validated_data

    def _validate_days(self, value):
        """
        This function is used to validate the 'Days' field in the CommunicationDetailsSerializer.
        It checks if the 'Days' field is provided and if it contains valid integers.
        If the 'BeforeAfter' field is set to 'before', it converts the 'Days' values to negative integers.

        Parameters:
        value (str): The input value for the 'Days' field.

        Returns:
        str: The validated and possibly modified value for the 'Days' field.
        """
        if not value:
            raise serializers.ValidationError("This field is required.")

        # Check if all values are integers in range -31 to 31
        try:
            days = [int(x) for x in value.split(',')]
            if not all(-31 <= x <= 31 for x in days):
                raise serializers.ValidationError("Days must be between -31 and 31.")
            return ','.join(map(str, days))
        except ValueError:
            raise serializers.ValidationError("Invalid input. Please provide a comma-separated list of integers.")

    def _validate_frequency(self, value):
        """
        This function is used to validate the 'frequency' field in the CommunicationDetailsSerializer.
        It checks if the frequency is one of the allowed values: 'yearly', 'monthly', 'weekly', 'daily'.

        Parameters:
        value (str): The input value for the 'frequency' field.

        Returns:
        str: The validated value for the 'frequency' field.
        """
        allowed_frequencies = ['yearly', 'monthly', 'weekly', 'daily']
        if value not in allowed_frequencies:
            raise serializers.ValidationError(f"Invalid frequency. Allowed values are: {', '.join(allowed_frequencies)}")
        return value

    def _validate_startdate(self, value):
        """
        This function is used to validate the 'startdate' field in the CommunicationDetailsSerializer.
        It checks if the start date is provided and if it is a valid date.

        Parameters:
        value (str): The input value for the 'startdate' field.

        Returns:
        str: The validated value for the 'startdate' field.
        """
        if not value:
            raise serializers.ValidationError("Start date is required.")
        try:
            # Assuming the date is in 'YYYY-MM-DD' format
            if isinstance(value, date):
                return value
            start_date = datetime.strptime(value, '%Y-%m-%d')
            return start_date
        except ValueError:
            raise serializers.ValidationError("Invalid date format. Please provide a valid date.")

    def _validate_interval(self, value):
        """
        This function is used to validate the 'interval' field in the CommunicationDetailsSerializer.
        It checks if the interval is a positive integer.

        Parameters:
        value (int): The input value for the 'interval' field.

        Returns:
        int: The validated value for the 'interval' field.
        """
        if value <= 0:
            raise serializers.ValidationError("Interval must be a positive integer.")
        return value

    def _validate_count(self, value: int):
        """
        This function is used to validate the 'count' field in the CommunicationDetailsSerializer.
        It checks if the count is a positive integer.

        Parameters:
        value (int): The input value for the 'count' field.

        Returns:
        int: The validated value for the 'count' field.
        """
        if value is not None and value <= 0:
            raise serializers.ValidationError("Count must be a positive integer.")
        return value

    def _validate_until(self, value, start_date=None):
        """
        This function is used to validate the 'until' field in the CommunicationDetailsSerializer.
        It checks if the until date is provided and if it is a valid date.

        Parameters:
        value (str): The input value for the 'until' field.

        Returns:
        str: The validated value for the 'until' field.
        """
        if not value:
            return None  # Allow until to be optional
        try:
            # Assuming the date is in 'YYYY-MM-DD' format
            if not isinstance(value, date):
                value = datetime.strptime(value, '%Y-%m-%d')
            start_date = start_date or datetime.now()
            if value < start_date:
                raise serializers.ValidationError("Until date must be in the future.")
            return value
        except ValueError:
            raise serializers.ValidationError("Invalid date format. Please provide a valid date.")

    def _validate_bymonth(self, value: str):
        """
        This function is used to validate the 'bymonth' field in the CommunicationDetailsSerializer.
        It checks if the bymonth value is a valid month (1-12).

        Parameters:
        value (str): The input value for the 'bymonth' field of comma separated integers.

        Returns:
        list: The validated list of months for the 'bymonth' field.
        """
        if not value:
            return ''  # Optional field

        try:
            months = [int(m.strip()) for m in value.split(',')]
        except ValueError:
            raise serializers.ValidationError("Invalid format. Use comma-separated numbers (e.g., '1,3,12').")

        if not all(1 <= m <= 12 for m in months):
            raise serializers.ValidationError("Months must be integers between 1 and 12.")

        return ','.join(map(str, months))  # Return normalized string

    def _validate_bymonthday(self, value: str):
        """
        This function is used to validate the 'bymonthday' field in the CommunicationDetailsSerializer.
        It checks if the bymonthday value is a valid day of the month (1-31), allowing a string of comma-separated values.

        Parameters:
        value (str): The input value for the 'bymonthday' field (comma-separated string).

        Returns:
        str: The validated value for the 'bymonthday' field as a comma-separated string.
        """
        if not value:
            return ''  # Allow bymonthday to be optional

        try:
            days = [int(day.strip()) for day in value.split(',')]
        except ValueError:
            raise serializers.ValidationError("Invalid format. Use comma-separated numbers (e.g., '1,15,31').")

        if not all(1 <= day <= 31 for day in days):
            raise serializers.ValidationError("Bymonthday must be integers between 1 and 31.")
        return ','.join(map(str, days))  # Return normalized string

    def _validate_byyearday(self, value: str):
        """
        This function is used to validate the 'byyearday' field in the CommunicationDetailsSerializer.
        It checks if the byyearday value is a valid day of the year (1-366), allowing a string of comma-separated values.

        Parameters:
        value (str): The input value for the 'byyearday' field (comma-separated string).

        Returns:
        str: The validated value for the 'byyearday' field as a comma-separated string.
        """
        if not value:
            return ''  # Allow byyearday to be optional
        try:
            days = [int(day.strip()) for day in value.split(',')]
        except ValueError:
            raise serializers.ValidationError("Invalid format. Use comma-separated numbers (e.g., '1,100,365').")
        if not all(1 <= day <= 366 for day in days):
            raise serializers.ValidationError("Byyearday must be integers between 1 and 366.")
        return ','.join(map(str, days))  # Return normalized string

    def _validate_byweekno(self, value: str):
        """
        This function is used to validate the 'byweekno' field in the CommunicationDetailsSerializer.
        It checks if the byweekno value is a valid week number (1-53), allowing a string of comma-separated values.

        Parameters:
        value (str): The input value for the 'byweekno' field (comma-separated string).

        Returns:
        str: The validated value for the 'byweekno' field as a comma-separated string.
        """
        if not value:
            return ''
        try:
            weeks = [int(week.strip()) for week in value.split(',')]
        except ValueError:
            raise serializers.ValidationError("Invalid format. Use comma-separated numbers (e.g., '1,2,53').")
        if not all(1 <= week <= 53 for week in weeks):
            raise serializers.ValidationError("Byweekno must be integers between 1 and 53.")
        return ','.join(map(str, weeks))  # Return normalized string

    def _validate_byweekday(self, value: str):
        """
        This function is used to validate the 'byweekday' field in the CommunicationDetailsSerializer.
        It checks if the byweekday value is a valid weekday (0-6, where 0 is Monday and 6 is Sunday),
        allowing a string of comma-separated values.

        Parameters:
        value (str): The input value for the 'byweekday' field (comma-separated string).

        Returns:
        str: The validated value for the 'byweekday' field as a comma-separated string.
        """
        if not value:
            return ''
        try:
            weekdays = [int(day.strip()) for day in value.split(',')]
        except ValueError:
            raise serializers.ValidationError("Invalid format. Use comma-separated numbers (e.g., '0,1,6').")
        if not all(0 <= day <= 6 for day in weekdays):
            raise serializers.ValidationError("Byweekday must be integers between 0 (Monday) and 6 (Sunday).")
        return ','.join(map(str, weekdays))  # Return normalized string

    def _validate_weekstart(self, value: int):
        """
        This function is used to validate the 'weekstart' field in the CommunicationDetailsSerializer.
        It checks if the weekstart value is a valid weekday (0-6, where 0 is Monday and 6 is Sunday).

        Parameters:
        value (int): The input value for the 'weekstart' field.

        Returns:
        int: The validated value for the 'weekstart' field.
        """
        if value is None:
            return None  # Allow weekstart to be optional
        if not (0 <= value <= 6):
            raise serializers.ValidationError("Weekstart must be between 0 (Monday) and 6 (Sunday).")
        return value

    def generate_rrule(self, attrs):
        """
        This function generates a recurrence rule (RRULE) based on the provided attributes.
        It uses the dateutil.rrule library to create a rule that can be used for scheduling.

        Parameters:
        attrs (dict): The input data containing the necessary fields to create the RRULE.

        Returns:
        rrule.rrule: The generated recurrence rule.
        """
        interval = int(attrs['interval']) if attrs['interval'] else 1
        count = int(attrs['count']) if attrs['count'] else None
        bymonth = [int(m.strip()) for m in attrs['bymonth'].split(',')] if attrs['bymonth'] else None
        byyearday = [int(d.strip()) for d in attrs['byyearday'].split(',')] if attrs['byyearday'] else None
        byweekno = [int(w.strip()) for w in attrs['byweekno'].split(',')] if attrs['byweekno'] else None
        byweekday = [int(d.strip()) for d in attrs['byweekday'].split(',')] if attrs['byweekday'] else None
        bymonthday = [int(d.strip()) for d in attrs['Days'].split(',')] if attrs['Days'] else None
        weekstart = int(attrs['weekstart']) if attrs['weekstart'] is not None else 0

        rule = rrule.rrule(
            freq=getattr(rrule, attrs['frequency'].upper(), None),
            dtstart=attrs['startdate'],
            interval=interval,
            count=count,
            until=attrs.get('until'),
            bymonth=bymonth,
            bymonthday=bymonthday,
            byyearday=byyearday,
            byweekno=byweekno,
            byweekday=byweekday,
            wkst=weekstart
        )

        return [r.date() for r in rule]

    def validate(self, attrs):
        """
        This function is used to validate the input data for the CommunicationDetailsSerializer.
        It checks if the 'Days' field is provided and if the 'BeforeAfter' field is set to 'before'.
        If so, it converts the 'Days' values to negative integers.

        Parameters:
        attrs (dict): The input data to be validated.

        Returns:
        dict: The validated data with 'Days' converted to negative integers if applicable.
        """
        try:
            if attrs.get('campaign_type') not in ['normal', 'periodic']:
                raise serializers.ValidationError("Invalid campaign type. Must be 'normal' or 'periodic'.")

            if attrs.get('campaign_type') == 'periodic':
                # Validate frequency and other periodic fields
                if attrs['count'] is not None and attrs['until'] is not None:
                    raise serializers.ValidationError("Cannot set both count and until. Please set only one.")

                attrs['frequency'] = self._validate_frequency(attrs.get('frequency'))
                attrs['startdate'] = self._validate_startdate(attrs.get('startdate'))
                attrs['interval'] = self._validate_interval(attrs.get('interval'))
                attrs['count'] = self._validate_count(attrs.get('count'))
                attrs['until'] = self._validate_until(attrs.get('until'), attrs.get('startdate'))
                attrs['bymonth'] = self._validate_bymonth(attrs.get('bymonth'))
                attrs['Days'] = self._validate_bymonthday(attrs.get('Days'))  # Assuming Days is used for bymonthday
                attrs['byyearday'] = self._validate_byyearday(attrs.get('byyearday'))
                attrs['byweekno'] = self._validate_byweekno(attrs.get('byweekno'))
                attrs['byweekday'] = self._validate_byweekday(attrs.get('byweekday'))
                attrs['weekstart'] = self._validate_weekstart(attrs.get('weekstart'))
            elif attrs.get('campaign_type') == 'normal':
                if 'Days' in attrs and attrs.get('BeforeAfter') == 'before':
                    attrs = self.convert_days_to_negative(attrs)
                # For non-periodic campaigns, ensure these fields are not set
                attrs['frequency'] = None
                attrs['startdate'] = None
                attrs['interval'] = None
                attrs['count'] = None
                attrs['until'] = None
                attrs['bymonth'] = None
                attrs['byyearday'] = None
                attrs['byweekno'] = None
                attrs['byweekday'] = None
                attrs['weekstart'] = None

            final = super().validate(attrs)
            logging.info("Final validated data: %s", final)  # Debugging line to check final validated data
            return final
        except Exception as e:
            logging.error("Validation error: %s", e, exc_info=True)
            raise e

    def update_dates_table(self, instance, validated_data):
        """
        This function updates the dates_table with the dates generated from the RRULE.
        It creates or updates entries in the dates_table for each date in the generated RRULE.

        Parameters:
        instance (CommFlow): The instance of CommFlow to be updated.
        validated_data (dict): The validated data containing the RRULE attributes.

        Returns:
        None
        """

        dates = self.generate_rrule(validated_data)
        print('----> Dates', dates)
        # delete existing dates for this CommFlow instance
        campmodels.dates_table.objects.filter(commflowid=instance).delete()

         # Step 2: Bulk create new entries
        date_entries = [
            campmodels.dates_table(date=date, commflowid=instance)
            for date in dates
        ]
        campmodels.dates_table.objects.bulk_create(date_entries)

    def create(self, validated_data):
        instance = super().create(validated_data)
        if validated_data.get('campaign_type') == 'periodic':
            self.update_dates_table(instance, validated_data)
        return instance

    def update(self, instance, validated_data):
        instance = super().update(instance, validated_data)
        print('------>', validated_data)
        if validated_data.get('campaign_type') == 'periodic':
            self.update_dates_table(instance, validated_data)
        return instance

# ### WhatsApp Flow ####
class WhatsappVariableMappingSerializer(ModelSerializer):
    class Meta:
        model = campmodels.WhatsappVariableMapping
        fields = '__all__'


class WhatsAppTemplateMappingSerializer(ModelSerializer):
    whatsappvariablemapping_set = WhatsappVariableMappingSerializer(many=True, read_only=True)

    class Meta:
        model = campmodels.WhatsAppTemplateMapping
        fields = '__all__'
        read_only_fields = ['whatsappvariablemapping_set']


class WhatsAppFlowMappingSerializer(ModelSerializer):
    WhatsAppTemplateMappingID = WhatsAppTemplateMappingSerializer(read_only=True)

    class Meta:
        model = campmodels.WhatsAppFlowMapping
        fields = '__all__'


class WhatsAppFlowMstSerializer(ModelSerializer):
    class Meta:
        model = campmodels.WhatsAppFlowMst
        fields = '__all__'


class WhatsAppFlowMstDetailsSerializer(ModelSerializer):
    # Do Not Remove read_only=True, and do not change the name of the field
    whatsappflowmapping_set = WhatsAppFlowMappingSerializer(many=True, read_only=True)  # read_only=True is important here

    class Meta:
        model = campmodels.WhatsAppFlowMst
        fields = [
            'WhatsAppFlowMstID', 'FlowName', 'IsActive', 'ExtraColumn1', 'ExtraColumn2', 'ExtraColumn3', 'ExtraColumn4', 'ExtraColumn5', 'BankMstID', 'whatsappflowmapping_set'
        ]

    def to_representation(self, instance):
        """
        This function is used to filter the whatsappflowmapping_set to only include those with IsStart=True.
        It modifies the serialized representation of the WhatsAppFlowMst instance to include only the relevant flow mappings.
        Parameters:
        instance (WhatsAppFlowMst): The instance of WhatsAppFlowMst to be serialized.
        Returns:
        dict: The serialized representation of the WhatsAppFlowMst instance with filtered flow mappings.
        """
        instance = super().to_representation(instance)
        instance['whatsappflowmapping_set'] = [flow for flow in instance.get('whatsappflowmapping_set', []) if flow.get('IsStart')]
        return instance



# ### Blaster Flow ####
class BlasterUserTemplateMappingSerializer(ModelSerializer):
    class Meta:
        model = campmodels.BlasterUserTemplate
        fields = '__all__'


class BlasterTemplateMappingSerializer(ModelSerializer):
    class Meta:
        model = campmodels.BlasterTemplateMapping
        fields = '__all__'


class BlasterTemplateMappingDetailsSerializer(ModelSerializer):
    blasterusertemplate_set = BlasterUserTemplateMappingSerializer(many=True, read_only=True)
    FlowName = serializers.SerializerMethodField('get_template_name')

    def get_template_name(self, obj):
        return obj.TemplateName

    class Meta:
        model = campmodels.BlasterTemplateMapping
        fields = '__all__'
        read_only_fields = ['blasterusertemplate_set', 'FlowName']


# ### VoiceBot Flow ####
class VoiceBotUserTemplateMappingSerializer(ModelSerializer):
    class Meta:
        model = campmodels.VoiceBotUserTemplate
        fields = '__all__'


class VoiceBotTemplateMappingDetailsSerializer(ModelSerializer):
    voicebotusertemplate_set = VoiceBotUserTemplateMappingSerializer(many=True, read_only=True)
    FlowName = serializers.SerializerMethodField('get_template_name')

    def get_template_name(self, obj):
        return obj.TemplateName

    class Meta:
        model = campmodels.VoiceBotTemplateMapping
        fields = '__all__'
        read_only_fields = ['voicebotusertemplate_set', 'FlowName']


class VoiceBotTemplateMappingSerializer(ModelSerializer):
    class Meta:
        model = campmodels.VoiceBotTemplateMapping
        fields = '__all__'


# ### IVR Flow ####
class IVRUserTemplateMappingSerializer(ModelSerializer):
    class Meta:
        model = campmodels.IVRUserTemplate
        fields = '__all__'


class IVRVariableMappingSerializer(ModelSerializer):
    class Meta:
        model = campmodels.IVRVariableMapping
        fields = '__all__'


class IVRTemplateMappingSerializer(ModelSerializer):
    ivrusertemplate_set = IVRUserTemplateMappingSerializer(many=True, read_only=True)
    ivrvariablemapping_set = IVRVariableMappingSerializer(many=True, read_only=True)

    class Meta:
        model = campmodels.IVRTemplateMapping
        fields = '__all__'
        read_only_fields = ['ivrusertemplate_set', 'ivrvariablemapping_set']


class IVRFlowMappingSerializer(ModelSerializer):
    IVRTemplateMappingID = IVRTemplateMappingSerializer(read_only=True)

    class Meta:
        model = campmodels.IVRFlowMapping
        fields = '__all__'


class IVRFlowMappingDetailsSerializer(ModelSerializer):
    ivrflowmapping_set = IVRFlowMappingSerializer(many=True, read_only=True)

    class Meta:
        model = campmodels.IVRFlowMst
        fields = '__all__'
        read_only_fields = ['ivrflowmapping_set']


class IVRFlowSerializer(ModelSerializer):
    class Meta:
        model = campmodels.IVRFlowMst
        fields = '__all__'


class SmsFlowSerializer(ModelSerializer):
    class Meta:
        model = campmodels.SMSTemplateMapping
        fields = '__all__'





# ### Calling Flow ####
class CallingUserTemplateMappingSerializer(ModelSerializer):
    class Meta:
        model = campmodels.CallingUserTemplate
        fields = '__all__'


class CallingTemplateMappingSerializer(ModelSerializer):
    class Meta:
        model = campmodels.CallingTemplateMapping
        fields = '__all__'


class CallingTemplateMappingDetailsSerializer(ModelSerializer):
    callingusertemplate_set = CallingUserTemplateMappingSerializer(many=True, read_only=True)
    FlowName = serializers.SerializerMethodField('get_template_name')

    def get_template_name(self, obj):
        return obj.TemplateName

    class Meta:
        model = campmodels.CallingTemplateMapping
        fields = '__all__'
        read_only_fields = ['callingusertemplate_set', 'FlowName']
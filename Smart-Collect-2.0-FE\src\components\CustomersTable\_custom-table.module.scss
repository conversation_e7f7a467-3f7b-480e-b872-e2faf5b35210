@import url("../../index.css");

$table-radius: 22px;
$attempts-bg: #bdd1ff;
$disable: #787777;
$body: #e4f8f9;

.download_button {
  background-color: var(--dark-blue);
  padding: 0.2rem 1rem;
  border-radius: 10px;
  cursor: pointer;
  margin-right: 1.3rem;
  border: none;
  img {
    width: 20px;
  }
}

.customTable {
  padding-top: 1.5rem;
  .name {
    font-weight: 400;
    color: var(--dark-blue);
    font-family: 'Kanit','Courier New', Courier, monospace;
    font-size: 13px;
  }

  .attempts {
    background-color: $attempts-bg;
    font-weight: 600;
    width: 25px;
    height: 25px;
    border-radius: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    font-family: 'Kanit','Courier New', Courier, monospace;
  }

  .customerId {
    font-weight: 400;
    color: var(--blue);
    font-family: 'Kanit','Courier New', Courier, monospace;
  }

  .loanType {
    text-transform: uppercase;
    font-family: 'Kanit','Courier New', Courier, monospace;
    font-weight: 400;
  }

  .icon_div {
    display: flex;
    justify-content: center;
    width: 100%;
    .icon_img {
      width: 18px;
      height: 18px;
      cursor: pointer;
      img {
        width: 100%;
        object-fit: contain;
        cursor: pointer;
      }
      input[type="image"]{
        width: 18px;
      }
    }
  }

  //Global ant design classes
  // Pagination
  :global(.ant-pagination) {
    justify-content: center;
    margin: 0 !important;

    :global(.ant-pagination-prev),
    :global(.ant-pagination-next) {
      color: var(--dark-blue);
      border: 0 solid var(--blue);
      background: #e4f8f9;
      height: 25px;
      min-width: 15px;
      border-radius: 0px;
      margin: 0;
      button {
        border-radius: 0px;
      }
    }
    :global(.ant-pagination-item) {
      margin-right: 0;
      height: 0;
      a {
        color: $disable;
        font-size: 0.9rem;
        line-height: 23px;
      }
      &:hover {
        background-color: transparent;
      }
    }
    :global(.ant-pagination-item-active) {
      border: none;
      a {
        color: var(--dark-blue);
        font-size: 1.2rem;
        padding: 0;
      }
    }
  }

  //Table container
  :global(.ant-table-container) {
    padding: 0 0.8rem;
    margin-bottom: 0.5rem;
    background: var(--light-green);
    border-start-start-radius: $table-radius;
    border-start-end-radius: $table-radius;

      // Table header
      :global(.ant-table-header) {
          position: relative;
          margin: 0 auto;
          top: -21px;
          border-radius: $table-radius;
          :global(.ant-table-column-has-sorters) {
          background-color: var(--dark-blue);
          &:hover{
              background-color: var(--dark-blue);
          }
  
          :global(.ant-table-column-sorter-up),
          :global(.ant-table-column-sorter-down) {
              svg{
              fill: white;
              }
              &:global(.active){
              svg{
                  fill: rgb(24, 155, 249)
              }
              }
          }
          }
      }

    // Table virtual body
    :global(.ant-table-tbody-virtual) {
      margin-top: -8px;
    }

    &::before {
      box-shadow: none !important;
    }
  }

  // Table rows header
  :global(.ant-table-thead > tr) {
    th {
      border-start-end-radius: 0 !important;
      background-color: var(--dark-blue);
      border-inline-end: none !important;
      color: white;
      border-bottom: none;
      text-align: center;
      font-family: 'Kanit','Courier New', Courier, monospace;
      font-weight: 400;

      &:not(:last-child):not(.ant-table-selection-column):not(
          .ant-table-row-expand-icon-cell
        ):not([colspan])::before {
        width: 0;
      }
    }
  }

  // Table body
  :global(.ant-table-tbody) {
    // Body rows
    :global(.ant-table-row) {
      // Cols
      :global(.ant-table-cell) {
        font-weight: 500;
        text-align: center;
        border-bottom: 2px solid white;
        font-family: 'Kanit','Courier New', Courier, monospace;

        &:global(.ant-table-cell-row-hover) {
          background-color: $body;
        }

        &:global(.ant-table-cell-fix-right) {
          border-bottom: none !important;
        }
        &:global(.ant-table-cell-fix-right-first) {
          border-left: 2px solid white;
        }
      }
    }
  }

  // Fixed Cols
  :global(.ant-table-cell-fix-right) {
    background-color: $body;
  }
}

// Responsive view
@media screen and (max-width: 768px) {
  .download_button{
    margin-top: 1rem;
    padding: 0rem 0.5rem;
    border-radius: 4px;
    img{
      width: 15px;
    }
  }

  .customTable {
    // Table rows header
    :global(.ant-table-thead > tr) {
      th {
        font-size: 12px;
        padding: 0.5rem;
      }
    }
    :global(.ant-table-tbody) {
      :global(.ant-table-row) {
        :global(.ant-table-cell) {
          padding: 0.6rem;
          font-size: 12px !important;
          :global(.ant-typography){
            font-size: 12px !important;
          }
        }
      }
    }

    .icon_div {
      .icon_img {
        width: 13px;
        height: 13px;
      }
    }
  }
}
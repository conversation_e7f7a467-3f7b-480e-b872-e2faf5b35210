@import url("../../../../../index.css");

.ready_to_pay {
  padding: 0rem 0 1.5rem 1rem;
  min-height: 150px;

  .text {
    color: var(--dark-blue);
    font-weight: 400;
    width: 300px;
    font-family: "<PERSON>nit", "Courier New", Courier, monospace;
  }

  .feedback_button {
    background-color: var(--dark-blue);
    color: white;
    padding: 1rem 2rem;
    border: none;
    font-family: "Kanit", "Courier New", Courier, monospace;

    &:hover {
      background-color: var(--dark-blue) !important;
      color: white !important;
      border: none;
      box-shadow: none;
    }
  }

  :global(.ant-picker-outlined),
  :global(.ant-input-number),
  :global(.ant-input-number-group-addon) {
    background-color: #e4f8f9;
    border: none !important;
    width: 200px;
  }

  :global(.ant-input-number-group-addon) {
    background-color: #e4f8f9;
    border: none !important;
    width: 0;
    padding: 0 0 0 5px;
  }

  :global(.ant-input-number) {
    width: 186px;
  }
  :global(.ant-input-number-input) {
    color: var(--dark-blue);
    font-weight: 500;
    font-family: "Kanit", "Courier New", Courier, monospace;
  }

  :global(.ant-input-number-outlined) {
    &:focus-within {
      box-shadow: none !important;
    }
  }

  :global(.ant-input-number-handler-wrap) {
    display: none;
  }

  :global(.ant-segmented) {
    background-color: var(--light-green);
    border-radius: 10px;

    :global(.ant-segmented-item) {
      :global(.ant-segmented-item-label) {
        color: #0f20504a;
        font-weight: 400;
        background-color: transparent;
        font-family: "Kanit", "Courier New", Courier, monospace;
      }
      &::after {
        background-color: var(--dark-blue) !important;
        display: none;
      }
    }

    :global(.ant-segmented-item-selected) {
      background-color: var(--dark-blue) !important;
      box-shadow: none;
      border-radius: 10px;

      :global(.ant-segmented-item-label) {
        color: white;
      }
    }
    :global(.ant-segmented-thumb) {
      background-color: var(--light-green);
      border-radius: 10px;
    }
  }

  .date_picker {
    :global(.ant-picker-input > input) {
      font-weight: 500;
      color: var(--dark-blue);
      font-family: "Kanit", "Courier New", Courier, monospace;

      &::placeholder {
        font-weight: 500;
      }
    }
    &::placeholder {
      color: #0f20504a;
      font-weight: 500;
    }
  }
}

// Responsive view
@media screen and (max-width: 786px) {
  .ready_to_pay {
    padding: 0;
    .text {
      font-size: 11px;
    }
    .feedback_button {
      padding: 0rem 1rem;
      font-size: 11px;
    }
    :global(.ant-input-number-input) {
      font-size: 11px;
    }

    :global(.ant-segmented) {
      :global(.ant-segmented-item-label) {
        font-size: 11px !important;
      }
    }

    .date_picker {
      font-size: 12px;
      :global(.ant-picker-input) {
        input {
          font-size: 11px;
        }
      }
    }
  }
}

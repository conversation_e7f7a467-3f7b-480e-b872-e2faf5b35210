import pandas as pd
import numpy as np

import pandas as pd
import numpy as np
import re
from datetime import datetime
import io
from ..utils import convert_excel_serial_date
from ..queries import insert_data_in_raw_table,get_previous_overdue_amount_from_rawtable
import threading
from ..sp import execute_post_processing_procedures

def process_ghoti_loan_data(df):
    """
    Process loan data for Ghoti bank (bank_id=27)
    Extract loan types and branch names from the file
    """
    # Read loan types (first table in the file)
    loan_types = df.copy()
    loan_types.columns = ['LOAN CODE', 'LOAN TYPE NAME']
    loan_types = loan_types.dropna()
   
    # Read branch names (second table in the file)
    # Note: This should be read from the same file structure
    branch_names = pd.read_excel(df, sheet_name=0, header=19, nrows=4, usecols="A:B")
    branch_names.columns = ['BR CODE', 'BR NAME']
    branch_names = branch_names.dropna()
   
    # Create dictionaries for faster lookup
    loan_type_dict = dict(zip(loan_types['LOAN CODE'], loan_types['LOAN TYPE NAME']))
    branch_name_dict = dict(zip(branch_names['BR CODE'], branch_names['BR NAME']))
   
    return loan_types, branch_names, loan_type_dict, branch_name_dict

def process_ghoti_od_data(df_original):
    """
    Process overdue data for Ghoti bank
    """
    import pandas as pd
    
    # Define column mapping
    column_mapping = {
        'BR': 'BranchCode',
        'Br Name': 'BranchName',  # Added mapping for BR NAME
        'AH': 'LoanCode',
        'AC': 'DisbursmentID',
        'NAME': 'CustomerName',
        'SAN AMT': 'DisbursedAmt',
        'SAN DATE': 'DisbursedDate',
        'OVERDUE': 'Overdue',
        'INSTL AMT': 'EMI Amount',
        'BALANCE': 'CurrentBalance',
        'MOBILE': 'MobileNo',
        'LOAN TYPE': 'LoanTypeName'  # Added mapping for LOAN TYPE
    }
   
    # Check which columns exist and apply mapping
    existing_columns = [col for col in column_mapping.keys() if col in df_original.columns]
    df_mapped = df_original.rename(columns={k: column_mapping[k] for k in existing_columns})
   
    # Clean up .0 from numeric columns after mapping
    for col in df_mapped.columns:
        try:
            # Check if the column exists and has a dtype attribute
            if hasattr(df_mapped[col], 'dtype'):
                # Check if it's a numeric float type
                if pd.api.types.is_float_dtype(df_mapped[col]):
                    non_null_values = df_mapped[col].dropna()
                    if len(non_null_values) > 0:
                        # Check if all non-null values are whole numbers
                        is_whole_numbers = True
                        for val in non_null_values:
                            if pd.notna(val) and val != int(val):
                                is_whole_numbers = False
                                break
                        
                        if is_whole_numbers:
                            df_mapped[col] = df_mapped[col].astype('Int64')
        except Exception as e:
            # Skip this column if there's any error in processing
            print(f"Warning: Could not process column '{col}' for dtype conversion: {e}")
            continue

    # Apply mobile number filtering
    df_filtered = df_mapped.copy()
    if 'MobileNo' in df_filtered.columns:
        try:
            df_filtered = df_filtered[
                (df_filtered['MobileNo'].notna()) &
                (df_filtered['MobileNo'] != 0) &
                (df_filtered['MobileNo'] != '0') &
                (df_filtered['MobileNo'] != '') &
                (df_filtered['MobileNo'].astype(str).str.len() >= 10)
            ].copy()
        except Exception as e:
            print(f"Warning: Could not apply mobile number filtering: {e}")
   
    # Apply overdue filtering
    if 'Overdue' in df_filtered.columns:
        try:
            df_filtered['Overdue'] = pd.to_numeric(df_filtered['Overdue'], errors='coerce')
            df_filtered = df_filtered[df_filtered['Overdue'] > 0].copy()
        except Exception as e:
            print(f"Warning: Could not apply overdue filtering: {e}")
   
    return df_filtered

def merge_ghoti_data(loan_data, od_data):
    """
    Merge loan and overdue data for Ghoti bank (bank_id=27)
    Handle cases where one or both datasets might be None
    
    Args:
        loan_data: Dictionary with loan_types and branch_names dataframes or None
        od_data: Overdue dataframe or None
        
    Returns:
        pandas.DataFrame: Merged dataframe or None if no data available
    """
    try:
        # Case 1: Both loan_data and od_data are None
        if loan_data is None and od_data is None:
            print("No data available for merging")
            return None
        
        # Case 2: Only loan_data is available
        if loan_data is not None and od_data is None:
            print("Only loan data available, creating reference file")
            if isinstance(loan_data, dict) and 'loan_types' in loan_data and 'branch_names' in loan_data:
                # Create a combined reference dataframe
                loan_types_df = loan_data['loan_types'].copy()
                branch_names_df = loan_data['branch_names'].copy()
                
                # Add a column to identify the source
                loan_types_df['DATA_TYPE'] = 'LOAN_TYPE'
                branch_names_df['DATA_TYPE'] = 'BRANCH_NAME'
                
                # Rename columns to match
                loan_types_df = loan_types_df.rename(columns={'LOAN CODE': 'CODE', 'LOAN TYPE NAME': 'NAME'})
                branch_names_df = branch_names_df.rename(columns={'BR CODE': 'CODE', 'BR NAME': 'NAME'})
                
                # Combine both dataframes
                combined_df = pd.concat([loan_types_df, branch_names_df], ignore_index=True)
                
                # Add combination columns if required columns exist
                if 'CODE' in combined_df.columns:
                    # For reference data, create dummy combination IDs based on codes
                    combined_df['DisbursementIDCombination'] = combined_df['CODE'].astype(str) + '_REF'
                    combined_df['CustomerIDCombination'] = combined_df['DisbursementIDCombination']
                
                return combined_df
            else:
                print("Invalid loan_data structure")
                return None
        
        # Case 3: Only od_data is available
        if loan_data is None and od_data is not None:
            print("Only overdue data available")
            # Add the required combination columns
            od_data = add_combination_columns(od_data, loan_data)
            return od_data
        
        # Case 4: Both are available - merge them
        if loan_data is not None and od_data is not None:
            print("Both loan and overdue data available, implementing merge logic")
            
            # Extract dictionaries from loan_data
            if isinstance(loan_data, dict):
                loan_type_dict = dict(zip(loan_data['loan_types']['LOAN CODE'], 
                                        loan_data['loan_types']['LOAN TYPE NAME']))
                branch_name_dict = dict(zip(loan_data['branch_names']['BR CODE'], 
                                          loan_data['branch_names']['BR NAME']))
            else:
                loan_type_dict = {}
                branch_name_dict = {}
            
            # Add combination columns and branch names to overdue data
            merged_data = add_combination_columns(od_data, loan_data)
            
            # Add branch names using the mapping
            if 'BranchCode' in merged_data.columns:
                merged_data['BranchName'] = merged_data['BranchCode'].map(branch_name_dict)
                print("Added BranchName column based on BranchCode mapping")
            
            # Add loan type names using the mapping
            if 'LoanCode' in merged_data.columns:
                merged_data['LoanTypeName'] = merged_data['LoanCode'].map(loan_type_dict)
                print("Added LoanTypeName column based on LoanCode mapping")
            
            return merged_data
    
    except Exception as e:
        print(f"Error in merge_ghoti_data: {str(e)}")
        return None

def add_combination_columns(df, loan_data):
    """
    Add CustomerIDCombination and DisbursementIDCombination columns to dataframe
    
    Args:
        df: Dataframe to add columns to
        loan_data: Loan data dictionary (can be None)
        
    Returns:
        pandas.DataFrame: Dataframe with added combination columns
    """
    df_copy = df.copy()
    
    # Create DisbursementIDCombination
    if all(col in df_copy.columns for col in ['LoanCode', 'DisbursmentID', 'BranchCode']):
        df_copy['DisbursementIDCombination'] = (
            df_copy['LoanCode'].astype(str) + 
            df_copy['DisbursmentID'].astype(str) + 
            df_copy['BranchCode'].astype(str)
        )
        print("Created DisbursementIDCombination from LoanCode + DisbursmentID + BranchCode")
    elif 'DisbursmentID' in df_copy.columns:
        # Fallback: use just DisbursmentID
        df_copy['DisbursementIDCombination'] = df_copy['DisbursmentID'].astype(str)
        print("Created DisbursementIDCombination from DisbursmentID only")
    else:
        # Generate unique IDs
        df_copy['DisbursementIDCombination'] = ['DISB_' + str(i) for i in range(len(df_copy))]
        print("Generated unique DisbursementIDCombination values")
    
    df_copy['CustomerIDCombination'] = df_copy['DisbursementIDCombination']
    
    return df_copy

def run_post_processing_in_background(bank_id):
    """
    Run post-processing procedures in a background thread
    """
    try:
        print(f"Starting background post-processing for bank_id: {bank_id}")
        execute_post_processing_procedures(bank_id)
        print(f"Background post-processing initiated for bank_id: {bank_id}")
    except Exception as e:
        print(f"Error starting background post-processing for bank_id {bank_id}: {str(e)}")

def process_raw_data_for_ghoti(df1, bank_id):
    db_operation_success = False
    db_error_message = None
    df1.columns = df1.columns.str.strip().str.replace(r'\s+', ' ', regex=True)
    df = pd.DataFrame()
    
    # Your existing column assignments...
    df['customerid'] = df1.get('CustomerIDCombination', '')
    df['disbursementid'] = df1.get('DisbursementIDCombination', '')
    df['branchcode'] = df1.get('BranchCode', '')
    df['branchname'] = df1.get('BranchName', '').astype(str).str.strip()
    df['loantype'] = df1.get('LoanTypeName', '').astype(str).str.strip()
    df['customername'] = df1.get('CustomerName', '').astype(str).str.strip()
    df['mobileno'] = df1.get('MobileNo', '')
    
    # FIX: Properly handle numeric columns - convert empty strings to 0
    numeric_columns = {
        'overdueamount': 'Overdue',
        'disbursementamount': 'DisbursedAmt', 
        'emiamount': 'EMI Amount',
        'currentbalance': 'CurrentBalance'
    }
    
    for df_col, source_col in numeric_columns.items():
        if source_col in df1.columns:
            # Convert to numeric, replacing empty strings and invalid values with 0
            df[df_col] = pd.to_numeric(df1[source_col], errors='coerce').fillna(0)
        else:
            df[df_col] = 0
    
    # Handle disbursement date
    if 'DisbursedDate' in df1.columns:
        df['disbursementdate'] = df1['DisbursedDate'].apply(convert_excel_serial_date)
    else:
        df['disbursementdate'] = None
    
    # FIX THE NaT ISSUE - Handle NPA DATE REMARKS with NaT conversion
    if 'NPA DATE REMARKS' in df1.columns:
        df['extracolumn1'] = df1['NPA DATE REMARKS'].apply(convert_excel_serial_date)
        # CRITICAL FIX: Replace NaT with None (NULL in database)
        df['extracolumn1'] = df['extracolumn1'].where(pd.notna(df['extracolumn1']), None)
    else:
        df['extracolumn1'] = None
        
    df['bankmstid'] = 27
    df['inserted_date'] = datetime.now().date()
    df['originaldisbursementid'] = df1.get('DisbursmentID', '').astype(str).str.strip()
    df['collectiondate'] = datetime.now().date() - pd.Timedelta(days=1)
    
    # Also fix disbursementdate if it has NaT values
    df['disbursementdate'] = df['disbursementdate'].where(pd.notna(df['disbursementdate']), None)
    
    # Rest of your function...
    customer_disbursement_pairs = list(zip(df['customerid'], df['disbursementid']))
    
    previous_overdue_dict = get_previous_overdue_amount_from_rawtable(
        customer_disbursement_pairs, bank_id
    )
    
    def calculate_collected_from_dict(row):
        key = (row['customerid'], row['disbursementid'])
        previous_overdue = previous_overdue_dict.get(key, 0.0)
        current_overdue = row.get('overdueamount',0)
        return max(0, previous_overdue - current_overdue)
    
    df['collectedamount'] = df.apply(calculate_collected_from_dict, axis=1)
    
    # CRITICAL FIX: Clean the DataFram  e before database insertion
    df = prepare_df_for_database(df)
    
    # Database operation
    # df.to_csv("ghoti.csv", index=False)
    try:
        result = insert_data_in_raw_table(df, bank_id=27)
        db_operation_success = result['success']
        if not result['success']:
            db_error_message = result['message']
    except Exception as e:
        db_operation_success = False
        db_error_message = str(e)
        print(f"Database update failed for ghoti data: {e}")

    # # ========== START BACKGROUND POST-PROCESSING ==========
    if db_operation_success:
        # Start post-processing in background thread
        background_thread = threading.Thread(
            target=run_post_processing_in_background,
            args=(bank_id,),
            daemon=True
        )
        background_thread.start()
        print(f"Post-processing started in background thread for bank_id: {bank_id}")
    else:
        print(f"Skipping post-processing due to database operation failure: {db_error_message}")
    
    return {
        "Total records processed": len(df),
        "New records inserted": result.get('inserted_count', 0) if db_operation_success else 0,
        "Duplicate records found": result.get('duplicate_count', 0) if db_operation_success else 0,
        "db_operation_success": db_operation_success,
        "db_error_message": db_error_message,
        "message": result.get('message', '') if db_operation_success else db_error_message,
    }

def prepare_df_for_database(df):
    """
    Prepare DataFrame for database insertion by handling problematic values
    """
    df_clean = df.copy()
    
    # Handle NaT values in date columns
    date_columns = ['disbursementdate', 'extracolumn1', 'collectiondate', 'inserted_date']
    for col in date_columns:
        if col in df_clean.columns:
            df_clean[col] = df_clean[col].where(pd.notna(df_clean[col]), None)
    
    # Handle empty strings and invalid values in numeric columns
    numeric_columns = ['overdueamount', 'disbursementamount', 'emiamount', 'currentbalance', 'collectedamount']
    for col in numeric_columns:
        if col in df_clean.columns:
            # Convert to numeric, coerce errors to NaN, then fill NaN with 0
            df_clean[col] = pd.to_numeric(df_clean[col], errors='coerce').fillna(0)
            # Ensure the column is numeric type
            df_clean[col] = df_clean[col].astype(float)
    
    # Handle string columns - ensure they're not None
    string_columns = ['customerid', 'disbursementid', 'branchcode', 'branchname', 'loantype', 'customername', 'mobileno', 'originaldisbursementid']
    for col in string_columns:
        if col in df_clean.columns:
            df_clean[col] = df_clean[col].fillna('').astype(str)
    
    return df_clean

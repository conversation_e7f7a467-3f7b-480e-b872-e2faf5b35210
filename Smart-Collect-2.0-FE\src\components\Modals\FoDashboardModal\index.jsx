import { Modal } from 'antd'
import React from 'react'
import { CustomerDetailsCard } from '../FoAllocationCustomerModal/CustomerDetailsCard'
import Style from "./_style.module.scss";
import PropTypes from 'prop-types';

export function FoDashboardModal({
    modalStatus, 
    data, 
    handleCancel}) {
return (
    <Modal
        centered
        className={Style.view_modal} 
        title={null}
        open={modalStatus} 
        onOk={handleCancel} 
        onCancel={handleCancel}
        footer={null}
        closable={false}
    >
        <CustomerDetailsCard 
            loanmstid={data?.LoanMstID}
            branchMstId={data?.BranchMstID}
            status={data?.Status}
            name={data?.CustomerName || "Customer Name"}
            contact={data?.MobileNumber || ""}
            branchName={data?.Branch}
            disbursementId={data?.DisbursementID}
            dpdAmount={data?.DPD || 0}
            feedbackDate={data?.FeedbackDate}
            loanType={data?.LoanType}
            overdueAmount={data?.OverDueAmt || data?.EMIAmount || 0}
            promiseAmount={data?.Amount || 0}
            promiseDate={data?.PromiseDateTime}
            modalButtonText={"Feedback"}
            isModal={true}
            />
    </Modal>
    )
}

FoDashboardModal.propTypes={
    modalStatus: PropTypes.bool, 
    data: PropTypes.object, 
    handleCancel: PropTypes.func
}
import { Flex, Typography } from "antd";
import PropTypes from "prop-types";
import React from "react";
import { formatDigits } from "../../../constant";
import Style from "./_style.module.scss";

const { Text } = Typography;

export default function AnalysisCategoryCard({ title, initial, current, textColor }) {
  return (
    <Flex gap={10} vertical className={Style.cate_card}>
      <Text className={Style.title} style={{color:textColor}}>{title}</Text>
      <Flex justify="space-between" align="end">
        <Flex vertical align="center">
          <Text className={Style.value}>{formatDigits(initial ?? 0)}</Text>
          <Text className={Style.small_text}>Initial</Text>
        </Flex>
        <Flex vertical align="center">
           <Text className={Style.value}>{formatDigits(current ?? 0)}</Text>
           <Text className={Style.small_text}>Current</Text>
        </Flex>
      </Flex>
    </Flex>
  );
}

AnalysisCategoryCard.propTypes = {
  title: PropTypes.string,
  textColor: PropTypes.string,
  current: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  initial: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
};

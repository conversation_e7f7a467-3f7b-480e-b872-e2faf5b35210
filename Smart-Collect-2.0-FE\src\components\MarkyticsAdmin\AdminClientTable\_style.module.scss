@import url("../../../index.css");

$table-radius:22px;
$attempts-bg:#BDD1FF;
$disable:#787777;
$body:#E4F8F9;

.customTable{
    padding-top: 1.5rem;
    .name{
        font-weight: 500;
        color:#1651D7;
        font-size:0.8rem;
        text-transform: uppercase;
    }
   
    //Global ant design classes
    // Pagination
    :global(.ant-pagination){
        justify-content: center;
        margin: 0 !important;
        
        :global(.ant-pagination-prev),
        :global(.ant-pagination-next){
            color: var(--dark-blue);
            border: 0 solid var(--blue);
            background: #E4F8F9;
            height: 25px;
            min-width: 15px;
            border-radius: 0px;
            margin: 0;
            button{
                border-radius: 0px;
            }
        }
        :global(.ant-pagination-item){
            margin-right: 0;
            height: 0;
            a{
                color:$disable;
                font-size: 0.9rem;
                line-height: 23px;
                font-family: 'Kanit','Courier New', Courier, monospace;
                font-weight: normal;
            }
            &:hover{
                background-color: transparent;
            }
        }
        :global(.ant-pagination-item-active){
            border:none;
           a{
                color:var(--dark-blue);
                font-size:1.2rem;
                padding: 0;
           }
        }
    }

    //Table container
    :global(.ant-table-container){
        padding: 0;
        margin-bottom: 0.5rem;
        border-start-start-radius: $table-radius;
        border-start-end-radius: $table-radius;

        // Table header
        :global(.ant-table-header){
            position: relative;
            margin: 0 auto;
            top: -21px;
            border-radius: $table-radius;
        }

        // Table virtual body
        :global(.ant-table-tbody-virtual){
            margin-top: -8px;
        }

        &::before{
            box-shadow: none !important;
        }
    }

    // Table rows header 
    :global(.ant-table-thead >tr){
        th{
            border-start-end-radius:0 !important;
            background-color: var(--dark-blue);
            border-inline-end: none !important;
            color: white;
            border-bottom:none;
            font-weight: normal;
            font-family: 'Kanit','Courier New', Courier, monospace;

            &:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before{
                width: 0;
            }
            &:last-child{
                text-align: end;
                padding-right: 2rem;
            }
        }
    }

    // Table body
    :global(.ant-table-tbody){
        // Body rows
        :global(.ant-table-row){  
            background-color: white;

            &:nth-child(even){
                background-color: var(--light-green) !important;
                :global(.ant-table-cell-row-hover){
                    background-color: var(--light-green);
                }
            }         
            // Cols
            :global(.ant-table-cell){
                    font-family: 'Kanit','Courier New', Courier, monospace;                    
                    border-bottom: 2px solid white; 
                    padding: 1rem;
                    font-weight: 500;
                
                    &:last-child{
                        text-align: end;
                        padding-right: 3rem;
                    }
            }

            :global(.ant-table-cell-row-hover){
                background-color: white;
            }

        }
    }

    // Fixed Cols
    :global(.ant-table-cell-fix-right){
        background-color: $body;
   }
}
// Responsive view
@media screen and (max-width:768px) {
    .customTable{
        .name{
            font-size: 11px;
        }

        :global(.ant-table-thead >tr){
            th{
                font-size: 10px;
                padding: 0.5rem;
            }
        }

        :global(.ant-table-tbody){
            // Body rows
            :global(.ant-table-row){ 
                :global(.ant-table-cell){
                    padding: 0.5rem;
                    font-size: 10px;
                    :global(.ant-typography){
                        font-size: 10px;
                    }
                }
            }
        }
    }
}
import pandas as pd
from sqlalchemy import create_engine
from datetime import datetime, timedelta
import re
from difflib import SequenceMatcher

# Database configuration
DATABASE = "SmartCollect2"
USERNAME = "postgres"
PASSWORD = "Markytics12345"
HOST = "*************"
PORT = 5432

# Create database engine
engine = create_engine(f"postgresql://{USERNAME}:{PASSWORD}@{HOST}:{PORT}/{DATABASE}")

# Data cleaning functions
def clean_column(value):
    if pd.isna(value) or str(value).strip() in ['', 'nan', 'None']:
        return None
    return str(value).strip()

# Function to convert Excel serial date to Python date
def convert_excel_serial_date(serial):
    if pd.isna(serial) or str(serial).strip() in ['', 'nan', 'None']:
        return None
    
    try:
        # First try parsing as string date
        if isinstance(serial, str) and not serial.replace('.', '').replace('-', '').isdigit():
            # Try common date formats
            for fmt in ['%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%d-%m-%Y', '%d-%m-%y']:
                try:
                    return datetime.strptime(serial.strip(), fmt).date()
                except:
                    continue
        
        # Convert to numeric if it's a string number
        try:
            if isinstance(serial, str):
                serial_num = float(serial.strip())
            else:
                serial_num = float(serial)
        except (ValueError, TypeError):
            return None
        
        # Handle Excel serial dates
        # Excel uses January 1, 1900 as serial number 1
        # But there's a bug: Excel treats 1900 as a leap year (it's not)
        # So we need to account for this
        if serial_num > 0:
            try:
                # For dates after March 1, 1900 (serial > 59), subtract 1 day
                # to account for Excel's leap year bug
                if serial_num > 59:  # After Feb 28, 1900
                    days_since_epoch = serial_num - 1
                else:
                    days_since_epoch = serial_num
                
                # Excel epoch: January 1, 1900 is serial number 1
                # So we start from December 31, 1899 (day 0)
                excel_epoch = datetime(1899, 12, 31)
                converted_date = excel_epoch + timedelta(days=days_since_epoch)
                return converted_date.date()
            except (OverflowError, ValueError) as e:
                print(f"Date conversion overflow for {serial}: {e}")
                return None
        
        return None
        
    except Exception as e:
        print(f"Error converting date {serial}: {e}")
        return None

def clean_mobile_number(mobile):
    if pd.isna(mobile) or str(mobile).strip() in ['', 'nan', 'None']:
        return None
    
    mobile_str = str(mobile).strip()
    
    # Remove any non-digit characters except +
    mobile_cleaned = re.sub(r'[^\d+]', '', mobile_str)
    
    # If it starts with +91, remove it
    if mobile_cleaned.startswith('+91'):
        mobile_cleaned = mobile_cleaned[3:]
    elif mobile_cleaned.startswith('91') and len(mobile_cleaned) == 12:
        mobile_cleaned = mobile_cleaned[2:]
    
    # Validate length (should be 10 digits for Indian mobile)
    if len(mobile_cleaned) == 10 and mobile_cleaned.isdigit():
        return mobile_cleaned
    
    return mobile_str  # Return original if can't clean properly

def clean_numeric_column(value, data_type='float'):
    """Clean numeric columns and convert to appropriate type"""
    if pd.isna(value) or str(value).strip() in ['', 'nan', 'None', '#N/A', '0']:
        return None
    
    try:
        # Remove any commas and other non-numeric characters except decimal point
        clean_value = re.sub(r'[^\d.-]', '', str(value))
        
        if clean_value == '' or clean_value == '-':
            return None
        
        if data_type == 'int':
            return int(float(clean_value))
        else:
            return float(clean_value)
    except (ValueError, TypeError):
        return None

def clean_disbursement_id(value):
    if pd.isna(value) or str(value).strip() in ['', 'nan', 'None']:
        return None
    value = str(value)
    # Remove only - characters
    value = value.replace("-", "")
    return value.strip()

def process_excel_file(excel_path):
    try:
        # Read the Excel file
        data = pd.read_excel(excel_path, header=None)
        
        print("Raw Excel file structure:")
        print(data.head(10))
        print("\n" + "="*50)
        
        # Find the actual header row (usually contains column names like "Demand Date", "Branch", etc.)
        header_row_idx = None
        for idx in range(min(10, len(data))):  # Check first 10 rows
            row_values = data.iloc[idx].astype(str).str.lower()
            # Look for typical column indicators
            if any(keyword in ' '.join(row_values.values) for keyword in ['demand date', 'branch', 'member', 'loan']):
                header_row_idx = idx
                break
        
        if header_row_idx is None:
            print("Could not find header row automatically. Assuming row 3.")
            header_row_idx = 3
        
        print(f"Using row {header_row_idx} as header")
        
        # Set column names from the identified header row
        column_names = data.iloc[header_row_idx].fillna("Unknown_Column").astype(str)
        
        # Create unique column names in case of duplicates
        seen_names = {}
        unique_names = []
        for name in column_names:
            clean_name = str(name).strip()
            if clean_name in seen_names:
                seen_names[clean_name] += 1
                unique_names.append(f"{clean_name}_{seen_names[clean_name]}")
            else:
                seen_names[clean_name] = 0
                unique_names.append(clean_name)
        
        data.columns = unique_names
        
        # Skip the header rows and reset index - THIS IS THE KEY FIX
        data = data.iloc[header_row_idx + 1:].reset_index(drop=True)
        
        # Remove completely empty rows
        data = data.dropna(how='all')
        
        print(f"\nProcessed Excel file with {len(data)} rows and {len(data.columns)} columns")
        print("Column names:")
        for i, col in enumerate(data.columns, 1):
            print(f"{i:2d}. {col}")
        
        print(f"\nFirst few rows of actual data:")
        print(data.head(3))
        
        return data
    except Exception as e:
        print(f"Error reading Excel file: {e}")
        return None

def similarity(a, b):
    """Calculate similarity between two strings"""
    a_str = str(a) if not pd.isna(a) else ""
    b_str = str(b) if not pd.isna(b) else ""
    return SequenceMatcher(None, a_str.lower(), b_str.lower()).ratio()

def find_best_match(target_column, excel_columns, threshold=0.6):
    """Find the best matching column name from Excel columns"""
    best_match = None
    best_score = 0
    
    for excel_col in excel_columns:
        if pd.isna(excel_col) or "Unknown_Column" in str(excel_col):
            continue
            
        score = similarity(target_column, excel_col)
        if score > best_score and score >= threshold:
            best_score = score
            best_match = excel_col
    
    return best_match, best_score

def map_columns(data):
    """Map Excel columns to database columns automatically"""
    
    # Define target database columns
    db_columns = [
        "Demand Date", "Mobile Number", "Member", "Contact No", "Loan Scheme",
        "Loan Status", "Loan Date", "Loan Amount", "Collection Date",
        "Last Receive Date", "Last Receive Amt", "Savings Demand Amount",
        "Total All Demand", "Loan No", "EMI Amount", "pos"
    ]
    
    # Get Excel column names (filter out NaN and unknown columns)
    excel_columns = [col for col in data.columns.tolist() 
                    if not pd.isna(col) and "Unknown_Column" not in str(col)]
    
    # Create mapping dictionary
    column_mapping = {}
    final_df = pd.DataFrame()
    
    print("Column Mapping Results:")
    print("=" * 80)
    
    # Map each database column to best matching Excel column
    for db_col in db_columns:
        best_match, score = find_best_match(db_col, excel_columns)
        
        if best_match:
            column_mapping[db_col] = best_match
            
            # Apply data cleaning based on column type
            if db_col in ["Demand Date", "Loan Date", "Collection Date", "Last Receive Date"]:
                final_df[db_col] = data[best_match].apply(convert_excel_serial_date)
            elif db_col in ["Mobile Number", "Contact No"]:
                final_df[db_col] = data[best_match].apply(clean_mobile_number)
            elif db_col == "Loan No":
                final_df[db_col] = data[best_match].apply(clean_disbursement_id)
            elif db_col in ["Member", "Loan Scheme", "Loan Status"]:
                final_df[db_col] = data[best_match].apply(clean_column)
            elif db_col in ["Loan Amount", "Last Receive Amt", "Savings Demand Amount", "Total All Demand", "EMI Amount", "pos"]:
                final_df[db_col] = data[best_match].apply(lambda x: clean_numeric_column(x, 'int'))
            else:
                final_df[db_col] = data[best_match].apply(clean_column)
            
            print(f"{db_col:25} <- {best_match:35} (Score: {score:.2f})")
        else:
            # If no good match found, fill with None
            final_df[db_col] = None
            print(f"{db_col:25} <- {'No match found':35} (Score: 0.00)")
    
    # Add special columns
    final_df['inserted_date'] = datetime.now().date()
    final_df['day_type'] = 'afternoon'
    
    print("\nAdded columns:")
    print(f"{'inserted_date':25} <- {datetime.now().date()}")
    print(f"{'day_type':25} <- afternoon")
    
    print(f"\nTotal rows mapped: {len(final_df)}")
    
    return final_df, column_mapping

def show_unmapped_columns(data, column_mapping):
    """Show Excel columns that weren't mapped"""
    excel_columns = set([col for col in data.columns.tolist() 
                        if not pd.isna(col) and "Unknown_Column" not in str(col)])
    mapped_columns = set(column_mapping.values())
    unmapped = excel_columns - mapped_columns
    
    if unmapped:
        print(f"\nUnmapped Excel columns ({len(unmapped)}):")
        print("-" * 40)
        for col in sorted(unmapped):
            print(f"  - {col}")
    else:
        print("\nAll Excel columns were mapped!")

def insert_to_database(final_df, table_name):
    """Insert mapped data to database"""
    try:
        # Show a preview before inserting
        print(f"\nPreview of data to be inserted:")
        print(final_df.head())
        print(f"\nData types:")
        print(final_df.dtypes)
        
        final_df.to_sql(table_name, engine, if_exists='append', index=False)
        print(f"\nData successfully inserted into {table_name} table!")
        return True
    except Exception as e:
        print(f"\nError inserting data: {str(e)}")
        return False

# Test function for date conversion
def test_date_conversion():
    """Test the date conversion function with known values"""
    test_cases = [
        (45874, "2025-08-05"),  # Your current case
        (1, "1900-01-01"),      # Excel serial 1 = Jan 1, 1900
        (39448, "2008-01-01"),  # Known from Microsoft docs
    ]
    
    print("Testing date conversion:")
    for serial, expected in test_cases:
        print(f"Input: {serial} (type: {type(serial)})")
        
        # Manual calculation for verification
        try:
            # Excel logic: serial 1 = Jan 1, 1900
            # So serial 0 would be Dec 31, 1899
            excel_epoch = datetime(1899, 12, 31)
            if serial > 59:  # Account for leap year bug
                manual_result = excel_epoch + timedelta(days=serial - 1)
            else:
                manual_result = excel_epoch + timedelta(days=serial)
            print(f"Manual calc: {manual_result.date()}")
        except Exception as e:
            print(f"Manual calc failed: {e}")
        
        result = convert_excel_serial_date(serial)
        print(f"Function result: {result} (expected: {expected})")
        print("-" * 40)
    print()

# Main execution
if __name__ == "__main__":
    # Test date conversion first
    test_date_conversion()
    
    # Process Excel file
    excel_path = r"C:\Users\<USER>\Downloads\NotCollectedData05Aug2025.xlsx"
    df = process_excel_file(excel_path)
    
    if df is not None:
        print("Excel file loaded successfully!")
        
        # Map columns
        print("\n" + "="*80)
        final_df, column_mapping = map_columns(df)
        
        # Show unmapped columns
        show_unmapped_columns(df, column_mapping)
        
        # Preview final dataframe
        print(f"\nFinal mapped dataframe preview:")
        print("-" * 50)
        print(final_df.head())
        
        # Show non-null data counts
        print(f"\nNon-null data counts:")
        print(final_df.count())
        
        # Ask user if they want to insert to database
        table_name = "not_collected_data"
        
        user_input = input(f"\nDo you want to insert {len(final_df)} rows into '{table_name}' table? (y/n): ")
        if user_input.lower() in ['y', 'yes']:
            success = insert_to_database(final_df, table_name)
            if success:
                print(f"Successfully inserted {len(final_df)} records into {table_name}")
        else:
            print("Data not inserted. You can review the mapping above.")
            
        # Optionally save to CSV for review
        csv_output = input("\nSave mapped data to CSV for review? (y/n): ")
        if csv_output.lower() in ['y', 'yes']:
            output_path = "mapped_data.csv"
            final_df.to_csv(output_path, index=False)
            print(f"Data saved to {output_path}")
    
    else:
        print("Failed to load Excel file. Please check the file path and try again.")
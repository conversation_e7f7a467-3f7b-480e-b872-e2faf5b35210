import { Flex, Table, Typography } from "antd";
import React, { useEffect, useState } from "react";
import {
  ALL_CACHE_NAMES,
  formatAmount,
  formatDigits,
  handleDateFormatter,
} from "../../../constant";
import Style from "./_style.module.scss";
import PropTypes from "prop-types";
import { AXIOS } from "../../../apis/ho-Instance";
import {
  getCacheKey,
  getFromCache,
  storeToCache,
} from "../../../utils/cacheHelper";
import ApplicationLoader from "../../ApplicationLoader";
import ReportsButton from "../ReportsButton";

const { Text } = Typography;
const CACHE_NAME = ALL_CACHE_NAMES.REPORTS_CACHE;

export default function Accounts({ currentTab }) {
  const [loading, setLoading] = useState(false);
  const [accountDetails, setAccountDetails] = useState({
    data: [],
    fromDate: null,
    toDate: null,
  });

  const [filterAccountDetails, setFilterAccountDetails] = useState({
    data: [],
    fromDate: null,
    toDate: null,
  });

  const handleFetchAccounts = async () => {
    setLoading(true);
    const cacheKey = getCacheKey({ endpoint: `reports/accounts` });
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setAccountDetails({
        fromDate: cachedData?.from_date,
        toDate: cachedData?.to_date,
        data: cachedData[0]?.account_summary_report,
      });
      setFilterAccountDetails({
        fromDate: cachedData?.from_date,
        toDate: cachedData?.to_date,
        data: cachedData[0]?.account_summary_report,
      });
      setLoading(false);
    }
    try {
      const res = await AXIOS.get("accountsummaryreport/");
      const data = res.data;
      setAccountDetails({
        fromDate: data?.from_date,
        toDate: data?.to_date,
        data: data[0]?.account_summary_report,
      });
      setFilterAccountDetails({
        fromDate: data?.from_date,
        toDate: data?.to_date,
        data: data[0]?.account_summary_report,
      });
      await storeToCache({
        cacheName: CACHE_NAME,
        key: cacheKey,
        data: data,
      });
    } catch (error) {
      console.warn("Error in accounts :", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    handleFetchAccounts();
  }, [currentTab]);

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      render: (_, data) => <Text>{data.key}.</Text>,
    },
    {
      title: "Customer ID",
      dataIndex: "CustomerID",
      sorter: (a, b) => (a.CustomerID || 0) - (b.CustomerID || 0),
      render: (CustomerID) => <Text>{CustomerID}</Text>,
    },

    {
      title: "Customer Name",
      dataIndex: "CustomerName",
      sorter: (a, b) => a.CustomerName?.localeCompare(b.CustomerName),
      render: (CustomerName) => <Text>{CustomerName}</Text>,
    },
    {
      title: "Branch Name",
      dataIndex: "Branch",
      sorter: (a, b) => a.Branch?.localeCompare(b.Branch),
      render: (Branch) => <Text>{Branch}</Text>,
    },
    {
      title: "Mobile Number",
      dataIndex: "MobileNumber",
      sorter: (a, b) => a.MobileNumber?.localeCompare(b.MobileNumber),
      render: (MobileNumber) => <Text>{MobileNumber}</Text>,
    },
    {
      title: "Disbursement ID",
      dataIndex: "DisbursementID",
      sorter: (a, b) => (a.DisbursementID || 0) - (b.DisbursementID || 0),
      render: (DisbursementID) => <Text>{DisbursementID}</Text>,
    },
    {
      title: "Next EMI Date",
      dataIndex: "NextEMIDate",
      sorter: (a, b) => a.NextEMIDate?.localeCompare(b.NextEMIDate),
      render: (NextEMIDate) => (
        <Text>{NextEMIDate ? handleDateFormatter(NextEMIDate) : "--"}</Text>
      ),
    },
    {
      title: "EMI Amount",
      dataIndex: "EMIAmount",
      sorter: (a, b) => (a.EMIAmount || 0) - (b.EMIAmount || 0),
      render: (value) => (
        <Text className={Style.blue_text}>₹ {formatAmount(value || 0)}</Text>
      ),
    },
    {
      title: "Overdue Amount",
      dataIndex: "OverDueAmt",
      sorter: (a, b) => (a.OverDueAmt || 0) - (b.OverDueAmt || 0),
      render: (OverDueAmt) => <Text>₹ {formatAmount(OverDueAmt ?? 0)}</Text>,
    },
    {
      title: "Latest Collected Amount",
      dataIndex: "LastCollectedAmount",
      sorter: (a, b) =>
        (a.LastCollectedAmount || 0) - (b.LastCollectedAmount || 0),
      render: (LastCollectedAmount) => (
        <Text className={Style.blue_text}>
          ₹ {formatAmount(LastCollectedAmount ?? 0)}
        </Text>
      ),
    },
    {
      title: "Loan Type",
      dataIndex: "LoanType",
      sorter: (a, b) => a.LoanType?.localeCompare(b.LoanType),
      render: (LoanType) => <Text>{LoanType}</Text>,
    },
    {
      title: "Next EMI Amount",
      dataIndex: "next_emi_amount",
      sorter: (a, b) => (a.next_emi_amount || 0) - (b.next_emi_amount || 0),
      render: (next_emi_amount) => (
        <Text>₹ {formatAmount(next_emi_amount || 0)}</Text>
      ),
    },

    {
      title: "Total Collection",
      dataIndex: "total_collection",
      sorter: (a, b) => (a.total_collection || 0) - (b.total_collection || 0),
      render: (total_collection) => (
        <Text className={Style.blue_text}>
          ₹ {formatAmount(total_collection ?? 0)}
        </Text>
      ),
    },
    {
      title: "Latest Collected Date",
      dataIndex: "latest_collected_date",
      sorter: (a, b) =>
        a.latest_collected_date?.localeCompare(b.latest_collected_date),
      render: (latest_collected_date) => (
        <Text>
          {latest_collected_date
            ? handleDateFormatter(latest_collected_date)
            : "--"}
        </Text>
      ),
    },

    {
      title: "WhatsApp",
      dataIndex: "whatsapp_count",
      sorter: (a, b) => (a.whatsapp_count || 0) - (b.whatsapp_count || 0),
      render: (value) => (
        <Text className={Style.blue_text}>{formatDigits(value ?? 0)}</Text>
      ),
    },
    {
      title: "Blaster",
      dataIndex: "blaster_count",
      sorter: (a, b) => (a.blaster_count || 0) - (b.blaster_count || 0),
      render: (value) => <Text>{formatDigits(value ?? 0)}</Text>,
    },
    {
      title: "AI Calls",
      dataIndex: "voicebot_count",
      sorter: (a, b) => (a.voicebot_count || 0) - (b.voicebot_count || 0),
      render: (value) => <Text>{formatDigits(value ?? 0)}</Text>,
    },
    {
      title: "SMS",
      dataIndex: "sms_count",
      sorter: (a, b) => (a.sms_count || 0) - (b.sms_count || 0),
      render: (value) => <Text>{formatDigits(value ?? 0)}</Text>,
    },
    {
      title: "Total Communications",
      dataIndex: "total_communications",
      sorter: (a, b) =>
        (a.total_communications || 0) - (b.total_communications || 0),
      render: (value) => <Text>{formatDigits(value ?? 0)}</Text>,
    },
    {
      title: "Pending Promise Count",
      dataIndex: "promise_pending_count",
      sorter: (a, b) =>
        (a.promise_pending_count || 0) - (b.promise_pending_count || 0),
      render: (value) => <Text>{formatDigits(value ?? 0)}</Text>,
    },
    {
      title: "Pending Broken Count",
      dataIndex: "promise_broken_count",
      sorter: (a, b) =>
        (a.promise_broken_count || 0) - (b.promise_broken_count || 0),
      render: (value) => <Text>{formatDigits(value ?? 0)}</Text>,
    },
    {
      title: "Pending Fulfilled Count",
      dataIndex: "promise_fulfilled_count",
      sorter: (a, b) =>
        (a.promise_fulfilled_count || 0) - (b.promise_fulfilled_count || 0),
      render: (value) => <Text>{formatDigits(value ?? 0)}</Text>,
    },
    {
      title: "Promise Partially Paid Count",
      dataIndex: "promise_partiallypaid_count",
      sorter: (a, b) =>
        (a.promise_partiallypaid_count || 0) -
        (b.promise_partiallypaid_count || 0),
      render: (value) => <Text>{formatDigits(value ?? 0)}</Text>,
    },
    {
      title: "Promise Terminated Count",
      dataIndex: "promise_terminated_count",
      sorter: (a, b) =>
        (a.promise_terminated_count || 0) - (b.promise_terminated_count || 0),
      render: (value) => <Text>{formatDigits(value ?? 0)}</Text>,
    },
    {
      title: "Promise Pending Amount",
      dataIndex: "promise_pending_amount",
      sorter: (a, b) =>
        (a.promise_pending_amount || 0) - (b.promise_pending_amount || 0),
      render: (value) => <Text>₹{formatAmount(value ?? 0)}</Text>,
    },
    {
      title: "Promise Broken Amount",
      dataIndex: "promise_broken_amount",
      sorter: (a, b) =>
        (a.promise_broken_amount || 0) - (b.promise_broken_amount || 0),
      render: (value) => <Text>₹{formatAmount(value ?? 0)}</Text>,
    },
    {
      title: "Promise Fulfilled Amount",
      dataIndex: "promise_fulfilled_amount",
      sorter: (a, b) =>
        (a.promise_fulfilled_amount || 0) - (b.promise_fulfilled_amount || 0),
      render: (value) => <Text>₹{formatAmount(value ?? 0)}</Text>,
    },
    {
      title: "Promise Partially Paid Amount",
      dataIndex: "promise_partiallypaid_amount",
      sorter: (a, b) =>
        (a.promise_partiallypaid_amount || 0) -
        (b.promise_partiallypaid_amount || 0),
      render: (value) => <Text>₹{formatAmount(value ?? 0)}</Text>,
    },
    {
      title: "Promise Terminate Amount",
      dataIndex: "promise_terminated_amount",
      sorter: (a, b) =>
        (a.promise_terminated_amount || 0) - (b.promise_terminated_amount || 0),
      render: (value) => <Text>₹{formatAmount(value ?? 0)}</Text>,
    },
    {
      title: "Promise Total Count",
      dataIndex: "promise_total_count",
      sorter: (a, b) =>
        (a.promise_total_count || 0) - (b.promise_total_count || 0),
      render: (value) => <Text>{formatDigits(value ?? 0)}</Text>,
    },
    {
      title: "Promise Total Amount",
      dataIndex: "promise_total_amount",
      sorter: (a, b) =>
        (a.promise_total_amount || 0) - (b.promise_total_amount || 0),
      render: (value) => <Text>₹{formatAmount(value ?? 0)}</Text>,
    },
    {
      title: "Denied Count",
      dataIndex: "denied_count",
      sorter: (a, b) => (a.denied_count || 0) - (b.denied_count || 0),
      render: (value) => <Text>{formatDigits(value ?? 0)}</Text>,
    },
    {
      title: "Already Paid Count",
      dataIndex: "alreadypaid_count",
      sorter: (a, b) => (a.alreadypaid_count || 0) - (b.alreadypaid_count || 0),
      render: (value) => <Text>{formatDigits(value ?? 0)}</Text>,
    },
    {
      title: "Already Paid Amount",
      dataIndex: "alreadypaid_amount",
      sorter: (a, b) =>
        (a.alreadypaid_amount || 0) - (b.alreadypaid_amount || 0),
      render: (value) => <Text>₹{formatAmount(value ?? 0)}</Text>,
    },
    {
      title: "Latest Response Date",
      dataIndex: "latest_response_date",
      sorter: (a, b) =>
        a.latest_response_date?.localeCompare(b.latest_response_date),
      render: (value) => (
        <Text>{value ? handleDateFormatter(value) : "--"}</Text>
      ),
    },
    {
      title: "Total Collected Amount",
      dataIndex: "total_collected_amount",
      sorter: (a, b) =>
        (a.total_collected_amount || 0) - (b.total_collected_amount || 0),
      render: (value) => <Text>₹{formatAmount(value ?? 0)}</Text>,
    },
    {
      title: "Latest Collected Date",
      dataIndex: "latest_collected_date",
      sorter: (a, b) =>
        a.latest_collected_date?.localeCompare(b.latest_collected_date),
      render: (value) => (
        <Text>{value ? handleDateFormatter(value) : "--"}</Text>
      ),
    },
    {
      title: "Latest Collected Amount",
      dataIndex: "latest_collected_amount",
      sorter: (a, b) =>
        (a.latest_collected_amount || 0) - (b.latest_collected_amount || 0),
      render: (value) => <Text>₹{formatAmount(value ?? 0)}</Text>,
    },
    {
      title: "Latest Overdue Amount",
      dataIndex: "latest_overdueamt",
      sorter: (a, b) => (a.latest_overdueamt || 0) - (b.latest_overdueamt || 0),
      render: (value) => <Text>₹{formatAmount(value ?? 0)}</Text>,
    },
    {
      title: "Latest DPD",
      dataIndex: "latest_dpd",
      sorter: (a, b) => (a.latest_dpd || 0) - (b.latest_dpd || 0),
      render: (value) => <Text>{formatDigits(value ?? 0)}</Text>,
    },
    {
      title: "First Overdue Amount",
      dataIndex: "first_overdueamt",
      sorter: (a, b) => (a.first_overdueamt || 0) - (b.first_overdueamt || 0),
      render: (value) => <Text>₹{formatAmount(value ?? 0)}</Text>,
    },
    {
      title: "First DPD",
      dataIndex: "first_dpd",
      sorter: (a, b) => (a.first_dpd || 0) - (b.first_dpd || 0),
      render: (value) => <Text>{formatDigits(value ?? 0)}</Text>,
    },
  ];

  const dataSource = Array.isArray(filterAccountDetails.data)
    ? filterAccountDetails.data.map((d, i) => ({ key: i + 1, ...d }))
    : [];
  return (
    <Flex vertical style={{ width: "100%" }} gap={10}>
      <Flex justify="end">
        <ReportsButton
          filteredData={filterAccountDetails.data}
          data={accountDetails.data}
          excelName={"Accounts"}
          fromDate={filterAccountDetails.fromDate}
          toDate={filterAccountDetails.toDate}
          onFilterApply={(filtered) => {
            setFilterAccountDetails({
              fromDate: filtered.fromDate ?? null,
              toDate: filtered.toDate ?? null,
              data: filtered.data ?? [],
            });
          }}
        />
      </Flex>
      {loading ? (
        <ApplicationLoader />
      ) : (
        <Table
          size="small"
          virtual
          className={Style.customTable}
          columns={columns}
          dataSource={dataSource}
          scroll={{
            x: 1000 * 8,
            y: 300,
          }}
          pagination={{
            showSizeChanger: false,
          }}
        />
      )}
    </Flex>
  );
}

Accounts.propTypes = {
  currentTab: PropTypes.string,
};

import pandas as pd
import numpy as np
from datetime import datetime
from ..queries import insert_data_in_raw_table
from ..utils import *
import threading
from ..sp import execute_post_processing_procedures

def run_post_processing_in_background(bank_id):
    """
    Run post-processing procedures in a background thread
    """
    try:
        print(f"Starting background post-processing for bank_id: {bank_id}")
        execute_post_processing_procedures(bank_id)
        print(f"Background post-processing initiated for bank_id: {bank_id}")
    except Exception as e:
        print(f"Error starting background post-processing for bank_id {bank_id}: {str(e)}")

def process_shareindia_raw_data(df1):
    db_operation_success = False
    db_error_message = None
    df = pd.DataFrame()
    frequency_mapping = {
        "fortnightly": 15,
        "fornightly": 15,  # Common typo
        "fortnight": 15,
        "monthly": 30,
        "weekly": 7,
        "daily": 1,
        "quarterly": 90,
        "bi-monthly": 60,
        "bimonthly": 60,
        "bi weekly":15,
        'm':30
    }
    df1.columns = df1.columns.str.strip().str.replace(r'\s+', ' ', regex=True)
    df['customerid'] = df1['CustomerID']
    df['disbursementid'] = df1['DisbursementID'].astype(str)
    df['loantype'] = df1['LoanType']
    df['customername'] = df1['CustomerName'].astype(str).str.upper().str.strip()
    df['disbursementamount'] = df1['DisbursementAmount']
    df['rateofinterest'] = df1['Rateofinterest']
    df['repaymenttenure'] = df1['RepaymentTenure']
    df['emiamount'] = df1['EMIAmount']
    df['paymentfrequency'] = df1['PaymentFreequency'].astype(str).str.lower().str.strip()
    df['paymentfrequency'] = df['paymentfrequency'].map(frequency_mapping)
    df['numberofdayspastdue'] = df1['NumberOfDaysPastDue']
    df['mobileno'] = df1['MobileNo.']
    df['inststartdate']=df1['InstStartDate'].apply(convert_excel_serial_date)
    df['collectionofficerid'] = df1['CollectionOfficerID']
    df['collectionofficername'] = df1['CollectionOfficerName']
    df['branchname'] = df1['BranchName']
    df['branchcode'] = df1['BranchCode'].str.lstrip('B')
    df['applicanttype'] = df1['ApplicantType']
    df['totaloutstanding'] = df1['Total Outstanding']
    df['overdueamount'] = df1['OverdueAmount']
    df['collectedamount'] = df1['CollectedAmount']
    df['collectiondate'] = df1['CollectionDate'].apply(convert_excel_serial_date)
    df['pos'] = df1['POS']
    df["language"]=df1['Primary Language']
    df['originaldisbursementid'] = df1['DisbursementID'].astype(str).str.strip()   
    # Standard fields
    df['bankmstid'] = 35
    df['inserted_date'] = datetime.now().date()
    # df.to_excel("shareindia.xlsx", index=False)
    try:
        result = insert_data_in_raw_table(df, bank_id=35)
        db_operation_success = result['success']
        if not result['success']:
            db_error_message = result['message']
    except Exception as e:
        db_operation_success = False
        db_error_message = str(e)
        print(f"Database update failed for bssbihar data: {e}")
    if db_operation_success:
        # Start post-processing in background thread
        bank_id = 35
        background_thread = threading.Thread(
            target=run_post_processing_in_background,
            args=(bank_id,),
            daemon=True
        )
        background_thread.start()
        print(f"Post-processing started in background thread for bank_id: {bank_id}")
    else:
        print(f"Skipping post-processing due to database operation failure: {db_error_message}")
    return {
        "Total records processed": len(df),
        "New records inserted": result.get('inserted_count', 0) if db_operation_success else 0,
        "Duplicate records found": result.get('duplicate_count', 0) if db_operation_success else 0,
        "db_operation_success": db_operation_success,
        "db_error_message": db_error_message,
        "message": result.get('message', '') if db_operation_success else db_error_message,
    }
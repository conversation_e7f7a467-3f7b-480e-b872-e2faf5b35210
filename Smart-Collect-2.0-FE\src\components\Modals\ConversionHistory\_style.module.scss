@import url("../../../index.css");

$light-gray:#D9D9D9;
$tab-height: 7px;

.history_modal{
    .icon_img{
        width: 13px;
        img{
            width: 100%;
            object-fit: contain;
        }
    }

    .history_title{
        margin: 0;
        color: white;
        font-size:16px; 
        font-family: 'Kanit','Courier New', Courier, monospace;
        font-weight: 400;
        text-transform: uppercase;
    }

    .history_button{
        background-color: var(--dark-blue);
        color: white;
        padding: 1rem 2rem;
        border: none;
        font-family: 'Kanit','Courier New', Courier, monospace;

        &:hover{
            background-color: var(--dark-blue) !important;
            color: white !important;
            border: none;
            box-shadow:none;
        }
    }

    :global(.ant-modal-close){
        right: 14px;
        top: 10px;
        color: white;
        &:hover{
          color: white !important;
        }
        :global(.ant-modal-close-x){
          font-size:10px;
          color: white;
        }
    }  

    :global(.ant-modal-header){
        background: var(--dark-blue);
        padding: 0.5rem 0px;
        text-align: center;
        border-radius: 8px;
        
        :global(.ant-modal-title){
            color: white;
            font-weight: 500;
        }
    }

    :global(.ant-modal-content){
       padding: 6px;

       :global(.ant-modal-body){                                                                                                                                                                                                                                                                                                                                                                                                                                                          
        padding: 1rem;
        height: 80vh;
        overflow-y: scroll;
        /* width */
        &::-webkit-scrollbar {
            width: 5px;
        }

        /* Track */
        &::-webkit-scrollbar-track {
            background: #f1f1f1; 
        }
        
        /* Handle */
        &::-webkit-scrollbar-thumb {
            background: var(--blue); 
        }

        /* Handle on hover */
        &::-webkit-scrollbar-thumb:hover {
            background: var(--dark-blue); 
        }
       }
    }
}
// Responsive view
@media screen and (max-width:768px) {
    .history_modal{
        .icon_img{
            width: 10px;
            height: 24px;
        }
        .history_title{
            font-size: 12px;
        }
        .history_button{
            padding: 0rem 1rem;
            font-size: 11px;
        }
        :global(.ant-modal-header){
            padding: 0.2rem 0;
        }
        :global(.ant-modal-close){
            top: 3px;
        }
      :global(.ant-modal-content){
        padding: 4px;

        :global(.ant-modal-body){  
            padding: 0.3rem;
            :global(.ant-tabs){
                :global(.ant-tabs-ink-bar){
                    height: 4px;
                }

                :global(.ant-tabs-nav){
                    :global(.ant-tabs-tab){
                        font-size: 10px;
                    }
                    &::before{
                        border-bottom: 4px solid $light-gray;
                    }
                }
            }
        }
      }
    }
}
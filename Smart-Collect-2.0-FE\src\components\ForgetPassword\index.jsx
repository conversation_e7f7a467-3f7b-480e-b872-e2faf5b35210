// main.jsx
import React, { useState } from "react";
import UserDetails from "./UserDetails";
import OtpPage from "./EnterOTP";
import UpdatePassword from "./UpdatePassword";
import Style from "./_forget-password.module.scss";
import COMPANY_LOGO from "../../assets/Images/logo.png";
import COMPANY_NAME_IMG_BLUE from "../../assets/Images/logo-title-blue.png";
import MARKYTICS_LOGO from "../../assets/Images/Rightside_Logo.png";
import { Card, Flex } from "antd";

export default function ForgetPassword() {
  const [step, setStep] = useState(2);
  const [email, setEmail] = useState("");
  const [UserID, setUserID] = useState("");
  const [otp, setOtp] = useState("");
  const stepTitles = {
    1: "OTP Verification",
    2: "OTP Verification",
    3: "Forgot Password",
  };
  return (
    <Flex className={Style.container}>
      {/* left Side with Login Form */}
      <Flex className={Style.leftSide}>
        {/* Header */}
        <div className={Style.header}>
          <div className={Style.logo_container}>
            <div className={Style.logo_image}>
              <img src={COMPANY_LOGO} alt="Company Logo" />
            </div>
            <div className={Style.logo_title_image}>
              <img
                src={COMPANY_NAME_IMG_BLUE}
                className={Style.desktopLogo}
                alt="Company Name"
              />
            </div>
          </div>
        </div>
        {/* Card */}
        <div className={Style.cardWrapper}>
          <Card className={Style.card} title={stepTitles[step] || ""}>
            <p>
              {step === 1 &&
                "We will send you a one-time password to your Email-ID"}
              {step === 2 && (
                <>
                  Enter the OTP sent to{" "}
                  <span style={{ color: "black" }}>
                    {email || "your email"}
                  </span>
                </>
              )}
              {step === 3 && "Welcome to Smart Collect!"}
            </p>
            {step === 1 && (
              <UserDetails
                setStep={setStep}
                setEmail={setEmail}
                setOtp={setOtp}
                setUserID={setUserID}
              />
            )}
            {step === 2 && (
              <OtpPage
                setStep={setStep}
                otp={otp}
                setOtp={setOtp}
                UserID={UserID}
              />
            )}
            {step === 3 && <UpdatePassword UserID={UserID} />}
          </Card>
        </div>
      </Flex>
      {/* right Side */}
      <div className={Style.backgroundCircle}></div>
      <Flex className={Style.imageContainer}>
        <div className={Style.power_by}>
          <img src={MARKYTICS_LOGO} alt="Markytics Logo" />
        </div>
      </Flex>
      <a
        href="https://www.markytics.ai"
        target="_blank"
        rel="noopener noreferrer"
        className={Style.websiteLink}
      >
        www.markytics.ai
      </a>
    </Flex>
  );
}

import { useEffect, useState } from "react";
import { Col, DatePicker, Flex, message, Row, Typography } from "antd";
import AppHeader from "../../AppHeader";
import Style from "./_style.module.scss";
import { DetailsCard } from "./DetailsCard";
import TOTAL_ENGAGEMENT_IMG from "../../../assets/Images/total-engagement.png";
import UNIQUE_CUSTOMER_IMG from "../../../assets/Images/unique-customer.png";
import TOTAL_PROMISE_IMG from "../../../assets/Images/total-promise.png";
import TOTAL_AMOUNT_IMG from "../../../assets/Images/total-amount.png";
import TOTAL_DENIAL_IMG from "../../../assets/Images/total-denial.png";
import PAID_IMG from "../../../assets/Images/paid.png";
import { ClientDetailsTable } from "./ClientDetailsTable";
import { useParams } from "react-router";
import { AXIOS } from "../../../apis/ho-Instance";
import ApplicationLoader from "../../ApplicationLoader";
import {
  formatAmount,
  handleDateFormatter,
  ALL_CACHE_NAMES,
} from "../../../constant";
import dayjs from "dayjs";
import {
  getCacheKey,
  getFromCache,
  storeToCache,
} from "../../../utils/cacheHelper";

const { Text } = Typography;
const CACHE_NAME = ALL_CACHE_NAMES.ADMIN_CLIENT_TABLE_CACHE;

export default function AdminClientDetails() {
  const params = useParams();
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState({});
  const [filterDate, setFilterDate] = useState({
    fromDate: null,
    toDate: null,
  });

  // set the filter dates
  const onChange = ({ date, dateString, type }) => {
    const updatedFilter = {
      ...filterDate,
      [type]: date,
    };
    setFilterDate(updatedFilter);
    handleFetchBankDetailsBasedOnDate(updatedFilter);
  };

  // Fetch client details
  const handleFetchBankDetails = async () => {
    setIsLoading(true);
    // Build cache key for GET request (by bank name)
    const cacheKey = getCacheKey({
      endpoint: "client_statistics/",
      params: { BankName: params.bankname },
    });
    // Try to get cached data for instant UI update
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setData(cachedData);
      setIsLoading(false);
    }
    try {
      const res = await AXIOS.get("client_statistics/", {
        params: {
          BankName: params.bankname,
        },
      });
      if (res.status === 200) {
        setData(res.data);
        // Store the latest data in cache
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: res.data,
        });
      }
    } catch (error) {
      console.log("Error in client statistics api", error?.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch client details based on date
  const handleFetchBankDetailsBasedOnDate = async (dateRange = filterDate) => {
    const { fromDate, toDate } = dateRange;
    if (!fromDate && !toDate) {
      await handleFetchBankDetails();
      return;
    }
    if (!fromDate || !toDate) {
      message.warning("Please, select from and to date!");
      return;
    }
    setIsLoading(true);
    try {
      const res = await AXIOS.post("client_statistics/", {
        BankName: params.bankname,
        FromDate: dayjs(fromDate).format("YYYY-MM-DD"),
        ToDate: dayjs(toDate).format("YYYY-MM-DD"),
      });
      if (res.status === 200) {
        setData(res.data);
        message.success("Client details fetched successfully!");
      }
    } catch (error) {
      console.log("Error in client statistics post request", error?.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Invoking the get method
  useEffect(() => {
    handleFetchBankDetails();
  }, [params]);
  return (
    <Flex vertical gap={20}>
      <AppHeader title={"Client Details"} />

      <Flex className={Style.client_details} vertical gap={20}>
        {/* Date */}
        <Flex justify="end" gap={20} className={Style.date}>
          <DatePicker
            format="DD-MM-YYYY"
            value={filterDate.fromDate}
            disabledDate={(current) => {
              return current?.isAfter(data?.product_install_date, "day");
            }}
            onChange={(date) => onChange({ date, type: "fromDate" })}
            placeholder="From Date"
            className={Style.date_picker}
          />

          <DatePicker
            format="DD-MM-YYYY"
            value={filterDate.toDate}
            placeholder="To Date"
            onChange={(date) => onChange({ date, type: "toDate" })}
            className={Style.date_picker}
          />
        </Flex>
        {isLoading ? (
          <ApplicationLoader />
        ) : (
          <>
            {/* Date details */}
            <Row align="center" className={Style.date_details}>
              <Col md={6} xs={24} className={Style.card}>
                <Flex gap={10} vertical align="center">
                  <Text className={Style.amount}>
                    {data.product_install_date
                      ? handleDateFormatter(data?.product_install_date)
                      : "--"}
                  </Text>
                  <Text className={Style.title}>Product Installation Date</Text>
                </Flex>
              </Col>
              <Col md={6} xs={24} className={Style.card}>
                <Flex gap={10} vertical align="center">
                  <Text className={Style.amount}>{data?.No_of_Licenses}</Text>
                  <Text className={Style.title}>Number of Licenses</Text>
                </Flex>
              </Col>
              <Col md={6} xs={24} className={Style.card}>
                <Flex gap={10} vertical align="center">
                  <Text className={Style.amount}>{data?.Unique_login}</Text>
                  <Text className={Style.title}>Unique Login to date</Text>
                </Flex>
              </Col>
              <Col md={6} xs={24} className={Style.card}>
                <Flex gap={10} vertical align="center">
                  <Text className={Style.amount}>{data?.Total_Login}</Text>
                  <Text className={Style.title}>Total Login to date</Text>
                </Flex>
              </Col>
            </Row>

            {/* details Cards */}
            <Row gutter={[50, 20]}>
              <Col md={8} xs={24}>
                <DetailsCard
                  title={"Total Engagement"}
                  amount={data?.Total_Engagement}
                  image={TOTAL_ENGAGEMENT_IMG}
                />
              </Col>
              <Col md={8} xs={24}>
                <DetailsCard
                  title={"Unique Customers Connected"}
                  amount={data?.Unique_cust_connected}
                  image={UNIQUE_CUSTOMER_IMG}
                />
              </Col>
              <Col md={8} xs={24}>
                <DetailsCard
                  title={"Total Promise Made"}
                  amount={data?.Total_Promise_Made}
                  image={TOTAL_PROMISE_IMG}
                />
              </Col>
              <Col md={8} xs={24}>
                <DetailsCard
                  title={"Total Amount"}
                  amount={`Rs.${formatAmount(data?.Total_Amount || 0)}`}
                  image={TOTAL_AMOUNT_IMG}
                />
              </Col>
              <Col md={8} xs={24}>
                <DetailsCard
                  title={"Total Denial"}
                  amount={data?.Total_Denial}
                  image={TOTAL_DENIAL_IMG}
                />
              </Col>
              <Col md={8} xs={24}>
                <DetailsCard
                  title={"Already Paid"}
                  amount={data?.Already_Paid}
                  image={PAID_IMG}
                />
              </Col>
            </Row>

            <ClientDetailsTable data={data?.client_statistics} />
          </>
        )}
      </Flex>
    </Flex>
  );
}

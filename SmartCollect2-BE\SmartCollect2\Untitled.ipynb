{"cells": [{"cell_type": "code", "execution_count": 1, "id": "cc43de7a-e2dd-4565-8783-59f21cf93b54", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:DB.db_manager:DatabaseManager instance created in process 780291\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Inside RegisterCallingTemplate\n"]}], "source": ["from SmartCollect2 import asgi"]}, {"cell_type": "code", "execution_count": 2, "id": "d5553bd5-40ec-4704-812a-26d60914c014", "metadata": {}, "outputs": [], "source": ["from WebApp.models import LoanMst, CustomerMst\n", "from django.db import close_old_connections\n", "from concurrent.futures import ThreadPoolExecutor, as_completed"]}, {"cell_type": "code", "execution_count": 3, "id": "514a986b-1ffc-46d8-a813-64d1dc634c28", "metadata": {}, "outputs": [], "source": ["import threading"]}, {"cell_type": "code", "execution_count": 4, "id": "9071b389-967e-408c-b554-52cb9680c320", "metadata": {}, "outputs": [], "source": ["def delete_loan(loan_id):\n", "    try:\n", "        close_old_connections()  # Ensure thread-safe DB access\n", "        CustomerMst.objects.filter(CustomerMstID=loan_id).delete()\n", "        return loan_id\n", "    except Exception as e:\n", "        print(f\"❌ Failed to delete LoanMstID={loan_id}: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 5, "id": "ddfd829e-bd56-4559-92bc-1428b4cde7ed", "metadata": {}, "outputs": [], "source": ["def run_parallel_deletes():\n", "    loan_ids = list(CustomerMst.objects.filter(BankMstID=369).values_list(\"CustomerMstID\", flat=True))\n", "    print(f\"🔍 Found {len(loan_ids)} loans to delete.\")\n", "\n", "    # Limit to 10 parallel threads\n", "    with ThreadPoolExecutor(max_workers=50) as executor:\n", "        futures = {executor.submit(delete_loan, loan_id): loan_id for loan_id in loan_ids}\n", "\n", "        deleted = 0\n", "        for future in as_completed(futures):\n", "            result = future.result()\n", "            if result is not None:\n", "                deleted += 1\n", "                print(f\"✅ Deleted LoanMstID={result} ({deleted}/{len(futures)})\")\n", "\n", "    print(f\"✅ All done. {deleted} out of {len(loan_ids)} deleted successfully.\")\n"]}, {"cell_type": "code", "execution_count": 6, "id": "aaf04062-9534-4364-a5bf-b01aad4365ec", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Found 505 loans to delete.\n", "✅ Deleted LoanMstID=716416 (1/505)\n", "✅ Deleted LoanMstID=716432 (2/505)\n", "✅ Deleted LoanMstID=716421 (3/505)\n", "✅ Deleted LoanMstID=716422 (4/505)\n", "✅ Deleted LoanMstID=716381 (5/505)\n", "✅ Deleted LoanMstID=716395 (6/505)\n", "✅ Deleted LoanMstID=716431 (7/505)\n", "✅ Deleted LoanMstID=716404 (8/505)\n", "✅ Deleted LoanMstID=716383 (9/505)\n", "✅ Deleted LoanMstID=716388 (10/505)\n", "✅ Deleted LoanMstID=716403 (11/505)\n", "✅ Deleted LoanMstID=716412 (12/505)\n", "✅ Deleted LoanMstID=716426 (13/505)\n", "✅ Deleted LoanMstID=716413 (14/505)\n", "✅ Deleted LoanMstID=716418 (15/505)\n", "✅ Deleted LoanMstID=716423 (16/505)\n", "✅ Deleted LoanMstID=716436 (17/505)\n", "✅ Deleted LoanMstID=716411 (18/505)\n", "✅ Deleted LoanMstID=716430 (19/505)\n", "✅ Deleted LoanMstID=716425 (20/505)\n", "✅ Deleted LoanMstID=716405 (21/505)\n", "✅ Deleted LoanMstID=716389 (22/505)\n", "✅ Deleted LoanMstID=716408 (23/505)\n", "✅ Deleted LoanMstID=716428 (24/505)\n", "✅ Deleted LoanMstID=716394 (25/505)\n", "✅ Deleted LoanMstID=716410 (26/505)\n", "✅ Deleted LoanMstID=716385 (27/505)\n", "✅ Deleted LoanMstID=716696 (28/505)\n", "✅ Deleted LoanMstID=716419 (29/505)\n", "✅ Deleted LoanMstID=716392 (30/505)\n", "✅ Deleted LoanMstID=716396 (31/505)\n", "✅ Deleted LoanMstID=716429 (32/505)\n", "✅ Deleted LoanMstID=716420 (33/505)\n", "✅ Deleted LoanMstID=716397 (34/505)\n", "✅ Deleted LoanMstID=716386 (35/505)\n", "✅ Deleted LoanMstID=716400 (36/505)\n", "✅ Deleted LoanMstID=716387 (37/505)\n", "✅ Deleted LoanMstID=716435 (38/505)\n", "✅ Deleted LoanMstID=716391 (39/505)\n", "✅ Deleted LoanMstID=716406 (40/505)\n", "✅ Deleted LoanMstID=716537 (41/505)\n", "✅ Deleted LoanMstID=716409 (42/505)\n", "✅ Deleted LoanMstID=716384 (43/505)\n", "✅ Deleted LoanMstID=716415 (44/505)\n", "✅ Deleted LoanMstID=716433 (45/505)\n", "✅ Deleted LoanMstID=716402 (46/505)\n", "✅ Deleted LoanMstID=716407 (47/505)\n", "✅ Deleted LoanMstID=716390 (48/505)\n", "✅ Deleted LoanMstID=716382 (49/505)\n", "✅ Deleted LoanMstID=716427 (50/505)\n", "✅ Deleted LoanMstID=716442 (51/505)\n", "✅ Deleted LoanMstID=716623 (52/505)\n", "✅ Deleted LoanMstID=716450 (53/505)\n", "✅ Deleted LoanMstID=716438 (54/505)\n", "✅ Deleted LoanMstID=716439 (55/505)\n", "✅ Deleted LoanMstID=716447 (56/505)\n", "✅ Deleted LoanMstID=716452 (57/505)\n", "✅ Deleted LoanMstID=716627 (58/505)\n", "✅ Deleted LoanMstID=716440 (59/505)\n", "✅ Deleted LoanMstID=716637 (60/505)\n", "✅ Deleted LoanMstID=716626 (61/505)\n", "✅ Deleted LoanMstID=716454 (62/505)\n", "✅ Deleted LoanMstID=716458 (63/505)\n", "✅ Deleted LoanMstID=716617 (64/505)\n", "✅ Deleted LoanMstID=716619 (65/505)\n", "✅ Deleted LoanMstID=716443 (66/505)\n", "✅ Deleted LoanMstID=716448 (67/505)\n", "✅ Deleted LoanMstID=716455 (68/505)\n", "✅ Deleted LoanMstID=716444 (69/505)\n", "✅ Deleted LoanMstID=716621 (70/505)\n", "✅ Deleted LoanMstID=716446 (71/505)\n", "✅ Deleted LoanMstID=716457 (72/505)\n", "✅ Deleted LoanMstID=716445 (73/505)\n", "✅ Deleted LoanMstID=716616 (74/505)\n", "✅ Deleted LoanMstID=716618 (75/505)\n", "✅ Deleted LoanMstID=716633 (76/505)\n", "✅ Deleted LoanMstID=716646 (77/505)\n", "✅ Deleted LoanMstID=716629 (78/505)\n", "✅ Deleted LoanMstID=716628 (79/505)\n", "✅ Deleted LoanMstID=716648 (80/505)\n", "✅ Deleted LoanMstID=716453 (81/505)\n", "✅ Deleted LoanMstID=716643 (82/505)\n", "✅ Deleted LoanMstID=716625 (83/505)\n", "✅ Deleted LoanMstID=716641 (84/505)\n", "✅ Deleted LoanMstID=716632 (85/505)\n", "✅ Deleted LoanMstID=716631 (86/505)\n", "✅ Deleted LoanMstID=716630 (87/505)\n", "✅ Deleted LoanMstID=716642 (88/505)\n", "✅ Deleted LoanMstID=716620 (89/505)\n", "✅ Deleted LoanMstID=716449 (90/505)\n", "✅ Deleted LoanMstID=716636 (91/505)\n", "✅ Deleted LoanMstID=716622 (92/505)\n", "✅ Deleted LoanMstID=716638 (93/505)\n", "✅ Deleted LoanMstID=716639 (94/505)\n", "✅ Deleted LoanMstID=716624 (95/505)\n", "✅ Deleted LoanMstID=716644 (96/505)\n", "✅ Deleted LoanMstID=716635 (97/505)\n", "✅ Deleted LoanMstID=716634 (98/505)\n", "✅ Deleted LoanMstID=716645 (99/505)\n", "✅ Deleted LoanMstID=716647 (100/505)\n", "✅ Deleted LoanMstID=716651 (101/505)\n", "✅ Deleted LoanMstID=716649 (102/505)\n", "✅ Deleted LoanMstID=716658 (103/505)\n", "✅ Deleted LoanMstID=716661 (104/505)\n", "✅ Deleted LoanMstID=716650 (105/505)\n", "✅ Deleted LoanMstID=716680 (106/505)\n", "✅ Deleted LoanMstID=716669 (107/505)\n", "✅ Deleted LoanMstID=716659 (108/505)\n", "✅ Deleted LoanMstID=716668 (109/505)\n", "✅ Deleted LoanMstID=716666 (110/505)\n", "✅ Deleted LoanMstID=716684 (111/505)\n", "✅ Deleted LoanMstID=716662 (112/505)\n", "✅ Deleted LoanMstID=716693 (113/505)\n", "✅ Deleted LoanMstID=716676 (114/505)\n", "✅ Deleted LoanMstID=716654 (115/505)\n", "✅ Deleted LoanMstID=716672 (116/505)\n", "✅ Deleted LoanMstID=716653 (117/505)\n", "✅ Deleted LoanMstID=716663 (118/505)\n", "✅ Deleted LoanMstID=716459 (119/505)\n", "✅ Deleted LoanMstID=716681 (120/505)\n", "✅ Deleted LoanMstID=716687 (121/505)\n", "✅ Deleted LoanMstID=716656 (122/505)\n", "✅ Deleted LoanMstID=716682 (123/505)\n", "✅ Deleted LoanMstID=716660 (124/505)\n", "✅ Deleted LoanMstID=716664 (125/505)\n", "✅ Deleted LoanMstID=716665 (126/505)\n", "✅ Deleted LoanMstID=716686 (127/505)\n", "✅ Deleted LoanMstID=716858 (128/505)\n", "✅ Deleted LoanMstID=716667 (129/505)\n", "✅ Deleted LoanMstID=716461 (130/505)\n", "✅ Deleted LoanMstID=716678 (131/505)\n", "✅ Deleted LoanMstID=716677 (132/505)\n", "✅ Deleted LoanMstID=716671 (133/505)\n", "✅ Deleted LoanMstID=716460 (134/505)\n", "✅ Deleted LoanMstID=716670 (135/505)\n", "✅ Deleted LoanMstID=716859 (136/505)\n", "✅ Deleted LoanMstID=716855 (137/505)\n", "✅ Deleted LoanMstID=716685 (138/505)\n", "✅ Deleted LoanMstID=716673 (139/505)\n", "✅ Deleted LoanMstID=716692 (140/505)\n", "✅ Deleted LoanMstID=716694 (141/505)\n", "✅ Deleted LoanMstID=716683 (142/505)\n", "✅ Deleted LoanMstID=716675 (143/505)\n", "✅ Deleted LoanMstID=716679 (144/505)\n", "✅ Deleted LoanMstID=716690 (145/505)\n", "✅ Deleted LoanMstID=716688 (146/505)\n", "✅ Deleted LoanMstID=716860 (147/505)\n", "✅ Deleted LoanMstID=716695 (148/505)\n", "✅ Deleted LoanMstID=716462 (149/505)\n", "✅ Deleted LoanMstID=716463 (150/505)\n", "✅ Deleted LoanMstID=716465 (151/505)\n", "✅ Deleted LoanMstID=716466 (152/505)\n", "✅ Deleted LoanMstID=716498 (153/505)\n", "✅ Deleted LoanMstID=716485 (154/505)\n", "✅ Deleted LoanMstID=716481 (155/505)\n", "✅ Deleted LoanMstID=716478 (156/505)\n", "✅ Deleted LoanMstID=716467 (157/505)\n", "✅ Deleted LoanMstID=716470 (158/505)\n", "✅ Deleted LoanMstID=716509 (159/505)\n", "✅ Deleted LoanMstID=716512 (160/505)\n", "✅ Deleted LoanMstID=716477 (161/505)\n", "✅ Deleted LoanMstID=716472 (162/505)\n", "✅ Deleted LoanMstID=716517 (163/505)\n", "✅ Deleted LoanMstID=716511 (164/505)\n", "✅ Deleted LoanMstID=716474 (165/505)\n", "✅ Deleted LoanMstID=716469 (166/505)\n", "✅ Deleted LoanMstID=716471 (167/505)\n", "✅ Deleted LoanMstID=716480 (168/505)\n", "✅ Deleted LoanMstID=716475 (169/505)\n", "✅ Deleted LoanMstID=716487 (170/505)\n", "✅ Deleted LoanMstID=716479 (171/505)\n", "✅ Deleted LoanMstID=716489 (172/505)\n", "✅ Deleted LoanMstID=716482 (173/505)\n", "✅ Deleted LoanMstID=716493 (174/505)\n", "✅ Deleted LoanMstID=716515 (175/505)\n", "✅ Deleted LoanMstID=716484 (176/505)\n", "✅ Deleted LoanMstID=716488 (177/505)\n", "✅ Deleted LoanMstID=716518 (178/505)\n", "✅ Deleted LoanMstID=716502 (179/505)\n", "✅ Deleted LoanMstID=716486 (180/505)\n", "✅ Deleted LoanMstID=716483 (181/505)\n", "✅ Deleted LoanMstID=716514 (182/505)\n", "✅ Deleted LoanMstID=716504 (183/505)\n", "✅ Deleted LoanMstID=716496 (184/505)\n", "✅ Deleted LoanMstID=716513 (185/505)\n", "✅ Deleted LoanMstID=716519 (186/505)\n", "✅ Deleted LoanMstID=716510 (187/505)\n", "✅ Deleted LoanMstID=716516 (188/505)\n", "✅ Deleted LoanMstID=716500 (189/505)\n", "✅ Deleted LoanMstID=716490 (190/505)\n", "✅ Deleted LoanMstID=716501 (191/505)\n", "✅ Deleted LoanMstID=716505 (192/505)\n", "✅ Deleted LoanMstID=716494 (193/505)\n", "✅ Deleted LoanMstID=716491 (194/505)\n", "✅ Deleted LoanMstID=716506 (195/505)\n", "✅ Deleted LoanMstID=716499 (196/505)\n", "✅ Deleted LoanMstID=716503 (197/505)\n", "✅ Deleted LoanMstID=716522 (198/505)\n", "✅ Deleted LoanMstID=716521 (199/505)\n", "✅ Deleted LoanMstID=716520 (200/505)\n", "✅ Deleted LoanMstID=716523 (201/505)\n", "✅ Deleted LoanMstID=716524 (202/505)\n", "✅ Deleted LoanMstID=716525 (203/505)\n", "✅ Deleted LoanMstID=716526 (204/505)\n", "✅ Deleted LoanMstID=716702 (205/505)\n", "✅ Deleted LoanMstID=716533 (206/505)\n", "✅ Deleted LoanMstID=716714 (207/505)\n", "✅ Deleted LoanMstID=716527 (208/505)\n", "✅ Deleted LoanMstID=716529 (209/505)\n", "✅ Deleted LoanMstID=716716 (210/505)\n", "✅ Deleted LoanMstID=716710 (211/505)\n", "✅ Deleted LoanMstID=716701 (212/505)\n", "✅ Deleted LoanMstID=716713 (213/505)\n", "✅ Deleted LoanMstID=716706 (214/505)\n", "✅ Deleted LoanMstID=716727 (215/505)\n", "✅ Deleted LoanMstID=716705 (216/505)\n", "✅ Deleted LoanMstID=716703 (217/505)\n", "✅ Deleted LoanMstID=716530 (218/505)\n", "✅ Deleted LoanMstID=716698 (219/505)\n", "✅ Deleted LoanMstID=716712 (220/505)\n", "✅ Deleted LoanMstID=716729 (221/505)\n", "✅ Deleted LoanMstID=716704 (222/505)\n", "✅ Deleted LoanMstID=716711 (223/505)\n", "✅ Deleted LoanMstID=716531 (224/505)\n", "✅ Deleted LoanMstID=716699 (225/505)\n", "✅ Deleted LoanMstID=716697 (226/505)\n", "✅ Deleted LoanMstID=716707 (227/505)\n", "✅ Deleted LoanMstID=716535 (228/505)\n", "✅ Deleted LoanMstID=716731 (229/505)\n", "✅ Deleted LoanMstID=716709 (230/505)\n", "✅ Deleted LoanMstID=716736 (231/505)\n", "✅ Deleted LoanMstID=716719 (232/505)\n", "✅ Deleted LoanMstID=716738 (233/505)\n", "✅ Deleted LoanMstID=716723 (234/505)\n", "✅ Deleted LoanMstID=716708 (235/505)\n", "✅ Deleted LoanMstID=716721 (236/505)\n", "✅ Deleted LoanMstID=716726 (237/505)\n", "✅ Deleted LoanMstID=716717 (238/505)\n", "✅ Deleted LoanMstID=716725 (239/505)\n", "✅ Deleted LoanMstID=716733 (240/505)\n", "✅ Deleted LoanMstID=716728 (241/505)\n", "✅ Deleted LoanMstID=716722 (242/505)\n", "✅ Deleted LoanMstID=716720 (243/505)\n", "✅ Deleted LoanMstID=716740 (244/505)\n", "✅ Deleted LoanMstID=716718 (245/505)\n", "✅ Deleted LoanMstID=716741 (246/505)\n", "✅ Deleted LoanMstID=716732 (247/505)\n", "✅ Deleted LoanMstID=716739 (248/505)\n", "✅ Deleted LoanMstID=716743 (249/505)\n", "✅ Deleted LoanMstID=716744 (250/505)\n", "✅ Deleted LoanMstID=716745 (251/505)\n", "✅ Deleted LoanMstID=716746 (252/505)\n", "✅ Deleted LoanMstID=716747 (253/505)\n", "✅ Deleted LoanMstID=716748 (254/505)\n", "✅ Deleted LoanMstID=716769 (255/505)\n", "✅ Deleted LoanMstID=716770 (256/505)\n", "✅ Deleted LoanMstID=716750 (257/505)\n", "✅ Deleted LoanMstID=716751 (258/505)\n", "✅ Deleted LoanMstID=716766 (259/505)\n", "✅ Deleted LoanMstID=716759 (260/505)\n", "✅ Deleted LoanMstID=716856 (261/505)\n", "✅ Deleted LoanMstID=716771 (262/505)\n", "✅ Deleted LoanMstID=716768 (263/505)\n", "✅ Deleted LoanMstID=716281 (264/505)\n", "✅ Deleted LoanMstID=716757 (265/505)\n", "✅ Deleted LoanMstID=716763 (266/505)\n", "✅ Deleted LoanMstID=716764 (267/505)\n", "✅ Deleted LoanMstID=716758 (268/505)\n", "✅ Deleted LoanMstID=716755 (269/505)\n", "✅ Deleted LoanMstID=716752 (270/505)\n", "✅ Deleted LoanMstID=716767 (271/505)\n", "✅ Deleted LoanMstID=716765 (272/505)\n", "✅ Deleted LoanMstID=716774 (273/505)\n", "✅ Deleted LoanMstID=716761 (274/505)\n", "✅ Deleted LoanMstID=716753 (275/505)\n", "✅ Deleted LoanMstID=716285 (276/505)\n", "✅ Deleted LoanMstID=716773 (277/505)\n", "✅ Deleted LoanMstID=716772 (278/505)\n", "✅ Deleted LoanMstID=716754 (279/505)\n", "✅ Deleted LoanMstID=716283 (280/505)\n", "✅ Deleted LoanMstID=716284 (281/505)\n", "✅ Deleted LoanMstID=716762 (282/505)\n", "✅ Deleted LoanMstID=716286 (283/505)\n", "✅ Deleted LoanMstID=716288 (284/505)\n", "✅ Deleted LoanMstID=716287 (285/505)\n", "✅ Deleted LoanMstID=716282 (286/505)\n", "✅ Deleted LoanMstID=716291 (287/505)\n", "✅ Deleted LoanMstID=716289 (288/505)\n", "✅ Deleted LoanMstID=716279 (289/505)\n", "✅ Deleted LoanMstID=716280 (290/505)\n", "✅ Deleted LoanMstID=716301 (291/505)\n", "✅ Deleted LoanMstID=716854 (292/505)\n", "✅ Deleted LoanMstID=716298 (293/505)\n", "✅ Deleted LoanMstID=716294 (294/505)\n", "✅ Deleted LoanMstID=716296 (295/505)\n", "✅ Deleted LoanMstID=716295 (296/505)\n", "✅ Deleted LoanMstID=716292 (297/505)\n", "✅ Deleted LoanMstID=716290 (298/505)\n", "✅ Deleted LoanMstID=716293 (299/505)\n", "✅ Deleted LoanMstID=716300 (300/505)\n", "✅ Deleted LoanMstID=716302 (301/505)\n", "✅ Deleted LoanMstID=716303 (302/505)\n", "✅ Deleted LoanMstID=716310 (303/505)\n", "✅ Deleted LoanMstID=716305 (304/505)\n", "✅ Deleted LoanMstID=716332 (305/505)\n", "✅ Deleted LoanMstID=716311 (306/505)\n", "✅ Deleted LoanMstID=716313 (307/505)\n", "✅ Deleted LoanMstID=716309 (308/505)\n", "✅ Deleted LoanMstID=716331 (309/505)\n", "✅ Deleted LoanMstID=716307 (310/505)\n", "✅ Deleted LoanMstID=716317 (311/505)\n", "✅ Deleted LoanMstID=716308 (312/505)\n", "✅ Deleted LoanMstID=716321 (313/505)\n", "✅ Deleted LoanMstID=716336 (314/505)\n", "✅ Deleted LoanMstID=716314 (315/505)\n", "✅ Deleted LoanMstID=716323 (316/505)\n", "✅ Deleted LoanMstID=716319 (317/505)\n", "✅ Deleted LoanMstID=716327 (318/505)\n", "✅ Deleted LoanMstID=716318 (319/505)\n", "✅ Deleted LoanMstID=716325 (320/505)\n", "✅ Deleted LoanMstID=716326 (321/505)\n", "✅ Deleted LoanMstID=716320 (322/505)\n", "✅ Deleted LoanMstID=716322 (323/505)\n", "✅ Deleted LoanMstID=716328 (324/505)\n", "✅ Deleted LoanMstID=716330 (325/505)\n", "✅ Deleted LoanMstID=716339 (326/505)\n", "✅ Deleted LoanMstID=716324 (327/505)\n", "✅ Deleted LoanMstID=716329 (328/505)\n", "✅ Deleted LoanMstID=716338 (329/505)\n", "✅ Deleted LoanMstID=716341 (330/505)\n", "✅ Deleted LoanMstID=716345 (331/505)\n", "✅ Deleted LoanMstID=716340 (332/505)\n", "✅ Deleted LoanMstID=716335 (333/505)\n", "✅ Deleted LoanMstID=716351 (334/505)\n", "✅ Deleted LoanMstID=716350 (335/505)\n", "✅ Deleted LoanMstID=716337 (336/505)\n", "✅ Deleted LoanMstID=716356 (337/505)\n", "✅ Deleted LoanMstID=716342 (338/505)\n", "✅ Deleted LoanMstID=716346 (339/505)\n", "✅ Deleted LoanMstID=716348 (340/505)\n", "✅ Deleted LoanMstID=716353 (341/505)\n", "✅ Deleted LoanMstID=716349 (342/505)\n", "✅ Deleted LoanMstID=716360 (343/505)\n", "✅ Deleted LoanMstID=716347 (344/505)\n", "✅ Deleted LoanMstID=716357 (345/505)\n", "✅ Deleted LoanMstID=716358 (346/505)\n", "✅ Deleted LoanMstID=716354 (347/505)\n", "✅ Deleted LoanMstID=716359 (348/505)\n", "✅ Deleted LoanMstID=716362 (349/505)\n", "✅ Deleted LoanMstID=716363 (350/505)\n", "✅ Deleted LoanMstID=716364 (351/505)\n", "✅ Deleted LoanMstID=716367 (352/505)\n", "✅ Deleted LoanMstID=716365 (353/505)\n", "✅ Deleted LoanMstID=716368 (354/505)\n", "✅ Deleted LoanMstID=716366 (355/505)\n", "✅ Deleted LoanMstID=716378 (356/505)\n", "✅ Deleted LoanMstID=716538 (357/505)\n", "✅ Deleted LoanMstID=716370 (358/505)\n", "✅ Deleted LoanMstID=716544 (359/505)\n", "✅ Deleted LoanMstID=716373 (360/505)\n", "✅ Deleted LoanMstID=716375 (361/505)\n", "✅ Deleted LoanMstID=716380 (362/505)\n", "✅ Deleted LoanMstID=716551 (363/505)\n", "✅ Deleted LoanMstID=716377 (364/505)\n", "✅ Deleted LoanMstID=716371 (365/505)\n", "✅ Deleted LoanMstID=716374 (366/505)\n", "✅ Deleted LoanMstID=716372 (367/505)\n", "✅ Deleted LoanMstID=716541 (368/505)\n", "✅ Deleted LoanMstID=716379 (369/505)\n", "✅ Deleted LoanMstID=716563 (370/505)\n", "✅ Deleted LoanMstID=716548 (371/505)\n", "✅ Deleted LoanMstID=716555 (372/505)\n", "✅ Deleted LoanMstID=716554 (373/505)\n", "✅ Deleted LoanMstID=716559 (374/505)\n", "✅ Deleted LoanMstID=716546 (375/505)\n", "✅ Deleted LoanMstID=716557 (376/505)\n", "✅ Deleted LoanMstID=716540 (377/505)\n", "✅ Deleted LoanMstID=716566 (378/505)\n", "✅ Deleted LoanMstID=716547 (379/505)\n", "✅ Deleted LoanMstID=716556 (380/505)\n", "✅ Deleted LoanMstID=716552 (381/505)\n", "✅ Deleted LoanMstID=716549 (382/505)\n", "✅ Deleted LoanMstID=716567 (383/505)\n", "✅ Deleted LoanMstID=716562 (384/505)\n", "✅ Deleted LoanMstID=716570 (385/505)\n", "✅ Deleted LoanMstID=716571 (386/505)\n", "✅ Deleted LoanMstID=716565 (387/505)\n", "✅ Deleted LoanMstID=716561 (388/505)\n", "✅ Deleted LoanMstID=716558 (389/505)\n", "✅ Deleted LoanMstID=716569 (390/505)\n", "✅ Deleted LoanMstID=716568 (391/505)\n", "✅ Deleted LoanMstID=716564 (392/505)\n", "✅ Deleted LoanMstID=716573 (393/505)\n", "✅ Deleted LoanMstID=716577 (394/505)\n", "✅ Deleted LoanMstID=716575 (395/505)\n", "✅ Deleted LoanMstID=716576 (396/505)\n", "✅ Deleted LoanMstID=716578 (397/505)\n", "✅ Deleted LoanMstID=716582 (398/505)\n", "✅ Deleted LoanMstID=716579 (399/505)\n", "✅ Deleted LoanMstID=716580 (400/505)\n", "✅ Deleted LoanMstID=716586 (401/505)\n", "✅ Deleted LoanMstID=716588 (402/505)\n", "✅ Deleted LoanMstID=716584 (403/505)\n", "✅ Deleted LoanMstID=716587 (404/505)\n", "✅ Deleted LoanMstID=716585 (405/505)\n", "✅ Deleted LoanMstID=716612 (406/505)\n", "✅ Deleted LoanMstID=716590 (407/505)\n", "✅ Deleted LoanMstID=716593 (408/505)\n", "✅ Deleted LoanMstID=716589 (409/505)\n", "✅ Deleted LoanMstID=716595 (410/505)\n", "✅ Deleted LoanMstID=716592 (411/505)\n", "✅ Deleted LoanMstID=716602 (412/505)\n", "✅ Deleted LoanMstID=716601 (413/505)\n", "✅ Deleted LoanMstID=716594 (414/505)\n", "✅ Deleted LoanMstID=716591 (415/505)\n", "✅ Deleted LoanMstID=716598 (416/505)\n", "✅ Deleted LoanMstID=716604 (417/505)\n", "✅ Deleted LoanMstID=716597 (418/505)\n", "✅ Deleted LoanMstID=716606 (419/505)\n", "✅ Deleted LoanMstID=716599 (420/505)\n", "✅ Deleted LoanMstID=716605 (421/505)\n", "✅ Deleted LoanMstID=716608 (422/505)\n", "✅ Deleted LoanMstID=716611 (423/505)\n", "✅ Deleted LoanMstID=716786 (424/505)\n", "✅ Deleted LoanMstID=716603 (425/505)\n", "✅ Deleted LoanMstID=716615 (426/505)\n", "✅ Deleted LoanMstID=716610 (427/505)\n", "✅ Deleted LoanMstID=716777 (428/505)\n", "✅ Deleted LoanMstID=716614 (429/505)\n", "✅ Deleted LoanMstID=716609 (430/505)\n", "✅ Deleted LoanMstID=716776 (431/505)\n", "✅ Deleted LoanMstID=716613 (432/505)\n", "✅ Deleted LoanMstID=716778 (433/505)\n", "✅ Deleted LoanMstID=716775 (434/505)\n", "✅ Deleted LoanMstID=716783 (435/505)\n", "✅ Deleted LoanMstID=716787 (436/505)\n", "✅ Deleted LoanMstID=716785 (437/505)\n", "✅ Deleted LoanMstID=716784 (438/505)\n", "✅ Deleted LoanMstID=716782 (439/505)\n", "✅ Deleted LoanMstID=716779 (440/505)\n", "✅ Deleted LoanMstID=716791 (441/505)\n", "✅ Deleted LoanMstID=716780 (442/505)\n", "✅ Deleted LoanMstID=716789 (443/505)\n", "✅ Deleted LoanMstID=716781 (444/505)\n", "✅ Deleted LoanMstID=716788 (445/505)\n", "✅ Deleted LoanMstID=716792 (446/505)\n", "✅ Deleted LoanMstID=716793 (447/505)\n", "✅ Deleted LoanMstID=716790 (448/505)\n", "✅ Deleted LoanMstID=716794 (449/505)\n", "✅ Deleted LoanMstID=716795 (450/505)\n", "✅ Deleted LoanMstID=716796 (451/505)\n", "✅ Deleted LoanMstID=716800 (452/505)\n", "✅ Deleted LoanMstID=716798 (453/505)\n", "✅ Deleted LoanMstID=716801 (454/505)\n", "✅ Deleted LoanMstID=716799 (455/505)\n", "✅ Deleted LoanMstID=716803 (456/505)\n", "✅ Deleted LoanMstID=716802 (457/505)\n", "✅ Deleted LoanMstID=716813 (458/505)\n", "✅ Deleted LoanMstID=716805 (459/505)\n", "✅ Deleted LoanMstID=716808 (460/505)\n", "✅ Deleted LoanMstID=716810 (461/505)\n", "✅ Deleted LoanMstID=716821 (462/505)\n", "✅ Deleted LoanMstID=716809 (463/505)\n", "✅ Deleted LoanMstID=716815 (464/505)\n", "✅ Deleted LoanMstID=716804 (465/505)\n", "✅ Deleted LoanMstID=716811 (466/505)\n", "✅ Deleted LoanMstID=716812 (467/505)\n", "✅ Deleted LoanMstID=716814 (468/505)\n", "✅ Deleted LoanMstID=716807 (469/505)\n", "✅ Deleted LoanMstID=716817 (470/505)\n", "✅ Deleted LoanMstID=716820 (471/505)\n", "✅ Deleted LoanMstID=716824 (472/505)\n", "✅ Deleted LoanMstID=716819 (473/505)\n", "✅ Deleted LoanMstID=716823 (474/505)\n", "✅ Deleted LoanMstID=716816 (475/505)\n", "✅ Deleted LoanMstID=716818 (476/505)\n", "✅ Deleted LoanMstID=716833 (477/505)\n", "✅ Deleted LoanMstID=716836 (478/505)\n", "✅ Deleted LoanMstID=716822 (479/505)\n", "✅ Deleted LoanMstID=716832 (480/505)\n", "✅ Deleted LoanMstID=716825 (481/505)\n", "✅ Deleted LoanMstID=716841 (482/505)\n", "✅ Deleted LoanMstID=716837 (483/505)\n", "✅ Deleted LoanMstID=716827 (484/505)\n", "✅ Deleted LoanMstID=716830 (485/505)\n", "✅ Deleted LoanMstID=716826 (486/505)\n", "✅ Deleted LoanMstID=716829 (487/505)\n", "✅ Deleted LoanMstID=716828 (488/505)\n", "✅ Deleted LoanMstID=716831 (489/505)\n", "✅ Deleted LoanMstID=716840 (490/505)\n", "✅ Deleted LoanMstID=716835 (491/505)\n", "✅ Deleted LoanMstID=716838 (492/505)\n", "✅ Deleted LoanMstID=716839 (493/505)\n", "✅ Deleted LoanMstID=716846 (494/505)\n", "✅ Deleted LoanMstID=716842 (495/505)\n", "✅ Deleted LoanMstID=716845 (496/505)\n", "✅ Deleted LoanMstID=716843 (497/505)\n", "✅ Deleted LoanMstID=716844 (498/505)\n", "✅ Deleted LoanMstID=716847 (499/505)\n", "✅ Deleted LoanMstID=716848 (500/505)\n", "✅ Deleted LoanMstID=716852 (501/505)\n", "✅ Deleted LoanMstID=716850 (502/505)\n", "✅ Deleted LoanMstID=716851 (503/505)\n", "✅ Deleted LoanMstID=716849 (504/505)\n", "✅ Deleted LoanMstID=716853 (505/505)\n", "✅ All done. 505 out of 505 deleted successfully.\n"]}], "source": ["thread = threading.Thread(target=run_parallel_deletes)\n", "thread.start()\n", "thread.join()"]}, {"cell_type": "code", "execution_count": null, "id": "7f3b5a1f-c61a-4268-8286-385073728725", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}
import os
import pandas as pd
from sqlalchemy import update,MetaData, Table, text, case
from django.db.models import Q, Max, Count, Avg, Min
from django.db import transaction
from datetime import datetime,timedelta
import logging
from typing import Dict,Any
from DB.db_manager import db_manager
from WebApp.model.raw_file import RawFile
from WebApp.models import BranchMst

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def check_existing_disbursement_ids(rawfile_df, bank_id):
    """Django ORM version using QuerySet to read existing records"""
    
    # Get all disbursement IDs from the dataframe and ensure consistent data type
    all_disbursement_ids = rawfile_df['disbursementid'].astype(str).tolist()
    
    # Fix: Get the first inserted_date value (assuming all records have the same date)
    inserted_date = rawfile_df['inserted_date'].iloc[0]
    
    if not all_disbursement_ids:
        return []
    
    try:
        # Use Django ORM to get existing records
        existing_records = RawFile.objects.filter(
            bankmstid=bank_id,
            inserted_date=inserted_date
        ).values_list('disbursementid', flat=True)
        
        # Convert to list and ensure consistent data type
        existing_disbursement_ids = [str(id) for id in existing_records]
        
        # Debug: Print sample values and their types
        if all_disbursement_ids and existing_disbursement_ids:
            print(f"Sample new ID: '{all_disbursement_ids[0]}' (type: {type(all_disbursement_ids[0])})")
            print(f"Sample existing ID: '{existing_disbursement_ids[0]}' (type: {type(existing_disbursement_ids[0])})")
        
        # Get intersection of existing IDs and new IDs
        matching_ids = list(set(all_disbursement_ids) & set(existing_disbursement_ids))
        
        if matching_ids:
            print(f"Found {len(matching_ids)} matching IDs. Sample: {matching_ids[:5]}")
        else:
            print("No matching IDs found - all records are new")
            
        return matching_ids
        
    except Exception as e:
        print(f"Error checking existing disbursement IDs: {e}")
        return []


def filter_new_records(rawfile_df, existing_disbursement_ids, bank_id):
    """Filter out records with disbursement IDs that already exist"""
    if not existing_disbursement_ids:
        return rawfile_df, 0  # Return 2 values consistently
    
    # CRITICAL FIX: Always convert DataFrame disbursementid to string for consistency
    rawfile_df_copy = rawfile_df.copy()
    rawfile_df_copy['disbursementid'] = rawfile_df_copy['disbursementid'].astype(str)
    
    # Find records that already exist
    duplicate_records = rawfile_df_copy[rawfile_df_copy['disbursementid'].isin(existing_disbursement_ids)]
    print(f"Found {len(duplicate_records)} duplicate records")
    
    # Filter out duplicate records to get only new records
    new_records_df = rawfile_df_copy[~rawfile_df_copy['disbursementid'].isin(existing_disbursement_ids)]
    print(f"Filtered to {len(new_records_df)} new records")
    
    return new_records_df, len(duplicate_records)


def get_branch_codes_from_db(bank_mst_id, branch_names):
    """
    Django ORM version to get branch codes from database based on BankMstID and BranchName
    
    Args:
        bank_mst_id: Bank Master ID (e.g., 13)
        branch_names: List of branch names to lookup
    
    Returns:
        Dictionary mapping branch names to branch codes
    """
    if not branch_names:
        return {}
    
    try:
        # Use Django ORM to query branch data
        branches = BranchMst.objects.filter(
            BankMstID=bank_mst_id,
            BranchName__in=list(branch_names)
        ).values('BranchName', 'BranchCode')
        
        # Create mapping dictionary: branch_name -> branch_code
        branch_mapping = {branch['BranchName']: branch['BranchCode'] for branch in branches}
        print(f"Successfully mapped {len(branch_mapping)} branches from database")
        return branch_mapping
        
    except Exception as e:
        print(f"Error querying branch codes: {e}")
        print(f"Attempted to query {len(branch_names)} branch names: {list(branch_names)[:5]}...")
        return {}


def get_branch_names_from_db(bank_mst_id):
    """
    Django ORM version to get branch names from database based on BankMstID
    
    Args:
        bank_mst_id: Bank Master ID (e.g., 13)
    
    Returns:
        Dictionary mapping branch codes to branch names
    """
    print("bank_mst_id: ", bank_mst_id)
    try:
        # Use Django ORM to query branch data
        branches = BranchMst.objects.filter(
            BankMstID=bank_mst_id
        ).values('BranchName', 'BranchCode')
        
        # Create mapping dictionary: branch_code -> branch_name
        branch_mapping = {branch['BranchCode']: branch['BranchName'] for branch in branches}
        print(f"Successfully mapped {len(branch_mapping)} branches from database")
        return branch_mapping
        
    except Exception as e:
        print(f"Error querying branch codes: {e}")
        return {}


def insert_rawfile_data(df: pd.DataFrame, bank_id: int) -> Dict[str, Any]:
    """
    Django ORM version to insert rawfile data after checking for duplicates
    
    Args:
        df: DataFrame containing records to insert
        bank_id: ID of the bank
    
    Returns:
        Dict containing success status, counts, and message
    """
    
    # Bank ID to name mapping
    BANK_MAPPING = {
        13: "Artha-Siddhi Bank",
        21: "Pavana Bank", 
        23: "Vinaya Finance",
        36: "Warna",
        27: "Ghoti Finance",
        28: "Shakti Finance",
        29: "Rajapur Bank",
        30: "Sindhu Dhurg Bank",
        32: "Bss Bihar Bank",
        34: "Credit Fair",
        35: "Share India Fin Cap PVT LTD",
        37: "Shrimant Malojiraje",
        369: "Swami Samarth Bank"
    }
    
    bank_name = BANK_MAPPING.get(bank_id, f"Unknown Bank (ID: {bank_id})")
    
    try:
        logger.info(f"Processing {len(df)} records for {bank_name}")
        
        # Get existing disbursement IDs from database
        logger.info("Checking existing disbursement IDs...")
        existing_disbursement_ids = check_existing_disbursement_ids(df, bank_id)
        
        # Filter out records that already exist
        logger.info("Filtering new records...")
        new_records_df, duplicate_count = filter_new_records(df, existing_disbursement_ids, bank_id)
        
        # Early return if no new records
        if new_records_df.empty:
            message = f"No new records to insert. All {len(df)} records already exist for {bank_name}"
            logger.info(message)
            return {
                'success': True,
                'inserted_count': 0,
                'duplicate_count': duplicate_count,
                'message': message
            }

        # Insert the new records using Django bulk_create
        try:
            inserted_count = bulk_insert_records(new_records_df)
            
            message = f"Successfully inserted {inserted_count} new records into rawfile for {bank_name}"
            if duplicate_count > 0:
                message += f". {duplicate_count} duplicate records were skipped"
            
            logger.info(message)
            
            return {
                'success': True,
                'inserted_count': inserted_count,
                'duplicate_count': duplicate_count,
                'message': message
            }
            
        except Exception as insert_error:
            error_msg = f"Failed to insert data for {bank_name}: {str(insert_error)}"
            logger.error(error_msg)
            return {
                'success': False,
                'inserted_count': 0,
                'duplicate_count': duplicate_count,
                'message': error_msg
            }
        
    except Exception as e:
        error_msg = f"Error processing data for {bank_name}: {str(e)}"
        logger.error(error_msg)
        return {
            'success': False,
            'inserted_count': 0,
            'duplicate_count': 0,
            'message': error_msg
        }


def bulk_insert_records(df: pd.DataFrame) -> int:
    """
    Django ORM version for bulk insert of records
    
    Args:
        df: DataFrame to insert
    
    Returns:
        Number of records inserted
    """
    dataset_size = len(df)
    
    if dataset_size < 2000:
        # For small datasets: Use Django bulk_create
        logger.info(f"Using Django bulk_create for {dataset_size} records")
        return bulk_insert_django_bulk_create(df)
    
    elif dataset_size < 10000:
        # For medium datasets: Use bulk_create with batch_size
        logger.info(f"Using Django bulk_create with batching for {dataset_size} records")
        return bulk_insert_django_batched(df)
    
    else:
        # For large datasets: Use bulk_create with smaller batches
        logger.info(f"Using Django bulk_create with small batches for {dataset_size} records")
        return bulk_insert_django_large_batched(df)


def bulk_insert_django_bulk_create(df: pd.DataFrame) -> int:
    """
    Use Django bulk_create for small datasets - modified to work without primary key
    """
    try:
        # Convert DataFrame to RawFile objects
        rawfile_objects = []
        for _, row in df.iterrows():
            # Convert row to dict and handle NaN values
            row_dict = row.to_dict()
            # Replace NaN with None for Django
            for key, value in row_dict.items():
                if pd.isna(value):
                    row_dict[key] = None
            
            rawfile_objects.append(RawFile(**row_dict))
        
        # Bulk create with transaction - ignore_conflicts to avoid primary key issues
        with transaction.atomic():
            RawFile.objects.bulk_create(
                rawfile_objects, 
                ignore_conflicts=True,  # This prevents primary key conflicts
                batch_size=1000
            )
            
        logger.info(f"Successfully inserted records using Django bulk_create")
        return len(rawfile_objects)  # Return the count we attempted to insert
        
    except Exception as e:
        logger.error(f"Django bulk_create failed: {e}")
        raise e


def bulk_insert_django_batched(df: pd.DataFrame) -> int:
    """
    Use Django bulk_create with batching for medium datasets - modified for no primary key
    """
    try:
        batch_size = 1000
        total_inserted = 0
        
        # Process in batches
        for start_idx in range(0, len(df), batch_size):
            end_idx = min(start_idx + batch_size, len(df))
            batch_df = df.iloc[start_idx:end_idx]
            
            # Convert batch to RawFile objects
            rawfile_objects = []
            for _, row in batch_df.iterrows():
                row_dict = row.to_dict()
                # Replace NaN with None for Django
                for key, value in row_dict.items():
                    if pd.isna(value):
                        row_dict[key] = None
                
                rawfile_objects.append(RawFile(**row_dict))
            
            # Bulk create batch with transaction
            with transaction.atomic():
                RawFile.objects.bulk_create(
                    rawfile_objects, 
                    batch_size=batch_size,
                    ignore_conflicts=True
                )
                total_inserted += len(rawfile_objects)
        
        logger.info(f"Successfully inserted {total_inserted} records using Django batched bulk_create")
        return total_inserted
        
    except Exception as e:
        logger.error(f"Django batched bulk_create failed: {e}")
        raise e


def bulk_insert_django_large_batched(df: pd.DataFrame) -> int:
    """
    Use Django bulk_create with small batches for large datasets - modified for no primary key
    """
    try:
        batch_size = 500  # Smaller batch size for large datasets
        total_inserted = 0
        
        # Process in smaller batches
        for start_idx in range(0, len(df), batch_size):
            end_idx = min(start_idx + batch_size, len(df))
            batch_df = df.iloc[start_idx:end_idx]
            
            # Convert batch to RawFile objects
            rawfile_objects = []
            for _, row in batch_df.iterrows():
                row_dict = row.to_dict()
                # Replace NaN with None for Django
                for key, value in row_dict.items():
                    if pd.isna(value):
                        row_dict[key] = None
                
                rawfile_objects.append(RawFile(**row_dict))
            
            # Bulk create batch with transaction
            with transaction.atomic():
                RawFile.objects.bulk_create(
                    rawfile_objects, 
                    batch_size=batch_size,
                    ignore_conflicts=True
                )
                total_inserted += len(rawfile_objects)
                
            # Optional: Add progress logging for large datasets
            if start_idx % (batch_size * 10) == 0:
                logger.info(f"Processed {end_idx}/{len(df)} records...")
        
        logger.info(f"Successfully inserted {total_inserted} records using Django large batched bulk_create")
        return total_inserted
        
    except Exception as e:
        logger.error(f"Django large batched bulk_create failed: {e}")
        raise e

def insert_data_in_raw_table(rawfile_df, bank_id):
    """Django ORM version to insert rawfile data"""
    try:
        # Process records and check for duplicates
        result = insert_rawfile_data(rawfile_df, bank_id)
        return result
            
    except Exception as e:
        print(f"Error in insert_data_in_raw_table: {e}")
        return {
            'success': False,
            'inserted_count': 0,
            'duplicate_count': 0,
            'message': str(e)
        }


def get_previous_overdue_amount_from_rawtable(customer_disbursement_pairs, bankmst_id):
    """
    Django ORM version to get all previous overdue amounts for multiple customer-disbursement pairs
    from the latest available date
    """
    try:
        # Get the latest inserted_date for this bank
        latest_date = RawFile.objects.filter(
            bankmstid=bankmst_id
        ).aggregate(max_date=Max('inserted_date'))['max_date']
        
        if not latest_date:
            return {}
        
        # Create Q objects for customer-disbursement pairs
        pair_conditions = Q()
        for customer_id, disbursement_id in customer_disbursement_pairs:
            pair_conditions |= Q(customerid=customer_id, disbursementid=disbursement_id)
        
        # Query for overdue amounts
        records = RawFile.objects.filter(
            pair_conditions,
            bankmstid=bankmst_id,
            inserted_date=latest_date
        ).values('customerid', 'disbursementid', 'overdueamount')
        
        # Create dictionary for quick lookup
        overdue_dict = {}
        for record in records:
            key = (record['customerid'], record['disbursementid'])
            overdue_dict[key] = float(record['overdueamount']) if record['overdueamount'] is not None else 0.0
        
        return overdue_dict
                
    except Exception as e:
        raise ValueError(f"Error in get_previous_overdue_amount_from_rawtable: {e}")


def get_all_previous_recovery_data_from_rawtable(bankmst_id):
    """
    Django ORM version to get all previous day's recovery amounts for all customers using the latest available date
    Enhanced with comprehensive debugging and error handling
    """
    try:
        print(f"DEBUG: Checking data for bank_id: {bankmst_id}")
        
        # Step 1: Basic data check
        basic_stats = RawFile.objects.filter(bankmstid=bankmst_id).aggregate(
            total_records=Count('id'),
            unique_dates=Count('inserted_date', distinct=True),
            earliest_date=Min('inserted_date'),
            latest_date=Max('inserted_date')
        )
        
        if basic_stats['total_records']:
            print(f"DEBUG: Total records for bank_id {bankmst_id}: {basic_stats['total_records']}")
            print(f"DEBUG: Unique dates: {basic_stats['unique_dates']}")
            print(f"DEBUG: Date range: {basic_stats['earliest_date']} to {basic_stats['latest_date']}")
        else:
            print(f"DEBUG: No basic data found for bank_id: {bankmst_id}")
            return {}
        
        if basic_stats['total_records'] == 0:
            print(f"ERROR: No records found for bank_id: {bankmst_id}")
            return {}
        
        # Step 2: Get the latest date
        latest_date = basic_stats['latest_date']
        if not latest_date:
            print(f"ERROR: Could not determine latest date for bank_id: {bankmst_id}")
            return {}
        
        print(f"DEBUG: Latest date found: {latest_date}")
        
        # Step 3: Check records for the latest date
        latest_date_stats = RawFile.objects.filter(
            bankmstid=bankmst_id,
            inserted_date=latest_date
        ).aggregate(
            count_on_latest_date=Count('id'),
            non_null_collected=Count('collectedamount'),
            avg_collected=Avg('collectedamount'),
            min_collected=Min('collectedamount'),
            max_collected=Max('collectedamount')
        )
        
        # Count positive collected amounts
        positive_collected = RawFile.objects.filter(
            bankmstid=bankmst_id,
            inserted_date=latest_date,
            collectedamount__gt=0
        ).count()
        
        print(f"DEBUG: Records on latest date ({latest_date}): {latest_date_stats['count_on_latest_date']}")
        print(f"DEBUG: Non-null collectedamount: {latest_date_stats['non_null_collected']}")
        print(f"DEBUG: Positive collectedamount: {positive_collected}")
        print(f"DEBUG: Average collectedamount: {latest_date_stats['avg_collected']}")
        print(f"DEBUG: Min collectedamount: {latest_date_stats['min_collected']}")
        print(f"DEBUG: Max collectedamount: {latest_date_stats['max_collected']}")
        
        # Step 4: Sample some actual data
        sample_records = RawFile.objects.filter(
            bankmstid=bankmst_id,
            inserted_date=latest_date
        ).values('customerid', 'disbursementid', 'collectedamount', 'inserted_date')[:10]
        
        print(f"DEBUG: Sample data from latest date (first 10 rows):")
        for i, record in enumerate(sample_records):
            print(f"  Row {i+1}: customerid={record['customerid']}, disbursementid={record['disbursementid']}, "
                  f"collectedamount={record['collectedamount']}, date={record['inserted_date']}")
        
        # Step 5: Execute main query
        print(f"DEBUG: Executing main query...")
        
        records = RawFile.objects.filter(
            bankmstid=bankmst_id,
            inserted_date=latest_date
        ).values('customerid', 'disbursementid', 'collectedamount')
        
        print(f"DEBUG: Main query returned {len(records)} rows")
        
        if not records:
            print(f"ERROR: No data found for bank_id: {bankmst_id} on date: {latest_date}")
            return {}
        
        recovery_dict = {}
        zero_count = 0
        positive_count = 0
        null_count = 0
        
        for record in records:
            key = (record['customerid'], record['disbursementid'])
            collected_amount = record['collectedamount']
            
            if collected_amount is None:
                null_count += 1
                recovery_dict[key] = 0.0
            elif collected_amount == 0:
                zero_count += 1
                recovery_dict[key] = 0.0
            else:
                positive_count += 1
                recovery_dict[key] = float(collected_amount)
        
        print(f"DEBUG: Processing results:")
        print(f"  - NULL collected amounts: {null_count}")
        print(f"  - Zero collected amounts: {zero_count}")
        print(f"  - Positive collected amounts: {positive_count}")
        print(f"  - Total records processed: {len(recovery_dict)}")
        
        # Show sample of processed data
        print(f"DEBUG: Sample of processed recovery_dict (first 5 entries):")
        for i, (key, value) in enumerate(list(recovery_dict.items())[:5]):
            print(f"  {key}: {value}")
        
        return recovery_dict

    except Exception as e:
        print(f"ERROR in get_all_previous_recovery_data_from_rawtable: {e}")
        print(f"ERROR: Exception type: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return {}

def update_sindhudhurg_bank_data(today_date):
    """
    Update sindhudhurg bank data with calculated overdue and collected amounts
    
    Args:
        today_date: The current date to use in the update query (format: 'YYYY-MM-DD')
    """
    try:
        # Get engine from the database manager
        engine = db_manager.get_engine()
        
        print(f"DEBUG: Starting update_sindhudhurg_bank_data for date: {today_date}")
        
        with engine.connect() as connection:
            # Start a transaction
            trans = connection.begin()
            
            try:
                # Create temporary table with calculated data
                temp_table_query = f"""
                CREATE TEMP TABLE temp_data AS
                SELECT
                    c.disbursementid,
                    c.customername,
                    c.branchname,
                    c.extracolumn3::numeric as "Original Overdue",
                    GREATEST((c.extracolumn3::numeric - c.extracolumn4::numeric), 0)::numeric as "Calculated Overdue",
                    '2025-07-14' as "Collected Date",
                    GREATEST((c.extracolumn4::numeric - a.c1::numeric), 0)::numeric as "Collected Amount"
                FROM
                (SELECT r.disbursementid, SUM(DISTINCT r.collectedamount) as c1
                 FROM rawfile r 
                 WHERE r.bankmstid = 30 
                 AND r.inserted_date >= '2025-07-11'
                 AND r.inserted_date < '{today_date}'::date
                 GROUP BY 1) a
                JOIN
                (SELECT DISTINCT r.disbursementid, r.customername, r.branchname, 
                        r.extracolumn3::numeric, r.extracolumn4::numeric
                 FROM rawfile r 
                 WHERE r.bankmstid = 30 
                 AND r.inserted_date = '{today_date}'::date) c
                ON a.disbursementid = c.disbursementid
                """
                
                connection.execute(text(temp_table_query))
                print(f"DEBUG: Created temporary table with calculated data")
                
                # Check how many records are in temp table
                count_query = "SELECT COUNT(*) FROM temp_data"
                count_result = connection.execute(text(count_query))
                temp_count = count_result.fetchone()[0]
                print(f"DEBUG: Temporary table contains {temp_count} records")
                
                if temp_count > 0:
                    # Update the rawfile table
                    update_query = f"""
                    UPDATE rawfile r
                    SET overdueamount = td."Calculated Overdue"::numeric,
                        collectiondate = td."Collected Date"::date,
                        collectedamount = td."Collected Amount"::numeric
                    FROM temp_data td
                    WHERE r.bankmstid = 30 
                    AND r.inserted_date = '{today_date}'::date
                    AND r.disbursementid = td.disbursementid
                    """
                    
                    update_result = connection.execute(text(update_query))
                    updated_rows = update_result.rowcount
                    print(f"DEBUG: Updated {updated_rows} rows in rawfile table")
                    
                    # Commit the transaction
                    trans.commit()
                    print(f"SUCCESS: Successfully updated sindhudhurg bank data for {today_date}")
                    return {
                        'success': True,
                        'updated_rows': updated_rows,
                        'message': f'Successfully updated {updated_rows} records for date {today_date}'
                    }
                else:
                    print(f"WARNING: No records found to update for date {today_date}")
                    trans.rollback()
                    return {
                        'success': False,
                        'updated_rows': 0,
                        'message': f'No records found to update for date {today_date}'
                    }
                    
            except Exception as e:
                trans.rollback()
                raise e
                
    except Exception as e:
        print(f"ERROR in update_sindhudhurg_bank_data: {e}")
        print(f"ERROR: Exception type: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'updated_rows': 0,
            'message': f'Error updating data: {str(e)}'
        }

def get_data_from_account_summary(bankmstid):
    try:
        # Get engine from the database manager
        engine = db_manager.get_engine()
        
        with engine.connect() as connection:
            # Start a transaction
            trans = connection.begin()
            query = """
            SELECT "CustomerName", "MobileNumber", "DisbursementID", "EMIAmount", "NextEMIDate"
            FROM public."AccountSummary"
            WHERE "BankMstID" = :bank_id 
            """
            
            # Execute the query - pass parameters correctly
            result = connection.execute(text(query), {"bank_id": bankmstid})
            
            # Fetch all results and convert to dictionary for easy lookup
            account_data = {}
            for row in result:
                account_data[row.DisbursementID] = {
                    'CustomerName': row.CustomerName,
                    'MobileNumber': row.MobileNumber,
                    'EMIAmount': row.EMIAmount,
                    'NextEMIDate': row.NextEMIDate
                }
            
            # Commit the transaction
            trans.commit()
            
            return {
                'success': True,
                'data': account_data,
                'message': f"Successfully retrieved {len(account_data)} account records"
            }
            
    except Exception as e:
        # Rollback in case of error
        if 'trans' in locals():
            trans.rollback()
        return {
            'success': False,
            'data': {},
            'message': f"Error retrieving account summary data: {str(e)}"
        }
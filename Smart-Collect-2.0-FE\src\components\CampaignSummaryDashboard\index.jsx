import { Col, Flex, Row, Tour, Typography, message } from "antd";
import React, { useEffect, useState } from "react";
import AppHeader from "../AppHeader";
import CustomerDetails from "./CustomerDetails";
import { CustomerEngagementChart } from "./CustomerEngagementChart";
import { PayDetails } from "./PayDetails";
import { DateCarousel } from "./DateCarousel";
import ApplicationLoader from "../ApplicationLoader";
import { AXIOS } from "../../apis/ho-Instance";
import dayjs from "dayjs";
import { EngagementDetails } from "./EngagementDetails";
import Style from "./_style.module.scss";
import { useTour } from "../../ContextApis/TourProvider";
import {
  ALL_CACHE_NAMES,
  LOCAL_KEYS,
  SMART_COLLECT_MENU_IDS,
  SUMMARY_FILTERS,
} from "../../constant";
import PropTypes from "prop-types";
import ServerCrash from "../ServerCrash";
import {
  getCache<PERSON>ey,
  getFromCache,
  getPostCacheKey,
  storeToCache,
} from "../../utils/cacheHelper";
import { encryptBody, isSameEncryptedBody } from "../../utils/crypto";
import LimitedTabsComponent from "../CategoriesTabsComponent";

const { Text } = Typography;
const CACHE_NAME = ALL_CACHE_NAMES.CAMPAIGN_SUMMARY_DASHBOARD;

export default function CampaignSummaryDashboard({ title = "Dashboard" }) {
  const [engagementData, setEngagementData] = useState([]);
  const [dashboardData, setDashboardData] = useState({});
  const [nonContactable, setNonContactable] = useState([]);
  const [loading, setLoading] = useState(false);
  const [serverDown, setServerDown] = useState(false);
  const [selectedDate, setSelectedDate] = useState({
    fromDate: null,
    toDate: null,
  });
  const storedFilter = localStorage.getItem(SUMMARY_FILTERS.summaryFilter);
  const storedCampaign = localStorage.getItem(
    SUMMARY_FILTERS.selectedCampaignId
  );
  const { open, setOpen, steps, currentStep, setCurrentStep } = useTour();
  const [campaignTabs, setCampaignTabs] = useState([]);
  const [currentCampaign, setCurrentCampaign] = useState(null);

  // Get branch ids
  const handleFetchCampsAndBranchIds = async () => {
    try {
      const storedCampaigns = localStorage.getItem(
        SUMMARY_FILTERS.summaryCampaignIds
      );
      const storedBranches = localStorage.getItem(
        SUMMARY_FILTERS.summaryBranchIds
      );

      const campaignIds = storedCampaigns ? JSON.parse(storedCampaigns) : [];
      const branchIds = storedBranches ? JSON.parse(storedBranches) : [];

      return { campaignIds, branchIds };
    } catch (error) {
      console.error(
        "Error reading campaign/branch IDs from localStorage",
        error
      );
      return { campaignIds: [], branchIds: [] };
    }
  };

  // Handle dpd category tab
  const handleTabChange = (key) => {
    setCurrentCampaign(key);
    localStorage.setItem(SUMMARY_FILTERS.selectedCampaignId, key);
  };

  // Apply dashboard data
  function applyDashboardData(data) {
    const dashboardData = Array.isArray(data.dashboard_data)
      ? data.dashboard_data[0]
      : data.dashboard_data;

    // Added the campaigns
    if (data.campaign_names) {
      const campaignsArr = Object.entries(data.campaign_names).map(
        ([id, name]) => ({
          id: parseInt(id),
          name,
        })
      );
      setCampaignTabs(campaignsArr);
      const campaignIds = campaignsArr?.map((c) => c?.id);
      // Store only IDs in localStorage
      localStorage.setItem(
        SUMMARY_FILTERS.summaryCampaignIds,
        JSON.stringify(campaignIds)
      );
    }
    setDashboardData(dashboardData);
    setNonContactable(data.NonContactable || []);

    const engagementData = Array.isArray(data?.engagement_data)
      ? data.engagement_data
      : [];

    const filledData = fillMissingDates(engagementData);
    setEngagementData(filledData);

    if (filledData.length) {
      const formattedDate = dayjs(filledData[0].CreatedDate).format(
        "YYYY-MM-DD"
      );
      localStorage.setItem(
        SUMMARY_FILTERS.summaryEngagementStartDate,
        JSON.stringify(formattedDate)
      );
    }
  }

  // Get dashboard data
  const handleGetDashboardData = async () => {
    setLoading(true);
    const cacheKey = getCacheKey({ endpoint: "get_campaign_dashboard/" });
    // Try fetching from cache first
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });

    if (cachedData) {
      applyDashboardData(cachedData);
      setLoading(false);
    }

    const { campaignIds, branchIds } = await handleFetchCampsAndBranchIds();
    try {
      const response = await AXIOS.get("get_campaign_dashboard/", {
        campaign_mstids: campaignIds,
        branch_id: branchIds,
      });
      // Check the response
      if (response.status === 200) {
        // Store fresh data to cache
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
        applyDashboardData(response.data);
        setServerDown(false);
      }
    } catch (error) {
      console.log("Error in get_campaign_dashboard", error);
      setServerDown(true);
    } finally {
      setLoading(false);
    }
  };

  // Post the dashboard data & set
  const handleDashboardFilteredData = async ({
    fromDate,
    toDate,
    BranchName,
    campaignMstIds,
  }) => {
    setLoading(true);
    const { campaignIds, branchIds } = await handleFetchCampsAndBranchIds();

    const body = {
      from_date: fromDate ?? "",
      to_date: toDate ?? "",
      branch_id: BranchName?.length ? BranchName : branchIds,
      dpdfrom: 0,
      dpdto: 10000,
      campaign_mstids: campaignMstIds ? [campaignMstIds] : campaignIds,
    };

    // Encrypt the body for caching
    const cacheKey = getPostCacheKey({
      endpoint: "get_campaign_dashboard/",
      body: body ?? {},
    });
    const encryptedOldBody = localStorage.getItem(LOCAL_KEYS.SUMMARY_FILTER);
    const sameBody =
      encryptedOldBody &&
      isSameEncryptedBody({ newBody: body, encryptedOldBody });

    if (sameBody) {
      const cachedData = await getFromCache({
        cacheName: CACHE_NAME,
        key: cacheKey,
      });
      if (cachedData) {
        applyDashboardData(cachedData);
        setLoading(false);
      }
    }
    try {
      const response = await AXIOS.post("get_campaign_dashboard/", body, {
        validateStatus: () => true,
      });
      // Check the response
      if (response.status === 200) {
        // Apply the dashboard data
        applyDashboardData(response.data);
        setServerDown(false);
        const encrypted = encryptBody(body);
        if (encrypted) {
          localStorage.setItem(LOCAL_KEYS.SUMMARY_FILTER, encrypted);
        }
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
      }
    } catch (error) {
      console.error("Error in Campaign:", error);
      setServerDown(true);
    } finally {
      setLoading(false);
    }
  };

  // Handle filter out the dashboard data based on the selected dates
  const handleDisplaySelectedData = async ({ date, value }) => {
    const formattedDates = {
      fromDate: new Date(date).toISOString(),
      toDate: new Date(date).toISOString(),
      disbursementID: "",
    };
    if (value === 0) {
      message.warning("Filter will not apply if total engaged is 0!");
      handleGetDashboardData();
      setSelectedDate({ fromDate: null, toDate: null });
      return localStorage.removeItem(SUMMARY_FILTERS.summaryFilter);
    } else {
      setSelectedDate({ fromDate: date, toDate: date });
      localStorage.setItem(
        SUMMARY_FILTERS.summaryFilter,
        JSON.stringify(formattedDates)
      );
      await handleDashboardFilteredData({
        fromDate: date,
        toDate: date,
      });
    }
  };

  // Handle missing dates
  function fillMissingDates(engagementData) {
    if (!engagementData.length) return [];

    // Sort data by date
    engagementData.sort((a, b) =>
      dayjs(a.CreatedDate).diff(dayjs(b.CreatedDate))
    );

    // Get the first date and today
    const firstDate = dayjs(engagementData[0].CreatedDate);
    const currentDate = dayjs();

    // Generate all dates in range
    const allDates = [];
    let tempDate = firstDate;

    while (
      tempDate.isBefore(currentDate) ||
      tempDate.isSame(currentDate, "day")
    ) {
      allDates.push(tempDate.format("YYYY-MM-DD"));
      tempDate = tempDate.add(1, "day");
    }

    // Create a lookup for existing data
    const dataMap = new Map(
      engagementData.map((item) => [item.CreatedDate, item.total_engaged])
    );

    // Map over all dates, filling missing ones with 0
    return allDates.map((date) => ({
      CreatedDate: date,
      total_engaged: dataMap.get(date) || 0,
    }));
  }

  // Handle apply filters
  const applyFilters = async ({
    fromDate,
    toDate,
    BranchName,
    campaignMstIds,
  }) => {
    await handleDashboardFilteredData({
      fromDate: fromDate,
      toDate: toDate,
      BranchName,
      campaignMstIds: campaignMstIds,
    });
  };

  // Handle clear the filter
  const ClearFilters = async () => {
    await handleGetDashboardData();
    setSelectedDate({ fromDate: null, toDate: null });
    localStorage.removeItem(SUMMARY_FILTERS.summaryFilter);
    localStorage.removeItem(LOCAL_KEYS.SUMMARY_FILTER);
  };

  // Handle remove dpd category
  const handleRemoveCampCategory = () => {
    setCurrentCampaign(null);
    localStorage.removeItem(SUMMARY_FILTERS.selectedCampaignId);
  };

  // Called the dashboard api as initial
  useEffect(() => {
    if (storedFilter || storedCampaign) {
      const { fromDate, toDate, BranchName } = storedFilter
        ? JSON.parse(storedFilter)
        : {
            fromDate: null,
            toDate: null,
            BranchName: [],
          };
      const parsedCamp = storedCampaign ? parseInt(storedCampaign) : null;
      setSelectedDate({ fromDate: fromDate, toDate: toDate });
      handleDashboardFilteredData({
        fromDate,
        toDate,
        BranchName,
        campaignMstIds: parsedCamp,
      });
    } else {
      setSelectedDate({ fromDate: null, toDate: null });
      handleGetDashboardData();
    }
  }, [storedFilter, storedCampaign]);

  // Set the current dpd tab value
  useEffect(() => {
    if (storedCampaign && campaignTabs?.length) {
      const parsed = parseInt(storedCampaign);
      const matchedTab = campaignTabs.find(
        (cate) => cate.id === parseInt(parsed)
      );
      if (matchedTab) {
        setCurrentCampaign(matchedTab.id);
      }
    }
  }, [campaignTabs]);
  return (
    <Flex gap={30} vertical>
      <AppHeader
        title={title}
        isDashboard={true}
        applyFilters={applyFilters}
        ClearFilters={ClearFilters}
        selectedDate={selectedDate}
        pageId={SMART_COLLECT_MENU_IDS.DASHBOARD}
        isDashboardOtherPages={false}
      />

      <Flex vertical gap={10} style={{ width: "100%", marginBlockEnd: "1rem" }}>
        {serverDown ? (
          <ServerCrash />
        ) : (
          <Row
            gutter={[40, 20]}
            justify={"center"}
            className={Style.dashboard_row}
          >
            {campaignTabs?.length ? (
              <Col
                span={24}
                style={{ padding: "0px" }}
                className={Style.dashboard_tabs}
              >
                <LimitedTabsComponent
                  campaignTabs={campaignTabs}
                  currentCampaign={currentCampaign}
                  handleTabChange={handleTabChange}
                  handleRemoveCampCategory={handleRemoveCampCategory}
                />
              </Col>
            ) : null}

            {engagementData?.length ? (
              <Col span={24} style={{ padding: "0px" }}>
                <DateCarousel
                  engagementData={engagementData}
                  handleDisplaySelectedData={handleDisplaySelectedData}
                  handleGetDashboardData={handleGetDashboardData}
                  selectedDate={selectedDate}
                  setSelectedDate={setSelectedDate}
                  loading={loading}
                />
              </Col>
            ) : null}

            {loading ? (
              <Col span={24}>
                <ApplicationLoader />
              </Col>
            ) : (
              <>
                <Col xl={11} span={24} style={{ paddingLeft: "0px" }}>
                  <Flex
                    vertical
                    gap={20}
                    style={{ height: "100%" }}
                    justify="space-between"
                  >
                    <EngagementDetails dashboardData={dashboardData} />
                    <CustomerDetails dashboardData={dashboardData} />
                  </Flex>
                </Col>

                <Col
                  span={12}
                  className={Style.graph_container}
                  style={{
                    background: "#EBFDFF",
                    boxShadow: "4px 4px 4px 0px #00000040",
                    borderRadius: "8px",
                    padding: "0.5rem 20px 0",
                  }}
                >
                  {engagementData?.length ? (
                    <CustomerEngagementChart engagementData={engagementData} />
                  ) : (
                    <Text
                      style={{
                        textAlign: "center",
                        fontWeight: 500,
                        width: "100%",
                        display: "inline-block",
                      }}
                    >
                      No Data Found!
                    </Text>
                  )}
                </Col>

                <Col span={24} style={{ padding: "0px" }}>
                  <PayDetails
                    dashboardData={dashboardData}
                    nonContactable={nonContactable}
                  />
                </Col>
              </>
            )}
          </Row>
        )}
      </Flex>
      <Tour
        open={open}
        onClose={() => setOpen(false)}
        steps={steps}
        current={currentStep}
        onChange={setCurrentStep}
      />
    </Flex>
  );
}

CampaignSummaryDashboard.propTypes = {
  title: PropTypes.string,
};

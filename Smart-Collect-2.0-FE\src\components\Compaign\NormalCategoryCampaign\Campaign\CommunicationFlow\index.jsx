import { useState } from "react";
import {
  Col,
  DatePicker,
  Flex,
  Input,
  InputNumber,
  message,
  Row,
  Segmented,
  Select,
  Typography,
} from "antd";
import ADD_ICON_IMG from "../../../../../assets/Images/add-icon.png";
import DELETE_IMG from "../../../../../assets/Images/delete.svg";
import ChannelDetails from "./ChannelDetails";
import Style from "./_style.module.scss";
import { useParams } from "react-router";
import { AXIOS } from "../../../../../apis/ho-Instance";
import { CampaignDeleteModal } from "../../../../Modals/CampaignDeleteModal";
import { CAMPAIGN_DATA_PROP_TYPES } from "../../../../../constant";
import PropTypes from "prop-types";
import dayjs from "dayjs";

const { Text } = Typography;

export function CommunicationFlowComponent({ campaignData, setCampaignData }) {
  const params = useParams();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isDeleteLoading, setIsDeleteLoading] = useState(false);
  const [selectedFlow, setSelectedFlow] = useState({
    index: null,
    commFlowID: null,
  });

  const handleApplicantTypeChange = ({ index, value }) => {
    setCampaignData((prevData) => {
      const updateFlow = prevData.communicationFlows.commFlow.map((flow, i) =>
        i === index
          ? {
              ...flow,
              applicantType: value,
              channelName: null,
              basedOn: null,
              isAfter: false,
              dayCount: null,
              daysValues: {},
              flowType: "normal",
              startDate: null,
              interval: 1,
              count: null,
              until: null,
              byWeekday: null,
            }
          : flow
      );
      return {
        ...prevData,
        communicationFlows: {
          ...prevData.communicationFlows,
          commFlow: updateFlow,
        },
      };
    });
  };

  const handleCommunicationChannelChange = ({ index, value }) => {
    setCampaignData((prevData) => {
      const updatedFlows = prevData.communicationFlows.commFlow.map((flow, i) =>
        i === index
          ? {
              ...flow,
              channelName: value,
              basedOn: null,
              isAfter: false,
              dayCount: null,
              daysValues: {},
              flowType: "normal",
              startDate: null,
              interval: 1,
              count: null,
              until: null,
              byWeekday: null,
            }
          : flow
      );
      return {
        ...prevData,
        communicationFlows: {
          ...prevData.communicationFlows,
          commFlow: updatedFlows,
        },
      };
    });
  };

  const handleBasedOnChange = ({ index, value }) => {
    setCampaignData((prevData) => {
      const updateFlow = prevData.communicationFlows.commFlow.map((flow, i) =>
        i === index
          ? {
              ...flow,
              basedOn: value,
              isAfter: false,
              dayCount: null,
              daysValues: {},
              flowType: "normal",
              startDate: null,
              interval: 1,
              count: null,
              until: null,
              byWeekday: null,
            }
          : flow
      );
      return {
        ...prevData,
        communicationFlows: {
          ...prevData.communicationFlows,
          commFlow: updateFlow,
        },
      };
    });
  };

  const handleDaysChange = ({ index, value, frequency }) => {
    setCampaignData((prevData) => {
      const updateFlow = prevData.communicationFlows.commFlow.map((flow, i) =>
        i === index
          ? {
              ...flow,
              dayCount: value,
              daysValues: generateDaysValues({ count: value, frequency }),
            }
          : flow
      );
      return {
        ...prevData,
        communicationFlows: {
          ...prevData.communicationFlows,
          commFlow: updateFlow,
        },
      };
    });
  };

  const handleChange = ({ index, field, value }) => {
    setCampaignData((prevData) => {
      if (field === "name") {
        return {
          ...prevData,
          communicationFlows: { ...prevData.communicationFlows, name: value },
        };
      } else {
        const updatedCommunicationFlows =
          prevData.communicationFlows.commFlow.map((flow, i) =>
            i === index
              ? {
                  ...flow,
                  [field]: value,
                  daysValues: reverseDaysValues({
                    daysValues: flow.daysValues,
                    dayCount: flow.dayCount,
                    isAfter: value,
                  }),
                }
              : flow
          );
        return {
          ...prevData,
          communicationFlows: {
            ...prevData.communicationFlows,
            commFlow: updatedCommunicationFlows,
          },
        };
      }
    });
  };

  const handleAddCommunicationFlows = () => {
    setCampaignData((prevData) => {
      const incompleteFlow = prevData.communicationFlows.commFlow.some(
        (flow) =>
          flow.commFlowID === null &&
          (!flow.channelName || !flow.basedOn || !flow.applicantType)
      );

      const incompletePeriodicFlow = prevData.communicationFlows.commFlow.some(
        (flow) =>
          flow.commFlowID === null &&
          flow.flowType === "periodic" &&
          (!flow.startDate ||
            flow.interval === null ||
            flow.interval === undefined ||
            (flow.periodType === "count" &&
              (flow.count === null || flow.count === undefined)) ||
            (flow.periodType === "date" && !flow.until))
      );

      if (!prevData.communicationFlows.name) {
        message.error("Please provide a name for the communicationFlows.");
        return prevData;
      }

      if (incompleteFlow || incompletePeriodicFlow) {
        message.error("Please fill in all fields before adding a new one.");
        return prevData;
      }

      // Add a new empty communication flow
      return {
        ...prevData,
        communicationFlows: {
          ...prevData.communicationFlows,
          commFlow: [
            ...prevData.communicationFlows.commFlow,
            {
              applicantType: null,
              channelName: null,
              basedOn: null,
              isAfter: true,
              dayCount: null,
              flowType: "normal",
              frequency: "daily",
              startDate: null,
              interval: 1,
              count: null,
              until: null,
              periodType: "count",
              byWeekday: null,
              daysValues: {},
              commFlowID: null, // Include this to mark it as new
            },
          ],
        },
      };
    });
  };

  const handleDeleteExitsFlows = async (id) => {
    try {
      await AXIOS.delete(`v1/getcommflows/${id}/`);
    } catch (error) {
      console.log("Error in Flow deleting,", error?.message);
    }
  };

  const handleDeleteCommFlow = async () => {
    setIsDeleteLoading(true);
    try {
      if (params.status === "saved" || params.status === "Saved") {
        if (
          selectedFlow.commFlowID !== undefined &&
          selectedFlow.commFlowID !== null
        )
          await handleDeleteExitsFlows(selectedFlow.commFlowID);
      }
      setCampaignData((prevData) => ({
        ...prevData,
        communicationFlows: {
          ...prevData.categories,
          commFlow: prevData.communicationFlows.commFlow.filter(
            (_, i) => i !== selectedFlow.index
          ),
        },
      }));
      message.success("Flow deleted successfully!");
    } catch (error) {
      console.log("Error in delete campaign", error);
      message.error("Flow is not deleted, try again");
    } finally {
      setIsDeleteLoading(false);
      setShowDeleteModal(false);
    }
  };

  const handleCheckboxChange = ({ index, day }) => {
    setCampaignData((prevData) => {
      const updatedFlows = prevData.communicationFlows.commFlow.map(
        (flow, i) => {
          if (i === index) {
            // Check if the flow index matches the selected index
            const daysValues = { ...flow.daysValues };
            const isCurrentlySelected = daysValues[day];

            // Count how many days are currently selected
            const selectedDays =
              Object.values(daysValues).filter(Boolean).length;

            // Prevent unchecking if it's the last selected day
            if (selectedDays === 1 && isCurrentlySelected) {
              message.error("Please, select at least one day.");
              return flow; // Return unchanged flow to prevent deselecting the last day
            }

            // Toggle the selected state
            return {
              ...flow,
              daysValues: { ...daysValues, [day]: !isCurrentlySelected },
            };
          }
          return flow;
        }
      );

      return {
        ...prevData,
        communicationFlows: {
          ...prevData.communicationFlows,
          commFlow: updatedFlows,
        },
      };
    });
  };

  const handleSelectAllDays = ({ index, value }) => {
    setCampaignData((prevData) => {
      const updatedFlows = prevData.communicationFlows.commFlow.map(
        (flow, i) => {
          if (i === index) {
            const updatedDays = {};
            for (let d = 1; d <= flow.dayCount; d++) {
              updatedDays[d] = value;
            }
            return {
              ...flow,
              daysValues: updatedDays,
            };
          }
          return flow;
        }
      );

      return {
        ...prevData,
        communicationFlows: {
          ...prevData.communicationFlows,
          commFlow: updatedFlows,
        },
      };
    });
  };

  const generateDaysValues = ({ count, frequency }) => {
    const days = {};

    if (frequency === "weekly") {
      for (let i = 0; i < 7; i++) {
        days[i] = true;
      }
    } else {
      for (let i = 1; i <= count; i++) {
        days[i] = true;
      }
    }

    return days;
  };

  const reverseDaysValues = ({ daysValues, dayCount, isAfter }) => {
    if (!daysValues || typeof daysValues !== "object" || !dayCount) {
      return {};
    }
    const daysArray = Object.entries(daysValues);
    const orderedDays = isAfter ? daysArray : daysArray.toReversed();
    return Object.fromEntries(
      orderedDays.map(([key, val], idx) => [
        isAfter ? idx + 1 : dayCount - idx,
        val,
      ])
    );
  };

  // Update flow type to set isAfter true for periodic
  const handleFlowTypeChange = ({ index, value }) => {
    setCampaignData((prevData) => {
      const updatedFlows = prevData.communicationFlows.commFlow.map(
        (flow, i) => {
          if (i === index) {
            return {
              ...flow,
              flowType: value,
              frequency: "daily",
              isAfter: true,
              dayCount: null,
              daysValues: {},
              startDate: null,
              interval: 1,
              count: null,
              until: null,
              byWeekday: null,
            };
          }
          return flow;
        }
      );
      return {
        ...prevData,
        communicationFlows: {
          ...prevData.communicationFlows,
          commFlow: updatedFlows,
        },
      };
    });
  };

  // Add handler for periodic fields
  const handlePeriodicFieldChange = ({ index, field, value }) => {
    setCampaignData((prevData) => {
      const updatedFlows = prevData.communicationFlows.commFlow.map((flow, i) =>
        i === index ? { ...flow, [field]: value } : flow
      );
      return {
        ...prevData,
        communicationFlows: {
          ...prevData.communicationFlows,
          commFlow: updatedFlows,
        },
      };
    });
  };

  return (
    <Flex vertical gap={20} className={Style.flow_container}>
      <Flex align="center" gap={10} className={Style.filters_container}>
        <Text className={Style.input_text}>Flow Name:</Text>
        <Input
          className={Style.custom_input}
          placeholder="Enter"
          value={campaignData?.communicationFlows.name}
          onChange={(e) => {
            handleChange({
              field: "name",
              value: e.target.value,
            });
          }}
        />
      </Flex>

      {campaignData?.communicationFlows?.commFlow?.map((flow, index) => {
        return (
          <Flex
            vertical
            className={Style.row}
            key={`${index}-${flow.applicantType}`}
          >
            <Row gutter={[20, 20]} justify={"start"}>
              {campaignData?.communicationFlows?.commFlow?.length > 1 && (
                <Col span={24}>
                  <Flex justify="end">
                    <input
                      type="image"
                      src={DELETE_IMG}
                      alt="delete"
                      style={{ cursor: "pointer", width: "9px" }}
                      onClick={() => {
                        setSelectedFlow({ index, commFlowID: flow.commFlowID });
                        setShowDeleteModal(true);
                      }}
                    />
                  </Flex>
                </Col>
              )}

              <Col sm={6} xs={24}>
                <Flex gap={10} align="center">
                  <Text className={Style.text}>Applicant Type:</Text>
                  <Select
                    value={flow?.applicantType}
                    className={Style.custom_select}
                    placeholder={"Select"}
                    onChange={(value) =>
                      handleApplicantTypeChange({ index, value })
                    }
                    options={[
                      { value: "applicant", label: "Applicant" },
                      { value: "co-applicant", label: "Co Applicant" },
                      { value: "guarantor", label: "Guarantor" },
                    ]}
                    allowClear
                    disabled={flow?.commFlowID}
                  />
                </Flex>
              </Col>

              <Col sm={6} xs={24}>
                <Flex gap={10} align="center">
                  <Text className={Style.text}>Communication Channels:</Text>
                  <Select
                    value={flow?.channelName}
                    className={Style.custom_select}
                    placeholder={"Select"}
                    onChange={(value) =>
                      handleCommunicationChannelChange({ index, value })
                    }
                    options={[
                      { value: "whatsapp", label: "Whatsapp" },
                      { value: "blaster", label: "Blaster" },
                      { value: "sms", label: "SMS" },
                      { value: "ai", label: "AI Calls" },
                      { value: "ivr", label: "IVR Calls" },
                      { value: "calling", label: "Calling Team" },
                    ]}
                    allowClear
                    disabled={flow?.commFlowID}
                  />
                </Flex>
              </Col>

              <Col sm={6} xs={24}>
                <Flex align="center" gap={10}>
                  <Text className={Style.text}>Based On:</Text>
                  <Select
                    value={flow?.basedOn}
                    className={Style.custom_select}
                    placeholder={"Select"}
                    onChange={(value) => handleBasedOnChange({ index, value })}
                    optionLabelProp="label"
                    options={[
                      { label: "Promise Date", value: "promiseDate" },
                      { label: "Created Date", value: "CreatedDate" },
                      { label: "Next EMI Date", value: "NextEMIDate" },
                      { label: "Closing Date", value: "ClosingDate" },
                    ]}
                    allowClear
                    disabled={flow?.commFlowID}
                  />
                </Flex>
              </Col>

              <Col sm={6} xs={24}>
                <Flex align="center" gap={10}>
                  <Text className={Style.text}>Flow Type:</Text>
                  <Select
                    value={flow?.flowType}
                    className={Style.custom_select}
                    placeholder={"Select"}
                    onChange={(value) => handleFlowTypeChange({ index, value })}
                    optionLabelProp="label"
                    options={[
                      { label: "Normal", value: "normal" },
                      { label: "Periodic", value: "periodic" },
                    ]}
                    disabled={flow?.commFlowID}
                  />
                </Flex>
              </Col>
            </Row>

            <Row justify={"center"} gutter={[10, 10]}>
              {flow?.flowType !== "periodic" ? (
                <Col sm={4} xs={24}>
                  <Flex align="center" gap={10}>
                    <Text className={Style.text}>Days:</Text>
                    <Select
                      value={flow?.dayCount}
                      className={Style.custom_select}
                      placeholder={"Select"}
                      onChange={(value) =>
                        handleDaysChange({
                          index,
                          value,
                          frequency: flow?.frequency,
                        })
                      }
                      optionLabelProp="label"
                      options={[
                        { label: "7", value: 7 },
                        { label: "15", value: 15 },
                        { label: "30", value: 30 },
                      ]}
                      allowClear
                      disabled={flow?.commFlowID}
                    />
                  </Flex>
                </Col>
              ) : (
                <>
                  <Col sm={4} xs={24}>
                    <Flex align="center" gap={10}>
                      <Text className={Style.text}>Start Date:</Text>
                      <DatePicker
                        className={Style.date_picker}
                        onChange={(date) => {
                          handlePeriodicFieldChange({
                            index,
                            field: "startDate",
                            value: date ? date.format("YYYY-MM-DD") : null,
                          });
                        }}
                        value={
                          flow?.startDate
                            ? dayjs(flow.startDate, "YYYY-MM-DD")
                            : null
                        } // parse string back to dayjs
                        format="DD-MM-YYYY"
                        placeholder="dd-mm-yyyy"
                        disabled={flow?.commFlowID}
                      />
                    </Flex>
                  </Col>

                  <Col sm={4} xs={24}>
                    <Flex align="center" gap={10}>
                      <Text className={Style.text}>Frequency:</Text>
                      <Select
                        value={flow?.frequency}
                        className={Style.custom_select}
                        placeholder={"Select"}
                        onChange={(value) => {
                          handlePeriodicFieldChange({
                            index,
                            field: "frequency",
                            value,
                          });
                          if (["weekly", "monthly"].includes(value)) {
                            let count = value === "monthly" ? 30 : 7;
                            handleDaysChange({
                              index,
                              value: count,
                              frequency: value,
                            });
                          } else {
                            handleDaysChange({
                              index,
                              value: null,
                              frequency: value,
                            });
                          }
                        }}
                        optionLabelProp="label"
                        options={[
                          { label: "Daily", value: "daily" },
                          { label: "Weekly", value: "weekly" },
                          { label: "Monthly", value: "monthly" },
                          { label: "Yearly", value: "yearly" },
                        ]}
                        disabled={flow?.commFlowID}
                      />
                    </Flex>
                  </Col>

                  <Col sm={4} xs={24}>
                    <Flex align="center" gap={10}>
                      <Text className={Style.text}>Interval:</Text>
                      <InputNumber
                        placeholder="interval"
                        className={Style.custom_input_number}
                        value={flow?.interval}
                        onChange={(value) =>
                          handlePeriodicFieldChange({
                            index,
                            field: "interval",
                            value,
                          })
                        }
                        disabled={flow?.commFlowID}
                      />
                    </Flex>
                  </Col>

                  <Col sm={8} xs={24}>
                    <Flex align="center" gap={10}>
                      <Text className={Style.text}>End After:</Text>
                      <Select
                        value={flow?.periodType}
                        className={Style.custom_select}
                        placeholder={"Select"}
                        onChange={(value) =>
                          handlePeriodicFieldChange({
                            index,
                            field: "periodType",
                            value,
                          })
                        }
                        optionLabelProp="label"
                        options={[
                          { label: "count", value: "count" },
                          { label: "Date", value: "date" },
                        ]}
                        disabled={flow?.commFlowID}
                      />
                      {flow?.periodType === "date" && (
                        <DatePicker
                          className={Style.date_picker}
                          onChange={(date) =>
                            handlePeriodicFieldChange({
                              index,
                              field: "until",
                              value: date
                                ? dayjs(date).format("YYYY-MM-DD")
                                : null,
                            })
                          }
                          value={
                            flow?.until ? dayjs(flow.until, "YYYY-MM-DD") : null
                          } // parse string back to dayjs
                          format="DD-MM-YYYY"
                          placeholder="dd-mm-yyyy"
                          disabled={flow?.commFlowID}
                        />
                      )}

                      {flow?.periodType === "count" && (
                        <InputNumber
                          placeholder="count"
                          className={Style.custom_input_number}
                          value={flow?.count}
                          onChange={(value) =>
                            handlePeriodicFieldChange({
                              index,
                              field: "count",
                              value,
                            })
                          }
                          disabled={flow?.commFlowID}
                        />
                      )}
                    </Flex>
                  </Col>
                </>
              )}

              {/* based on */}
              <Col sm={3} xs={24}>
                <Segmented
                  value={flow?.isAfter ? "After" : "Before"}
                  style={{
                    marginBottom: 8,
                  }}
                  onChange={(value) =>
                    handleChange({
                      index,
                      field: "isAfter",
                      value: value === "After",
                    })
                  }
                  options={
                    flow?.flowType === "normal"
                      ? ["After", "Before"]
                      : ["After"]
                  }
                  disabled={flow?.flowType === "periodic" || flow?.commFlowID}
                />
              </Col>

              {((flow?.flowType === "periodic" &&
                ["weekly"].includes(flow.frequency)) ||
                (flow?.channelName && flow?.dayCount > 0 && flow?.basedOn)) && (
                <Col span={24}>
                  <ChannelDetails
                    channelName={flow.channelName}
                    basedOn={flow.basedOn}
                    frequency={flow.frequency}
                    dayCount={flow.dayCount}
                    daysValues={flow.daysValues}
                    isAfter={flow.isAfter}
                    onCheckboxChange={(day) =>
                      handleCheckboxChange({ index, day })
                    }
                    onSelectAllToggle={(value) =>
                      handleSelectAllDays({ index, value })
                    }
                    flowType={flow.flowType}
                  />
                </Col>
              )}
            </Row>
          </Flex>
        );
      })}

      {/* Add button */}
      <Flex justify="end">
        <button
          className={Style.add_button}
          onClick={handleAddCommunicationFlows}
        >
          <div className={Style.icon_img}>
            <img src={ADD_ICON_IMG} alt="add-icon" />
          </div>
        </button>
      </Flex>

      <CampaignDeleteModal
        modalStatus={showDeleteModal}
        setModalStatus={setShowDeleteModal}
        handleDeleteCampaign={handleDeleteCommFlow}
        desc={`Flow`}
        isLoading={isDeleteLoading}
      />
    </Flex>
  );
}

CommunicationFlowComponent.propTypes = {
  campaignData: CAMPAIGN_DATA_PROP_TYPES,
  setCampaignData: PropTypes.func,
};

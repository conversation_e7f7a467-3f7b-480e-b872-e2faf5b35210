@import url('https://fonts.googleapis.com/css2?family=Goldman:wght@400;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Goudy+Bookletter+1911&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Kanit:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

:root {
  --font-size-base: clamp(14px, 2vw, 18px); /* Base font size */
  --spacing-unit: clamp(8px, 2vw, 16px); /* Base spacing unit */
  --max-card-width: 400px; /* Max card width */
  --image-container-width: 50%; /* Width of the image container */
}

.container {
  width: 100%;
  height: 100vh;
  display: flex;
  font-family: 'Kanit','Courier New', Courier, monospace, serif;
  overflow: hidden;
  position: relative;

  .backgroundCircle {
    position: absolute;
    width: clamp(300px, 40vw, 90vh);
    height: 90vh;
    border-radius: 50%;
    background-color: rgba(181, 204, 255, 1);
    top: 50%;
    right: calc(31.5% - 50vh);/* Adjust this to position it slightly behind imageContainer */
    transform: translateY(-50%);
    z-index: -1; /* Ensure it's behind imageContainer */
    
  }
  .imageContainer {
    width: clamp(350px, 60vw, 130vh);
    height:  130vh;
    border-radius: 50%;
    background-color: #0F2050;
    position: absolute;
    top: 50%;
    right: calc(10% - 50vh); /* Pull the circle beyond the right edge */
    transform: translateY(-50%);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    
    .power_by {
      display: flex;
      justify-content: center;
      width: 100%;
      padding-right: 1vw;
      img {
        width: clamp(350px, 25vw, 600px);
        object-fit: contain;
      }
    }
  } 

  .leftSide {
    width: 50%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .header {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 2rem 0;      

      .logo_container {
        display: flex;
        align-items: center;
        gap: 2px;

        .logo_image {
          width: clamp(100px, 12vw, 100px);
        }

        .logo_title_image {
          width: clamp(240px, 32vw, 260px);
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
    }

    .cardWrapper {
      width: 500px;
      margin-top: 50px;
      text-align: center;
      border: 1.5px solid rgba(2, 36, 113, 1);
      border-radius: 10px;
      box-shadow: 5.17552px 5.17552px 5.17552px rgba(0, 0, 0, 0.25);
      
      .card {
        width: 100%;
        border: none;
        background: transparent;
        font-family: 'Kanit','Courier New', Courier, monospace, serif;

        p {
          color: rgba(105, 122, 173, 1);
          margin-top: calc(-1 * var(--spacing-unit));
          margin-bottom: calc(2 * var(--spacing-unit));
          font-size: clamp(14px, 2.2vw, 16px); 
        }
        .inputContainer {
          margin-bottom: calc(1 * var(--spacing-unit));
          padding-top: var(--spacing-unit);
          
        }
        .label {
          position: absolute;
          margin-top: calc(-0.7 * var(--spacing-unit));
          left: calc(2 * var(--spacing-unit));
          background: white;
          padding: 0 calc(0.5 * var(--spacing-unit));
          font-size: clamp(12px, 2vw, 14px); 
          color: rgba(2, 36, 113, 1) ;
          z-index: 100;
        }
        .labelOTP {
          font-family: 'Kanit','Courier New', Courier, monospace, serif;
          position: relative; 
          display: block; 
          margin-bottom: var(--spacing-unit); 
          padding-bottom: calc(0.1 * var(--spacing-unit)); 
          
        }
        .resendOtpContainer{
          .resendOtpButton{
            font-family: 'Kanit','Courier New', Courier, monospace, serif;
            color: rgba(64, 123, 255, 1);
            padding: 0 
          }
        }
        .button {
          font-size: var(--font-size-base);
          background-color: #0F2050;
          border-color: #0F2050;
          border-radius: 8px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
          width: 100%;
          padding: calc(1.2 * var(--spacing-unit));
          margin-top: calc(2 * var(--spacing-unit));
        }
        .disabledButton {
          background: rgba(15, 32, 80, 0.42);
          border-color: rgba(255, 255, 255, 0.42);
          color: rgb(255, 255, 255);
          cursor: not-allowed;
        }
        :global(.ant-card-head-title) {
          font-family: 'Kanit','Courier New', Courier, monospace, serif !important;
          border-bottom: none !important;
          font-size: clamp(18px, 3vw, 20px); /* Dynamic font size */
          text-decoration: underline;
          text-underline-offset: 4px;
          font-weight: 400;
          margin-top: calc(2 * var(--spacing-unit));
        }
        :global(.ant-card-head) {
          border-bottom: none !important;
        }
        :global(.ant-card-body){
          display: flex;
          flex-direction: column;
          justify-content: center;
        }
      }
    }
  }
  .websiteLink {
    position: absolute;
    bottom: 10px;
    left: 25px;
    font-size: clamp(13px, 3vw, 15px);
    color: rgba(15, 32, 80, 1);
    font-family: 'Kanit','Courier New', Courier, monospace, sans-serif;
    z-index: 100;
    text-decoration: underline;

  }  

  .inputContainer{
    :global(.ant-input),
    :global(.ant-input-password) {
      width: 100%;
      padding: calc(1.2* var(--spacing-unit)); /* Adjust padding */
      height: clamp(35px, 5vh, 40px); /* Increased height by 5px */
      font-size: clamp(12px, 2vw, 14px); /* Maintain readable font size */
      border: 1.5px solid rgba(2, 36, 113, 1) !important;
      border-radius: 8px !important;
      &:hover {
        border-color: rgba(2, 36, 113, 1) ;
      }
    
      &:focus {
        border-color: #000;
        box-shadow: none;
      }
    }
    :global(.ant-otp .ant-otp-input) {
      display: flex !important;
      background-color: rgba(217, 217, 217, 0.53); /* Add desired background color */
      padding: 5px !important; /* Optional: Add some padding for better aesthetics */
      border-radius: 0 !important;
      border-color: rgba(217, 217, 217, 0.53) !important;
      width: 40px; 
      height: 40px;
      margin-right: 20px;
      font-size: clamp(17px, 3vw, 20px);
      font-weight: bold;
    }
  }
}
@media screen and (max-width: 768px) {
  .container {
    .backgroundCircle, .imageContainer{
      display: none;
    }
    .leftSide{
      width: 100%;
      justify-content: center;
      .header{
        margin: 0.5rem;
        .logo_container{
          .logo_image{
            width: 30px;
          }
          .logo_title_image{
            width: 150px;
          }
        }
      }

      .cardWrapper{
        width: 90%;
        padding: 0.1rem;
        margin-top: 0;

        .card{
          :global(.ant-card-head-title){
            font-size: 20px;
          }
          p{
            font-size: 10px;
          }
        }
      }
    }
    .inputContainer{
      :global(.ant-otp .ant-otp-input){
          height: 30px;
          width: 30px;
          margin-right: 7px;
        }
    }
  }
}

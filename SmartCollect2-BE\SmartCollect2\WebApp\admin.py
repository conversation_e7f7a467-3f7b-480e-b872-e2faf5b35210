from django.contrib import admin

# Register your models here.
from WebApp.models import UserMst, BankMst, BranchMst


# Register your models here.
class UserAdmin(admin.ModelAdmin):
    list_display = ('UserID', 'username', 'email', 'MobileNumber', 'is_active', 'date_joined', 'UpdatedDate')
    search_fields = ('username', 'email', 'MobileNumber')
    list_filter = ('is_active', 'date_joined', 'UpdatedDate')
    ordering = ('UserID',)
    filter_horizontal = ()
    fieldsets = ()
    readonly_fields = ('UserID', 'date_joined', 'UpdatedDate')


class BankAdmin(admin.ModelAdmin):
    list_display = ('BankMstID', 'BankName', 'IsActive', 'OnBoardingDate', 'UpdatedDate', 'Remarks')
    search_fields = ('BankName',)
    list_filter = ('IsActive', 'OnBoardingDate', 'UpdatedDate')
    ordering = ('BankMstID',)
    filter_horizontal = ()
    fieldsets = ()
    readonly_fields = ('BankMstID', 'OnBoardingDate', 'UpdatedDate')


class BranchAdmin(admin.ModelAdmin):
    list_display = ('BranchMstID', 'BankMstID', 'BranchCode', 'BranchName', 'IsActive', 'CreatedDate', 'UpdatedDate', 'Remarks')
    search_fields = ('BranchName',)
    list_filter = ('IsActive', 'CreatedDate', 'UpdatedDate')
    ordering = ('BranchMstID',)
    filter_horizontal = ()
    fieldsets = ()
    readonly_fields = ('BranchMstID', 'CreatedDate', 'UpdatedDate')


admin.site.register(UserMst, UserAdmin)
admin.site.register(BankMst, BankAdmin)
admin.site.register(BranchMst, BranchAdmin)
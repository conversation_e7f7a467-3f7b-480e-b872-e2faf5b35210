import { Spin, Table, Typography } from 'antd'
import React from 'react'
import { handleDateFormatter } from '../../../constant';
import Style from "./_style.module.scss";
import PropTypes from 'prop-types';

const {Text} = Typography;

export function CustomerTable({
data, 
isLoading,   
pagination,
onPaginationChange}) {
    const columns = [
        {
            title: 'DisbursementID',
            dataIndex: 'DisbursementID',
            render: (DisbursementID) => <Text>{DisbursementID || "-"}</Text>
        },
        {
            title: 'Customer Name',
            dataIndex: 'customer_name',
            render: (value) => <Text>{value || "-"}</Text>
        },
        {
            title: 'Branch',
            dataIndex: 'Branch',
            render: (branch) => <Text>{branch || "-"}</Text>
        },
        {
            title: 'Disbursement Date',
            dataIndex: 'DisbursementDate',
            render: (DisbursementDate) => <Text style={{color:"#07223D"}}>{ DisbursementDate? handleDateFormatter(DisbursementDate) : "-"}</Text>
        },
        {
            title: 'Total Demand',
            dataIndex: 'Demand',
            render: (Demand) => <Text style={{color:"#07223D"}}>{Demand}</Text>
        },
        {
            title: 'Date Of Last Payment',
            dataIndex: 'LastPaymentDate',
            render: (DateOfLastPayment) => <Text style={{color:"#07223D"}}>{DateOfLastPayment? handleDateFormatter(DateOfLastPayment) : "-"}</Text>
        },
        {
            title: 'DPD',
            dataIndex: 'DPD',
            render: (DPD) => <Text style={{color:"#07223D"}}>{DPD}</Text>
        }
    ];

    const dataSource = Array.isArray(data)? data.map((d, i) => ({
        key: i+1,
        ...d
    })):[];
  return (
    <Spin tip={"Loading..."} spinning={isLoading}>
       <Table
           bordered
           virtual
           className={Style.CustomTable}
           columns={columns}
           dataSource={dataSource}
           scroll={{
               x:800,
               y: 360,
           }}
           pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            onChange: onPaginationChange,
            showSizeChanger:false
          }}
       />
   </Spin>
 )}

 CustomerTable.propTypes={
    data: PropTypes.array, 
    isLoading: PropTypes.bool,   
    pagination: PropTypes.object,
    onPaginationChange:PropTypes.func
 }
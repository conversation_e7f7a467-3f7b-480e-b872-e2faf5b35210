@import url("../../../index.css");

$table-radius: 22px;
$attempts-bg: #bdd1ff;
$disable: #787777;
$body: #e4f8f9;
$light-blue: #e4f8f9;
$blue: #becfff;
.download_button {
  background-color: var(--dark-blue);
  padding: 0.2rem 1rem;
  border-radius: 10px;
  cursor: pointer;
  margin-right: 1.3rem;
  border: 0px solid white;
  
  img {
    width: 20px;
  }
}

.CustomTable {
  padding-top: 1rem;

  .icon_img {
    img {
      cursor: pointer;
    }
  }

  .text {
    font-weight: 500;
  }

  .blueText {
    font-weight: 400;
    color: var(--blue);
    cursor: pointer;
  }

  .button {
    border: none;
    background-color: transparent;
    color: var(--blue);
    font-weight: 700;
    cursor: pointer;
  }

  // Global ant design classes
  // Pagination
  :global(.ant-pagination) {
    justify-content: center;
    margin: 0 !important;

    :global(.ant-pagination-prev),
    :global(.ant-pagination-next) {
      color: var(--dark-blue);
      border: 0 solid var(--blue);
      background: #e4f8f9;
      height: 25px;
      min-width: 15px;
      border-radius: 0px;
      margin: 0;

      button {
        border-radius: 0px;
      }
    }

    :global(.ant-pagination-item) {
      margin-right: 0;
      height: 0;

      a {
        color: $disable;
        font-size: 0.9rem;
        line-height: 23px;
      }

      &:hover {
        background-color: transparent;
      }
    }

    :global(.ant-pagination-item-active) {
      border: none;

      a {
        color: var(--dark-blue);
        font-size: 1.2rem;
        padding: 0;
      }
    }
  }

  // Table container
  :global(.ant-table-container) {
    padding: 0 1rem;
    margin-bottom: 0.5rem;
    background: var(--light-green);
    border-start-start-radius: $table-radius;
    border-start-end-radius: $table-radius;

    // Table header
    :global(.ant-table-header) {
      position: relative;
      margin: 0 auto;
      top: -21px;
      border-radius: $table-radius;
      :global(.ant-table-column-has-sorters) {
        background-color: var(--dark-blue);
        &:hover{
          background-color: var(--dark-blue);
        }
    
        :global(.ant-table-column-sorter-up),
        :global(.ant-table-column-sorter-down) {
          svg{
            fill: white;
          }
          &:global(.active){
            svg{
                fill: rgb(24, 155, 249)
                }
                }
            }
            }
    }

    // Table virtual body
    :global(.ant-table-tbody-virtual) {
      margin-top: -8px;
    }

    &::before {
      box-shadow: none !important;
    }
  }

  // Table rows header
  :global(.ant-table-thead > tr) {
    th {
      border-start-end-radius: 0 !important;
      background-color: var(--dark-blue);
      border-inline-end: none !important;
      color: white;
      border-bottom: none;
      white-space: nowrap;
      text-align: center;
      font-family: 'Kanit','Courier New', Courier, monospace;
      font-weight: normal;
      // &:nth-child(2) {
      //     padding-right: 150px; // Shift the second column header to the right
      // }

      &:not(:last-child):not(.ant-table-selection-column):not(
          .ant-table-row-expand-icon-cell
        ):not([colspan])::before {
        width: 0;
      }
    }
  }

  // Table body
  :global(.ant-table-tbody) {
    // Body rows
    :global(.ant-table-row) {
      // Cols
      :global(.ant-table-cell) {
        font-weight: 400;
        text-align: center;
        border-bottom: 2px solid white;
        padding: 10px;
        font-family: 'Kanit','Courier New', Courier, monospace;

        :global(.ant-typography) {
          font-family: 'Kanit','Courier New', Courier, monospace;
        }

        &:first-child {
          border-right: none;
        }

        &:nth-child(2) {
          border-right: none;
        }

        &:global(.ant-table-cell-row-hover) {
          background-color: $body;
        }
      }
    }
  }

  // Fixed Cols
  :global(.ant-table-cell-fix-right) {
    background-color: $body;
  }
}

// Responsive view
@media screen and (max-width: 768px) {
  .download_button{
    padding: 0rem 0.5rem;
    border-radius: 4px;
    img{
      width: 15px;
    }
  }

  .CustomTable {
    // Table rows header
    :global(.ant-table-thead > tr) {
      th {
        font-size: 12px;
        padding: 0.5rem;
      }
    }
    :global(.ant-table-tbody) {
      :global(.ant-table-row) {
        :global(.ant-table-cell) {
          padding: 0.6rem;
          font-size: 12px !important;
          :global(.ant-typography){
            font-size: 12px !important;
          }
        }
      }
    }

    .icon_div {
      .icon_img {
        width: 13px;
        height: 13px;
      }
    }
  }
}
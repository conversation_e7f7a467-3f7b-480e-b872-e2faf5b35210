@import url("../../../index.css");

$light-gray:#D9D9D9;
$tab-height: 7px;
$light-blue: #E4F8F9;
$switch-disable:#E6E6E8;
$switch-icon:#B8C9F9;

.update_modal{
    caret-color: #407BFF;
    .icon_img{
        width: 13px;
        height: 20px;
        img{
            width: 100%;
            object-fit: contain;
        }
    }
  
    .update_title{
        margin: 0;
        color: white;
        font-size:16px;   
        font-family: 'Kanit','Courier New', Courier, monospace;  
        font-weight: 400;
    }

    .update_button{
        background-color: var(--dark-blue);
        color: white;
        padding: 1rem 2rem;
        font-family: 'Kanit','Courier New', Courier, monospace;
        border: none;

        &:hover{
            background-color: var(--dark-blue) !important;
            color: white !important;
            border-color: white !important;  
            box-shadow:none;
        }
    }

    :global(.ant-modal-close){
        right: 14px;
        top: 10px;
        color: white;
        &:hover{
          color: white !important;
        }
        :global(.ant-modal-close-x){
          font-size:10px;
          color: white;
        }
    }  

    :global(.ant-modal-header){
        background: var(--dark-blue);
        padding: 0.5rem 0px;
        text-align: center;
        border-radius: 8px;
        
        :global(.ant-modal-title){
            color: white;
            font-weight: 500;
        }
    }

    :global(.ant-modal-content){
       padding: 6px;
       :global(.ant-modal-body){                                                                                                                                                                                                                                                                                                                                                                                                                                                          
        padding: 2rem 1rem;

        .container{
            margin-bottom: 2rem;
        }
        .text{
            color: var(--dark-blue);
            font-weight: 500;
            word-wrap: break-word;
            width: 300px;
        }
    
        :global(.ant-input-number){
            background-color: #E4F8F9;
            border:none !important;
            width: 400px;
        }

        :global(.ant-input-number-input){
           color: var(--dark-blue);
           font-weight: 400;
           font-family: 'Kanit','Courier New', Courier, monospace;
        }
        
        :global(.ant-input-number-outlined){
            &:focus-within{
               box-shadow:none !important;
            }
        }
    
        :global(.ant-input-number-handler-wrap){
            display: none;
        }
       }
    }

    :global(.ant-switch){
        min-width: 60px;
        height: 18px;
        background-color: $switch-disable;

        &:hover{
            background-color: $switch-disable;   
        }

        :global(.ant-switch-handle){
            width: 20px;
            height: 20px;
            top: -1px;
            &::before{
                background-color: $switch-icon;
                border-radius: 50%;
                top:0;
            }
        }
        &:global(.ant-switch.ant-switch-checked){
            background-color: $switch-icon;
            :global(.ant-switch-handle){
                &::before{
                    background-color: var(--dark-blue);
                }
            }
        }
    }
}

// Responsive view
@media screen and (max-width:768px){
    .update_modal{
      :global(.ant-modal-content){
        :global(.ant-modal-body){  
            padding: 0.4rem;
    
            .text,
            :global(.ant-input-number-input){
                font-size: 11px;
            }
        }
      }
      .icon_img{
        width: 10px;
        height: 25px;
      }

      .update_title{
        font-size:11px;
      }
    }
}
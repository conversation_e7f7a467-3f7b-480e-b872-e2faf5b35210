import React from 'react'
import ICON_IMG from "../../../../assets/Images/i.png";
import Style from "../_card.module.scss";
import { Flex, Typography } from 'antd';
import PropTypes from 'prop-types';

const {Title}  = Typography;

export function Card({title, loanCount, amount, image}) {
  return (
    <Flex className={Style.card_container} vertical gap={20}>
        <Flex justify='space-between' align='center'>
            <Title level={3} className={Style.title}>{title}</Title>
            <div className={Style.icon_img}>
                <img src={ICON_IMG} alt='icon'/>
            </div>
        </Flex>

        <Flex gap={20} justify='space-between'>
          <Flex justify='space-between' align='end' gap={30}>

            <Flex vertical align='center' gap={10}>
              <Title level={3} className={Style.title}>{loanCount}</Title>
              <p className={Style.desc}>#Loans</p>
            </Flex>

            <Flex vertical align='center' gap={10}>
              <Title level={3} className={Style.title}>Rs.{amount}</Title>
              <p className={Style.desc}>Amount</p>
            </Flex>   
          </Flex>
          <div className={Style.image_container}>
             <img src={image} alt={title}/>
          </div>       
        </Flex>
    </Flex>
  )
}

Card.propTypes={
  title: PropTypes.string,
  loanCount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  amount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  image: PropTypes.string
}
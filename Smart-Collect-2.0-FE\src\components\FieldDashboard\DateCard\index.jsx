import { Col, Flex, Typography } from "antd";
import React from "react";
import Style from "./_date.module.scss";
import { SMART_COLLECT_MENU_IDS } from "../../../constant";
import { Link } from "react-router";
import PropTypes from "prop-types";

const { Title, Text } = Typography;

export function DateCard({
  typeName,
  routeKey,
  readyToPayAmount,
  emiCollectionAmount,
  image,
  imageWidth,
}) {
  return (
    <Col span={24} className={Style.date_container}>
      <Flex justify="space-between">
        <div className={Style.image_container} style={{ width: imageWidth }}>
          <img src={image} alt={typeName} />
        </div>

        <Flex vertical gap={30} style={{ paddingTop: "1rem" }}>
          <Title level={3} className={Style.name}>
            {typeName}
          </Title>

          <Flex gap={60}>
            <Flex vertical align="center">
              <Text className={Style.amount}>{readyToPayAmount}</Text>
              <Text className={Style.text}>
                <Link to={`${SMART_COLLECT_MENU_IDS.READY_TO_PAY}/${routeKey}`}>
                  Promised to pay
                </Link>
              </Text>
            </Flex>

            <Flex vertical align="center">
              <Text className={Style.amount}>{emiCollectionAmount}</Text>
              <Text className={Style.text}>
                <Link
                  to={`${SMART_COLLECT_MENU_IDS.EMI_COLLECTION}/${routeKey}`}
                >
                  EMI Collection
                </Link>
              </Text>
            </Flex>
          </Flex>
        </Flex>
      </Flex>
    </Col>
  );
}
DateCard.propTypes = {
  typeName: PropTypes.string,
  routeKey: PropTypes.string,
  readyToPayAmount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  emiCollectionAmount: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
  image: PropTypes.string,
  imageWidth: PropTypes.string,
};

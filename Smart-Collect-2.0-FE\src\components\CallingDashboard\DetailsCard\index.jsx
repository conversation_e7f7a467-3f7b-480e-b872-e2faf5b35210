import { Flex, Space, Typography } from 'antd'
import React from 'react'
import Style from "./_detail-card.module.scss";
import CircularProgress from '../CircularProgress';
import { SMART_COLLECT_MENU_IDS } from '../../../constant';
import { Link } from 'react-router';
import PropTypes from 'prop-types';

const {Text, Title} = Typography;

export function DetailsCard({
cardName, 
customerCount, 
totalAmount, 
amountCollected, 
percentValue, 
image}) {
return (
  <Link to={`/${SMART_COLLECT_MENU_IDS.AGENT_CALLING_DASHBOARD}/${SMART_COLLECT_MENU_IDS.AGENT_CALLING_SUMMARY}/${cardName}`}> 
    <Flex gap={10} className={Style.detail_container} >
      <div className={Style.image_container}>
        <img src={image} alt={cardName}/>
      </div>

      <Flex vertical gap={15} style={{ flex:1 }}>
        <Flex justify='space-between'>
          <Space direction='vertical'>
            <Title level={2} className={Style.name}>{cardName}</Title>
            <Text className={Style.amount_text}>Count : <span className={Style.amount}>{customerCount}</span></Text>
          </Space>
          <CircularProgress percentage={percentValue} duration={1000}/>
        </Flex>
              
        <Flex 
          justify='space-between'
          align='center'
          gap={15} 
          className={Style.amount_details}>
            <Flex vertical gap={5}>
              <Title level={3} className={Style.amount}>Rs.{totalAmount}</Title>
              <Text className={Style.amount_text}> Total Amount</Text>
            </Flex>

            <Flex vertical gap={10} align='center'>
              <Title level={3} className={Style.amount}>Rs.{amountCollected}</Title>
              <Text className={Style.amount_text}> Amount Collected</Text>
            </Flex>
        </Flex>
      </Flex>
            
    </Flex>
  </Link>
  )
}

// Define the types
DetailsCard.propTypes={
  cardName: PropTypes.string,
  customerCount: PropTypes.number,
  totalAmount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  amountCollected: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  percentValue: PropTypes.number,
  image: PropTypes.string,
}
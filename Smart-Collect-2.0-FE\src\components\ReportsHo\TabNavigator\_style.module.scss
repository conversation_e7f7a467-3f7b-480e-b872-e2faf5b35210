@import url("../../../index.css");

$light-blue: #E3F5F6;

.container {
  position: relative;
  z-index: 100;

  .button_container {
    background-color: transparent;
    color: white;
    width: 40px;
    .img_container {
      display: flex;
      justify-content: center;
      cursor: pointer;
      input[type="image"] {
        width: 15px;
        padding: 0.5rem 0;
        cursor: pointer;
      }
    }
  }

  .modal {
    position: absolute;
    left: 0;
    top: 0;
    background-color: $light-blue;
    box-shadow: 4px 4px 4px 0px #00000040;
    width: 250px;
    padding: 0.4rem 1rem;
    z-index: 1;
    border-radius: 10px;

    .text {
      text-align: center;
      color: #407bff;
      font-family: 'Kanit','Courier New', Courier, monospace;
      font-size: 16px;
    }

    .fields {
      color: var(--dark-blue);
      font-family: 'Kanit','Courier New', Courier, monospace;
    }

    .custom_dropdown {
      .dropdown_item {
        padding: 8px;
        cursor: pointer;
        text-align: left;
        color: var(--dark-blue);
        font-family: 'Kanit','Courier New', Courier, monospace;
        background-color: transparent;
        border: none;
        display: block;
        
        &:first-child {
          margin-top: 10px;
        }
        &:last-child {
            margin-bottom: 10px;
        }

        &.selected {
          background-color: var(--dark-blue);
          box-shadow: 0px 4px 4px 0px #00000040;
          color: white; 
          border-radius: 10px;
        }
      }
    }
  }
}

// Responsive view
@media only screen and (max-width:768px){
  .container{
    .modal {
      width: 200px;
      padding: 0.4rem 0.5rem;

      img{
        width: 14px;
      }

      .text {
        font-size: 14px;
      }
      
      .custom_dropdown {
        .dropdown_item {
          padding: 4px;
          font-size: 12px;
        }
      }
    }
  }
}
import { Table, Typography } from 'antd'
import React from 'react'
import Style from "../_style.module.scss";

const {Text} = Typography;

export function LoanTable() {
    const columns = [
        {
            title: 'Loan Type',
            dataIndex: 'LoanType',
            render: (_, {LoanType}) => <Text>{LoanType || "-"}</Text>
        },
        {
            title: 'Active Count',
            dataIndex: 'ActiveCount',
            render: (_, {ActiveCount}) => <Text>{ActiveCount || "0"}</Text>
        },
        {
            title: 'Active Count (Contribution)',
            dataIndex: 'ActiveCountContribution',
            render: (_, {ActiveCountContribution}) => <Text style={{color:"#0D7430"}}>{ActiveCountContribution || "-"}</Text>
        },
        {
            title: 'POS Count',
            dataIndex: 'POSCount',
            render: (_, {POSCount}) => <Text>{POSCount || 0}</Text>
        },
        {
            title: 'POS Count (Contribution)',
            dataIndex: 'POSCountContribution',
            render: (_, {POSCountContribution}) => <Text style={{color:"#0D7430"}}>{POSCountContribution || "-"}</Text>
        }
    ];

    const dataSource = Array(2).fill({
        LoanType:"JLG Loan",
        ActiveCount:900,
        ActiveCountContribution:"58%",
        POSCount:90,
        POSCountContribution:"58%"

    }).map((data, i) => ({
        key: i+1,
        ...data
    }));
  return (
    <>  <Table
            bordered
            virtual
            className={Style.CustomTable}
            columns={columns}
            dataSource={dataSource}
            scroll={{
                x:800,
                y: 360,
            }}
            pagination={false}
        />
    </>
  )
}

from typing import Any, List, Optional
from sqlalchemy import <PERSON><PERSON><PERSON>ger, <PERSON>olean, CHAR, CheckConstraint, Column, Date, DateTime, Double, ForeignKeyConstraint, Identity, Index, Integer, JSON, Numeric, PrimaryKeyConstraint, Sequence, SmallInteger, String, Table, Text, Time, UniqueConstraint, Uuid, text
from sqlalchemy.dialects.postgresql import INET, JSONB
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship
import datetime
import decimal


class Base(DeclarativeBase):
    pass


class AccountSummaryHistory(Base):
    __tablename__ = 'AccountSummaryHistory'
    __table_args__ = (
        PrimaryKeyConstraint('HistoryID', name='AccountSummaryHistory_pkey'),
    )

    HistoryID: Mapped[int] = mapped_column(Integer, primary_key=True)
    BranchMstID: Mapped[int] = mapped_column(Integer)
    BankMstID: Mapped[int] = mapped_column(Integer)
    CustomerMstID: Mapped[int] = mapped_column(Integer)
    CustomerCode: Mapped[str] = mapped_column(String(255))
    CustomerName: Mapped[str] = mapped_column(String(255))
    MobileNumber: Mapped[str] = mapped_column(String(15))
    DisbursementID: Mapped[str] = mapped_column(String(255))
    DisbursementDate: Mapped[datetime.date] = mapped_column(Date)
    DisbursementAmt: Mapped[decimal.Decimal] = mapped_column(Numeric(18, 2))
    LoanType: Mapped[str] = mapped_column(String(50))
    CurrentBalance: Mapped[decimal.Decimal] = mapped_column(Numeric(18, 2))
    OverDueAmt: Mapped[decimal.Decimal] = mapped_column(Numeric(18, 2))
    PrincipleOutstanding: Mapped[decimal.Decimal] = mapped_column(Numeric(18, 2))
    InterestOutstanding: Mapped[decimal.Decimal] = mapped_column(Numeric(18, 2))
    TotalOutstanding: Mapped[decimal.Decimal] = mapped_column(Numeric(18, 2))
    PrinciplePending: Mapped[decimal.Decimal] = mapped_column(Numeric(18, 2))
    InterestPending: Mapped[decimal.Decimal] = mapped_column(Numeric(18, 2))
    TotalPending: Mapped[decimal.Decimal] = mapped_column(Numeric(18, 2))
    IsActive: Mapped[bool] = mapped_column(Boolean, server_default=text('true'))
    EMIAmount: Mapped[decimal.Decimal] = mapped_column(Numeric(18, 2))
    LoanMstID: Mapped[int] = mapped_column(Integer)
    Gender: Mapped[Optional[str]] = mapped_column(String(10))
    DateOfBirth: Mapped[Optional[datetime.date]] = mapped_column(Date)
    DPD: Mapped[Optional[int]] = mapped_column(Integer)
    LoanClassification: Mapped[Optional[str]] = mapped_column(String(50))
    LastPaymentDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    LastCollectedAmount: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(18, 2))
    ClosingDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    NextEMIDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    CreatedDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    Branch_id: Mapped[Optional[str]] = mapped_column(String(255))
    Branch: Mapped[Optional[str]] = mapped_column(String(255))
    Region_id: Mapped[Optional[str]] = mapped_column(String(255))
    Region: Mapped[Optional[str]] = mapped_column(String(255))
    State_id: Mapped[Optional[str]] = mapped_column(String(255))
    State: Mapped[Optional[str]] = mapped_column(String(255))
    CollectionOfficerID: Mapped[Optional[str]] = mapped_column(String)
    CollectionOfficerName: Mapped[Optional[str]] = mapped_column(String)
    groupid: Mapped[Optional[int]] = mapped_column(Integer)
    groupname: Mapped[Optional[str]] = mapped_column(String)
    CurrentDPD: Mapped[Optional[int]] = mapped_column(Integer)
    LastMonthDPD: Mapped[Optional[int]] = mapped_column(Integer)


class ActiveConversation(Base):
    __tablename__ = 'ActiveConversation'
    __table_args__ = (
        CheckConstraint("conversation_status::text = ANY (ARRAY['active'::character varying, 'completed'::character varying, 'abandoned'::character varying]::text[])", name='ActiveConversation_conversation_status_check'),
        PrimaryKeyConstraint('uuid', name='ActiveConversation_pkey')
    )

    uuid: Mapped[Uuid] = mapped_column(Uuid, primary_key=True)
    user_id: Mapped[str] = mapped_column(String(20))
    created_at: Mapped[datetime.datetime] = mapped_column(DateTime(True), server_default=text('CURRENT_TIMESTAMP'))
    last_updated: Mapped[datetime.datetime] = mapped_column(DateTime(True), server_default=text('CURRENT_TIMESTAMP'))
    conversation_status: Mapped[str] = mapped_column(String(20), server_default=text("'active'::character varying"))
    form_data: Mapped[Optional[str]] = mapped_column(Text)
    title: Mapped[Optional[str]] = mapped_column(String)

    ConversationMessage: Mapped[List['ConversationMessage']] = relationship('ConversationMessage', back_populates='conversation')
    UserConversations: Mapped[List['UserConversations']] = relationship('UserConversations', back_populates='last_active_conversation')
    UserConversations_active_conversations: Mapped[List['UserConversationsActiveConversations']] = relationship('UserConversationsActiveConversations', back_populates='activeconversation')


class BankMst(Base):
    __tablename__ = 'BankMst'
    __table_args__ = (
        PrimaryKeyConstraint('BankMstID', name='TBL_BankMst_pkey'),
    )

    BankMstID: Mapped[int] = mapped_column(Integer, primary_key=True)
    BankName: Mapped[str] = mapped_column(String(255))
    OnBoardingDate: Mapped[Optional[datetime.date]] = mapped_column(Date, server_default=text('CURRENT_DATE'))
    IsActive: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('true'))
    UpdatedDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    Remarks: Mapped[Optional[str]] = mapped_column(Text)
    SubscriptionID: Mapped[Optional[int]] = mapped_column(Integer)
    SubscriptionType: Mapped[Optional[str]] = mapped_column(String(50))
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))
    WhatsApp: Mapped[Optional[bool]] = mapped_column(Boolean)
    VoiceBot: Mapped[Optional[bool]] = mapped_column(Boolean)
    Blaster: Mapped[Optional[bool]] = mapped_column(Boolean)
    IVR: Mapped[Optional[bool]] = mapped_column(Boolean)
    SMS: Mapped[Optional[bool]] = mapped_column(Boolean)
    Is_UPI: Mapped[Optional[bool]] = mapped_column(Boolean)
    Key_ID: Mapped[Optional[str]] = mapped_column(String(255))
    Secret_Key: Mapped[Optional[str]] = mapped_column(String(255))

    CampaignMst: Mapped[List['CampaignMst']] = relationship('CampaignMst', back_populates='BankMst_')
    CommFlowMst: Mapped[List['CommFlowMst']] = relationship('CommFlowMst', back_populates='BankMst_')
    DPDCategory: Mapped[List['DPDCategory']] = relationship('DPDCategory', back_populates='BankMst_')
    DesignationMst: Mapped[List['DesignationMst']] = relationship('DesignationMst', back_populates='BankMst_')
    DisbursementFile: Mapped[List['DisbursementFile']] = relationship('DisbursementFile', back_populates='BankMst_')
    Filters: Mapped[List['Filters']] = relationship('Filters', back_populates='BankMst_')
    GeographyMst: Mapped[List['GeographyMst']] = relationship('GeographyMst', back_populates='BankMst_')
    IVRFlowMst: Mapped[List['IVRFlowMst']] = relationship('IVRFlowMst', back_populates='BankMst_')
    LanguageMst: Mapped[List['LanguageMst']] = relationship('LanguageMst', back_populates='BankMst_')
    PT_ExtraColumn: Mapped[List['PTExtraColumn']] = relationship('PTExtraColumn', back_populates='BankMst_')
    SubscriptionMst: Mapped[List['SubscriptionMst']] = relationship('SubscriptionMst', back_populates='BankMst_')
    WhatsAppFlowMst: Mapped[List['WhatsAppFlowMst']] = relationship('WhatsAppFlowMst', foreign_keys='[WhatsAppFlowMst.BankID]', back_populates='BankMst_')
    WhatsAppFlowMst_: Mapped[List['WhatsAppFlowMst']] = relationship('WhatsAppFlowMst', foreign_keys='[WhatsAppFlowMst.BankMstID]', back_populates='BankMst1')
    WhatsAppKeyMapping: Mapped[List['WhatsAppKeyMapping']] = relationship('WhatsAppKeyMapping', back_populates='BankMst_')
    bankcontrols: Mapped[Optional['Bankcontrols']] = relationship('Bankcontrols', uselist=False, back_populates='BankMst_')
    BlasterTemplateMapping: Mapped[List['BlasterTemplateMapping']] = relationship('BlasterTemplateMapping', back_populates='BankMst_')
    BranchMst: Mapped[List['BranchMst']] = relationship('BranchMst', back_populates='BankMst_')
    CommFlow: Mapped[List['CommFlow']] = relationship('CommFlow', back_populates='BankMst_')
    EmployeeMst: Mapped[List['EmployeeMst']] = relationship('EmployeeMst', back_populates='BankMst_')
    IVRTemplateMapping: Mapped[List['IVRTemplateMapping']] = relationship('IVRTemplateMapping', back_populates='BankMst_')
    PeriodicCommunication: Mapped[List['PeriodicCommunication']] = relationship('PeriodicCommunication', back_populates='BankMst_')
    SMSTemplateMapping: Mapped[List['SMSTemplateMapping']] = relationship('SMSTemplateMapping', back_populates='BankMst_')
    VoiceBotTemplateMapping: Mapped[List['VoiceBotTemplateMapping']] = relationship('VoiceBotTemplateMapping', back_populates='BankMst_')
    WhatsAppTemplateMapping: Mapped[List['WhatsAppTemplateMapping']] = relationship('WhatsAppTemplateMapping', back_populates='BankMst_')
    BlasterUserTemplate: Mapped[List['BlasterUserTemplate']] = relationship('BlasterUserTemplate', back_populates='BankMst_')
    CustomerMst: Mapped[List['CustomerMst']] = relationship('CustomerMst', back_populates='BankMst_')
    EmployeeAccess: Mapped[List['EmployeeAccess']] = relationship('EmployeeAccess', back_populates='BankMst_')
    FilterMst: Mapped[List['FilterMst']] = relationship('FilterMst', back_populates='BankMst_')
    IVRFlowMapping: Mapped[List['IVRFlowMapping']] = relationship('IVRFlowMapping', back_populates='BankMst_')
    SMSUserTemplate: Mapped[List['SMSUserTemplate']] = relationship('SMSUserTemplate', back_populates='BankMst_')
    UserMst: Mapped[List['UserMst']] = relationship('UserMst', back_populates='BankMstID')
    VoiceBotUserTemplate: Mapped[List['VoiceBotUserTemplate']] = relationship('VoiceBotUserTemplate', back_populates='BankMst_')
    WhatsAppFlowMapping: Mapped[List['WhatsAppFlowMapping']] = relationship('WhatsAppFlowMapping', back_populates='BankMst_')
    WhatsAppUserTemplate: Mapped[List['WhatsAppUserTemplate']] = relationship('WhatsAppUserTemplate', back_populates='BankMst_')
    LoanMst: Mapped[List['LoanMst']] = relationship('LoanMst', back_populates='BankMst_')
    BMAllocation: Mapped[List['BMAllocation']] = relationship('BMAllocation', back_populates='BankMst_')
    BlasterHistory: Mapped[List['BlasterHistory']] = relationship('BlasterHistory', back_populates='BankMst_')
    BlasterQueue: Mapped[List['BlasterQueue']] = relationship('BlasterQueue', back_populates='BankMst_')
    CommunicationQueue: Mapped[List['CommunicationQueue']] = relationship('CommunicationQueue', back_populates='BankMst_')
    DNDHistory: Mapped[List['DNDHistory']] = relationship('DNDHistory', back_populates='BankMst_')
    Dialer: Mapped[List['Dialer']] = relationship('Dialer', back_populates='BankMst_')
    IVRCallHistory: Mapped[List['IVRCallHistory']] = relationship('IVRCallHistory', back_populates='BankMst_')
    IVRQueue: Mapped[List['IVRQueue']] = relationship('IVRQueue', back_populates='BankMst_')
    LoanInstallmentMst: Mapped[List['LoanInstallmentMst']] = relationship('LoanInstallmentMst', back_populates='BankMst_')
    Response: Mapped[List['Response']] = relationship('Response', back_populates='BankMst_')
    SMSHistory: Mapped[List['SMSHistory']] = relationship('SMSHistory', back_populates='BankMst_')
    SMSQueue: Mapped[List['SMSQueue']] = relationship('SMSQueue', back_populates='BankMst_')
    SecondaryCustMst: Mapped[List['SecondaryCustMst']] = relationship('SecondaryCustMst', back_populates='BankMst_')
    Transactions: Mapped[List['Transactions']] = relationship('Transactions', back_populates='BankMst_')
    UserFeedback: Mapped[List['UserFeedback']] = relationship('UserFeedback', back_populates='BankMst_')
    VoiceBotHistory: Mapped[List['VoiceBotHistory']] = relationship('VoiceBotHistory', back_populates='BankMst_')
    VoiceBotQueue: Mapped[List['VoiceBotQueue']] = relationship('VoiceBotQueue', back_populates='BankMst_')
    WhatsAppHistory: Mapped[List['WhatsAppHistory']] = relationship('WhatsAppHistory', back_populates='BankMst_')
    WhatsAppQueue: Mapped[List['WhatsAppQueue']] = relationship('WhatsAppQueue', back_populates='BankMst_')
    CoAppMst: Mapped[List['CoAppMst']] = relationship('CoAppMst', back_populates='BankMst_')


class CollectionFile(Base):
    __tablename__ = 'CollectionFile'
    __table_args__ = (
        PrimaryKeyConstraint('CollectionFileID', name='CollectionFile_pkey'),
        UniqueConstraint('DisbursementID', 'BankMstID', name='collectionfile_unique')
    )

    CollectionFileID: Mapped[int] = mapped_column(Integer, primary_key=True)
    DisbursementID: Mapped[str] = mapped_column(String(255))
    PrincipleCollected: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    InterestCollected: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    InstStartDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    CollectedAmount: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    CollectionDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    BMID: Mapped[Optional[int]] = mapped_column(Integer)
    POS: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    LoanType: Mapped[Optional[str]] = mapped_column(String(255))
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)
    BranchMstID: Mapped[Optional[int]] = mapped_column(Integer)
    CreatedDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    BranchName: Mapped[Optional[str]] = mapped_column(String)
    CustomerId: Mapped[Optional[int]] = mapped_column(Integer)


class CommunicationMapping(Base):
    __tablename__ = 'CommunicationMapping'
    __table_args__ = (
        PrimaryKeyConstraint('id', name='CommunicationMapping_pkey'),
    )

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    BankMstID: Mapped[int] = mapped_column(Integer)
    WhatsApp: Mapped[Optional[bool]] = mapped_column(Boolean)
    VoiceBot: Mapped[Optional[bool]] = mapped_column(Boolean)
    Blaster: Mapped[Optional[bool]] = mapped_column(Boolean)
    IVR: Mapped[Optional[bool]] = mapped_column(Boolean)
    SMS: Mapped[Optional[bool]] = mapped_column(Boolean)


class ComponentMst(Base):
    __tablename__ = 'ComponentMst'
    __table_args__ = (
        PrimaryKeyConstraint('id', name='ComponentMst_pkey'),
    )

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    BankMstID: Mapped[int] = mapped_column(Integer)
    Designation: Mapped[str] = mapped_column(String(100))
    Screens: Mapped[Optional[dict]] = mapped_column(JSONB)


class ConversationHistory(Base):
    __tablename__ = 'ConversationHistory'
    __table_args__ = (
        PrimaryKeyConstraint('uuid', name='ConversationHistory_pkey'),
    )

    uuid: Mapped[Uuid] = mapped_column(Uuid, primary_key=True)
    user_id: Mapped[str] = mapped_column(String(20))
    created_at: Mapped[datetime.datetime] = mapped_column(DateTime(True))
    completed_at: Mapped[datetime.datetime] = mapped_column(DateTime(True), server_default=text('CURRENT_TIMESTAMP'))
    full_conversation: Mapped[str] = mapped_column(Text)
    form_data: Mapped[Optional[str]] = mapped_column(Text)
    title: Mapped[Optional[str]] = mapped_column(String)

    UserConversations_completed_conversations: Mapped[List['UserConversationsCompletedConversations']] = relationship('UserConversationsCompletedConversations', back_populates='conversationhistory')


class PaymentLogs(Base):
    __tablename__ = 'PaymentLogs'
    __table_args__ = (
        PrimaryKeyConstraint('id', name='PaymentLogs_pkey'),
    )

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    CustMstID: Mapped[Optional[int]] = mapped_column(Integer)
    QR_ID: Mapped[Optional[str]] = mapped_column(String)
    Payment_Link: Mapped[Optional[str]] = mapped_column(String(200))
    QR_Link: Mapped[Optional[str]] = mapped_column(String(200))
    DateTime_: Mapped[Optional[datetime.datetime]] = mapped_column('DateTime', DateTime)
    Status: Mapped[Optional[str]] = mapped_column(String(200))
    ResponseID: Mapped[Optional[int]] = mapped_column(Integer)
    Link_ID: Mapped[Optional[str]] = mapped_column(String(200))


class RecordingSummary(Base):
    __tablename__ = 'RecordingSummary'
    __table_args__ = (
        PrimaryKeyConstraint('id', name='RecordingSummary_pkey'),
    )

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    Recording: Mapped[Optional[str]] = mapped_column(String)
    Summarization: Mapped[Optional[str]] = mapped_column(String)


class TransactionLogs(Base):
    __tablename__ = 'Transaction_Logs'
    __table_args__ = (
        PrimaryKeyConstraint('id', name='Transaction_Logs_pkey'),
    )

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    QR_ID: Mapped[Optional[str]] = mapped_column(String)
    Link_ID: Mapped[Optional[str]] = mapped_column(String)
    Payment_ID: Mapped[Optional[str]] = mapped_column(String)
    Amount: Mapped[Optional[int]] = mapped_column(Integer)
    Status: Mapped[Optional[str]] = mapped_column(String)
    Method: Mapped[Optional[str]] = mapped_column(String)
    VPA: Mapped[Optional[str]] = mapped_column(String)
    Contact: Mapped[Optional[str]] = mapped_column(String)
    Payment_Cust_ID: Mapped[Optional[str]] = mapped_column(String)
    Transaction_date: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime)
    Body: Mapped[Optional[dict]] = mapped_column(JSON)
    DateTime_: Mapped[Optional[datetime.datetime]] = mapped_column('DateTime', DateTime, server_default=text('CURRENT_TIMESTAMP'))


t_WrongNumberHistory = Table(
    'WrongNumberHistory', Base.metadata,
    Column('BankMstID', Integer, nullable=False),
    Column('CustomerMstID', Integer),
    Column('LoanMstID', Integer),
    Column('PreviousNumber', CHAR(15)),
    Column('UpdatedNumber', CHAR(15))
)


class AuthGroup(Base):
    __tablename__ = 'auth_group'
    __table_args__ = (
        PrimaryKeyConstraint('id', name='auth_group_pkey'),
        UniqueConstraint('name', name='auth_group_name_key'),
        Index('auth_group_name_a6ea08ec_like', 'name')
    )

    id: Mapped[int] = mapped_column(Integer, Identity(start=1, increment=1, minvalue=1, maxvalue=**********, cycle=False, cache=1), primary_key=True)
    name: Mapped[str] = mapped_column(String(150))

    auth_group_permissions: Mapped[List['AuthGroupPermissions']] = relationship('AuthGroupPermissions', back_populates='group')
    UserMst_groups: Mapped[List['UserMstGroups']] = relationship('UserMstGroups', back_populates='group')


class CampaignBlasterusertemplate(Base):
    __tablename__ = 'campaign_blasterusertemplate'
    __table_args__ = (
        PrimaryKeyConstraint('BlasterUserTemplateID', name='campaign_blasterusertemplate_pkey'),
    )

    BlasterUserTemplateID: Mapped[int] = mapped_column(Integer, Identity(start=1, increment=1, minvalue=1, maxvalue=**********, cycle=False, cache=1), primary_key=True)
    TemplateName: Mapped[str] = mapped_column(String(255))
    TemplateBody: Mapped[str] = mapped_column(Text)
    BankMstID: Mapped[int] = mapped_column(Integer)
    EmployeeMstID: Mapped[int] = mapped_column(Integer)
    VariableNumber: Mapped[int] = mapped_column(Integer)
    VariableField: Mapped[str] = mapped_column(String(255))


t_campaign_count = Table(
    'campaign_count', Base.metadata,
    Column('count', BigInteger)
)


t_commflow_id = Table(
    'commflow_id', Base.metadata,
    Column('CommFlowID', Integer)
)


t_communication_type = Table(
    'communication_type', Base.metadata,
    Column('CommunicationType', String(100))
)


t_dashboard_data = Table(
    'dashboard_data', Base.metadata,
    Column('json_agg', JSON)
)


class DjangoContentType(Base):
    __tablename__ = 'django_content_type'
    __table_args__ = (
        PrimaryKeyConstraint('id', name='django_content_type_pkey'),
        UniqueConstraint('app_label', 'model', name='django_content_type_app_label_model_76bd3d3b_uniq')
    )

    id: Mapped[int] = mapped_column(Integer, Identity(start=1, increment=1, minvalue=1, maxvalue=**********, cycle=False, cache=1), primary_key=True)
    app_label: Mapped[str] = mapped_column(String(100))
    model: Mapped[str] = mapped_column(String(100))

    auth_permission: Mapped[List['AuthPermission']] = relationship('AuthPermission', back_populates='content_type')
    django_admin_log: Mapped[List['DjangoAdminLog']] = relationship('DjangoAdminLog', back_populates='content_type')


class DjangoMigrations(Base):
    __tablename__ = 'django_migrations'
    __table_args__ = (
        PrimaryKeyConstraint('id', name='django_migrations_pkey'),
    )

    id: Mapped[int] = mapped_column(BigInteger, Identity(start=1, increment=1, minvalue=1, maxvalue=9223***************, cycle=False, cache=1), primary_key=True)
    app: Mapped[str] = mapped_column(String(255))
    name: Mapped[str] = mapped_column(String(255))
    applied: Mapped[datetime.datetime] = mapped_column(DateTime(True))


class DjangoSession(Base):
    __tablename__ = 'django_session'
    __table_args__ = (
        PrimaryKeyConstraint('session_key', name='django_session_pkey'),
        Index('django_session_expire_date_a5c62663', 'expire_date'),
        Index('django_session_session_key_c0390e0f_like', 'session_key')
    )

    session_key: Mapped[str] = mapped_column(String(40), primary_key=True)
    session_data: Mapped[str] = mapped_column(Text)
    expire_date: Mapped[datetime.datetime] = mapped_column(DateTime(True))


t_dynamic_sql = Table(
    'dynamic_sql', Base.metadata,
    Column('generated_query', Text)
)


t_engagement_data = Table(
    'engagement_data', Base.metadata,
    Column('json_agg', JSON)
)


t_filter_count = Table(
    'filter_count', Base.metadata,
    Column('count', BigInteger)
)


t_where_conditions = Table(
    'where_conditions', Base.metadata,
    Column('string_agg', Text)
)


class CampaignMst(Base):
    __tablename__ = 'CampaignMst'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='CampaignMst_FK_BankMstID'),
        PrimaryKeyConstraint('CampaignMstID', name='CampaignMst_pkey'),
        UniqueConstraint('Name', 'BankMstID', name='campaignmst_unique')
    )

    CampaignMstID: Mapped[int] = mapped_column(Integer, primary_key=True)
    Name: Mapped[str] = mapped_column(String(255))
    IsActive: Mapped[Optional[bool]] = mapped_column(Boolean)
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)
    CreatedDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    Priority: Mapped[Optional[str]] = mapped_column(String)
    Status: Mapped[Optional[str]] = mapped_column(String)
    CampaignType: Mapped[Optional[str]] = mapped_column(String)
    Language: Mapped[Optional[str]] = mapped_column(String)

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='CampaignMst')
    CommFlow: Mapped[List['CommFlow']] = relationship('CommFlow', back_populates='CampaignMst_')
    FilterMst: Mapped[List['FilterMst']] = relationship('FilterMst', back_populates='CampaignMst_')


class CommFlowMst(Base):
    __tablename__ = 'CommFlowMst'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='CommFlowMst_fk_BankMstID'),
        PrimaryKeyConstraint('CommFlowMstID', name='CommFlowMst_pkey')
    )

    CommFlowMstID: Mapped[int] = mapped_column(Integer, primary_key=True)
    BankName: Mapped[str] = mapped_column(String(255))
    CreatedDate: Mapped[datetime.datetime] = mapped_column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    BankMstID: Mapped[int] = mapped_column(Integer)
    IsActive: Mapped[bool] = mapped_column(Boolean, server_default=text('true'))
    Type: Mapped[str] = mapped_column(String(50))
    ColumnName: Mapped[Optional[str]] = mapped_column(String(255))
    BasedOnTable: Mapped[Optional[str]] = mapped_column(String)
    BasedOnColumn: Mapped[Optional[str]] = mapped_column(String(255))
    FlowName: Mapped[Optional[str]] = mapped_column(String)

    BankMst_: Mapped['BankMst'] = relationship('BankMst', back_populates='CommFlowMst')
    CommFlow: Mapped[List['CommFlow']] = relationship('CommFlow', back_populates='CommFlowMst_')
    PeriodicCommunication: Mapped[List['PeriodicCommunication']] = relationship('PeriodicCommunication', back_populates='CommFlowMst_')
    FilterMst: Mapped[List['FilterMst']] = relationship('FilterMst', back_populates='CommFlowMst_')


class ConversationMessage(Base):
    __tablename__ = 'ConversationMessage'
    __table_args__ = (
        ForeignKeyConstraint(['conversation_id'], ['ActiveConversation.uuid'], ondelete='CASCADE', name='ConversationMessage_conversation_id_fkey'),
        PrimaryKeyConstraint('id', name='ConversationMessage_pkey')
    )

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    conversation_id: Mapped[Uuid] = mapped_column(Uuid)
    user_message: Mapped[str] = mapped_column(Text)
    ai_response: Mapped[str] = mapped_column(Text)
    created_at: Mapped[datetime.datetime] = mapped_column(DateTime(True), server_default=text('CURRENT_TIMESTAMP'))
    is_edited: Mapped[Optional[bool]] = mapped_column(Boolean)

    conversation: Mapped['ActiveConversation'] = relationship('ActiveConversation', back_populates='ConversationMessage')


class DPDCategory(Base):
    __tablename__ = 'DPDCategory'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='DPDCategory_BankMstID_fkey'),
        PrimaryKeyConstraint('DPDCategoryID', name='DPDCategory_pkey')
    )

    DPDCategoryID: Mapped[int] = mapped_column(Integer, primary_key=True)
    RangeStart: Mapped[int] = mapped_column(Integer)
    RangeEnd: Mapped[int] = mapped_column(Integer)
    CategoryLabel: Mapped[str] = mapped_column(String(255))
    CreatedOn: Mapped[datetime.datetime] = mapped_column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    BankMstID: Mapped[int] = mapped_column(Integer)
    UpdatedOn: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime)
    DeletedOn: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime)
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))

    BankMst_: Mapped['BankMst'] = relationship('BankMst', back_populates='DPDCategory')


class DesignationMst(Base):
    __tablename__ = 'DesignationMst'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='DesignationMst_BankMstID_fkey'),
        PrimaryKeyConstraint('DesignationMstID', name='DesignationMst_pkey')
    )

    DesignationMstID: Mapped[int] = mapped_column(Integer, primary_key=True)
    DesignationName: Mapped[str] = mapped_column(String(255))
    BankMstID: Mapped[int] = mapped_column(Integer)
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))

    BankMst_: Mapped['BankMst'] = relationship('BankMst', back_populates='DesignationMst')
    EmployeeMst: Mapped[List['EmployeeMst']] = relationship('EmployeeMst', back_populates='DesignationMst_')
    EmployeeAccess: Mapped[List['EmployeeAccess']] = relationship('EmployeeAccess', back_populates='DesignationMst_')


class DisbursementFile(Base):
    __tablename__ = 'DisbursementFile'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], ondelete='CASCADE', name='FK_DisbursementFile_BankMstID'),
        PrimaryKeyConstraint('ID', name='DisbursementFile_pkey'),
        UniqueConstraint('DisbursementID', 'BankMstID', name='disbursementfile_unique')
    )

    ID: Mapped[int] = mapped_column(Integer, primary_key=True)
    CustomerId: Mapped[int] = mapped_column(Integer)
    DisbursementID: Mapped[str] = mapped_column(String(255))
    LoanType: Mapped[Optional[str]] = mapped_column(String(100))
    DATAOPEN: Mapped[Optional[datetime.date]] = mapped_column(Date)
    DateClose: Mapped[Optional[datetime.date]] = mapped_column(Date)
    CustomerName: Mapped[Optional[str]] = mapped_column(String(255))
    DisburseAmount: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    RateOfInterest: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(5, 2))
    RepaymentTenure: Mapped[Optional[int]] = mapped_column(Integer)
    EMIAmount: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    PaymentFrequency: Mapped[Optional[int]] = mapped_column(Integer)
    NumberOfDaysPastDue: Mapped[Optional[int]] = mapped_column(Integer)
    BUID: Mapped[Optional[int]] = mapped_column(Integer)
    BU_NAME: Mapped[Optional[str]] = mapped_column('BU NAME', String(255))
    BMID: Mapped[Optional[int]] = mapped_column(Integer)
    Gender: Mapped[Optional[str]] = mapped_column(String(10))
    Mobile_No: Mapped[Optional[str]] = mapped_column('Mobile No', String(15))
    Alternate_Mobile_No: Mapped[Optional[str]] = mapped_column('Alternate Mobile No', String(15))
    PANNo: Mapped[Optional[str]] = mapped_column(String(10))
    EmailID: Mapped[Optional[str]] = mapped_column(String(255))
    Address: Mapped[Optional[str]] = mapped_column(Text)
    Pincode: Mapped[Optional[str]] = mapped_column(String(6))
    InstStartDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    DateOfBirth: Mapped[Optional[datetime.date]] = mapped_column(Date)
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)
    CollectionOfficerID: Mapped[Optional[str]] = mapped_column(String)
    CollectionOfficerName: Mapped[Optional[str]] = mapped_column(String)
    BranchName: Mapped[Optional[str]] = mapped_column(String)
    BranchCode: Mapped[Optional[str]] = mapped_column(String)
    CustomerCode: Mapped[Optional[str]] = mapped_column(String)
    DND: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('false'))
    groupid: Mapped[Optional[str]] = mapped_column(String)
    groupname: Mapped[Optional[str]] = mapped_column(String)

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='DisbursementFile')


class Filters(Base):
    __tablename__ = 'Filters'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], ondelete='CASCADE', name='FilterMst_fk_BankID'),
        PrimaryKeyConstraint('FiltersID', name='Filters_pkey')
    )

    FiltersID: Mapped[int] = mapped_column(Integer, primary_key=True)
    BankMstID: Mapped[int] = mapped_column(Integer)
    Column: Mapped[str] = mapped_column(String(255))
    Comparison: Mapped[str] = mapped_column(String(50))
    Value: Mapped[str] = mapped_column(String(255))

    BankMst_: Mapped['BankMst'] = relationship('BankMst', back_populates='Filters')
    FilterMst: Mapped[List['FilterMst']] = relationship('FilterMst', back_populates='Filters_')


class GeographyMst(Base):
    __tablename__ = 'GeographyMst'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='GeographyMst_BankMstID_fkey'),
        PrimaryKeyConstraint('ID', name='GeographyMst_pkey'),
        Index('idx_geographymst_bankmstid', 'BankMstID'),
        Index('idx_geographymst_bucode', 'BUCode'),
        Index('idx_geographymst_buname', 'BUName'),
        Index('idx_geographymst_butype', 'BUType'),
        Index('idx_geographymst_reportingbuid', 'ReportingBUID')
    )

    ID: Mapped[int] = mapped_column(Integer, Sequence('GeographyMst_BUID_seq'), primary_key=True)
    BUCode: Mapped[str] = mapped_column(String(50))
    BUName: Mapped[str] = mapped_column(String(255))
    BUType: Mapped[str] = mapped_column(String(50))
    ReportingBUID: Mapped[Optional[str]] = mapped_column(String(50))
    ReportingBUType: Mapped[Optional[str]] = mapped_column(String(50))
    BUMobile: Mapped[Optional[str]] = mapped_column(String(15))
    BUEmail: Mapped[Optional[str]] = mapped_column(String(255))
    IsActive: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('true'))
    CreatedDate: Mapped[Optional[datetime.date]] = mapped_column(Date, server_default=text('CURRENT_DATE'))
    UpdatedDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='GeographyMst')
    EmployeeMst: Mapped[List['EmployeeMst']] = relationship('EmployeeMst', back_populates='GeographyMst_')
    EmployeeAccess: Mapped[List['EmployeeAccess']] = relationship('EmployeeAccess', back_populates='GeographyMst_')


class IVRFlowMst(Base):
    __tablename__ = 'IVRFlowMst'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='IVRFlowMst_fk_BankMstID'),
        PrimaryKeyConstraint('IVRFlowMstID', name='IVRFlowMst_pkey')
    )

    IVRFlowMstID: Mapped[int] = mapped_column(Integer, primary_key=True)
    IsActive: Mapped[bool] = mapped_column(Boolean, server_default=text('true'))
    FlowName: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='IVRFlowMst')
    IVRFlowMapping: Mapped[List['IVRFlowMapping']] = relationship('IVRFlowMapping', back_populates='IVRFlowMst_')


class LanguageMst(Base):
    __tablename__ = 'LanguageMst'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='LanguageMst_BankMstID_fkey'),
        PrimaryKeyConstraint('LngMstID', name='LanguageMst_pkey')
    )

    LngMstID: Mapped[int] = mapped_column(Integer, primary_key=True)
    Language: Mapped[str] = mapped_column(String(255))
    IsActive: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('true'))
    CreatedDate: Mapped[Optional[datetime.date]] = mapped_column(Date, server_default=text('CURRENT_DATE'))
    UpdatedDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='LanguageMst')
    BlasterTemplateMapping: Mapped[List['BlasterTemplateMapping']] = relationship('BlasterTemplateMapping', back_populates='LanguageMst_')
    BranchMst: Mapped[List['BranchMst']] = relationship('BranchMst', back_populates='LanguageMst_')
    CollectionOfficerAllocation: Mapped[List['CollectionOfficerAllocation']] = relationship('CollectionOfficerAllocation', back_populates='LanguageMst_')
    IVRTemplateMapping: Mapped[List['IVRTemplateMapping']] = relationship('IVRTemplateMapping', back_populates='LanguageMst_')
    SMSTemplateMapping: Mapped[List['SMSTemplateMapping']] = relationship('SMSTemplateMapping', back_populates='LanguageMst_')
    VoiceBotTemplateMapping: Mapped[List['VoiceBotTemplateMapping']] = relationship('VoiceBotTemplateMapping', back_populates='LanguageMst_')
    WhatsAppTemplateMapping: Mapped[List['WhatsAppTemplateMapping']] = relationship('WhatsAppTemplateMapping', back_populates='LanguageMst_')
    IVRFlowMapping: Mapped[List['IVRFlowMapping']] = relationship('IVRFlowMapping', back_populates='LanguageMst_')
    WhatsAppFlowMapping: Mapped[List['WhatsAppFlowMapping']] = relationship('WhatsAppFlowMapping', back_populates='LanguageMst_')
    BMAllocation: Mapped[List['BMAllocation']] = relationship('BMAllocation', back_populates='LanguageMst_')
    WhatsAppHistory: Mapped[List['WhatsAppHistory']] = relationship('WhatsAppHistory', back_populates='LanguageMst_')


class PTExtraColumn(Base):
    __tablename__ = 'PT_ExtraColumn'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='PT_ExtraColumn_BankMstID_fkey'),
        PrimaryKeyConstraint('PT_EC_ID', name='PT_ExtraColumn_pkey')
    )

    PT_EC_ID: Mapped[int] = mapped_column(Integer, primary_key=True)
    TableName: Mapped[str] = mapped_column(String(255))
    ColumnName: Mapped[str] = mapped_column(String(255))
    BankMstID: Mapped[int] = mapped_column(Integer)
    User_ColumnName: Mapped[str] = mapped_column(String(255))
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))

    BankMst_: Mapped['BankMst'] = relationship('BankMst', back_populates='PT_ExtraColumn')


class PriorityTable(BankMst):
    __tablename__ = 'PriorityTable'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='PriorityTable_BankMstID_fkey'),
        PrimaryKeyConstraint('BankMstID', name='PriorityTable_pkey')
    )

    BankMstID: Mapped[int] = mapped_column(Integer, primary_key=True)
    IsWhatsApp: Mapped[Optional[bool]] = mapped_column(Boolean)
    WhatsAppComm: Mapped[Optional[int]] = mapped_column(Integer)
    IsVoiceBot: Mapped[Optional[bool]] = mapped_column(Boolean)
    VoiceBotComm: Mapped[Optional[int]] = mapped_column(Integer)
    IsSMS: Mapped[Optional[bool]] = mapped_column(Boolean)
    SMSComm: Mapped[Optional[int]] = mapped_column(Integer)
    IsIVR: Mapped[Optional[bool]] = mapped_column(Boolean)
    IVRComm: Mapped[Optional[int]] = mapped_column(Integer)
    IsBlaster: Mapped[Optional[bool]] = mapped_column(Boolean)
    BlasterComm: Mapped[Optional[int]] = mapped_column(Integer)


class SubscriptionMst(Base):
    __tablename__ = 'SubscriptionMst'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='SubscriptionMst_BankMstID_fkey'),
        PrimaryKeyConstraint('SubscriptionMstID', name='SubscriptionMst_pkey')
    )

    SubscriptionMstID: Mapped[int] = mapped_column(Integer, primary_key=True)
    SubscriptionType: Mapped[str] = mapped_column(String(255))
    IsActive: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('true'))
    StartDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    EndDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    LastUpdated: Mapped[Optional[datetime.date]] = mapped_column(Date)
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)
    Screens: Mapped[Optional[dict]] = mapped_column(JSONB)

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='SubscriptionMst')


class UserConversations(Base):
    __tablename__ = 'UserConversations'
    __table_args__ = (
        ForeignKeyConstraint(['last_active_conversation_id'], ['ActiveConversation.uuid'], ondelete='SET NULL', name='fk_last_active_conversation'),
        PrimaryKeyConstraint('id', name='UserConversations_pkey'),
        Index('idx_userconversations_user_id', 'user_id')
    )

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    user_id: Mapped[str] = mapped_column(String(20))
    last_active_conversation_id: Mapped[Optional[Uuid]] = mapped_column(Uuid)

    last_active_conversation: Mapped[Optional['ActiveConversation']] = relationship('ActiveConversation', back_populates='UserConversations')
    UserConversations_active_conversations: Mapped[List['UserConversationsActiveConversations']] = relationship('UserConversationsActiveConversations', back_populates='userconversations')
    UserConversations_completed_conversations: Mapped[List['UserConversationsCompletedConversations']] = relationship('UserConversationsCompletedConversations', back_populates='userconversations')


class WhatsAppFlowMst(Base):
    __tablename__ = 'WhatsAppFlowMst'
    __table_args__ = (
        ForeignKeyConstraint(['BankID'], ['BankMst.BankMstID'], name='WhatsAppFlowMst_fk_BankID'),
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='WhatsAppFlowMst_BankMstID_fkey'),
        PrimaryKeyConstraint('WhatsAppFlowMstID', name='WhatsAppFlowMst_pkey')
    )

    WhatsAppFlowMstID: Mapped[int] = mapped_column(Integer, primary_key=True)
    FlowName: Mapped[str] = mapped_column(String(255))
    IsActive: Mapped[bool] = mapped_column(Boolean, server_default=text('true'))
    BankID: Mapped[Optional[int]] = mapped_column(Integer)
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', foreign_keys=[BankID], back_populates='WhatsAppFlowMst')
    BankMst1: Mapped[Optional['BankMst']] = relationship('BankMst', foreign_keys=[BankMstID], back_populates='WhatsAppFlowMst_')
    WhatsAppFlowMapping: Mapped[List['WhatsAppFlowMapping']] = relationship('WhatsAppFlowMapping', back_populates='WhatsAppFlowMst_')


class WhatsAppKeyMapping(Base):
    __tablename__ = 'WhatsAppKeyMapping'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='WhatsAppKeyMapping_fk_BankID'),
        PrimaryKeyConstraint('WhatsAppKeyMappingID', name='WhatsAppKeyMapping_pkey')
    )

    WhatsAppKeyMappingID: Mapped[int] = mapped_column(Integer, primary_key=True)
    BankMstID: Mapped[int] = mapped_column(Integer)
    CreatedDate: Mapped[datetime.datetime] = mapped_column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    AuthorizationKey: Mapped[str] = mapped_column(String(255))
    APIVersion: Mapped[str] = mapped_column(String(50))
    PhoneNumberID: Mapped[str] = mapped_column(String)
    Service: Mapped[str] = mapped_column(String(255))
    Extra_Column1: Mapped[Optional[str]] = mapped_column(String(255))
    Extra_Column2: Mapped[Optional[str]] = mapped_column(String(255))
    Extra_Column3: Mapped[Optional[str]] = mapped_column(String(255))
    Extra_Column4: Mapped[Optional[str]] = mapped_column(String(255))
    Extra_Column5: Mapped[Optional[str]] = mapped_column(String(255))

    BankMst_: Mapped['BankMst'] = relationship('BankMst', back_populates='WhatsAppKeyMapping')


class AuthPermission(Base):
    __tablename__ = 'auth_permission'
    __table_args__ = (
        ForeignKeyConstraint(['content_type_id'], ['django_content_type.id'], deferrable=True, initially='DEFERRED', name='auth_permission_content_type_id_2f476e4b_fk_django_co'),
        PrimaryKeyConstraint('id', name='auth_permission_pkey'),
        UniqueConstraint('content_type_id', 'codename', name='auth_permission_content_type_id_codename_01ab375a_uniq'),
        Index('auth_permission_content_type_id_2f476e4b', 'content_type_id')
    )

    id: Mapped[int] = mapped_column(Integer, Identity(start=1, increment=1, minvalue=1, maxvalue=**********, cycle=False, cache=1), primary_key=True)
    name: Mapped[str] = mapped_column(String(255))
    content_type_id: Mapped[int] = mapped_column(Integer)
    codename: Mapped[str] = mapped_column(String(100))

    content_type: Mapped['DjangoContentType'] = relationship('DjangoContentType', back_populates='auth_permission')
    auth_group_permissions: Mapped[List['AuthGroupPermissions']] = relationship('AuthGroupPermissions', back_populates='permission')
    UserMst_user_permissions: Mapped[List['UserMstUserPermissions']] = relationship('UserMstUserPermissions', back_populates='permission')


class Bankcontrols(Base):
    __tablename__ = 'bankcontrols'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='bankcontrols_BankMstID_fkey'),
        PrimaryKeyConstraint('ControlID', name='bankcontrols_pkey'),
        UniqueConstraint('BankMstID', name='uk_bank_mst_id')
    )

    ControlID: Mapped[int] = mapped_column(Integer, primary_key=True)
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)
    Json: Mapped[Optional[dict]] = mapped_column(JSONB, server_default=text("'{}'::json"))

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='bankcontrols')


class BlasterTemplateMapping(Base):
    __tablename__ = 'BlasterTemplateMapping'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='BlasterTemplateMapping_BankMstID_fkey'),
        ForeignKeyConstraint(['LngMstID'], ['LanguageMst.LngMstID'], name='BlasterTemplateMapping_fk_LngMstID'),
        PrimaryKeyConstraint('BlasterTemplateMappingID', name='BlasterTemplateMapping_pkey')
    )

    BlasterTemplateMappingID: Mapped[int] = mapped_column(Integer, primary_key=True)
    Status: Mapped[str] = mapped_column(String(50))
    TemplateName: Mapped[str] = mapped_column(String(255))
    IsActive: Mapped[bool] = mapped_column(Boolean, server_default=text('true'))
    LngMstID: Mapped[int] = mapped_column(Integer)
    IsAdminCreated: Mapped[bool] = mapped_column(Boolean, server_default=text('false'))
    CreatedDate: Mapped[Optional[datetime.date]] = mapped_column(Date, server_default=text('CURRENT_DATE'))
    FileName: Mapped[Optional[str]] = mapped_column(String(255))
    RecordingURL: Mapped[Optional[str]] = mapped_column(String(255))
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)
    TemplateBody: Mapped[Optional[str]] = mapped_column(String)

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='BlasterTemplateMapping')
    LanguageMst_: Mapped['LanguageMst'] = relationship('LanguageMst', back_populates='BlasterTemplateMapping')
    BlasterUserTemplate: Mapped[List['BlasterUserTemplate']] = relationship('BlasterUserTemplate', back_populates='BlasterTemplateMapping_')


class BranchMst(Base):
    __tablename__ = 'BranchMst'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='BranchMst_BankMstID_fkey'),
        ForeignKeyConstraint(['LngMstID'], ['LanguageMst.LngMstID'], name='BranchMst_LngID_fkey'),
        PrimaryKeyConstraint('BranchMstID', name='BranchMst_pkey')
    )

    BranchMstID: Mapped[int] = mapped_column(Integer, primary_key=True)
    BankMstID: Mapped[int] = mapped_column(Integer)
    BranchName: Mapped[str] = mapped_column(String(255))
    BranchCode: Mapped[Optional[str]] = mapped_column(String(50))
    IsActive: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('true'))
    CreatedDate: Mapped[Optional[datetime.date]] = mapped_column(Date, server_default=text('CURRENT_DATE'))
    UpdatedDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    LngMstID: Mapped[Optional[int]] = mapped_column(Integer)
    Remarks: Mapped[Optional[str]] = mapped_column(Text)
    State: Mapped[Optional[str]] = mapped_column(String(100))
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))

    BankMst_: Mapped['BankMst'] = relationship('BankMst', back_populates='BranchMst')
    LanguageMst_: Mapped[Optional['LanguageMst']] = relationship('LanguageMst', back_populates='BranchMst')
    CustomerMst: Mapped[List['CustomerMst']] = relationship('CustomerMst', back_populates='BranchMst_')
    UserMst: Mapped[List['UserMst']] = relationship('UserMst', back_populates='BranchMstID')
    LoanMst: Mapped[List['LoanMst']] = relationship('LoanMst', back_populates='BranchMst_')
    BMAllocation: Mapped[List['BMAllocation']] = relationship('BMAllocation', back_populates='BranchMst_')
    BlasterHistory: Mapped[List['BlasterHistory']] = relationship('BlasterHistory', back_populates='BranchMst_')
    BlasterQueue: Mapped[List['BlasterQueue']] = relationship('BlasterQueue', back_populates='BranchMst_')
    CommunicationQueue: Mapped[List['CommunicationQueue']] = relationship('CommunicationQueue', back_populates='BranchMst_')
    Dialer: Mapped[List['Dialer']] = relationship('Dialer', back_populates='BranchMst_')
    IVRCallHistory: Mapped[List['IVRCallHistory']] = relationship('IVRCallHistory', back_populates='BranchMst_')
    IVRQueue: Mapped[List['IVRQueue']] = relationship('IVRQueue', back_populates='BranchMst_')
    Response: Mapped[List['Response']] = relationship('Response', back_populates='BranchMst_')
    SMSHistory: Mapped[List['SMSHistory']] = relationship('SMSHistory', back_populates='BranchMst_')
    SMSQueue: Mapped[List['SMSQueue']] = relationship('SMSQueue', back_populates='BranchMst_')
    SecondaryCustMst: Mapped[List['SecondaryCustMst']] = relationship('SecondaryCustMst', back_populates='BranchMst_')
    Transactions: Mapped[List['Transactions']] = relationship('Transactions', back_populates='BranchMst_')
    UserFeedback: Mapped[List['UserFeedback']] = relationship('UserFeedback', back_populates='BranchMst_')
    VoiceBotHistory: Mapped[List['VoiceBotHistory']] = relationship('VoiceBotHistory', back_populates='BranchMst_')
    VoiceBotQueue: Mapped[List['VoiceBotQueue']] = relationship('VoiceBotQueue', back_populates='BranchMst_')
    WhatsAppHistory: Mapped[List['WhatsAppHistory']] = relationship('WhatsAppHistory', back_populates='BranchMst_')
    WhatsAppQueue: Mapped[List['WhatsAppQueue']] = relationship('WhatsAppQueue', back_populates='BranchMst_')


class CollectionOfficerAllocation(Base):
    __tablename__ = 'CollectionOfficerAllocation'
    __table_args__ = (
        ForeignKeyConstraint(['LngMstID'], ['LanguageMst.LngMstID'], name='COAllocation_LngstID_fkey'),
        PrimaryKeyConstraint('CollectionOfficerAllocationID', name='CollectionOfficerAllocation_pkey')
    )

    CollectionOfficerAllocationID: Mapped[int] = mapped_column(Integer, primary_key=True)
    BankMstID: Mapped[int] = mapped_column(Integer)
    BranchMstID: Mapped[int] = mapped_column(Integer)
    CustomerMstID: Mapped[int] = mapped_column(Integer)
    CollectionOfficerID: Mapped[str] = mapped_column(String)
    CollectionOfficerName: Mapped[str] = mapped_column(String(255))
    LoanMstID: Mapped[Optional[int]] = mapped_column(Integer)
    CreatedDate: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime)
    ContactNumber: Mapped[Optional[str]] = mapped_column(String(15))
    BranchName: Mapped[Optional[str]] = mapped_column(String(255))
    LoanType: Mapped[Optional[str]] = mapped_column(String(100))
    IsAction: Mapped[Optional[bool]] = mapped_column(Boolean)
    LngMstID: Mapped[Optional[int]] = mapped_column(Integer)
    BasedOn: Mapped[Optional[str]] = mapped_column(String)
    Status: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('false'))

    LanguageMst_: Mapped[Optional['LanguageMst']] = relationship('LanguageMst', back_populates='CollectionOfficerAllocation')


class CommFlow(Base):
    __tablename__ = 'CommFlow'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='CommFlow_BankMstID_fkey'),
        ForeignKeyConstraint(['CampaignMstID'], ['CampaignMst.CampaignMstID'], name='CommFlow_FK_CampaignMst'),
        ForeignKeyConstraint(['CommFlowMstID'], ['CommFlowMst.CommFlowMstID'], name='CommFlow_fk_CommFlowMstID'),
        PrimaryKeyConstraint('CommFlowID', name='CommFlow_pkey')
    )

    CommFlowID: Mapped[int] = mapped_column(Integer, primary_key=True)
    CommunicationType: Mapped[str] = mapped_column(String(100))
    CommFlowMstID: Mapped[int] = mapped_column(Integer)
    Days: Mapped[str] = mapped_column(String)
    BeforeAfter: Mapped[str] = mapped_column(String(10))
    FlowID: Mapped[Optional[int]] = mapped_column(Integer)
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)
    BasedOn: Mapped[Optional[str]] = mapped_column(String)
    BasedOnTable: Mapped[Optional[str]] = mapped_column(String)
    TemplateID: Mapped[Optional[str]] = mapped_column(String)
    CampaignMstID: Mapped[Optional[int]] = mapped_column(Integer)

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='CommFlow')
    CampaignMst_: Mapped[Optional['CampaignMst']] = relationship('CampaignMst', back_populates='CommFlow')
    CommFlowMst_: Mapped['CommFlowMst'] = relationship('CommFlowMst', back_populates='CommFlow')
    FilterMst: Mapped[List['FilterMst']] = relationship('FilterMst', back_populates='CommFlow_')
    BlasterQueue: Mapped[List['BlasterQueue']] = relationship('BlasterQueue', back_populates='CommFlow_')
    IVRQueue: Mapped[List['IVRQueue']] = relationship('IVRQueue', back_populates='CommFlow_')
    SMSQueue: Mapped[List['SMSQueue']] = relationship('SMSQueue', back_populates='CommFlow_')
    VoiceBotQueue: Mapped[List['VoiceBotQueue']] = relationship('VoiceBotQueue', back_populates='CommFlow_')
    WhatsAppQueue: Mapped[List['WhatsAppQueue']] = relationship('WhatsAppQueue', back_populates='CommFlow_')


class EmployeeMst(Base):
    __tablename__ = 'EmployeeMst'
    __table_args__ = (
        ForeignKeyConstraint(['BUID'], ['GeographyMst.ID'], name='EmployeeMst_BUID_fkey'),
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='EmployeeMst_BankMstID_fkey'),
        ForeignKeyConstraint(['DesignationID'], ['DesignationMst.DesignationMstID'], name='EmployeeMst_DesignationID_fkey'),
        PrimaryKeyConstraint('EmployeeMstID', name='EmployeeMst_pkey')
    )

    EmployeeMstID: Mapped[int] = mapped_column(Integer, primary_key=True)
    BankMstID: Mapped[int] = mapped_column(Integer)
    UserCode: Mapped[str] = mapped_column(String(50))
    EmployeeName: Mapped[str] = mapped_column(String(255))
    Contact: Mapped[str] = mapped_column(String(20))
    DesignationID: Mapped[int] = mapped_column(Integer)
    BUID: Mapped[int] = mapped_column(Integer)
    IsActive: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('true'))
    UpdateDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    ReportingID: Mapped[Optional[int]] = mapped_column(Integer)
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))

    GeographyMst_: Mapped['GeographyMst'] = relationship('GeographyMst', back_populates='EmployeeMst')
    BankMst_: Mapped['BankMst'] = relationship('BankMst', back_populates='EmployeeMst')
    DesignationMst_: Mapped['DesignationMst'] = relationship('DesignationMst', back_populates='EmployeeMst')
    BlasterUserTemplate: Mapped[List['BlasterUserTemplate']] = relationship('BlasterUserTemplate', back_populates='EmployeeMst_')
    EmployeeAccess: Mapped[List['EmployeeAccess']] = relationship('EmployeeAccess', back_populates='EmployeeMst_')
    SMSUserTemplate: Mapped[List['SMSUserTemplate']] = relationship('SMSUserTemplate', back_populates='EmployeeMst_')
    VoiceBotUserTemplate: Mapped[List['VoiceBotUserTemplate']] = relationship('VoiceBotUserTemplate', back_populates='EmployeeMst_')
    WhatsAppUserTemplate: Mapped[List['WhatsAppUserTemplate']] = relationship('WhatsAppUserTemplate', back_populates='EmployeeMst_')


class IVRTemplateMapping(Base):
    __tablename__ = 'IVRTemplateMapping'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='fk_bankmstid'),
        ForeignKeyConstraint(['LngMstID'], ['LanguageMst.LngMstID'], name='fk_ivr_template'),
        PrimaryKeyConstraint('IVRTemplateMappingID', name='IVRTemplateMapping_pkey')
    )

    IVRTemplateMappingID: Mapped[int] = mapped_column(Integer, primary_key=True)
    CreatedDate: Mapped[datetime.datetime] = mapped_column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    Status: Mapped[str] = mapped_column(String(50))
    TemplateName: Mapped[str] = mapped_column(String(255))
    IsActive: Mapped[bool] = mapped_column(Boolean, server_default=text('true'))
    LngMstID: Mapped[int] = mapped_column(Integer)
    IsAdminCreated: Mapped[bool] = mapped_column(Boolean)
    FileName: Mapped[Optional[str]] = mapped_column(String(255))
    RecordingURL: Mapped[Optional[str]] = mapped_column(String(255))
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)
    TemplateBody: Mapped[Optional[str]] = mapped_column(Text)

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='IVRTemplateMapping')
    LanguageMst_: Mapped['LanguageMst'] = relationship('LanguageMst', back_populates='IVRTemplateMapping')
    IVRFlowMapping: Mapped[List['IVRFlowMapping']] = relationship('IVRFlowMapping', back_populates='IVRTemplateMapping_')
    IVRUserTemplate: Mapped[List['IVRUserTemplate']] = relationship('IVRUserTemplate', back_populates='IVRTemplateMapping_')
    IVRVariableMapping: Mapped[List['IVRVariableMapping']] = relationship('IVRVariableMapping', back_populates='IVRTemplateMapping_')


class PeriodicCommunication(Base):
    __tablename__ = 'PeriodicCommunication'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='PeriodicCommunication_BankMstID_fkey'),
        ForeignKeyConstraint(['CommFlowMstID'], ['CommFlowMst.CommFlowMstID'], name='PeriodicCommunication_fk_CommFlowMstID'),
        PrimaryKeyConstraint('PeriodicCommID', name='PeriodicCommunication_pkey')
    )

    PeriodicCommID: Mapped[int] = mapped_column(Integer, primary_key=True)
    StartDate: Mapped[datetime.date] = mapped_column(Date)
    StartTime: Mapped[datetime.time] = mapped_column(Time)
    RepeatFrequency: Mapped[str] = mapped_column(String(50))
    Interval: Mapped[int] = mapped_column(Integer)
    CommunicationType: Mapped[str] = mapped_column(String(100))
    CommFlowMstID: Mapped[int] = mapped_column(Integer)
    EndDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    EndTime: Mapped[Optional[datetime.time]] = mapped_column(Time)
    Until: Mapped[Optional[datetime.date]] = mapped_column(Date)
    ByWeekday: Mapped[Optional[str]] = mapped_column(String(50))
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='PeriodicCommunication')
    CommFlowMst_: Mapped['CommFlowMst'] = relationship('CommFlowMst', back_populates='PeriodicCommunication')


class SMSTemplateMapping(Base):
    __tablename__ = 'SMSTemplateMapping'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='SMSTemplateMapping_BankMstID_fkey'),
        ForeignKeyConstraint(['LngMstID'], ['LanguageMst.LngMstID'], name='SMSTemplateMapping_fk_LngMstID'),
        PrimaryKeyConstraint('SMSTemplateMappingID', name='SMSTemplateMapping_pkey')
    )

    SMSTemplateMappingID: Mapped[int] = mapped_column(Integer, primary_key=True)
    Status: Mapped[str] = mapped_column(String(50))
    TemplateName: Mapped[str] = mapped_column(String(255))
    IsActive: Mapped[bool] = mapped_column(Boolean, server_default=text('true'))
    LngMstID: Mapped[int] = mapped_column(Integer)
    FileName: Mapped[str] = mapped_column(String(255))
    IsAdminCreated: Mapped[bool] = mapped_column(Boolean)
    CreatedDate: Mapped[Optional[datetime.date]] = mapped_column(Date, server_default=text('CURRENT_DATE'))
    RecordingURL: Mapped[Optional[str]] = mapped_column(String(255))
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)
    TemplateBody: Mapped[Optional[str]] = mapped_column(String)

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='SMSTemplateMapping')
    LanguageMst_: Mapped['LanguageMst'] = relationship('LanguageMst', back_populates='SMSTemplateMapping')
    SMSUserTemplate: Mapped[List['SMSUserTemplate']] = relationship('SMSUserTemplate', back_populates='SMSTemplateMapping_')


class UserConversationsActiveConversations(Base):
    __tablename__ = 'UserConversations_active_conversations'
    __table_args__ = (
        ForeignKeyConstraint(['activeconversation_id'], ['ActiveConversation.uuid'], ondelete='CASCADE', name='UserConversations_active_conversatio_activeconversation_id_fkey'),
        ForeignKeyConstraint(['userconversations_id'], ['UserConversations.id'], ondelete='CASCADE', name='UserConversations_active_conversation_userconversations_id_fkey'),
        PrimaryKeyConstraint('id', name='UserConversations_active_conversations_pkey'),
        UniqueConstraint('userconversations_id', 'activeconversation_id', name='UserConversations_active_conv_userconversations_id_activeco_key')
    )

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    userconversations_id: Mapped[int] = mapped_column(Integer)
    activeconversation_id: Mapped[Uuid] = mapped_column(Uuid)

    activeconversation: Mapped['ActiveConversation'] = relationship('ActiveConversation', back_populates='UserConversations_active_conversations')
    userconversations: Mapped['UserConversations'] = relationship('UserConversations', back_populates='UserConversations_active_conversations')


class UserConversationsCompletedConversations(Base):
    __tablename__ = 'UserConversations_completed_conversations'
    __table_args__ = (
        ForeignKeyConstraint(['conversationhistory_id'], ['ConversationHistory.uuid'], ondelete='CASCADE', name='UserConversations_completed_convers_conversationhistory_id_fkey'),
        ForeignKeyConstraint(['userconversations_id'], ['UserConversations.id'], ondelete='CASCADE', name='UserConversations_completed_conversat_userconversations_id_fkey'),
        PrimaryKeyConstraint('id', name='UserConversations_completed_conversations_pkey'),
        UniqueConstraint('userconversations_id', 'conversationhistory_id', name='UserConversations_completed_c_userconversations_id_conversa_key')
    )

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    userconversations_id: Mapped[int] = mapped_column(Integer)
    conversationhistory_id: Mapped[Uuid] = mapped_column(Uuid)

    conversationhistory: Mapped['ConversationHistory'] = relationship('ConversationHistory', back_populates='UserConversations_completed_conversations')
    userconversations: Mapped['UserConversations'] = relationship('UserConversations', back_populates='UserConversations_completed_conversations')


class VoiceBotTemplateMapping(Base):
    __tablename__ = 'VoiceBotTemplateMapping'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='VoiceBotTemplateMapping_BankMstID_fkey'),
        ForeignKeyConstraint(['LngMstID'], ['LanguageMst.LngMstID'], name='VoiceBotTemplateMapping_fk_LngMstID'),
        PrimaryKeyConstraint('VoiceBotTemplateMappingID', name='VoiceBotTemplateMapping_pkey')
    )

    VoiceBotTemplateMappingID: Mapped[int] = mapped_column(Integer, primary_key=True)
    CreatedDate: Mapped[datetime.datetime] = mapped_column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    Status: Mapped[str] = mapped_column(String(50))
    TemplateName: Mapped[str] = mapped_column(String(255))
    IsActive: Mapped[bool] = mapped_column(Boolean, server_default=text('true'))
    LngMstID: Mapped[int] = mapped_column(Integer)
    FileName: Mapped[Optional[str]] = mapped_column(String(255))
    IsAdminCreated: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('false'))
    RecordingURL: Mapped[Optional[str]] = mapped_column(String(255))
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)
    TemplateBody: Mapped[Optional[str]] = mapped_column(String)
    StartMsg: Mapped[Optional[str]] = mapped_column(String)
    EndMsg: Mapped[Optional[str]] = mapped_column(String)
    SystemInstructions: Mapped[Optional[str]] = mapped_column(String)

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='VoiceBotTemplateMapping')
    LanguageMst_: Mapped['LanguageMst'] = relationship('LanguageMst', back_populates='VoiceBotTemplateMapping')
    VoiceBotUserTemplate: Mapped[List['VoiceBotUserTemplate']] = relationship('VoiceBotUserTemplate', back_populates='VoiceBotTemplateMapping_')


class WhatsAppTemplateMapping(Base):
    __tablename__ = 'WhatsAppTemplateMapping'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='WhatsAppTemplateMapping_BankMstID_fkey'),
        ForeignKeyConstraint(['LngMstID'], ['LanguageMst.LngMstID'], name='WhatsAppTemplateMapping_fk_LanguageID'),
        PrimaryKeyConstraint('WhatsAppTemplateMappingID', name='WhatsAppTemplateMapping_pkey')
    )

    WhatsAppTemplateMappingID: Mapped[int] = mapped_column(Integer, primary_key=True)
    CreatedDate: Mapped[datetime.datetime] = mapped_column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    Status: Mapped[str] = mapped_column(String(50))
    TemplateName: Mapped[str] = mapped_column(String(255))
    IsActive: Mapped[bool] = mapped_column(Boolean, server_default=text('true'))
    LngMstID: Mapped[int] = mapped_column(Integer)
    FileName: Mapped[str] = mapped_column(String(255))
    IsAdminCreated: Mapped[bool] = mapped_column(Boolean)
    MetaTemplateID: Mapped[Optional[str]] = mapped_column(String(255))
    Extra_Column1: Mapped[Optional[str]] = mapped_column(String(255))
    Extra_Column2: Mapped[Optional[str]] = mapped_column(String(255))
    Extra_Column3: Mapped[Optional[str]] = mapped_column(String(255))
    Extra_Column4: Mapped[Optional[str]] = mapped_column(String(255))
    Extra_Column5: Mapped[Optional[str]] = mapped_column(String(255))
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)
    TemplateBody: Mapped[Optional[str]] = mapped_column(String)

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='WhatsAppTemplateMapping')
    LanguageMst_: Mapped['LanguageMst'] = relationship('LanguageMst', back_populates='WhatsAppTemplateMapping')
    WhatsAppFlowMapping: Mapped[List['WhatsAppFlowMapping']] = relationship('WhatsAppFlowMapping', back_populates='WhatsAppTemplateMapping_')
    WhatsAppUserTemplate: Mapped[List['WhatsAppUserTemplate']] = relationship('WhatsAppUserTemplate', back_populates='WhatsAppTemplateMapping_')
    WhatsappVariableMapping: Mapped[List['WhatsappVariableMapping']] = relationship('WhatsappVariableMapping', back_populates='WhatsAppTemplateMapping_')


class AuthGroupPermissions(Base):
    __tablename__ = 'auth_group_permissions'
    __table_args__ = (
        ForeignKeyConstraint(['group_id'], ['auth_group.id'], deferrable=True, initially='DEFERRED', name='auth_group_permissions_group_id_b120cbf9_fk_auth_group_id'),
        ForeignKeyConstraint(['permission_id'], ['auth_permission.id'], deferrable=True, initially='DEFERRED', name='auth_group_permissio_permission_id_84c5c92e_fk_auth_perm'),
        PrimaryKeyConstraint('id', name='auth_group_permissions_pkey'),
        UniqueConstraint('group_id', 'permission_id', name='auth_group_permissions_group_id_permission_id_0cd325b0_uniq'),
        Index('auth_group_permissions_group_id_b120cbf9', 'group_id'),
        Index('auth_group_permissions_permission_id_84c5c92e', 'permission_id')
    )

    id: Mapped[int] = mapped_column(BigInteger, Identity(start=1, increment=1, minvalue=1, maxvalue=9223***************, cycle=False, cache=1), primary_key=True)
    group_id: Mapped[int] = mapped_column(Integer)
    permission_id: Mapped[int] = mapped_column(Integer)

    group: Mapped['AuthGroup'] = relationship('AuthGroup', back_populates='auth_group_permissions')
    permission: Mapped['AuthPermission'] = relationship('AuthPermission', back_populates='auth_group_permissions')


class BlasterUserTemplate(Base):
    __tablename__ = 'BlasterUserTemplate'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='BlasterUserTemplate_fk_BankMstID'),
        ForeignKeyConstraint(['BlasterTemplateMappingID'], ['BlasterTemplateMapping.BlasterTemplateMappingID'], ondelete='SET NULL', name='fk_voicebot_template'),
        ForeignKeyConstraint(['EmployeeMstID'], ['EmployeeMst.EmployeeMstID'], name='BlasterUserTemplate_fk_EmployeeID'),
        PrimaryKeyConstraint('BlasterUserTemplateID', name='BlasterUserTemplate_pkey')
    )

    BlasterUserTemplateID: Mapped[int] = mapped_column(Integer, primary_key=True)
    TemplateName: Mapped[str] = mapped_column(String(255))
    BankMstID: Mapped[int] = mapped_column(Integer)
    VariableNumber: Mapped[int] = mapped_column(Integer)
    VariableField: Mapped[str] = mapped_column(String(255))
    TemplateBody: Mapped[Optional[str]] = mapped_column(Text)
    EmployeeMstID: Mapped[Optional[int]] = mapped_column(Integer)
    BlasterTemplateMappingID: Mapped[Optional[int]] = mapped_column(Integer)

    BankMst_: Mapped['BankMst'] = relationship('BankMst', back_populates='BlasterUserTemplate')
    BlasterTemplateMapping_: Mapped[Optional['BlasterTemplateMapping']] = relationship('BlasterTemplateMapping', back_populates='BlasterUserTemplate')
    EmployeeMst_: Mapped[Optional['EmployeeMst']] = relationship('EmployeeMst', back_populates='BlasterUserTemplate')


class CustomerMst(Base):
    __tablename__ = 'CustomerMst'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='CustomerMst_BankMstID_fkey'),
        ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='CustomerMst_BranchMstID_fkey'),
        PrimaryKeyConstraint('CustomerMstID', name='CustomerMst_pkey')
    )

    CustomerMstID: Mapped[int] = mapped_column(Integer, primary_key=True)
    BranchMstID: Mapped[int] = mapped_column(Integer)
    CustomerID: Mapped[int] = mapped_column(Integer)
    CustomerName: Mapped[str] = mapped_column(String(255))
    CustomerCode: Mapped[Optional[str]] = mapped_column(String(50))
    MobileNumber: Mapped[Optional[str]] = mapped_column(String(15))
    Gender: Mapped[Optional[str]] = mapped_column(String(10))
    DateOfBirth: Mapped[Optional[datetime.date]] = mapped_column(Date)
    IsDeath: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('false'))
    DeathDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    CreatedDate: Mapped[Optional[datetime.date]] = mapped_column(Date, server_default=text('CURRENT_DATE'))
    UpdatedDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    EmailID: Mapped[Optional[str]] = mapped_column(String(255))
    Latitude: Mapped[Optional[str]] = mapped_column(String(255))
    Longitude: Mapped[Optional[str]] = mapped_column(String(255))
    PanNo: Mapped[Optional[str]] = mapped_column(String(20))
    Pincode: Mapped[Optional[str]] = mapped_column(String(6))
    CustomerAddress: Mapped[Optional[str]] = mapped_column(Text)
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='CustomerMst')
    BranchMst_: Mapped['BranchMst'] = relationship('BranchMst', back_populates='CustomerMst')
    LoanMst: Mapped[List['LoanMst']] = relationship('LoanMst', back_populates='CustomerMst_')
    BMAllocation: Mapped[List['BMAllocation']] = relationship('BMAllocation', back_populates='CustomerMst_')
    LoanInstallmentMst: Mapped[List['LoanInstallmentMst']] = relationship('LoanInstallmentMst', back_populates='CustomerMst_')


class EmployeeAccess(Base):
    __tablename__ = 'EmployeeAccess'
    __table_args__ = (
        ForeignKeyConstraint(['BUID'], ['GeographyMst.ID'], name='EmployeeAccess_BUID_fkey'),
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='EmployeeAccess_BankMstID_fkey'),
        ForeignKeyConstraint(['DesignationID'], ['DesignationMst.DesignationMstID'], name='EmployeeAccess_DesignationID_fkey'),
        ForeignKeyConstraint(['EmployeeMstID'], ['EmployeeMst.EmployeeMstID'], name='EmployeeAccess_EmployeeID_fkey'),
        PrimaryKeyConstraint('EmployeeAccessID', name='EmployeeAccess_pkey')
    )

    EmployeeAccessID: Mapped[int] = mapped_column(Integer, primary_key=True)
    EmployeeMstID: Mapped[int] = mapped_column(Integer)
    BUID: Mapped[int] = mapped_column(Integer)
    DesignationID: Mapped[int] = mapped_column(Integer)
    IsActive: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('true'))
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)

    GeographyMst_: Mapped['GeographyMst'] = relationship('GeographyMst', back_populates='EmployeeAccess')
    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='EmployeeAccess')
    DesignationMst_: Mapped['DesignationMst'] = relationship('DesignationMst', back_populates='EmployeeAccess')
    EmployeeMst_: Mapped['EmployeeMst'] = relationship('EmployeeMst', back_populates='EmployeeAccess')


class FilterMst(Base):
    __tablename__ = 'FilterMst'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], ondelete='CASCADE', name='FilterMst_fk_BankID'),
        ForeignKeyConstraint(['CampaignMstID'], ['CampaignMst.CampaignMstID'], name='FilterMst_CampaignMstID_fkey'),
        ForeignKeyConstraint(['CommFlowID'], ['CommFlow.CommFlowID'], ondelete='CASCADE', name='FilterMst_fk_CommFlow'),
        ForeignKeyConstraint(['CommFlowMstID'], ['CommFlowMst.CommFlowMstID'], ondelete='CASCADE', name='fk_commflowmst'),
        ForeignKeyConstraint(['FiltersID'], ['Filters.FiltersID'], name='FilterMst_FiltersID_fkey'),
        PrimaryKeyConstraint('ID', name='FilterMst_pkey')
    )

    ID: Mapped[int] = mapped_column(Integer, primary_key=True)
    Name: Mapped[str] = mapped_column(String(255))
    IsActive: Mapped[bool] = mapped_column(Boolean, server_default=text('true'))
    BankMstID: Mapped[int] = mapped_column(Integer)
    CommFlowID: Mapped[Optional[int]] = mapped_column(Integer)
    FiltersID: Mapped[Optional[int]] = mapped_column(Integer)
    CampaignMstID: Mapped[Optional[int]] = mapped_column(Integer)
    CommFlowMstID: Mapped[Optional[int]] = mapped_column(Integer)

    BankMst_: Mapped['BankMst'] = relationship('BankMst', back_populates='FilterMst')
    CampaignMst_: Mapped[Optional['CampaignMst']] = relationship('CampaignMst', back_populates='FilterMst')
    CommFlow_: Mapped[Optional['CommFlow']] = relationship('CommFlow', back_populates='FilterMst')
    CommFlowMst_: Mapped[Optional['CommFlowMst']] = relationship('CommFlowMst', back_populates='FilterMst')
    Filters_: Mapped[Optional['Filters']] = relationship('Filters', back_populates='FilterMst')


class IVRFlowMapping(Base):
    __tablename__ = 'IVRFlowMapping'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='IVRFlowmapping_fk_BankMstID'),
        ForeignKeyConstraint(['IVRFlowMstID'], ['IVRFlowMst.IVRFlowMstID'], name='IVRFlowmapping_fk_IVRFlowMstID'),
        ForeignKeyConstraint(['IVRTemplateMappingID'], ['IVRTemplateMapping.IVRTemplateMappingID'], name='IVRFlowMapping_fk_IVRTemplateMappingID'),
        ForeignKeyConstraint(['LngMstID'], ['LanguageMst.LngMstID'], name='fk_ivr_template'),
        PrimaryKeyConstraint('IVRFlowID', name='IVRFlowMapping_pkey')
    )

    IVRFlowID: Mapped[int] = mapped_column(Integer, primary_key=True)
    FlowID: Mapped[int] = mapped_column(Integer)
    CreatedDate: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime)
    FlowName: Mapped[Optional[str]] = mapped_column(String(255))
    Response: Mapped[Optional[str]] = mapped_column(Text)
    IsActive: Mapped[Optional[bool]] = mapped_column(Boolean)
    LngMstID: Mapped[Optional[int]] = mapped_column(Integer)
    IVRTemplateMappingID: Mapped[Optional[int]] = mapped_column(Integer)
    IVRFlowMstID: Mapped[Optional[int]] = mapped_column(Integer)
    IsStart: Mapped[Optional[bool]] = mapped_column(Boolean)
    ButtonPressed: Mapped[Optional[str]] = mapped_column(String(50))
    NextTemplate: Mapped[Optional[int]] = mapped_column(Integer)
    CurrentTemplate: Mapped[Optional[int]] = mapped_column(Integer)
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='IVRFlowMapping')
    IVRFlowMst_: Mapped[Optional['IVRFlowMst']] = relationship('IVRFlowMst', back_populates='IVRFlowMapping')
    IVRTemplateMapping_: Mapped[Optional['IVRTemplateMapping']] = relationship('IVRTemplateMapping', back_populates='IVRFlowMapping')
    LanguageMst_: Mapped[Optional['LanguageMst']] = relationship('LanguageMst', back_populates='IVRFlowMapping')


class IVRUserTemplate(Base):
    __tablename__ = 'IVRUserTemplate'
    __table_args__ = (
        ForeignKeyConstraint(['IVRTemplateMappingID'], ['IVRTemplateMapping.IVRTemplateMappingID'], name='fk_ivr_template_mapping'),
        PrimaryKeyConstraint('IVRUserTemplateID', name='IVRUserTemplate_pkey')
    )

    IVRUserTemplateID: Mapped[int] = mapped_column(Integer, primary_key=True)
    TemplateName: Mapped[str] = mapped_column(String(255))
    BankMstID: Mapped[int] = mapped_column(Integer)
    EmployeeMstID: Mapped[Optional[int]] = mapped_column(Integer)
    VariableNumber: Mapped[Optional[int]] = mapped_column(Integer)
    VariableField: Mapped[Optional[str]] = mapped_column(String(255))
    IVRTemplateMappingID: Mapped[Optional[int]] = mapped_column(Integer)

    IVRTemplateMapping_: Mapped[Optional['IVRTemplateMapping']] = relationship('IVRTemplateMapping', back_populates='IVRUserTemplate')


class IVRVariableMapping(Base):
    __tablename__ = 'IVRVariableMapping'
    __table_args__ = (
        ForeignKeyConstraint(['IVRTemplateMappingID'], ['IVRTemplateMapping.IVRTemplateMappingID'], name='fk_ivr_template'),
        PrimaryKeyConstraint('IVRVariableMappingID', name='IVRVariableMapping_pkey')
    )

    IVRVariableMappingID: Mapped[int] = mapped_column(Integer, primary_key=True)
    IVRTemplateMappingID: Mapped[Optional[int]] = mapped_column(Integer)
    VariableNo: Mapped[Optional[int]] = mapped_column(Integer)
    VariableField: Mapped[Optional[str]] = mapped_column(String)

    IVRTemplateMapping_: Mapped[Optional['IVRTemplateMapping']] = relationship('IVRTemplateMapping', back_populates='IVRVariableMapping')


class SMSUserTemplate(Base):
    __tablename__ = 'SMSUserTemplate'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='SMSUserTemplate_fk_BankMstID'),
        ForeignKeyConstraint(['EmployeeMstID'], ['EmployeeMst.EmployeeMstID'], name='SMSUserTemplate_fk_EmployeeID'),
        ForeignKeyConstraint(['SMSTemplateMappingID'], ['SMSTemplateMapping.SMSTemplateMappingID'], name='SMSTemplateMapping_fk_SMSTemplateMappingID'),
        PrimaryKeyConstraint('SMSUserTemplateID', name='SMSUserTemplate_pkey')
    )

    SMSUserTemplateID: Mapped[int] = mapped_column(Integer, primary_key=True)
    TemplateName: Mapped[str] = mapped_column(String(255))
    BankMstID: Mapped[int] = mapped_column(Integer)
    VariableNumber: Mapped[int] = mapped_column(Integer)
    VariableField: Mapped[str] = mapped_column(String(255))
    EmployeeMstID: Mapped[Optional[int]] = mapped_column(Integer)
    SMSTemplateMappingID: Mapped[Optional[int]] = mapped_column(Integer)

    BankMst_: Mapped['BankMst'] = relationship('BankMst', back_populates='SMSUserTemplate')
    EmployeeMst_: Mapped[Optional['EmployeeMst']] = relationship('EmployeeMst', back_populates='SMSUserTemplate')
    SMSTemplateMapping_: Mapped[Optional['SMSTemplateMapping']] = relationship('SMSTemplateMapping', back_populates='SMSUserTemplate')


class UserMst(Base):
    __tablename__ = 'UserMst'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID_id'], ['BankMst.BankMstID'], deferrable=True, initially='DEFERRED', name='UserMst_BankMstID_id_4b4302a0_fk_BankMst_BankMstID'),
        ForeignKeyConstraint(['BranchMstID_id'], ['BranchMst.BranchMstID'], deferrable=True, initially='DEFERRED', name='UserMst_BranchMstID_id_224814fd_fk_BranchMst_BranchMstID'),
        PrimaryKeyConstraint('UserID', name='UserMst_pkey'),
        UniqueConstraint('username', name='UserMst_username_key'),
        Index('UserMst_BankMstID_id_4b4302a0', 'BankMstID_id'),
        Index('UserMst_BranchMstID_id_224814fd', 'BranchMstID_id'),
        Index('UserMst_username_219fb066_like', 'username')
    )

    password: Mapped[str] = mapped_column(String(128))
    is_superuser: Mapped[bool] = mapped_column(Boolean)
    username: Mapped[str] = mapped_column(String(150))
    first_name: Mapped[str] = mapped_column(String(150))
    last_name: Mapped[str] = mapped_column(String(150))
    email: Mapped[str] = mapped_column(String(254))
    is_staff: Mapped[bool] = mapped_column(Boolean)
    is_active: Mapped[bool] = mapped_column(Boolean)
    date_joined: Mapped[datetime.datetime] = mapped_column(DateTime(True))
    UserID: Mapped[int] = mapped_column(Integer, Identity(start=1, increment=1, minvalue=1, maxvalue=**********, cycle=False, cache=1), primary_key=True)
    is_admin: Mapped[bool] = mapped_column(Boolean)
    BankMaster: Mapped[bool] = mapped_column(Boolean)
    UpdatedDate: Mapped[datetime.datetime] = mapped_column(DateTime(True))
    last_login: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime(True))
    BranchID: Mapped[Optional[str]] = mapped_column(String(50))
    Designation: Mapped[Optional[str]] = mapped_column(String(50))
    MobileNumber: Mapped[Optional[str]] = mapped_column(String(50))
    ExtraColumn_1: Mapped[Optional[str]] = mapped_column(Text)
    ExtraColumn_2: Mapped[Optional[str]] = mapped_column(Text)
    ExtraColumn_3: Mapped[Optional[str]] = mapped_column(Text)
    ExtraColumn_4: Mapped[Optional[str]] = mapped_column(Text)
    ExtraColumn_5: Mapped[Optional[str]] = mapped_column(Text)
    BankMstID_id: Mapped[Optional[int]] = mapped_column(Integer)
    BranchMstID_id: Mapped[Optional[int]] = mapped_column(Integer)
    FO_id: Mapped[Optional[int]] = mapped_column(Integer)

    BankMstID: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='UserMst')
    BranchMstID: Mapped[Optional['BranchMst']] = relationship('BranchMst', back_populates='UserMst')
    LoginHistory: Mapped[List['LoginHistory']] = relationship('LoginHistory', back_populates='user')
    UserMst_groups: Mapped[List['UserMstGroups']] = relationship('UserMstGroups', back_populates='usermst')
    UserMst_user_permissions: Mapped[List['UserMstUserPermissions']] = relationship('UserMstUserPermissions', back_populates='usermst')
    django_admin_log: Mapped[List['DjangoAdminLog']] = relationship('DjangoAdminLog', back_populates='user')


class VoiceBotUserTemplate(Base):
    __tablename__ = 'VoiceBotUserTemplate'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='VoiceBotUserTemplate_fk_BankMstID'),
        ForeignKeyConstraint(['EmployeeMstID'], ['EmployeeMst.EmployeeMstID'], name='VoiceBotUserTemplate_fk_EmployeeID'),
        ForeignKeyConstraint(['VoiceBotTemplateMappingID'], ['VoiceBotTemplateMapping.VoiceBotTemplateMappingID'], ondelete='SET NULL', name='fk_voicebot_template'),
        PrimaryKeyConstraint('VoiceBotUserTemplateID', name='VoiceBotUserTemplate_pkey')
    )

    VoiceBotUserTemplateID: Mapped[int] = mapped_column(Integer, primary_key=True)
    TemplateName: Mapped[str] = mapped_column(String(255))
    BankMstID: Mapped[int] = mapped_column(Integer)
    VariableNumber: Mapped[int] = mapped_column(Integer)
    VariableField: Mapped[str] = mapped_column(String(255))
    EmployeeMstID: Mapped[Optional[int]] = mapped_column(Integer)
    VoiceBotTemplateMappingID: Mapped[Optional[int]] = mapped_column(Integer)
    StartEnd: Mapped[Optional[str]] = mapped_column(String)

    BankMst_: Mapped['BankMst'] = relationship('BankMst', back_populates='VoiceBotUserTemplate')
    EmployeeMst_: Mapped[Optional['EmployeeMst']] = relationship('EmployeeMst', back_populates='VoiceBotUserTemplate')
    VoiceBotTemplateMapping_: Mapped[Optional['VoiceBotTemplateMapping']] = relationship('VoiceBotTemplateMapping', back_populates='VoiceBotUserTemplate')


class WhatsAppFlowMapping(Base):
    __tablename__ = 'WhatsAppFlowMapping'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='WhatsAppFlowMapping_BankMstID_fkey'),
        ForeignKeyConstraint(['LngMstID'], ['LanguageMst.LngMstID'], name='WhatsAppFlowMapping_fk_LanguageID'),
        ForeignKeyConstraint(['WhatsAppFlowMstID'], ['WhatsAppFlowMst.WhatsAppFlowMstID'], name='WhatsAppFlowMapping_fk_WhatsAppFlowMstID'),
        ForeignKeyConstraint(['WhatsAppTemplateMappingID'], ['WhatsAppTemplateMapping.WhatsAppTemplateMappingID'], name='WhatsAppFlowMapping_fk_WhatsAppTemplateMappingID'),
        PrimaryKeyConstraint('WhatsAppFlowID', name='WhatsAppFlowMapping_pkey')
    )

    WhatsAppFlowID: Mapped[int] = mapped_column(Integer, primary_key=True)
    CreatedDate: Mapped[datetime.datetime] = mapped_column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    FlowID: Mapped[int] = mapped_column(Integer)
    FlowName: Mapped[str] = mapped_column(String(255))
    Response: Mapped[str] = mapped_column(Text)
    IsActive: Mapped[bool] = mapped_column(Boolean, server_default=text('true'))
    LngMstID: Mapped[int] = mapped_column(Integer)
    WhatsAppTemplateMappingID: Mapped[int] = mapped_column(Integer)
    WhatsAppFlowMstID: Mapped[int] = mapped_column(Integer)
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)
    IsStart: Mapped[Optional[bool]] = mapped_column(Boolean)
    NextTemplate: Mapped[Optional[str]] = mapped_column(String)

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='WhatsAppFlowMapping')
    LanguageMst_: Mapped['LanguageMst'] = relationship('LanguageMst', back_populates='WhatsAppFlowMapping')
    WhatsAppFlowMst_: Mapped['WhatsAppFlowMst'] = relationship('WhatsAppFlowMst', back_populates='WhatsAppFlowMapping')
    WhatsAppTemplateMapping_: Mapped['WhatsAppTemplateMapping'] = relationship('WhatsAppTemplateMapping', back_populates='WhatsAppFlowMapping')


class WhatsAppUserTemplate(Base):
    __tablename__ = 'WhatsAppUserTemplate'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='WhatsAppUserTemplate_fk_BankID'),
        ForeignKeyConstraint(['EmployeeMstID'], ['EmployeeMst.EmployeeMstID'], name='WhatsAppUserTemplate_fk_EmployeeID'),
        ForeignKeyConstraint(['WhatsAppTemplateMappingID'], ['WhatsAppTemplateMapping.WhatsAppTemplateMappingID'], name='fk_whatsapp_template'),
        PrimaryKeyConstraint('WhatsAppUserTemplateID', name='WhatsAppUserTemplate_pkey')
    )

    WhatsAppUserTemplateID: Mapped[int] = mapped_column(Integer, primary_key=True)
    TemplateName: Mapped[str] = mapped_column(String(255))
    BankMstID: Mapped[int] = mapped_column(Integer)
    EmployeeMstID: Mapped[Optional[int]] = mapped_column(Integer)
    Variable1: Mapped[Optional[str]] = mapped_column(String(255))
    Variable2: Mapped[Optional[str]] = mapped_column(String(255))
    Variable3: Mapped[Optional[str]] = mapped_column(String(255))
    Variable4: Mapped[Optional[str]] = mapped_column(String(255))
    Variable5: Mapped[Optional[str]] = mapped_column(String(255))
    Variable6: Mapped[Optional[str]] = mapped_column(String(255))
    Variable7: Mapped[Optional[str]] = mapped_column(String(255))
    Variable8: Mapped[Optional[str]] = mapped_column(String(255))
    Variable9: Mapped[Optional[str]] = mapped_column(String(255))
    Variable10: Mapped[Optional[str]] = mapped_column(String(255))
    Button1: Mapped[Optional[str]] = mapped_column(String)
    Button2: Mapped[Optional[str]] = mapped_column(String)
    Button3: Mapped[Optional[str]] = mapped_column(String)
    Button4: Mapped[Optional[str]] = mapped_column(String)
    WhatsAppTemplateMappingID: Mapped[Optional[int]] = mapped_column(Integer)
    Response1: Mapped[Optional[str]] = mapped_column(String)
    Response2: Mapped[Optional[str]] = mapped_column(String)
    Response3: Mapped[Optional[str]] = mapped_column(String)
    Response4: Mapped[Optional[str]] = mapped_column(String)

    BankMst_: Mapped['BankMst'] = relationship('BankMst', back_populates='WhatsAppUserTemplate')
    EmployeeMst_: Mapped[Optional['EmployeeMst']] = relationship('EmployeeMst', back_populates='WhatsAppUserTemplate')
    WhatsAppTemplateMapping_: Mapped[Optional['WhatsAppTemplateMapping']] = relationship('WhatsAppTemplateMapping', back_populates='WhatsAppUserTemplate')


class WhatsappVariableMapping(Base):
    __tablename__ = 'WhatsappVariableMapping'
    __table_args__ = (
        ForeignKeyConstraint(['WhatsAppTemplateMappingID'], ['WhatsAppTemplateMapping.WhatsAppTemplateMappingID'], name='fk_whatsapp_template'),
        PrimaryKeyConstraint('WhatsappVariableMappingID', name='WhatsappVariableMapping_pkey')
    )

    WhatsappVariableMappingID: Mapped[int] = mapped_column(Integer, primary_key=True)
    WhatsAppTemplateMappingID: Mapped[Optional[int]] = mapped_column(Integer)
    VariableNo: Mapped[Optional[int]] = mapped_column(Integer)
    VariableField: Mapped[Optional[str]] = mapped_column(String)

    WhatsAppTemplateMapping_: Mapped[Optional['WhatsAppTemplateMapping']] = relationship('WhatsAppTemplateMapping', back_populates='WhatsappVariableMapping')


class LoanMst(Base):
    __tablename__ = 'LoanMst'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], ondelete='SET NULL', name='fk_LoanMst_BankMstID'),
        ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='LoanMst_BranchMstID_fkey'),
        ForeignKeyConstraint(['CustomerMstID'], ['CustomerMst.CustomerMstID'], name='LoanMst_CustomerMstID_fkey'),
        PrimaryKeyConstraint('LoanMstID', name='LoanMst_pkey'),
        UniqueConstraint('DisbursementID', 'BranchMstID', 'CustomerMstID', name='unique_disbursement_combined')
    )

    LoanMstID: Mapped[int] = mapped_column(Integer, primary_key=True)
    CustomerMstID: Mapped[int] = mapped_column(Integer)
    BranchMstID: Mapped[int] = mapped_column(Integer)
    BankMstID: Mapped[int] = mapped_column(Integer)
    DisbursementID: Mapped[Optional[str]] = mapped_column(String(255))
    DisbursementDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    DisbursementAmt: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    LoanType: Mapped[Optional[str]] = mapped_column(String(50))
    ClosingDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    InstallmentStartDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    RateOfInterest: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(5, 2))
    RepaymentTenure: Mapped[Optional[int]] = mapped_column(Integer)
    EMIAmount: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    PaymentFrequency: Mapped[Optional[int]] = mapped_column(Integer)
    LastModifiedDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    IsActive: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('true'))
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))
    BMID: Mapped[Optional[int]] = mapped_column(Integer)
    CollectionOfficerID: Mapped[Optional[str]] = mapped_column(String)
    CollectionOfficerName: Mapped[Optional[str]] = mapped_column(String)
    DND: Mapped[Optional[bool]] = mapped_column(Boolean)
    groupid: Mapped[Optional[int]] = mapped_column(Integer)
    groupname: Mapped[Optional[str]] = mapped_column(String)

    BankMst_: Mapped['BankMst'] = relationship('BankMst', back_populates='LoanMst')
    BranchMst_: Mapped['BranchMst'] = relationship('BranchMst', back_populates='LoanMst')
    CustomerMst_: Mapped['CustomerMst'] = relationship('CustomerMst', back_populates='LoanMst')
    BMAllocation: Mapped[List['BMAllocation']] = relationship('BMAllocation', back_populates='LoanMst_')
    BlasterHistory: Mapped[List['BlasterHistory']] = relationship('BlasterHistory', back_populates='LoanMst_')
    BlasterQueue: Mapped[List['BlasterQueue']] = relationship('BlasterQueue', back_populates='LoanMst_')
    CommunicationQueue: Mapped[List['CommunicationQueue']] = relationship('CommunicationQueue', back_populates='LoanMst_')
    DNDHistory: Mapped[List['DNDHistory']] = relationship('DNDHistory', back_populates='LoanMst_')
    Dialer: Mapped[List['Dialer']] = relationship('Dialer', back_populates='LoanMst_')
    IVRCallHistory: Mapped[List['IVRCallHistory']] = relationship('IVRCallHistory', back_populates='LoanMst_')
    IVRQueue: Mapped[List['IVRQueue']] = relationship('IVRQueue', back_populates='LoanMst_')
    LoanInstallmentMst: Mapped[List['LoanInstallmentMst']] = relationship('LoanInstallmentMst', back_populates='LoanMst_')
    Response: Mapped[List['Response']] = relationship('Response', back_populates='LoanMst_')
    SMSHistory: Mapped[List['SMSHistory']] = relationship('SMSHistory', back_populates='LoanMst_')
    SMSQueue: Mapped[List['SMSQueue']] = relationship('SMSQueue', back_populates='LoanMst_')
    SecondaryCustMst: Mapped[List['SecondaryCustMst']] = relationship('SecondaryCustMst', back_populates='LoanMst_')
    Transactions: Mapped[List['Transactions']] = relationship('Transactions', back_populates='LoanMst_')
    UserFeedback: Mapped[List['UserFeedback']] = relationship('UserFeedback', back_populates='LoanMst_')
    VoiceBotHistory: Mapped[List['VoiceBotHistory']] = relationship('VoiceBotHistory', back_populates='LoanMst_')
    VoiceBotQueue: Mapped[List['VoiceBotQueue']] = relationship('VoiceBotQueue', back_populates='LoanMst_')
    WhatsAppHistory: Mapped[List['WhatsAppHistory']] = relationship('WhatsAppHistory', back_populates='LoanMst_')
    WhatsAppQueue: Mapped[List['WhatsAppQueue']] = relationship('WhatsAppQueue', back_populates='LoanMst_')
    WhatsApp_Messages: Mapped[List['WhatsAppMessages']] = relationship('WhatsAppMessages', back_populates='LoanMst_')
    CoAppMst: Mapped[List['CoAppMst']] = relationship('CoAppMst', back_populates='LoanMst_')


class LoginHistory(Base):
    __tablename__ = 'LoginHistory'
    __table_args__ = (
        ForeignKeyConstraint(['user_id'], ['UserMst.UserID'], deferrable=True, initially='DEFERRED', name='LoginHistory_user_id_b2cfd3c6_fk_UserMst_UserID'),
        PrimaryKeyConstraint('id', name='LoginHistory_pkey'),
        Index('LoginHistory_ip_19de82e3', 'ip'),
        Index('LoginHistory_user_id_b2cfd3c6', 'user_id')
    )

    id: Mapped[int] = mapped_column(BigInteger, Identity(start=1, increment=1, minvalue=1, maxvalue=9223***************, cycle=False, cache=1), primary_key=True)
    ip: Mapped[Any] = mapped_column(INET)
    ip_info: Mapped[dict] = mapped_column(JSONB)
    city: Mapped[str] = mapped_column(String(80))
    region: Mapped[str] = mapped_column(String(80))
    region_code: Mapped[str] = mapped_column(String(10))
    country_code: Mapped[str] = mapped_column(String(2))
    country_name: Mapped[str] = mapped_column(String(80))
    currency: Mapped[str] = mapped_column(String(5))
    user_agent: Mapped[str] = mapped_column(Text)
    created_at: Mapped[datetime.datetime] = mapped_column(DateTime(True))
    user_id: Mapped[int] = mapped_column(Integer)

    user: Mapped['UserMst'] = relationship('UserMst', back_populates='LoginHistory')


class UserMstGroups(Base):
    __tablename__ = 'UserMst_groups'
    __table_args__ = (
        ForeignKeyConstraint(['group_id'], ['auth_group.id'], deferrable=True, initially='DEFERRED', name='UserMst_groups_group_id_bfa15f2a_fk_auth_group_id'),
        ForeignKeyConstraint(['usermst_id'], ['UserMst.UserID'], deferrable=True, initially='DEFERRED', name='UserMst_groups_usermst_id_d5c7157a_fk_UserMst_UserID'),
        PrimaryKeyConstraint('id', name='UserMst_groups_pkey'),
        UniqueConstraint('usermst_id', 'group_id', name='UserMst_groups_usermst_id_group_id_082a8409_uniq'),
        Index('UserMst_groups_group_id_bfa15f2a', 'group_id'),
        Index('UserMst_groups_usermst_id_d5c7157a', 'usermst_id')
    )

    id: Mapped[int] = mapped_column(BigInteger, Identity(start=1, increment=1, minvalue=1, maxvalue=9223***************, cycle=False, cache=1), primary_key=True)
    usermst_id: Mapped[int] = mapped_column(Integer)
    group_id: Mapped[int] = mapped_column(Integer)

    group: Mapped['AuthGroup'] = relationship('AuthGroup', back_populates='UserMst_groups')
    usermst: Mapped['UserMst'] = relationship('UserMst', back_populates='UserMst_groups')


class UserMstUserPermissions(Base):
    __tablename__ = 'UserMst_user_permissions'
    __table_args__ = (
        ForeignKeyConstraint(['permission_id'], ['auth_permission.id'], deferrable=True, initially='DEFERRED', name='UserMst_user_permiss_permission_id_06930a11_fk_auth_perm'),
        ForeignKeyConstraint(['usermst_id'], ['UserMst.UserID'], deferrable=True, initially='DEFERRED', name='UserMst_user_permissions_usermst_id_ca7abfab_fk_UserMst_UserID'),
        PrimaryKeyConstraint('id', name='UserMst_user_permissions_pkey'),
        UniqueConstraint('usermst_id', 'permission_id', name='UserMst_user_permissions_usermst_id_permission_id_384750fe_uniq'),
        Index('UserMst_user_permissions_permission_id_06930a11', 'permission_id'),
        Index('UserMst_user_permissions_usermst_id_ca7abfab', 'usermst_id')
    )

    id: Mapped[int] = mapped_column(BigInteger, Identity(start=1, increment=1, minvalue=1, maxvalue=9223***************, cycle=False, cache=1), primary_key=True)
    usermst_id: Mapped[int] = mapped_column(Integer)
    permission_id: Mapped[int] = mapped_column(Integer)

    permission: Mapped['AuthPermission'] = relationship('AuthPermission', back_populates='UserMst_user_permissions')
    usermst: Mapped['UserMst'] = relationship('UserMst', back_populates='UserMst_user_permissions')


class DjangoAdminLog(Base):
    __tablename__ = 'django_admin_log'
    __table_args__ = (
        CheckConstraint('action_flag >= 0', name='django_admin_log_action_flag_check'),
        ForeignKeyConstraint(['content_type_id'], ['django_content_type.id'], deferrable=True, initially='DEFERRED', name='django_admin_log_content_type_id_c4bce8eb_fk_django_co'),
        ForeignKeyConstraint(['user_id'], ['UserMst.UserID'], deferrable=True, initially='DEFERRED', name='django_admin_log_user_id_c564eba6_fk_UserMst_UserID'),
        PrimaryKeyConstraint('id', name='django_admin_log_pkey'),
        Index('django_admin_log_content_type_id_c4bce8eb', 'content_type_id'),
        Index('django_admin_log_user_id_c564eba6', 'user_id')
    )

    id: Mapped[int] = mapped_column(Integer, Identity(start=1, increment=1, minvalue=1, maxvalue=**********, cycle=False, cache=1), primary_key=True)
    action_time: Mapped[datetime.datetime] = mapped_column(DateTime(True))
    object_repr: Mapped[str] = mapped_column(String(200))
    action_flag: Mapped[int] = mapped_column(SmallInteger)
    change_message: Mapped[str] = mapped_column(Text)
    user_id: Mapped[int] = mapped_column(Integer)
    object_id: Mapped[Optional[str]] = mapped_column(Text)
    content_type_id: Mapped[Optional[int]] = mapped_column(Integer)

    content_type: Mapped[Optional['DjangoContentType']] = relationship('DjangoContentType', back_populates='django_admin_log')
    user: Mapped['UserMst'] = relationship('UserMst', back_populates='django_admin_log')


t_AccountSummary = Table(
    'AccountSummary', Base.metadata,
    Column('BranchMstID', Integer, nullable=False),
    Column('BankMstID', Integer, nullable=False),
    Column('CustomerMstID', Integer, nullable=False),
    Column('CustomerCode', String(255)),
    Column('CustomerName', String(255), nullable=False),
    Column('MobileNumber', String(15), nullable=False),
    Column('Gender', String(10)),
    Column('DateOfBirth', Date),
    Column('DisbursementID', String(255), nullable=False),
    Column('DisbursementDate', Date),
    Column('DisbursementAmt', Numeric(18, 2), nullable=False),
    Column('LoanType', String(50), nullable=False),
    Column('DPD', Integer),
    Column('LoanClassification', String(50)),
    Column('LastPaymentDate', Date),
    Column('LastCollectedAmount', Numeric(18, 2)),
    Column('CurrentBalance', Numeric(18, 2)),
    Column('OverDueAmt', Numeric(18, 2), nullable=False),
    Column('PrincipleOutstanding', Numeric(18, 2)),
    Column('InterestOutstanding', Numeric(18, 2)),
    Column('TotalOutstanding', Numeric(18, 2)),
    Column('PrinciplePending', Numeric(18, 2), nullable=False),
    Column('InterestPending', Numeric(18, 2), nullable=False),
    Column('TotalPending', Numeric(18, 2), nullable=False),
    Column('ClosingDate', Date),
    Column('IsActive', Boolean, nullable=False, server_default=text('true')),
    Column('EMIAmount', Numeric(18, 2), nullable=False),
    Column('NextEMIDate', Date),
    Column('LoanMstID', Integer, nullable=False),
    Column('ExtraColumn1', String(255)),
    Column('ExtraColumn2', String(255)),
    Column('ExtraColumn3', String(255)),
    Column('ExtraColumn4', String(255)),
    Column('ExtraColumn5', String(255)),
    Column('CreatedDate', Date),
    Column('Branch_id', String(255)),
    Column('Branch', String(255)),
    Column('Region_id', String(255)),
    Column('Region', String(255)),
    Column('State_id', String(255)),
    Column('State', String(255)),
    Column('CollectionOfficerID', String),
    Column('CollectionOfficerName', String),
    Column('groupid', String),
    Column('groupname', String),
    Column('CurrentDPD', String),
    Column('LastMonthDPD', String),
    Column('Division', String(100)),
    Column('District_id', String(50)),
    Column('CO', String(100)),
    Column('Circle_id', String(50)),
    Column('Zone', String(100)),
    Column('Zone_id', String(50)),
    Column('Circle', String(100)),
    Column('Division_id', String(50)),
    Column('CO_id', String(50)),
    Column('HeadOffice_id', String(50)),
    Column('HeadOffice', String(100)),
    Column('District', String(100)),
    ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='AccountSummary_BankID_fkey'),
    ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='AccountSummary_BranchID_fkey'),
    ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='AccountSummary_LoanID_fkey')
)


class BMAllocation(Base):
    __tablename__ = 'BMAllocation'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], ondelete='CASCADE', name='BMAllocation_fk_BankMstID'),
        ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], ondelete='CASCADE', name='BMAllocation_fk_BranchMstID'),
        ForeignKeyConstraint(['CustomerMstID'], ['CustomerMst.CustomerMstID'], ondelete='CASCADE', name='BMAllocation_fk_CustomerMstID'),
        ForeignKeyConstraint(['LngMstID'], ['LanguageMst.LngMstID'], ondelete='CASCADE', name='BMAllocation_fk_LngID'),
        ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], ondelete='SET NULL', name='fk_BMAllocation_LoanMstID'),
        PrimaryKeyConstraint('BMAllocationID', name='BMAllocation_pkey')
    )

    BMAllocationID: Mapped[int] = mapped_column(Integer, primary_key=True)
    BankMstID: Mapped[int] = mapped_column(Integer)
    BranchMstID: Mapped[int] = mapped_column(Integer)
    CustomerMstID: Mapped[int] = mapped_column(Integer)
    CreatedDate: Mapped[datetime.datetime] = mapped_column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    ContactNumber: Mapped[str] = mapped_column(String(15))
    BranchName: Mapped[str] = mapped_column(String(255))
    LoanType: Mapped[str] = mapped_column(String(100))
    IsAction: Mapped[bool] = mapped_column(Boolean, server_default=text('false'))
    LngMstID: Mapped[int] = mapped_column(Integer)
    LoanMstID: Mapped[Optional[int]] = mapped_column(Integer)

    BankMst_: Mapped['BankMst'] = relationship('BankMst', back_populates='BMAllocation')
    BranchMst_: Mapped['BranchMst'] = relationship('BranchMst', back_populates='BMAllocation')
    CustomerMst_: Mapped['CustomerMst'] = relationship('CustomerMst', back_populates='BMAllocation')
    LanguageMst_: Mapped['LanguageMst'] = relationship('LanguageMst', back_populates='BMAllocation')
    LoanMst_: Mapped[Optional['LoanMst']] = relationship('LoanMst', back_populates='BMAllocation')


class BlasterHistory(Base):
    __tablename__ = 'BlasterHistory'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='BlasterHistory_fk_BankMstID'),
        ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='BlasterHistory_fk_BranchMstID'),
        ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='BlasterHistory_fk_LoanMstID'),
        PrimaryKeyConstraint('BlasterHistoryID', name='BlasterHistory_pkey'),
        UniqueConstraint('BlasterQueueID', name='unique_blasterqueueid_history')
    )

    BlasterHistoryID: Mapped[int] = mapped_column(Integer, primary_key=True)
    BlasterQueueID: Mapped[int] = mapped_column(Integer)
    CreatedDate: Mapped[datetime.datetime] = mapped_column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    BankMstID: Mapped[int] = mapped_column(Integer)
    BranchMstID: Mapped[int] = mapped_column(Integer)
    LoanMstID: Mapped[int] = mapped_column(Integer)
    CustomerID: Mapped[int] = mapped_column(Integer)
    DisbursementID: Mapped[str] = mapped_column(String(255))
    ContactNumber: Mapped[str] = mapped_column(String(15))
    LoanType: Mapped[str] = mapped_column(String(100))
    CustomerName: Mapped[str] = mapped_column(String(255))
    IsSent: Mapped[bool] = mapped_column(Boolean, server_default=text('false'))
    Type: Mapped[str] = mapped_column(String(50))
    BankName: Mapped[str] = mapped_column(String(255))
    Language: Mapped[str] = mapped_column(String)
    Next_EMI_Amount: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    Next_EMI_Date: Mapped[Optional[datetime.date]] = mapped_column(Date)
    Total_Collection: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    Latest_CollectedDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    Latest_CollectedAmt: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    Identifier: Mapped[Optional[str]] = mapped_column(String(255))
    CallID: Mapped[Optional[str]] = mapped_column(String(255))
    Recording: Mapped[Optional[str]] = mapped_column(String(255))
    Conversation_json: Mapped[Optional[dict]] = mapped_column(JSONB)
    Conclusion: Mapped[Optional[str]] = mapped_column(String(255))
    CallTried: Mapped[Optional[int]] = mapped_column(Integer)
    CallDuration: Mapped[Optional[float]] = mapped_column(Double(53))
    CallConnected: Mapped[Optional[bool]] = mapped_column(Boolean)
    Slot: Mapped[Optional[str]] = mapped_column(String)
    BasedOn: Mapped[Optional[str]] = mapped_column(String)
    BranchCode: Mapped[Optional[str]] = mapped_column(String)
    CustomerCode: Mapped[Optional[str]] = mapped_column(String)
    FlowID: Mapped[Optional[int]] = mapped_column(Integer)
    BlasterTemplateMappingID: Mapped[Optional[int]] = mapped_column(Integer)
    IsDelivered: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('false'))
    IsRead: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('false'))
    IsResponded: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('false'))
    OverDueAmt: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(18, 2))

    BankMst_: Mapped['BankMst'] = relationship('BankMst', back_populates='BlasterHistory')
    BranchMst_: Mapped['BranchMst'] = relationship('BranchMst', back_populates='BlasterHistory')
    LoanMst_: Mapped['LoanMst'] = relationship('LoanMst', back_populates='BlasterHistory')


class BlasterQueue(Base):
    __tablename__ = 'BlasterQueue'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='BlasterQueue_BankMstID_fkey'),
        ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='BlasterQueue_BranchMstID_fkey'),
        ForeignKeyConstraint(['CommFlowID'], ['CommFlow.CommFlowID'], ondelete='SET NULL', name='fk_commflow_id'),
        ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='BlasterQueue_LoanMstID_fkey'),
        PrimaryKeyConstraint('BlasterQueueID', name='BlasterQueue_pkey')
    )

    BlasterQueueID: Mapped[int] = mapped_column(Integer, primary_key=True)
    CreatedDate: Mapped[datetime.datetime] = mapped_column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    BranchMstID: Mapped[int] = mapped_column(Integer)
    LoanMstID: Mapped[int] = mapped_column(Integer)
    BranchCode: Mapped[str] = mapped_column(String(50))
    LoanType: Mapped[str] = mapped_column(String(100))
    CustomerName: Mapped[str] = mapped_column(String(255))
    BankName: Mapped[str] = mapped_column(String(255))
    CustomerCode: Mapped[Optional[str]] = mapped_column(String(50))
    Language: Mapped[Optional[str]] = mapped_column(String(100))
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))
    Slot: Mapped[Optional[str]] = mapped_column(String(255))
    CallTried: Mapped[Optional[int]] = mapped_column(Integer, server_default=text('0'))
    CallConnected: Mapped[Optional[bool]] = mapped_column(Boolean)
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)
    BasedOn: Mapped[Optional[str]] = mapped_column(String)
    CommFlowID: Mapped[Optional[int]] = mapped_column(Integer)
    ContactNumber: Mapped[Optional[str]] = mapped_column(String)
    CallDuration: Mapped[Optional[float]] = mapped_column(Double(53))
    CustomerID: Mapped[Optional[int]] = mapped_column(Integer)
    DisbursementID: Mapped[Optional[str]] = mapped_column(String)
    FlowID: Mapped[Optional[int]] = mapped_column(Integer)
    BlasterTemplateMappingID: Mapped[Optional[int]] = mapped_column(Integer)
    Total_Collection: Mapped[Optional[int]] = mapped_column(Integer)
    Latest_CollectedDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    Latest_CollectedAmt: Mapped[Optional[int]] = mapped_column(Integer)
    Next_EMI_Amount: Mapped[Optional[int]] = mapped_column(Integer)
    Next_EMI_Date: Mapped[Optional[datetime.date]] = mapped_column(Date)
    Identifier: Mapped[Optional[str]] = mapped_column(String)
    Type: Mapped[Optional[str]] = mapped_column(String)
    OverDueAmt: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(18, 2))

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='BlasterQueue')
    BranchMst_: Mapped['BranchMst'] = relationship('BranchMst', back_populates='BlasterQueue')
    CommFlow_: Mapped[Optional['CommFlow']] = relationship('CommFlow', back_populates='BlasterQueue')
    LoanMst_: Mapped['LoanMst'] = relationship('LoanMst', back_populates='BlasterQueue')


class CommunicationQueue(Base):
    __tablename__ = 'CommunicationQueue'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='CommunicationQueue_fk_BankID'),
        ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='CommunicationQueue_fk_BranchID'),
        ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='CommunicationQueue_fk_LoanMstID'),
        PrimaryKeyConstraint('CommunicationQueueID', name='CommunicationQueue_pkey')
    )

    CommunicationQueueID: Mapped[int] = mapped_column(Integer, primary_key=True)
    BranchMstID: Mapped[int] = mapped_column(Integer)
    IsWhatsApp: Mapped[bool] = mapped_column(Boolean, server_default=text('false'))
    IsBlaster: Mapped[bool] = mapped_column(Boolean, server_default=text('false'))
    IsVoiceBot: Mapped[bool] = mapped_column(Boolean, server_default=text('false'))
    IsSMS: Mapped[bool] = mapped_column(Boolean, server_default=text('false'))
    BankMstID: Mapped[int] = mapped_column(Integer)
    LoanMstID: Mapped[int] = mapped_column(Integer)
    Type: Mapped[str] = mapped_column(String(50))
    IsIVR: Mapped[bool] = mapped_column(Boolean, server_default=text('false'))
    Slot: Mapped[Optional[str]] = mapped_column(String(50))
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))
    CreatedDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    BasedOnDate: Mapped[Optional[str]] = mapped_column(String)
    BMAllocate: Mapped[Optional[str]] = mapped_column(String)
    CollectionOfficerAllocate: Mapped[Optional[str]] = mapped_column(String)
    CampaignMstID: Mapped[Optional[int]] = mapped_column(Integer)
    FlowID: Mapped[Optional[int]] = mapped_column(Integer)

    BankMst_: Mapped['BankMst'] = relationship('BankMst', back_populates='CommunicationQueue')
    BranchMst_: Mapped['BranchMst'] = relationship('BranchMst', back_populates='CommunicationQueue')
    LoanMst_: Mapped['LoanMst'] = relationship('LoanMst', back_populates='CommunicationQueue')


class DNDHistory(Base):
    __tablename__ = 'DNDHistory'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='fk_bankmst'),
        ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='fk_loanmst'),
        PrimaryKeyConstraint('DNDHistoryID', name='DNDHistory_pkey')
    )

    DNDHistoryID: Mapped[int] = mapped_column(Integer, primary_key=True)
    LoanMstID: Mapped[int] = mapped_column(Integer)
    BankMstID: Mapped[int] = mapped_column(Integer)
    InsertionDate: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime)
    DeletionDate: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime)

    BankMst_: Mapped['BankMst'] = relationship('BankMst', back_populates='DNDHistory')
    LoanMst_: Mapped['LoanMst'] = relationship('LoanMst', back_populates='DNDHistory')


class Dialer(Base):
    __tablename__ = 'Dialer'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], ondelete='CASCADE', name='Dialer_fk_BankMstID'),
        ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], ondelete='CASCADE', name='Dialer_fk_BranchMstID'),
        ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], ondelete='CASCADE', name='Dialer_fk_LoanMstID'),
        PrimaryKeyConstraint('DialerID', name='Dialer_pkey')
    )

    DialerID: Mapped[int] = mapped_column(Integer, primary_key=True)
    BankMstID: Mapped[int] = mapped_column(Integer)
    BranchMstID: Mapped[int] = mapped_column(Integer)
    LoanMstID: Mapped[int] = mapped_column(Integer)
    Call_ID: Mapped[str] = mapped_column(String(50))
    DateTime_: Mapped[datetime.datetime] = mapped_column('DateTime', DateTime, server_default=text('CURRENT_TIMESTAMP'))
    RecordingURL: Mapped[str] = mapped_column(Text)
    CustomerMstID: Mapped[Optional[int]] = mapped_column(Integer)
    InitiatingNumber: Mapped[Optional[str]] = mapped_column(String)

    BankMst_: Mapped['BankMst'] = relationship('BankMst', back_populates='Dialer')
    BranchMst_: Mapped['BranchMst'] = relationship('BranchMst', back_populates='Dialer')
    LoanMst_: Mapped['LoanMst'] = relationship('LoanMst', back_populates='Dialer')


class IVRCallHistory(Base):
    __tablename__ = 'IVRCallHistory'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='IVRCallHistory_fk_BankMstID'),
        ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='IVRCallHistory_fk_BranchMstID'),
        ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='IVRCallHistory_fk_LoanMstID'),
        PrimaryKeyConstraint('IVRCallHistoryID', name='IVRCallHistory_pkey'),
        UniqueConstraint('IVRCallQueueID', name='unique_ivrcallqueueid_history'),
        UniqueConstraint('IVRCallQueueID', name='IVRCallHistory_Unique_IVRQueueID')
    )

    IVRCallHistoryID: Mapped[int] = mapped_column(Integer, primary_key=True)
    IVRCallQueueID: Mapped[int] = mapped_column(Integer)
    CreatedDate: Mapped[datetime.datetime] = mapped_column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    BankMstID: Mapped[int] = mapped_column(Integer)
    BranchMstID: Mapped[int] = mapped_column(Integer)
    LoanMstID: Mapped[int] = mapped_column(Integer)
    DisbursementID: Mapped[str] = mapped_column(String(255))
    CustomerName: Mapped[str] = mapped_column(String(255))
    IsSent: Mapped[bool] = mapped_column(Boolean, server_default=text('false'))
    BankName: Mapped[str] = mapped_column(String(255))
    BranchCode: Mapped[Optional[str]] = mapped_column(String)
    CustomerID: Mapped[Optional[int]] = mapped_column(Integer)
    ContactNumber: Mapped[Optional[str]] = mapped_column(String(15))
    LoanType: Mapped[Optional[str]] = mapped_column(String(100))
    Next_EMI_Amount: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    Next_EMI_Date: Mapped[Optional[datetime.date]] = mapped_column(Date)
    Total_Collection: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    Latest_CollectedDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    Latest_CollectedAmt: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    Type: Mapped[Optional[str]] = mapped_column(String(50))
    Identifier: Mapped[Optional[str]] = mapped_column(String(255))
    CallID: Mapped[Optional[str]] = mapped_column(String(255))
    Recording: Mapped[Optional[str]] = mapped_column(String(255))
    Conversation_json: Mapped[Optional[dict]] = mapped_column(JSONB)
    Conclusion: Mapped[Optional[str]] = mapped_column(String(255))
    CallTried: Mapped[Optional[int]] = mapped_column(Integer)
    CallDuration: Mapped[Optional[float]] = mapped_column(Double(53))
    CustomerCode: Mapped[Optional[str]] = mapped_column(String(50))
    OverDueAmt: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(18, 2))
    Language: Mapped[Optional[str]] = mapped_column(String(100))
    Slot: Mapped[Optional[str]] = mapped_column(String(255))
    CallConnected: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('false'))
    BasedOn: Mapped[Optional[str]] = mapped_column(String)
    CommFlowID: Mapped[Optional[int]] = mapped_column(Integer)
    FlowID: Mapped[Optional[int]] = mapped_column(Integer)
    IVRTemplateMappingID: Mapped[Optional[int]] = mapped_column(Integer)
    IsDelivered: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('false'))
    IsRead: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('false'))
    IsResponded: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('false'))

    BankMst_: Mapped['BankMst'] = relationship('BankMst', back_populates='IVRCallHistory')
    BranchMst_: Mapped['BranchMst'] = relationship('BranchMst', back_populates='IVRCallHistory')
    LoanMst_: Mapped['LoanMst'] = relationship('LoanMst', back_populates='IVRCallHistory')


class IVRQueue(Base):
    __tablename__ = 'IVRQueue'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='IVRQueue_BankMstID_fkey'),
        ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='IVRQueue_BranchMstID_fkey'),
        ForeignKeyConstraint(['CommFlowID'], ['CommFlow.CommFlowID'], ondelete='SET NULL', name='fk_commflow_id'),
        ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='IVRQueue_LoanMstID_fkey'),
        PrimaryKeyConstraint('IVRQueueID', name='IVRQueue_pkey')
    )

    IVRQueueID: Mapped[int] = mapped_column(Integer, primary_key=True)
    CreatedDate: Mapped[datetime.datetime] = mapped_column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    BranchMstID: Mapped[int] = mapped_column(Integer)
    LoanMstID: Mapped[int] = mapped_column(Integer)
    BranchCode: Mapped[str] = mapped_column(String(50))
    LoanType: Mapped[str] = mapped_column(String(100))
    CustomerName: Mapped[str] = mapped_column(String(255))
    Language: Mapped[str] = mapped_column(String(100))
    BankName: Mapped[str] = mapped_column(String(255))
    CustomerCode: Mapped[Optional[str]] = mapped_column(String(50))
    OverDueAmt: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(18, 2))
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))
    Slot: Mapped[Optional[str]] = mapped_column(String(255))
    CallTried: Mapped[Optional[int]] = mapped_column(Integer, server_default=text('0'))
    CallConnected: Mapped[Optional[bool]] = mapped_column(Boolean)
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)
    BasedOn: Mapped[Optional[str]] = mapped_column(String)
    CommFlowID: Mapped[Optional[int]] = mapped_column(Integer)
    DisbursementID: Mapped[Optional[str]] = mapped_column(String)
    CustomerID: Mapped[Optional[int]] = mapped_column(Integer)
    ContactNumber: Mapped[Optional[str]] = mapped_column(String)
    Next_EMI_Amount: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    Next_EMI_Date: Mapped[Optional[datetime.date]] = mapped_column(Date)
    Total_Collection: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    Latest_CollectedDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    Latest_CollectedAmt: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    Type: Mapped[Optional[str]] = mapped_column(String(50))
    FlowID: Mapped[Optional[int]] = mapped_column(Integer)
    IVRTemplateMappingID: Mapped[Optional[int]] = mapped_column(Integer)
    Identifier: Mapped[Optional[str]] = mapped_column(String)

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='IVRQueue')
    BranchMst_: Mapped['BranchMst'] = relationship('BranchMst', back_populates='IVRQueue')
    CommFlow_: Mapped[Optional['CommFlow']] = relationship('CommFlow', back_populates='IVRQueue')
    LoanMst_: Mapped['LoanMst'] = relationship('LoanMst', back_populates='IVRQueue')


class LoanInstallmentMst(Base):
    __tablename__ = 'LoanInstallmentMst'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='LoanInstallmentMst_BankMstID_fkey'),
        ForeignKeyConstraint(['CustomerMstID'], ['CustomerMst.CustomerMstID'], name='LoanInstallmentMst_CustomerMstID_fkey'),
        ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='LoanInstallmentMst_LoanMstID_fkey'),
        PrimaryKeyConstraint('InstallmentMstID', name='LoanInstallmentMst_pkey')
    )

    InstallmentMstID: Mapped[int] = mapped_column(Integer, Sequence('LoanInstallmentMst_LoanInstallmentMstID_seq'), primary_key=True)
    LoanMstID: Mapped[int] = mapped_column(Integer)
    Demand: Mapped[decimal.Decimal] = mapped_column(Numeric(15, 2))
    ScheduleID: Mapped[Optional[int]] = mapped_column(Integer)
    ScheduleDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    PrincipleAmt: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    InterestAmt: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))
    DisbursementID: Mapped[Optional[str]] = mapped_column(String(255))
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)
    CustomerMstID: Mapped[Optional[int]] = mapped_column(Integer)

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='LoanInstallmentMst')
    CustomerMst_: Mapped[Optional['CustomerMst']] = relationship('CustomerMst', back_populates='LoanInstallmentMst')
    LoanMst_: Mapped['LoanMst'] = relationship('LoanMst', back_populates='LoanInstallmentMst')


class Response(Base):
    __tablename__ = 'Response'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='Response_BankMstID_fkey'),
        ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='Response_BranchMstID_fkey'),
        ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='Response_fkey_LOANID'),
        PrimaryKeyConstraint('ResponseID', name='Response_pkey')
    )

    ResponseID: Mapped[int] = mapped_column(Integer, primary_key=True)
    LoanMstID: Mapped[int] = mapped_column(Integer)
    BlasterQueueID: Mapped[Optional[int]] = mapped_column(Integer)
    IVRQueueID: Mapped[Optional[int]] = mapped_column(Integer)
    VoiceBotQueueID: Mapped[Optional[int]] = mapped_column(Integer)
    WhatsappQueueID: Mapped[Optional[int]] = mapped_column(Integer)
    AllocationID: Mapped[Optional[int]] = mapped_column(Integer)
    FeedbackID: Mapped[Optional[int]] = mapped_column(Integer)
    ModeOfPayment: Mapped[Optional[str]] = mapped_column(String(50))
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))
    PromiseDateTime: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime)
    Status: Mapped[Optional[str]] = mapped_column(String(50))
    Amount: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    ResponseDateTime: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime)
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)
    BranchMstID: Mapped[Optional[int]] = mapped_column(Integer)
    wrong_number: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('false'))

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='Response')
    BranchMst_: Mapped[Optional['BranchMst']] = relationship('BranchMst', back_populates='Response')
    LoanMst_: Mapped['LoanMst'] = relationship('LoanMst', back_populates='Response')


class SMSHistory(Base):
    __tablename__ = 'SMSHistory'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='SMSHistory_fk_BankMstID'),
        ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='SMSHistory_fk_BranchMstID'),
        ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='SMSHistory_fk_LoanMstID'),
        PrimaryKeyConstraint('SMSHistoryID', name='SMSHistory_pkey'),
        UniqueConstraint('SMSQueueID', name='unique_smsqueueid_history')
    )

    SMSHistoryID: Mapped[int] = mapped_column(Integer, primary_key=True)
    SMSQueueID: Mapped[int] = mapped_column(Integer)
    CreatedDate: Mapped[datetime.datetime] = mapped_column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    BankMstID: Mapped[int] = mapped_column(Integer)
    BranchMstID: Mapped[int] = mapped_column(Integer)
    LoanMstID: Mapped[int] = mapped_column(Integer)
    CustomerID: Mapped[int] = mapped_column(Integer)
    DisbursementID: Mapped[str] = mapped_column(String(255))
    ContactNumber: Mapped[str] = mapped_column(String(15))
    LoanType: Mapped[str] = mapped_column(String(100))
    CustomerName: Mapped[str] = mapped_column(String(255))
    IsSent: Mapped[bool] = mapped_column(Boolean, server_default=text('false'))
    IsDelivered: Mapped[bool] = mapped_column(Boolean, server_default=text('false'))
    IsRead: Mapped[bool] = mapped_column(Boolean, server_default=text('false'))
    IsResponded: Mapped[bool] = mapped_column(Boolean, server_default=text('false'))
    Type: Mapped[str] = mapped_column(String(50))
    BankName: Mapped[str] = mapped_column(String(255))
    BranchName: Mapped[Optional[str]] = mapped_column(String(255))
    Next_EMI_Amount: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    Next_EMI_Date: Mapped[Optional[datetime.date]] = mapped_column(Date)
    Total_Collection: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    Latest_CollectedDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    Latest_CollectedAmt: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    Response_: Mapped[Optional[str]] = mapped_column('Response', Text)
    Identifier: Mapped[Optional[str]] = mapped_column(String(255))
    Conversation_json: Mapped[Optional[dict]] = mapped_column(JSONB)
    Conclusion: Mapped[Optional[str]] = mapped_column(String(255))
    Attempts: Mapped[Optional[int]] = mapped_column(Integer)
    BranchCode: Mapped[Optional[str]] = mapped_column(String(50))
    CustomerCode: Mapped[Optional[str]] = mapped_column(String(50))
    OverDueAmt: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(18, 2))
    Language: Mapped[Optional[str]] = mapped_column(String(100))
    Slot: Mapped[Optional[str]] = mapped_column(String(255))
    BasedOn: Mapped[Optional[str]] = mapped_column(String)
    CommFlowID: Mapped[Optional[int]] = mapped_column(Integer)
    FlowID: Mapped[Optional[int]] = mapped_column(Integer)
    SMSTemplateMappingID: Mapped[Optional[int]] = mapped_column(Integer)

    BankMst_: Mapped['BankMst'] = relationship('BankMst', back_populates='SMSHistory')
    BranchMst_: Mapped['BranchMst'] = relationship('BranchMst', back_populates='SMSHistory')
    LoanMst_: Mapped['LoanMst'] = relationship('LoanMst', back_populates='SMSHistory')


class SMSQueue(Base):
    __tablename__ = 'SMSQueue'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='SMSQueue_BankMstID_fkey'),
        ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='SMSQueue_BranchMstID_fkey'),
        ForeignKeyConstraint(['CommFlowID'], ['CommFlow.CommFlowID'], ondelete='SET NULL', name='fk_commflow_id'),
        ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='SMSQueue_LoanMstID_fkey'),
        PrimaryKeyConstraint('SMSQueueID', name='SMSQueue_pkey')
    )

    SMSQueueID: Mapped[int] = mapped_column(Integer, primary_key=True)
    CreatedDate: Mapped[datetime.datetime] = mapped_column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    BranchMstID: Mapped[int] = mapped_column(Integer)
    LoanMstID: Mapped[int] = mapped_column(Integer)
    BranchCode: Mapped[str] = mapped_column(String(50))
    LoanType: Mapped[str] = mapped_column(String(100))
    CustomerName: Mapped[str] = mapped_column(String(255))
    BankName: Mapped[str] = mapped_column(String(255))
    CustomerCode: Mapped[Optional[str]] = mapped_column(String(50))
    Language: Mapped[Optional[str]] = mapped_column(String(100))
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))
    Slot: Mapped[Optional[str]] = mapped_column(String(255))
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)
    BasedOn: Mapped[Optional[str]] = mapped_column(String)
    CommFlowID: Mapped[Optional[int]] = mapped_column(Integer)
    CustomerID: Mapped[Optional[int]] = mapped_column(Integer)
    DisbursementID: Mapped[Optional[str]] = mapped_column(String(255))
    ContactNumber: Mapped[Optional[str]] = mapped_column(String(15))
    BranchName: Mapped[Optional[str]] = mapped_column(String(255))
    Next_EMI_Amount: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    Next_EMI_Date: Mapped[Optional[datetime.date]] = mapped_column(Date)
    Total_Collection: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    Latest_CollectedDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    Latest_CollectedAmt: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    Type: Mapped[Optional[str]] = mapped_column(String(50))
    LanguageID: Mapped[Optional[int]] = mapped_column(Integer)
    Identifier: Mapped[Optional[str]] = mapped_column(String(255))
    FlowID: Mapped[Optional[int]] = mapped_column(Integer)
    SMSTemplateMappingID: Mapped[Optional[int]] = mapped_column(Integer)
    OverDueAmt: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(18, 2))

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='SMSQueue')
    BranchMst_: Mapped['BranchMst'] = relationship('BranchMst', back_populates='SMSQueue')
    CommFlow_: Mapped[Optional['CommFlow']] = relationship('CommFlow', back_populates='SMSQueue')
    LoanMst_: Mapped['LoanMst'] = relationship('LoanMst', back_populates='SMSQueue')


class SecondaryCustMst(Base):
    __tablename__ = 'SecondaryCustMst'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='SecondaryCustMst_BankMstID_fkey'),
        ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='CoApplicantMst_BranchID_fkey'),
        ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='CoApplicantMst_LoanID_fkey'),
        PrimaryKeyConstraint('SecondaryCustMstID', name='CoApplicantMst_pkey')
    )

    SecondaryCustMstID: Mapped[int] = mapped_column(Integer, primary_key=True)
    ApplicantName: Mapped[str] = mapped_column(String(255))
    BranchMstID: Mapped[Optional[int]] = mapped_column(Integer)
    DateOfBirth: Mapped[Optional[datetime.date]] = mapped_column(Date)
    CoApplicantContact: Mapped[Optional[str]] = mapped_column(String(20))
    IsActive: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('true'))
    LastModifiedDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    DisbursementID: Mapped[Optional[str]] = mapped_column(String(255))
    LoanMstID: Mapped[Optional[int]] = mapped_column(Integer)
    Gender: Mapped[Optional[str]] = mapped_column(String(10))
    MobileNumber: Mapped[Optional[str]] = mapped_column(String(20))
    AlternativeMobileNumber: Mapped[Optional[str]] = mapped_column(String(20))
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='SecondaryCustMst')
    BranchMst_: Mapped[Optional['BranchMst']] = relationship('BranchMst', back_populates='SecondaryCustMst')
    LoanMst_: Mapped[Optional['LoanMst']] = relationship('LoanMst', back_populates='SecondaryCustMst')
    CoAppMst: Mapped[List['CoAppMst']] = relationship('CoAppMst', back_populates='SecondaryCustMst_')


class Transactions(Base):
    __tablename__ = 'Transactions'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='Transactions_BankMstID_fkey'),
        ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='Transactions_BranchID_fkey'),
        ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='Transactions_LoanID_fkey'),
        PrimaryKeyConstraint('TransactionID', name='Transactions_pkey')
    )

    TransactionID: Mapped[int] = mapped_column(Integer, primary_key=True)
    BranchMstID: Mapped[int] = mapped_column(Integer)
    LoanMstID: Mapped[int] = mapped_column(Integer)
    CreatedDate: Mapped[Optional[datetime.date]] = mapped_column(Date, server_default=text('CURRENT_DATE'))
    DisbursementID: Mapped[Optional[str]] = mapped_column(String(255))
    CollectedAmt: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(10, 2))
    CollectedDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    Processed: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('false'))
    PrincipleCollected: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    InterestCollected: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)
    BranchName: Mapped[Optional[str]] = mapped_column(String)
    LoanType: Mapped[Optional[str]] = mapped_column(String)
    CustomerId: Mapped[Optional[int]] = mapped_column(Integer)

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='Transactions')
    BranchMst_: Mapped['BranchMst'] = relationship('BranchMst', back_populates='Transactions')
    LoanMst_: Mapped['LoanMst'] = relationship('LoanMst', back_populates='Transactions')


class UserFeedback(Base):
    __tablename__ = 'UserFeedback'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='UserFeedback_BankMstID_fkey'),
        ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], ondelete='CASCADE', name='UserFeedback_fk_BranchID'),
        ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], ondelete='CASCADE', name='UserFeedback_fk_LoanID'),
        PrimaryKeyConstraint('UserFeedbackID', name='UserFeedback_pkey')
    )

    UserFeedbackID: Mapped[int] = mapped_column(Integer, primary_key=True)
    LoanMstID: Mapped[int] = mapped_column(Integer)
    FeedbackDate: Mapped[datetime.date] = mapped_column(Date)
    AgreedToPay: Mapped[bool] = mapped_column(Boolean)
    RefusedToPay: Mapped[bool] = mapped_column(Boolean)
    WrongNumber: Mapped[bool] = mapped_column(Boolean)
    CustomerBusy: Mapped[bool] = mapped_column(Boolean)
    BranchMstID: Mapped[Optional[int]] = mapped_column(Integer)
    promise_date: Mapped[Optional[datetime.date]] = mapped_column(Date)
    promise_amount: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    ReasonForDenial: Mapped[Optional[str]] = mapped_column(Text)
    mode_of_payment: Mapped[Optional[str]] = mapped_column(String(255))
    CollectionDate: Mapped[Optional[str]] = mapped_column(String(255))
    CollectionAmount: Mapped[Optional[str]] = mapped_column(String(255))
    CustomerNotReply: Mapped[Optional[str]] = mapped_column(String(255))
    OtherComments: Mapped[Optional[str]] = mapped_column(String(255))
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='UserFeedback')
    BranchMst_: Mapped[Optional['BranchMst']] = relationship('BranchMst', back_populates='UserFeedback')
    LoanMst_: Mapped['LoanMst'] = relationship('LoanMst', back_populates='UserFeedback')


class VoiceBotHistory(Base):
    __tablename__ = 'VoiceBotHistory'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='VoiceBotHistory_fk_BankMstID'),
        ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='VoiceBotHistory_fk_BranchMstID'),
        ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='VoiceBotHistory_fk_LoanMstID'),
        PrimaryKeyConstraint('VoiceBotHistoryID', name='VoiceBotHistory_pkey'),
        UniqueConstraint('VoiceBotQueueID', name='unique_voicebotqueueid_history')
    )

    VoiceBotHistoryID: Mapped[int] = mapped_column(Integer, primary_key=True)
    VoiceBotQueueID: Mapped[int] = mapped_column(Integer)
    CreatedDate: Mapped[datetime.datetime] = mapped_column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    BankMstID: Mapped[int] = mapped_column(Integer)
    BranchMstID: Mapped[int] = mapped_column(Integer)
    LoanMstID: Mapped[int] = mapped_column(Integer)
    BranchCode: Mapped[str] = mapped_column(String)
    LoanType: Mapped[str] = mapped_column(String(100))
    Type: Mapped[str] = mapped_column(String(50))
    BankName: Mapped[str] = mapped_column(String(255))
    CustomerID: Mapped[Optional[int]] = mapped_column(Integer)
    DisbursementID: Mapped[Optional[str]] = mapped_column(String(255))
    ContactNumber: Mapped[Optional[str]] = mapped_column(String(15))
    BranchName: Mapped[Optional[str]] = mapped_column(String(255))
    CustomerName: Mapped[Optional[str]] = mapped_column(String(255))
    Overdue_Amount: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    Next_EMI_Amount: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    Next_EMI_Date: Mapped[Optional[datetime.date]] = mapped_column(Date)
    Total_Collection: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    Latest_CollectedDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    Latest_CollectedAmt: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    IsSent: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('false'))
    IsDelivered: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('false'))
    IsRead: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('false'))
    IsResponded: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('false'))
    Response_: Mapped[Optional[str]] = mapped_column('Response', Text)
    Identifier: Mapped[Optional[str]] = mapped_column(String(255))
    CallID: Mapped[Optional[str]] = mapped_column(String(255))
    Recording: Mapped[Optional[str]] = mapped_column(String(255))
    Conversation_json: Mapped[Optional[dict]] = mapped_column(JSONB)
    Conclusion: Mapped[Optional[str]] = mapped_column(String(255))
    CallTried: Mapped[Optional[int]] = mapped_column(Integer)
    CallDuration: Mapped[Optional[float]] = mapped_column(Double(53))
    BasedOn: Mapped[Optional[str]] = mapped_column(String)
    CommFlowID: Mapped[Optional[int]] = mapped_column(Integer)
    CustomerCode: Mapped[Optional[str]] = mapped_column(String)
    Language: Mapped[Optional[str]] = mapped_column(String)
    OverdueAmt: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(18, 2))
    Slot: Mapped[Optional[str]] = mapped_column(String(255))
    FlowID: Mapped[Optional[int]] = mapped_column(Integer)
    VoiceBotTemplateMappingID: Mapped[Optional[int]] = mapped_column(Integer)

    BankMst_: Mapped['BankMst'] = relationship('BankMst', back_populates='VoiceBotHistory')
    BranchMst_: Mapped['BranchMst'] = relationship('BranchMst', back_populates='VoiceBotHistory')
    LoanMst_: Mapped['LoanMst'] = relationship('LoanMst', back_populates='VoiceBotHistory')


class VoiceBotQueue(Base):
    __tablename__ = 'VoiceBotQueue'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='VoiceBotQueue_BankMstID_fkey'),
        ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='VoiceBotQueue_BranchMstID_fkey'),
        ForeignKeyConstraint(['CommFlowID'], ['CommFlow.CommFlowID'], ondelete='SET NULL', name='fk_commflow_id'),
        ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='VoiceBotQueue_LoanMstID_fkey'),
        PrimaryKeyConstraint('VoiceBotQueueID', name='VoiceBotQueue_pkey')
    )

    VoiceBotQueueID: Mapped[int] = mapped_column(Integer, primary_key=True)
    CreatedDate: Mapped[datetime.datetime] = mapped_column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    BranchMstID: Mapped[int] = mapped_column(Integer)
    LoanMstID: Mapped[int] = mapped_column(Integer)
    BranchCode: Mapped[str] = mapped_column(String(50))
    LoanType: Mapped[str] = mapped_column(String(100))
    CustomerName: Mapped[str] = mapped_column(String(255))
    BankName: Mapped[str] = mapped_column(String(255))
    CustomerCode: Mapped[Optional[str]] = mapped_column(String(50))
    OverdueAmt: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(18, 2))
    Language: Mapped[Optional[str]] = mapped_column(String(100))
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))
    Slot: Mapped[Optional[str]] = mapped_column(String(255))
    CallTried: Mapped[Optional[int]] = mapped_column(Integer, server_default=text('0'))
    CallConnected: Mapped[Optional[bool]] = mapped_column(Boolean)
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)
    BasedOn: Mapped[Optional[str]] = mapped_column(String)
    CommFlowID: Mapped[Optional[int]] = mapped_column(Integer)
    ContactNumber: Mapped[Optional[str]] = mapped_column(String)
    CustomerID: Mapped[Optional[int]] = mapped_column(Integer)
    DisbursementID: Mapped[Optional[str]] = mapped_column(String(255))
    BranchID: Mapped[Optional[int]] = mapped_column(Integer)
    BranchName: Mapped[Optional[str]] = mapped_column(String(255))
    Next_EMI_Amount: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    Next_EMI_Date: Mapped[Optional[datetime.date]] = mapped_column(Date)
    Total_Collection: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    Latest_CollectedDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    Latest_CollectedAmt: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    Type: Mapped[Optional[str]] = mapped_column(String(50))
    Identifier: Mapped[Optional[str]] = mapped_column(String(255))
    FlowID: Mapped[Optional[int]] = mapped_column(Integer)
    VoiceBotTemplateMappingID: Mapped[Optional[int]] = mapped_column(Integer)

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='VoiceBotQueue')
    BranchMst_: Mapped['BranchMst'] = relationship('BranchMst', back_populates='VoiceBotQueue')
    CommFlow_: Mapped[Optional['CommFlow']] = relationship('CommFlow', back_populates='VoiceBotQueue')
    LoanMst_: Mapped['LoanMst'] = relationship('LoanMst', back_populates='VoiceBotQueue')


class WhatsAppHistory(Base):
    __tablename__ = 'WhatsAppHistory'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='WhatsAppHistory_fk_BankMstID'),
        ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='WhatsAppHistory_fk_BranchMstID'),
        ForeignKeyConstraint(['LngMstID'], ['LanguageMst.LngMstID'], name='WhatsAppHistory_fk_LanguageID'),
        ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='WhatsAppHistory_fk_LoanMstID'),
        PrimaryKeyConstraint('WhatsAppHistoryID', name='WhatsAppHistory_pkey'),
        UniqueConstraint('WhatsAppQueueID', name='unique_whatsappqueueid_history')
    )

    WhatsAppHistoryID: Mapped[int] = mapped_column(Integer, primary_key=True)
    WhatsAppQueueID: Mapped[int] = mapped_column(Integer)
    CreatedDate: Mapped[datetime.datetime] = mapped_column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    BankMstID: Mapped[int] = mapped_column(Integer)
    BranchMstID: Mapped[int] = mapped_column(Integer)
    LoanMstID: Mapped[int] = mapped_column(Integer)
    BranchCode: Mapped[str] = mapped_column(String)
    DisbursementID: Mapped[str] = mapped_column(String(255))
    CustomerName: Mapped[str] = mapped_column(String(255))
    BankName: Mapped[str] = mapped_column(String(255))
    CustomerID: Mapped[Optional[int]] = mapped_column(Integer)
    ContactNumber: Mapped[Optional[str]] = mapped_column(String(15))
    BranchName: Mapped[Optional[str]] = mapped_column(String(255))
    LoanType: Mapped[Optional[str]] = mapped_column(String(100))
    Overdue_Amount: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    Next_EMI_Amount: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    Next_EMI_Date: Mapped[Optional[datetime.date]] = mapped_column(Date)
    Total_Collection: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    Latest_CollectedDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    Latest_CollectedAmt: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    IsSent: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('false'))
    IsDelivered: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('false'))
    IsRead: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('false'))
    IsResponded: Mapped[Optional[bool]] = mapped_column(Boolean, server_default=text('false'))
    Response_: Mapped[Optional[str]] = mapped_column('Response', Text)
    Type: Mapped[Optional[str]] = mapped_column(String(50))
    Identifier: Mapped[Optional[str]] = mapped_column(String(50))
    LngMstID: Mapped[Optional[int]] = mapped_column(Integer)
    BasedOn: Mapped[Optional[str]] = mapped_column(String)
    CommFlowID: Mapped[Optional[int]] = mapped_column(Integer)
    CustomerCode: Mapped[Optional[str]] = mapped_column(String)
    Language: Mapped[Optional[str]] = mapped_column(String)
    FlowID: Mapped[Optional[int]] = mapped_column(Integer)
    WhatsAppTemplateMappingID: Mapped[Optional[int]] = mapped_column(Integer)
    OverdueAmt: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(18, 2))
    Slot: Mapped[Optional[str]] = mapped_column(String(255))

    BankMst_: Mapped['BankMst'] = relationship('BankMst', back_populates='WhatsAppHistory')
    BranchMst_: Mapped['BranchMst'] = relationship('BranchMst', back_populates='WhatsAppHistory')
    LanguageMst_: Mapped[Optional['LanguageMst']] = relationship('LanguageMst', back_populates='WhatsAppHistory')
    LoanMst_: Mapped['LoanMst'] = relationship('LoanMst', back_populates='WhatsAppHistory')


class WhatsAppQueue(Base):
    __tablename__ = 'WhatsAppQueue'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='WhatsAppQueue_BankMstID_fkey'),
        ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='WhatsAppQueue_BranchMstID_fkey'),
        ForeignKeyConstraint(['CommFlowID'], ['CommFlow.CommFlowID'], ondelete='SET NULL', name='fk_commflow_id'),
        ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='WhatsAppQueue_LoanMstID_fkey'),
        PrimaryKeyConstraint('WhatsAppQueueID', name='WhatsAppQueue_pkey')
    )

    WhatsAppQueueID: Mapped[int] = mapped_column(Integer, primary_key=True)
    CreatedDate: Mapped[datetime.datetime] = mapped_column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    BranchMstID: Mapped[int] = mapped_column(Integer)
    LoanMstID: Mapped[int] = mapped_column(Integer)
    BranchCode: Mapped[str] = mapped_column(String(50))
    LoanType: Mapped[str] = mapped_column(String(100))
    CustomerCode: Mapped[str] = mapped_column(String(50))
    CustomerName: Mapped[str] = mapped_column(String(255))
    BankName: Mapped[str] = mapped_column(String(255))
    OverdueAmt: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(18, 2))
    Language: Mapped[Optional[str]] = mapped_column(String(100))
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))
    Slot: Mapped[Optional[str]] = mapped_column(String(255))
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)
    BasedOn: Mapped[Optional[str]] = mapped_column(String)
    CommFlowID: Mapped[Optional[int]] = mapped_column(Integer)
    ContactNumber: Mapped[Optional[str]] = mapped_column(String)
    FlowID: Mapped[Optional[int]] = mapped_column(Integer)
    WhatsAppTemplateMappingID: Mapped[Optional[int]] = mapped_column(Integer)
    CustomerID: Mapped[Optional[int]] = mapped_column(Integer)
    DisbursementID: Mapped[Optional[str]] = mapped_column(String(255))
    Next_EMI_Amount: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    Next_EMI_Date: Mapped[Optional[datetime.date]] = mapped_column(Date)
    Total_Collection: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    Latest_CollectedDate: Mapped[Optional[datetime.date]] = mapped_column(Date)
    Latest_CollectedAmt: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric(15, 2))
    Response_: Mapped[Optional[str]] = mapped_column('Response', Text)
    Type: Mapped[Optional[str]] = mapped_column(String(50))
    Identifier: Mapped[Optional[str]] = mapped_column(String(50))
    LngMstID: Mapped[Optional[int]] = mapped_column(Integer)
    BranchName: Mapped[Optional[str]] = mapped_column(String)

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='WhatsAppQueue')
    BranchMst_: Mapped['BranchMst'] = relationship('BranchMst', back_populates='WhatsAppQueue')
    CommFlow_: Mapped[Optional['CommFlow']] = relationship('CommFlow', back_populates='WhatsAppQueue')
    LoanMst_: Mapped['LoanMst'] = relationship('LoanMst', back_populates='WhatsAppQueue')


class WhatsAppMessages(Base):
    __tablename__ = 'WhatsApp_Messages'
    __table_args__ = (
        ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='WhatsAppMessages_LoanMstID_fkey'),
        PrimaryKeyConstraint('WhatsAppMessageID', name='WhatsApp_Messages_pkey')
    )

    WhatsAppMessageID: Mapped[int] = mapped_column(Integer, primary_key=True)
    MessageDate: Mapped[datetime.date] = mapped_column(Date)
    MessageID: Mapped[str] = mapped_column(String(255))
    Sender: Mapped[str] = mapped_column(String(255))
    Content: Mapped[str] = mapped_column(Text)
    Status: Mapped[str] = mapped_column(String(50))
    WhatsAppQueueID: Mapped[Optional[int]] = mapped_column(Integer)
    AssignedTo: Mapped[Optional[str]] = mapped_column(String(255))
    CustomerName: Mapped[Optional[str]] = mapped_column(String(255))
    CustomerNumber: Mapped[Optional[str]] = mapped_column(String(20))
    LoanMstID: Mapped[Optional[int]] = mapped_column(Integer)
    Button: Mapped[Optional[str]] = mapped_column(String)
    Type: Mapped[Optional[str]] = mapped_column(String)
    Identifier: Mapped[Optional[str]] = mapped_column(String)
    WhatsAppUserTemplateID: Mapped[Optional[int]] = mapped_column(Integer)

    LoanMst_: Mapped[Optional['LoanMst']] = relationship('LoanMst', back_populates='WhatsApp_Messages')


class CoAppMst(Base):
    __tablename__ = 'CoAppMst'
    __table_args__ = (
        ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='CoAppMst_BankMstID_fkey'),
        ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='CoAppMst_LoanMstID_fkey'),
        ForeignKeyConstraint(['SecondaryCustMstID'], ['SecondaryCustMst.SecondaryCustMstID'], name='CoAppMst_SecondaryCustMstID_fkey'),
        PrimaryKeyConstraint('CoAppMstID', name='CoAppMst_pkey')
    )

    CoAppMstID: Mapped[int] = mapped_column(Integer, primary_key=True)
    LoanMstID: Mapped[Optional[int]] = mapped_column(Integer)
    SecondaryCustMstID: Mapped[Optional[int]] = mapped_column(Integer)
    CoAppType: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn1: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn2: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn3: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn4: Mapped[Optional[str]] = mapped_column(String(255))
    ExtraColumn5: Mapped[Optional[str]] = mapped_column(String(255))
    BankMstID: Mapped[Optional[int]] = mapped_column(Integer)

    BankMst_: Mapped[Optional['BankMst']] = relationship('BankMst', back_populates='CoAppMst')
    LoanMst_: Mapped[Optional['LoanMst']] = relationship('LoanMst', back_populates='CoAppMst')
    SecondaryCustMst_: Mapped[Optional['SecondaryCustMst']] = relationship('SecondaryCustMst', back_populates='CoAppMst')

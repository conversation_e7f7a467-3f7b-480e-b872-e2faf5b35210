import logging
import time
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from rest_framework.response import Response as DRFResponse
from rest_framework import status
from django.db import transaction
from django.http import JsonResponse
from DB.db_manager import db_manager
from sqlalchemy import text

logger = logging.getLogger(__name__)

class DeleteDisbursementView(APIView):
    permission_classes = (IsAuthenticated,)
    
    def get(self, request):
        """
        Get disbursement data and full response data based on bank ID and optionally disbursement ID(s)
        Query parameters: 
        - bank_id (required)
        - disbursement_id (optional) - can be single ID or comma-separated IDs
        - disbursement_ids (optional) - alternative parameter for multiple IDs
        """
        try:
            # Get parameters from query parameters
            bank_id = request.query_params.get('bank_id')
            disbursement_id = request.query_params.get('disbursement_id')
            disbursement_ids = request.query_params.get('disbursement_ids')
            
            # Validate required parameters
            if not bank_id:
                return DRFResponse({
                    'error': 'bank_id parameter is required',
                    'status': 'error'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Validate bank_id is numeric
            try:
                bank_id = int(bank_id)
            except ValueError:
                return DRFResponse({
                    'error': 'bank_id must be a valid integer',
                    'status': 'error'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Handle disbursement_id(s) validation if provided
            disbursement_ids_list = []
            if disbursement_id or disbursement_ids:
                # Determine which parameter to use (disbursement_ids takes precedence)
                ids_param = disbursement_ids if disbursement_ids else disbursement_id
                
                # Split by comma and validate each ID
                try:
                    id_strings = [id_str.strip() for id_str in ids_param.split(',') if id_str.strip()]
                    disbursement_ids_list = [id_str for id_str in id_strings]
                    
                    if not disbursement_ids_list:
                        return DRFResponse({
                            'error': 'At least one valid disbursement_id must be provided',
                            'status': 'error'
                        }, status=status.HTTP_400_BAD_REQUEST)
                        
                except ValueError:
                    return DRFResponse({
                        'error': 'All disbursement_ids must be valid integers',
                        'status': 'error'
                    }, status=status.HTTP_400_BAD_REQUEST)
            
            # Build query based on whether disbursement_id(s) are provided
            if disbursement_ids_list:
                # Query with specific disbursement_id(s) - Include ALL available fields
                disbursement_ids_str = [str(did) for did in disbursement_ids_list]
                
                query = """
                    SELECT 
                    r."ResponseID",
                    r."Status",
                    r."Amount",
                    r."ResponseDateTime",
                    r."PromiseDateTime",
                    r."LoanMstID",
                    r."BankMstID",
                    r."wrong_number",
                    r."Despute",
                    r."ModeOfPayment",
                    as2."CustomerName",
                    as2."DisbursementID",
                    as2."BankMstID" as account_bank_id,
                    as2."LoanMstID" as account_loan_id,
                    COUNT(*) OVER() as total_count
                    FROM "Response" r
                    JOIN "AccountSummary" as2 ON as2."BankMstID" = %s 
                        AND r."BankMstID" = %s 
                        AND as2."LoanMstID" = r."LoanMstID"
                        AND as2."DisbursementID" = ANY(%s)
                    ORDER BY as2."DisbursementID", r."ResponseID"
                """
                
                # Execute query with positional parameters
                rows, columns = db_manager.execute_query_with_positional_params(
                    query, (bank_id, bank_id, disbursement_ids_str)
                )
                
                if not rows:
                    return DRFResponse({
                        'error': f'No data found for bank_id: {bank_id} and disbursement_ids: {disbursement_ids_list}',
                        'status': 'error'
                    }, status=status.HTTP_404_NOT_FOUND)
                
                # Process results for specific disbursement(s) - Include ALL fields
                responses = []
                disbursement_summary = {}
                total_count = rows[0][14] if rows else 0  # Get total count from first row
                
                for row in rows:
                    response_data = {
                        'response_id': row[0],
                        'status': row[1],
                        'amount': float(row[2]) if row[2] else None,
                        'response_date_time': row[3].isoformat() if row[3] else None,
                        'promise_date_time': row[4].isoformat() if row[4] else None,
                        'loan_mst_id': row[5],
                        'bank_mst_id': row[6],
                        'wrong_number': row[7],
                        'dispute': row[8],
                        'mode_of_payment': row[9],
                        'customer_name': row[10],
                        'disbursement_id': row[11],
                        'account_bank_id': row[12],
                        'account_loan_id': row[13]
                    }
                    
                    responses.append(response_data)
                    
                    # Build summary by disbursement_id
                    disbursement_id = row[11]
                    if disbursement_id not in disbursement_summary:
                        disbursement_summary[disbursement_id] = {
                            'disbursement_id': disbursement_id,
                            'customer_name': row[10],
                            'response_count': 0,
                            'total_amount': 0.0
                        }
                    
                    disbursement_summary[disbursement_id]['response_count'] += 1
                    if row[2]:  # if amount is not None
                        disbursement_summary[disbursement_id]['total_amount'] += float(row[2])
                
                logger.info(f"Found {len(responses)} responses for bank_id {bank_id} and disbursement_ids {disbursement_ids_list}")
                
                return DRFResponse({
                    'message': f'Found data for bank_id: {bank_id} and disbursement_ids: {disbursement_ids_list}',
                    'data': {
                        'responses': responses,
                        'bank_id': bank_id,
                        'disbursement_ids': disbursement_ids_list,
                        'disbursement_summary': list(disbursement_summary.values()),
                        'total_count': total_count
                    },
                    'count': len(responses),
                    'disbursement_count': len(disbursement_summary),
                    'status': 'success'
                }, status=status.HTTP_200_OK)
                
            else:
                # Query to get all disbursements for the bank with additional summary data
                query = """
                    SELECT DISTINCT
                    as2."DisbursementID",
                    as2."CustomerName",
                    as2."BankMstID",
                    as2."LoanMstID",
                    COUNT(r."ResponseID") as response_count,
                    MAX(r."ResponseDateTime") as latest_response_date,
                    SUM(CASE WHEN r."Amount" IS NOT NULL THEN r."Amount" ELSE 0 END) as total_amount,
                    STRING_AGG(DISTINCT r."Status", ', ') as statuses
                    FROM "AccountSummary" as2
                    LEFT JOIN "Response" r ON r."BankMstID" = as2."BankMstID" 
                        AND r."LoanMstID" = as2."LoanMstID"
                    WHERE as2."BankMstID" = %s
                    GROUP BY as2."DisbursementID", as2."CustomerName", as2."BankMstID", as2."LoanMstID"
                    ORDER BY as2."DisbursementID"
                """
                
                # Execute query with positional parameters
                rows, columns = db_manager.execute_query_with_positional_params(
                    query, (bank_id,)
                )
                
                if not rows:
                    return DRFResponse({
                        'message': f'No disbursements found for bank_id: {bank_id}',
                        'data': {
                            'disbursements': [],
                            'bank_id': bank_id,
                            'summary': {
                                'total_disbursements': 0,
                                'total_responses': 0,
                                'total_amount': 0
                            }
                        },
                        'count': 0,
                        'status': 'success'
                    }, status=status.HTTP_200_OK)
                
                # Process results for all disbursements with full data
                disbursements = []
                total_responses = 0
                total_amount = 0
                
                for row in rows:
                    disbursement_data = {
                        'disbursement_id': row[0],
                        'customer_name': row[1],
                        'bank_mst_id': row[2],
                        'loan_mst_id': row[3],
                        'response_count': row[4] if row[4] else 0,
                        'latest_response_date': row[5].isoformat() if row[5] else None,
                        'total_amount': float(row[6]) if row[6] else 0.0,
                        'statuses': row[7] if row[7] else 'No responses'
                    }
                    
                    disbursements.append(disbursement_data)
                    total_responses += disbursement_data['response_count']
                    total_amount += disbursement_data['total_amount']
                
                # Create summary data
                summary = {
                    'total_disbursements': len(disbursements),
                    'total_responses': total_responses,
                    'total_amount': total_amount
                }
                
                logger.info(f"Found {len(disbursements)} disbursements for bank_id {bank_id}")
                
                return DRFResponse({
                    'message': f'Found {len(disbursements)} disbursements for bank_id: {bank_id}',
                    'data': {
                        'disbursements': disbursements,
                        'bank_id': bank_id,
                        'summary': summary
                    },
                    'count': len(disbursements),
                    'status': 'success'
                }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error in get disbursements: {str(e)}")
            return DRFResponse({
                'error': f'Failed to fetch disbursements: {str(e)}',
                'status': 'error'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        """
        Delete responses based on response IDs OR update promise date
        Request body should contain: 
        - For deletion: {'response_ids': [list of response IDs]}
        - For promise date update: {'response_ids': [list of response IDs], 'promise_date': 'YYYY-MM-DD'}
        """
        try:
            # Get response_ids and promise_date from request body
            response_ids = request.data.get('response_ids', [])
            promise_date = request.data.get('promise_date', None)
            
            if not response_ids:
                return DRFResponse({
                    'error': 'response_ids list is required in request body',
                    'status': 'error'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            if not isinstance(response_ids, list):
                return DRFResponse({
                    'error': 'response_ids must be a list',
                    'status': 'error'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Validate all response_ids are integers
            try:
                response_ids = [int(rid) for rid in response_ids]
            except ValueError:
                return DRFResponse({
                    'error': 'All response_ids must be valid integers',
                    'status': 'error'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Validate promise_date format if provided
            if promise_date:
                try:
                    from datetime import datetime
                    datetime.strptime(promise_date, '%Y-%m-%d')
                except ValueError:
                    return DRFResponse({
                        'error': 'promise_date must be in YYYY-MM-DD format',
                        'status': 'error'
                    }, status=status.HTTP_400_BAD_REQUEST)
            
            # Check if responses exist before operation
            check_query = """
                SELECT "ResponseID", "LoanMstID", "BankMstID" 
                FROM "Response" 
                WHERE "ResponseID" = ANY(%s)
            """
            
            # Use the new method with positional parameters
            existing_rows, _ = db_manager.execute_query_with_positional_params(
                check_query, 
                (response_ids,)
            )
            
            existing_response_ids = [row[0] for row in existing_rows]
            
            if not existing_response_ids:
                operation = "update" if promise_date else "delete"
                return DRFResponse({
                    'message': f'No responses found with the provided IDs for {operation}',
                    'requested_ids': response_ids,
                    f'{operation}_count': 0,
                    'status': 'success'
                }, status=status.HTTP_200_OK)
            
            start_time = time.time()
            
            # Determine operation based on promise_date presence
            if promise_date:
                # UPDATE OPERATION
                logger.info(f"About to update promise date for {len(existing_response_ids)} responses: {existing_response_ids} to {promise_date}")
                
                update_query = """
                    UPDATE "Response" 
                    SET "PromiseDateTime" = %s
                    WHERE "ResponseID" = ANY(%s)
                """
                
                try:
                    # Use the new method with positional parameters
                    result_rows, _ = db_manager.execute_query_with_positional_params(
                        update_query, 
                        (promise_date, existing_response_ids)
                    )
                    updated_count = len(existing_response_ids)  # Assume success if no exception
                except Exception as db_manager_error:
                    logger.warning(f"Positional parameter method failed for update, trying fallback: {str(db_manager_error)}")
                    
                    # Fallback to raw database connection
                    engine = db_manager.get_engine()
                    with engine.begin() as conn:
                        result = conn.execute(
                            text("UPDATE \"Response\" SET \"PromiseDateTime\" = :promise_date WHERE \"ResponseID\" = ANY(:response_ids)"),
                            {'promise_date': promise_date, 'response_ids': existing_response_ids}
                        )
                        updated_count = result.rowcount if hasattr(result, 'rowcount') else len(existing_response_ids)
                
                end_time = time.time()
                
                logger.info(f"Successfully updated promise date for {updated_count} responses in {end_time - start_time:.2f} seconds")
                
                return DRFResponse({
                    'message': f'Successfully updated promise date for {updated_count} responses',
                    'updated_count': updated_count,
                    'updated_response_ids': existing_response_ids,
                    'promise_date': promise_date,
                    'execution_time_seconds': round(end_time - start_time, 2),
                    'status': 'success'
                }, status=status.HTTP_200_OK)
            
            else:
                # DELETE OPERATION (original functionality)
                logger.info(f"About to delete {len(existing_response_ids)} responses: {existing_response_ids}")
                
                delete_query = """
                    DELETE FROM "Response" 
                    WHERE "ResponseID" = ANY(%s)
                """
                
                try:
                    # Use the new method with positional parameters
                    result_rows, _ = db_manager.execute_query_with_positional_params(delete_query, (existing_response_ids,))
                    deleted_count = len(existing_response_ids)  # Assume success if no exception
                except Exception as db_manager_error:
                    logger.warning(f"Positional parameter method failed for delete, trying fallback: {str(db_manager_error)}")
                    
                    # Fallback to raw database connection
                    engine = db_manager.get_engine()
                    with engine.begin() as conn:
                        result = conn.execute(
                            text("DELETE FROM \"Response\" WHERE \"ResponseID\" = ANY(:response_ids)"),
                            {'response_ids': existing_response_ids}
                        )
                        deleted_count = result.rowcount if hasattr(result, 'rowcount') else len(existing_response_ids)
                
                end_time = time.time()
                
                logger.info(f"Successfully deleted {deleted_count} responses in {end_time - start_time:.2f} seconds")
                
                return DRFResponse({
                    'message': f'Successfully deleted {deleted_count} responses',
                    'deleted_count': deleted_count,
                    'deleted_response_ids': existing_response_ids,
                    'execution_time_seconds': round(end_time - start_time, 2),
                    'status': 'success'
                }, status=status.HTTP_200_OK)
                
        except Exception as e:
            logger.error(f"Error in post method: {str(e)}")
            return DRFResponse({
                'error': 'Internal server error occurred',
                'status': 'error'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
@import url("../../../../../index.css");

$light-blue:#E3F5F6;

.templates{
    background-color:$light-blue;
    border-radius: 8px;
    padding: 1rem 2rem;
    margin-top: 1rem;
    gap: 30px;
    
    .text{
        font-size: 14px;
        color: var(--dark-blue);
        font-family: '<PERSON>nit','Courier New', Courier, monospace;
        white-space: nowrap;
    }

    .custom_select{
        width: 100%;
        border-radius: 10px;
        box-shadow: 0px 4px 4px 0px #00000040;

        :global(.ant-select-selector){
            color: var(--dark-blue);
            border:none !important;
            padding: 1.1rem;
            box-shadow: none !important;
            font-family: 'Kanit', 'Courier New', Courier, monospace;
        }     
        :global(.ant-select-selection-item){
            font-weight: 400;
            color: var(--dark-blue);
        }
        :global(.ant-select-selection-placeholder){
            color: #0F205052;
            font-weight: 400;
        }
    }

    .add_button{
        background-color: var(--dark-blue);
        color: white;
        border-radius: 50px;
        padding: 0.1rem;
        cursor: pointer;
        height: 30px;
        width: 30px;
        border: none;
          
        .icon_img{
            img{
                width: 100%;
                object-fit: contain;
            }
        }
    }
}

.create_new_template{
    background-color: var(--dark-blue);
    color: white;
    width: 100%;
    border:none;
    position: absolute;
    bottom: 0;
    left: 0;
    &:hover{
        background-color: var(--dark-blue) !important;
        color: white !important;
    }
}

// Responsive View
@media screen and (max-width:768px) {
    .templates{  
        padding: 0.5rem;
        .text{
            font-size: 11px;
        }
        .container{
            flex-wrap: wrap;
        }
        .custom_select{   
            :global(.ant-select-selection-item){
             font-size: 10px;
            }
            :global(.ant-select-selection-placeholder){
                font-size: 10px;
            }
        }}
    :global(.ant-select-dropdown){
        :global(.ant-select-item-option){
          font-size: 10px;
        }
    }
}
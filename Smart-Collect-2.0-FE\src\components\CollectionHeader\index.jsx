import { Divide<PERSON>, <PERSON>lex, <PERSON>lt<PERSON>, Typography } from "antd";
import { Header } from "antd/es/layout/layout";
import React, { useState } from "react";
import MENU_OPEN_ICON from "../../assets/Images/menu-icon.svg";
import MENU_CLOSE_ICON from "../../assets/Images/cross-icon.svg";
import LOGO_TITLE_ICON from "../../assets/Images/logo-title.png";
import LOGO_ICON from "../../assets/Images/logo.png";
import PROFILE_ICON from "../../assets/Images/profile.svg";
import LOGOUT_ICON from "../../assets/Images/logout.svg";
import CROSS_IMG from "../../assets/Images/cross.svg";
import { SideMenu } from "./SideMenu";
import Style from "./_style.module.scss";
import { handleLogout } from "../../apis/ho-Instance";
import { useNavigate } from "react-router";
import { useTour } from "../../ContextApis/TourProvider";
import PropTypes from "prop-types";

const { Text } = Typography;

export default function CollectionHeader({
  isMenu = true,
  menuItems,
  toggleCollapsedSideMenu,
  collapsedSideMenu,
  setCollapsedSideMenu,
}) {
  const navigate = useNavigate();
  const { menuRef, profileRef, logoutRef } = useTour();
  const [showProfile, setShowProfile] = useState(false);
  const userDetails = JSON.parse(localStorage.getItem("user"));

  const handleLogoClick = () => {
    const user = JSON.parse(localStorage.getItem("user"));
    const designation = user?.designation;

    const routes = {
      HO: "/ho",
      BM: "/ho",
      FO: "/field",
      admin: "/admin",
    };
    navigate(routes[designation], { replace: true });
  };

  return (
    <Header className={Style.app_container_header}>
      <Flex align="center">
        {/* Menu icon */}
        {isMenu && (
          <Flex
            gap={10}
            align="center"
            justify="center"
            className={Style.menu_container}
            onClick={toggleCollapsedSideMenu}
          >
            <img
              ref={menuRef}
              src={collapsedSideMenu ? MENU_OPEN_ICON : MENU_CLOSE_ICON}
              alt="menu-open-icon"
              className={Style.menu_icon}
              style={{
                width: "15px",
              }}
            />

            {/* menu items */}
            <Flex
              className={Style.custom_menubar}
              style={{
                "--container-height": !collapsedSideMenu
                  ? `${menuItems.length * 100}px`
                  : "0px", // Adjust max height as needed
              }}
            >
              {!collapsedSideMenu && (
                <SideMenu
                  menuItems={menuItems}
                  setCollapsedSideMenu={setCollapsedSideMenu}
                />
              )}
            </Flex>
            <Divider type="vertical">Solid</Divider>
          </Flex>
        )}

        {/* Logo & filters */}
        <Flex align="center" justify="space-between" style={{ width: "100%" }}>
          <Flex
            gap={10}
            onClick={handleLogoClick}
            style={{ cursor: "pointer" }}
            className={Style.logo_container}
          >
            <div className={Style.logo}>
              <img src={LOGO_ICON} alt="logo" />
            </div>
            <div className={Style.logo_title}>
              <img src={LOGO_TITLE_ICON} alt="title" />
            </div>
          </Flex>

          <Flex
            align="center"
            justify="end"
            gap={10}
            className={Style.filter_container}
          >
            {/* <Search className={Style.custom_search_input}onSearch={()=>{}} /> */}

            <div
              className={`${isMenu ? Style.menu_header_icon : null} ${
                Style.header_icon
              }`}
              ref={profileRef}
              style={{ position: "relative" }}
            >
              <Tooltip placement="bottom" title={"Profile"}>
                <input
                  type="image"
                  src={PROFILE_ICON}
                  alt="profile"
                  onClick={() => {
                    setShowProfile(true);
                  }}
                  style={{ width: `${isMenu ? "32px" : "22px"}` }}
                />
              </Tooltip>
              {showProfile && (
                <Flex className={Style.modal} vertical>
                  <Flex justify="space-between">
                    <Flex justify="center" style={{ flex: 1 }}>
                      <Text className={Style.text}>Profile</Text>
                    </Flex>
                    <input
                      type="image"
                      src={CROSS_IMG}
                      alt="cross"
                      style={{
                        width: `${isMenu ? "20px" : "10px"}`,
                        cursor: "pointer",
                        background: "transparent",
                      }}
                      onClick={() => setShowProfile(false)}
                    />
                  </Flex>

                  <Divider
                    style={{ backgroundColor: "#0F2050", margin: "1rem 0" }}
                  />

                  <Flex gap={15} vertical>
                    <Flex vertical>
                      <Text className={Style.fields}>Username:</Text>
                      <Text className={Style.value}>
                        {userDetails?.username}
                      </Text>
                    </Flex>

                    <Flex vertical>
                      <Text className={Style.fields}>Designation:</Text>
                      <Text className={Style.value}>
                        {userDetails?.designation}
                      </Text>
                    </Flex>
                  </Flex>
                </Flex>
              )}
            </div>

            <div
              className={`${isMenu ? Style.menu_header_icon : null} ${
                Style.header_icon
              }`}
              ref={logoutRef}
            >
              <Tooltip placement="bottom" title={"Logout"}>
                <input
                  type="image"
                  src={LOGOUT_ICON}
                  alt="logout"
                  onClick={handleLogout}
                  style={{ width: `${isMenu ? "32px" : "22px"}` }}
                />
              </Tooltip>
            </div>
          </Flex>
        </Flex>
      </Flex>
    </Header>
  );
}

CollectionHeader.propTypes = {
  isMenu: PropTypes.bool,
  menuItems: PropTypes.array,
  toggleCollapsedSideMenu: PropTypes.func,
  collapsedSideMenu: PropTypes.bool,
  setCollapsedSideMenu: PropTypes.func,
};

@import url("../../../index.css");
$table-radius: 22px;
$disable: #787777;
$body: #e4f8f9;
$light-blue: #e4f8f9;
$blue: #becfff;

.download_button {
  background-color: var(--dark-blue);
  padding: 0.2rem 1rem;
  border-radius: 10px;
  cursor: pointer;
  margin-right: 1.3rem;
  margin-bottom: 10px;
  border: 0px solid white;
  img {
    width: 20px;
  }
}

.snapshot {
  padding: 1rem 1rem;

  .icon {
    width: 20px;
  }
  .icon_2 {
    width: 15px;
  }

  :global(.ant-segmented) {
    padding: 0.2rem 0.2rem;

    background-color: var(--dark-blue);
    border-radius: 50px;

    :global(.ant-segmented-item) {
      :global(.ant-segmented-item-label) {
        color: #ffffff;
        font-weight: 400;
        font-family: "Kanit", "Courier New", Courier, monospace;
        background-color: transparent;
      }
      &::after {
        background-color: transparent !important;
        display: none;
      }
    }

    :global(.ant-segmented-item-selected) {
      background-color: $light-blue !important;
      color: var(--dark-blue) !important;
      box-shadow: none;
      border-radius: 50px;
      :global(.ant-segmented-item-label) {
        color: var(--dark-blue) !important;
      }
    }
    :global(.ant-segmented-thumb) {
      background-color: $light-blue;
      border-radius: 50px;
    }
  }

  .filter_container {
    border: 1px solid gray;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    cursor: pointer;

    .apply_btn,
    .clear_btn {
      background-color: var(--dark-blue);
      color: white;
      border: none;
      outline: none;
      font-family: "kanit", "Courier New", Courier, monospace;
      font-size: 13px;

      &:hover {
        background-color: var(--dark-blue);
        color: white;
      }
    }

    .clear_btn {
      background-color: white;
      color: var(--dark-blue);
      border: 1px solid var(--dark-blue);

      &:hover {
        color: var(--dark-blue);
        background-color: white;
        border: 1px solid var(--dark-blue);
      }
    }
  }

  .select_title {
    white-space: nowrap;
    font-family: "kanit", "Courier New", Courier, monospace;
  }

  .custom_select {
    width: 100%;
    :global(.ant-select-selector) {
      background-color: $light-blue;
      box-shadow: none !important;
      border: none;
      outline: none;
      font-family: "Kanit", "Courier New", Courier, monospace;
    }
    :global(.ant-select-selection-item) {
      font-weight: 400;
      color: var(--dark-blue);
      font-family: "Kanit", "Courier New", Courier, monospace;
    }
  }
}

.title {
  font-family: "Kanit", "Courier New", Courier, monospace;
  font-weight: 400;
  font-size: 16px;
}

.CustomTable {
  padding-top: 1rem;

  .text {
    font-weight: 500;
    white-space: nowrap;
    color: var(--dark-blue);
  }

  .blueText {
    font-weight: 400;
    color: var(--blue);
    cursor: pointer;
  }

  .button {
    border: none;
    background-color: transparent;
    color: var(--blue);
    font-weight: 700;
    cursor: pointer;
  }

  .customer_count {
    border-radius: 2px;
    background-color: white;
    border: 1px solid #305496;
    padding: 0rem 0.5rem;
  }

  // Global ant design classes
  // Pagination
  :global(.ant-pagination) {
    justify-content: center;
    margin: 0 !important;

    :global(.ant-pagination-prev),
    :global(.ant-pagination-next) {
      color: var(--dark-blue);
      border: 0 solid var(--blue);
      background: #e4f8f9;
      height: 25px;
      min-width: 15px;
      border-radius: 0px;
      margin: 0;

      button {
        border-radius: 0px;
      }
    }

    :global(.ant-pagination-item) {
      margin-right: 0;
      height: 0;

      a {
        color: $disable;
        font-size: 0.9rem;
        line-height: 23px;
      }

      &:hover {
        background-color: transparent;
      }
    }

    :global(.ant-pagination-item-active) {
      border: none;

      a {
        color: var(--dark-blue);
        font-size: 1.2rem;
        padding: 0;
      }
    }
  }

  // Table container
  :global(.ant-table-container) {
    padding: 0 1rem 0.5rem 1rem;
    margin-bottom: 0.5rem;
    background: var(--light-green);
    border-start-start-radius: $table-radius;
    border-start-end-radius: $table-radius;
    box-shadow: 2px 2px 2px 0px #00000013;

    // Table header
    :global(.ant-table-header) {
      position: relative;
      margin: 0 auto;
      top: -21px;
      border-radius: $table-radius;
      :global(.ant-table-column-has-sorters) {
        background-color: var(--dark-blue);
        &:hover {
          background-color: var(--dark-blue);
        }

        :global(.ant-table-column-sorter-up),
        :global(.ant-table-column-sorter-down) {
          svg {
            fill: white;
          }
          &:global(.active) {
            svg {
              fill: rgb(24, 155, 249);
            }
          }
        }
      }
    }
    // Table virtual body
    :global(.ant-table-tbody-virtual) {
      margin-top: -8px;
      position: static !important;

      &::-webkit-scrollbar {
        display: none;
      }
    }

    &::before {
      box-shadow: none !important;
    }
  }

  // Table rows header
  :global(.ant-table-thead > tr) {
    th {
      border-start-end-radius: 0 !important;
      background-color: var(--dark-blue);
      border-inline-end: none !important;
      color: white;
      border-bottom: none;
      white-space: nowrap;
      text-align: center;
      font-family: "Kanit", "Courier New", Courier, monospace;
      font-weight: normal;

      &:not(:last-child):not(.ant-table-selection-column):not(
          .ant-table-row-expand-icon-cell
        ):not([colspan])::before {
        width: 0;
      }
    }
  }

  // Table body
  :global(.ant-table-tbody) {
    // Body rows
    :global(.ant-table-row) {
      // Cols
      :global(.ant-table-cell) {
        font-weight: 400;
        text-align: center;
        padding: 10px;
        font-family: "Kanit", "Courier New", Courier, monospace;
        border: none;

        :global(.ant-typography) {
          font-family: "Kanit", "Courier New", Courier, monospace;
          color: #305496;
        }

        &:first-child {
          border-right: none;
        }

        &:nth-child(2) {
          border-right: none;
        }

        &:global(.ant-table-cell-row-hover) {
          background-color: $body;
        }
      }
    }
  }

  // Fixed Cols
  :global(.ant-table-cell-fix-right) {
    background-color: $body;
  }

  // Footer
  :global(.ant-table-summary) {
    text-align: center;
    box-shadow: 0px 4px 4px 0px #0000001a inset;
    border-radius: 20px;

    :global(.ant-table-cell) {
      font-family: "Kanit", "Courier New", Courier, monospace;
      font-weight: 300;
      padding: 0.6rem;
    }
  }
}

.donut_chart_container {
  background-color: $light-blue;
  padding: 20px;
  border-radius: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);

  :global(.apexcharts-legend-text) {
    font-size: 20px;
  }
}

import pandas as pd
from datetime import datetime
import numpy as np
from ..utils import *
from ..queries import insert_data_in_raw_table,get_all_previous_recovery_data_from_rawtable,update_sindhudhurg_bank_data


def process_sindhudhurg_raw_data(df1):
    db_operation_success = False
    db_error_message = None
    
    # Clean column names
    df1.columns = df1.columns.str.strip().str.replace(r'\s+', ' ', regex=True)
    
    # Payment frequency mapping
    payment_freq_mapping = {
        'Y': 365,    # Yearly
        'M': 30,     # Monthly
        'Q': 90,     # Quarterly
        'U': 0,      # U
        'CC': 0      # Cash-Credit
    }
    
    # Initialize lists to collect records
    rawfile_records = []
    records_with_guarantors = 0
    records_without_guarantors = 0

    # # Get all previous overdue amounts at once for efficiency
    # previous_overdue_dict = get_all_previous_recovery_data_from_rawtable(30)
    
    # def calculate_collected_amount(row):
    #     """
    #     Calculate collected amount = Previous overdue - Current overdue
    #     Fixed to handle None values properly using pd.to_numeric
    #     """
    #     key = (row['customerid'], row['disbursementid'])
    #     previous_recovery = previous_overdue_dict.get(key)
    #     current_recovery = row.get('collectedamount')
        
    #     # Use pd.to_numeric with errors='coerce' to handle None/invalid values
    #     previous_recovery = pd.to_numeric(previous_recovery, errors='coerce') or 0
    #     current_recovery = pd.to_numeric(current_recovery, errors='coerce') or 0
        
    #     collected = max(0, current_recovery - previous_recovery)
    #     print('current recovery:', current_recovery, '-', 'previous recovery: ', previous_recovery, ' = ', collected)
    #     return collected
    
    # Process each row in the input dataframe
    for index, row in df1.iterrows():
        # Calculate overdue amount as overdue - recovery with proper null handling
        # overdue_amount = pd.to_numeric(row.get('OVERDUEAMOUNT', 0), errors='coerce')
        # recovery_amount = pd.to_numeric(row.get('RECOVERY', 0), errors='coerce')
        
        # # Handle None values
        # if pd.isna(overdue_amount) or overdue_amount is None:
        #     overdue_amount = 0
        # if pd.isna(recovery_amount) or recovery_amount is None:
        #     recovery_amount = 0
            
        # calculated_overdue = max(0, float(overdue_amount) - float(recovery_amount))  # Ensure non-negative
        
        # Create base record for each row with improved null handling
        base_record = {
            'customerid': row.get('CUSTOMERID', ''),
            'disbursementid': row.get('DISBURSEMENTID', ''),
            'branchcode': row.get('BRANCHCODE', ''),
            'branchname': str(row.get('BRANCHNAME', '')).strip(),
            'loantype': str(row.get('LOANPRD', '')).upper().strip(),
            'customername': str(row.get('CUSTOMERNAME', '')).strip(),
            'mobileno': clean_mobile_number(row.get('MOBILENO', '')),
            'overdueamount': row.get('CURERNT OD'),
            'disbursementamount': pd.to_numeric(row.get('SANCTIONAMOUNT', 0), errors='coerce') or 0,
            'disbursementdate': convert_excel_serial_date(row.get('INSTSTARTDATE')),
            'emiamount': pd.to_numeric(row.get('EMIAMOUNT', 0), errors='coerce') or 0,
            'rateofinterest': pd.to_numeric(row.get('RATEOFINTEREST', 0), errors='coerce') or 0,
            'repaymenttenure': pd.to_numeric(row.get('LOANTENURE', 0), errors='coerce') or 0,
            'principlepending': pd.to_numeric(row.get('PRINCIPALOVERDUE', 0), errors='coerce') or 0,
            'extracolumn1': row.get('ASSETCODESTATUS', ''),
            'paymentfrequency': payment_freq_mapping.get(row.get('PAYMENTFREEQUENCY', ''), 0),
            'numberofdayspastdue': pd.to_numeric(row.get('NUMBEROFDAYSPASTDUE', 0), errors='coerce') or 0,
            'inststartdate': convert_excel_serial_date(row.get('INSTSTARTDATE')),
            'applicanttype': row.get('APPLICANTTYPE', ''),
            'totaloutstanding': abs(pd.to_numeric(row.get('TOTALOUTSTANDING', 0), errors='coerce') or 0),
            'bankmstid': 30,
            'inserted_date': datetime.now().date(),
            'guarantor': None,
            'guarantor_mobile': None,
            'secondary_user_type': None,  # Initialize as None
            'extracolumn2': row.get('PAYMENTFREEQUENCY', ''),
            'collectedamount':row.get('RECOVERY'),
            'extracolumn3':row.get('OVERDUEAMOUNT'),
            'collectiondate': datetime.now().date() - pd.Timedelta(days=1),
            'extracolumn4':row.get('RECOVERY'),
            'extracolumn5':row.get('CURERNT OD')
        }
        
        # Calculate collected amount for this record
        # base_record['collectedamount'] = calculate_collected_amount(base_record)
        
        # Process guarantors with improved null checking
        guarantors = []
        if (pd.notna(row.get('GURANTOR1')) and str(row.get('GURANTOR1', '')).strip() and 
            pd.notna(row.get('MOBGURANTOR1')) and 
            row.get('MOBGURANTOR1') is not None):
            guarantors.append({
                'name': str(row.get('GURANTOR1', '')).strip(),
                'mobile': clean_mobile_number(row.get('MOBGURANTOR1', '')),
                'id': row.get('CUSTGURANTOR1', '')
            })
        
        if (pd.notna(row.get('GURANTOR2')) and str(row.get('GURANTOR2', '')).strip() and 
            pd.notna(row.get('MOBGURANTOR2')) and 
            row.get('MOBGURANTOR2') is not None):
            guarantors.append({
                'name': str(row.get('GURANTOR2', '')).strip(),
                'mobile': clean_mobile_number(row.get('MOBGURANTOR2', '')),
                'id': row.get('CUSTGURANTOR2', '')
            })
        
        # Add records with guarantors
        if guarantors:
            for guarantor in guarantors:
                record = base_record.copy()
                record['guarantor'] = guarantor['name']
                record['guarantor_mobile'] = guarantor['mobile']
                record['guarantorid'] = guarantor['id']
                record['secondary_user_type'] = 'guarantor'  # Set to 'guarantor' for records with guarantors
                rawfile_records.append(record)
            records_with_guarantors += len(guarantors)
        else:
            # Add record without guarantor
            rawfile_records.append(base_record)
            records_without_guarantors += 1
    
    # Convert list of records to DataFrame
    df = pd.DataFrame(rawfile_records)
    
    # Save to Excel file for debugging (optional)
    # df.to_excel("sindhudhurg.xlsx", index=False)
    
    # Database operation
    result = {'success': False, 'message': '', 'inserted_count': 0, 'duplicate_count': 0}
    try:
        result = insert_data_in_raw_table(df, bank_id=30)
        db_operation_success = result['success']
        if not result['success']:
            db_error_message = result['message']
    except Exception as e:
        db_operation_success = False
        db_error_message = str(e)
        print(f"Database update failed for sindhudhurg data: {e}")
    
    # Initialize update result
    update_result = {'success': False, 'updated_rows': 0, 'message': 'Update not attempted'}
    
    # If database insertion was successful, run the update function
    # if db_operation_success:
    #     try:
    #         today_date = datetime.now().strftime('%Y-%m-%d')
    #         print(f"DEBUG: Running update_sindhudhurg_bank_data for date: {today_date}")
    #         update_result = update_sindhudhurg_bank_data(today_date)
    #         print(f"DEBUG: Update result: {update_result}")
    #     except Exception as e:
    #         update_result = {
    #             'success': False,
    #             'updated_rows': 0,
    #             'message': f'Error during update: {str(e)}'
    #         }
    #         print(f"ERROR during update_sindhudhurg_bank_data: {e}")
    
    return {
        "Total records processed": len(df),
        "New records inserted": result.get('inserted_count', 0) if db_operation_success else 0,
        "Duplicate records found": result.get('duplicate_count', 0) if db_operation_success else 0,
        "db_operation_success": db_operation_success,
        "Records with guarantors": records_with_guarantors,
        "Records without guarantors": records_without_guarantors,
        "db_error_message": db_error_message,
        "message": result.get('message', '') if db_operation_success else db_error_message,
        "update_operation_success": update_result['success'],
        "update_rows_affected": update_result['updated_rows'],
        "update_message": update_result['message']
    }
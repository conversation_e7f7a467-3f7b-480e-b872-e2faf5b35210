import { Flex, Space, Typography } from 'antd'
import React from 'react'
import Style from "./_detail-card.module.scss";
import CircularProgress from '../CircularProgress';
import PropTypes from 'prop-types';
import { formatAmount } from '../../../constant';

const {Text, Title} = Typography;
export function DetailsCard({
cardName, 
customerCount, 
totalAmount, 
amountCollected, 
percentValue, 
image}) {
return (
  <Flex gap={10} className={Style.detail_container} >
    <div className={Style.image_container}>
       <img src={image} alt={cardName}/>
    </div>

    <Flex vertical gap={30} style={{flex:1}}>
      <Flex justify='space-between'>
        <Space direction='vertical'>
          <Title level={2} className={Style.name}>{cardName}</Title>
          <Text className={Style.amount_text}>Total customers:<span className={Style.amount}>{customerCount}</span></Text>
        </Space>
        <CircularProgress percentage={percentValue} duration={1000}/>
      </Flex>
            
      <Flex 
        justify='space-between'
        align='center'
        gap={20} 
        className={Style.amount_details}>
          <Flex vertical gap={10}>
            <Title level={3} className={Style.amount}>Rs.{formatAmount(totalAmount)}</Title>
            <Text className={Style.amount_text}> Total Amount</Text>
          </Flex>

          <Flex vertical gap={10} align='center'>
            <Title level={3} className={Style.amount}>Rs.{formatAmount(amountCollected)}</Title>
            <Text className={Style.amount_text}> Amount Collected</Text>
          </Flex>
      </Flex>
    </Flex>
          
  </Flex>
  )
}

DetailsCard.propTypes ={
  cardName: PropTypes.string,
  customerCount: PropTypes.oneOfType([
    PropTypes.string, PropTypes.number
  ]),
  totalAmount: PropTypes.oneOfType([
    PropTypes.string, PropTypes.number
  ]),
  amountCollected:  PropTypes.oneOfType([
    PropTypes.string, PropTypes.number
  ]),
  percentValue: PropTypes.oneOfType([
    PropTypes.string, PropTypes.number
  ]),
  image: PropTypes.string
}
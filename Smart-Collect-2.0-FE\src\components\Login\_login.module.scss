@import url("https://fonts.googleapis.com/css2?family=Goldman:wght@400;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Goudy+Bookletter+1911&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Kanit:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

:root {
  --font-size-base: clamp(14px, 2vw, 18px); /* Base font size */
  --spacing-unit: clamp(8px, 2vw, 16px); /* Base spacing unit */
  --max-card-width: 900px; /* Max card width */
  --image-container-width: 50%; /* Width of the image container */
}

.container {
  width: 100%;
  height: 100vh;
  display: flex;
  font-family: "Kanit", "Courier New", Courier, monospace, serif;
  overflow: hidden;
  position: relative;

  .backgroundCircle {
    position: absolute;
    width: clamp(300px, 40vw, 90vh);
    height: 90vh;
    border-radius: 50%;
    background-color: rgba(181, 204, 255, 1);
    top: 50%;
    right: calc(
      31.5% - 50vh
    ); /* Adjust this to position it slightly behind imageContainer */
    transform: translateY(-50%);
    z-index: -1; /* Ensure it's behind imageContainer */
  }
  .imageContainer {
    width: clamp(350px, 60vw, 130vh);
    height: 130vh;
    border-radius: 50%;
    background-color: #0f2050;
    position: absolute;
    top: 50%;
    right: calc(10% - 50vh); /* Pull the circle beyond the right edge */
    transform: translateY(-50%);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;

    .power_by {
      display: flex;
      justify-content: center;
      width: 100%;
      padding-right: 1vw;
      img {
        width: clamp(350px, 25vw, 600px);
        object-fit: contain;
      }
    }
  }

  /* left Side with Login Form */
  .leftSide {
    width: 50%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    /* Header */
    .header {
      display: flex;
      justify-content: center;
      align-items: center;
      .logo_container {
        display: flex;
        align-items: center;
        gap: 2px;

        .logo_image {
          width: clamp(100px, 12vw, 100px);
        }

        .logo_title_image {
          width: clamp(240px, 32vw, 260px);
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
    }

    /* Login Card */
    .cardWrapper {
      width: 500px;
      max-height: 450px;
      text-align: center;
      border: 1.5px solid rgba(2, 36, 113, 1);
      border-radius: 10px;
      box-shadow: 5.17552px 5.17552px 5.17552px rgba(0, 0, 0, 0.25);

      .card {
        width: 100%;
        border: none;
        background: transparent;
        font-family: "Kanit", "Courier New", Courier, monospace, serif;

        p {
          color: rgba(105, 122, 173, 1);
          font-size: var(--font-size-base);
          text-decoration: none;
        }

        .inputContainer {
          margin-bottom: var(--spacing-unit);
          padding-top: var(--spacing-unit);

          .label {
            position: absolute;
            margin-top: calc(-0.7 * var(--spacing-unit));
            left: calc(2 * var(--spacing-unit));
            background: white;
            padding: 0 calc(0.5 * var(--spacing-unit));
            font-size: clamp(12px, 2vw, 14px);
            color: rgba(2, 36, 113, 1);
            z-index: 100;
          }
        }

        .forgotPassword {
          display: block;
          margin-top: calc(-1 * var(--spacing-unit));
          text-align: right;
          padding-bottom: calc(1 * var(--spacing-unit));
          color: #000;
          font-size: clamp(12px, 2vw, 14px);
        }

        .button {
          font-size: var(--font-size-base);
          background-color: #0f2050;
          border-color: #0f2050;
          border-radius: 8px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
          width: 100%;
          padding: calc(1.2 * var(--spacing-unit));
        }

        :global(.ant-card-head-title) {
          font-family: "Kanit", "Courier New", Courier, monospace, serif !important;
          border-bottom: none !important;
          font-size: 1.5em;
          text-underline-offset: 4px;
          font-weight: 400;
          margin-top: calc(2 * var(--spacing-unit));
        }

        :global(.ant-card-head) {
          border-bottom: none !important;
        }

        :global(.ant-card-body) {
          display: flex;
          flex-direction: column;
          justify-content: center;
        }

        .form {
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          height: 220px;
        }
      }
    }
  }
  .websiteLink {
    position: absolute;
    bottom: 10px;
    left: 25px;
    font-size: clamp(13px, 3vw, 15px);
    color: rgba(15, 32, 80, 1);
    font-family: "Kanit", "Courier New", Courier, monospace, sans-serif;
    z-index: 100;
    text-decoration: underline;
  }

  .inputContainer {
    :global(.ant-input),
    :global(.ant-input-password) {
      width: 100%;
      padding: 0.6rem;
      font-size: clamp(12px, 2vw, 14px);
      border: 1.5px solid rgba(2, 36, 113, 1) !important;
      border-radius: 8px !important;
      &:hover {
        border-color: rgba(2, 36, 113, 1);
      }

      &:focus {
        border-color: #000;
        box-shadow: none;
      }
    }
    :global(.ant-input-password) {
      padding: 0rem 0.5rem 0 0;
      background-color: white !important;
      input {
        border: none !important;
      }
      :global(.ant-input-suffix) {
        svg {
          fill: #0f2050;
        }
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .container {
    .backgroundCircle,
    .imageContainer {
      display: none;
    }
    .leftSide {
      width: 100%;

      .header {
        .logo_container {
          .logo_image {
            width: 30px;
          }
          .logo_title_image {
            width: 150px;
          }
        }
      }

      .cardWrapper {
        width: 90%;
        padding: 0.1rem;

        .card {
          :global(.ant-card-head-title) {
            font-size: 20px;
          }
          p {
            font-size: 10px;
          }
        }
      }
    }
  }
}

@import url("../../../index.css");

$light-blue: #defbff;
$table-radius: 22px;
$disable: #787777;
$body: #e4f8f9;
$blue: #becfff;

.communication_history {
  .heading {
    font-family: "kanit", "Courier New", Courier, monospace;
    color: var(--dark-blue);
  }

  .img_container {
    img {
      object-fit: contain;
      width: 100%;
    }
  }

  .card_container {
    background-color: $light-blue;
    border-radius: 8px;
    width: 180px;
    padding: 1.3rem 0.9rem;
    box-shadow: 2px 2px 2px 0px #00000040;

    .count,
    .title {
      color: #1d3261;
      font-family: "kanit", "Courier New", Courier, monospace;
      font-size: 14px;
    }
    .count {
      font-weight: 500;
    }

    &.card_title_container {
      padding: 0.8rem 0.9rem;
    }
  }

  .divider_container {
    border-inline-start: 2px solid #d3d3d3;
    height: 2rem;
    top: 1rem;
  }

  .chart_container {
    background: $light-blue;
    padding: 1rem 1rem 1rem;
    box-shadow: 2px 2px 2px 0px #00000040;
    border-radius: 10px;

    .heading {
      color: #407bff;
      font-family: "kanit", "Courier New", Courier, monospace;
    }
  }

  .chart {
    position: relative; /* Add this to make it a positioning context */
    width: 100%;
    height: 330px;

    :global(.apexcharts-canvas) {
      :global(.apexcharts-pie-series) {
        path {
          transition: transform 0.2s ease-in-out;
          transform-origin: center;
          &:hover {
            transform: scale(1.04);
          }
        }
      }
    }

    .chart_overlay {
      position: absolute;
      top: 45%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 150px; /* Reduced from 160px for better fit */
      height: 150px;
      background-color: #fff;
      border-radius: 50%;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 2;

      img {
        max-width: 80%;
        max-height: 80%;
        object-fit: contain;
      }
    }
  }

  .btn {
    background-color: var(--dark-blue) !important;
    color: white !important;
    border: none;
    outline: none;
    border-radius: 50px;
    font-family: "kanit", "Courier New", Courier, monospace;
  }

  .back_btn {
    color: var(--dark-blue);
    font-family: "kanit", "Courier New", Courier, monospace;
    font-weight: 400;
    line-height: 0;
  }

  // table
  .custom_table {
    padding-top: 1.5rem;

    .text {
      font-weight: 500;
      white-space: nowrap;
      color: var(--dark-blue);
    }
    .blueText {
      font-weight: 700;
      color: var(--blue);
      cursor: pointer;
    }

    //Global ant design classes
    // Pagination
    :global(.ant-pagination) {
      justify-content: center;
      margin: 0 !important;

      :global(.ant-pagination-prev),
      :global(.ant-pagination-next) {
        color: var(--dark-blue);
        border: 0 solid var(--blue);
        background: #e4f8f9;
        height: 25px;
        min-width: 15px;
        border-radius: 0px;
        margin: 0;
        button {
          border-radius: 0px;
        }
      }
      :global(.ant-pagination-item) {
        margin-right: 0;
        height: 0;
        a {
          color: $disable;
          font-size: 0.9rem;
          line-height: 23px;
          font-weight: normal;
          font-family: "Kanit", "Courier New", Courier, monospace;
        }
        &:hover {
          background-color: transparent;
        }
      }
      :global(.ant-pagination-item-active) {
        border: none;
        a {
          color: var(--dark-blue);
          font-size: 1.2rem;
          padding: 0;
        }
      }
    }

    //Table container
    :global(.ant-table-container) {
      padding: 0 1rem;
      margin-bottom: 0.5rem;
      background: var(--light-green);
      border-start-start-radius: $table-radius;
      border-start-end-radius: $table-radius;

      // Table header
      :global(.ant-table-header) {
        position: relative;
        margin: 0 auto;
        top: -21px;
        border-radius: $table-radius;
        :global(.ant-table-column-has-sorters) {
          background-color: var(--dark-blue);
          &:hover {
            background-color: var(--dark-blue);
          }

          :global(.ant-table-column-sorter-up),
          :global(.ant-table-column-sorter-down) {
            svg {
              fill: white;
            }
            &:global(.active) {
              svg {
                fill: rgb(24, 155, 249);
              }
            }
          }
        }
      }

      // Table virtual body
      :global(.ant-table-tbody-virtual) {
        margin-top: -8px;
      }

      &::before {
        box-shadow: none !important;
      }
    }

    // Table rows header
    :global(.ant-table-thead > tr) {
      th {
        border-start-end-radius: 0 !important;
        border-inline-end: none !important;
        background-color: var(--dark-blue);
        color: white;
        border-bottom: none;
        text-align: center;
        font-family: "Kanit", "Courier New", Courier, monospace;
        font-weight: normal;
        padding: 10px;
        &:not(:last-child):not(.ant-table-selection-column):not(
            .ant-table-row-expand-icon-cell
          ):not([colspan])::before {
          width: 0;
        }
      }
    }

    // Table body
    :global(.ant-table-tbody) {
      // Body rows
      :global(.ant-table-row) {
        // Cols
        :global(.ant-table-cell) {
          text-align: center;
          border-bottom: 2px solid white;
          border-inline-end: 0px;
          font-family: "Kanit", "Courier New", Courier, monospace;
          font-weight: normal;
          padding: 10px;

          :global(.ant-typography) {
            font-weight: 400;
            font-family: "Kanit", "Courier New", Courier, monospace;
            cursor: pointer;
          }
          &:global(.ant-table-cell-row-hover) {
            background-color: transparent;
          }
        }
      }
    }
  }

  .download_button {
    background-color: var(--dark-blue);
    padding: 0.2rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    border: none;
    img {
      width: 20px;
    }
  }

  // Segment style
  :global(.ant-segmented) {
    padding: 0.2rem 0.2rem;
    background-color: #dde8ff !important;
    border-radius: 50px !important;
    box-shadow: 3px 2px 2px 0px #00000040;

    :global(.ant-segmented-item) {
      :global(.ant-segmented-item-label) {
        color: var(--dark-blue);
        font-weight: 400;
        font-family: "Kanit", "Courier New", Courier, monospace;
        background-color: transparent;
      }
      &::after {
        background-color: transparent !important;
        display: none;
      }
    }

    :global(.ant-segmented-item-selected) {
      background-color: var(--dark-blue) !important;
      color: white !important;
      box-shadow: none;
      border-radius: 50px;
      :global(.ant-segmented-item-label) {
        color: white !important;
      }
    }
    :global(.ant-segmented-thumb) {
      background-color: #dde8ff;
      border-radius: 50px;
    }
  }
}

@media screen and (max-width: 568px) {
  :global(.apexcharts-canvas) {
    width: 100% !important;
  }
}

import { Flex, Input, Typography, Button, Segmented, <PERSON>, Col } from "antd";
import React, { useEffect, useState } from "react";
import { useParams } from "react-router";
import AppHeader from "../../../AppHeader";
import { AXIOS } from "../../../../apis/ho-Instance";
import Style from "./_style.module.scss";
import ApplicationLoader from "../../../ApplicationLoader";
import { handleDateFormatter } from "../../../../constant";
import { TemplateScripting } from "../Campaign/Template/TemplateScripting";

const { Text } = Typography;
export default function NormalCampaignView() {
  const params = useParams();
  const [campaignData, setCampaignData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [viewTemplates, setViewTemplates] = useState([]);
  const [selectedTemplateIndex, setSelectedTemplateIndex] = useState(null);
  const CampaignMstId = Number(localStorage.getItem("campaignId"));

  const getLabel = (filter) => {
    if (filter.comparisonType === "columns") return "Column:";
    if (filter.comparison === "range") return "Range:";
    if (filter.comparison === "list") return "List:";
    return "Value:";
  };

  const handleGetCampaignDetails = async (id) => {
    setIsLoading(true);
    try {
      const res = await AXIOS.get(`v1/getcampaigns/${id}/`);
      if (res.status === 200) {
        setCampaignData(res.data);
      }
    } catch (error) {
      console.log("Error in get campaign details", error?.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle Fetch Whatsapp Scripts
  const handleWhatsApp = (flowData) => {
    return flowData?.whatsappflowmapping_set.map((flow) => {
      let templateBody = flow.WhatsAppTemplateMappingID.TemplateBody;
      let variableMappings =
        flow.WhatsAppTemplateMappingID.whatsappvariablemapping_set;

      // Create a mapping of VariableNo to VariableField
      let variableMap = {};
      variableMappings.forEach(({ VariableNo, VariableField }) => {
        variableMap[VariableNo] = VariableField;
      });

      // Replace {1}, {2}, etc., with actual field names
      templateBody = templateBody.replace(/\{(\d+)\}/g, (match, variableNo) => {
        return variableMap[variableNo] || match; // Replace if found, else keep original
      });

      return { templateBody, variableMappings };
    });
  };

  // Handle Fetch IVR Scripts
  const handleFetchIVR = (flowData) => {
    return flowData?.ivrflowmapping_set.map((flow) => {
      let templateBody = flow.IVRTemplateMappingID.TemplateBody;
      let variableMappings = flow.IVRTemplateMappingID.ivrvariablemapping_set;

      // Create a mapping of VariableNo to VariableField
      let variableMap = {};
      variableMappings.forEach(({ VariableNo, VariableField }) => {
        variableMap[VariableNo] = VariableField;
      });

      // Replace {1}, {2}, etc., with actual field names
      templateBody = templateBody.replace(/\{(\d+)\}/g, (match, variableNo) => {
        return variableMap[variableNo] || match; // Replace if found, else keep original
      });

      return { templateBody, variableMappings };
    });
  };

  // Handle Fetch Whatsapp Scripts
  const handleFetchAI = (flowData) => {
    let StartMsg = flowData.StartMsg;
    let EndMsg = flowData.EndMsg;
    let templateBody = flowData.TemplateBody;
    let variableMappings = flowData.voicebotusertemplate_set;

    // Create a mapping of VariableNo to VariableField
    let variableMap = {};
    variableMappings.forEach(({ VariableNumber, VariableField }) => {
      variableMap[VariableNumber] = VariableField;
    });

    // Replace {1}, {2}, etc., with actual field names
    templateBody = templateBody.replace(
      /\{(\d+)\}/g,
      (match, VariableNumber) => {
        return variableMap[VariableNumber] || match; // Replace if found, else keep original
      }
    );
    let formattedTemplateBody = `
            ${StartMsg}
            ${templateBody}
            ${EndMsg}`;
    return [{ templateBody: formattedTemplateBody, variableMappings }];
  };

  // Handle Fetch Whatsapp Scripts
  const handleFetchBlaster = (flowData) => {
    let templateBody = flowData.TemplateBody;
    let variableMappings = flowData.blasterusertemplate_set;

    // Create a mapping of VariableNo to VariableField
    let variableMap = {};
    variableMappings.forEach(({ VariableNumber, VariableField }) => {
      variableMap[VariableNumber] = VariableField;
    });

    // Replace {1}, {2}, etc., with actual field names
    templateBody = templateBody.replace(
      /\{(\d+)\}/g,
      (match, VariableNumber) => {
        return variableMap[VariableNumber] || match; // Replace if found, else keep original
      }
    );
    return [{ templateBody, variableMappings }];
  };

  // Fetch scripts
  const handleViewTemplate = ({ communicationType, flowData, index }) => {
    if (selectedTemplateIndex === index) {
      setSelectedTemplateIndex(null); // Hide template if it's already open
      return;
    }
    setSelectedTemplateIndex(index);
    let details = [];
    if (communicationType === "whatsapp") {
      details = handleWhatsApp(flowData);
    } else if (communicationType === "ai") {
      details = handleFetchAI(flowData);
    } else if (communicationType === "blaster") {
      details = handleFetchBlaster(flowData);
    } else if (communicationType === "ivr") {
      details = handleFetchIVR(flowData);
    } else {
      setViewTemplates([]);
    }
    setViewTemplates(details);
  };

  useEffect(() => {
    handleGetCampaignDetails(CampaignMstId);
  }, [params]);

  return (
    <Flex vertical gap={10}>
      <AppHeader title={params.campaignName || "View Campaign"} />

      {isLoading ? (
        <ApplicationLoader />
      ) : (
        <Flex
          className={Style.campaign_container}
          vertical
          style={{ paddingTop: "2rem" }}
        >
          {/* Campaign details */}
          <Flex
            justify="space-between"
            align="center"
            gap={15}
            className={Style.campaign_details}
          >
            {/* Name */}
            <Flex
              gap={10}
              direction="vertical"
              className={Style.input_container}
            >
              <Text className={Style.text}>Campaign Name:</Text>
              <Input
                className={Style.custom_input}
                placeholder="Enter"
                value={campaignData?.Name || "Campaign Name"}
                disabled={true}
              />
            </Flex>

            {/* Priority */}
            <Flex direction="vertical" className={Style.input_container}>
              <Text className={Style.text}>Campaign Priority:</Text>
              <Input
                className={`${Style.custom_input} ${Style.priority_input}`}
                value={campaignData?.Priority || 0}
                onChange={(e) => {}}
                disabled={true}
              />
            </Flex>

            {/* Type */}
            <Flex direction="vertical" className={Style.input_container}>
              <Text className={Style.text}>Campaign Type:</Text>
              <Input
                className={`${Style.custom_input} ${Style.priority_input}`}
                value={campaignData?.CampaignType || "Normal"}
                onChange={(e) => {}}
                disabled={true}
              />
            </Flex>
          </Flex>

          {/* All filters */}
          <Flex className={Style.container} vertical>
            {/* Category Section */}
            <Flex
              direction="horizontal"
              align="middle"
              className={Style.section}
            >
              <Text className={Style.text}>Category</Text>
            </Flex>
            {campaignData?.filtermst_set?.length ? (
              campaignData?.filtermst_set.map(({ FiltersID }, index) => (
                <Flex
                  direction="horizontal"
                  gap={10}
                  className={Style.row}
                  key={`${index}-${FiltersID?.Comparison}`}
                >
                  <Flex
                    direction="vertical"
                    className={Style.input_container_small}
                  >
                    <Text className={Style.text}>Parameter Name:</Text>
                    <Input
                      className={Style.custom_input_small}
                      value={
                        FiltersID?.Column?.replace(
                          /([a-z])([A-Z])/g,
                          "$1 $2"
                        ) || ""
                      }
                      readOnly
                      disabled={true}
                    />
                  </Flex>
                  <Flex
                    direction="vertical"
                    className={Style.input_container_small}
                  >
                    <Text className={Style.text}>Comparison:</Text>
                    <Input
                      className={Style.custom_input_small}
                      value={FiltersID?.Comparison || ""}
                      readOnly
                      disabled={true}
                    />
                  </Flex>
                  {!["in", "not in"].includes(FiltersID?.Comparison) && (
                    <Flex
                      direction="vertical"
                      className={Style.input_container_small}
                    >
                      <Text className={Style.text}>Comparison Type:</Text>
                      <Input
                        className={Style.custom_input_small}
                        value={FiltersID?.ValueType || "Value"}
                        readOnly
                        disabled={true}
                      />
                    </Flex>
                  )}
                  <Flex
                    gap={10}
                    direction="vertical"
                    className={Style.input_container_small}
                  >
                    <Text className={Style.text}>{getLabel(FiltersID)}</Text>
                    {FiltersID?.Comparison === "range" && (
                      <>
                        <Input
                          className={Style.custom_input_small}
                          value={FiltersID?.rangeFrom || ""}
                          readOnly
                          disabled={true}
                        />
                        <Input
                          className={Style.custom_input_small}
                          value={FiltersID?.rangeTo || ""}
                          readOnly
                          disabled={true}
                        />
                      </>
                    )}
                    {FiltersID?.Comparison === "list" ? (
                      <Input
                        className={Style.custom_input_small}
                        value={FiltersID?.list || ""}
                        readOnly
                        disabled={true}
                      />
                    ) : (
                      <Input
                        className={Style.custom_input_small}
                        value={
                          FiltersID?.Column === "DateOfBirth"
                            ? handleDateFormatter(FiltersID.Value)
                            : FiltersID.Value || ""
                        }
                        readOnly
                        disabled={true}
                      />
                    )}
                  </Flex>
                </Flex>
              ))
            ) : (
              <Text>No categories have been created yet!</Text>
            )}

            {/* Divider */}
            <hr className={Style.divider} />

            {/* Communication Flow Section */}
            <Flex
              direction="horizontal"
              align="middle"
              className={Style.section}
            >
              <Text className={Style.text}>Communication Flow</Text>
            </Flex>
            {campaignData?.commflow_set?.length ? (
              campaignData?.commflow_set.map((flow, index) => (
                <div
                  key={`${index}-${flow.CommunicationType}`}
                  className={Style.row}
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    gap: "10px",
                  }}
                >
                  {" "}
                  {/* Changed to div, flex column */}
                  <Row
                    gutter={[16, 16]}
                    align="middle"
                    style={{ display: "flex" }}
                    className={Style.flow_container}
                  >
                    <Col md={24} lg={10} style={{ flex: 1 }}>
                      <Flex className={Style.input_container_small}>
                        {" "}
                        {/* Keep Flex */}
                        <Text className={Style.text}>Applicant Type:</Text>
                        <Input
                          className={Style.custom_input_small}
                          value={flow?.ExtraColumn1 || ""}
                          disabled
                        />
                      </Flex>
                    </Col>
                    <Col md={24} lg={10} style={{ flex: 1 }}>
                      <Flex className={Style.input_container_small}>
                        {" "}
                        {/* Keep Flex */}
                        <Text className={Style.text}>
                          Communication Channel:
                        </Text>
                        <Input
                          className={Style.custom_input_small}
                          value={flow?.CommunicationType || ""}
                          disabled
                        />
                      </Flex>
                    </Col>
                    <Col md={24} lg={10} style={{ flex: 1 }}>
                      <Flex className={Style.input_container_small}>
                        {" "}
                        {/* Keep Flex */}
                        <Text className={Style.text}>Based On:</Text>
                        <Input
                          className={Style.custom_input_small}
                          value={flow?.BasedOn || ""}
                          disabled
                        />
                      </Flex>
                    </Col>
                    <Col xs={24} sm={4} style={{ flex: 1 }}>
                      <Flex className={Style.input_container_small}>
                        {" "}
                        {/* Keep Flex */}
                        <Text className={Style.text}>Days:</Text>
                        <Input
                          className={Style.custom_input_small}
                          value={flow?.Days.split(",").length || 0}
                          disabled
                        />
                      </Flex>
                    </Col>
                    <Col xs={24} sm={4} style={{ flex: 1 }}>
                      <Flex className={Style.input_container_small}>
                        {" "}
                        {/* Keep Flex */}
                        <Segmented
                          value={flow?.BeforeAfter ? "After" : "Before"}
                          options={["After", "Before"]}
                          disabled
                        />
                      </Flex>
                    </Col>
                  </Row>
                </div>
              ))
            ) : (
              <Text>No flows have been created yet!</Text>
            )}

            {/* Divider */}
            <hr className={Style.divider} />

            {/* Template Section */}
            <Flex
              direction="horizontal"
              align="middle"
              className={Style.section}
            >
              <Text className={Style.text}>Template</Text>
            </Flex>

            {campaignData?.commflow_set?.length ? (
              campaignData.commflow_set.map((template, index) => {
                return (
                  <Flex
                    key={`${index}-${template.CommFlowID}`}
                    vertical
                    gap={10}
                    className={Style.row}
                  >
                    <Flex direction="horizontal" gap={10} className={Style.row}>
                      <Flex
                        direction="vertical"
                        className={Style.input_container_small}
                      >
                        <Text className={Style.text}>Communication Type:</Text>
                        <Input
                          className={Style.custom_input_small}
                          value={`${template?.CommFlowID}-${template?.CommunicationType}-${template?.BeforeAfter}`}
                          disabled
                        />
                      </Flex>
                      <Flex
                        direction="vertical"
                        className={Style.input_container_small}
                      >
                        <Text className={Style.text}>Selected Template:</Text>
                        <Input
                          className={Style.custom_input_small}
                          disabled
                          value={template?.FlowID_data?.FlowName || "N/A"}
                        />
                      </Flex>
                      {template?.FlowID_data?.FlowName && (
                        <Flex className={Style.input_container_small}>
                          <Button
                            type="link"
                            className={Style.text}
                            style={{ textDecoration: "underline" }}
                            onClick={handleViewTemplate.bind(null, {
                              communicationType: template.CommunicationType,
                              flowData: template.FlowID_data,
                              index,
                            })}
                          >
                            {selectedTemplateIndex === index
                              ? "Hide Template"
                              : "View Template"}
                          </Button>
                        </Flex>
                      )}
                    </Flex>
                    {selectedTemplateIndex === index && viewTemplates?.length
                      ? viewTemplates.map((temp, i) => (
                          <Flex
                            key={`${i}-${temp.templateBody}`}
                            vertical
                            gap={10}
                            style={{ width: "100%" }}
                          >
                            <TemplateScripting
                              visible={true}
                              scriptData={[temp.templateBody]}
                              showCross={false}
                            />
                          </Flex>
                        ))
                      : null}
                  </Flex>
                );
              })
            ) : (
              <Text>No templates have been created yet!</Text>
            )}
          </Flex>
        </Flex>
      )}
    </Flex>
  );
}

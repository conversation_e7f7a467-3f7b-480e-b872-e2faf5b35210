import { Flex } from "antd";
import React, { useEffect, useState } from "react";
import Style from "./_style.module.scss";
import HISTORY_ICON from "../../assets/Images/ai-history.svg";
import SENDER_ICON from "../../assets/Images/sender-icon.svg";
import CHAT_ICON from "../../assets/Images/ai-chat.svg";
import { HistoryChat } from "./HistoryChat";
import { AIChatSection } from "./AIChatSection";
import { AXIOS } from "../../apis/ho-Instance";
import { v4 as uuidv4 } from 'uuid';

export default function CampaignManagementAI() {
  const userDetails = JSON.parse(localStorage.getItem("user"));
  const USER_ID = userDetails?.username;
  const [messageData, setMessageData] = useState([]);
  const [historyData, setHistoryData] = useState({
    active: [],
    completed: [],
    lastActive: {},
  });
  const [text, setText] = useState("");
  const [conversationUuid, setConversationUuid] = useState(
    localStorage.getItem("conversationUuid") || null
  );
    // history chat
  const [showHistory, setShowHistory] = useState(false);
  const [language] = useState("english");
  const [webSocket, setWebSocket] = useState(null);
  const [isTyping, setIsTyping] = useState(false);
  const [isEditedMessage, setIsEditedMessage] = useState(false);
  const [lastMessageId, setLastMessageId] = useState(false);
  const [historyLoader, setHistoryLoader] = useState(false);

  const handleInputChange = (e) => {
    setText(e.target.value);
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      if (e.shiftKey) {
        // Shift+Enter: Insert a new line
        e.preventDefault();
        setText((prevText) => prevText + "\n");
      } else {
        // Enter: Send message
        e.preventDefault();
        handleSendMessage();
      }
    }
  };

  //Handle fetch history data
  const handleFetchHistoryData = async () => {
    setHistoryLoader(true);
    try {
      const response = await AXIOS.get(`user-conversations/${USER_ID}/`);
      if (response.status === 200 && response.data.conversations) {
        setHistoryData({
          active: response.data.conversations.active,
          completed: response.data.conversations.completed,
          lastActive: response.data.conversations.last_active,
        });
      }
    } catch (error) {
      console.log("Error in history api", error);
    }finally{
      setHistoryLoader(false);
    }
  };

  //Handle send message
  const handleSendMessage = () => {
    if (text.trim() === "") return;
    const messageId =
      Date.now().toString() + uuidv4().toString(36).substring(2, 5);
    setMessageData((prev) => [
      ...prev,
      {
        id: messageId,
        text,
        sender: "user",
        timestamp: new Date().toISOString(),
        isEdit: true,
      },
    ]);

    // Send message to LLM
    if (webSocket && webSocket.readyState === WebSocket.OPEN) {
      webSocket.send(
        JSON.stringify({
          type: "message",
          message: text,
          user_id: USER_ID,
          designation: userDetails.designation,
          BankMstID: userDetails.BankMstID,
          conversation_uuid: conversationUuid,
          language: language,
          ...(userDetails?.designation === "BM" && {
            BranchMstID: userDetails?.BranchMstID,
          }),
        })
      );
    } else {
      // Handle disconnected websocket
      setMessageData((prev) => [
        ...prev,
        {
          text: "Not Connected to server. Please try again.",
          sender: "system",
          isError: true,
          timestamp: new Date().toISOString(),
        },
      ]);
      // Attempt reconnection (optional)
      handleConnectWebSocket();
    }
    // Clear input field
    setText("");
  };

  // Handle editing a message
  const handleEditMessage = () => {
    if (!webSocket || webSocket.readyState !== WebSocket.OPEN) {
      console.log("WebSocket not connected, can't edit message");
      return;
    }

    // Update the message text locally first
    setMessageData((prev) => [
      ...prev,
      {
        id: lastMessageId,
        text,
        sender: "user",
        timestamp: new Date().toISOString(),
        isEdit: true,
      },
    ]);

    // Send edit message to WebSocket
    webSocket.send(
      JSON.stringify({
        type: "edit_message",
        message_id: lastMessageId,
        message: text,
        user_id: USER_ID,
        designation: userDetails.designation,
        BankMstID: userDetails.BankMstID,
        conversation_uuid: conversationUuid,
        ...(userDetails?.designation === "BM" && {
          BranchMstID: userDetails?.BranchMstID,
        }),
      })
    );

    // Empty the field
    setText("");
  };

  // Handle Edit Last Message
  const handleEditLastMessage = ({ oldMessage, index }) => {
    setText(oldMessage?.text);
    setLastMessageId(oldMessage?.id);
    setIsEditedMessage(true);

    // Remove all messages below the selected one, including the edited one
    setMessageData((prevMessages) => {
      // Remove the selected message & all below it
      const updatedMessages = prevMessages.slice(0, index);
      return updatedMessages.map((msg, idx) => ({
        ...msg,
        isEdit: false,
      }));
    });
  };

  // Handle to add new chat
  const handleNewConversation = () => {
    // Reset conversation UUID
    setConversationUuid(null);
    localStorage.removeItem("conversationUuid");

    // Clear messages
    setMessageData([]);

    // Create new chat object
    const newChat = {
      uuid: `New Chat ${historyData.active.length + 1}`, // You can customize the title
      created_at: new Date().toISOString(),
      campaign_name: "New campaign",
    };

    // Update historyData's active chats
    setHistoryData((prev) => ({
      ...prev,
      active: [newChat, ...prev.active], // Add  chat in beginning
      lastActive: newChat,
    }));

    // If connected, send reset message
    if (webSocket && webSocket.readyState === WebSocket.OPEN) {
      webSocket.send(
        JSON.stringify({
          type: "reset",
          user_id: USER_ID,
          language: language,
          BankMstID: userDetails?.BankMstID,
          designation: userDetails?.designation,
          ...(userDetails?.designation === "BM" && {
            BranchMstID: userDetails?.BranchMstID,
          }),
        })
      );
    } else {
      // Try to reconnect with a fresh state
      handleConnectWebSocket();
    }
  };

  // Handle WebSocket messages
  const handleWebSocketMessage = (data) => {
    if (!data?.type) {
      console.warn("Invalid WebSocket message received:", data);
      return;
    }
    const timestamp = data.timestamp || new Date().toISOString();
    const newMessages = [];

    switch (data.type) {
      case "greeting":
        newMessages.push({
          text: data.message,
          sender: "ai",
          timestamp,
        });

        // Store conversation UUID if provided
        if (data.conversation_uuid) {
          setConversationUuid(data.conversation_uuid);
          localStorage.setItem("conversationUuid", data.conversation_uuid);
        }
        break;

      case "response":
        newMessages.push({
          text: data.message,
          csv_download_url: data.csv_download_url,
          sender: "ai",
          timestamp,
        });

        // Handle form completion if needed
        if (data.form_complete) {
          console.log("Form completed:", data.form_data);
        }
        break;

      case "error":
        newMessages.push({
          text: data.message,
          sender: "ai",
          isError: true,
          timestamp,
        });
        break;

      case "typing":
        setIsTyping(data.is_typing);
        break;

      case "restore_confirmation":
        setConversationUuid(data.conversation_uuid);
        localStorage.setItem("conversationUuid", data.conversation_uuid);

        // Clear messages and add system message
        setMessageData([]);
        newMessages.push({
          text: data.message || "Previous conversation restored.",
          sender: "system",
          timestamp,
        });
        // Add conversation history if provided
        if (data.history && Array.isArray(data.history)) {
          data.history.forEach((msg) => {
            newMessages.push(
              {
                text: msg.user_message,
                sender: "user",
                timestamp: msg.timestamp,
              },
              { text: msg.ai_response, sender: "ai", timestamp: msg.timestamp }
            );
          });
        }
        break;

      case "reset_confirmation":
        setConversationUuid(data.conversation_uuid);
        localStorage.setItem("conversationUuid", data.conversation_uuid);

        // Clear all messages and notify the user
        setMessageData([]);
        newMessages.push({
          text: data.message || "Conversation has been reset.",
          sender: "system",
          timestamp,
        });
        break;

      case "message_updated":
        console.log("Message updated:", data);
        break;

      case "ai_response_updated":
        newMessages.push({
          id: data.message_id,
          text: data.response,
          csv_download_url: data.csv_download_url,
          sender: "ai",
          timestamp,
          isEdit: false,
        });
        break;

      case "delete_confirmation":
        // If the current conversation was deleted, start a new one
        if (data.conversation_uuid === conversationUuid) {
          handleNewConversation();
        }

        // Add system message about deletion if needed
        newMessages.push({
          text: data.message || "Conversation deleted successfully.",
          sender: "system",
          timestamp: data.timestamp || new Date().toISOString(),
        });
        break;

      default:
        console.log("Unknown message type:", data.type, data);
        break;
    }
    // Batch add messages for performance optimization
    if (newMessages.length > 0) {
      setMessageData((prevMessages) => [...prevMessages, ...newMessages]);
    }
  };

  //Handle connect WebSocket function
  const handleConnectWebSocket = () => {
    try {
      // Step1: Close existing connection if any
      if (webSocket && webSocket.readyState !== WebSocket.CLOSED) {
        console.log("Closing existing WebSocket connection");
        webSocket.close();
      }

      // Step2: Create socket URL & Connect new socket
      // const wsScheme = window.location.protocol === 'https:' ? 'wss://' : 'ws://';
      const wsHost = "smartcollect.markytics.ai";
      const wsUrl = `wss://${wsHost}/ws/chat/`;
      const ws = new WebSocket(wsUrl);

      // Handle get initial message
      ws.onopen = () => {
        // Get the current value from localStorage, which should be null after refresh
        const currentUuid = localStorage.getItem("conversationUuid");

        // Initialize or restore conversation
        const initMessage = {
          type: "initialize",
          user_id: USER_ID,
          language: language,
          BankMstID: userDetails?.BankMstID,
          designation: userDetails?.designation,
          ...(userDetails?.designation === "BM" && {
            BranchMstID: userDetails?.BranchMstID,
          }),
        };

        // Add conversation_uuid if we have one stored
        if (currentUuid) {
          initMessage.conversation_uuid = currentUuid;
        } else {
          console.log("Starting new conversation - no UUID provided");
        }

        ws.send(JSON.stringify(initMessage));
      };

      // Handle closing socket
      ws.onclose = (event) => {
        console.log(
          `WebSocket closed: code=${event.code}, reason=${
            event.reason || "No reason"
          }`
        );
      };

      // Handle socket error
      ws.onerror = (error) => {
        console.error("WebSocket error:", error);
      };

      // Handle display response message
      ws.onmessage = (event) => {
        console.log("Message received:");
        try {
          const data = JSON.parse(event.data);
          handleWebSocketMessage(data);
        } catch (error) {
          console.error("Error parsing WebSocket message:", error);
        }
      };

      setWebSocket(ws);
      // Return cleanup function
      return () => {
        if (ws && ws.readyState !== WebSocket.CLOSED) {
          ws.close();
        }
      };
    } catch (error) {
      console.error("Error setting up WebSocket:", error);
      return null;
    }
  };

  useEffect(() => {
    console.log("Initial call on refresh");
    setConversationUuid(null);
    localStorage.removeItem("conversationUuid");
    handleFetchHistoryData();
    const cleanup = handleConnectWebSocket();
    return () => {
      if (cleanup) cleanup();
    };
  }, []);
  return (
    <Flex className={Style.container}>
      <Flex
        vertical
        justify="space-between"
        className={Style.history_chat_container}
      >
        <Flex justify="end" gap="10px">
          <button className={Style.history_icon} onClick={handleNewConversation}>
            <img src={CHAT_ICON} alt="sender" />
          </button>
          <button className={Style.history_icon} onClick={()=> setShowHistory(true)}>
            <img src={HISTORY_ICON} alt="sender" />
          </button>
        </Flex>

        <HistoryChat
          showHistory={showHistory}
          toggleHistory={()=> setShowHistory(false)}
          Data={historyData}
          historyLoader={historyLoader}
        />

        {/*Recent history  */}

        {/* Chats */}
        <AIChatSection
          data={messageData}
          isTyping={isTyping}
          handleEditLastMessage={handleEditLastMessage}
        />

        {/* Input field & sender button  */}
        <Flex className={Style.input_box} justify="space-between">
          <textarea
            value={text}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            className={Style.custom_input}
            placeholder="Ask Smart Collect AI"
            rows={2} // Reduce default rows
          />
          {isEditedMessage ? (
            <button className={Style.sender_btn} onClick={handleEditMessage}>
              <img src={SENDER_ICON} alt="sender" />
            </button>
          ) : (
            <button className={Style.sender_btn} onClick={handleSendMessage}>
              <img src={SENDER_ICON} alt="sender" />
            </button>
          )}
        </Flex>
      </Flex>
    </Flex>
  );
}

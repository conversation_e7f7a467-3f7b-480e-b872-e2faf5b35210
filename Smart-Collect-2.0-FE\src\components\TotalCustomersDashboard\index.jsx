import { Flex, message, Table, Typography } from "antd";
import React, { useState, useEffect } from "react";
import AppHeader from "../AppHeader";
import {
  ALL_CACHE_NAMES,
  formatAmount,
  handleDateFormatter,
  handleDownloadTable,
} from "../../constant";
import DOWNLOAD_IMG from "../../assets/Images/download.svg";
import Style from "./_style.module.scss";
import { AXIOS } from "../../apis/ho-Instance";
import ApplicationLoader from "../ApplicationLoader";
import HISTORY_IMG from "../../assets/Images/history_icon.png";
import { ConversionHistory } from "../Modals/ConversionHistory";
import {
  getCacheKey,
  getFromCache,
  storeToCache,
} from "../../utils/cacheHelper";

const { Text } = Typography;
const CACHE_NAME = ALL_CACHE_NAMES.DASHBOARD_CACHE;

export default function TotalCustomersDashboard() {
  const [customersData, setCustomersData] = useState({
    results: [],
    pagination: [],
  });
  const [isLoading, setIsLoading] = useState(false);
  const storedFilter = localStorage.getItem("filterDate");
  const storedDpd = localStorage.getItem("DashboardDpdCategory");
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [modalState, setModalState] = useState({
    history: { isOpen: false, data: null },
  });

  const openModal = ({ type, data }) => {
    setModalState((prev) => ({
      ...prev,
      [type]: { isOpen: true, data },
    }));
  };

  const closeModal = (type) => {
    setModalState((prev) => ({
      ...prev,
      [type]: { isOpen: false, data: null },
    }));
  };

  // Fetch total customers
  const getTotalCustomersData = async ({
    page = 1,
    page_size = 10,
    fromDate,
    toDate,
    BranchName,
    dpdRange = { start: -1, end: -1 },
    disbursementID,
    loanTypes,
    customerNames,
  }) => {
    setIsLoading(true);
    const params = {
      page,
      page_size,
      fromDate,
      toDate,
      BranchName,
      dpdfrom: dpdRange?.start ?? -1,
      dpdto: dpdRange?.end ?? -1,
      DisbursementID: disbursementID,
      LoanType: loanTypes,
      CustomerName: customerNames,
    };
    const cacheKey = getCacheKey({
      endpoint: "total_customers/",
      params: params,
    });
    // Check cache
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setCustomersData({
        results: cachedData.results,
        pagination: cachedData.pagination,
      });
      setIsLoading(false);
    }
    try {
      const response = await AXIOS.get("total_customers/", {
        params: params,
      });
      if (response.status === 200 && response.data?.results?.length) {
        const { results, pagination } = response.data;
        // Store data in cache
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
        setCustomersData({ results, pagination });
      } else {
        setCustomersData({ results: [], pagination: {} });
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Apply filter and fetch data
  const applyFilters = async ({
    fromDate,
    toDate,
    BranchName,
    disbursementID,
    customerNames,
    loanTypes,
  }) => {
    await getTotalCustomersData({
      fromDate: fromDate,
      toDate: toDate,
      BranchName,
      dpdRange: JSON.parse(storedDpd),
      disbursementID,
      customerNames,
      loanTypes,
      page: 1,
    });
  };

  // Clear the filters
  const ClearFilters = () => {
    if (storedFilter) {
      getTotalCustomersData({ page: 1 });
      localStorage.removeItem("filterDate");
    }
  };

  // Handle downloading
  const handleDownload = async () => {
    if (customersData.results.length === 0 || !customersData.results) {
      return message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: "TotalCustomer",
        worksheetName: "Dashboard-Total-Customer",
        tableData: customersData.results,
      });
    }
  };

  // On mount, check localStorage for saved filters
  useEffect(() => {
    if (storedFilter || storedDpd) {
      const {
        fromDate,
        toDate,
        BranchName,
        disbursementID,
        loanTypes,
        customerNames,
      } = storedFilter
        ? JSON.parse(storedFilter)
        : {
            fromDate: null,
            toDate: null,
            BranchName: null,
            disbursementID: "",
            loanTypes: "",
            customerNames: "",
          };

      const parsedDpd = storedDpd
        ? JSON.parse(storedDpd)
        : { start: -1, end: -1 };
      getTotalCustomersData({
        fromDate,
        toDate,
        BranchName,
        dpdRange: parsedDpd,
        disbursementID,
        loanTypes,
        customerNames,
      });
    } else {
      getTotalCustomersData({ page: 1 });
    }
  }, []);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Table columns
  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      sorter: (a, b) => (a?.key ?? 0) - (b?.key ?? 0),
      render: (_, { key }) => <Text>{key ?? "-"}.</Text>,
    },
    {
      title: "Customer Id",
      dataIndex: "CustomerID",
      sorter: (a, b) => Number(a?.CustomerID ?? 0) - Number(b?.CustomerID ?? 0),
      render: (CustomerId) => <Text>{CustomerId ?? "-"}</Text>,
    },
    {
      title: "Customer Name",
      dataIndex: "CustomerName",
      sorter: (a, b) =>
        String(a?.CustomerName ?? "").localeCompare(
          String(b?.CustomerName ?? "")
        ),
      render: (_, { CustomerName }) => (
        <Text className={Style.text}>{CustomerName ?? "-"}</Text>
      ),
    },
    {
      title: "Branch Name",
      dataIndex: "Branch",
      sorter: (a, b) =>
        String(a?.Branch ?? "").localeCompare(String(b?.Branch ?? "")),
      render: (value) => <Text className={Style.text}>{value ?? "-"}</Text>,
    },
    {
      title: "Disbursement ID",
      dataIndex: "DisbursementID",
      sorter: (a, b) =>
        String(a?.DisbursementID ?? "").localeCompare(
          String(b?.DisbursementID ?? "")
        ),
      render: (value) => <Text className={Style.text}>{value ?? "-"}</Text>,
    },
    {
      title: "Disbursement Date",
      dataIndex: "DisbursementDate",
      sorter: (a, b) => {
        const dateA = a?.DisbursementDate
          ? new Date(a.DisbursementDate)
          : new Date(0);
        const dateB = b?.DisbursementDate
          ? new Date(b.DisbursementDate)
          : new Date(0);
        return dateA - dateB;
      },
      render: (_, { DisbursementDate }) => (
        <Text>
          {DisbursementDate ? handleDateFormatter(DisbursementDate) : "--"}
        </Text>
      ),
    },
    {
      title: "Disbursement Amount",
      dataIndex: "DisbursementAmt",
      sorter: (a, b) => (a?.DisbursementAmt ?? 0) - (b?.DisbursementAmt ?? 0),
      render: (DisbursementAmt) => (
        <Text>{formatAmount(DisbursementAmt ?? 0)}</Text>
      ),
    },
    {
      title: "Loan Type",
      dataIndex: "LoanType",
      sorter: (a, b) =>
        String(a?.LoanType ?? "").localeCompare(String(b?.LoanType ?? "")),
      render: (_, { LoanType }) => <Text>{LoanType ?? "-"}</Text>,
    },
    {
      title: "EMI Amount",
      dataIndex: "EMIAmount",
      sorter: (a, b) => (a?.EMIAmount ?? 0) - (b?.EMIAmount ?? 0),
      render: (EMIAmount) => <Text>{formatAmount(EMIAmount ?? 0)}</Text>,
    },
    {
      title: "Overdue Amount",
      dataIndex: "OverDueAmt",
      sorter: (a, b) => (a?.OverDueAmt ?? 0) - (b?.OverDueAmt ?? 0),
      render: (value) => <Text>{formatAmount(value ?? 0)}</Text>,
    },
    {
      title: "DPD",
      dataIndex: "DPD",
      sorter: (a, b) => (a?.DPD ?? 0) - (b?.DPD ?? 0),
      render: (value) => <Text>{value ?? 0}</Text>,
    },
    {
      title: "History",
      key: "history",
      dataIndex: "history",
      fixed: isMobile ? null : "right",
      width: 92,
      render: (_, data) => (
        <button
          className={Style.icon_div}
          onClick={() => openModal({ type: "history", data })}
        >
          <div className={Style.icon_img}>
            <img src={HISTORY_IMG} alt="history" />
          </div>
        </button>
      ),
    },
  ];

  const dataSource = Array.isArray(customersData.results)
    ? customersData.results.map((data, i) => ({
        key:
          (customersData.pagination.page - 1) *
            customersData.pagination.page_size +
          i +
          1,
        ...data,
      }))
    : [];
  return (
    <Flex gap={20} vertical>
      <AppHeader
        title={"Total Customers"}
        applyFilters={applyFilters}
        ClearFilters={ClearFilters}
        isDashboardOtherPages={true}
        pageId={"TotalLoans"}
      />
      <Flex gap={20} vertical>
        <Flex justify="end">
          <button className={Style.download_button} onClick={handleDownload}>
            <img src={DOWNLOAD_IMG} alt="download-button" />
          </button>
        </Flex>

        {isLoading ? (
          <ApplicationLoader />
        ) : (
          <Table
            bordered
            virtual
            className={Style.CustomTable}
            columns={columns}
            dataSource={dataSource}
            scroll={{ x: 2000, y: 360 }}
            pagination={{
              current: customersData.pagination.page,
              pageSize: customersData.pagination.page_size,
              total: customersData.pagination.total_entries,
              showSizeChanger: false,
              onChange: (page) => {
                getTotalCustomersData({ page });
              },
            }}
          />
        )}
      </Flex>

      <ConversionHistory
        customerName={modalState.history.data?.CustomerName}
        loanMstId={modalState.history.data?.LoanMstID}
        modalStatus={modalState.history.isOpen}
        handleCancel={() => closeModal("history")}
      />
    </Flex>
  );
}

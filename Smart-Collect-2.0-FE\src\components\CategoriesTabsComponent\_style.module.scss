@import url("../../index.css");

$light-gray: #d9d9d9;
$tab-height: 3px;
$tab-width: 300px;

.tabs {
  margin-top: 1rem;
  &:global(.ant-tabs) {
    width: 100%;

    :global(.ant-tabs-nav-wrap) {
      padding: 0 0.9rem;
    }

    :global(.ant-tabs-nav) {
      :global(.ant-tabs-nav-list) {
        width: 100%;
        justify-content: space-between;

        :global(.ant-tabs-tab) {
          font-size: 14px;
          font-weight: 400;
          padding-bottom: 9px;
          margin: 0;
          width: $tab-width;
          display: flex;
          justify-content: center;
          align-items: center;
          position: relative;
          z-index: 2;
          font-family: "Kanit", "Courier New", Courier, monospace;

          :global(.ant-tabs-tab-btn) {
            color: var(--dark-blue);
          }
          &:global(.ant-tabs-tab-active) {
            :global(.ant-tabs-tab-btn) {
              color: white;
            }
          }
        }
      }

      &::before {
        border-bottom: $tab-height solid $light-gray;
        border-radius: 20px;
        margin: 0 0.9rem;
      }

      :global(.ant-tabs-ink-bar) {
        background-color: var(--dark-blue);
        height: 40px;
        border-start-start-radius: 8px;
        border-start-end-radius: 8px;
        z-index: 1;
        &::before {
          content: "";
          position: absolute;
          bottom: 0px;
          left: -10px;
          width: 34px;
          height: 50px;
          background: var(--dark-blue);
          transform: translateX(0%);
          clip-path: polygon(100% 55%, 0 100%, 100% 100%);
        }
        &::after {
          content: "";
          position: absolute;
          bottom: 0px;
          right: -10px;
          width: 34px;
          height: 50px;
          background: var(--dark-blue);
          transform: translateX(0%);
          clip-path: polygon(0 55%, 0 100%, 100% 100%);
        }
      }
    }
  }
}

.tab_null {
  margin-top: 1rem;
  &:global(.ant-tabs) {
    width: 100%;

    :global(.ant-tabs-nav-wrap) {
      padding: 0 0.9rem;
    }

    :global(.ant-tabs-nav) {
      :global(.ant-tabs-nav-list) {
        width: 100%;
        justify-content: space-between;

        :global(.ant-tabs-tab) {
          font-size: 14px;
          font-weight: 400;
          padding-bottom: 9px;
          margin: 0;
          width: $tab-width;
          display: flex;
          justify-content: center;
          align-items: center;
          position: relative;
          z-index: 2;
          font-family: "Kanit", "Courier New", Courier, monospace;
          :global(.ant-tabs-tab-btn) {
            color: var(--dark-blue);
          }
          &:global(.ant-tabs-tab-active) {
            :global(.ant-tabs-tab-btn) {
              color: white;
            }
          }
        }
      }

      &::before {
        border-bottom: $tab-height solid $light-gray;
        border-radius: 20px;
        margin: 0 0.9rem;
      }

      :global(.ant-tabs-ink-bar) {
        height: 0px;
        left: 0 !important;
        transition: none !important;
        &::before,
        &::after {
          transition: none !important;
          transform: none !important; // if not using animation
        }
      }
    }
  }
}

// Responsive view
@media screen and (max-width: 768px) {
  .tabs {
    &:global(.ant-tabs) {
      :global(.ant-tabs-nav-wrap) {
        padding: 0;
        overflow: visible;
        overflow-x: clip;
      }

      :global(.ant-tabs-nav) {
        &::before {
          margin: 0;
        }

        :global(.ant-tabs-nav-list) {
          :global(.ant-tabs-tab) {
            padding: 4px 8px;
            &:global(.ant-tabs-tab-active) {
              :global(.ant-tabs-tab-btn) {
                color: var(--dark-blue);
              }
            }
          }
        }

        :global(.ant-tabs-ink-bar) {
          height: $tab-height;
          border-radius: 10px;
          &::before,
          &::after {
            height: 0;
            width: 0;
          }
        }
      }
    }
  }
}

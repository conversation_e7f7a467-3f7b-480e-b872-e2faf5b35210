import React, { useEffect, useState } from "react";
import {
  Col,
  Row,
  Flex,
  Typography,
  Segmented,
  message,
  Select,
  Button,
} from "antd";
import Style from "./_style.module.scss";
import { SwitchComponent } from "../../SwitchComponent";
import DOWNLOAD_IMG from "../../../assets/Images/download.svg";
import { RegionTable } from "./RegionTable";
import { BranchTable } from "./BranchTable";
import { CustomerTable } from "./CustomerTable";
import { StateTable } from "./StateTable";
import { DpdLoanTable } from "./DpdLoanTable";
import DPD_IMG from "../../../assets/Images/Vector-9.svg";
import DPD_LOAN_IMG from "../../../assets/Images/Vector-10.svg";
import DonutChart from "./DonutChart";
import { AXIOS } from "../../../apis/ho-Instance";
import { handleDownloadTable } from "../../../constant";

const { Text } = Typography;

export function Snapshot() {
  // Local storage
  const userDetails = JSON.parse(localStorage.getItem("user"));

  // Loading states
  const [isLoading, setIsLoading] = useState(false);
  const [isRegionLoading, setIsRegionLoading] = useState(false);
  const [isBranchLoading, setIsBranchLoading] = useState(false);
  const [isCustomerLoading, setIsCustomerLoading] = useState(false);

  // Toggle states
  // const [isRegionSwitchOn, setIsRegionSwitchOn] = useState(false);
  // const [isBranchSwitchOn, setIsBranchSwitchOn] = useState(false);
  const [isDpdLoan, setIsDpdLoan] = useState(false);
  const [isDpdOutstandingLoan, setIsDpdOutstandingLoan] = useState(false);
  const [showFilters, setShowFilters] = useState(false);

  // states
  const [dpdValue, setDpdValue] = useState("Account");
  const [regionDpdValue, setRegionDpdValue] = useState("Account");
  const [branchDpdValue, setBranchDpdValue] = useState("Account");
  const [data, setData] = useState({});
  const [regionData, setRegionData] = useState([]);
  const [branchData, setBranchData] = useState([]);
  const [selectedState, setSelectedState] = useState({ id: null, name: "" });
  const [selectedRegion, setSelectedRegion] = useState({ id: null, name: "" });
  const [filtersOptions, setFiltersOptions] = useState({
    uniqueStates: [],
    uniqueRegions: [],
    uniqueBranches: [],
  });
  const [selectedFilters, setSelectedFilters] = useState({
    states: [],
    regions: [],
    branches: [],
  });

  // Customer states
  const [customerData, setCustomerData] = useState([]);
  const [customerFilterParams, setCustomerFilterParams] = useState({
    stateId: "all",
    regionId: "all",
    branchId: "all",
    tablewise: "State",
    range: "0",
  });
  const [customerTablePagination, setCustomerTablePagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // Function to get data based on selected tab
  const getFilteredData = () => {
    if (!data) return [];
    switch (dpdValue) {
      case "Account":
        return data.grouped;
      case "Od":
        return data.grouped_amount;
      case "POS":
        return data.grouped_amount_pos;
      default:
        return [];
    }
  };

  // Handle Region & Branch Data
  const getRelatedRegionAndBranchData = ({ type }) => {
    if (type === "region") {
      if (!regionData) return [];
      if (regionDpdValue === "Account") return regionData.grouped_with_total;
      if (regionDpdValue === "Od") return regionData.grouped_amount_with_total;
      return [];
    } else {
      if (!branchData) return [];
      if (branchDpdValue === "Account") return branchData.grouped_with_total;
      if (branchDpdValue === "Od") return branchData.grouped_amount_with_total;
      return [];
    }
  };

  // Fetch Filter options.
  const handleFetchFiltersOptions = async (stateId = "all") => {
    try {
      const response = await AXIOS.post("filter/", {
        State_id: stateId, // Now dynamically fetch based on selected state
        Region_id: "all",
        Branch_id: "all",
        CollectionOfficerID: "all",
        LoanType: "all",
        BankMstID: userDetails?.BankMstID,
      });
      if (response.status === 200) {
        const finalResponse = response.data.final_response;
        setFiltersOptions((prev) => ({
          ...prev,
          uniqueStates: Array.isArray(finalResponse?.unique_states)
            ? finalResponse.unique_states
                .filter((item) => item.State_id !== "N/A")
                .map((item) => ({
                  label: item?.State || "Unknown",
                  value: item?.State_id || "",
                }))
            : [],
          uniqueRegions: Array.isArray(finalResponse?.unique_regions)
            ? finalResponse.unique_regions
                .filter((item) => item.Region_id !== "N/A")
                .map((item) => ({
                  label: item?.Region || "Unknown",
                  value: item?.Region_id || "",
                }))
            : [],
          uniqueBranches: Array.isArray(finalResponse?.unique_branches)
            ? finalResponse.unique_branches
                .filter((item) => item.Branch_id !== "N/A")
                .map((item) => ({
                  label: item?.Branch || "Unknown",
                  value: item?.Branch_id || "",
                }))
            : [],
        }));
      }
    } catch (error) {
      console.log("Error in filters options", error?.message);
    }
  };

  // Fetch the Summary & State Data
  const handleFetchDPDAndState = async (payload) => {
    setIsLoading(true);
    try {
      const response = await AXIOS.post("dpd_snapshot_top/", payload);
      if (response.status === 200) {
        setData(response.data.final_response);
        setCustomerData([]);
      }
    } catch (error) {
      console.log("Error in dpd api", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch the Region, branch
  const handleFetchRelatedData = async ({
    stateId = "all",
    regionId = "all",
    branchId = "all",
    tableShowName = "Region",
    stateName = "",
    regionName = "",
  }) => {
    // Hide customer table
    setCustomerData([]);

    let finalStateId = stateId;

    // If user clicks on a region, use the previously selected state ID
    if (tableShowName === "Branch" && selectedState.id) {
      finalStateId = selectedState.id;
    }

    // Update the state accordingly
    if (tableShowName === "Region" && stateId !== "all") {
      setSelectedState({ id: stateId, name: stateName });
      setSelectedRegion({ id: null, name: "" });
      setIsRegionLoading(true);
    } else if (tableShowName === "Branch" && regionId !== "all") {
      setSelectedRegion({ id: regionId, name: regionName });
      setIsBranchLoading(true);
    } else {
      setSelectedState({ id: null, name: "" });
      setSelectedRegion({ id: null, name: "" });
    }
    // Called the api
    try {
      const response = await AXIOS.post("dpd_snapshot_other_tables/", {
        State_id: finalStateId,
        Region_id: regionId,
        Branch_id: branchId,
        CollectionOfficerID: "all",
        LoanType: "all",
        BankMstID: userDetails?.BankMstID,
        designation: 1,
        tableshow: tableShowName,
      });
      if (response.status === 200) {
        if (tableShowName === "Region") setRegionData(response.data);
        else if (tableShowName === "Branch") setBranchData(response.data);
      }
    } catch (error) {
      console.log("Error in dpd api", error);
    } finally {
      if (tableShowName === "Branch") setIsBranchLoading(false);
      if (tableShowName === "Region") setIsRegionLoading(false);
    }
  };

  // Fetch State Options based on the Ids.
  const handleStateChange = async (selectedState) => {
    setSelectedFilters((prev) => ({
      ...prev,
      states: selectedState,
      regions: [],
      branches: [],
    }));
    setCustomerData([]);
    // Fetch new regions and branches based on selected state
    await handleFetchFiltersOptions(selectedState);
  };

  // Handle customer details
  const handleFetchCustomerDetails = async ({
    stateId = "all",
    regionId = "all",
    branchId = "all",
    tablewise = "State",
    range = "0",
    page = 1,
  }) => {
    setIsCustomerLoading(true);
    // Save current filters (except page)
    setCustomerFilterParams({ stateId, regionId, branchId, tablewise, range });
    try {
      const res = await AXIOS.post("dpd_snapshot_customers/", {
        State_id: stateId,
        Region_id: regionId,
        Branch_id: branchId,
        CollectionOfficerID: "all",
        LoanType: "all",
        BankMstID: 4,
        tablewise: tablewise,
        dpd_range: range,
        page_limit: 10,
        offset: page,
      });
      if (res.status === 200 && res.data?.data.length > 0) {
        setCustomerData(res.data?.data);
        setCustomerTablePagination((prev) => ({
          ...prev,
          current: res.data.current_page,
          total: res.data.total_records,
          pageSize: res.data.page_size,
        }));
        message.success(
          `Fetched range ${range} customers summary successfully!`
        );
      } else {
        message.error("No customers found!");
        setCustomerData([]);
      }
    } catch (error) {
      console.log("Error in customer details", error?.message);
    } finally {
      setIsCustomerLoading(false);
    }
  };

  // Handle Customer table pagination
  const handleCustomerTablePagination = async ({ page = 1 }) => {
    await handleFetchCustomerDetails({
      ...customerFilterParams,
      page,
    });
  };

  // handle download
  const handleDownload = async ({ data, excelName, worksheetName }) => {
    if (data.length === 0 || !data) {
      return message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: excelName,
        worksheetName: worksheetName,
        tableData: data,
      });
    }
  };

  // Handle Apply Filter
  const handleApplyFilters = async () => {
    const updatedPayload = {
      State_id:
        selectedFilters.states.length > 0 ? selectedFilters.states : "all",
      Region_id:
        selectedFilters.regions.length > 0 ? selectedFilters.regions : "all",
      Branch_id:
        selectedFilters.branches.length > 0 ? selectedFilters.branches : "all",
      CollectionOfficerID: "all",
      LoanType: "all",
      BankMstID: userDetails?.BankMstID,
      designation: 1,
    };
    await handleFetchDPDAndState(updatedPayload);
  };

  // Handle clear filter
  const handleClearFilters = async () => {
    setSelectedFilters({ states: [], regions: [], branches: [] });

    await handleFetchDPDAndState({
      State_id: "all",
      Region_id: "all",
      Branch_id: "all",
      CollectionOfficerID: "all",
      LoanType: "all",
      BankMstID: userDetails?.BankMstID,
      designation: 1,
    });
  };

  useEffect(() => {
    handleFetchDPDAndState({
      State_id: "all",
      Region_id: "all",
      Branch_id: "all",
      CollectionOfficerID: "all",
      LoanType: "all",
      BankMstID: userDetails?.BankMstID,
      designation: 1,
    });
    handleFetchFiltersOptions();
  }, []);

  return (
    <Row className={Style.snapshot} gutter={[30, 20]}>
      {/* Filters */}
      <Col span={24}>
        <Flex vertical gap={20} className={Style.filter_container}>
          <Flex
            justify="space-between"
            align="center"
            onClick={() => setShowFilters((prev) => !prev)}
          >
            <Text className={Style.title}>Filters</Text>
            <Text className={Style.title}>
              {!showFilters ? "Show" : "Hide"}
            </Text>
          </Flex>

          {showFilters && (
            <Row
              gutter={[20, 20]}
              justify={"space-between"}
              style={{ width: "100%" }}
            >
              <Col span={8}>
                <Flex gap={10} align="center">
                  <Text className={Style.select_title}> State </Text>
                  <Select
                    showSearch
                    mode="multiple"
                    placeholder="Select State"
                    options={filtersOptions.uniqueStates}
                    className={Style.custom_select}
                    value={selectedFilters.states}
                    onChange={(value) => handleStateChange(value)}
                    filterOption={(input, option) =>
                      (option?.label ?? "")
                        ?.toLowerCase()
                        .includes(input?.toLowerCase())
                    }
                  />
                </Flex>
              </Col>
              <Col span={8}>
                <Flex gap={10} align="center">
                  <Text className={Style.select_title}>Region</Text>
                  <Select
                    showSearch
                    mode="multiple"
                    placeholder="Select Region"
                    options={filtersOptions.uniqueRegions}
                    className={Style.custom_select}
                    value={selectedFilters.regions}
                    onChange={(value) =>
                      setSelectedFilters((prev) => ({
                        ...prev,
                        regions: value,
                      }))
                    }
                    filterOption={(input, option) =>
                      (option?.label ?? "")
                        ?.toLowerCase()
                        .includes(input?.toLowerCase())
                    }
                  />
                </Flex>
              </Col>
              <Col span={8}>
                <Flex gap={10} align="center">
                  <Text className={Style.select_title}>Branch</Text>
                  <Select
                    showSearch
                    mode="multiple"
                    placeholder="Select Branch"
                    options={filtersOptions.uniqueBranches}
                    className={Style.custom_select}
                    value={selectedFilters.branches}
                    onChange={(value) =>
                      setSelectedFilters((prev) => ({
                        ...prev,
                        branches: value,
                      }))
                    }
                    filterOption={(input, option) =>
                      (option?.label ?? "")
                        ?.toLowerCase()
                        .includes(input?.toLowerCase())
                    }
                  />
                </Flex>
              </Col>

              {/*
            <Col span={6}>
              <Flex gap={10} align="center">
                <Text className={Style.select_title}>Collection Officer</Text>
                <Select 
                  showSearch
                  placeholder="Select Collection Officer"
                  options={[]}
                  className={Style.custom_select}
                  />
              </Flex>
            </Col>
             <Col span={4}>
              <Flex gap={10} align="center">
                <Text className={Style.select_title}>Loans</Text>
                <Select 
                  showSearch
                  placeholder="Select Loan"
                  options={[]}
                  className={Style.custom_select}
                  />
              </Flex>
            </Col> */}

              <Col span={24}>
                <Flex justify="center" gap={10} align="center">
                  <Button
                    className={Style.apply_btn}
                    onClick={handleApplyFilters}
                  >
                    Apply
                  </Button>
                  <Button
                    className={Style.clear_btn}
                    onClick={handleClearFilters}
                  >
                    Clear
                  </Button>
                </Flex>
              </Col>
            </Row>
          )}
        </Flex>
      </Col>

      {/* Loan */}
      <Col sm={12} xs={24}>
        <Flex vertical gap={20}>
          {/* Details */}
          <Flex justify="space-between">
            <Flex align="center" gap={4}>
              <img src={DPD_LOAN_IMG} className={Style.icon_2} alt="dpd" />
              <Text className={Style.title} style={{ color: "#0F2050" }}>
                DPD Wise Loan and Amount
              </Text>
            </Flex>
            <Flex gap={10}>
              {isDpdLoan && (
                <SwitchComponent
                  isSnapshot={true}
                  offText={"Outstanding"}
                  onText={"Accounts"}
                  onColor={"#0F2050"}
                  offColor={"#E4F8F9"}
                  checked={isDpdOutstandingLoan}
                  onToggle={() =>
                    setIsDpdOutstandingLoan(!isDpdOutstandingLoan)
                  }
                />
              )}
              <SwitchComponent
                offText={"Table"}
                onText={"Graph"}
                onColor={"#0F2050"}
                offColor={"#E4F8F9"}
                checked={isDpdLoan}
                onToggle={() => setIsDpdLoan(!isDpdLoan)}
              />
            </Flex>
          </Flex>

          {/* Table & Graph */}
          {isDpdLoan ? (
            <DonutChart summaryData={data?.summary} isLoading={isLoading} />
          ) : (
            <DpdLoanTable summaryData={data?.summary} isLoading={isLoading} />
          )}
        </Flex>
      </Col>

      {/* State */}
      <Col sm={12} xs={24}>
        <Flex vertical gap={20}>
          {/* Details */}
          <Flex justify="space-between">
            <Flex align="center" gap={4}>
              <img src={DPD_IMG} className={Style.icon} alt="dpd" />
              <Text className={Style.title} style={{ color: "#0F2050" }}>
                State Wise DPD Distribution
              </Text>
            </Flex>
            <Segmented
              value={dpdValue}
              onChange={(value) => {
                setDpdValue(value);
                setSelectedState({
                  name: "",
                  id: null,
                });
                setSelectedRegion({
                  name: "",
                  id: null,
                });
              }}
              options={["Account", "Od", "POS"]}
            />
          </Flex>

          {/* Table */}
          <StateTable
            isLoading={isLoading}
            data={getFilteredData()}
            onStateClick={handleFetchRelatedData}
            onRangeClick={handleFetchCustomerDetails}
          />
        </Flex>
      </Col>

      {/* Region */}
      {selectedState.id ? (
        <Col span={24}>
          <Flex vertical gap={20}>
            {/* Details */}
            <Flex justify="space-between">
              <Text className={Style.title}>{selectedState.name}: </Text>
              <Flex gap={20}>
                <Segmented
                  value={regionDpdValue}
                  style={{ marginBottom: 8 }}
                  onChange={(value) => {
                    setRegionDpdValue(value);
                    setSelectedRegion({
                      name: "",
                      id: null,
                    });
                  }}
                  options={["Account", "Od"]}
                />

                {/* <SwitchComponent
                  isSnapshot={true}
                  offText={"Outstanding"}
                  onText={"Accounts"}
                  onColor={"#0F2050"}
                  offColor={"#E4F8F9"}
                  checked={isRegionSwitchOn}
                  onToggle={() => setIsRegionSwitchOn(!isRegionSwitchOn)}
                /> */}

                <Flex justify="end">
                  <button
                    className={Style.download_button}
                    onClick={() =>
                      handleDownload({
                        data: regionData,
                        excelName: `${selectedState.name} Region`,
                        worksheetName: `${selectedState.name}-worksheet`,
                      })
                    }
                  >
                    <img src={DOWNLOAD_IMG} alt="download-button" />
                  </button>
                </Flex>
              </Flex>
            </Flex>

            {/* Table */}
            <RegionTable
              isLoading={isRegionLoading}
              // isRegionSwitchOn={!isRegionSwitchOn}
              data={getRelatedRegionAndBranchData({ type: "region" })}
              onRegionClick={handleFetchRelatedData}
              onRangeClick={handleFetchCustomerDetails}
            />
          </Flex>
        </Col>
      ) : null}

      {/* Branch */}
      {selectedRegion.id ? (
        <Col span={24}>
          <Flex vertical gap={20}>
            {/* Details */}
            <Flex justify="space-between">
              <Text className={Style.title}>{selectedRegion.name}:</Text>
              <Flex gap={20}>
                <Segmented
                  value={branchDpdValue}
                  style={{ marginBottom: 8 }}
                  onChange={(value) => {
                    setBranchDpdValue(value);
                  }}
                  options={["Account", "Od"]}
                />
                {/* <SwitchComponent
                  isSnapshot={true}
                  offText={"Outstanding"}
                  onText={"Accounts"}
                  onColor={"#0F2050"}
                  offColor={"#E4F8F9"}
                  checked={isBranchSwitchOn}
                  onToggle={() => setIsBranchSwitchOn(!isBranchSwitchOn)}
                /> */}

                <Flex justify="end">
                  <button
                    className={Style.download_button}
                    onClick={() =>
                      handleDownload({
                        data: branchData,
                        excelName: `${selectedRegion.name} Branch`,
                        worksheetName: `${selectedRegion.name}-worksheet`,
                      })
                    }
                  >
                    <img src={DOWNLOAD_IMG} alt="download-button" />
                  </button>
                </Flex>
              </Flex>
            </Flex>

            {/* Table */}
            <BranchTable
              isLoading={isBranchLoading}
              data={getRelatedRegionAndBranchData({ type: "branch" })}
              onRangeClick={handleFetchCustomerDetails}
            />
          </Flex>
        </Col>
      ) : null}

      {/*Customers  */}
      {customerData?.length > 0 ? (
        <Col span={24}>
          <Flex vertical gap={20}>
            {/* Details */}
            <Flex justify="space-between">
              <Text className={Style.title}>Customer Details</Text>
              <Flex gap={20}>
                {/* <SwitchComponent
                  isSnapshot={true}
                  offText={"Outstanding"}
                  onText={"Accounts"}
                  onColor={"#0F2050"}
                  offColor={"#E4F8F9"}
                  checked={isLoanSwitchOn}
                  onToggle={() => setIsLoanSwitchOn(!isLoanSwitchOn)}
                /> */}

                <Flex justify="end">
                  <div className={Style.download_button}>
                    <img src={DOWNLOAD_IMG} alt="download-button" />
                  </div>
                </Flex>
              </Flex>
            </Flex>

            {/* Table */}
            <CustomerTable
              data={customerData}
              isLoading={isCustomerLoading}
              pagination={customerTablePagination}
              onPaginationChange={(page) =>
                handleCustomerTablePagination({ page })
              }
            />
          </Flex>
        </Col>
      ) : null}
    </Row>
  );
}

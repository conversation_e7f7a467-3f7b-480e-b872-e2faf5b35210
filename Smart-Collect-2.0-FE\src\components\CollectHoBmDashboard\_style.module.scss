@import url("../../index.css");

$light-blue: #ebfdff;
$text-blue: #407bff;

.customer_engagement,
.customer_details {
  border-radius: 5px;
  background: $light-blue;
  padding: 0.5rem;
  box-shadow: 0px 4px 4px 0px #00000030;
  position: relative;

  .total_container {
    border-radius: 5px;

    .img_icon {
      width: 30px;
    }

    .title,
    .value {
      color: $text-blue;
      font-size: 1.1rem;
      font-family: "Kanit", "Courier New", Courier, monospace;
    }
    &.down {
      font-size: 1.2rem;
    }

    .value {
      color: #0156a6;
      font-weight: 500;
    }
  }

  .img_div {
    width: 20px;
    img {
      width: 100%;
      object-fit: contain;
    }
  }
  .engagement_text,
  .engagement_value {
    color: #0f2050;
    font-family: "Kanit", "Courier New", Courier, monospace;
    font-size: 12px;
    font-weight: normal;
  }

  .engagement_value {
    font-size: 14px;
  }

  .dropdown_container {
    width: 98%;
    margin: 0 auto;
    background-color: white;
    padding: 0.7rem;
    border-radius: 6px;

    // Arrow
    .arrow_container {
      width: 100%;
      display: flex;
      justify-content: end;
      cursor: pointer;
      border: none;
      background-color: transparent;

      .arrow {
        border: solid #0156a6;
        border-width: 0 3px 3px 0;
        padding: 3px;
        cursor: pointer;
        transition: transform 0.3s ease-in-out;
        z-index: 1;
        background-color: white;
        margin-bottom: 0.3rem;
        &:global(.up) {
          transform: rotate(-135deg);
        }

        &:global(.down) {
          transform: rotate(45deg);
        }
      }
    }

    // Dropdown
    .list {
      :global(.ant-typography) {
        margin: 0;
        color: var(--dark-black);
        font-family: "Kanit", "Courier New", Courier, monospace;
        font-weight: 400;
      }
      span {
        font-size: 14px;
        font-weight: 500;
        letter-spacing: 0.2px;
        color: black !important;
        white-space: nowrap;
        font-family: "Kanit", "Courier New", Courier, monospace;
      }
    }
  }
}

.customer_engagement {
  height: 155px;

  // for items 1
  .forOneItem {
    justify-content: center;

    .sizeIncrease {
      flex-direction: row;
      gap: 10px;
      padding: 0 1rem;
      .img_div {
        width: 30px !important;
      }
      .engagement_value {
        font-size: 20px;
      }
      .engagement_text {
        font-size: 18px;
      }
    }
  }

  .normal {
    // for item 3 & 2
    .sizeIncrease {
      flex-direction: row;
      gap: 10px;
      padding: 0 1rem;
      .img_div {
        width: 30px !important;
      }
      .engagement_value {
        font-size: 16px;
      }
      .engagement_text {
        font-size: 14px;
      }
    }
  }

  .engagement_value {
    font-weight: 600;
  }
}

.customer_details {
  padding: 1rem;

  .container {
    justify-content: space-evenly;
    flex: 1;
  }

  .customer_container {
    width: 150px;
  }
  .customer_text,
  .customer_value {
    color: var(--dark-blue);
    font-family: "Kanit", "Courier New", Courier, monospace;
    font-size: 13px;
    text-align: center;
    word-wrap: break-word;
  }
  .customer_value {
    font-size: 16px;
  }
  .customer_img_div {
    background: white;
    border-radius: 50px;
    width: 30px;
    height: 30px;
    padding: 0.2rem;
    img {
      width: 100%;
      object-fit: contain;
    }
  }
}

.chart_container {
  background-color: $light-blue;
  border-radius: 10px;
  // height: 300px; /* Adjust as needed */
  // max-height: 100vh; /* Ensure it doesn’t exceed the viewport */
  .title,
  .day_title {
    color: var(--dark-blue);
    font-family: "Kanit", "Courier New", Courier, monospace;
    font-size: 1rem;
  }

  .day_title {
    font-size: 14px;
  }

  .custom_select {
    :global(.ant-select-selector) {
      box-shadow: none !important;
      outline: none;
      border: 1px solid var(--dark-blue) !important;
      font-family: "Kanit", "Courier New", Courier, monospace;
    }
    :global(.ant-select-selection-item) {
      font-weight: 400;
      color: var(--dark-blue);
      font-family: "Kanit", "Courier New", Courier, monospace;
    }
  }
}

.pay_container {
  border-radius: 6px;
  width: 100%;

  .card {
    background-color: $light-blue;
    margin: 0.2rem 2rem 0.2rem 0;
    padding: 0.1rem 1rem;
    border-left: 5px solid var(--dark-blue);
    border-radius: 5px;
    box-shadow: 4px 4px 4px 0px #00000033;
    height: 120px;
    .title {
      font-family: "Kanit", "Courier New", Courier, monospace;
      font-size: 17px;
      text-align: center;
    }
    .text {
      font-family: "Kanit", "Courier New", Courier, monospace;
      font-size: 12px;
    }

    .img_div {
      width: 30px;
      height: 30px;
      background-color: white;
      padding: 0.2rem;
      border-radius: 50px;
      img {
        width: 100%;
        object-fit: contain;
      }
    }
    &.no_content_card {
      .img_div {
        width: 100px;
        height: 100px;
        background-color: transparent;
        img {
          width: 100%;
          object-fit: contain;
        }
      }
    }
  }

  // Slick list
  :global(.slick-list) {
    margin-left: 2rem;
  }

  // slick arrow
  :global(.slick-arrow) {
    background-color: var(--dark-blue) !important;
    height: 20px;
    width: 20px;
    border-radius: 50px;

    &::before {
      color: var(--blue);
      position: absolute;
      content: "";
      display: inline-block;
      width: 6px;
      height: 6px;
      border: 0 solid white;
      border-inline-width: 3px 0;
      border-block-width: 3px 0;
      border-radius: 2px !important;
      top: 28.5%;
    }

    &:global(.slick-disabled) {
      ::before {
        opacity: 0.9;
      }
    }
  }

  // prev
  :global(.slick-prev) {
    left: 0px;
    z-index: 1;
    top: 50%;

    &::before {
      transform: rotate(-45deg);
      left: 7px;
    }
  }

  // next
  :global(.slick-next) {
    right: 0px;
    top: 50%;

    &::before {
      transform: rotate(135deg);
      right: 7px;
    }
  }

  // Dots
  :global(.slick-dots) {
    li {
      width: 100px;
      button {
        &::before {
          content: "";
          width: 100px;
          height: 4px;
          border-radius: 10px;
          background-color: #d9d9d9;
          opacity: 1;
        }
      }
      &:global(.slick-active) {
        button {
          &::before {
            background-color: var(--dark-blue);
          }
        }
      }
    }
  }
}

.timeline_container {
  border-bottom: 1px solid #c3c2c291;
  padding-bottom: 0.5rem;
  .title {
    font-family: "Kanit", "Courier New", Courier, monospace;
    font-size: 16px;
    color: var(--dark-blue);
    margin-bottom: 1rem;
  }
  .slider_container {
    margin-top: 0.5rem;
    .value {
      color: var(--dark-blue);
      margin: 0;
      font-family: "Kanit", "Courier New", Courier, monospace;
      font-weight: 400;
      font-size: 16px;

      &.selectedText {
        color: #ffffff !important;
      }
    }
    .date {
      font-size: 13px;
      font-weight: 500;
      font-family: "Kanit", "Courier New", Courier, monospace;

      color: var(--dark-blue);
      &.selectedText {
        color: #ffffff !important;
      }
    }

    // Carousel card
    .d_card {
      cursor: pointer;
      justify-content: center;
      position: relative;

      &::after {
        content: "";
        position: absolute;
        width: 69px;
        height: 2px;
        background-color: var(--dark-blue);
        top: 50%;
        right: -37px;
      }
    }

    .last_card {
      &::after {
        content: "" !important;
        display: none;
      }
    }

    .card {
      padding: 0.3rem;
      margin: 0 2rem;
      border-radius: 10px;
      background-color: $light-blue;
      position: relative;
      height: 65px;

      &.selected {
        background-color: var(--dark-blue);
      }

      &.holiday_card {
        border-radius: 10px;
        text-align: center;
        z-index: 0;
        overflow: hidden;
        background: linear-gradient(
          100deg,
          #defbff 10%,
          #8ef4bc 100%,
          #c2ebf1 190%
        );

        // Fixes border issue
        &::before {
          content: "";
          position: absolute;
          inset: 0;
          background: linear-gradient(180deg, #02951a, #ffffff);
          border-radius: inherit;
          padding: 1px;
          -webkit-mask: linear-gradient(#fff 0 0) content-box,
            linear-gradient(#fff 0 0);
          -webkit-mask-composite: xor;
          mask-composite: exclude;
          z-index: -1;
          pointer-events: none;
        }

        .ribbon {
          color: white;
          background: #499956;
          position: absolute;
          left: -3.6rem;
          top: 0.6rem;
          width: 10rem;
          transform: rotate(-45deg);
          text-align: center;
          padding: 0.2rem 0;
          font-size: 0.7rem;
          font-weight: 600;
        }
      }

      .cross_icon {
        position: absolute;
        width: 100%;
        right: 10px;
        top: 10px;
      }
    }

    // ant typography
    :global(.ant-typography) {
      text-align: center;
      display: block;
      width: 100%;
    }

    // slick arrow
    :global(.slick-arrow) {
      background-color: var(--dark-blue);
      height: 20px;
      width: 20px;
      border-radius: 50px;

      &::before {
        color: var(--blue);
        position: absolute;
        content: "";
        display: inline-block;
        width: 6px;
        height: 6px;
        border: 0 solid white;
        border-inline-width: 3px 0;
        border-block-width: 3px 0;
        border-radius: 2px !important;
        top: 28.5%;
      }

      &:global(.slick-disabled) {
        ::before {
          opacity: 0.9;
        }
      }
    }

    // prev
    :global(.slick-prev) {
      left: 0px;
      z-index: 1;
      top: 50%;

      &::before {
        transform: rotate(-45deg);
        left: 7px;
      }
    }

    // next
    :global(.slick-next) {
      right: 0px;
      top: 50%;

      &::before {
        transform: rotate(135deg);
        right: 7px;
      }
    }
  }
}

.dashboard_tabs {
  :global(.ant-tabs) {
    margin-top: 0;
    :global(.ant-tabs-nav) {
      margin-bottom: 0;
      :global(.ant-tabs-nav-list) {
        :global(.ant-tabs-tab) {
          color: var(--dark-blue);
        }
      }
      &::before {
        border-bottom-width: 3px;
        margin: 0 0.3rem;
      }
    }
  }
  .selected_tab {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 8px;
  }
}

.dashboard_row {
  margin: 0 !important;
}

// Responsive View
@media only screen and (max-width: 1024px) {
  .dashboard_row {
    :global(.ant-col) {
      padding: 0 !important;
    }
  }

  .graph_container,
  .chart_container {
    display: none;
  }

  .timeline_container {
    .title {
      font-size: 13px;
    }
    .slider_container {
      .value,
      .date {
        font-size: 12px;
      }
      // slick arrow
      :global(.slick-arrow) {
        height: 16px;
        width: 16px;

        &::before {
          width: 5px;
          height: 5px;
        }

        &:global(.slick-disabled) {
          ::before {
            opacity: 0.9;
          }
        }
      }
      // prev
      :global(.slick-prev) {
        &::before {
          left: 5px;
          top: 26.5%;
        }
      }

      // next
      :global(.slick-next) {
        &::before {
          right: 5px;
          top: 26.5%;
        }
      }
    }
  }

  .customer_engagement {
    .total_container {
      .title,
      .value {
        font-size: 14px;
      }
    }

    .engagement_text,
    .engagement_value {
      font-size: 12px;
    }

    .img_div {
      width: 12px !important;
    }
  }

  .customer_details {
    .customer_text,
    .customer_value {
      font-size: 11px;
    }
  }

  .pay_container {
    :global(.slick-dots) {
      display: none !important;
    }
  }
}

@media only screen and (max-width: 798px) {
  .dashboard_row {
    padding: 0 20px !important;
  }
  .customer_details {
    flex-direction: column;
    align-items: center;
    gap: 10px;
    .container {
      width: 100%;
      justify-content: space-between;
    }
    .customer_container {
      width: auto;
    }
    a {
      width: 50%;
      border: 1px solid rgba(211, 211, 211, 0.4);
      :global(.ant-flex) {
        border-right: none !important;
        padding: 0 !important;
      }
    }
  }
  .timeline_container {
    .slider_container {
      .d_card {
        &::after {
          width: 60px;
        }
      }
      .card {
        margin: 0 1.5rem 0;
      }
    }
  }
  .pay_container {
    .card {
      .title {
        font-size: 13px;
      }
      .text {
        font-size: 11px;
        span {
          font-size: 13px !important;
        }
      }
      .img_div {
        width: 25px;
        height: 25px;
      }
    }
  }
  .customer_engagement {
    .engagement_text,
    .engagement_value {
      font-size: 10px;
    }
  }
}

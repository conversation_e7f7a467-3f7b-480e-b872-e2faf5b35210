import uuid

from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser, Group, Permission
from django.db import models

# Create your models here.


class BankMst(models.Model):
    BankMstID = models.AutoField(primary_key=True, db_column="BankMstID")
    OnBoardingDate = models.DateField(auto_now_add=True)
    IsActive = models.BooleanField(default=True)
    UpdatedDate = models.DateField(auto_now=True)
    Remarks = models.TextField(blank=True, null=True)
    SubscriptionID = models.IntegerField()
    SubscriptionType = models.CharField(max_length=255)
    ExtraColumn1 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn2 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn3 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn4 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn5 = models.CharField(max_length=255, blank=True, null=True)
    BankName = models.CharField(max_length=255)
    Key_ID = models.CharField(max_length=255)
    Secret_Key = models.CharField(max_length=255)
    Is_UPI = models.BooleanField(default=False)
    Is_recipt = models.BooleanField(default=False)
    Geolocation = models.BooleanField(default=False)
    WhatsappService = models.CharField(max_length=255)
    Bank_Details = models.JSONField(default=dict)
    claim_period = models.IntegerField()
    class Meta:
        managed = False
        db_table = "BankMst"


class BranchMst(models.Model):
    BranchMstID = models.AutoField(primary_key=True, db_column="BranchMstID")
    BankMstID = models.ForeignKey(
        BankMst, on_delete=models.CASCADE, db_column="BankMstID"
    )
    BranchCode = models.CharField(max_length=255)
    BranchName = models.CharField(max_length=255)
    IsActive = models.BooleanField(default=True)
    CreatedDate = models.DateField(auto_now_add=True)
    UpdatedDate = models.DateField(auto_now=True)
    LngMstID = models.IntegerField()
    Remarks = models.TextField(blank=True, null=True)
    State = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn1 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn2 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn3 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn4 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn5 = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = "BranchMst"


class UserMst(AbstractUser):
    UserID = models.AutoField(primary_key=True)
    BankMstID = models.ForeignKey(
        BankMst, on_delete=models.PROTECT, null=True, blank=True
    )
    BranchMstID = models.ForeignKey(
        BranchMst, on_delete=models.PROTECT, null=True, blank=True
    )
    BranchID = models.CharField(max_length=50, blank=True, null=True)
    Designation = models.CharField(max_length=50, blank=True, null=True)
    MobileNumber = models.CharField(max_length=50, blank=True, null=True)
    is_admin = models.BooleanField(default=False)
    BankMaster = models.BooleanField(default=False)
    UpdatedDate = models.DateTimeField(auto_now=True)
    ExtraColumn_1 = models.TextField(null=True, blank=True)
    ExtraColumn_2 = models.TextField(null=True, blank=True)
    ExtraColumn_3 = models.TextField(null=True, blank=True)
    ExtraColumn_4 = models.TextField(null=True, blank=True)
    ExtraColumn_5 = models.TextField(null=True, blank=True)
    FO_id = models.IntegerField(null=True, blank=True)
    BUcode = models.IntegerField(null=True, blank=True)

    groups = models.ManyToManyField(
        Group,
        related_name="usermst_groups",  # Change related_name to avoid conflict
        blank=True,
    )

    user_permissions = models.ManyToManyField(
        Permission,
        related_name="usermst_permissions",  # Change related_name to avoid conflict
        blank=True,
    )

    class Meta:
        managed = False
        db_table = "UserMst"
        ordering = ["BankMstID", "BranchMstID", "UserID"]

    def __str__(self):
        return self.username

    @property
    def Fullname(self):
        return f"{self.first_name} {self.last_name}".strip()

    def to_dict(self):
        return {
            "id": self.UserID,
            "username": self.username,
            "email": self.email,
            "first_name": self.first_name,
            "last_name": self.last_name,
        }


class LoginHistory(models.Model):
    user = models.ForeignKey(get_user_model(), on_delete=models.CASCADE, db_index=True)
    ip = models.GenericIPAddressField(db_index=True)
    ip_info = models.JSONField(default=dict)
    city = models.CharField(max_length=80, blank=True)
    region = models.CharField(max_length=80, blank=True)
    region_code = models.CharField(max_length=10, blank=True)
    country_code = models.CharField(max_length=2, blank=True)
    country_name = models.CharField(max_length=80, blank=True)
    currency = models.CharField(max_length=5, blank=True)
    user_agent = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.user.username + " (" + self.ip + ") at " + str(self.created_at)

    class Meta:
        managed = False
        db_table = "LoginHistory"
        ordering = ["-created_at"]


class CustomerMst(models.Model):
    CustomerMstID = models.AutoField(primary_key=True)
    BranchMstID = models.ForeignKey(
        BankMst,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        db_column="BranchMstID",
    )
    CustomerID = models.CharField(max_length=50, blank=True, null=True)
    CustomerCode = models.IntegerField(null=True, blank=True)
    CustomerName = models.CharField(max_length=100, null=False)
    MobileNumber = models.CharField(max_length=15, blank=True, null=True)
    Gender = models.CharField(max_length=50, blank=True, null=True)
    DateOfBirth = models.DateField(null=True, blank=True)
    IsDeath = models.BooleanField(null=True, blank=True)
    CreatedDate = models.DateField(auto_now_add=True)
    UpdatedDate = models.DateField(auto_now=True)
    EmailID = models.CharField(max_length=100, blank=True, null=True)
    Latitude = models.CharField(max_length=100, blank=True, null=True)
    Longitude = models.CharField(max_length=100, blank=True, null=True)
    PanNo = models.CharField(max_length=100, blank=True, null=True)
    Pincode = models.IntegerField(null=True, blank=True)
    CustomerAddress = models.CharField(max_length=100, blank=True, null=True)
    ExtraColumn1 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn2 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn3 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn4 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn5 = models.CharField(max_length=255, blank=True, null=True)
    BankMstID = models.ForeignKey(
        BranchMst, on_delete=models.PROTECT, null=True, db_column="BankMstID"
    )
    # CollectionOfficerID=models.CharField(max_length=255, blank=True, null=True)
    # CollectionOfficerName=models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = "CustomerMst"


CHOICES_WHATSAPP_OPTED = [
    ("YES", "Yes"),
    ("NO", "No"),
    ('NA', "NA"),
]


class LoanMst(models.Model):
    LoanMstID = models.AutoField(primary_key=True)
    CustomerMstID = models.ForeignKey(
        CustomerMst,
        on_delete=models.CASCADE,
        db_column="CustomerMstID",
        null=True,
        blank=True,
    )
    # CustomerMstID = models.IntegerField(null=True, blank=True)
    DisbursementID = models.CharField(max_length=50, blank=True, null=True)
    DisbursementDate = models.DateField(null=True, blank=True)
    DisbursementAmt = models.DecimalField(
        max_digits=15, decimal_places=2, default=0.00, null=True, blank=True
    )
    BranchMstID = models.ForeignKey(
        BranchMst,
        on_delete=models.CASCADE,
        db_column="BranchMstID",
        null=True,
        blank=True,
    )
    LoanType = models.CharField(max_length=50, blank=True, null=True)
    # ExpectedClosingDate=models.DateField(null=False, blank=False)
    InstallmentStartDate = models.DateField(null=True, blank=True)
    RateOfInterest = models.DecimalField(
        max_digits=15, decimal_places=2, default=0.00, null=True, blank=True
    )
    RepaymentTenure = models.IntegerField(null=True, blank=True)
    EMIAmount = models.DecimalField(
        max_digits=15, decimal_places=2, default=0.00, null=True, blank=True
    )
    PaymentFrequency = models.IntegerField(null=True, blank=True)
    LastModifiedDate = models.DateField(null=True, blank=True)
    IsActive = models.BooleanField(null=True, blank=True)
    # ApplicantType=models.CharField(max_length=50, blank=True, null=True)
    ClosingDate = models.DateField(null=True, blank=True)
    ExtraColumn1 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn2 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn3 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn4 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn5 = models.CharField(max_length=255, blank=True, null=True)
    BankMstID = models.ForeignKey(
        BankMst, on_delete=models.PROTECT, null=True, db_column="BankMstID"
    )
    CollectionOfficerID = models.CharField(max_length=255, blank=True, null=True)
    CollectionOfficerName = models.CharField(max_length=255, blank=True, null=True)
    BMID = models.IntegerField(null=True, blank=True)
    whatsapp_opted = models.CharField(
        max_length=3,
        blank=True,
        null=True,
        verbose_name="WhatsApp Opted",
        help_text="Indicates if the customer has opted for WhatsApp communication. Values can be 'YES', 'NO', or 'NA'.",
        choices=CHOICES_WHATSAPP_OPTED,
        default='NA',
    )

    class Meta:
        managed = False
        db_table = "LoanMst"


class LanguageMst(models.Model):
    LngMstID = models.AutoField(primary_key=True)
    Language = models.CharField(max_length=100)
    IsActive = models.BooleanField(default=True)
    CreatedDate = models.DateField(auto_now_add=True)
    UpdatedDate = models.DateField(auto_now=True)
    ExtraColumn1 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn2 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn3 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn4 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn5 = models.CharField(max_length=255, blank=True, null=True)
    BankMstID = models.ForeignKey(
        BankMst, on_delete=models.PROTECT, null=True, db_column="BankMstID"
    )

    def __str__(self):
        return self.Language

    class Meta:
        managed = False
        db_table = "LanguageMst"


class AccountSummary(models.Model):
    BranchMstID = models.ForeignKey(
        BranchMst,
        on_delete=models.CASCADE,
        db_column="BranchMstID",
        null=True,
        blank=True,
        verbose_name="Branch Name",
    )
    BankMstID = models.ForeignKey(
        BankMst,
        on_delete=models.CASCADE,
        db_column="BankMstID",
        null=True,
        blank=True,
        verbose_name="Bank Name",
    )
    CustomerMstID = models.CharField(
        max_length=500, blank=True, null=True, verbose_name="Customer Info ID"
    )
    CustomerCode = models.CharField(
        max_length=500, blank=True, null=True, verbose_name="Customer Code"
    )
    CustomerName = models.CharField(
        max_length=500, blank=True, null=True, verbose_name="Customer Name"
    )
    MobileNumber = models.CharField(
        max_length=500, blank=True, null=True, verbose_name="Mobile Number"
    )
    Gender = models.CharField(
        max_length=500, blank=True, null=True, verbose_name="Gender"
    )
    DateOfBirth = models.DateField(null=True, blank=True, verbose_name="Date of Birth")
    DisbursementID = models.CharField(
        max_length=500, blank=True, null=True, verbose_name="Disbursement ID"
    )
    DisbursementDate = models.DateField(
        null=True, blank=True, verbose_name="Disbursement Date"
    )
    DisbursementAmt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0.00,
        null=True,
        blank=True,
        verbose_name="Disbursement Amount",
    )
    LoanType = models.CharField(
        max_length=50, blank=True, null=True, verbose_name="Loan Type"
    )
    DaysPastDue = models.IntegerField(
        null=True, blank=True, verbose_name="Days Past Due"
    )
    LoanClassification = models.CharField(
        max_length=50, blank=True, null=True, verbose_name="Loan Classification"
    )
    LastPaymentDate = models.DateField(
        null=True, blank=True, verbose_name="Last Payment Date"
    )
    LastCollectedAmount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0.00,
        null=True,
        blank=True,
        verbose_name="Last Collected Amount",
    )
    CurrentBalance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0.00,
        null=True,
        blank=True,
        verbose_name="Current Balance",
    )
    OverDueAmt = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0.00,
        null=True,
        blank=True,
        verbose_name="Overdue Amount",
    )
    PrincipleOutstanding = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0.00,
        null=True,
        blank=True,
        verbose_name="Principal Outstanding",
    )
    InterestOutstanding = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0.00,
        null=True,
        blank=True,
        verbose_name="Interest Outstanding",
    )
    TotalOutstanding = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0.00,
        null=True,
        blank=True,
        verbose_name="Total Outstanding",
    )
    PrincipalPending = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0.00,
        null=True,
        blank=True,
        verbose_name="Principal Pending",
    )
    InterestPending = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0.00,
        null=True,
        blank=True,
        verbose_name="Interest Pending",
    )
    TotalPending = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0.00,
        null=True,
        blank=True,
        verbose_name="Total Pending",
    )
    ClosingDate = models.DateField(null=True, blank=True, verbose_name="Closing Date")
    IsActive = models.BooleanField(default=True, verbose_name="Is Active")
    EMIAmount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0.00,
        null=True,
        blank=True,
        verbose_name="EMI Amount",
    )
    NextEMIDate = models.DateField(null=True, blank=True, verbose_name="Next EMI Date")
    LoanMstID = models.IntegerField(verbose_name="Loan Master ID")
    ExtraColumn1 = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="Extra Column 1"
    )
    ExtraColumn2 = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="Extra Column 2"
    )
    ExtraColumn3 = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="Extra Column 3"
    )
    ExtraColumn4 = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="Extra Column 4"
    )
    ExtraColumn5 = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="Extra Column 5"
    )
    As_On_Date = models.DateField(null=True, blank=True, verbose_name="As On Date")
    Branch_id = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="Branch ID"
    )
    Branch = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="Branch Name"
    )
    Region_id = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="Region ID"
    )
    Region = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="Region Name"
    )
    State_id = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="State ID"
    )
    Region = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="Region Name"
    )
    State = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="State Name"
    )
    CollectionOfficerID = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="Collection Officer ID"
    )
    CollectionOfficerName = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="Collection Officer Name"
    )
    Circle_id = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="Circle ID"
    )
    Circle = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="Circle Name"
    )
    District = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="District Name"
    )
    District_id = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="District ID"
    )
    HeadOffice = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="Head Office Name"
    )
    HeadOffice_id = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="Head Office ID"
    )
    First_Time_Arrear_Clients = models.BooleanField(default=False)
    primarylanguage = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="primarylanguage"
    )
    secondarylanguage = models.CharField(
        max_length=255, blank=True, null=True, verbose_name="secondarylanguage"
    )
    whatsapp_opted = models.CharField(
        max_length=3,
        blank=True,
        null=True,
        verbose_name="WhatsApp Opted",
        help_text="Indicates if the customer has opted for WhatsApp communication. Values can be 'YES', 'NO', or NULL.",
        choices=CHOICES_WHATSAPP_OPTED,
    )

    class Meta:
        """
        Meta:
            - managed (bool): Indicates whether the model is managed by Django's ORM.
              Set to False to prevent Django from creating or modifying the corresponding database table.
            - db_table (str): Specifies the name of the database table associated with the model.
            - column_metadata (dict): Contains metadata for each column in the table, including:
                - verbose_name (str): A human-readable name for the column.
                - type (str): The data type or input type for the column (e.g., 'text', 'dropdown', 'date', 'number', 'boolean').
        """

        managed = False
        db_table = "AccountSummary"

    column_metadata = {
        # "BranchMstID": {"verbose_name": "Branch Name", "type": "dropdown"},
        # "BankMstID": {"verbose_name": "Bank Name", "type": "dropdown"},
        "CustomerInfoID": {"verbose_name": "Customer Info ID", "type": "dropdown"},
        "CustomerCode": {"verbose_name": "Customer Code", "type": "dropdown"},
        "CustomerName": {"verbose_name": "Customer Name", "type": "dropdown"},
        "MobileNumber": {"verbose_name": "Mobile Number", "type": "dropdown"},
        "Gender": {"verbose_name": "Gender", "type": "dropdown"},
        "DateOfBirth": {"verbose_name": "Date of Birth", "type": "date"},
        "DisbursementID": {"verbose_name": "Disbursement ID", "type": "dropdown"},
        "DisbursementDate": {"verbose_name": "Disbursement Date", "type": "date"},
        "DisbursementAmt": {"verbose_name": "Disbursement Amount", "type": "number"},
        "LoanType": {"verbose_name": "Loan Type", "type": "dropdown"},
        "DaysPastDue": {"verbose_name": "Days Past Due", "type": "number"},
        "LoanClassification": {
            "verbose_name": "Loan Classification",
            "type": "dropdown",
        },
        "LastPaymentDate": {"verbose_name": "Last Payment Date", "type": "date"},
        "LastCollectedAmount": {
            "verbose_name": "Last Collected Amount",
            "type": "number",
        },
        "CurrentBalance": {"verbose_name": "Current Balance", "type": "number"},
        "OverDueAmt": {"verbose_name": "Overdue Amount", "type": "number"},
        "PrincipleOutstanding": {
            "verbose_name": "Principal Outstanding",
            "type": "number",
        },
        "InterestOutstanding": {
            "verbose_name": "Interest Outstanding",
            "type": "number",
        },
        "TotalOutstanding": {"verbose_name": "Total Outstanding", "type": "number"},
        "PrincipalPending": {"verbose_name": "Principal Pending", "type": "number"},
        "InterestPending": {"verbose_name": "Interest Pending", "type": "number"},
        "TotalPending": {"verbose_name": "Total Pending", "type": "number"},
        "ClosingDate": {"verbose_name": "Closing Date", "type": "date"},
        "IsActive": {"verbose_name": "Is Active", "type": "boolean"},
        "EMIAmount": {"verbose_name": "EMI Amount", "type": "number"},
        "NextEMIDate": {"verbose_name": "Next EMI Date", "type": "date"},
        "LoanMstID": {"verbose_name": "Loan Master ID", "type": "number"},
        "ExtraColumn1": {"verbose_name": "Pending EMIs", "type": "text"},
        "ExtraColumn2": {"verbose_name": "Extra Column 2", "type": "text"},
        "ExtraColumn3": {"verbose_name": "Extra Column 3", "type": "text"},
        "ExtraColumn4": {"verbose_name": "Extra Column 4", "type": "text"},
        "ExtraColumn5": {"verbose_name": "Extra Column 5", "type": "text"},
        "As_On_Date": {"verbose_name": "As On Date", "type": "date"},
        "Branch_id": {"verbose_name": "Branch ID", "type": "dropdown"},
        "Branch": {"verbose_name": "Branch Name", "type": "dropdown"},
        "Region_id": {"verbose_name": "Region ID", "type": "dropdown"},
        "Region": {"verbose_name": "Region Name", "type": "dropdown"},
        "State_id": {"verbose_name": "State ID", "type": "dropdown"},
        "State": {"verbose_name": "State Name", "type": "dropdown"},
        "CollectionOfficerID": {
            "verbose_name": "Collection Officer ID",
            "type": "dropdown",
        },
        "CollectionOfficerName": {
            "verbose_name": "Collection Officer Name",
            "type": "dropdown",
        },
        "Circle_id": {"verbose_name": "Circle ID", "type": "dropdown"},
        "Circle": {"verbose_name": "Circle Name", "type": "dropdown"},
        "District": {"verbose_name": "District Name", "type": "dropdown"},
        "District_id": {"verbose_name": "District ID", "type": "dropdown"},
        "HeadOffice": {"verbose_name": "Head Office Name", "type": "dropdown"},
        "HeadOffice_id": {"verbose_name": "Head Office ID", "type": "dropdown"},
        "First_Time_Arrear_Clients": {"verbose_name": "First Time Arrear Clients","type": "dropdown"},
        "primarylanguage": {
            "verbose_name": "Primary Language",
            "type": "dropdown",
        },
        "secondarylanguage": {
            "verbose_name": "Secondary Language",
            "type": "dropdown",
        },
        "whatsapp_opted": {
            "verbose_name": "WhatsApp Opted",
            "type": "dropdown",
            "choices": CHOICES_WHATSAPP_OPTED,
        },
    }

    def __str__(self):
        return f"{self.CustomerName} - {self.DisbursementID}"


class UserFeedback(models.Model):
    UserFeedbackID = models.AutoField(primary_key=True)
    BranchMstID = models.IntegerField()
    LoanMstID = models.IntegerField()
    FeedbackDate = models.DateField()
    AgreedToPay = models.BooleanField()
    RefusedToPay = models.BooleanField()
    WrongNumber = models.BooleanField()
    CustomerBusy = models.BooleanField()
    promise_date = models.DateField(null=True, blank=True)
    promise_amount = models.DecimalField(max_digits=10, decimal_places=2)
    ReasonForDenial = models.TextField(null=True, blank=True)
    mode_of_payment = models.CharField(max_length=255, blank=True, null=True)
    CollectionDate = models.CharField(max_length=255, blank=True, null=True)
    CollectionAmount = models.CharField(max_length=255, blank=True, null=True)
    CustomerNotReply = models.CharField(max_length=255, blank=True, null=True)
    OtherComments = models.CharField(max_length=255, blank=True, null=True)
    BankMstID = models.IntegerField()
    Longitude = models.CharField(max_length=255, blank=True, null=True)
    Latitude = models.CharField(max_length=255, blank=True, null=True)
    ImageUrl = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = "UserFeedback"


class Response(models.Model):
    ResponseID = models.AutoField(primary_key=True, db_column="ResponseID")
    LoanMstID = models.IntegerField(db_column="LoanMstID")
    BlasterQueueID = models.IntegerField(db_column="BlasterQueueID")
    IVRQueueID = models.IntegerField(db_column="IVRQueueID")
    VoiceBotQueueID = models.IntegerField(db_column="VoiceBotQueueID")
    WhatsappQueueID = models.IntegerField(db_column="WhatsappQueueID")
    AllocationID = models.IntegerField(db_column="AllocationID")
    FeedbackID = models.IntegerField(db_column="FeedbackID")
    ModeOfPayment = models.CharField(max_length=255, db_column="ModeOfPayment")
    ExtraColumn1 = models.CharField(
        max_length=255, blank=True, null=True, db_column="ExtraColumn1"
    )
    ExtraColumn2 = models.CharField(
        max_length=255, blank=True, null=True, db_column="ExtraColumn2"
    )
    ExtraColumn3 = models.CharField(
        max_length=255, blank=True, null=True, db_column="ExtraColumn3"
    )
    ExtraColumn4 = models.CharField(
        max_length=255, blank=True, null=True, db_column="ExtraColumn4"
    )
    ExtraColumn5 = models.CharField(
        max_length=255, blank=True, null=True, db_column="ExtraColumn5"
    )
    PromiseDateTime = models.DateTimeField(db_column="PromiseDateTime")
    Status = models.CharField(max_length=255, db_column="Status")
    Amount = models.DecimalField(max_digits=15, decimal_places=2, db_column="Amount")
    ResponseDateTime = models.DateTimeField(db_column="ResponseDateTime")
    BankMstID = models.IntegerField(db_column="BankMstID")
    BranchMstID = models.IntegerField()
    wrong_number = models.BooleanField()
    Despute = models.CharField(max_length=255, db_column="Despute")
    CampaignMstID = models.IntegerField(blank=True, null=True)
    class Meta:
        managed = False
        db_table = "Response"


class Transactions(models.Model):
    TransactionID = models.IntegerField(db_column="TransactionID")
    BranchMstID = models.IntegerField(db_column="BranchMstID")
    CreatedDate = models.DateField(db_column="CreatedDate")
    LoanMstID = models.IntegerField(db_column="LoanMstID")
    DisbursementID = models.CharField(max_length=255, db_column="DisbursementID")
    CollectedAmt = models.DecimalField(
        max_digits=10, decimal_places=2, db_column="CollectedAmt"
    )
    CollectedDate = models.DateField(db_column="CollectedDate")
    Processed = models.BooleanField(db_column="Processed")
    PrincipleCollected = models.DecimalField(
        max_digits=10, decimal_places=2, db_column="PrincipleCollected"
    )
    InterestCollected = models.DecimalField(
        max_digits=10, decimal_places=2, db_column="InterestCollected"
    )
    ExtraColumn1 = models.CharField(
        max_length=255, null=True, blank=True, db_column="ExtraColumn1"
    )
    ExtraColumn2 = models.CharField(
        max_length=255, null=True, blank=True, db_column="ExtraColumn2"
    )
    ExtraColumn3 = models.CharField(
        max_length=255, null=True, blank=True, db_column="ExtraColumn3"
    )
    ExtraColumn4 = models.CharField(
        max_length=255, null=True, blank=True, db_column="ExtraColumn4"
    )
    ExtraColumn5 = models.CharField(
        max_length=255, null=True, blank=True, db_column="ExtraColumn5"
    )
    BankMstID = models.IntegerField(db_column="BankMstID")

    class Meta:
        managed = False
        db_table = "Transactions"


# For Chatbot to store conversation history
class ActiveConversation(models.Model):
    """Stores active conversations with their UUIDs"""

    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user_id = models.CharField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    title = models.CharField(max_length=255, default="Conversation")
    conversation_status = models.CharField(
        max_length=255,
        choices=[
            ("active", "Active"),
            ("completed", "Completed"),
            ("abandoned", "Abandoned"),
        ],
        default="active",
    )
    form_data = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"Active Conversation {self.uuid} - {self.user_id} ({self.conversation_status})"

    class Meta:
        managed = False
        db_table = "ActiveConversation"


class ConversationHistory(models.Model):
    """Stores conversation history of completed conversations"""

    uuid = models.UUIDField(primary_key=True, editable=False)
    user_id = models.CharField(max_length=255)
    created_at = models.DateTimeField()
    completed_at = models.DateTimeField(auto_now_add=True)
    form_data = models.TextField(blank=True, null=True)
    full_conversation = models.TextField()  # JSON serialized conversation
    title = models.CharField(max_length=255, default="Conversation")

    def __str__(self):
        return f"Conversation History {self.uuid} - {self.user_id}"

    class Meta:
        managed = False
        db_table = "ConversationHistory"


class ConversationMessage(models.Model):
    """Stores individual messages in a conversation"""

    conversation = models.ForeignKey(
        ActiveConversation, on_delete=models.CASCADE, related_name="messages"
    )
    user_message = models.TextField()
    ai_response = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    is_edited = models.BooleanField(default=False)

    class Meta:
        ordering = ["created_at"]

    def __str__(self):
        return f"Message in Conversation {self.conversation.uuid}"

    class Meta:
        managed = False
        db_table = "ConversationMessage"


# Add a new model to track user's conversations
class UserConversations(models.Model):
    """Maps users to all their conversations"""

    user_id = models.CharField(max_length=255, db_index=True)  # For fast lookups
    active_conversations = models.ManyToManyField(
        ActiveConversation, related_name="users_active", blank=True
    )
    completed_conversations = models.ManyToManyField(
        ConversationHistory, related_name="users_completed", blank=True
    )
    last_active_conversation = models.ForeignKey(
        ActiveConversation,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="last_active_for_users",
    )

    def __str__(self):
        return f"User {self.user_id} Conversations"

    class Meta:
        managed = False
        db_table = "UserConversations"


class SubscriptionMst(models.Model):
    SubscriptionMstID = models.AutoField(primary_key=True)
    SubscriptionType = models.CharField(max_length=255)
    IsActive = models.BooleanField(default=True)
    StartDate = models.DateField()
    EndDate = models.DateField()
    LastUpdated = models.DateField()
    ExtraColumn1 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn2 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn3 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn4 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn5 = models.CharField(max_length=255, blank=True, null=True)
    BankMstID = models.IntegerField()
    Screens = models.JSONField()

    class Meta:
        managed = False
        db_table = "SubscriptionMst"


class ComponentMst(models.Model):
    BankMstID = models.IntegerField()
    Designation = models.CharField(max_length=255)
    Screens = models.JSONField()

    class Meta:
        managed = False
        db_table = "ComponentMst"


class CommunicationMapping(models.Model):
    BankMstID = models.IntegerField()
    WhatsApp = models.BooleanField()
    VoiceBot = models.BooleanField()
    Blaster = models.BooleanField()
    IVR = models.BooleanField()
    SMS = models.BooleanField()

    class Meta:
        managed = False
        db_table = "CommunicationMapping"


class RecordingSummary(models.Model):
    Recording = models.CharField(max_length=555)
    Summarization = models.CharField(max_length=555)

    class Meta:
        managed = False
        db_table = "RecordingSummary"


class BankControls(models.Model):
    ControlID = models.AutoField(primary_key=True)
    BankMstID = models.OneToOneField(
        BankMst, on_delete=models.CASCADE, db_column="BankMstID"
    )
    Json = models.JSONField(default=dict)

    class Meta:
        managed = False
        db_table = "bankcontrols"


class WrongNumberHistory(models.Model):
    BankMstID = models.IntegerField()
    CustomerMstID = models.IntegerField()
    LoanMstID = models.IntegerField()
    PreviousNumber = models.CharField(max_length=555)
    UpdatedNumber = models.CharField(max_length=555)

    class Meta:
        managed = False
        db_table = "WrongNumberHistory"


class VoiceBotHistory(models.Model):
    VoiceBotHistoryID = models.AutoField(primary_key=True)
    VoiceBotQueueID = models.IntegerField(null=True, blank=True)
    CreatedDate = models.DateTimeField(null=True, blank=True)
    BankMstID = models.IntegerField(null=True, blank=True)
    BranchMstID = models.IntegerField(null=True, blank=True)
    LoanMstID = models.IntegerField(null=True, blank=True)
    BranchCode = models.CharField(max_length=255, null=True, blank=True)
    CustomerID = models.CharField(max_length=255, null=True, blank=True)
    DisbursementID = models.CharField(max_length=255, null=True, blank=True)
    ContactNumber = models.CharField(max_length=255, null=True, blank=True)
    BranchName = models.CharField(max_length=255, null=True, blank=True)
    LoanType = models.CharField(max_length=255, null=True, blank=True)
    CustomerName = models.CharField(max_length=255, null=True, blank=True)
    Overdue_Amount = models.DecimalField(
        max_digits=15, decimal_places=2, null=True, blank=True
    )
    Next_EMI_Amount = models.DecimalField(
        max_digits=15, decimal_places=2, null=True, blank=True
    )
    Next_EMI_Date = models.DateField(null=True, blank=True)
    Total_Collection = models.DecimalField(
        max_digits=15, decimal_places=2, null=True, blank=True
    )
    Latest_CollectedDate = models.DateField(null=True, blank=True)
    Latest_CollectedAmt = models.DecimalField(
        max_digits=15, decimal_places=2, null=True, blank=True
    )
    IsSent = models.BooleanField(default=False)
    IsDelivered = models.BooleanField(default=False)
    IsRead = models.BooleanField(default=False)
    IsResponded = models.BooleanField(default=False)
    Response = models.TextField(null=True, blank=True)
    Type = models.CharField(max_length=255, null=True, blank=True)
    BankName = models.CharField(max_length=255, null=True, blank=True)
    Identifier = models.CharField(max_length=255, null=True, blank=True)
    CallID = models.CharField(max_length=255, null=True, blank=True)
    Recording = models.CharField(max_length=255, null=True, blank=True)
    Conversation_json = models.JSONField(null=True, blank=True)
    Conclusion = models.CharField(max_length=255, null=True, blank=True)
    CallTried = models.IntegerField(null=True, blank=True)
    CallDuration = models.FloatField(null=True, blank=True)
    BasedOn = models.CharField(max_length=255, null=True, blank=True)
    CommFlowID = models.IntegerField(null=True, blank=True)
    CustomerCode = models.CharField(max_length=255, null=True, blank=True)
    Language = models.CharField(max_length=255, null=True, blank=True)
    OverdueAmt = models.DecimalField(
        max_digits=15, decimal_places=2, null=True, blank=True
    )
    Slot = models.CharField(max_length=255, null=True, blank=True)
    FlowID = models.IntegerField(null=True, blank=True)
    VoiceBotTemplateMappingID = models.IntegerField(null=True, blank=True)
    Extracolumn1 = models.CharField(max_length=255, null=True, blank=True)

    class Meta:
        db_table = "VoiceBotHistory"
        managed = False


class BlasterHistory(models.Model):
    BlasterHistoryID = models.AutoField(primary_key=True)
    BlasterQueueID = models.IntegerField(null=True, blank=True)
    CreatedDate = models.DateTimeField(null=True, blank=True)
    BankMstID = models.IntegerField(null=True, blank=True)
    BranchMstID = models.IntegerField(null=True, blank=True)
    LoanMstID = models.IntegerField(null=True, blank=True)
    CustomerID = models.IntegerField(null=True, blank=True)
    DisbursementID = models.CharField(max_length=255, null=True, blank=True)
    ContactNumber = models.CharField(max_length=255, null=True, blank=True)
    LoanType = models.CharField(max_length=255, null=True, blank=True)
    CustomerName = models.CharField(max_length=255, null=True, blank=True)
    Next_EMI_Amount = models.DecimalField(
        max_digits=15, decimal_places=2, null=True, blank=True
    )
    Next_EMI_Date = models.DateField(null=True, blank=True)
    Total_Collection = models.DecimalField(
        max_digits=15, decimal_places=2, null=True, blank=True
    )
    Latest_CollectedDate = models.DateField(null=True, blank=True)
    Latest_CollectedAmt = models.DecimalField(
        max_digits=15, decimal_places=2, null=True, blank=True
    )
    IsSent = models.BooleanField(default=False)
    Type = models.CharField(max_length=255, null=True, blank=True)
    BankName = models.CharField(max_length=255, null=True, blank=True)
    Identifier = models.CharField(max_length=255, null=True, blank=True)
    Language = models.CharField(max_length=255, null=True, blank=True)
    CallID = models.CharField(max_length=255, null=True, blank=True)
    Recording = models.CharField(max_length=255, null=True, blank=True)
    Conversation_json = models.JSONField(null=True, blank=True)
    Conclusion = models.CharField(max_length=255, null=True, blank=True)
    CallTried = models.IntegerField(null=True, blank=True)
    CallDuration = models.FloatField(null=True, blank=True)
    CallConnected = models.BooleanField(default=False)
    Slot = models.CharField(max_length=255, null=True, blank=True)
    BasedOn = models.CharField(max_length=255, null=True, blank=True)
    BranchCode = models.CharField(max_length=255, null=True, blank=True)
    CustomerCode = models.CharField(max_length=255, null=True, blank=True)
    FlowID = models.IntegerField(null=True, blank=True)
    BlasterTemplateMappingID = models.IntegerField(null=True, blank=True)
    IsDelivered = models.BooleanField(default=False)
    IsRead = models.BooleanField(default=False)
    IsResponded = models.BooleanField(default=False)
    OverDueAmt = models.DecimalField(
        max_digits=15, decimal_places=2, null=True, blank=True
    )

    class Meta:
        db_table = "BlasterHistory"
        managed = False


class AI_Route(models.Model):
    LoanMstIDS = models.CharField(max_length=2555, null=True, blank=True)
    FO_ID = models.IntegerField(null=True, blank=True)
    CreatedDate = models.DateTimeField(null=True, blank=True)
    BranchMstID = models.IntegerField(null=True, blank=True)
    BankMstID = models.IntegerField(null=True, blank=True)
    Route_Name = models.CharField(max_length=2555, null=True, blank=True)

    class Meta:
        db_table = "AI_Route"
        managed = False




class Reallocation(models.Model):
    ReallocationID = models.AutoField(primary_key=True)
    LoanMstID = models.IntegerField()
    CustomerMstID = models.IntegerField()
    OldOfficerID = models.IntegerField()
    OldOfficerName = models.CharField(max_length=255)
    NewOfficerID = models.IntegerField()
    NewOfficerName = models.CharField(max_length=255)
    ActionType = models.CharField(max_length=20)  # 'Temporary' or 'Permanent'
    StartDate = models.DateField(null=True, blank=True)
    EndDate = models.DateField(null=True, blank=True)
    BranchName = models.CharField(max_length=255)
    CustomerName = models.CharField(max_length=255)
    DisbursementID = models.CharField(max_length=255)
    Status = models.CharField(max_length=255)  # 'Pending', 'Completed', 'Cancelled'
    CreatedBy = models.CharField(max_length=255)
    CreatedDate = models.DateTimeField(auto_now_add=True)
    IsReverted = models.BooleanField(default=False)
    RevertDate = models.DateTimeField(null=True, blank=True)
    Designation = models.CharField(max_length=255, null=True, blank=True)
    class Meta:
        db_table = 'Reallocation'
        managed = False  # Set to False if you do not want Django to manage the table

    def __str__(self):
        return f'Reallocation {self.ReallocationID} - Loan {self.LoanMstID}'
    
    
    
    
class CallingQueue(models.Model):
    CallingQueueID = models.IntegerField(primary_key=True)
    CreatedDate = models.DateTimeField()
    BranchMstID = models.IntegerField()
    LoanMstID = models.IntegerField()
    BranchCode = models.CharField(max_length=50)
    LoanType = models.CharField(max_length=100)
    CustomerCode = models.CharField(max_length=50)
    CustomerName = models.CharField(max_length=255)
    Language = models.CharField(max_length=100)
    BankName = models.CharField(max_length=255)
    ExtraColumn1 = models.CharField(max_length=255)
    ExtraColumn2 = models.CharField(max_length=255)
    ExtraColumn3 = models.CharField(max_length=255)
    ExtraColumn4 = models.CharField(max_length=255)
    ExtraColumn5 = models.CharField(max_length=255)
    Slot = models.CharField(max_length=255)
    CallTried = models.IntegerField()
    CallConnected = models.BooleanField()
    BankMstID = models.IntegerField()
    BasedOn = models.CharField(max_length=255)
    CommFlowID = models.IntegerField()
    ContactNumber = models.CharField(max_length=255)
    CallDuration = models.FloatField()

    CustomerID = models.IntegerField()
    DisbursementID = models.CharField(max_length=255)
    FlowID = models.IntegerField()
    CallingTemplateMappingID = models.IntegerField()
    Total_Collection = models.IntegerField()
    Latest_CollectedDate = models.DateField()
    Latest_CollectedAmt = models.IntegerField()
    Next_EMI_Amount = models.IntegerField()
    Next_EMI_Date = models.DateField()
    Identifier = models.CharField(max_length=255)
    Type = models.CharField(max_length=255)
    OverDueAmt = models.DecimalField(max_digits=18, decimal_places=2)
    AlternativeMobileNumber = models.CharField(max_length=20)
    SecondaryCustMstID = models.IntegerField()
    ApplicantName = models.CharField(max_length=255)
    CoApplicantContact = models.CharField(max_length=20)
    
    class Meta:
        managed = False
        db_table = "CallingQueue"
    
    def __str__(self):
        return f"{self.CustomerName} - {self.ContactNumber}"
    
    
    
class CallingHistory(models.Model):
    CallingHistoryID = models.IntegerField(primary_key=True)
    CallingQueueID = models.IntegerField()
    CreatedDate = models.DateTimeField()
    BankMstID = models.IntegerField()
    BranchMstID = models.IntegerField()
    LoanMstID = models.IntegerField()
    CustomerID = models.IntegerField()
    DisbursementID = models.CharField(max_length=255)
    ContactNumber = models.CharField(max_length=15)
    LoanType = models.CharField(max_length=100)
    CustomerName = models.CharField(max_length=255)
    Next_EMI_Amount = models.DecimalField(max_digits=15, decimal_places=2)
    Next_EMI_Date = models.DateField()
    Total_Collection = models.DecimalField(max_digits=20, decimal_places=2)
    Latest_CollectedDate = models.DateField()
    Latest_CollectedAmt = models.DecimalField(max_digits=15, decimal_places=2)
    IsSent = models.BooleanField()
    Type = models.CharField(max_length=50)
    BankName = models.CharField(max_length=255)
    Identifier = models.CharField(max_length=255)
    Language = models.CharField(max_length=255)
    CallID = models.CharField(max_length=255)
    Recording = models.CharField(max_length=255)

    Conversation_json = models.JSONField()
    Conclusion = models.CharField(max_length=255)
    CallTried = models.IntegerField()
    CallDuration = models.FloatField()
    CallConnected = models.BooleanField()
    Slot = models.CharField(max_length=255)
    BasedOn = models.CharField(max_length=255)
    BranchCode = models.CharField(max_length=255)
    CustomerCode = models.CharField(max_length=255)
    FlowID = models.IntegerField()
    CallingTemplateMappingID = models.IntegerField()
    IsDelivered = models.BooleanField()
    IsRead = models.BooleanField()
    IsResponded = models.BooleanField()
    OverDueAmt = models.DecimalField(max_digits=18, decimal_places=2)
    AlternativeMobileNumber = models.CharField(max_length=20)
    SecondaryCustMstID = models.IntegerField()
    ApplicantName = models.CharField(max_length=255)
    CoApplicantContact = models.CharField(max_length=20)
    
    class Meta:
        managed = False
        db_table = "CallingHistory"
    
    def __str__(self):
        return f"History #{self.CallingHistoryID} - {self.CustomerName}"    
import PropTypes from 'prop-types';
import React from 'react';
import ReactApex<PERSON>hart from 'react-apexcharts';

export function AccountChart({data}) {
  // Map API data to categories and series
  const categories = data.map(item => item.loan_cycle);
  const seriesData = data.map(item => item.accounts);
  
  const options = {
    chart: {
      type: 'bar',
      toolbar: { show: false }
    },
    xaxis: {
      categories: categories,
      title: { text: 'Loan Cycle ID' }
    },
    yaxis: {
      title: { text: 'Accounts' }
    },
    plotOptions: {
      bar: {
        borderRadius: 0,
        columnWidth: '40px',
        distributed: false
      }
    },
    dataLabels: {
      enabled: true,
      style: {
        fontSize: '14px',
        colors: ['#000000'] 
      }
    },
    colors: ['#0033A0'],
      responsive: [
        {
          breakpoint: 768,
          options: {
            chart: {
              height: 250
            },
            plotOptions: {
              bar: {
                columnWidth: '50%' // wider bars for smaller screens
              }
            },
            xaxis: {
              labels: {
                rotate: -30,
                style: {
                  fontSize: '10px'
                }
              }
            },
            dataLabels: {
              style: {
                fontSize: '12px'
              }
            }
          }
        }
      ]    
    };
    
    const series = [
      {
        name: 'Accounts',
        data: seriesData
      }
  ];
    
  return <ReactApexChart options={options} series={series} type="bar" height={300} />;
}
AccountChart.propTypes={
  data: PropTypes.array
}
import { Flex, message, Select, Table, Typography } from "antd";
import React, { useEffect, useState } from "react";
import { AXIOS } from "../../../apis/ho-Instance";
import Style from "./_style.module.scss";
import ApplicationLoader from "../../ApplicationLoader";
import { ALL_CACHE_NAMES, handleDownloadTable } from "../../../constant";
import DOWNLOAD_IMG from "../../../assets/Images/download.svg";
import {
  getFromCache,
  getPostCacheKey,
  storeToCache,
} from "../../../utils/cacheHelper";

const { Text } = Typography;

const CACHE_NAME = ALL_CACHE_NAMES.ANALYSIS_CACHE;

export function PromiseTable() {
  const [data, setData] = useState([]);
  const [prevData, setPrevData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedReason, setSelectedReason] = useState("today");
  const [selectedDates, setSelectedDates] = useState({
    fromDate: new Date().toISOString().split("T")[0],
    toDate: new Date().toISOString().split("T")[0],
  });

  const handleLanPromiseClick = (lanPromise) => {
    console.log("LAN Promise clicked:", lanPromise);
  };

  // Get state
  const handleGetState = async () => {
    setIsLoading(true);
    const body = {
      from_date: selectedDates.fromDate,
      to_date: selectedDates.toDate,
      BasedOn: "PromiseDate",
    };
    const cacheKey = getPostCacheKey({
      endpoint: "analysis-promise/",
      body,
    });
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setData(cachedData);
      setPrevData(cachedData);
      setIsLoading(false);
    }
    try {
      const response = await AXIOS.post("allocation/", body);
      if (
        response.status === 200 &&
        Array.isArray(response.data) &&
        response.data?.length
      ) {
        setData(response.data);
        setPrevData(response.data);
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
      } else {
        setData([]);
        setPrevData([]);
      }
    } catch (error) {
      console.log("Error in Promise", error);
      setData([]);
      setPrevData([]);
    } finally {
      setIsLoading(false);
    }
  };

  //Get Region, branch, CO.
  const handleGetOtherColsData = async ({ BUType, BUName }) => {
    setIsLoading(true);
    const body = {
      from_date: selectedDates.fromDate,
      to_date: selectedDates.toDate,
      BasedOn: "PromiseDate",
      BUType,
      BUName,
    };
    const cacheKey = getPostCacheKey({ endpoint: "analysis-promise/", body });
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setData(cachedData);
      setPrevData(cachedData);
      setIsLoading(false);
    }
    try {
      const res = await AXIOS.post("allocation/", body);
      if (res.status === 200 && Array.isArray(res.data) && res.data?.length) {
        setData(res.data);
        setPrevData(res.data);
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: res.data,
        });
      } else {
        message.warning(`Sorry, there is not data related to ${BUName}`);
        setData(prevData);
      }
    } catch (error) {
      console.log("Error in Promise table", error?.message);
      message.warning(`No data found for ${BUName}`);
      setData(prevData);
    } finally {
      setIsLoading(false);
    }
  };

  // Extract dynamic keys from the first item of the API response (excluding lan & amount)
  const dynamicKeys = data?.length
    ? Object.keys(data[0]).filter((key) => key !== "lan" && key !== "amount")
    : [];

  // Create dynamic column definitions
  const dynamicColumns = dynamicKeys.map((key) => ({
    title: key,
    dataIndex: key,
    sorter: (a, b) => a[key]?.localeCompare?.(b[key]),
    render: (_, record) => (
      <Text
        className={Style.blueText}
        onClick={() =>
          handleGetOtherColsData({ BUType: key, BUName: record[key] })
        }
      >
        {record[key] || "--"}
      </Text>
    ),
  }));

  // Change Date
  const handleDateSelection = (value) => {
    setSelectedReason(value);
    if (value === "today") {
      const today = new Date().toISOString().split("T")[0];
      setSelectedDates({ fromDate: today, toDate: today });
    } else if (value === "next_week") {
      const nextWeekDate = new Date();
      nextWeekDate.setDate(nextWeekDate.getDate() + 7);
      const formattedNextWeekDate = nextWeekDate.toISOString().split("T")[0];
      const today = new Date().toISOString().split("T")[0];
      setSelectedDates({
        fromDate: today,
        toDate: formattedNextWeekDate,
      });
    }
  };

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      sorter: (a, b) => a.key - b.key,
      render: (_, data) => <Text>{data?.key}.</Text>,
    },
    ...dynamicColumns,
    {
      title: "#LAN",
      dataIndex: "lan",
      sorter: (a, b) => a.lan - b.lan,
      render: (_, data) => (
        <Text
          onClick={() => handleLanPromiseClick(data.lanPromise)}
          style={{ cursor: "pointer", color: "black" }}
        >
          {data?.lan}
        </Text>
      ),
    },
    {
      title: "Promise Amount",
      dataIndex: "amount",
      key: "amount",
      sorter: (a, b) => a.amount - b.amount,
    },
  ];

  const dataSource = Array.isArray(data)
    ? data.map((data, i) => ({
        key: i + 1,
        ...data,
      }))
    : [];

  useEffect(() => {
    handleGetState();
  }, [selectedDates]);

  // Handle downloading
  const handleDownload = async () => {
    if (data.length === 0) {
      return message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: "Allocation-Analysis EMI Collection",
        worksheetName: "Allocation-Analysis-EMI-Collection",
        tableData: data,
      });
    }
  };
  return (
    <Flex vertical gap={20}>
      <div className={Style.dropdownContainer}>
        <Select
          className={Style.dropdown}
          defaultValue={selectedReason}
          onChange={handleDateSelection}
          options={[
            { label: "Today", value: "today" },
            { label: "Next week", value: "next_week" },
          ]}
        />
        <button className={Style.download_button} onClick={handleDownload}>
          <img src={DOWNLOAD_IMG} alt="download-button" />
        </button>
      </div>
      {isLoading ? (
        <ApplicationLoader />
      ) : (
        <Table
          bordered
          virtual
          className={Style.CustomTable}
          columns={columns}
          dataSource={dataSource}
          scroll={{
            x: 800,
            y: 460,
          }}
          pagination={{
            showSizeChanger: false,
          }}
        />
      )}
    </Flex>
  );
}

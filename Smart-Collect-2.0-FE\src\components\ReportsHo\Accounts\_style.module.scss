$table-radius: 22px;
$attempts-bg: #bdd1ff;
$disable: #787777;
$body: #e4f8f9;

.customTable {
  padding-top: 1.5rem;
  .blue_text {
    font-weight: 500;
    color: #407bff;
  }

  .icon_div {
    display: flex;
    justify-content: center;
    width: 100%;
    .icon_img {
      width: 20px;
      height: 20px;
      img {
        width: 100%;
        object-fit: contain;
        cursor: pointer;
      }
    }
  }

  .icon {
    display: flex;
    align-items: center;
    cursor: pointer;
    img {
      width: 3.5px;
    }
  }

  .text {
    color: white;
    font-family: "Kanit", "Courier New", Courier, monospace;
  }

  //Global ant design classes

  // Pagination
  :global(.ant-pagination) {
    justify-content: center;
    margin: 0 !important;

    :global(.ant-pagination-prev),
    :global(.ant-pagination-next) {
      color: var(--dark-blue);
      border: 0 solid var(--blue);
      background: #e4f8f9;
      height: 25px;
      min-width: 15px;
      border-radius: 0px;
      margin: 0;
      button {
        border-radius: 0px;
      }
    }
    :global(.ant-pagination-item) {
      margin-right: 0;
      height: 0;
      a {
        color: $disable;
        font-size: 0.9rem;
        line-height: 23px;
        font-family: "Kanit", "Courier New", Courier, monospace;
        font-weight: normal;
      }
      &:hover {
        background-color: transparent;
      }
    }
    :global(.ant-pagination-item-active) {
      border: none;
      a {
        color: var(--dark-blue);
        font-size: 1.2rem;
        padding: 0;
      }
    }
  }

  //Table container
  :global(.ant-table-container) {
    padding: 0;
    margin-bottom: 0.5rem;
    border-start-start-radius: $table-radius;
    border-start-end-radius: $table-radius;

    // Table header
    :global(.ant-table-header) {
      position: relative;
      margin: 0 auto;
      top: -21px;
      border-radius: $table-radius;
      :global(.ant-table-column-has-sorters) {
        background-color: var(--dark-blue);
        &:hover {
          background-color: var(--dark-blue);
        }

        :global(.ant-table-column-sorter-up),
        :global(.ant-table-column-sorter-down) {
          svg {
            fill: white;
          }
          &:global(.active) {
            svg {
              fill: rgb(24, 155, 249);
            }
          }
        }
      }
    }

    // Table virtual body
    :global(.ant-table-tbody-virtual) {
      margin-top: -8px;
    }

    &::before {
      box-shadow: none !important;
    }
  }

  // Table rows header
  :global(.ant-table-thead > tr) {
    th {
      border-start-end-radius: 0 !important;
      background-color: var(--dark-blue);
      border-inline-end: none !important;
      color: white;
      border-bottom: none;
      text-align: center;
      font-family: "Kanit", "Courier New", Courier, monospace;
      font-weight: normal;
      &:not(:last-child):not(.ant-table-selection-column):not(
          .ant-table-row-expand-icon-cell
        ):not([colspan])::before {
        width: 0;
      }
    }
  }

  // Table body
  :global(.ant-table-tbody) {
    // Body rows
    :global(.ant-table-row) {
      &:nth-child(even) {
        background-color: var(--light-green);
      }

      :global(.ant-table-cell-row-hover) {
        background: transparent;
      }
      // Cols
      :global(.ant-table-cell) {
        font-family: "Kanit", "Courier New", Courier, monospace;
        font-weight: normal;
        text-align: center;
        border-bottom: 2px solid white;
        font-size: 11px;
        padding: 0.8rem;

        :global(.ant-typography) {
          font-family: "Kanit", "Courier New", Courier, monospace;
        }

        &:global(.ant-table-cell-fix-right) {
          border-bottom: none !important;
        }
        &:global(.ant-table-cell-fix-right-first) {
          border-left: 2px solid white;
        }
      }
    }
  }

  // Fixed Cols
  :global(.ant-table-cell-fix-right) {
    background-color: $body;
  }
}

.comm_card,
.cate_card {
  background-color: #ebfdff;
  border-left: 4px solid var(--dark-blue);
  border-radius: 5px;
  padding: 0.5rem 1rem 0 1rem;
  flex: 1;

  .img_div {
    width: 55px;
    height: 55px;
    img {
      width: 100%;
      object-fit: contain;
    }
  }
  .title,
  .value,
  .small_text {
    font-family: "kanit", "Courier New", Courier, monospace;
    font-size: 1rem;
    color: #0f2050;
  }
  .title {
    font-weight: 300;
  }
  .value {
    font-weight: 500;
    font-size: 1.2rem;
  }
}

.cate_card {
  background-color: white;
  box-shadow: 2.8px 2.8px 2.8px 0px #00000033;
  border-color: #0f2050;
  padding: 0 1rem;
  .title {
    text-align: center;
    margin-bottom: 0.2rem;
    font-size: 16px;
    font-weight: 400;
  }
  .small_text {
    font-size: 13px;
  }
}

.analysis_container {
  width: 100%;

  .collection_summary,
  .matrix_summary {
    background-color: #ebfdff;
    padding: 1rem;
    border-radius: 5px;

    .header {
      margin-bottom: 0.5rem;
      padding-left: 50%; // to align with data rows
      :global(.ant-col) {
        :global(.ant-typography) {
          font-family: "Kanit", "Courier New", Courier, monospace;
          font-size: 16px;
        }
      }
    }

    .summary_row {
      margin-bottom: 10px;
      align-items: center;

      .label {
        font-weight: 500;
        font-size: 16px;
        color: #0f2050;
        font-family: "Kanit", "Courier New", Courier, monospace;
      }

      .count_input,
      .amount_input {
        font-weight: 600;
        text-align: right;
        border-radius: 6px;
        background-color: #fff;
        color: #0f2050;
      }

      .amount_input {
        color: #1677ff; // same as ₹ blue color
      }

      .collection_count {
        background-color: white;
        box-shadow: 2px 2px 2px 0px #00000026;
        :global(.ant-col) {
          text-align: center;
          :global(.ant-typography) {
            font-family: "Kanit", "Courier New", Courier, monospace;
          }
        }
      }
    }
  }

  .matrix_summary {
    .heading,
    .label,
    .value {
      color: #0f2050;
      font-family: "Kanit", "Courier New", Courier, monospace;
    }
    .heading {
      font-weight: 500;
      font-size: 16px;
    }
    .label {
      font-weight: 300;
      font-size: 14px;
    }
    .value {
      font-weight: 600;
      font-size: 14px;
    }
    .count_container {
      padding: 0 1.5rem;
    }
    .summary {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.5rem 15px !important;
    }
  }
  .responses_summary {
    background-color: #ebfdff;
    padding: 2px 1rem 0 !important;
    border-radius: 5px;

    .heading {
      font-weight: 600;
      color: #0f2050;
      display: block;
      margin-bottom: 0.5rem;
      font-size: 1.2rem;
    }

    .header {
      margin-bottom: 0.3rem;
      .title {
        font-size: 14px;
        color: #0f2050;
        font-weight: 400;
      }
    }
    .summary_row {
      background-color: white;
      margin-bottom: 0.5rem;
      padding: 0.3rem 0.5rem;
      border-radius: 7px;
      .value,
      .amount {
        font-weight: 600;
        color: #0f2050;
      }
      .amount {
        color: #1677ff;
      }
    }

    .label {
      font-weight: 500;
      color: #0f2050;
      font-size: 14px;
    }
  }
}

.category_container {
  background-color: #ebfdff;
  padding: 1rem;
  border-radius: 4px;

  .heading {
    color: #0f2050;
    font-family: "Kanit", "Courier New", Courier, monospace;
    font-size: 1.2rem;
    margin-bottom: 0.4rem;
    font-weight: 500;
  }
}
// Responsive view
@media screen and (max-width: 768px) {
  .customTable {
    :global(.ant-table-thead > tr) {
      th {
        padding: 0.5rem;
      }
    }
    :global(.ant-table-tbody) {
      :global(.ant-table-row) {
        :global(.ant-table-cell) {
          padding: 0.5rem;
        }
      }
    }
  }
  .container{
    flex-direction: column;
  }
  .analysis_container{
    .collection_matrix{
      padding: 2px 0 0 !important;  
      margin-top: 1rem;
      .header {
        :global(.ant-col) {
          :global(.ant-typography) {
            font-size: 11px;
          }
        }
      }
      .summary_row{
        .label{
            font-size: 10px;
          }
        .count_input,
        .amount_input{
          font-size: 10px;
        }
        .collection_count{
          line-height: 0;
        }
      }
    }
    .responses_summary{
      padding: 2px 10px !important;
      .heading{
        font-size: 14px;
      }
      .header{
        .title{
          font-size: 11px;
        }
      }
      .label{
        font-size: 11px;
      }
      .summary_row{
        .value,
        .amount {
         font-size: 11px;
        }
      }
    }
    .matrix_summary {
    .heading {
      font-size: 14px;
    }
    .label {
      font-size: 11px;
    }
    .value {
      font-size: 11px;
    }
    .count_container {
      padding: 0 0;
    }
    .summary {
      border: none !important;
    }
    }
  }
  .comm_card,
  .cate_card{
    .img_div {
      width: 40px;
      height: 40px;
    }
    .title,
    .value,
    .small_text {
      font-size: 13px;
    }
    .value {
      font-size: 13px;
    }
  }
  .category_container{
    .heading{
      font-size: 14px;
    }
  }
}

@media screen and (max-width: 568px) {
  .container{
    flex-direction: column;
  }
  .analysis_container{
    .responses_summary{
      .summary_row{
        .amount{
          font-size: 10px;
        }
      }
    }
  }
}

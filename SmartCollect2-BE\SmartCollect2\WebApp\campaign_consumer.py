import json
import uuid
import logging
import asyncio
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from vertexai.generative_models import GenerationConfig, GenerativeModel, Part, Content ,Tool, FunctionDeclaration

from datetime import datetime
from WebApp.models import ActiveConversation, ConversationHistory, ConversationMessage, UserConversations, BankMst, LanguageMst
from WebApp.views import CampaignChatbot,QueryPrompt
from django.db import connection
import pandas as pd
from google.cloud import storage
import io
import csv

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ChatConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        await self.accept()
        self.user_id = None
        self.conversation_uuid = None
        self.language = "english"
        self.BankMstID=None
        self.designation=None
        self.BranchMstID=None   
        self.model = GenerativeModel("gemini-1.5-flash-002") 
        self.chat = self.model.start_chat()
        self.history = []
        
        logger.info("WebSocket connection established")

    async def disconnect(self, close_code):
        logger.info(f"WebSocket disconnected with code: {close_code}")
        
        if self.conversation_uuid:
            await self._archive_conversation(self.conversation_uuid)
            

    async def receive(self, text_data):
        # Parse incoming message
        try:
            data = json.loads(text_data)
            message_type = data.get('type', '')
            
            if message_type == 'initialize':
                await self._handle_initialize(data)
            elif message_type == 'message':
                await self._handle_message(data)
            elif message_type == 'reset':
                await self._handle_reset(data)
            elif message_type == 'restore':
                await self._handle_restore(data)
            elif message_type == 'edit_message':
                await self._handle_edit_message(data)
            elif message_type == 'delete_conversation':
                await self._handle_delete_conversation(data)    
                
            else:
                await self._send_error("Unknown message type")
                
        except json.JSONDecodeError:
            await self._send_error("Invalid JSON format")
        except Exception as e:
            logger.error(f"Error in WebSocket receive: {e}")
            await self._send_error(f"An unexpected error occurred: {str(e)}")

    async def _handle_initialize(self, data):
        """Initialize conversation and send greeting"""
        try:
            self.user_id = data.get('user_id', '')
            self.conversation_uuid = data.get('conversation_uuid', None)
            self.language = data.get('language', 'english')
            self.BankMstID=data.get('BankMstID',None)
            self.designation=data.get('designation',None)
            self.BranchMstID=data.get('BranchMstID',None)
            #print("bankmstid",self.BankMstID)
            
            if not self.user_id:
                await self._send_error("User ID is required")
                return
                
            await self._delete_empty_conversations(self.user_id)        
            
            last_active = await self._get_last_active_conversation()
            if last_active and last_active.conversation_status == 'active':
                old_uuid = str(last_active.uuid)
                logger.info(f"Archiving previous conversation: {old_uuid}")
                
                await self._archive_conversation(old_uuid)
            
            if not self.conversation_uuid:
                conversation = await self._create_conversation()
                self.conversation_uuid = str(conversation.uuid)
                logger.info(f"Created new conversation: {self.conversation_uuid}")
            else:
                conversation_exists = await self._check_conversation_exists(self.conversation_uuid)
                if (conversation_exists):
                    conversation = await self._get_conversation(self.conversation_uuid)
                    logger.info(f"Restored existing conversation: {self.conversation_uuid}")
                else:
                    conversation = await self._create_conversation()
                    self.conversation_uuid = str(conversation.uuid)
                    logger.info(f"Created new conversation (invalid UUID): {self.conversation_uuid}")
            
            await self._associate_conversation_with_user()
            await self._initialize_ai()
            
            greeting = "Hello! I'm SmartCollect, your AI assistant. How may I assist you today?"
            
            await self.send(text_data=json.dumps({
                'type': 'greeting',
                'message': greeting,
                'conversation_uuid': self.conversation_uuid,
                'timestamp': datetime.now().isoformat()
            }))
        except Exception as e:
            logger.error(f"Error in initialize: {e}")
            await self._send_error(f"Failed to initialize: {str(e)}")
            
            
            
    async def _handle_edit_message(self, data):
        """Handle editing a previously sent message"""
        try:
            message_id = data.get('message_id', '')
            new_message = data.get('message', '')
            
            if not message_id or not new_message.strip():
                await self._send_error("Message ID and new message content are required")
                return
            
            # Update the message in the database
            message_obj = await self._update_message_text(message_id, new_message)
            if not message_obj:
                await self._send_error("Message not found or could not be updated")
                return
                
            await self.send(text_data=json.dumps({
                'type': 'message_updated',
                'message_id': message_id,
                'timestamp': datetime.now().isoformat()
            }))
            
            await self._send_typing(True)
            
            # Make sure the AI gets the updated conversation history with the edited message
            await self._initialize_ai_with_edit(message_id, new_message)
            
            # Replace the auto-extraction call with the standard response function
            ai_response = await self._get_ai_response(new_message)
            
            await self._update_ai_response(message_obj, ai_response)
            
            await self._send_typing(False)
            await self.send(text_data=json.dumps({
                'type': 'ai_response_updated',
                'message_id': message_id,
                'response': ai_response,
                'timestamp': datetime.now().isoformat()
            }))
            
        except Exception as e:
            logger.error(f"Error handling message edit: {e}")
            await self._send_typing(False)
            await self._send_error(f"Failed to update message: {str(e)}")
            
            
            
            
            
    @database_sync_to_async
    def _update_message_text(self, message_id, new_text):
        """Update the text of a user message"""
        try:
            if not self.conversation_uuid:
                return None
                
            conversation = ActiveConversation.objects.get(uuid=self.conversation_uuid)
            
            messages = ConversationMessage.objects.filter(conversation=conversation)
            
            message = None
            try:
                message = messages.get(id=message_id)
            except (ValueError, ConversationMessage.DoesNotExist):
                for msg in messages:
                    if str(msg.id) == str(message_id):
                        message = msg
                        break
                
                if not message:
                    message = messages.order_by('-created_at').first()
                    logger.info(f"Using fallback approach: selected most recent message id={message.id}")
            
            if not message:
                logger.error(f"Message with ID {message_id} not found for conversation {self.conversation_uuid}")
                return None
                
            message.user_message = new_text
            message.is_edited = True
            message.save()
            logger.info(f"Successfully updated message id={message.id} with new text")
            
            return message
        except (ActiveConversation.DoesNotExist, ConversationMessage.DoesNotExist):
            logger.error(f"Message {message_id} not found for conversation {self.conversation_uuid}")
            return None
        except Exception as e:
            logger.error(f"Error updating message: {e}")
            return None

    @database_sync_to_async
    def _update_ai_response(self, message_obj, ai_response):
        """Update AI response for a message"""
        try:
            if message_obj:
                message_obj.ai_response = ai_response
                message_obj.save()
                return True
            return False
        except Exception as e:
            logger.error(f"Error updating AI response: {e}")
            return False
        
        
        
        
    @database_sync_to_async
    def _delete_empty_conversations(self, user_id):
        """Delete any empty conversations for this user"""
        try:
            # First check active conversations
            empty_active = ActiveConversation.objects.filter(user_id=user_id, title="Empty Conversation")
            for conv in empty_active:
                # Check if it has any messages
                message_count = ConversationMessage.objects.filter(conversation=conv).count()
                if message_count == 0:
                    logger.info(f"Deleting empty active conversation: {conv.uuid}")
                    
                    # Remove from user's conversations if exists
                    try:
                        user_convs = UserConversations.objects.get(user_id=user_id)
                        if user_convs.last_active_conversation and str(user_convs.last_active_conversation.uuid) == str(conv.uuid):
                            user_convs.last_active_conversation = None
                        
                        user_convs.active_conversations.remove(conv)
                        user_convs.save()
                    except (UserConversations.DoesNotExist, Exception) as e:
                        logger.error(f"Error removing from user conversations: {e}")
                    
                    # Delete the conversation
                    conv.delete()
            
            # Then check archived conversations
            empty_history = ConversationHistory.objects.filter(user_id=user_id, title="Empty Conversation")
            for conv in empty_history:
                # For archived conversations, check if the full_conversation is empty or contains no real interaction
                has_content = False
                try:
                    full_conv = json.loads(conv.full_conversation)
                    if full_conv and len(full_conv) > 0:
                        for msg in full_conv:
                            if msg.get("user_message") and msg.get("ai_response"):
                                has_content = True
                                break
                except:
                    pass
                
                if not has_content:
                    logger.info(f"Deleting empty archived conversation: {conv.uuid}")
                    
                    # Remove from user's conversations if exists
                    try:
                        user_convs = UserConversations.objects.get(user_id=user_id)
                        user_convs.completed_conversations.remove(conv)
                        user_convs.save()
                    except (UserConversations.DoesNotExist, Exception) as e:
                        logger.error(f"Error removing from user conversations: {e}")
                    
                    # Delete the conversation
                    conv.delete()
            
            return True
        except Exception as e:
            logger.error(f"Error deleting empty conversations: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False    
        
        
    async def _handle_delete_conversation(self, data):
        """Delete a specific conversation from history"""
        try:
            conversation_uuid = data.get('conversation_uuid')
            
            if not conversation_uuid:
                await self._send_error("Conversation UUID is required for deletion")
                return
                
            success = await self._delete_conversation(conversation_uuid)
            
            if success:
                await self.send(text_data=json.dumps({
                    'type': 'delete_confirmation',
                    'conversation_uuid': conversation_uuid,
                    'message': 'Conversation deleted successfully',
                    'timestamp': datetime.now().isoformat()
                }))
            else:
                await self._send_error("Failed to delete conversation. It may have been already deleted or doesn't exist.")
                
        except Exception as e:
            logger.error(f"Error deleting conversation: {e}")
            await self._send_error(f"Failed to delete conversation: {str(e)}")  
            
            
            
    @database_sync_to_async
    def _delete_conversation(self, conversation_uuid):
        """Delete a conversation from history"""
        try:
            if ConversationHistory.objects.filter(uuid=conversation_uuid).exists():
                conversation = ConversationHistory.objects.get(uuid=conversation_uuid)
                
                user_convs = UserConversations.objects.get(user_id=conversation.user_id)
                
                user_convs.completed_conversations.remove(conversation)
                user_convs.save()
                
                conversation.delete()
                
                logger.info(f"Deleted conversation: {conversation_uuid}")
                return True
                
            elif ActiveConversation.objects.filter(uuid=conversation_uuid).exists():
                conversation = ActiveConversation.objects.get(uuid=conversation_uuid)
                
                try:
                    user_convs = UserConversations.objects.get(user_id=conversation.user_id)
                    if user_convs.last_active_conversation and str(user_convs.last_active_conversation.uuid) == conversation_uuid:
                        user_convs.last_active_conversation = None
                    
                    user_convs.active_conversations.remove(conversation)
                    user_convs.save()
                except UserConversations.DoesNotExist:
                    pass
                
                ConversationMessage.objects.filter(conversation=conversation).delete()
                
                conversation.delete()
                
                logger.info(f"Deleted active conversation: {conversation_uuid}")
                return True
                
            return False
        except Exception as e:
            logger.error(f"Error in _delete_conversation: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False          
                                  
     

   
    async def _handle_message(self, data):
        """Process user message and get AI response"""
        try:
            user_message = data.get('message', '')
            
            if not user_message.strip():
                await self._send_error("Message cannot be empty")
                return
                
            await self._send_typing(True)
            
            user_message_obj = await self._save_message(user_message, None)
            
            ai_response = await self._get_ai_response(user_message)
            
            await self._update_message_response(user_message_obj, ai_response)
            
            form_complete = "FORM_COMPLETED:" in ai_response
            form_data = {}
            
            if form_complete:
                form_data = await self._extract_form_data(ai_response)
                await self._archive_conversation()
                # await self._process_and_send_extracted_form_data(form_data)
            else:
                form_data= await self._get_form_data()
            
            await self._send_typing(False)
            await self.send(text_data=json.dumps({
                'type': 'response',
                'message': ai_response,
                'form_complete': form_complete,
                'form_data': form_data,
                'conversation_uuid': self.conversation_uuid,
                'timestamp': datetime.now().isoformat()
            }))
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            import traceback
            logger.error(traceback.format_exc())
            await self._send_typing(False)
            await self._send_error(f"Failed to process message: {str(e)}")

    async def _handle_reset(self, data):
        """Reset conversation state with a new UUID"""
        try:
            old_uuid = self.conversation_uuid
            
            self.user_id = data.get('user_id', self.user_id)
            self.language = data.get('language', self.language)
            
            conversation = await self._create_conversation()
            self.conversation_uuid = str(conversation.uuid)
            
            if old_uuid:
                archived = await self._archive_conversation(old_uuid)
                await self._abandon_conversation(old_uuid)
                
                if archived:
                    await self._update_archived_conversation_title(old_uuid)
            
            await self._initialize_ai()
            
            await self._associate_conversation_with_user()
            
            await self.send(text_data=json.dumps({
                'type': 'reset_confirmation',
                'conversation_uuid': self.conversation_uuid,
                'message': "Conversation has been reset.",
                'timestamp': datetime.now().isoformat()
            }))
            
            greeting = "Hello! I'm SmartCollect, your AI assistant. How may I assist you today?"
            
            await self.send(text_data=json.dumps({
                'type': 'greeting',
                'message': greeting,
                'conversation_uuid': self.conversation_uuid,
                'timestamp': datetime.now().isoformat()
            }))
            
        except Exception as e:
            logger.error(f"Error resetting conversation: {e}")
            await self._send_error(f"Failed to reset conversation: {str(e)}")

    async def _handle_restore(self, data):
        """Restore a previous conversation by UUID"""
        try:
            restore_uuid = data.get('conversation_uuid', None)
            if not restore_uuid:
                await self._send_error("Conversation UUID is required for restoration")
                return
                
            conversation_exists = await self._check_conversation_exists(restore_uuid)
            if not conversation_exists:
                await self._send_error("Conversation not found or no longer active")
                return
            old_uuid = self.conversation_uuid
            
            # Generate title for previous conversation before switching
            if old_uuid and old_uuid != restore_uuid:
                logger.info(f"Archiving previous conversation {old_uuid} before switching to {restore_uuid}")
                await self._archive_conversation(old_uuid)
                    
            self.conversation_uuid = restore_uuid
            
            # await self._archive_conversation(restore_uuid)
            await self._initialize_ai()
            
            last_messages = await self._get_last_messages(50) 
            
            await self.send(text_data=json.dumps({
                'type': 'restore_confirmation',
                'conversation_uuid': self.conversation_uuid,
                'message': "Welcome Back.How can i help you today?",
                'history': last_messages,
                'timestamp': datetime.now().isoformat()
            }))
            
        except Exception as e:
            logger.error(f"Error restoring conversation: {e}")
            await self._send_error(f"Failed to restore conversation: {str(e)}")
            
            
            
    async def _generate_conversation_title(self):
        """Generate a descriptive title for the conversation"""
        try:
            conversation_history = await self._get_last_messages(50)
            
            if not conversation_history or len(conversation_history) == 0:
                return "Empty Conversation"
            
            title_prompt = "Based on this conversation, generate a short, descriptive title (max 5-7 words) that captures the main topic or purpose. The title should be concise and summarize what the conversation was about. Only respond with the title, nothing else.\n\n"
            #print(title_prompt)
            for message in conversation_history:
                title_prompt += f"User: {message['user_message']}\n"
                title_prompt += f"AI: {message['ai_response']}\n"
            
            
            
            response = await asyncio.to_thread(
                self.chat.send_message,
                Part.from_text(title_prompt),
                generation_config=GenerationConfig(
                    temperature=0.2,
                    top_p=0.95,
                    top_k=40,
                    max_output_tokens=128,  
                )
            )
            
            title = response.text.strip()
            title = title.replace('"', '').replace("'", "")
            title = " ".join(title.split())  
            
                
            return title
            
        except Exception as e:
            logger.error(f"Error generating conversation title: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return "Conversation"         

    async def _initialize_ai(self):
        """Initialize the AI model with system prompt and conversation history"""
        try:
            system_prompt = await self._get_system_prompt()
            
            # Create the initial system message that includes function calling instructions
            function_calling_instruction = f"""
You are SmartCollect Campaign Assistant, designed specifically to help users create campaigns through a conversational interface.

IMPORTANT FUNCTION CALLING INSTRUCTIONS:
1. When the user provides complete information for any section (campaign name, filter, communication flow, or template) you MUST: 
   - Call the appropriate function IMMEDIATELY (without confirmation)
   - Continue with the NEXT STEP in the conversation flow
2. Never stop at just calling a function - always guide to the next logical step

FUNCTION CALLING TRIGGERS:
1. Campaign Name: Call extract_campaign_name when a valid name is provided
   - Then immediately ask about first filter category

2. Filters: Call extract_filter when all four components are provided
   - Then ask if user wants to add another filter OR move to communication flows

3. Communication Flows: Call extract_communication_flow when all fields are complete
   - Then ask about templates or adding another flow

4. Templates: Call extract_template when all fields are complete
   - Then ask about adding another template OR confirm completion

{system_prompt}

EXAMPLE INTERACTIONS:
User: "Let's name it Summer Collection"
AI: [Calls extract_campaign_name] 
"Great! The campaign is now named 'Summer Collection'. Let's set up your first filter - what category would you like to use?"

User: "Finance category, EMI Amount, greater than, 5000"
AI: [Calls extract_filter]
"I've created a filter for EMI Amount > 5000. Would you like to add another filter or should we move to communication flows?"
"""
            
            self.history = [
                Content(
                    role="user",
                    parts=[Part.from_text(function_calling_instruction)]
                ),
                Content(
                    role="model",
                    parts=[Part.from_text("I understand my role as SmartCollect Campaign Assistant. I'll help you create and configure campaigns by collecting information about campaign name, filters, communication flows, and templates. I'll guide you through each step of the campaign creation process.")]
                )
            ]
            
            # Add conversation history if available
            if self.conversation_uuid:
                conversation_history = await self._get_conversation_history()
                
                if conversation_history:
                    for message in conversation_history:
                        self.history.append(Content(
                            role="user",
                            parts=[Part.from_text(message["user_message"])]
                        ))
                        
                        self.history.append(Content(
                            role="model",
                            parts=[Part.from_text(message["ai_response"])]
                        ))
            
            # Add current form data as context if available
            form_data = await self._get_form_data()
            if form_data and form_data != {}:
                form_context = f"""
Current campaign data:
{json.dumps(form_data, indent=2)}

Continue helping the user build this campaign. Remember to use function calling when appropriate.
"""
                self.history.append(Content(
                    role="user",
                    parts=[Part.from_text(form_context)]
                ))
                
                self.history.append(Content(
                    role="model",
                    parts=[Part.from_text("I see the current campaign data. Let's continue building your campaign from here.")]
                ))
            
            # Initialize the chat with the constructed history
            self.chat = self.model.start_chat(history=self.history)
        
        except Exception as e:
            logger.error(f"Error in _initialize_ai: {e}")
            import traceback
            logger.error(traceback.format_exc())
            
            # Create a simpler fallback history if something went wrong
            self.history = [
                Content(
                    role="user", 
                    parts=[Part.from_text("You are SmartCollect Campaign Assistant. Help users create campaigns with filters, communication flows, and templates.")]
                ),
                Content(
                    role="model",
                    parts=[Part.from_text("I'll help you create your campaign. What would you like to name it?")]
                )
            ]
            
            self.chat = self.model.start_chat(history=self.history)

    async def _initialize_ai_with_edit(self, message_id, edited_message):
        """Initialize AI with edited message in conversation history"""
        system_prompt = await self._get_system_prompt()
        
        self.history = [
            Content(
                role="user",
                parts=[Part.from_text(system_prompt)]
            ),
            Content(
                role="model",
                parts=[Part.from_text("I understand my role as SmartCollect Campaign Assistant. I'll help you create and configure campaigns by collecting information about campaign name, filters, communication flows, and templates. I'll guide you through each step of the campaign creation process.")]
            )
        ]
        
        if self.conversation_uuid:
            # Get conversation history but replace the edited message
            conversation_history = await self._get_conversation_history_with_edit(message_id, edited_message)
            
            if conversation_history:
                for message in conversation_history:
                    self.history.append(Content(
                        role="user",
                        parts=[Part.from_text(message["user_message"])]
                    ))
                    
                    self.history.append(Content(
                        role="model",
                        parts=[Part.from_text(message["ai_response"])]
                    ))
        
        form_data = await self._get_form_data()
        if form_data:
            form_context = f"Current form data: {json.dumps(form_data)}"
            self.history.append(Content(
                role="user",
                parts=[Part.from_text(form_context)]
            ))
            
            self.history.append(Content(
                role="model",
                parts=[Part.from_text("I understand the current form state.")]
            ))
        
        self.chat = self.model.start_chat(history=self.history)

    @database_sync_to_async
    def _get_conversation_history_with_edit(self, message_id, edited_message):
        """Get conversation history with an edited message"""
        if not self.conversation_uuid:
            return []
            
        conversation = ActiveConversation.objects.get(uuid=self.conversation_uuid)
        messages = ConversationMessage.objects.filter(conversation=conversation).order_by('created_at')
        
        history = []
        for message in messages:
            # Use the edited message if this is the message being edited
            if str(message.id) == str(message_id):
                user_msg = edited_message
            else:
                user_msg = message.user_message
                
            if message.ai_response:
                history.append({
                    "user_message": user_msg,
                    "ai_response": message.ai_response
                })
        
        return history

    async def _get_ai_response(self, user_message):
        """Get response from AI model"""
        try:
            # Add user message to history before sending
            self.history.append(Content(
                role="user",
                parts=[Part.from_text(user_message)]
            ))
            
            response = await asyncio.to_thread(
                self.chat.send_message,
                Part.from_text(user_message),
                generation_config=GenerationConfig(
                    temperature=0.2,
                    top_p=0.95,
                    top_k=40,
                    max_output_tokens=1024,
                )
            )
            
            # Add AI response to history for tracking
            self.history.append(Content(
                role="model",
                parts=[Part.from_text(response.text)]
            ))
            
            return response.text
        except Exception as e:
            logger.error(f"Error getting AI response: {e}")
            error_message = "I'm sorry, I'm having trouble processing your request. Please try again."
            
            # Add error response to history for tracking
            self.history.append(Content(
                role="model",
                parts=[Part.from_text(error_message)]
            ))
            
            return error_message

    async def _get_ai_follow_up_response(self, follow_up_prompt):
        """Get a follow-up response from AI model"""
        try:
            # Add follow-up prompt to history
            self.history.append(Content(
                role="user",
                parts=[Part.from_text(follow_up_prompt)]
            ))
            
            response = await asyncio.to_thread(
                self.chat.send_message,
                Part.from_text(follow_up_prompt),
                generation_config=GenerationConfig(
                    temperature=0.2,
                    top_p=0.95,
                    top_k=40,
                    max_output_tokens=1024,
                )
            )
            
            # Add AI response to history
            self.history.append(Content(
                role="model",
                parts=[Part.from_text(response.text)]
            ))
            
            return response.text
        except Exception as e:
            logger.error(f"Error getting AI follow-up response: {e}")
            error_message = "I'm sorry, I'm having trouble analyzing the data. Please try a different question or approach."
            
            # Add error response to history
            self.history.append(Content(
                role="model",
                parts=[Part.from_text(error_message)]
            ))
            
            return error_message

    async def _send_error(self, message):
        """Send error message to client"""
        await self.send(text_data=json.dumps({
            'type': 'error',
            'message': message,
            'timestamp': datetime.now().isoformat()
        }))
        
    async def _send_typing(self, is_typing):
        """Send typing indicator to client"""
        await self.send(text_data=json.dumps({
            'type': 'typing',
            'is_typing': is_typing
        }))

    @database_sync_to_async
    def _get_system_prompt(self):
        """Get system prompt for AI"""
        logger.info(f"bankmstid in system {self.BankMstID}")
        logger.info(f"designation in system {self.designation}")
        logger.info(f"branchmstid {self.BranchMstID}")
        

        return CampaignChatbot.get_system_prompt(self.language, self.user_id,self.BankMstID,self.designation,self.BranchMstID)


    @database_sync_to_async
    def _create_conversation(self):
        """Create a new conversation with a UUID"""
        return ActiveConversation.objects.create(
            user_id=self.user_id,
            conversation_status='active',
            form_data=json.dumps({})
        )

    @database_sync_to_async
    def _check_conversation_exists(self, conversation_uuid):
        """Check if conversation exists"""
        try:
            return ActiveConversation.objects.filter(uuid=conversation_uuid).exists()
        except:
            return False

    @database_sync_to_async
    def _get_conversation(self, conversation_uuid):
        """Get conversation by UUID"""
        return ActiveConversation.objects.get(uuid=conversation_uuid)

    @database_sync_to_async
    def _save_message(self, user_message, ai_response):
        """Save conversation message"""
        if not self.conversation_uuid:
            return None
            
        conversation = ActiveConversation.objects.get(uuid=self.conversation_uuid)
        
        conversation.last_updated = datetime.now()
        conversation.save()
        
        return ConversationMessage.objects.create(
            conversation=conversation,
            user_message=user_message,
            ai_response=ai_response or ""
        )
        
    @database_sync_to_async
    def _update_message_response(self, message_obj, ai_response):
        """Update message with AI response"""
        if message_obj:
            message_obj.ai_response = ai_response
            message_obj.save()

    @database_sync_to_async
    def _get_conversation_history(self):
        """Get conversation history"""
        if not self.conversation_uuid:
            return []
            
        conversation = ActiveConversation.objects.get(uuid=self.conversation_uuid)
        messages = ConversationMessage.objects.filter(conversation=conversation).order_by('created_at')
        
        return [
            {
                "user_message": message.user_message,
                "ai_response": message.ai_response
            }
            for message in messages if message.ai_response 
        ]
    
    @database_sync_to_async
    def _get_last_messages(self, count=10):
        """Get last few messages for context"""
        if not self.conversation_uuid:
            return []
            
        conversation = ActiveConversation.objects.get(uuid=self.conversation_uuid)
        messages = ConversationMessage.objects.filter(
            conversation=conversation
        ).order_by('-created_at')[:count]
        
        return [
            {
                "user_message": message.user_message,
                "ai_response": message.ai_response,
                "timestamp": message.created_at.isoformat()
            }
            for message in reversed(messages) if message.ai_response  
        ]

    @database_sync_to_async
    def _get_conversation_length(self):
        """Get the number of messages in the conversation"""
        if not self.conversation_uuid:
            return 0
            
        conversation = ActiveConversation.objects.get(uuid=self.conversation_uuid)
        return ConversationMessage.objects.filter(conversation=conversation).count()

    @database_sync_to_async
    def _complete_conversation(self):
        """Mark conversation as completed"""
        if not self.conversation_uuid:
            return
            
        conversation = ActiveConversation.objects.get(uuid=self.conversation_uuid)
        conversation.conversation_status = 'completed'
        conversation.save()
        
        if self.user_id:
            self._complete_conversation_for_user()

    @database_sync_to_async
    def _abandon_conversation(self, conversation_uuid=None):
        """Mark conversation as abandoned"""
        uuid_to_use = conversation_uuid or self.conversation_uuid
        if not uuid_to_use:
            return
            
        conversation = ActiveConversation.objects.get(uuid=uuid_to_use)
        conversation.conversation_status = 'abandoned'
        conversation.save()
        
        
        
    @database_sync_to_async
    def _do_archive_conversation(self, conversation_uuid, title):
        """Perform the actual archiving operation"""
        try:
            # Check if already archived
            if ConversationHistory.objects.filter(uuid=conversation_uuid).exists():
                return ConversationHistory.objects.get(uuid=conversation_uuid)
                
            conversation = ActiveConversation.objects.get(uuid=conversation_uuid)
            
            messages = ConversationMessage.objects.filter(
                conversation=conversation
            ).order_by('created_at')
            
            if not messages.exists():
                return None
                
            conversation_data = []
            for message in messages:
                conversation_data.append({
                    "user_message": message.user_message,
                    "ai_response": message.ai_response,
                    "timestamp": message.created_at.isoformat()
                })
            
            history = ConversationHistory.objects.create(
                uuid=conversation.uuid,
                user_id=conversation.user_id,
                created_at=conversation.created_at,
                form_data=conversation.form_data,
                full_conversation=json.dumps(conversation_data),
                title=title  # Save the AI-generated title here
            )
            
            user_convs, _ = UserConversations.objects.get_or_create(user_id=conversation.user_id)
            user_convs.active_conversations.remove(conversation)  # Remove from active
            user_convs.completed_conversations.add(history)       # Add to completed
            user_convs.save()
            
            return history
                
        except Exception as e:
            logger.error(f"Error in _do_archive_conversation: {e}")
            return None




    async def _archive_conversation(self, conversation_uuid=None):
        try:
            uuid_to_use = conversation_uuid or self.conversation_uuid
            logger.info(f"Archiving conversation: {uuid_to_use}")
            
            if not uuid_to_use:
                return
                
            
            old_uuid = self.conversation_uuid
            self.conversation_uuid = uuid_to_use
            
            try:
                title = await self._generate_conversation_title()
                logger.info(f"Generated title for conversation {uuid_to_use}: {title}")
                
                
                await self._update_active_conversation_title(uuid_to_use, title)
            except Exception as e:
                logger.error(f"Failed to generate title: {e}")
                title = "Conversation"
            finally:
                
                self.conversation_uuid = old_uuid
            
            
            history =  self._do_archive_conversation(uuid_to_use, title)
            return history
                
        except Exception as e:
            logger.error(f"Error in _archive_conversation: {e}")
            return None

    @database_sync_to_async
    def _update_active_conversation_title(self, conversation_uuid, title):
        try:
            conversation = ActiveConversation.objects.get(uuid=conversation_uuid)
            conversation.title = title
            conversation.save()
            logger.info(f"Updated title in ActiveConversation: {title}")
            return True
        except Exception as e:
            logger.error(f"Failed to update title in ActiveConversation: {e}")
            return False

    

    async def _update_archived_conversation_title(self, conversation_uuid):
        """Generate and update title for an archived conversation"""
        try:
            
            old_uuid = self.conversation_uuid
            self.conversation_uuid = conversation_uuid
            
            
            title = await self._generate_conversation_title()
            
            
            await self._update_conversation_title_in_db(conversation_uuid, title)
            
            
            self.conversation_uuid = old_uuid
            
            return title
        except Exception as e:
            logger.error(f"Error updating archived conversation title: {e}")
            return "Conversation"

    @database_sync_to_async
    def _update_conversation_title_in_db(self, conversation_uuid, title):
        """Update conversation title in database"""
        try:
            history = ActiveConversation.objects.get(uuid=conversation_uuid)
            history.title = title
            history.save()
            logger.info(f"Updated title for conversation {conversation_uuid}: {title}")
            return True
        except ActiveConversation.DoesNotExist:
            logger.error(f"Cannot update title: Conversation {conversation_uuid} not found")
            return False
        except Exception as e:
            logger.error(f"Error updating conversation title in DB: {e}")
            return False

    @database_sync_to_async
    def _get_form_data(self):
        """Get form data from conversation"""
        if not self.conversation_uuid:
            return {}
            
        conversation = ActiveConversation.objects.get(uuid=self.conversation_uuid)
        
        if not conversation.form_data:
            return {}
            
        try:
            return json.loads(conversation.form_data)
        except:
            return {}

    
    async def _extract_form_data(self, response_text):
        """Extract structured form data from FORM_COMPLETED response using function calling"""
        if not self.conversation_uuid or "FORM_COMPLETED:" not in response_text:
            return {}
            
        try:
            logger.info(f"Extracting form data from response: {response_text}")
            
            # Import required Vertex AI classes for function calling
            from vertexai.generative_models import Tool, FunctionDeclaration

            # Create proper function declaration using Vertex AI classes
            function_declaration = FunctionDeclaration(
                name="extract_campaign_data",
                description="Extract campaign details from a conversation with the user",
                parameters={
                    "type": "object",
                    "properties": {
                        "campaign_name": {
                            "type": "string",
                            "description": "The name of the campaign"
                        },
                        "language": {
                            "type": "string", 
                            "description": "Campaign language"
                        },
                        "filters": {
                            "type": "array",
                            "description": "List of filters applied to the campaign",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "category": {"type": "string", "description": "Filter category name"},
                                    "parameter": {"type": "string", "description": "Filter parameter name"},
                                    "comparison": {"type": "string", "description": "Comparison operator"},
                                    "value": {"type": "string", "description": "Filter value"}
                                }
                            }
                        },
                        "communication_flows": {
                            "type": "array",
                            "description": "List of communication flows",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "name": {"type": "string", "description": "Flow name"},
                                    "applicant_type": {"type": "string", "description": "Type of applicant"},
                                    "channel": {"type": "string", "description": "Communication channel"},
                                    "based_on": {"type": "string", "description": "What the flow is based on"},
                                    "days": {"type": "string", "description": "Number of days"},
                                    "skip_days": {"type": "string", "description": "Days to skip, or null if none"}
                                }
                            }
                        },
                        "templates": {
                            "type": "array",
                            "description": "List of message templates",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "name": {"type": "string", "description": "Template name"},
                                    "language": {"type": "string", "description": "Template language"},
                                    "content": {"type": "string", "description": "Template content"},
                                    "buttons": {"type": "string", "description": "Template buttons, or null if none"},
                                    "variables": {"type": "string", "description": "Template variables, or null if none"}
                                }
                            }
                        }
                    },
                    "required": ["campaign_name"]
                }
            )
            
            # Create Tool object with function declaration
            extract_tool = Tool(
                function_declarations=[function_declaration]
            )
            
            # In older versions of the Vertex AI SDK, we can't set function_calling_config directly,
            # so we'll try a different approach
            
            # Use function calling to extract structured data
            response = await asyncio.to_thread(
                self.model.generate_content,
                response_text,
                generation_config=GenerationConfig(
                    temperature=0
                    # Remove response_mime_type as it conflicts with function calling
                ),
                tools=[extract_tool]
            )
            print("response of function call",response.candidates)
            # Extract the function call response
            if response.candidates and hasattr(response.candidates[0], 'function_calls') and response.candidates[0].function_calls:
                # Access the first function call in the list
                function_calls = response.candidates[0].function_calls
                if function_calls and len(function_calls) > 0 and function_calls[0].name == "extract_campaign_data":
                    structured_form_data = function_calls[0].args
                    logger.info(f"Extracted form data using function calling: {json.dumps(structured_form_data, indent=2)}")
                    
                    # Print the structured data for now
                    print("EXTRACTED CAMPAIGN DATA:")
                    print(json.dumps(structured_form_data, indent=2))
                    
                    # Save to conversation
                    conversation = await database_sync_to_async(ActiveConversation.objects.get)(uuid=self.conversation_uuid)
                    conversation.form_data = json.dumps(structured_form_data)
                    await database_sync_to_async(conversation.save)()
                    api_results=await self._process_and_send_extracted_form_data(structured_form_data)
                    logger.info(f"API results: {api_results}")
                    return structured_form_data
                else:
                    logger.warning("Function calling didn't return expected results, falling back to manual extraction")

            else:
                logger.warning("Function calling didn't return expected results, falling back to manual extraction")
                
                # Fallback to the original extraction method
                form_sections = response_text.split("FORM_COMPLETED:")[1].strip()
                
                structured_form_data = {
                    "campaign_name": "",
                    "filters": [],
                    "communication_flows": [],
                    "templates": []
                }
                
                # Original extraction logic...
                if "- Campaign Name:" in form_sections:
                    campaign_line = form_sections.split("- Campaign Name:")[1].split("\n")[0].strip()
                    structured_form_data["campaign_name"] = campaign_line
                
                # Continue with existing code for extraction...
                if "- Filters:" in form_sections:
                    filters_section_start = form_sections.find("- Filters:")
                    next_major_section_start = form_sections.find("- Communication Flows:")
                    
                    if filters_section_start > -1 and next_major_section_start > -1:
                        filters_section = form_sections[filters_section_start + len("- Filters:"):next_major_section_start].strip()
                        
                        import re
                        filter_blocks = re.split(r'\s*Filter \d+:', filters_section)
                        filter_blocks = [block.strip() for block in filter_blocks if block.strip()]
                        
                        for filter_block in filter_blocks:
                            filter_data = {}
                            
                            if "- Category Name:" in filter_block:
                                filter_data["category"] = filter_block.split("- Category Name:")[1].split("\n")[0].strip()
                            
                            if "- Parameter Name:" in filter_block:
                                filter_data["parameter"] = filter_block.split("- Parameter Name:")[1].split("\n")[0].strip()
                            
                            if "- Comparison Operator:" in filter_block:
                                filter_data["comparison"] = filter_block.split("- Comparison Operator:")[1].split("\n")[0].strip()
                            
                            if "- Value:" in filter_block:
                                value_parts = filter_block.split("- Value:")[1].split("\n")[0].strip()
                                filter_data["value"] = value_parts
                            
                            if filter_data:
                                structured_form_data["filters"].append(filter_data)
                
                # Continue with existing code for rest of extraction...
                
                logger.info(f"Extracted form data using fallback method: {json.dumps(structured_form_data, indent=2)}")
                
                conversation = await database_sync_to_async(ActiveConversation.objects.get)(uuid=self.conversation_uuid)
                conversation.form_data = json.dumps(structured_form_data)
                await database_sync_to_async(conversation.save)()
                
                return structured_form_data
                
        except Exception as e:
            logger.error(f"Error extracting form data: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {}

    # Update the _process_and_send_extracted_form_data method to create flows first, then templates

    async def _process_and_send_extracted_form_data(self, form_data):
        """Process extracted form data and send to appropriate API endpoints"""
        try:
            logger.info("Processing extracted form data and sending to APIs")
            results = {}

            # Step 1: Create Campaign
            if 'campaign_name' in form_data:
                campaign_result = await self._create_campaign(form_data)
                results['campaign'] = campaign_result

                campaign_id = campaign_result.get('campaign_id', {}).get('CampaignMstID')
                if not campaign_id:
                    logger.error("Failed to create campaign - no campaign ID returned")
                    return {"error": "Failed to create campaign", "details": campaign_result}

                # Step 2: Create Filters
                if 'filters' in form_data and form_data['filters']:
                    filter_result = await self._create_filters(form_data['filters'], campaign_id)
                    results['filters'] = filter_result

                # Step 3: Create Communication Flows FIRST (before templates)
                flow_mapping = {}
                template_channel_shortcut=""  # Tempory fixed
                if 'communication_flows' in form_data and form_data['communication_flows']:
                    # Create communication flows
                    flow_result = await self._create_communication_flow(form_data['communication_flows'], campaign_id)
                    results['communication_flows'] = flow_result
                    
                    # Map flow channels to their IDs for later template association
                    if 'flowdata' in flow_result:
                        flows = flow_result.get('flowdata', [])
                        for flow in flows:
                            flow_channel = flow.get('CommunicationType', '').lower()
                            template_channel_shortcut=flow_channel
                            flow_id = flow.get('CommFlowID')
                            if flow_channel and flow_id:
                                if flow_channel not in flow_mapping:
                                    flow_mapping[flow_channel] = []
                                flow_mapping[flow_channel].append({
                                    'CommFlowID': flow_id,
                                    'CommunicationType': flow.get('CommunicationType')
                                })
                                logger.info(f"Mapped flow channel '{flow_channel}' to CommFlowID {flow_id}")

                # Step 4: Create Templates WITH the CommFlowIDs from flows
                if 'templates' in form_data and form_data['templates']:
                    template_results = []

                    for template in form_data['templates']:
                        # Determine channel type
                        template_channel = template.get('channel', '').lower()
                        template_channel=template_channel_shortcut
                        print("template short cut",template_channel)
                        # Find matching flow by channel
                        comm_flow_id = None
                        if template_channel in flow_mapping and flow_mapping[template_channel]:
                            # Use the first matching flow
                            comm_flow_id = flow_mapping[template_channel][0]['CommFlowID']
                            logger.info(f"Found matching flow for template '{template.get('name')}' with channel '{template_channel}', CommFlowID: {comm_flow_id}")
                            
                            # Remove this flow from available flows to avoid reuse
                            flow_mapping[template_channel].pop(0)
                        
                        # Set the CommFlowID in the template
                        if comm_flow_id:
                            template['CommFlowID'] = comm_flow_id
                            logger.info(f"Setting CommFlowID {comm_flow_id} for template '{template.get('name')}'")
                        
                        # Create the template with the CommFlowID
                        template_result = await self._create_template(template, template_channel)
                        template_results.append(template_result)
                        
                        # Extract template ID
                        template_id = None
                        if isinstance(template_result, dict):
                            template_id = (template_result.get('approvedTemplateId') or 
                                        template_result.get('FlowID') or 
                                        self._extract_template_id(template_result, template_channel))
                        
                        # Update FlowID in communication flow
                        if comm_flow_id and template_id:
                            await self._update_flow_template_link(comm_flow_id, template_id)
                    
                    results['templates'] = template_results

                # Step 5: Finalize the campaign
                # finalize_result = await self._finalize_campaign(campaign_id, form_data['campaign_name'])
                # results['finalize'] = finalize_resultb 

                return results
            else:
                return {"error": "Campaign name not provided"}

        except Exception as e:
            logger.error(f"Error processing extracted form data: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {"error": str(e)}
  
    @database_sync_to_async
    def _update_flow_template_link(self, comm_flow_id, template_id):
        """Update the template ID in a communication flow"""
        from campaign.views import UpdateFlowIdCommFlow
        
        try:
            # Create a mock request
            request = type('Request', (), {})()
            request.data = [{
                'CommFlowID': comm_flow_id,
                'FlowID': template_id
            }]
            
            # Create a mock user
            user = type('MockUser', (), {})()
            bank = BankMst.objects.get(BankMstID=self.BankMstID) if self.BankMstID else BankMst.objects.first()
            user.BankMstID = bank
            request.user = user
            
            # Call the API view
            view = UpdateFlowIdCommFlow()
            response = view.post(request)
            
            if hasattr(response, 'status_code') and response.status_code == 200:
                logger.info(f"Successfully updated CommFlow {comm_flow_id} with FlowID {template_id}")
                return True
            else:
                logger.error(f"Failed to update communication flow: {response.data if hasattr(response, 'data') else 'Unknown error'}")
                return False
                
        except Exception as e:
            logger.error(f"Error updating flow template link: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False





    @database_sync_to_async
    def _associate_conversation_with_user(self):
        """Associate this conversation with the user"""
        if not self.user_id or not self.conversation_uuid:
            return
            
        
        user_convs, created = UserConversations.objects.get_or_create(user_id=self.user_id)
        
        
        conversation = ActiveConversation.objects.get(uuid=self.conversation_uuid)
        
        
        user_convs.active_conversations.add(conversation)
        
       
        user_convs.last_active_conversation = conversation
        user_convs.save()

    @database_sync_to_async
    def _complete_conversation_for_user(self):
        """Mark conversation as completed for this user"""
        if not self.user_id or not self.conversation_uuid:
            return
            
        try:
            user_convs = UserConversations.objects.get(user_id=self.user_id)
            
            conversation = ActiveConversation.objects.get(uuid=self.conversation_uuid)
            user_convs.active_conversations.remove(conversation)
            
            if user_convs.last_active_conversation and user_convs.last_active_conversation.uuid == self.conversation_uuid:
                user_convs.last_active_conversation = None
                
            try:
                history = ConversationHistory.objects.get(uuid=self.conversation_uuid)
                user_convs.completed_conversations.add(history)
            except ConversationHistory.DoesNotExist:
                pass
                
            user_convs.save()
        except UserConversations.DoesNotExist:
            pass

    @database_sync_to_async
    def _get_user_conversations(self):
        """Get all conversations for this user"""
        if not self.user_id:
            return [], []
            
        try:
            user_convs = UserConversations.objects.get(user_id=self.user_id)
            
            active = list(user_convs.active_conversations.all())
            
            completed = list(user_convs.completed_conversations.all())
            
            return active, completed
        except UserConversations.DoesNotExist:
            return [], []

    @database_sync_to_async
    def _get_last_active_conversation(self):
        """Get the user's last active conversation"""
        if not self.user_id:
            return None
            
        try:
            user_convs = UserConversations.objects.get(user_id=self.user_id)
            return user_convs.last_active_conversation
        except UserConversations.DoesNotExist:
            return None

    @database_sync_to_async
    def _create_campaign(self, form_data):
        """Create a campaign using the CreateCampaign API"""
        from campaign.views import CreateCampaign
        from WebApp.models import BankMst
        
        try:
            campaign_data = {
                'Name': form_data['campaign_name'],
                'CampaignType': 'Normal',  # Default type, could be customized
                'Language': form_data['language'],  # Assuming language is part of form_data
            }
            
            # Create a mock request object
            request = type('Request', (), {})()
            request.data = campaign_data
            
            # Create a mock user with BankMstID attribute
            user = type('MockUser', (), {})()
            
            # Get the BankMst object
            if self.BankMstID:
                try:
                    bank = BankMst.objects.get(BankMstID=self.BankMstID)
                    user.BankMstID = bank
                    request.user = user
                except BankMst.DoesNotExist:
                    return {"error": f"Bank with ID {self.BankMstID} not found"}
            else:
                # Use a default bank if needed
                try:
                    bank = BankMst.objects.first()
                    user.BankMstID = bank
                    request.user = user
                except Exception:
                    return {"error": "No banks found in the system"}
            
            # Call the API view
            view = CreateCampaign()
            view.request = request
            response = view.post(request)
            
            return response.data
        except Exception as e:
            import traceback
            logger.error(f"Error creating campaign: {e}")
            logger.error(traceback.format_exc())
            return {"error": str(e)}

    @database_sync_to_async
    def _create_filters(self, filters, campaign_id):
        """Create filters using the CreateFilters API"""
        from campaign.views import CreateFilters
        from WebApp.models import BankMst
        
        try:
            formatted_filters = []
            print("filter data",filters)
            for filter_item in filters:
                formatted_filter = {
                    'Column': filter_item['parameter'],
                    'Comparison': filter_item['comparison'],
                    'Value': filter_item['value']
                }
                
                # Handle range comparison if needed
                if 'range' in filter_item['comparison'].lower():
                    formatted_filter['range_from'] = filter_item.get('value', '')
                    formatted_filter['range_to'] = filter_item.get('value_max', '')
                
                formatted_filters.append(formatted_filter)
            
            category_name = None
            if filters:
                for filter_item in filters:
                    if filter_item.get('category'):
                        category_name = filter_item.get('category')
                        break
            
            filter_data = {
                'Filters': formatted_filters,
                'CampaignMstID': campaign_id,
                'Name': category_name or f"Filter for {campaign_id}"  # Use category name if available
            }
            
            print("filter data",filter_data)
            # Create a mock request with user that has BankMstID
            request = type('Request', (), {})()
            request.data = filter_data
            
            # Create a mock user with BankMstID attribute
            user = type('MockUser', (), {})()
            
            # Get the BankMst object
            if self.BankMstID:
                try:
                    bank = BankMst.objects.get(BankMstID=self.BankMstID)
                    user.BankMstID = bank
                    request.user = user
                except BankMst.DoesNotExist:
                    return {"error": f"Bank with ID {self.BankMstID} not found"}
            else:
                # Use a default bank if needed
                try:
                    bank = BankMst.objects.first()
                    user.BankMstID = bank
                    request.user = user
                except Exception:
                    return {"error": "No banks found in the system"}
            
            # Call the API view
            view = CreateFilters()
            view.request = request
            response = view.post(request)
            
            return response.data
        except Exception as e:
            import traceback
            logger.error(f"Error creating filters: {e}")
            logger.error(traceback.format_exc())
            return {"error": str(e)}

    @database_sync_to_async
    def _create_communication_flow(self, flows, campaign_id):
        """Create communication flow using the CreateCommunicationFlow API"""
        from campaign.views import CreateCommunicationFlow
        from WebApp.models import BankMst
        
        try:
            formatted_flows = []
            for flow in flows:
                print("flows", flow)
                formatted_flow = {
                    'CommFlowID': '',  # New flow
                    'CommunicationType': flow['channel'].lower(),
                    'Days': flow['days'],
                    'BeforeAfter': 'after',  # Default
                    'BasedOn': flow['based_on'],
                    'CampaignMstID': campaign_id
                }
                
                # Add skip days if provided
                if flow.get('skip_days'):
                    formatted_flow['ExtraColumn2'] = flow['skip_days']
                
                # Handle applicant type
                if flow.get('applicant_type'):
                    formatted_flow['ExtraColumn1'] = flow['applicant_type']
                
                # If there's a template ID associated with this flow, include it
                if flow.get('template_id'):
                    formatted_flow['TemplateID'] = flow['template_id']
                    
                formatted_flows.append(formatted_flow)
            
            flow_name = None
            if flows and len(flows) > 0:
                for name_field in ['name', 'flowName', 'commFlowName', 'channelName']:
                    if flows[0].get(name_field):
                        flow_name = flows[0].get(name_field)
                        break

            flow_data = {
                'FlowName': flow_name or f"Communication Flow for {campaign_id}",
                'CommFlow': formatted_flows
            }
            
            # Create a mock request with user that has BankMstID
            request = type('Request', (), {})()
            request.data = flow_data
            
            # Create a mock user with BankMstID attribute
            user = type('MockUser', (), {})()
            
            # Get the BankMst object
            if self.BankMstID:
                try:
                    bank = BankMst.objects.get(BankMstID=self.BankMstID)
                    user.BankMstID = bank
                    request.user = user
                except BankMst.DoesNotExist:
                    return {"error": f"Bank with ID {self.BankMstID} not found"}
            else:
                # Use a default bank if needed
                try:
                    bank = BankMst.objects.first()
                    user.BankMstID = bank
                    request.user = user
                except Exception:
                    return {"error": "No banks found in the system"}
            
            # Call the API view
            view = CreateCommunicationFlow()
            view.request = request
            response = view.post(request)
            
            return response.data
        except Exception as e:
            import traceback
            logger.error(f"Error creating communication flow: {e}")
            logger.error(traceback.format_exc())
            return {"error": str(e)}

    @database_sync_to_async
    def _create_template(self, template, flow_channel=None):
        """Create appropriate template based on channel type and update FlowID in communication flow"""
        from campaign.views import RegisterWhatsappTemplate, RegisterSmsTemplate, RegisterBlasterTemplate, RegisterVoiceBotTemplate, RegisterIVRTemplate, UpdateFlowIdCommFlow
        from WebApp.models import BankMst, LanguageMst

        try:
            effective_channel = template.get('channel', '').lower() or (flow_channel.lower() if flow_channel else '')
            logger.info(f"Creating template '{template.get('name')}' with channel type: {effective_channel}")
            logger.info(f"Template details: {template}")
            request = type('Request', (), {})()
            user = type('MockUser', (), {})()

            # Get the BankMst object
            if self.BankMstID:
                bank = BankMst.objects.get(BankMstID=self.BankMstID)
                user.BankMstID = bank
                request.user = user
            else:
                bank = BankMst.objects.first()
                if bank:
                    user.BankMstID = bank
                    request.user = user
                    logger.info(f"Using default bank with ID {bank.BankMstID}")
                else:
                    return {"error": "No banks found in the system"}

            # Prepare template data
            template_name = template.get('name') or f"Template for {effective_channel}"
            template_data = {
                'Name': template_name,
                'Language': template.get('language', 'English'),
                'TemplateText': template.get('content', ''),
                'communication_type': effective_channel
            }
            
            # Add FlowID if available
            if template.get('FlowID'):
                template_data['FlowID'] = template['FlowID']

            # Add variables and buttons if present
            variable_mapping = {}
            sample_mapping = {}
            if template.get('variables'):
                variables = template['variables']
                if isinstance(variables, str):
                    # Try to parse from string if needed
                    try:
                        variables = json.loads(variables)
                    except:
                        variables = variables.split(',')
                
                if isinstance(variables, list):
                    for i, variable in enumerate(variables, 1):
                        variable_mapping[str(i)] = variable.strip()
                        sample_mapping[str(i)] = f"Sample for {variable.strip()}"
                    
            template_data['VariableMapping'] = variable_mapping
            template_data['SampleMapping'] = sample_mapping

            button_mapping = {}
            if template.get('buttons'):
                buttons = template['buttons']
                if isinstance(buttons, str):
                    # Try to parse from string if needed
                    try:
                        buttons = json.loads(buttons)
                    except:
                        buttons = buttons.split(',')
                
                if isinstance(buttons, list):
                    for i, button in enumerate(buttons, 1):
                        button_text = button.strip()
                        button_mapping[str(i)] = {
                            "Name": button_text,
                            "Response": f"Response for {button_text}"
                        }
            
            template_data['ButtonMapping'] = button_mapping
            request.data = template_data
            
            # Register the template based on the channel type
            if 'whatsapp' in effective_channel:
                logger.info("Registering WhatsApp template")
                view = RegisterWhatsappTemplate()
            elif 'sms' in effective_channel:
                logger.info("Registering SMS template")
                view = RegisterSmsTemplate() 
            elif 'blaster' in effective_channel:
                logger.info("Registering Blaster template")
                view = RegisterBlasterTemplate()
            elif 'ivr' in effective_channel:
                logger.info("Registering IVR template")
                view = RegisterIVRTemplate()
            elif 'voice' in effective_channel or 'ai' in effective_channel:
                logger.info("Registering VoiceBot template")
                view = RegisterVoiceBotTemplate()
            else:
                logger.error(f"Unsupported channel type: {effective_channel}")
                return {"error": f"Unsupported channel type: {effective_channel}"}

            # Call the template registration API
            response = view.post(request)
            flow_id = None
            if 'whatsapp' in effective_channel and 'whmst' in response.data:
                flow_id = response.data['whmst']
            elif 'sms' in effective_channel and 'sms' in response.data:
                flow_id = response.data['sms']
            elif 'blaster' in effective_channel and 'blaster' in response.data:
                flow_id = response.data['blaster']
            elif ('voice' in effective_channel or 'ai' in effective_channel) and 'voice' in response.data:
                flow_id = response.data['voice']
            elif 'ivr' in effective_channel and 'ivr' in response.data:
                flow_id = response.data['ivr']
            else:
                # Try to extract any ID from the response
                for key, value in response.data.items():
                    if key in ['whmst', 'sms', 'blaster', 'voice', 'ivr', 'id', 'templateid', 'template_id', 'FlowID']:
                        flow_id = value
                        break

                if not flow_id and isinstance(response.data, dict):
                    # Check for any numerical value that might be an ID
                    for key, value in response.data.items():
                        if key not in ['message', 'status']:
                            if isinstance(value, int) or (isinstance(value, str) and value.isdigit()):
                                flow_id = value
                                logger.info(f"Extracted flow ID {flow_id} from field {key}")
                                break

            logger.info(f"Extracted flow_id: {flow_id} for channel: {effective_channel}")
            print("flow_id", flow_id)
            # print("template_name",template_name)
            response_data = response.data if hasattr(response, 'data') else {}
            logger.info(f"Template registration response: {response_data}")
            
            # Build a comprehensive template result for the frontend
            template_result = {
                'name': template_name,
                'selectedTemplateName': template_name,
                'communicationType': effective_channel,
                'language': template_data['Language'],
                'commFlowID': template.get('CommFlowID'),
                'scriptValue': template_data['TemplateText'],
                'showScripting': True,
                'intents': [{"intent": "Default Intent", "responses": ["Default Response"]}],
                'response': [{"text": "Default Response"}],
                'initialTexts': '',
                'endingTexts': '',
                'initialTextVariableMapping': [],
                'endingTextVariableMapping': [],
                'variablesMapping': list(variable_mapping.values()),
                'buttonsMapping': list(button_mapping.keys()),
                'nodeButtonMapping': [],
                'nodes': [],
                'description': '',
                'rules': '',
                'interactionPlans': ''
            }
            
            # Add the template ID based on channel type
            if 'whatsapp' in effective_channel and 'whmst' in response_data:
                template_result['WhatsAppTemplateMappingID'] = response_data['whmst']
                template_result['approvedTemplateId'] = response_data['whmst']
                template_result['FlowID'] = response_data['whmst']
            elif 'sms' in effective_channel and 'sms' in response_data:
                template_result['SMSTemplateMappingID'] = response_data['sms']
                template_result['approvedTemplateId'] = response_data['sms']
                template_result['FlowID'] = response_data['sms']
            elif 'blaster' in effective_channel and 'blaster' in response_data:
                template_result['BlasterTemplateMappingID'] = response_data['blaster']
                template_result['approvedTemplateId'] = response_data['blaster']
                template_result['FlowID'] = response_data['blaster']
            elif ('voice' in effective_channel or 'ai' in effective_channel) and 'voice' in response_data:
                template_result['VoiceBotTemplateMappingID'] = response_data['voice']
                template_result['approvedTemplateId'] = response_data['voice']
                template_result['FlowID'] = response_data['voice']
            elif 'ivr' in effective_channel and 'ivr' in response_data:
                template_result['IVRTemplateMappingID'] = response_data['ivr']
                template_result['approvedTemplateId'] = response_data['ivr']
                template_result['FlowID'] = response_data['ivr']
            else:
                # Try to extract any ID from the response
                for key, value in response_data.items():
                    if key in ['whmst', 'sms', 'blaster', 'voice', 'ivr', 'id', 'templateid', 'template_id', 'FlowID']:
                        template_result['approvedTemplateId'] = value
                        template_result['FlowID'] = value
                        break
            
            # If template has a CommFlowID, update the communication flow
            print("template.get('CommFlowID')",template.get('CommFlowID'))
            if template.get('CommFlowID') :
                print("template.get('CommFlowID')",template.get('CommFlowID'))
                update_request = type('Request', (), {})()
                update_request.data = [{
                    'CommFlowID': template.get('CommFlowID'),
                    'FlowID': flow_id
                }]
                update_request.user = user

                update_view = UpdateFlowIdCommFlow()
                update_response = update_view.post(update_request)
                if hasattr(update_response, 'status_code') and update_response.status_code != 200:
                    logger.error(f"Failed to update communication flow: {update_response.data}")
                else:
                    template_result['commFlowID'] = template.get('CommFlowID')
                    logger.info(f"Updated CommFlow {template.get('CommFlowID')} with FlowID {template_result.get('FlowID')}")
            
            # Update the form data with the template metadata
            self._update_form_data_with_template(template_result)
            
            return template_result

        except Exception as e:
            logger.error(f"Error creating template: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {"error": str(e)}
        
        
        
    @database_sync_to_async
    def _update_form_data_with_template(self, template_metadata):
        """Update form data with template metadata"""
        if not self.conversation_uuid:
            return False
            
        try:
            conversation = ActiveConversation.objects.get(uuid=self.conversation_uuid)
            form_data = {}
            
            # Parse existing form data
            if conversation.form_data:
                try:
                    form_data = json.loads(conversation.form_data)
                except json.JSONDecodeError:
                    form_data = {}
            
            # Ensure templates array exists
            if 'templates' not in form_data:
                form_data['templates'] = []
            
            # Check if this template already exists (by name or id)
            template_name = template_metadata.get('name', '')
            template_id = template_metadata.get('approvedTemplateId')
            template_found = False
            
            for i, template in enumerate(form_data['templates']):
                if (template.get('name') == template_name) or (template.get('approvedTemplateId') == template_id):
                    # Update existing template with new metadata
                    form_data['templates'][i] = template_metadata
                    template_found = True
                    break
            
            # If template not found, add it
            if not template_found and template_metadata:
                form_data['templates'].append(template_metadata)
            
            # Save updated form data
            conversation.form_data = json.dumps(form_data)
            conversation.save()
            
            return True
        except Exception as e:
            logger.error(f"Error updating form data with template: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False    

    def _extract_template_id(self, template_result, channel_type=None):
        """Extract the appropriate template ID and metadata based on channel type"""
        try:
            if not template_result:
                logger.warning("No template result provided")
                return None
                
            # If template_result is already a dict with 'id' field, return the id
            if isinstance(template_result, dict) and 'id' in template_result:
                return template_result['id']
                
            # Handle direct ID input
            if isinstance(template_result, int) or (isinstance(template_result, str) and template_result.isdigit()):
                logger.info(f"Direct template ID provided: {template_result}")
                return template_result
                
            # Check for error in response
            if isinstance(template_result, dict) and "error" in template_result:
                logger.warning(f"Error in template result: {template_result['error']}")
                return None
                
            # Normalize channel type
            channel = channel_type.lower() if channel_type else ''
            logger.info(f"Extracting template ID for channel: {channel}")
            
            # Check if template has a communication_type field
            if isinstance(template_result, dict) and 'communication_type' in template_result:
                channel = template_result['communication_type'].lower()
                logger.info(f"Using communication_type from template: {channel}")
            
            # Channel-specific ID mapping
            channel_id_map = {
                'whatsapp': ['WhatsAppTemplateMappingID', 'whmst'],
                'sms': ['SMSTemplateMappingID', 'sms'],
                'blaster': ['BlasterTemplateMappingID', 'blaster'],
                'voicebot': ['VoiceBotTemplateMappingID', 'voice'],
                'ai calls': ['VoiceBotTemplateMappingID', 'voice'],
                'ai': ['VoiceBotTemplateMappingID', 'voice'],
                'ivr calls': ['IVRTemplateMappingID', 'ivr'],
                'ivr': ['IVRTemplateMappingID', 'ivr', 'IVRFlowMstID']
            }
            
            # First try to match by channel type
            for ch, keys in channel_id_map.items():
                if ch in channel:
                    for key in keys:
                        if isinstance(template_result, dict) and key in template_result:
                            logger.info(f"Found ID in field {key}: {template_result[key]}")
                            return template_result[key]
            
            # Then try generic ID fields
            generic_id_fields = ['id', 'templateid', 'template_id', 'FlowID', 'approvedTemplateId']
            for field in generic_id_fields:
                if isinstance(template_result, dict) and field in template_result:
                    logger.info(f"Found ID in generic field {field}: {template_result[field]}")
                    return template_result[field]
            
            # If we have a success status, try to find any numeric value that could be an ID
            if isinstance(template_result, dict) and 'status' in template_result and template_result['status'] == 'success':
                for key, value in template_result.items():
                    if key not in ['status', 'message', 'channel', 'communication_type']:
                        if isinstance(value, int) or (isinstance(value, str) and value.isdigit()):
                            logger.info(f"Found potential ID in field {key}: {value}")
                            return value
            
            # Look for a dictionary with an ID
            if isinstance(template_result, dict):
                for key, value in template_result.items():
                    if isinstance(value, dict):
                        for id_key in ['id', 'templateid', 'template_id']:
                            if id_key in value:
                                logger.info(f"Found ID in nested dictionary {key}.{id_key}: {value[id_key]}")
                                return value[id_key]
            
            logger.warning(f"Could not extract template ID from response: {template_result}")
            logger.warning(f"Template result keys: {list(template_result.keys()) if isinstance(template_result, dict) else type(template_result)}")
            return None
            
        except Exception as e:
            logger.error(f"Error extracting template ID: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return None
    
    @database_sync_to_async
    def _finalize_campaign(self, campaign_id, campaign_name, priority='1'):
        """Finalize a campaign using the FinalizeCampaign API"""
        from campaign.views import FinalizeCampaign
        from WebApp.models import BankMst
        
        try:
            finalize_data = {
                'CampaignMstID': campaign_id,
                'Name': campaign_name,
                'Priority': priority
            }
            
            
            request = type('Request', (), {})()
            request.data = finalize_data
            
            
            user = type('MockUser', (), {})()
           
            if self.BankMstID:
                try:
                    bank = BankMst.objects.get(BankMstID=self.BankMstID)
                    user.BankMstID = bank
                    request.user = user
                except BankMst.DoesNotExist:
                    return {"error": f"Bank with ID {self.BankMstID} not found"}
            else:
                
                try:
                    bank = BankMst.objects.first()
                    user.BankMstID = bank
                    request.user = user
                except Exception:
                    return {"error": "No banks found in the system"}
            
            
            view = FinalizeCampaign()
            view.request = request
            response = view.post(request)
            
            return response.data
            
        except Exception as e:
            import traceback
            logger.error(f"Error finalizing campaign: {e}")
            logger.error(traceback.format_exc())
            return {"error": str(e)}

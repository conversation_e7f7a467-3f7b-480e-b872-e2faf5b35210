@import url("../../index.css");

$main_color:#ecf0f1;
$point_color: #555;
$size: 5px;

.loader {
    overflow: hidden;
    width: 100%;
    display: flex;
    align-items: center;
    align-content: center;
    justify-content: center;
    z-index: 100000;
    margin: 1rem 0;
    .element {
        border-radius: 100%;
        border: $size solid var(--slate);
        margin: calc($size * 2);

        &:nth-child(1){
            animation: preloader 0.6s ease-in-out alternate infinite;
        }
        &:nth-child(2){
            animation: preloader 0.6s ease-in-out alternate 0.2s infinite;
        }
        &:nth-child(3){
            animation: preloader 0.6s ease-in-out alternate 0.4s infinite;
        }
    } 
} 

@keyframes preloader {
    100% {
      transform: scale(2);
    }
}
  
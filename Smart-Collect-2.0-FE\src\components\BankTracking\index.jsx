import React, { useEffect, useState } from "react";
import { DatePicker, Flex, message, Tabs, Typography } from "antd";
import AppHeader from "../AppHeader";
import Style from "./_style.module.scss";
import TodayAllocation from "./TodayAllocation";
import { AXIOS } from "../../apis/ho-Instance";
import Whatsapp from "./Whatsapp";
import AiCalls from "./AiCalls";
import Blaster from "./Blaster";
import Responses from "./Responses";
import dayjs from "dayjs";

const { Text } = Typography;

export default function BankTracking() {
  const [dates, setDates] = useState({
    toDate: dayjs(),
    fromDate: dayjs(),
  });
  const [dailyAllocationData, setDailyAllocationData] = useState([]);
  const [loading, setLoading] = useState(false);

  const items = [
    {
      key: "today",
      label: "Today Allocation",
      children: (
        <TodayAllocation
          isLoading={loading}
          data={dailyAllocationData}
          dates={dates}
        />
      ),
    },
    {
      key: "whatsapp",
      label: "WhatsApp Communication",
      children: (
        <Whatsapp
          isLoading={loading}
          data={dailyAllocationData}
          dates={dates}
        />
      ),
    },
    {
      key: "ai",
      label: "AI Communication",
      children: (
        <AiCalls isLoading={loading} data={dailyAllocationData} dates={dates} />
      ),
    },
    {
      key: "blaster",
      label: "Blaster Communication",
      children: (
        <Blaster isLoading={loading} data={dailyAllocationData} dates={dates} />
      ),
    },
    {
      key: "response",
      label: "Responses",
      children: (
        <Responses
          isLoading={loading}
          data={dailyAllocationData}
          dates={dates}
        />
      ),
    },
  ];

  // Handle fetching the allocations
  const handleFetchAllocation = async () => {
    setLoading(true);
    try {
      const response = await AXIOS.get("dailyallocation/");
      if (
        response.status === 200 &&
        Array.isArray(response.data) &&
        response.data?.length > 0
      ) {
        setDailyAllocationData(response.data);
      } else {
        setDailyAllocationData([]);
      }
    } catch (error) {
      console.log("Error in fetching allocation data", error?.message);
      message.warning("Please try again, something wrong!");
    } finally {
      setLoading(false);
    }
  };

  // Handle fetching the allocations
  const handleFilterAllocation = async () => {
    const { fromDate, toDate } = dates;
    setLoading(true);
    try {
      const response = await AXIOS.post("dailyallocation/", {
        from_date: fromDate ? dayjs(fromDate).format("YYYY-MM-DD") : null,
        to_date: toDate ? dayjs(toDate).format("YYYY-MM-DD") : null,
      });
      console.log(response);
      if (
        response.status === 200 &&
        Array.isArray(response.data) &&
        response.data?.length > 0
      ) {
        setDailyAllocationData(response.data);
      } else {
        setDailyAllocationData([]);
      }
    } catch (error) {
      console.log("Error in fetching allocation data", error?.message);
      message.warning("Please try again, something wrong!");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    handleFetchAllocation();
  }, []);
  return (
    <Flex vertical gap={20} className={Style.container}>
      <AppHeader title={"Daily Allocation"} />
      <Flex vertical gap={20}>
        {/* Date */}
        <Flex justify="end" align="center" gap={10}>
          <Text className={Style.date_text}>From Date:</Text>
          <DatePicker
            className={Style.date_picker}
            onChange={(date) => {
              const newDates = {
                ...dates,
                fromDate: date,
              };
              setDates(newDates);

              if (newDates.fromDate && newDates.toDate) {
                handleFilterAllocation();
              } else if (newDates.fromDate || newDates.toDate) {
                message.warning("Please select both From and To dates!");
              }
            }}
            value={dates.fromDate}
            format="DD-MM-YYYY"
            placeholder="dd-mm-yyyy"
            disabledDate={(current) =>
              current && current > dayjs().endOf("day")
            }
          />

          <Text className={Style.date_text}>To Date:</Text>
          <DatePicker
            className={Style.date_picker}
            onChange={(date) => {
              const newDates = {
                ...dates,
                toDate: date,
              };
              setDates(newDates);

              if (newDates.fromDate && newDates.toDate) {
                handleFilterAllocation();
              } else if (newDates.fromDate || newDates.toDate) {
                message.warning("Please select both From and To dates!");
              }
            }}
            value={dates.toDate}
            format="DD-MM-YYYY"
            placeholder="dd-mm-yyyy"
            disabledDate={(current) =>
              current && current > dayjs().endOf("day")
            }
          />
        </Flex>

        <Tabs defaultActiveKey="promise" items={items} className={Style.tabs} />
      </Flex>
    </Flex>
  );
}

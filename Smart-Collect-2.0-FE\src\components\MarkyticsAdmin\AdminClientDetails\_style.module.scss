@import url("../../../index.css");

$light-blue:#E3F5F6;

.client_details{
    width: 99%;
    .date_picker{
        background-color: $light-blue;
        border:none;
        :global(.ant-picker-input){
            flex-direction: row-reverse;
            gap: 10px;
            input{
                font-weight:normal;
                color:var(--dark-blue);
                font-family: 'Kanit','Courier New', Courier, monospace;
    
                &::placeholder{
                  font-weight: normal;  
                }
            }
            &::placeholder{
                color: #0F20504A;
                font-weight: normal;
            }
        }
    }

    .date_details{
        background-color: $light-blue;
        color: var(--dark-blue);
        border-radius: 8px;
        box-shadow: 4px 4px 4px 0px #00000040;
        padding: 1rem;
        .card{
            border-right: 2px solid rgba(58, 56, 56, 0.097);   
           .amount{
                color: var(--dark-blue);
                font-weight: 500;
                font-size: 1.2rem;
                font-family: 'Kanit','Courier New', Courier, monospace;
           }

            .title{
                color: var(--dark-blue);
                font-size: 1rem;
                font-family: 'Kanit','Courier New', Courier, monospace;
                font-weight: normal;
            }    
        }

        :global(.ant-col){
            &:last-child{
               border: none;
            }
        }
    }
}

// Responsive view
@media screen and (max-width:768px) {
    .client_details{
        .date{
            margin-top: 1rem;
        }
        .text{
            font-size: 11px;
            width: 200px;
        } 
         .date_picker{
             font-size: 12px;
             :global(.ant-picker-input){
                 input{
                  font-size: 11px;
                }
             }
        }

        .date_details{
            flex-direction: column;
            padding: 0.5rem;
            row-gap: 10px;
            .card{
                border-right: none;
                border-bottom: 2px solid rgba(58, 56, 56, 0.097);
              .amount{
                font-size: 14px;
              }  
              .title{
                font-size: 12px;
              }
            }
           
        }
    }
}
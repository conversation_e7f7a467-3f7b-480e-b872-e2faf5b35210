import {
  <PERSON><PERSON>,
  <PERSON><PERSON>icker,
  Divider,
  Flex,
  message,
  Select,
  Typography,
} from "antd";
import { useEffect, useState } from "react";
import DOWNLOAD_IMG from "../../../assets/Images/download.svg";
import FILTER_IMG from "../../../assets/Images/filter.svg";
import CROSS_IMG from "../../../assets/Images/cross.svg";
import Style from "./_style.module.scss";
import { handleDownloadTable } from "../../../constant";
import PropTypes from "prop-types";
import { AXIOS } from "../../../apis/ho-Instance";
import dayjs from "dayjs";
const { Text } = Typography;

export default function ReportsButton({
  filteredData,
  data,
  excelName,
  onFilterApply,
  fromDate,
  toDate,
}) {
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [options, setOptions] = useState({
    branches: [],
    loanTypes: [],
    customerIds: [],
    disbursementIds: [],
  });
  const [filterKeys, setFilterKeys] = useState({
    fromDate: null,
    toDate: null,
  });
  const [selectedFilters, setSelectedFilters] = useState({
    branches: [],
    loanTypes: [],
    customerIds: [],
    disbursementIds: [],
  });
  const [messageApi, contextHolder] = message.useMessage();

  // Handle downloading
  const handleDownload = async () => {
    const actualData = excelName === "Accounts" ? filteredData : data;
    if (!actualData || actualData.length === 0) {
      return message.error("There is no data for downloading!");
    }

    // Let the loading message render first
    try {
      const cleanedData = actualData.map(({ CreatedDate, ...rest }) => rest);
      let finalTableData = cleanedData;
      if (excelName === "Accounts") {
        finalTableData = cleanedData.map((item) => ({
          from_date: fromDate || "--",
          to_date: toDate || "--",
          ...item,
        }));
      }
      await handleDownloadTable({
        excelName,
        worksheetName: `sheet-${excelName}`,
        tableData: finalTableData,
      });
    } catch (error) {
      console.error("Download error:", error);
      messageApi.error("Failed to generate download!");
    }
  };

  const handleFilterKeysChange = ({ type, key }) => {
    setFilterKeys((prev) => ({ ...prev, [type]: key }));
  };

  // Handle the accounts filtering
  const handleAccountsFilter = async (branches) => {
    setLoading(true);
    try {
      const res = await AXIOS.post("accountsummaryreport/", {
        branch_id: branches,
        from_date: filterKeys.fromDate
          ? dayjs(filterKeys.fromDate).format("YYYY-MM-DD")
          : null,
        to_date: filterKeys.toDate
          ? dayjs(filterKeys.toDate).format("YYYY-MM-DD")
          : null,
      });
      return {
        fromDate: Array.isArray(res.data)
          ? res.data[0]?.from_date
          : res.data?.from_date,
        toDate: Array.isArray(res.data)
          ? res.data[0]?.to_date
          : res.data?.to_date,
        data: res.data[0]?.account_summary_report,
      };
    } catch (error) {
      console.warn("Error in accounts filter", error);
      message.info("No accurate data found!");
      return {
        fromDate,
        toDate,
        data,
      };
    } finally {
      setLoading(false);
    }
  };

  // Handle Clear filter
  const handleClear = () => {
    setSelectedFilters({
      branches: [],
      loanTypes: [],
      customerIds: [],
      disbursementIds: [],
    });
    setFilterKeys({
      toDate: null,
      fromDate: null,
    });
    if (excelName === "Accounts") {
      onFilterApply({
        fromDate: null,
        toDate: null,
        data: data,
      });
    } else {
      onFilterApply(data);
    }
    setVisible(false);
  };

  // Handle Apply filter
  const handleApplyFilter = async () => {
    const { branches, loanTypes, customerIds, disbursementIds } =
      selectedFilters;
    let filtered;
    if (excelName === "Accounts") {
      filtered = await handleAccountsFilter(branches);
    } else if (excelName === "Communication") {
      // For Communication, return the date filters
      filtered = {
        fromDate: filterKeys.fromDate
          ? dayjs(filterKeys.fromDate).format("YYYY-MM-DD")
          : null,
        toDate: filterKeys.toDate
          ? dayjs(filterKeys.toDate).format("YYYY-MM-DD")
          : null,
      };
    } else {
      // Filter out matched data
      filtered = data.filter(
        (item) =>
          (!branches.length ||
            branches.includes(item.BranchName) ||
            branches.includes(item.branchname)) &&
          (!loanTypes.length || loanTypes.includes(item.LoanType)) &&
          (!customerIds.length || customerIds.includes(item.CustomerMstID)) &&
          (!disbursementIds.length ||
            disbursementIds.includes(item.DisbursementID))
      );
      // No data found
      if (!filtered.length) {
        message.info("No data matched your filter.");
      }
    }
    onFilterApply(filtered); // Set the filtered data
    setVisible(false); // hide the modal
  };

  const getUnique = ({ array, key }) => {
    const keys = Array.isArray(key)
      ? key.map((k) => k?.toLowerCase())
      : [key?.toLowerCase()];
    const seen = new Set();

    return array?.reduce((acc, item) => {
      const matchedKey = Object.keys(item).find((k) =>
        keys.includes(k?.toLowerCase())
      );
      if (matchedKey && item[matchedKey] && !seen.has(item[matchedKey])) {
        seen.add(item[matchedKey]);
        acc.push(item[matchedKey]);
      }
      return acc;
    }, []);
  };

  // Set the options based on data
  useEffect(() => {
    const uniqueBranches = Array.from(
      new Map(
        getUnique({
          array: data,
          key: ["BranchName", "branchname", "BRANCH"],
        })?.map((val) => {
          const branchItem = data.find(
            (item) => item.Branch === val || item.branchname === val
          );
          const value =
            excelName === "Accounts" ? branchItem?.BranchMstID ?? val : val;
          return [value, { label: val, value }];
        })
      ).values()
    );

    const uniqueCustomerIds = Array.from(
      new Set(
        getUnique({
          array: data,
          key: ["CustomerMstID", "CustomerID"],
        })?.map((val) => val)
      )
    ).map((val) => ({ label: val, value: val }));

    const uniqueDisbursementIds = Array.from(
      new Set(
        getUnique({
          array: data,
          key: "DisbursementID",
        })?.map((val) => val)
      )
    ).map((val) => ({ label: val, value: val }));

    const uniqueLoanTypes = Array.from(
      new Set(
        getUnique({
          array: data,
          key: "LoanType",
        })?.map((val) => val)
      )
    ).map((val) => ({ label: val, value: val }));

    setOptions({
      branches: uniqueBranches,
      customerIds: uniqueCustomerIds,
      disbursementIds: uniqueDisbursementIds,
      loanTypes: uniqueLoanTypes,
    });

    setSelectedFilters({
      branches: [],
      loanTypes: [],
      customerIds: [],
      disbursementIds: [],
    });

    setFilterKeys({
      fromDate: null,
      toDate: null,
    });
  }, [data]);

  return (
    <>
      {contextHolder}
      <div className={Style.container}>
        <Flex justify="end" vertical>
          <Flex className={Style.button_container} justify="center">
            {/* Download button - only show if not Communication */}
            {excelName !== "Communication" && (
              <>
                <div className={Style.img_container}>
                  <input
                    type="image"
                    src={DOWNLOAD_IMG}
                    alt="download"
                    onClick={handleDownload}
                  />
                </div>
                <Divider
                  type="vertical"
                  style={{ borderColor: "#FFFFFF", height: "inherit" }}
                />
              </>
            )}

            {/* Filter button */}
            <div className={Style.img_container}>
              <input
                type="image"
                src={FILTER_IMG}
                alt="filter"
                onClick={() => setVisible(true)}
              />
            </div>
          </Flex>
        </Flex>

        {visible && (
          <Flex className={Style.modal} vertical>
            <Flex justify="space-between">
              <Flex justify="center" style={{ flex: 1 }}>
                <Text className={Style.text}>Filter</Text>
              </Flex>
              <input
                type="image"
                src={CROSS_IMG}
                alt="cross"
                style={{ cursor: "pointer" }}
                onClick={() => setVisible(false)}
              />
            </Flex>

            <Divider style={{ backgroundColor: "#0F2050", margin: "1rem 0" }} />

            <Flex gap={15} vertical className={Style.container}>
              {excelName === "Communication" ? (
                <>
                  <Flex vertical>
                    <Text className={Style.fields}>From Date:</Text>
                    <DatePicker
                      className={Style.date_picker}
                      onChange={(date) =>
                        handleFilterKeysChange({ type: "fromDate", key: date })
                      }
                      value={filterKeys.fromDate}
                      format="DD-MM-YYYY"
                      placeholder="dd-mm-yyyy"
                    />
                  </Flex>
                  <Flex vertical>
                    <Text className={Style.fields}>To Date:</Text>
                    <DatePicker
                      className={Style.date_picker}
                      onChange={(date) =>
                        handleFilterKeysChange({ type: "toDate", key: date })
                      }
                      value={filterKeys.toDate}
                      format="DD-MM-YYYY"
                      placeholder="dd-mm-yyyy"
                    />
                  </Flex>
                </>
              ) : excelName === "Accounts" ? (
                <>
                  <Flex vertical>
                    <Text className={Style.fields}>Select Branch:</Text>
                    <Select
                      mode="multiple"
                      value={selectedFilters.branches}
                      onChange={(val) =>
                        setSelectedFilters((prev) => ({ ...prev, branches: val }))
                      }
                      options={options.branches || []}
                      placeholder="Select Branch"
                      className={Style.custom_select}
                    />
                  </Flex>
                  <Flex vertical>
                    <Text className={Style.fields}>From Date:</Text>
                    <DatePicker
                      className={Style.date_picker}
                      onChange={(date) =>
                        handleFilterKeysChange({ type: "fromDate", key: date })
                      }
                      value={filterKeys.fromDate}
                      format="DD-MM-YYYY"
                      placeholder="dd-mm-yyyy"
                    />
                  </Flex>
                  <Flex vertical>
                    <Text className={Style.fields}>To Date:</Text>
                    <DatePicker
                      className={Style.date_picker}
                      onChange={(date) =>
                        handleFilterKeysChange({ type: "toDate", key: date })
                      }
                      value={filterKeys.toDate}
                      format="DD-MM-YYYY"
                      placeholder="dd-mm-yyyy"
                    />
                  </Flex>
                </>
              ) : (
                <>
                  <Flex vertical>
                    <Text className={Style.fields}>Select Branch:</Text>
                    <Select
                      mode="multiple"
                      value={selectedFilters.branches}
                      onChange={(val) =>
                        setSelectedFilters((prev) => ({ ...prev, branches: val }))
                      }
                      options={options.branches || []}
                      placeholder="Select Branch"
                      className={Style.custom_select}
                    />
                  </Flex>
                  <Flex vertical>
                    <Text className={Style.fields}>Select Loan Type:</Text>
                    <Select
                      mode="multiple"
                      value={selectedFilters.loanTypes}
                      onChange={(val) =>
                        setSelectedFilters((prev) => ({
                          ...prev,
                          loanTypes: val,
                        }))
                      }
                      options={options.loanTypes || []}
                      placeholder="All Loan Types"
                      className={Style.custom_select}
                    />
                  </Flex>
                  <Flex vertical>
                    <Text className={Style.fields}>Customer ID:</Text>
                    <Select
                      mode="multiple"
                      value={selectedFilters.customerIds}
                      onChange={(val) =>
                        setSelectedFilters((prev) => ({
                          ...prev,
                          customerIds: val,
                        }))
                      }
                      options={options.customerIds || []}
                      placeholder="All customer ids"
                      className={Style.custom_select}
                    />
                  </Flex>
                  <Flex vertical>
                    <Text className={Style.fields}>Disbursement ID:</Text>
                    <Select
                      mode="multiple"
                      value={selectedFilters.disbursementIds}
                      onChange={(val) =>
                        setSelectedFilters((prev) => ({
                          ...prev,
                          disbursementIds: val,
                        }))
                      }
                      options={options.disbursementIds || []}
                      placeholder="All disbursement ids"
                      className={Style.custom_select}
                    />
                  </Flex>
                </>
              )}

              <Flex justify="space-evenly">
                <Button
                  className={Style.apply}
                  onClick={handleApplyFilter}
                  loading={loading}
                >
                  Apply
                </Button>
                <Button className={Style.clear} onClick={handleClear}>
                  Clear
                </Button>
              </Flex>
            </Flex>
          </Flex>
        )}
      </div>
    </>
  );
}

ReportsButton.propTypes = {
  filteredData: PropTypes.oneOfType([PropTypes.array, PropTypes.object]),
  data: PropTypes.array,
  excelName: PropTypes.string,
  fromDate: PropTypes.string,
  toDate: PropTypes.string,
  onFilterApply: PropTypes.func,
};

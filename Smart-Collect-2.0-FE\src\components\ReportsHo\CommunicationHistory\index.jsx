import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Typography,
} from "antd";
import { useEffect, useState } from "react";
import PropTypes from "prop-types";
import dayjs from "dayjs";

import Style from "./_style.module.scss";
import { formatDigits } from "../../../constant";
import { CommunicationCard } from "./CommunicationCard";
import { CommunicationsChart } from "./CommunicationsChart";
import { CommunicationChannelChart } from "./CommunicationChannelChart";
import { ChannelDetailsTable } from "./ChannelDetailsTable";
import ApplicationLoader from "../../ApplicationLoader";
import { AXIOS } from "../../../apis/ho-Instance";
import ReportsButton from "../ReportsButton";

// Images
import TOTAL_SVG from "../../../assets/Images/comm-total.svg";
import WHAT_SVG from "../../../assets/Images/whatsapp.png";
import AI_SVG from "../../../assets/Images/ai.png";
import BlASTER_SVG from "../../../assets/Images/blaster.png";
import DIALERS_IMG from "../../../assets/Images/customer-care.png";
import SMS_IMG from "../../../assets/Images/image 79.png";
import IVR_IMG from "../../../assets/Images/IVR.png";

const { Text } = Typography;

export default function CommunicationHistory({ currentTab }) {
  const [showDetailsTable, setShowDetailsTable] = useState(false);
  const [loading, setLoading] = useState(false);
  const [commData, setCommData] = useState(null);
  const [error, setError] = useState(null);

  const getBranchIdsFromLocalStorage = () => {
    const ids = localStorage.getItem("summaryBranchIds");
    if (!ids) return [];
    try {
      return JSON.parse(ids);
    } catch {
      return ids.replace(/[\[\]\s]/g, "").split(",").map(Number).filter(Boolean);
    }
  };

  const getDefaultDates = () => {
    // Get EngagementStartDate from localStorage
    const engagementStartDate = localStorage.getItem("EngagementStartDate");
    let fromDate = "2025-06-04"; // fallback default
    
    if (engagementStartDate) {
      try {
        fromDate = JSON.parse(engagementStartDate);
      } catch {
        fromDate = engagementStartDate;
      }
    }
    
    // Use today's date as to date
    const toDate = dayjs().format("YYYY-MM-DD");
    
    return { fromDate, toDate };
  };

  // Handle missing dates function (copied from CollectHoBmDashboard)
  const fillMissingDates = (engagementData) => {
    if (!engagementData.length) return [];

    // Sort data by date
    engagementData.sort((a, b) =>
      dayjs(a.CreatedDate).diff(dayjs(b.CreatedDate))
    );

    // Get the first date and today
    const firstDate = dayjs(engagementData[0].CreatedDate);
    const currentDate = dayjs();

    // Generate all dates in range
    const allDates = [];
    let tempDate = firstDate;

    while (
      tempDate.isBefore(currentDate) ||
      tempDate.isSame(currentDate, "day")
    ) {
      allDates.push(tempDate.format("YYYY-MM-DD"));
      tempDate = tempDate.add(1, "day");
    }

    // Create a lookup for existing data
    const dataMap = new Map(
      engagementData.map((item) => [item.CreatedDate, item.total_engaged])
    );

    // Map over all dates, filling missing ones with 0
    return allDates.map((date) => ({
      CreatedDate: date,
      total_engaged: dataMap.get(date) || 0,
    }));
  };

  // Get dashboard data to set EngagementStartDate
  const handleGetDashboardData = async () => {
    try {
      const response = await AXIOS.get("dashboard/");
      if (response.status === 200) {
        const data = response.data;
        const engagementData = Array.isArray(data?.engagement_data)
          ? data.engagement_data
          : [];

        const filledData = fillMissingDates(engagementData);
        
        if (filledData.length) {
          const formattedDate = dayjs(filledData[0].CreatedDate).format(
            "YYYY-MM-DD"
          );
          localStorage.setItem(
            "EngagementStartDate",
            JSON.stringify(formattedDate)
          );
        }
      }
    } catch (error) {
      console.log("Error in dashboard API:", error);
    }
  };

  const handleFetchCommunication = async (fromDate = null, toDate = null) => {
    setLoading(true);
    setError(null);

    // Get default dates if not provided
    const defaultDates = getDefaultDates();
    
    const payload = {
      from_date: fromDate || defaultDates.fromDate,
      to_date: toDate || defaultDates.toDate,
      branch_ids: getBranchIdsFromLocalStorage(),
    };

    console.log("[CommunicationHistory] API Payload:", payload);

    try {
      const res = await AXIOS.post("/daywise-communication-counts/", payload, {
        headers: { "Content-Type": "application/json" },
      });
      console.log("[CommunicationHistory] API Response:", res.data);
      setCommData(res.data);
    } catch (err) {
      console.warn("Error in communication summary:", err);
      setError(err.message || "Unknown error");
    } finally {
      setLoading(false);
    }
  };

  const handleFilterApply = (filteredResult) => {
    if (filteredResult && filteredResult.fromDate && filteredResult.toDate) {
      // Handle date filtering
      handleFetchCommunication(filteredResult.fromDate, filteredResult.toDate);
    } else {
      // Handle clear filter
      handleFetchCommunication();
    }
  };

  useEffect(() => {
    // First get dashboard data to set EngagementStartDate
    handleGetDashboardData().then(() => {
      // Then fetch communication data with the updated dates
      handleFetchCommunication();
    });
    // eslint-disable-next-line
  }, [currentTab]);

  const {
    summary: {
      total_communications: totalComm = 0,
      whatsapp_total: whatsapp = 0,
      voicebot_total: ai = 0,
      blaster_total: blaster = 0,
      ivr_total: ivr = 0,
      sms_total: sms = 0,
    } = {},
    data: daywiseData = [],
  } = commData || {};

  const dialer = 0; // Placeholder if not yet available

    return (
    <Flex vertical gap={20} className={Style.communication_history}>
      {showDetailsTable ? (
        <ChannelDetailsTable setShowDetailsTable={setShowDetailsTable} />
      ) : loading ? (
        <ApplicationLoader />
      ) : error ? (
        <div style={{ color: "red" }}>Error: {error}</div>
      ) : (
        <>
          {/* Summary section */}
          <Flex align="center" gap={10}>
            <Flex vertical gap={15}>
              <Text className={Style.heading}>Total Communication:</Text>
              <CommunicationCard count={formatDigits(totalComm)} icon={TOTAL_SVG} />
            </Flex>

            <Divider
              variant="solid"
              type="vertical"
              size="large"
              className={Style.divider_container}
            />

            <Flex vertical gap={10} style={{ flex: 1 }}>
              <Flex justify="space-between" align="center">
                <Text className={Style.heading}>Channel Wise Communications:</Text>
                <Flex gap={10} align="center">
                  <ReportsButton
                    data={[]}
                    excelName="Communication"
                    onFilterApply={handleFilterApply}
                  />
                  <Button
                    className={Style.btn}
                    onClick={() => setShowDetailsTable(true)}
                  >
                    Details
                  </Button>
                </Flex>
              </Flex>

              <Flex gap={10} align="center" justify="space-between">
                <CommunicationCard count={formatDigits(whatsapp)} icon={WHAT_SVG} title="Whatsapp" />
                <CommunicationCard count={formatDigits(ai)} icon={AI_SVG} title="AI" />
                <CommunicationCard count={formatDigits(dialer)} icon={DIALERS_IMG} title="Dialer" size={35} />
                <CommunicationCard count={formatDigits(ivr)} icon={IVR_IMG} title="IVR" size={32} />
                <CommunicationCard count={formatDigits(blaster)} icon={BlASTER_SVG} title="Blaster" />
                <CommunicationCard count={formatDigits(sms)} icon={SMS_IMG} title="SMS" size={25} />
              </Flex>
            </Flex>
          </Flex>

          {/* Charts section */}
          <Row gutter={[30, 10]}>
            <Col md={12} xs={24}>
              <CommunicationsChart data={daywiseData} />
            </Col>
            <Col md={12} xs={24}>
              <CommunicationChannelChart summary={commData?.summary || {}} />
            </Col>
          </Row>
        </>
      )}
    </Flex>
  );
}

CommunicationHistory.propTypes = {
  currentTab: PropTypes.string,
};

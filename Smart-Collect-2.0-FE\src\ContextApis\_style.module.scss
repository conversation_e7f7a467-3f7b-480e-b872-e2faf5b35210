@import url("../index.css");

.container{
    background-color: #EDF3FF;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 0 0.5rem 0.5rem;
    border-radius: 5px;
    width: 80px;
    opacity: 0.3;
    animation: fadeInSequence 2s ease-in forwards;

    .title{
        font-family: 'kanit','Courier New', Courier, monospace;
        white-space: nowrap;
        font-size: 12px;
        text-align: center;
    }

    .icon{
        position: relative;
        top: -10px;
        width: 18px;
        height: 18px;
        border-radius: 50px;
        padding: 0.5rem;
        background-color: var(--dark-blue);
        img{
            width: 100%;
            object-fit: contain;
        }
    }
    &:nth-child(1) { animation-delay: 0.5s;}
    &:nth-child(2) { animation-delay: 1.5s; }
    &:nth-child(3) { animation-delay: 2.5s;}
    &:nth-child(4) { animation-delay: 3.5s; }
    &:nth-child(5) { animation-delay: 4.5s;}
    &:nth-child(6) { animation-delay: 5s; }

    @keyframes fadeInSequence {
        0% { opacity: 0.3}
        100% { opacity: 1 }
    }
}

// TOUR CUSTOM CSS
:global(.ant-tour-mask){
    z-index: 1234 !important; 
}

:global(.ant-tour){
    z-index: 1234 !important;
    :global(.ant-tour-header){
        padding: 2rem;
        :global(.ant-tour-title){
            color: #407BFF;
        }
    } 
    :global(.ant-tour-inner){
        background-color: transparent;

        :global(.ant-tour-close){
            svg{
                width: 10px;
                height: 10px;
            }
            &:hover{
                background-color: transparent;
            }
        }

        // header
        :global(.ant-tour-header){
            width: 100%;
            background-color: white;
            padding: 20px;
            border-top-right-radius: 10px;
            border-top-left-radius: 10px;
        }

        // desc
        :global(.ant-tour-description){
            padding: 0 10px 10px 20px;
            background-color: white;
            border-bottom-right-radius: 10px;
            border-bottom-left-radius: 10px;

            .next_btn, .skip_btn{
                width: 70px;
                height: 25px;
                border-color: var(--dark-blue);
                outline: none !important;
                color: black !important;
                padding: 0;
                font-size: 13px;
            }
            .skip_btn{
                background-color: var(--dark-blue);
                color: white !important;
            }
        }

        // footer
        :global(.ant-tour-footer){
            display: none;   
            :global(.ant-tour-indicators){
                display: none;   
            }
        }
    }
}

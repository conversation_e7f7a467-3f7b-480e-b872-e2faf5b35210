import React, { useState } from "react";
import { Input, <PERSON><PERSON>, <PERSON>, message, Flex } from "antd";
import Style from "./_login.module.scss";
import { useNavigate, <PERSON> } from "react-router";
import COMPANY_LOGO from "../../assets/Images/logo.png";
import COMPANY_NAME_IMG_BLUE from "../../assets/Images/logo-title-blue.png";
import MARKYTICS_LOGO from "../../assets/Images/Rightside_Logo.png";
import { AXIOS } from "../../apis/ho-Instance";
import { FaEye, FaEyeSlash } from "react-icons/fa";

const passwordIconRender = (visible) => (visible ? <FaEyeSlash /> : <FaEye />);

export default function Login() {
  const [messageApi, contextHolder] = message.useMessage();
  const [loading, setLoading] = useState(false);
  const [loginCredentials, setLoginCredentials] = useState({
    username: "",
    password: "",
  });
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      if (
        loginCredentials.password.trim() === "" ||
        loginCredentials.username.trim() === ""
      ) {
        messageApi.error("Please, Enter your credentials");
        return;
      }

      const response = await AXIOS.post("token/generate/", {
        username: loginCredentials.username,
        password: loginCredentials.password,
      });
      const { Designation: designation, is_admin, ...rest } = response.data;
      const userDetails = {
        ...rest,
        is_admin,
        designation,
      };
      localStorage.setItem("user", JSON.stringify(userDetails));

      // Navigate to according designation
      if (is_admin) {
        navigate("/admin", { replace: true });
      } else {
        const path = (() => {
          const lowerDesignation = designation?.toLowerCase();
          if (["fo", "co"].includes(lowerDesignation)) return "/field";
          if (lowerDesignation === "calling") return "/agent-calling";
          return "/ho";
        })();

        navigate(path, { replace: true });
      }
    } catch (error) {
      if (error?.response?.data?.detail)
        messageApi.open({
          content: error?.response?.data?.detail,
          type: "error",
          duration: 1,
        });
      else if (error?.message)
        messageApi.open({
          type: "error",
          content: `${error.message}`,
          duration: 1,
        });
      else
        messageApi.open({
          type: "error",
          content: `Something wrong please try again`,
          duration: 1,
        });
    } finally {
      setLoading(false);
      setLoginCredentials({ username: "", password: "" });
    }
  };

  const handleInputChange = (e) => {
    const name = e.target.name;
    const value = e.target.value;
    setLoginCredentials((prev) => ({ ...prev, [name]: value }));
  };

  return (
    <>
      {contextHolder}
      <Flex className={Style.container}>
        {/* left Side with Login Form */}
        <Flex className={Style.leftSide}>
          {/* Header */}
          <div className={Style.header}>
            <div className={Style.logo_container}>
              <div className={Style.logo_image}>
                <img src={COMPANY_LOGO} alt="Company Logo" />
              </div>
              <div className={Style.logo_title_image}>
                <img
                  src={COMPANY_NAME_IMG_BLUE}
                  alt="Company Name"
                  className={Style.desktopLogo}
                />
              </div>
            </div>
          </div>

          {/* Login Card */}
          <div className={Style.cardWrapper}>
            <Card
              className={Style.card}
              title={
                <Flex vertical gap={1}>
                  <span
                    style={{
                      color: "rgb(15, 32, 80)",
                      textDecoration: "underline",
                    }}
                  >
                    Login
                  </span>
                  <p style={{ margin: 0 }}>Welcome to Smart Collect!</p>
                </Flex>
              }
            >
              <form onSubmit={handleSubmit} className={Style.form}>
                <Flex vertical gap={10}>
                  <div className={Style.inputContainer}>
                    <label className={Style.label} htmlFor="username">
                      UserName
                    </label>
                    <Input
                      value={loginCredentials.username}
                      onChange={handleInputChange}
                      className={Style.input}
                      name="username"
                      id="username"
                    />
                  </div>

                  <div className={Style.inputContainer}>
                    <Input.Password
                      type="password"
                      value={loginCredentials.password}
                      onChange={handleInputChange}
                      className={Style.input}
                      name="password"
                      id="password"
                      iconRender={passwordIconRender}
                    />
                  </div>

                  <Link to="/forget-password" className={Style.forgotPassword}>
                    Forgot Password?
                  </Link>
                </Flex>

                <Button
                  type="primary"
                  htmlType="submit"
                  block
                  className={Style.button}
                  loading={loading}
                >
                  Login
                </Button>
              </form>
            </Card>
          </div>
        </Flex>
        {/* right Side */}
        <div className={Style.backgroundCircle}></div>
        <Flex className={Style.imageContainer}>
          <div className={Style.power_by}>
            <img src={MARKYTICS_LOGO} alt="Markytics Logo" />
          </div>
        </Flex>
        <a
          href="https://www.markytics.ai"
          target="_blank"
          rel="noopener noreferrer"
          className={Style.websiteLink}
        >
          www.markytics.ai
        </a>
      </Flex>
    </>
  );
}

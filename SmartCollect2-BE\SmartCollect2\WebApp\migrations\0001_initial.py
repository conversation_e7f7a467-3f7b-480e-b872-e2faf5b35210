# Generated by Django 5.1.5 on 2025-02-01 06:04

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="BankMst",
            fields=[
                ("BankMstID", models.AutoField(primary_key=True, serialize=False)),
                ("OnBoardingDate", models.DateField()),
                ("IsActive", models.BooleanField(default=True)),
                ("UpdatedDate", models.DateField(auto_now=True)),
                ("Remarks", models.TextField(blank=True, null=True)),
                ("SubscriptionID", models.IntegerField()),
                ("SubscriptionType", models.CharField(max_length=255)),
                (
                    "ExtraColumn1",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn2",
                    models.Char<PERSON>ield(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn3",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn4",
                    models.Char<PERSON>ield(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn5",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("BankName", models.CharField(max_length=255)),
            ],
            options={
                "db_table": "BankMst",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="BranchMst",
            fields=[
                ("BranchMstID", models.AutoField(primary_key=True, serialize=False)),
                ("BankMstID", models.IntegerField()),
                ("BranchCode", models.CharField(max_length=255)),
                ("BranchName", models.CharField(max_length=255)),
                ("IsActive", models.BooleanField(default=True)),
                ("CreatedDate", models.DateField(auto_now_add=True)),
                ("UpdatedDate", models.DateField(auto_now=True)),
                ("LngMstID", models.IntegerField()),
                ("Remarks", models.TextField(blank=True, null=True)),
                ("State", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "ExtraColumn1",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn2",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn3",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn4",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn5",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
            ],
            options={
                "db_table": "BranchMst",
                "managed": False,
            },
        ),
    ]

import importlib
from django.dispatch import receiver
from django.dispatch import Signal
from django.contrib.auth.signals import user_logged_in
from django.utils.translation import gettext_lazy as _
from WebApp.ip import get_client_ip
from WebApp.app_settings import (
    GEOLOCATION_METHOD, GEOLOCATION_PLACEHOLDER_IP, GEOLOCATION_BLOCK_FIELDS)
from WebApp.models import LoginHistory
from WebApp.utils import get_geolocation_data
from django.core.exceptions import FieldDoesNotExist


jwt_logged_in = Signal()

@receiver(user_logged_in)
@receiver(jwt_logged_in)
def post_login(sender, user, request, **kwargs):
    print('User logged in', sender, user, request, kwargs)

    client_ip, is_routable = get_client_ip(request)
    method_path = GEOLOCATION_METHOD
    result = None
    mapped_fields = {}

    if not client_ip:
        client_ip = GEOLOCATION_PLACEHOLDER_IP

    else:
        if not is_routable:
            result = {"error": True, "reason": "Address not routable"}

        elif method_path:
            module_name, func_name = method_path.rsplit('.', 1)
            try:
                module = importlib.import_module(module_name)
                geolocation_func = getattr(module, func_name)
                result = geolocation_func(client_ip)
            except (ImportError, AttributeError) as er:
                raise ValueError("Invalid geolocation method specified in settings.\n", er) from er

    if not result:
        result = get_geolocation_data(client_ip)
        assert isinstance(result, dict)

    for key, value in result.items():
        if key in GEOLOCATION_BLOCK_FIELDS:
            continue
        try:
            _ = LoginHistory._meta.get_field(key)
            mapped_fields[key] = value
        except FieldDoesNotExist:
            pass

    _ = LoginHistory.objects.create(
        user=user,
        ip=client_ip,
        user_agent=request.META.get('HTTP_USER_AGENT', ''),
        ip_info=result,
        **mapped_fields
    )

@import url("../../../index.css");

$light-gray:#D9D9D9;
$tab-height: 7px;

.dialer_modal{
    .icon_img{
        width: 13px;
        height: 20px;
        img{
            width: 100%;
            object-fit: contain;
        }
    }

    .feedback_title{
        margin: 0;
        color: white;
        font-size:18px; 
        font-family: 'Kanit','Courier New', Courier, monospace;
        font-weight: 400;
    }

    .feedback_button{
        background-color: var(--dark-blue);
        color: white;
        padding: 1rem 2rem;
        border: none;
        font-family: 'Kanit','Courier New', Courier, monospace;

        &:hover{
            background-color: var(--dark-blue) !important;
            color: white !important;
            border: none;
            box-shadow:none;
        }
    }

    .message_text{
        font-size: 12px;
        font-family: 'Kanit','Courier New', Courier, monospace;
        color: #EC3939;
        font-weight: 300;
        letter-spacing: 0.7px;
    }

    .text{
        color: var(--dark-blue);
        font-family: 'Kanit','Courier New', Courier, monospace;
        white-space: nowrap;
    }

    .custom_input {
        background-color: #E4F8F9;
        caret-color: var(--dark-blue);
        padding: 0.3rem 0.5rem;
        text-transform: capitalize;
        width: 300px;

    
        &:global(.ant-input-outlined) {
            border: none;
            box-shadow: none;
            color: var(--dark-blue);
            font-weight: 400;
            border-radius: 6px;
            font-family: 'Kanit','Courier New', Courier, monospace;
        }
        
    }

    .custom_number{
        &:global(.ant-picker-outlined),
        &:global(.ant-input-number){
            background-color: #E4F8F9;
            border:none !important;
        }

        &:global(.ant-input-number){
            width: 300px;
            box-shadow: 0px 2px 2px 0px #00000040 inset;
        }

        :global(.ant-input-number-input){
            color: var(--dark-blue);
            font-weight: 400;
            font-family: 'Kanit','Courier New', Courier, monospace;
            padding: 0.3rem 0.5rem;
        }
        
        :global(.ant-input-number-handler-wrap){
            display: none;
        }  
    }

    .custom_button{
        .img{
            width: 15px;
            img{
                width: 100%;
                object-fit: contain;
                fill: white;
            }
        }

        background-color: var(--dark-blue);
        color: white;
        padding: 1rem 4rem;
        margin-top: 1.5rem;
        outline: none;
        border: none;

        :global(.ant-typography){
            font-family: 'Kanit','Courier New', Courier, monospace;
            color: white;
        }

        &:hover{
            background-color: var(--dark-blue) !important;
        }

    }

    :global(.ant-modal-close){
        right: 14px;
        top: 10px;
        color: white;
        &:hover{
          color: white !important;
        }
        :global(.ant-modal-close-x){
          font-size:10px;
          color: white;
        }
    }  

    :global(.ant-modal-header){
        background: var(--dark-blue);
        padding: 0.5rem 0px;
        text-align: center;
        border-bottom: 1px solid #FFFFFF;
        border-radius:10px;

        :global(.ant-modal-title){
            color: white;
            font-weight: 500;
        }
    }

    :global(.ant-modal-content){
       padding: 6px;
       background-color: white;

       :global(.ant-modal-body){                                                                                                                                                                                                                                                                                                                                                                                                                                                          
        padding: 1rem;

       }
    }
}

// Responsive view
@media screen and (max-width:768px) {
    .feedback_modal{
        .icon_img{
            width: 10px;
            height: 24px;
        }
        .feedback_title{
            font-size: 12px;
        }
        .feedback_button{
            padding: 0rem 1rem;
            font-size: 11px;
        }
      :global(.ant-modal-content){
        padding: 4px;

        :global(.ant-modal-body){  
            padding: 0.3rem;
            :global(.ant-tabs){
                :global(.ant-tabs-ink-bar){
                    height: 4px;
                }

                :global(.ant-tabs-nav){
                    :global(.ant-tabs-tab){
                        font-size: 10px;
                    }
                    &::before{
                        border-bottom: 4px solid $light-gray;
                    }
                }
            }
        }
      }
    }
}
@import url("../../../../../index.css");

$light-blue: #E4F8F9;
$blue: #BECFFF;
$switch-disable:#E6E6E8;
$switch-icon:#B8C9F9;

.other{
    padding: 0rem 0 1.5rem 1rem;
    min-height: 150px;

    :global(.ant-switch){
        min-width: 60px;
        height: 18px;
        background-color: $switch-disable;

        &:hover{
            background-color: $switch-disable;   
        }

        :global(.ant-switch-handle){
            width: 20px;
            height: 20px;
            top: -1px;
            &::before{
                background-color: $switch-icon;
                border-radius: 50%;
                top:0;
            }
        }
        &:global(.ant-switch.ant-switch-checked){
            background-color: $switch-icon;
            :global(.ant-switch-handle){
                &::before{
                    background-color: var(--dark-blue);
                }
            }
        }
    }
    

    .text{
        color: var(--dark-blue);
        font-weight: 400;
        word-wrap: break-word;
        width: 300px;
        font-family: 'Kanit','Courier New', Courier, monospace;
    }
    .feedback_button{
        background-color: var(--dark-blue);
        color: white;
        padding: 1rem 2rem;
        border: none;
        font-family: 'Kanit','Courier New', Courier, monospace;

        &:hover{
            background-color: var(--dark-blue) !important;
            color: white !important;
            border: none;
            box-shadow:none;
        }
    }
    .custom_input{
        width: 300px;
        caret-color: #407BFF;

        &:global(.ant-input-outlined){
            border:none;
            box-shadow: none;
            background-color: $light-blue;
            color: var(--dark-blue);
            font-weight: 400;
            font-family: 'Kanit','Courier New', Courier, monospace;
            &:focus-within{
                box-shadow:none !important;
             }
        }
    }
}

// Responsive view
@media screen and (max-width:768px) {
    .other{
        padding: 0;
        .text{
            font-size: 11px;
            width: 200px;
        }
        .custom_input{
            font-size: 10px;
        }
        .feedback_button{
            padding: 0rem 1rem;
            font-size: 11px;
        }
        
        :global(.ant-switch){
            min-width: 45px;
            height: 16px;
            :global(.ant-switch-handle){
                width: 18px;
                height: 18px;
            }
        }
    }
}
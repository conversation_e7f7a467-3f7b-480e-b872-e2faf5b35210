import { Col, Flex, Row, Typography } from "antd";
import React, { useCallback, useState } from "react";
import GIF from "../../../assets/Images/doller.gif";
import DASHBOARD_LOGO from "../../../assets/Images/dashboard_icon.png";
import { DetailsCard } from "./DetailsCard";
import HAND_IMG from "../../../assets/Images/solar_hand-money-outline-1.png";
import PROFILE_IMG from "../../../assets/Images/iconamoon_profile-fill.png";
import CONNECT_IMG from "../../../assets/Images/connect.png";
import {
  SMART_COLLECT_MENU_IDS,
  formatAmount,
  formatDigits,
} from "../../../constant";
import Style from "./_customer.module.scss";
import { Link } from "react-router";
import PropTypes from "prop-types";

const { Title, Text } = Typography;

export function CustomerDetails({ dashboardData }) {
  const [expanded, setExpanded] = useState(false);

  const handleToggleExpansion = useCallback(() => {
    setExpanded((prevState) => !prevState);
  }, []);
  return (
    <div className={Style.customer_details_container}>
      <Row justify={"space-between"} gutter={[0, 50]}>
        <Col
          xxl={12}
          xl={12}
          xs={24}
          style={{
            boxShadow: "4px 4px 8px rgba(0, 0, 0, 0.1)",
            position: "relative",
            cursor: "pointer",
            backgroundColor: " #40d0d124",
          }}
          onClick={handleToggleExpansion}
        >
          {/* Customer details */}
          <Flex vertical className={Style.customer}>
            {/* details */}
            <Flex
              justify="space-between"
              align="center"
              className={Style.gif_and_title_container}
            >
              <div className={Style.gif_container}>
                <img src={GIF} alt="gif" />
                <img
                  src={DASHBOARD_LOGO}
                  alt="dashboard Logo"
                  className={Style.dashboard_logo}
                />
              </div>
              <h2 className={Style.total_customers}>
                {dashboardData?.today_engage || 0}
              </h2>
            </Flex>
            <h3 className={Style.heading}>Total Customer Engagement</h3>
            <div className={`${Style.circle} ${Style.left}`}></div>
            <div className={`${Style.circle} ${Style.right}`}></div>
          </Flex>

          {/* Dropdown */}
          <Flex vertical className={Style.dropdown_container}>
            {/* arrow */}
            <div className={Style.arrow_container}>
              <button
                className={`${Style.arrow} ${expanded ? "down" : "up"}`}
                onClick={(event) => {
                  event.stopPropagation();
                  handleToggleExpansion();
                }}
              ></button>
            </div>

            {/* data */}
            {expanded && (
              <Row
                align={"top"}
                className={Style.list}
                justify={"space-between"}
              >
                <Col span={3}>
                  <Link to={SMART_COLLECT_MENU_IDS.WHATSAPP}>
                    <Flex vertical align="center">
                      <Title level={3} style={{ fontWeight: 500 }}>
                        {dashboardData?.whatsapp_count || 0}
                      </Title>
                      <Text>Whatsapp</Text>
                    </Flex>
                  </Link>
                </Col>

                <Col span={3}>
                  <Link to={SMART_COLLECT_MENU_IDS.AI_CALLS}>
                    <Flex vertical align="center">
                      <Title level={3} style={{ fontWeight: 500 }}>
                        {dashboardData?.aicall_count || 0}
                      </Title>
                      <Text>AI Calls</Text>
                    </Flex>
                  </Link>
                </Col>

                <Col span={3}>
                  <Link to={SMART_COLLECT_MENU_IDS.BLASTER}>
                    <Flex vertical align="center">
                      <Title level={3} style={{ fontWeight: 500 }}>
                        {dashboardData?.blaster_count || 0}
                      </Title>
                      <Text>Blaster</Text>
                    </Flex>
                  </Link>
                </Col>

                <Col span={3}>
                  <Link to={SMART_COLLECT_MENU_IDS.DIALERS}>
                    <Flex vertical align="center">
                      <Title level={3} style={{ fontWeight: 500 }}>
                        {dashboardData?.dialed || 0}
                      </Title>
                      <Text>Dialers</Text>
                    </Flex>
                  </Link>
                </Col>

                <Col span={3}>
                  <Link to={SMART_COLLECT_MENU_IDS.SMS}>
                    <Flex vertical align="center">
                      <Title level={3} style={{ fontWeight: 500 }}>
                        {dashboardData?.sms_count || 0}
                      </Title>
                      <Text>SMS</Text>
                    </Flex>
                  </Link>
                </Col>

                <Col span={3}>
                  <Link to={SMART_COLLECT_MENU_IDS.IVR_CALLS}>
                    <Flex vertical align="center">
                      <Title level={3} style={{ fontWeight: 500 }}>
                        {dashboardData?.ivr_count || 0}
                      </Title>
                      <Text>IVR Calls</Text>
                    </Flex>
                  </Link>
                </Col>

                <Col span={3}>
                  <Link to={SMART_COLLECT_MENU_IDS.EMAIL}>
                    <Flex vertical align="center">
                      <Title level={3} style={{ fontWeight: 500 }}>
                        {dashboardData?.email_count || 0}
                      </Title>
                      <Text>Email</Text>
                    </Flex>
                  </Link>
                </Col>
              </Row>
            )}
          </Flex>
        </Col>

        <Col xxl={11} xl={11} xs={24}>
          <Flex vertical gap={11}>
            <DetailsCard
              imageSrc={PROFILE_IMG}
              title={"Total Customers"}
              amount={formatDigits(dashboardData?.total_loans || 0)}
              link={SMART_COLLECT_MENU_IDS.TOTAL_CUSTOMERS}
            />
            <DetailsCard
              imageSrc={CONNECT_IMG}
              title={"Total Customers Connected"}
              amount={formatDigits(dashboardData?.customers_connected || 0)}
              link={SMART_COLLECT_MENU_IDS.TOTAL_CUSTOMERS_CONNECTED}
            />
            <DetailsCard
              imageSrc={HAND_IMG}
              title={"Total Amount Promised By Customers"}
              amount={formatAmount(dashboardData?.promised_amount || 0)}
              link={SMART_COLLECT_MENU_IDS.TOTAL_AMOUNT_PROMISED}
            />
            <DetailsCard
              imageSrc={HAND_IMG}
              title={"Total Amount Collected From Customers"}
              amount={formatAmount(dashboardData?.actual_collected || 0)}
              link={SMART_COLLECT_MENU_IDS.TOTAL_AMOUNT_COLLECTED}
            />
          </Flex>
        </Col>
      </Row>
    </div>
  );
}

CustomerDetails.propTypes = {
  dashboardData: PropTypes.object,
};

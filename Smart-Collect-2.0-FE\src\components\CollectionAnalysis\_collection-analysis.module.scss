@import url("../../index.css");

$light-gray: #d9d9d9;
$tab-height: 7px;
$tab-width: 525px; // Increased the tab width
$table-radius: 22px;
$attempts-bg: #bdd1ff;
$disable: #787777;
$body: #e4f8f9;
$light-blue: #e4f8f9;
$blue: #becfff;

.download_button {
  background-color: var(--dark-blue);
  padding: 0.2rem 1rem;
  border-radius: 10px;
  cursor: pointer;
  border: none;
  img {
    width: 20px;
  }
}

.tabs {
  margin-top: 0rem;

  &:global(.ant-tabs) {
    width: 100%;

    :global(.ant-tabs-nav-wrap) {
      padding: 0 0.9rem;
    }

    :global(.ant-tabs-nav) {
      :global(.ant-tabs-nav-list) {
        width: 100%;
        justify-content: space-between;

        :global(.ant-tabs-tab) {
          font-size: 18px;
          font-weight: 400;
          color: #97ace6;
          padding-bottom: 9px;
          margin: 0;
          width: $tab-width;
          display: flex;
          justify-content: center;
          align-items: center;
          position: relative;
          z-index: 2;
          font-family: 'Kanit','Courier New', Courier, monospace;

          &:hover {
            color: #97ace6;
          }

          &:global(.ant-tabs-tab-active) {
            :global(.ant-tabs-tab-btn) {
              color: white;
            }
          }
        }
      }

      &::before {
        border-bottom: $tab-height solid $light-gray;
        border-radius: 20px;
        margin: 0 0.9rem;
      }

      :global(.ant-tabs-ink-bar) {
        background-color: var(--dark-blue);
        height: 50px;
        border-start-start-radius: 20px;
        border-start-end-radius: 20px;
        z-index: 1;

        &::before {
          content: "";
          position: absolute;
          bottom: 0px;
          left: -15px;
          width: 34px;
          height: 50px;
          background: var(--dark-blue);
          transform: translateX(0%);
          clip-path: polygon(100% 55%, 0 100%, 100% 100%);
        }

        &::after {
          content: "";
          position: absolute;
          bottom: 0px;
          right: -15px;
          width: 34px;
          height: 50px;
          background: var(--dark-blue);
          transform: translateX(0%);
          clip-path: polygon(0 55%, 0 100%, 100% 100%);
        }
      }
    }
  }
}

.custom_table {
  padding-top: 1.5rem;

  .text {
    font-weight: 500;
    white-space: nowrap;
    color: var(--dark-blue);
  }
  .blueText {
    font-weight: 700;
    color: var(--blue);
    cursor: pointer;
  }

  //Global ant design classes

  // global css for table sorting
  :global {
    // Keep header background the same even after sorting
    .ant-table-thead .ant-table-column-sort {
      background-color: var(
        --dark-blue
      ) !important; // Change sorted header background to red
    }

    // When sorting is active, change only the header text color
    .ant-table-column-sort-up .ant-table-column-title,
    .ant-table-column-sort-down .ant-table-column-title {
      color: red !important; // Color only the sorted header text
    }

    // Ensure sort icons are white
    .ant-table-column-sorter-up svg,
    .ant-table-column-sorter-down svg {
      fill: white !important;
    }

    // Optional: Highlight active sort icon red (if desired)
    .ant-table-column-sorter-up.active svg,
    .ant-table-column-sorter-down.active svg {
      fill: rgb(24, 155, 249) !important;
    }
  }
  // Pagination
  :global(.ant-pagination) {
    justify-content: center;
    margin: 0 !important;

    :global(.ant-pagination-prev),
    :global(.ant-pagination-next) {
      color: var(--dark-blue);
      border: 0 solid var(--blue);
      background: #e4f8f9;
      height: 25px;
      min-width: 15px;
      border-radius: 0px;
      margin: 0;
      button {
        border-radius: 0px;
      }
    }
    :global(.ant-pagination-item) {
      margin-right: 0;
      height: 0;
      a {
        color: $disable;
        font-size: 0.9rem;
        line-height: 23px;
        font-weight: normal;
        font-family: 'Kanit','Courier New', Courier, monospace;
      }
      &:hover {
        background-color: transparent;
      }
    }
    :global(.ant-pagination-item-active) {
      border: none;
      a {
        color: var(--dark-blue);
        font-size: 1.2rem;
        padding: 0;
      }
    }
  }

  //Table container
  :global(.ant-table-container) {
    padding: 0 1rem;
    margin-bottom: 0.5rem;
    background: var(--light-green);
    border-start-start-radius: $table-radius;
    border-start-end-radius: $table-radius;

      // Table header
      :global(.ant-table-header) {
        position: relative;
        margin: 0 auto;
        top: -21px;
        border-radius: $table-radius;
            :global(.ant-table-column-has-sorters) {
            background-color: var(--dark-blue);
            &:hover{
                background-color: var(--dark-blue);
            }
    
            :global(.ant-table-column-sorter-up),
            :global(.ant-table-column-sorter-down) {
                svg{
                fill: white;
                }
                &:global(.active){
                svg{
                    fill: rgb(24, 155, 249)
                }
                }
            }
            }
        }

    // Table virtual body
    :global(.ant-table-tbody-virtual) {
      margin-top: -8px;
    }

    &::before {
      box-shadow: none !important;
    }
  }

  // Table rows header
  :global(.ant-table-thead > tr) {
    // First row
    &:nth-child(1) {
      // last 1 columns
      th {
        &:nth-last-child(1) {
          border-inline-start: 1px solid white;
          border-bottom: 1px solid white !important;
        }
      }
    }

    // Second row first col
    &:nth-child(2) {
      th {
        &:nth-child(1) {
          border-inline-start: 1px solid white;
        }
      }
    }
    th {
      border-start-end-radius: 0 !important;
      border-inline-end: none !important;
      background-color: var(--dark-blue);
      color: white;
      border-bottom: none;
      text-align: center;
      font-family: 'Kanit','Courier New', Courier, monospace;
      font-weight: normal;
      padding: 10px;
      &:not(:last-child):not(.ant-table-selection-column):not(
          .ant-table-row-expand-icon-cell
        ):not([colspan])::before {
        width: 0;
      }
    }
  }

  // Table body
  :global(.ant-table-tbody) {
    // Body rows
    :global(.ant-table-row) {
      // Cols
      :global(.ant-table-cell) {
        text-align: center;
        border-bottom: 2px solid white;
        border-inline-end: 0px;
        font-family: 'Kanit','Courier New', Courier, monospace;
        font-weight: normal;
        padding: 10px;

        :global(.ant-typography) {
          font-weight: 400;
          font-family: 'Kanit','Courier New', Courier, monospace;
          cursor: pointer;
        }

        // first column
        &:nth-last-child(7) {
          border-inline-start: 2px solid white;
        }

        &:global(.ant-table-cell-row-hover) {
          background-color: transparent;
        }
      }
    }
  }
}

.refresh_btn {
  border-color: var(--dark-blue);
  color: var(--dark-blue);
  outline: none;
  box-shadow: none;
  &:hover {
    border-color: var(--dark-blue) !important;
    color: var(--dark-blue) !important;
  }
}

.dropdownContainer {
  display: flex;
  justify-content: flex-end; // Align dropdown to the right
  padding-bottom: 0px;
  margin-right: 4px;
  gap: 10px;

  .dropdown {
    width: 130px;

    :global(.ant-select-selector) {
      background-color: $light-blue !important;
      border: none !important;
      display: flex; // Ensure the arrow is aligned properly
      justify-content: space-between;
      align-items: center;
      padding: 8px;
      box-shadow: 3px 3px 5px rgba(0, 0, 0, 0.3);
    }

    :global(.ant-select-selection-item) {
      font-weight: 400;
      color: var(--dark-blue);
      font-family: 'Kanit','Courier New', Courier, monospace;
    }

    :global(.ant-select-arrow) {
      color: #000; // Make arrow black
      font-weight: bold; // Make arrow bold
    }
  }

  :global(.ant-select-dropdown) {
    margin-top: 1.2rem;
    background-color: $light-blue !important;

    :global(.ant-select-item-option) {
      color: var(--dark-blue);
      font-weight: 600;

      &:global(.ant-select-item-option-disabled) {
        color: rgba(0, 0, 0, 0.56);
      }
    }

    :global(.ant-select-item-option-active) {
      &:not(.ant-select-item-option-disabled) {
        background-color: $blue;
        color: var(--dark-blue);
        font-weight: 400;
      }
    }
  }
}

// Responsive view
@media screen and (max-width: 768px) {
  .tabs {
    &:global(.ant-tabs) {
      :global(.ant-tabs-nav) {
        :global(.ant-tabs-nav-list) {
          :global(.ant-tabs-tab) {
            font-size: 12px;
          }
        }

        :global(.ant-tabs-ink-bar) {
          height: 35px;

          &::before {
            left: -9px;
          }

          &::after {
            right: -9px;
          }
        }
      }
    }
  }

  .download_button{
    border-radius: 4px;
    img{
      width: 15px;
    }
  }  
  .refresh_btn{
    font-size: 12px;
    height: auto;
    padding: 0.24rem .5rem;
  }
  .custom_table {
    // Table rows header
    :global(.ant-table-thead > tr) {
      th {
        font-size: 12px;
        padding: 0.5rem;
      }
    }
    :global(.ant-table-tbody) {
      :global(.ant-table-row) {
        :global(.ant-table-cell) {
          padding: 0.6rem;
          font-size: 12px !important;
          :global(.ant-typography){
            font-size: 12px !important;
          }
        }
      }
    }

    .icon_div {
      .icon_img {
        width: 13px;
        height: 13px;
      }
    }
  }

  .dropdownContainer{
    align-items: center;
    margin-top: 1rem;
    .dropdown {  
      height: 28px;
      :global(.ant-select-arrow) {
        font-size: 10px;
      }
    }
  }
}

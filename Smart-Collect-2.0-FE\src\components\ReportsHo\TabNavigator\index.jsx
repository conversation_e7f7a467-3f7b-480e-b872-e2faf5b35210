import { Divider, Flex, Typography } from 'antd';
import React, { useState } from 'react';
import HAMBURGER_MENU_IMG from "../../../assets/Images/hamburger_menu.svg";
import CROSS_IMG_BLUE from "../../../assets/Images/blue_cross.svg";
import REPORTS_GRAPH_IMG from "../../../assets/Images/reports_graph.svg";
import Style from "./_style.module.scss";
import PropTypes from 'prop-types';
const { Text } = Typography;

export default function TabNavigator({ options = [],activeTab, handleTabChange }) {
  const [visible, setVisible] = useState(false);

  const handleOptionClick = (item) => { 
    handleTabChange(item.key); 
  };

  return (
    <div className={Style.container}>
      <Flex justify='start' vertical>
        <Flex className={Style.button_container} justify='center'>
          <div className={Style.img_container}>
            <input type='image' src={HAMBURGER_MENU_IMG} alt="hamburger_menu" onClick={() => setVisible(true)} />
          </div>
        </Flex>
      </Flex>

      {visible && (
        <Flex className={Style.modal} vertical>
          <Flex justify='space-between'>
            <Flex  gap={3} justify='center' style={{ flex: 1 }}>
              <img src={REPORTS_GRAPH_IMG} alt="reports graph"/> 
              <Text className={Style.text}>Reports</Text>
            </Flex>
            <input
              type='image'
              src={CROSS_IMG_BLUE}
              alt='cross'
              style={{ cursor: "pointer" }}
              onClick={() => setVisible(false)}
            />
          </Flex>

          <Divider style={{ backgroundColor: "#AEC4F6", margin: "0.1rem 0" }} />

          <Flex gap={15} vertical>
            <div className={Style.custom_dropdown}>
              {options.map((item) => (
                <button
                  key={item.key}
                  className={`${Style.dropdown_item} ${
                    activeTab === item.key ? Style.selected : ""
                  }`}
                  onClick={() => handleOptionClick(item)}
                >
                  {item.label}
                </button>
              ))}
            </div>
          </Flex>
        </Flex>
      )}
    </div>
  );
}

TabNavigator.propTypes={
   options: PropTypes.array,
   activeTab: PropTypes.string, 
   handleTabChange: PropTypes.func 
}
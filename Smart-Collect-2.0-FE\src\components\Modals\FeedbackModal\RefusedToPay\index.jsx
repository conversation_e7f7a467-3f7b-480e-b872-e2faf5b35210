import React, { useState } from "react";
import Style from "./_refused.module.scss";
import { Button, Flex, Input, Select, Typography, message} from "antd";
import { REFUSED_TO_PAY_OPTIONS_DATA } from "../../../../constant";
import { AXIOS } from "../../../../apis/ho-Instance";
import PropTypes from "prop-types";

const { Text } = Typography;

export function RefusedToPay({ loanmstid, branchMstId, handleCross }) {
  const [loading, setLoading] = useState(false);

  const [selectedReason, setSelectedReason] = useState(null);
  const [customReason, setCustomReason] = useState("");

  const handleReasonChange = (value) => {
    setSelectedReason(value);
  };
  const handleCustomReason = (value) => {
    setCustomReason(value);
  };

  const handlePostRefusedData = async () => {
    setLoading(true);

    const Reason_of_refusal =
      selectedReason === "custom" ? customReason : selectedReason;

    const requestData = {
      BranchMstID: branchMstId,
      LoanMstID: loanmstid,
      type: "RefusedToPay",
      Reason_of_refusal: Reason_of_refusal,
    };

    try {
      const response = await AXIOS.post("feedback/", requestData);
      console.log("Promise Data Submitted:", response.data);

      // Show success message
      message.success("Promise created successfully!");

      // Close the modal after 2 seconds
      setTimeout(() => {
        handleCross();
        window.location.reload();
      }, 500);
    } catch (error) {
      console.log("Error in Ready to Pay:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={Style.refused_to_pay}>
      <Flex vertical gap={20} justify="center">
        {/* reason */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Reason for Customer Denial:</Text>
          <Select
            className={Style.refused_select}
            defaultValue="Select"
            onChange={handleReasonChange}
            options={REFUSED_TO_PAY_OPTIONS_DATA}
          />
        </Flex>

        {/* custom */}
        {selectedReason === "custom" && (
          <Flex justify="space-between" align="center">
            <Text className={Style.text}>Description for Denial:</Text>
            <Input
              onChange={(e) => handleCustomReason(e.target.value)}
              placeholder="Enter"
              className={Style.custom_input}
            />
          </Flex>
        )}

        <Flex justify="center">
          <Button
            loading={loading}
            className={Style.feedback_button}
            onClick={handlePostRefusedData}
          >
            Create Denial
          </Button>
        </Flex>
      </Flex>
    </div>
  );
}

RefusedToPay.propTypes={
  loanmstid: PropTypes.oneOfType([PropTypes.string, PropTypes.number]), 
  branchMstId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]), 
  handleCross: PropTypes.func
}
import React from 'react';
import Chart from 'react-apexcharts';
import { formatAmount, formatDigits } from '../../../../constant';
import PropTypes from 'prop-types';

export function ScrubGraph({data, isAccount, setSelectedState, handleUpdateStateTable}) {
   // Extract state names for x-axis
   const categories = data.map(item => item.State);

   const riskCategories = [
    { key: "Very Low Risk", name: "Very Low" },
    { key: "Low Risk", name: "Low" },
    { key: "Medium Risk", name: "Medium" },
    { key: "High Risk", name: "High" },
    { key: "Very High Risk", name: "Very High" },
  ];
  // Build series dynamically
  const series = riskCategories.map(risk => ({
    name: risk.name,
    data: data.map(item => item[risk.key] || 0),
  }));

  const options = {
    chart: {
      type: 'bar',
      stacked: false,
      toolbar: { show: false },
      events: {
        dataPointSelection: function(event, chartContext, config) {
          const dataPointIndex = config.dataPointIndex;
          const selectedState = data[dataPointIndex];
          const selectedStateId = selectedState?.State_id;
      
          if (selectedStateId) {
            setSelectedState(selectedStateId);
            handleUpdateStateTable({ stateId: selectedStateId });
          }
        }
      }      
    },
    plotOptions: {
          bar: {
            columnWidth:'40%', // Wider bar if only 1 state
            distributed: false, // Set to true if you want different colors for each bar
            horizontal: false,
          },
        },
        colors: ['#a7b9cf', '#a4c8f0', '#679de6', '#2e74cc', '#0047ab', '#ffd700'],
        dataLabels: {
          enabled: true,
          position: 'top',
          style: {
            colors: ['#000'], // Optional: label color
          },
          formatter: function (val) {
            return isAccount ? formatDigits(val) : `₹${formatAmount(val)}`;
          },
        },
        xaxis: {
          categories:categories,
          title: {
            text: 'State',
          },
        },
        legend: {
          position: 'top',
          horizontalAlign: 'right',
        },
  };
       
  return (
    <div>
      <Chart options={options} series={series} type="bar" height={350} />
    </div>
  );
}
ScrubGraph.propTypes={
  data: PropTypes.array,
  isAccount: PropTypes.bool,
  setSelectedState: PropTypes.func,
  handleUpdateStateTable:PropTypes.func
}
import { Button, Flex, message, Select, Table, Typography } from "antd";
import React, { useEffect, useState } from "react";
import AppHeader from "../AppHeader";
import Style from "./_style.module.scss";
import {
  ALL_CACHE_NAMES,
  formatAmount,
  handleDownloadTable,
  SMART_COLLECT_MENU_IDS,
} from "../../constant";
import HISTORY_IMG from "../../assets/Images/history_icon.png";
import FEEDBACK_IMG from "../../assets/Images/fluent-mdl2_feedback.svg";
import PHONE_IMG from "../../assets/Images/call_icon.png";
import { AXIOS } from "../../apis/ho-Instance";
import ApplicationLoader from "../ApplicationLoader";
import { ConversionHistory } from "../Modals/ConversionHistory";
import { FeedbackModal } from "../Modals/FeedbackModal";
import { DialerModal } from "../Modals/DialerModal";
import { LogsCustomDialerModal } from "../Modals/LogsCustomDialerModal";
import DOWNLOAD_IMG from "../../assets/Images/download.svg";
import { CustomerDetailsCard } from "../CustomerAllCards/CustomerDetailsCard";
import CustomerProfileModal from "../Modals/CustomerProfileModal";
import {
  getFromCache,
  getPostCacheKey,
  storeToCache,
} from "../../utils/cacheHelper";

const { Text } = Typography;
const CACHE_NAME = ALL_CACHE_NAMES.ACTIVITY_CACHE;

export default function ActivityLogs() {
  const PAGE_ID_VALUE = {
    Promise: SMART_COLLECT_MENU_IDS.READY_TO_PAY,
    Denails: SMART_COLLECT_MENU_IDS.REFUSED_TO_PAY,
    AlreadyPaid: SMART_COLLECT_MENU_IDS.ALREADY_PAY,
    WrongNumber: SMART_COLLECT_MENU_IDS.WRONG_NUMBER,
    NonResponsive: SMART_COLLECT_MENU_IDS.BMI_ALLOCATION,
    NonContactable: SMART_COLLECT_MENU_IDS.NON_CONTACTABLE,
  };

  const [data, setData] = useState([]);
  const [selectCategory, setSelectCategory] = useState("Promise");
  const [isLoading, setIsLoading] = useState(false);
  const [isDialerLoading, setIsDialerLoading] = useState(false);
  const [modalState, setModalState] = useState({
    history: { isOpen: false, data: null },
    feedback: { isOpen: false, data: null },
    dialer: { isOpen: false, data: null },
    customer: { isOpen: false, data: null },
  });
  const [isCustomDialer, setIsCustomDialer] = useState(false);

  // Handle logs fetching
  const handleFetchLogsData = async (category = "Promise") => {
    setIsLoading(true);
    const body = { category };
    const cacheKey = getPostCacheKey({
      endpoint: `${category}-response/`,
      body,
    });
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setData(cachedData);
      setIsLoading(false);
    }
    try {
      const response = await AXIOS.post("categorywiseresponse/", body);
      if (response.status === 200) {
        setData(response.data);
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
      } else {
        setData([]);
      }
    } catch (error) {
      console.log("Error in Fetching logs in", category, error?.message);
      setData([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDialerSubmit = async ({
    number,
    loanmstid,
    overdueAmount,
    branchMstId,
  }) => {
    setIsDialerLoading(true);
    message.success("Initiate call");

    // Close Dialer & Open Feedback Modal with Correct Data
    setModalState((prev) => ({
      ...prev,
      dialer: { isOpen: false, data: null }, // Close Dialer Modal
      feedback: {
        isOpen: true,
        data: {
          loanmstid,
          overdue_amount: overdueAmount,
          branchMstId,
        }, // Open Feedback Modal with Data
      },
    }));
    try {
      const res = await AXIOS.post("v1/dialer/", {
        loanMstId: loanmstid,
        bm_phone: number,
      });
      console.log(res, "Dialer Response");
    } catch (error) {
      console.error("Error Logs  dialer", error?.message);
    } finally {
      setIsDialerLoading(false);
    }
  };

  // Handle Open Modal
  const openModal = ({ type, data }) => {
    setModalState((prev) => ({
      ...prev,
      [type]: { isOpen: true, data },
    }));
  };

  // Handle Close Modal
  const closeModal = (type) => {
    setModalState((prev) => ({
      ...prev,
      [type]: { isOpen: false, data: null },
    }));
  };

  useEffect(() => {
    handleFetchLogsData(selectCategory);
  }, [selectCategory]);

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      sorter: (a, b) => a.key - b.key,
      rowScope: "row",
      width: 90,

      render: (_, data) => <Text>{data?.key}.</Text>,
    },
    {
      title: "Customer Name",
      dataIndex: "customername",
      sorter: (a, b) => a.customername?.localeCompare(b.customername),
      render: (value, data) => (
        <Text
          className={Style.blueText}
          style={{ cursor: "pointer" }}
          onClick={() => openModal({ type: "customer", data })}
        >
          {value || "--"}
        </Text>
      ),
    },
    {
      title: "Customer Id",
      dataIndex: "custmstid",
      sorter: (a, b) => String(a.custmstid).localeCompare(String(b.custmstid)),
      render: (value) => <Text className={Style.text}> {value || "--"} </Text>,
    },
    {
      title: "Disbursement Id",
      dataIndex: "disbursementid",
      sorter: (a, b) => a.disbursementid?.localeCompare(b.disbursementid),
      render: (value) => <Text>{value}</Text>,
    },
    {
      title: "Loan Type",
      dataIndex: "loantype",
      sorter: (a, b) => a.loantype?.localeCompare(b.loantype),
      render: (value) => <Text className={Style.text}>{value || "--"}</Text>,
    },
    {
      title: "Overdue Amount",
      dataIndex: "overdue_amount",
      sorter: (a, b) => a.overdue_amount - b.overdue_amount,
      render: (value) => (
        <Text className={Style.text}>Rs. {formatAmount(value || 0)}</Text>
      ),
    },
    {
      title: "Dialer",
      dataIndex: "dialer",
      width: 70,
      render: (_, data) => (
        <div className={Style.icon_div}>
          <div className={Style.icon_img}>
            <input
              type="image"
              src={PHONE_IMG}
              alt="dialer"
              onClick={() => openModal({ type: "dialer", data })}
            />
          </div>
        </div>
      ),
    },
    {
      title: "History",
      dataIndex: "history",
      width: 80,
      render: (_, data) => (
        <div className={Style.icon_div}>
          <div className={Style.icon_img}>
            <input
              type="image"
              src={HISTORY_IMG}
              alt="history"
              onClick={() => openModal({ type: "history", data })}
            />
          </div>
        </div>
      ),
    },
    {
      title: "Feedback",
      dataIndex: "feedback",
      width: 100,
      render: (_, data) => (
        <div className={Style.icon_div}>
          <div className={Style.icon_img}>
            <input
              type="image"
              src={FEEDBACK_IMG}
              alt="feedback"
              onClick={() => openModal({ type: "feedback", data })}
            />
          </div>
        </div>
      ),
    },
  ];

  const dataSource = Array.isArray(data)
    ? data.map((data, i) => ({
        key: i + 1,
        ...data,
      }))
    : [];

  // Handle downloading
  const handleDownload = async () => {
    if (data.length === 0) {
      return message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: "Activity Logs",
        worksheetName: "activity-logs",
        tableData: data,
      });
    }
  };

  return (
    <Flex vertical gap={10} className={Style.activity}>
      <AppHeader title={"Activity"} />
      <Flex vertical gap={15}>
        <Flex
          align="center"
          justify="space-between"
          gap={10}
          className={Style.select_button_container}
        >
          <Flex gap={10} align="center" className={Style.select_container}>
            <Text className={Style.title}>Select Responsive Category:</Text>
            <Select
              className={Style.custom_select}
              options={[
                { label: "Promised to pay", value: "Promise" },
                { label: "Refused to pay", value: "Denails" },
                { label: "Already Paid", value: "AlreadyPaid" },
                { label: "Wrong Number", value: "WrongNumber" },
                { label: "Non-Responsive", value: "NonResponsive" },
                { label: "Non-Contactable ", value: "NonContactable " },
              ]}
              value={selectCategory}
              onChange={setSelectCategory}
              placeholder={"Select category"}
            />
          </Flex>

          <Flex gap="1" className={Style.button_container}>
            <Button
              className={Style.btn}
              onClick={() => setIsCustomDialer(true)}
            >
              Custom Dialer
            </Button>

            <button className={Style.download_button} onClick={handleDownload}>
              <img src={DOWNLOAD_IMG} alt="download-button" />
            </button>
          </Flex>
        </Flex>

        {/* Table */}
        {isLoading ? (
          <ApplicationLoader />
        ) : (
          <Table
            bordered
            virtual
            className={Style.CustomTable}
            columns={columns}
            dataSource={dataSource}
            scroll={{
              x: 1000,
              y: 400,
            }}
            pagination={{
              showSizeChanger: false,
            }}
          />
        )}
        <ConversionHistory
          customerName={modalState.history.data?.customername}
          loanMstId={modalState.history.data?.loanmstid}
          modalStatus={modalState.history.isOpen}
          handleCancel={() => closeModal("history")}
        />

        <FeedbackModal
          pageId={null}
          overdueAmount={modalState.feedback.data?.overdue_amount}
          branchMstId={modalState.feedback.data?.branchmstid}
          loanmstid={modalState.feedback.data?.loanmstid}
          modalStatus={modalState.feedback.isOpen}
          handleSubmit={() => closeModal("feedback")}
          handleCancel={() => closeModal("feedback")}
          componentProp={
            <CustomerDetailsCard
              loanmstid={modalState.feedback.data?.loanmstid}
              branchMstId={modalState.feedback.data?.branchMstId}
              communicationCount={modalState.feedback.data?.communication_count}
              last5CommunicationTypes={
                modalState.feedback.data?.last_5_communication_types
              }
              status={modalState.feedback.data?.status}
              name={modalState.feedback.data?.customername}
              customerId={modalState.feedback.data?.customerid}
              disbursementId={modalState.feedback.data?.disbursementid}
              branchName={modalState.feedback.data?.branchname}
              loanType={modalState.feedback.data?.loantype}
              overdueAmount={modalState.feedback.data?.overdue_amount}
              promiseAmount={modalState.feedback.data?.promise_amount}
              promiseDate={modalState.feedback.data?.promise_date}
              responseDate={modalState.feedback.data?.responsedatetime}
              feedbackDate={modalState.feedback.data?.feedbackdate}
              dpdAmount={modalState.feedback.data?.dpd}
              modalButtonText={"Feedback"}
              isModal={true}
              pageId={PAGE_ID_VALUE[selectCategory]}
              firstTimeDefaulter={false}
              isFeedbackClick={true}
            />
          }
        />

        <DialerModal
          branchMstId={modalState.feedback.data?.branchmstid}
          customerName={modalState.dialer.data?.customername}
          overdueAmount={modalState.dialer.data?.overdue_amount}
          loanmstid={modalState.dialer.data?.loanmstid}
          isLoading={isDialerLoading}
          modalStatus={modalState.dialer.isOpen}
          handleSubmit={handleDialerSubmit}
          handleCancel={() => closeModal("dialer")}
        />

        <LogsCustomDialerModal
          modalStatus={isCustomDialer}
          setModalStatus={setIsCustomDialer}
        />

        <CustomerProfileModal
          modalStatus={modalState.customer.isOpen}
          name={modalState.customer.data?.customername}
          customerId={modalState?.customer.data?.customerid}
          disbursementId={modalState?.customer.data?.disbursementid}
          branchName={modalState?.customer.data?.branchname}
          loanType={modalState.customer.data?.loantype}
          dpdAmount={modalState.customer.data?.dpd}
          overdueAmount={modalState.customer.data?.overdue_amount}
          promiseAmount={modalState.customer.data?.promise_amount}
          loanmstid={modalState.customer.data?.loanmstid}
          branchMstId={modalState.customer.data?.branchmstid}
          pageId={PAGE_ID_VALUE[selectCategory]}
          handleClose={() => closeModal("customer")}
        />
      </Flex>
    </Flex>
  );
}

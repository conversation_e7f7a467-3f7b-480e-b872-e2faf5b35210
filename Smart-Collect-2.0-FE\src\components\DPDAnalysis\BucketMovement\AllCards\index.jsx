import React from 'react';
import ICON_IMG from "../../../../assets/Images/i.png";
import Style from "./_card.module.scss";
import { Col, Flex, Row, Typography } from 'antd';
import TOTAL_CUSTOMER_IMG from "../../../../assets/Images/customer.png";
import PENDING_IMG from "../../../../assets/Images/pending.png";
import COMPLETED_IMG from "../../../../assets/Images/complete.png";
import PropTypes from 'prop-types';

const {Title}  = Typography;
export default function AllCards({cardData}) {
  return (
    <Row gutter={[50,30]} align={'stretch'} justify={"space-between"}>
        <Col span={8} style={{alignSelf:"stretch"}}>
            <Flex className={Style.card_container} vertical gap={20}>
                <Flex justify='space-between' align='center'>
                    <Title level={3} className={Style.title}>Same Bucket</Title>
                    <div className={Style.icon_img}>
                        <img src={ICON_IMG} alt='icon'/>
                    </div>
                </Flex>

                <Flex gap={3} justify='space-between' align='center'>
                    <Title level={3} className={Style.title}>{cardData?.same_bucket}</Title>
                    <div className={Style.image_container}>
                        <img src={TOTAL_CUSTOMER_IMG} alt={"Same Bucket"}/>
                    </div>       
                </Flex>
            </Flex>
        </Col>
        <Col span={8} style={{alignSelf:"stretch"}}>
            <Flex className={Style.card_container} vertical gap={20}>
                <Flex justify='space-between' align='center'>
                    <Title level={3} className={Style.title}>Movement to Lower Bucket</Title>
                    <div className={Style.icon_img}>
                        <img src={ICON_IMG} alt='icon'/>
                    </div>
                </Flex>

                <Flex gap={3} justify='space-between' align='center'>
                    <Title level={3} className={Style.title}>{cardData?.upper_bucket}</Title>
                    <div className={Style.image_container}>
                        <img src={PENDING_IMG} alt={"Movement to Lower Bucket"}/>
                    </div>       
                </Flex>
            </Flex>
        </Col>
        <Col span={8} style={{alignSelf:"stretch"}}>
            <Flex className={Style.card_container} vertical gap={20}>
                <Flex justify='space-between' align='center'>
                    <Title level={3} className={Style.title}>Movement to Upper Bucket</Title>
                    <div className={Style.icon_img}>
                        <img src={ICON_IMG} alt='icon'/>
                    </div>
                </Flex>

                <Flex gap={3} justify='space-between' align='center'>
                    <Title level={3} className={Style.title}>{cardData?.lower_bucket}</Title>
                    <div className={Style.image_container}>
                        <img src={COMPLETED_IMG} alt={"Movement to Upper Bucket"}/>
                    </div>       
                </Flex>
            </Flex>
        </Col>
   </Row>
  )
}

AllCards.propTypes={
    cardData: PropTypes.object
}
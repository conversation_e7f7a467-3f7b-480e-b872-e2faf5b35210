@import url("../../../../../index.css");

.chat_container{
    max-width: 600px;
    margin: auto;
    width: 100%;

    .chat_message {
        .chat_bubble{
            border-radius: 8px;
            font-family: 'Kanit','Courier New', Courier, monospace;
            font-weight: 400;
            font-size: 11px;
            width: 85%;

            :global(.ant-card-body){
                padding: 8px 10px;
                p{
                    margin: 0;
                }
            }
        }

        &.bot {
            justify-content: flex-end;
            .chat_bubble{
                background: var(--dark-blue);
                color: white;
                &::after{
                    content: '';
                    position: absolute;
                    top: 0px;
                    right: -18px;
                    transform: translateX(-50%);
                    border-width: 10px;
                    border-style: solid;
                    border-color: var(--dark-blue) transparent transparent transparent;
                    border-radius: 0 0 10px 10px;
                }
            }
        }
          
        &.user {
            justify-content: flex-start;
            .chat_bubble{
                background: #C2D9FF;
                color: var(--dark-blue);
                min-width: 300px;
                max-width: 300px;

                &::before{
                    content: '';
                    position: absolute;
                    top: 0px;
                    left: 1px;
                    transform: translateX(-50%);
                    border-width: 10px;
                    border-style: solid;
                    border-color: #C2D9FF transparent transparent transparent;
                    border-radius: 0 0 10px 10px;
                }
            }
        }

    }   
}

// Responsive View
@media only screen and (max-width:768px) {
    .chat_container{
        .chat_message{
            .chat_bubble{
                font-size: 8px;
            }
        }
    }
}
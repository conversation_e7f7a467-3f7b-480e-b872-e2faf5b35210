import { Flex, Space, Typography } from "antd"
import Style from "./_style.module.scss";
import PropTypes from "prop-types";

const { Text } = Typography;

export default function CommunicationChannel({recentEngagementAppsData}) {
  return (
    <Flex className={Style.recent} justify="space-between">
        {recentEngagementAppsData?.map((app, index)=>{
            return <Flex key={`${index}-${app.count}`} vertical align="center">
                    <Text className={Style.value}>{app.count ?? 0}</Text>
                    <Space>
                      <div className={Style.icon_img}>{app?.imgSrc &&<img src={app?.imgSrc} alt="app"/>}</div>
                      <Text className={Style.channel}>{app.name ?? ""}</Text>
                    </Space>
                </Flex>
        })
        } 
  </Flex>
  )
}
CommunicationChannel.propTypes={
  recentEngagementAppsData: PropTypes.array,
}
import React, { useCallback, useState } from 'react'
import { Flex, Segmented, Table,Typography } from 'antd';
import Style from "./_table.module.scss";
import PropTypes from 'prop-types';

const {Text, Title} = Typography;

export function BucketTable({data}) {
  const [tableType, setTableType] = useState("Account");
  // Mapping for tableType -> data keys
  const tableMap = {
    Account: data?.dpd_account_table || [],
    Overdue: data?.dpd_overdue_table || [],
    POS: data?.dpd_pos_table || []
  };
  const tableData = tableMap[tableType];
  
  // Get all column keys except "prevdpdbucket"
  const dynamicKeys = tableData.length
  ? Object.keys(tableData[0]).filter(key => key !== 'prevdpdbucket')
  : [];

  // Table columns
  const columns = [
    {
      title: 'Previous month',
      dataIndex: 'prevdpdbucket',
      render:(_, row)=> <Text className={Style.key}>{row?.key}</Text>
    },
    {
      title: 'Current Month',
      dataIndex: 'currentMonth',
      children:[
      ...dynamicKeys.map((key) => ({
        title: key,
        dataIndex: key,
        render: (value) => <Text>{typeof value === 'number' ? value.toFixed(2) : value}</Text>
      }))]
    }
  ];
    
  const dataSource = Array.isArray(tableData)? 
   tableData.filter(item => item.prevdpdbucket !== 'Total').map((item) => ({
    key: item.prevdpdbucket,
    ...item,
    })):[];
  
  // Find the "Total" row separately for the summary
  const totalRow = tableData.find(item => item.prevdpdbucket === 'Total');

  const summaryContent = useCallback(() => {
    return <TableSummaryRow totalRow={totalRow} dynamicKeys={dynamicKeys} />;
  }, [totalRow, dynamicKeys]);
  return (
    <Flex vertical gap={10}>
      <Flex justify='space-between' align='center' className={Style.title_container}>
        <Title level={4} className={Style.title}>{`${tableType} wise DPD Bucket Movement`}</Title>
        <Segmented
          value={tableType}
          style={{ marginBottom: 8 }}
          onChange={(value) => setTableType(value)}
          options={["Account", "Overdue", "POS"]}
        />
      </Flex>
      <Table
        // virtual
        bordered
          className={Style.customTable}
          columns={columns}
          dataSource={dataSource}
          pagination={false}
          summary={summaryContent}/>
    </Flex>
  )
}
const TableSummaryRow = ({ totalRow, dynamicKeys }) => {
  if (!totalRow) return null;

  return (
    <Table.Summary.Row>
      <Table.Summary.Cell index={0}>
        <strong>Total</strong>
      </Table.Summary.Cell>

      {dynamicKeys.map((key, index) => (
        <Table.Summary.Cell key={index + 1} index={index + 1}>
          <strong>
            {typeof totalRow[key] === "number"
              ? totalRow[key].toFixed(2)
              : totalRow[key]}
          </strong>
        </Table.Summary.Cell>
      ))}
    </Table.Summary.Row>
  );
};

TableSummaryRow.propTypes={
  totalRow: PropTypes.object,
  dynamicKeys: PropTypes.array,
}
BucketTable.propTypes={
  data: PropTypes.object
}


import React from 'react'
import Style from "../_style.module.scss";
import { Flex, Typography } from 'antd';
import DELETE_ICON from "../../../assets/Images/delete-2.svg";
import ARROW_ICON from "../../../assets/Images/arrow.svg";
import PropTypes from 'prop-types';

const {Text} = Typography;

export function HistoryChat({data, handleRestoreConversation, handleDeleteConversion}) {
  return (
    <Flex vertical className={Style.history_details} onClick={handleRestoreConversation.bind(null,data.uuid)}>
        {/* title */}
        <Flex justify='space-between'>
            <Text style={{overflowWrap:"break-word", inlineSize:"200px"}}> 
                <span 
                style={{
                fontSize:"1.5rem", 
                lineHeight:0,
                marginInlineEnd:'10px'
                }}>&#x2022;</span>
             {data?.title || "Untitled Chat"}
            </Text>
            <div className={Style.delete_icon}>
                <input type='image' alt="delete" src={DELETE_ICON} onClick={(e)=>{
                    e.stopPropagation();
                    handleDeleteConversion(data.uuid)
                }}/>
            </div>
        </Flex>

        {/* desc */}
        <Flex gap={10}>
            <div style={{width:'10px'}}>
                <img src={ARROW_ICON} alt='arrow' style={{width:"100%", objectFit:"contain"}}/>
            </div>
            <Text className={Style.history_message}>{data?.campaign_name || "No description"}</Text>
        </Flex>
   </Flex>
  )
}

HistoryChat.propTypes={
    data: PropTypes.object, 
    handleRestoreConversation: PropTypes.func, 
    handleDeleteConversion: PropTypes.func
}
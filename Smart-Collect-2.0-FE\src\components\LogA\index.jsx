import React from 'react';
import { Flex, Tabs} from 'antd';
import AppHeader from '../AppHeader';
import Style from './_allocation-analysis.module.scss';
import { FeedbackLogTable } from './FeedbackLog';
import { DialerLogTable } from './DialerLog';

export default function LogA() {
  const items = [
    {
      key: 'feedback-logs',
      label: 'Feedback Log',
      children: <FeedbackLogTable/>,
    },
    {
      key: 'dialer-log',
      label: 'Dialer Log',
      children: <DialerLogTable/>,
    },
  ];
  return (
    <Flex vertical gap={20} className={Style.container}>
      <AppHeader title={"Logs"} />
      <Tabs defaultActiveKey="feedback-logs" items={items} className={Style.tabs}/>
    </Flex>
  );
}

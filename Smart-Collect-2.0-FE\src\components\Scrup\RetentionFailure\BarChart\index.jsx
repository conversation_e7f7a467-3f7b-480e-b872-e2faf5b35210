import PropTypes from 'prop-types';
import React from 'react'
import ReactApex<PERSON>hart from 'react-apexcharts';

export function Bar<PERSON><PERSON>({data,selectedRetentionCard, onBarClick}) {
  const states = data.map(item => item.State);
  const houseLoanCounts = data.map(item => item.house_loan_count);
  const goldLoanCounts = data.map(item => item.gold_loan_count);

  const options = {
    chart: {
      type: 'bar',
      toolbar: { show: false },
      events: {
        dataPointSelection: (event, chartContext, config) => {
          const index = config.dataPointIndex;
          const selectedState = data[index];
          if (onBarClick) onBarClick({selectedState, selectedRetentionCard});
        }
      },
    },
    xaxis: {
      categories: states,
      title: { text: 'State' },
      labels: {
        rotate: -45,
        style: {
          fontSize: '12px'
        }
      }
    },
    yaxis: {
      title: { text: 'Total Accounts' }
    },
    plotOptions: {
      bar: {
        borderRadius: 0,
        columnWidth: '40px',
        distributed: false
      }
    },
    dataLabels: {
      enabled: true,
      style: {
        fontSize: '12px',
        colors: ['#000000'] 
      }
    },
    colors: ['#0033A0', '#FFB200'], 
    responsive: [{
      breakpoint: 768, // Tablets and smaller
      options: {
        chart: {
          height: 250
        },
        plotOptions: {
          bar: {
            columnWidth: '50%'
          }
        },
        xaxis: {
          labels: {
          rotate: -30,
          style: {
            fontSize: '10px'
            }
          }
        },
        dataLabels: {
          style: {
            fontSize: '12px'
          }
        }
      }
    }
    ]
  };
  
  const series = [
    { name: 'House Loan', data: houseLoanCounts},
    { name: 'Gold Loan', data: goldLoanCounts}
  ];
  
  return <ReactApexChart options={options} series={series} type="bar" height={300} />;
}
BarChart.propTypes={
  data: PropTypes.array,
  selectedRetentionCard: PropTypes.any,
  onBarClick: PropTypes.func
}
import { Button, Flex, Table, Typography } from "antd";
import React, { useEffect } from "react";
import Style from "./_style.module.scss";
import PropTypes from "prop-types";
import SimilarTable from "./Similartable";

const { Text } = Typography;

export default function NumberTable({ data, handleSearchBySimilar }) {
  const [tableData, setTableData] = React.useState([]);
  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      render: () => <Text>1</Text>,
    },
    {
      title: "Name",
      dataIndex: "name",
      render: (value) => <Text>{value !== null ? value : "-"}</Text>,
    },
    {
      title: "Phone",
      dataIndex: "phone",
      render: (value) => <Text>{value ?? "-"}</Text>,
    },
    {
      title: "Address",
      dataIndex: "address",
      render: (value) => <Text>{value !== null ? value : "-"}</Text>,
    },
    {
      title: "Alternate Phone",
      dataIndex: "alternate_phone",
      render: (value) => <Text>{value ?? "--"}</Text>,
    },
    {
      title: "State",
      dataIndex: "Folder_Name",
      render: (value) => <Text>{value ?? "-"}</Text>,
    },
    {
      title: "Pincode",
      dataIndex: "pincode",
      render: (value) => <Text>{value ?? "N/A"}</Text>,
    },
  ];
  const dataSource = Array.isArray(tableData)
    ? tableData.map((data, i) => ({ key: i + 1, ...data }))
    : [];

  // Set te data
  useEffect(() => {
    setTableData(data);
  }, [data]);

  return (
    <Flex vertical gap={15} className={Style.table_container}>
      <Flex justify="end" align="center">
        <Button
          type="primary"
          className={Style.button}
          onClick={handleSearchBySimilar}
        >
          Search for similar address and sirname
        </Button>
      </Flex>
      {/* Table */}
      <Table
        bordered
        virtual
        className={Style.customTable}
        columns={columns}
        dataSource={dataSource}
        scroll={{
          x: 1000,
          y: 220,
        }}
        pagination={{
          pageSize: 10,
          showSizeChanger: false,
        }}
      />
    </Flex>
  );
}

NumberTable.propTypes = {
  data: PropTypes.array,
  handleSearchBySimilar: PropTypes.func.isRequired,
};

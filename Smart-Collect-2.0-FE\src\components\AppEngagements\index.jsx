import { Flex, Typography } from "antd";
import React from "react";
import Style from "./_app-engagements.module.scss";
import { APP_ENGAGEMENTS_DATA } from "../../constant";

const { Text } = Typography;

export default function AppEngagements() {
  return (
    <Flex justify="end" style={{ marginTop: "0.5rem" }}>
      <Flex className={Style.app_engagements_container}>
        {APP_ENGAGEMENTS_DATA?.map((data, index) => {
          return (
            <Flex
              key={`${index}-${data.name}`}
              align="center"
              className={Style.box}
            >
              <div className={Style.icon_img}>
                <img src={data?.imgSrc} alt={data.name} />
              </div>
              <Text className={Style.title}>{data?.name}</Text>
            </Flex>
          );
        })}
      </Flex>
    </Flex>
  );
}

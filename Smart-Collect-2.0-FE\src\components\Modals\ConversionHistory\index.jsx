import React, { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Spin, Typography } from "antd";
import ICON_IMG from "../../../assets/Images/Vector (4).svg";
import Style from "./_style.module.scss";
import { WhatsApp } from "./WhatsApp";
import { AI as Ai } from "./AI";
import { Blaster } from "./Blaster";
import { AXIOS } from "../../../apis/ho-Instance";
import { Dialer } from "./Dialer";
import { BM as Bm } from "./BM";
import { IVR as Ivr } from "./IVR";
import PropTypes from "prop-types";

const { Title, Text } = Typography;

export function ConversionHistory({
  customerName,
  loanMstId,
  modalStatus = false,
  handleCancel,
}) {
  const aiRef = useRef(null);
  const dialerRef = useRef(null);
  const blasterRef = useRef(null);
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [historyData, setHistoryData] = useState({});

  // Group messages by conversation_date
  const handleGroupedData = (data) => {
    if (!Array.isArray(data)) {
    console.error("Invalid data passed to handleGroupedData");
    return {};
  }
  const grouped = {};

    // Loop over the data and group by date and conversation_type
    data.forEach((item) => {
      const date = item.conversation_date; // This is the date key
      const type = item.conversation_type; // This is the conversation type key

      if (!date || !type) return; // skip invalid records

      // Initialize the date if not already present
      if (!grouped[date]) {
        grouped[date] = {};
      }

      // Initialize the type if not already present for this date
      if (!grouped[date][type]) {
        grouped[date][type] = [];
      }

      // Push the current item to the appropriate date and conversation type
      grouped[date][type].push(item);
    });
    return grouped;
  };

  // Fetch history data
  const handleGetHistoryData = async () => {
    setIsLoading(true);
    try {
      const response = await AXIOS.get(`/communicationhistory/`, {
        params: { loanMstID: loanMstId }, // Query parameter
      });

      const data = response.data.data || [];
      const groupedData = handleGroupedData(data);
      setHistoryData(groupedData);

      /* OLD CODE: Filter Data after setting state
        setWhatsappData(
          data.filter((item) => item.conversation_type === "WhatsApp")
        );
        setIVRData(
          data.filter((item) => item.conversation_type === "IVR")
        );
        setAiCallData(
          data.filter((item) => item.conversation_type === "AI Call")
        );
        setBlasterData(
          data.filter((item) => item.conversation_type === "Blaster")
        );
        setDialerData(
          data.filter((item) => item.conversation_type === "Dialer")
        );
        setBmData(
          data.filter((item) => item.conversation_type === "BM")
        );
      */
    } catch (error) {
      console.error("Error fetching history data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle cancel the modal
  const handleModalCancel = ()=>{
    if (aiRef.current) {
      aiRef.current.stopAllAudio(); // Stop all audio when modal closes
    }
    if (dialerRef.current) {
      dialerRef.current.stopAllAudio(); // Stop all audio when modal closes
    }
    if (blasterRef.current) {
      blasterRef.current.stopAllAudio(); // Stop all audio when modal closes
    }
    handleCancel();
  }

  // Handle modal status
  useEffect(() => {
    setIsHistoryModalOpen(modalStatus);
    if (modalStatus) {
      handleGetHistoryData();
    }
  }, [modalStatus]);

  /* OLD CODE:
   const isEmpty =
    !whatsappData.length && 
    !ivrData.length && 
    !aiCallData.length && 
    !blasterData.length && 
    !dialerData.length &&
    !bmData.length;
  */

  return (
    <Modal
      centered
      className={Style.history_modal}
      title={
        <Flex align="center" gap={5} justify="center">
          <div className={Style.icon_img}>
            <img src={ICON_IMG} alt="icon" />
          </div>
          <Title level={4} className={Style.history_title}>
            {customerName}
          </Title>
        </Flex>
      }
      open={isHistoryModalOpen}
      onCancel={handleModalCancel}
      footer={null}
    >
      <Spin spinning={isLoading} tip="Loading...">
        <Flex vertical gap={30}>
          {Object.keys(historyData).length === 0 ? (
            <Text
              className={Style.no_data}
              type="secondary"
              style={{ textAlign: "center" }}
            >
              No Data Available
            </Text>
          ) : (
            <>
              {/* ***OLD CODE*** 
                <WhatsApp whatsappData={whatsappData} />
                <IVR ivrData={ivrData} />
                <AI aiCallData={aiCallData} ref={aiRef}/>
                <Blaster blasterData={blasterData} />
                <BM bmData={bmData} />
                <Dialer dialerCallData={dialerData} ref={dialerRef}/> 
             */}

              {Object.entries(historyData).map(([date, types]) => (
                <div key={date}>
                  <Flex vertical gap={15}>
                    {Object.entries(types).map(([type, items]) => {
                      switch (type) {
                        case "WhatsApp":
                          return <WhatsApp key={type} whatsappData={items} />;
                        case "AI Call":
                          return <Ai key={type} aiCallData={items} ref={aiRef}/>;
                        case "IVR":
                          return <Ivr key={type} ivrData={items} />;
                        case "Blaster":
                          return <Blaster key={type} ref={blasterRef} blasterCallData={items} />;
                        case "Dialer":
                          return <Dialer key={type} dialerCallData={items} ref={dialerRef}/>;
                        case "BM":
                          return <Bm key={type} bmData={items} />;
                        default:
                          return null;
                      }
                    })}
                  </Flex>
                </div>
              ))}

            </>
          )}
        </Flex>
      </Spin>
    </Modal>
  );
}

ConversionHistory.propTypes={
  customerName: PropTypes.string,
  loanMstId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  modalStatus: PropTypes.bool,
  handleCancel: PropTypes.func,
}
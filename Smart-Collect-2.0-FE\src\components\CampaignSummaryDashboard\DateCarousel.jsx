import React, { useEffect, useState } from "react";
import <PERSON>lide<PERSON> from "react-slick";
import { Flex, message, Typography } from "antd";
import { handleDateFormatter, SUMMARY_FILTERS } from "../../constant";
import CROSS_IMG from "../../assets/Images/cross-white.svg";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import Style from "./_style.module.scss";
import { useTour } from "../../ContextApis/TourProvider";
import dayjs from "dayjs";
import PropTypes from "prop-types";

const { Title, Text } = Typography;

export function DateCarousel({
  engagementData,
  handleDisplaySelectedData,
  handleGetDashboardData,
  selectedDate,
  setSelectedDate,
  loading,
}) {
  const { dateWiseCustomerEngagementRef } = useTour();
  const [data, setData] = useState([]);

  const handleRemoveDates = async () => {
    if (loading)
      return message.warning("Please wait, data is still loading... ");
    // Remove the dates if api is not pending
    setSelectedDate({ fromDate: null, toDate: null });
    localStorage.removeItem(SUMMARY_FILTERS.summaryFilter);
    await handleGetDashboardData();
  };

  useEffect(() => {
    setData(engagementData);
  }, [engagementData]);

  const settings = {
    dots: false,
    infinite: false,
    speed: 500,
    slidesToShow: 7,
    slidesToScroll: 2,
    arrows: true,
    initialSlide: data?.length > 7 ? data.length - 7 : 0,
    adaptiveHeight: true,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 3,
        },
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 2,
        },
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 2,
        },
      },
      {
        breakpoint: 280,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
    ],
  };

  return (
    <div className={Style.timeline_container}>
      {/* <Text className={Style.title}>Datewise Customers Engagement:</Text> */}

      <div
        className={Style.slider_container}
        ref={dateWiseCustomerEngagementRef}
      >
        <Slider {...settings}>
          {data?.length
            ? data?.map((d, index) => {
                const formattedSelectedDate = selectedDate
                  ? dayjs(selectedDate.fromDate).format("YYYY-MM-DD")
                  : null;

                const isSelected =
                  formattedSelectedDate === d?.CreatedDate &&
                  d?.total_engaged !== 0;
                const isLast = index === data?.length - 1; // Check if it's the last item

                // Get day of the week: 0 (Sun) - 6 (Sat)
                const dayOfWeek = dayjs(d?.CreatedDate).day();
                const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
                const isHoliday = isWeekend && d?.total_engaged === 0;
                return (
                  <Flex
                    vertical
                    key={`${index}-${d.total_engaged}`}
                    className={`${Style.d_card} ${
                      isLast ? Style.last_card : ""
                    }`}
                    gap={0}
                    onClick={() => {
                      if (loading)
                        return message.warning(
                          "Please wait, data is still loading... "
                        );
                      // Set the dates & call the api
                      setSelectedDate({
                        fromDate: d?.CreatedDate,
                        toDate: d?.CreatedDate,
                      });
                      handleDisplaySelectedData({
                        date: d?.CreatedDate,
                        value: d?.total_engaged,
                      });
                    }}
                  >
                    <Flex
                      vertical
                      className={[
                        Style.card,
                        isSelected && Style.selected,
                        isHoliday && Style.holiday_card,
                      ]}
                    >
                      {isSelected && (
                        <Flex
                          justify="end"
                          className={Style.cross_icon}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRemoveDates();
                          }}
                        >
                          <img
                            src={CROSS_IMG}
                            alt="Cross icon"
                            style={{ cursor: "pointer", width: "13px" }}
                          />
                        </Flex>
                      )}
                      {isHoliday && (
                        <Text className={Style.ribbon}>Holiday</Text>
                      )}
                      <Text
                        className={`${Style.value} ${
                          isSelected ? Style.selectedText : ""
                        }`}
                      >
                        {d?.total_engaged}
                      </Text>
                      <Text
                        style={{ fontWeight: 400 }}
                        className={`${Style.date} ${
                          isSelected ? Style.selectedText : ""
                        }`}
                      >
                        {handleDateFormatter(d?.CreatedDate)}
                      </Text>
                    </Flex>
                  </Flex>
                );
              })
            : null}
        </Slider>
      </div>
    </div>
  );
}

DateCarousel.propTypes = {
  engagementData: PropTypes.array,
  handleDisplaySelectedData: PropTypes.func,
  handleGetDashboardData: PropTypes.func,
  selectedDate: PropTypes.object,
  setSelectedDate: PropTypes.func,
  loading: PropTypes.bool,
};

{"cells": [{"cell_type": "code", "execution_count": 1, "id": "73b0058c", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n"]}, {"cell_type": "code", "execution_count": 2, "id": "8dd35fc8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Defaulting to user installation because normal site-packages is not writeable\n", "Requirement already satisfied: sqlalchemy in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (2.0.38)\n", "Requirement already satisfied: greenlet!=0.4.17 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from sqlalchemy) (3.1.1)\n", "Requirement already satisfied: typing-extensions>=4.6.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from sqlalchemy) (4.12.2)\n", "Note: you may need to restart the kernel to use updated packages.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "[notice] A new release of pip is available: 25.1.1 -> 25.2\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}], "source": ["pip install sqlalchemy"]}, {"cell_type": "code", "execution_count": 3, "id": "e13a4b74", "metadata": {}, "outputs": [], "source": ["from sqlalchemy import create_engine"]}, {"cell_type": "code", "execution_count": 4, "id": "ccaad423", "metadata": {}, "outputs": [], "source": ["engine = create_engine('***************************************************************')"]}, {"cell_type": "code", "execution_count": 5, "id": "7adfa5ef", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv('Summary 1.csv')"]}, {"cell_type": "code", "execution_count": 6, "id": "82648f2d", "metadata": {}, "outputs": [{"data": {"text/plain": ["20"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["total_records = len(df)\n", "total_records"]}, {"cell_type": "code", "execution_count": 7, "id": "fe9f6132", "metadata": {}, "outputs": [], "source": ["# Drop column with blank name\n", "df = df.loc[:, df.columns != '']\n"]}, {"cell_type": "code", "execution_count": 8, "id": "bc471afa", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['CREDT-RPT-ID', ' LOS-APP-ID', ' CUSTOMER ID/MBR ID', ' STATUS',\n", "       ' ERROR', ' PERFOR<PERSON>_CNS SCORE', ' PERFORM_CNS SCORE-DESCRIPTION',\n", "       ' PRI-NO-OF-ACCTS', ' PRI-ACTIVE-ACCTS', ' PRI-OVERDUE-ACCTS',\n", "       ' PRI-CURRENT-BALANCE', ' PRI-SANCTIONED-AMOUNT',\n", "       ' PRI-DISBURSED-AMOUNT', ' SEC-NO-OF-ACCTS', ' SEC-ACTIVE-ACCTS',\n", "       ' SEC-OVERDUE-ACCTS', ' SEC-CURRENT-BALANCE', ' SEC-SANCTIONED-AMOUNT',\n", "       ' SEC-DISBURSED-AMOUNT', ' PRIMARY-INSTAL-AMT', ' SEC-INSTAL-AMT',\n", "       ' NEW-ACCTS-IN-LAST-SIX-MONTHS', ' DELINQUENT-ACCTS-IN-LAST-SIX-MONTHS',\n", "       ' AVERAGE-ACCT-AGE', ' CREDIT-HISTORY-LENGTH', ' NO-OF_INQUIRIES', ' '],\n", "      dtype='object')"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": null, "id": "555af352", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 9, "id": "ef0c0ca2", "metadata": {}, "outputs": [], "source": ["def clean_column(col):\n", "        return col.strip().lower().replace('-', '_').replace(' ', '_').replace('/', '_')\n", "df.columns = [clean_column(col) for col in df.columns]"]}, {"cell_type": "code", "execution_count": 10, "id": "8bfa47f9", "metadata": {}, "outputs": [], "source": ["def check_columns_summary(df) :\n", "    df = df.loc[:, ~df.columns.str.contains('^Unnamed')]\n", "    def clean_column(col):\n", "        return col.strip().lower().replace('-', '_').replace(' ', '_').replace('/', '_')\n", "    df.columns = [clean_column(col) for col in df.columns]\n", "    required_columns = ['credt_rpt_id', 'los_app_id', 'customer_id_mbr_id', 'status', 'error',\n", "       'perform_cns_score', 'perform_cns_score_description', 'pri_no_of_accts',\n", "       'pri_active_accts', 'pri_overdue_accts', 'pri_current_balance',\n", "       'pri_sanctioned_amount', 'pri_disbursed_amount', 'sec_no_of_accts',\n", "       'sec_active_accts', 'sec_overdue_accts', 'sec_current_balance',\n", "       'sec_sanctioned_amount', 'sec_disbursed_amount', 'primary_instal_amt',\n", "       'sec_instal_amt', 'new_accts_in_last_six_months',\n", "       'delinquent_accts_in_last_six_months', 'average_acct_age',\n", "       'credit_history_length', 'no_of_inquiries']\n", "    missing_columns = []\n", "    for column in required_columns:\n", "        if column not in df.columns:\n", "            missing_columns.append(column)\n", "    return missing_columns, df"]}, {"cell_type": "code", "execution_count": 11, "id": "ad3c46bf", "metadata": {}, "outputs": [], "source": [" numeric_columns = ['perform_cns_score', 'pri_active_accts', 'pri_overdue_accts', 'pri_current_balance',\n", "                            'pri_sanctioned_amount', 'pri_disbursed_amount','sec_no_of_accts',\n", "                            'sec_active_accts', 'sec_overdue_accts', 'sec_current_balance',\n", "                            'sec_sanctioned_amount', 'sec_disbursed_amount', 'primary_instal_amt',\n", "                            'sec_instal_amt', 'new_accts_in_last_six_months',\n", "                            'delinquent_accts_in_last_six_months', 'no_of_inquiries','BankMstID']"]}, {"cell_type": "code", "execution_count": 12, "id": "9aa3e18d", "metadata": {}, "outputs": [], "source": ["def clean_numeric(value):\n", "    if pd.isna(value) or value is None or str(value).lower() == \"nan\":\n", "        return None\n", "    try:\n", "        return float(\n", "            value.replace(\",\", \"\") if isinstance(value, str) else value\n", "        )\n", "    except (ValueError, TypeError) as e:\n", "        print(f\"Invalid numeric value: {value}, error: {str(e)}\")\n", "        return None\n"]}, {"cell_type": "code", "execution_count": 13, "id": "0a844a4b", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "for col in numeric_columns:\n", "    if col in df.columns:\n", "        df[col] = df[col].apply(clean_numeric)\n", "df.replace(np.nan, None, inplace=True)"]}, {"cell_type": "code", "execution_count": 14, "id": "9b0d3195", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['credt_rpt_id', 'los_app_id', 'customer_id_mbr_id', 'status', 'error', 'perform_cns_score', 'perform_cns_score_description', 'pri_no_of_accts', 'pri_active_accts', 'pri_overdue_accts', 'pri_current_balance', 'pri_sanctioned_amount', 'pri_disbursed_amount', 'sec_no_of_accts', 'sec_active_accts', 'sec_overdue_accts', 'sec_current_balance', 'sec_sanctioned_amount', 'sec_disbursed_amount', 'primary_instal_amt', 'sec_instal_amt', 'new_accts_in_last_six_months', 'delinquent_accts_in_last_six_months', 'average_acct_age', 'credit_history_length', 'no_of_inquiries', '']\n"]}], "source": ["print(df.columns.tolist())\n", "df.columns = df.columns.str.lower().str.replace('-', '_').str.strip()\n"]}, {"cell_type": "code", "execution_count": 15, "id": "dcd3ff90", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['credt_rpt_id', 'los_app_id', 'customer_id_mbr_id', 'status', 'error',\n", "       'perform_cns_score', 'perform_cns_score_description', 'pri_no_of_accts',\n", "       'pri_active_accts', 'pri_overdue_accts', 'pri_current_balance',\n", "       'pri_sanctioned_amount', 'pri_disbursed_amount', 'sec_no_of_accts',\n", "       'sec_active_accts', 'sec_overdue_accts', 'sec_current_balance',\n", "       'sec_sanctioned_amount', 'sec_disbursed_amount', 'primary_instal_amt',\n", "       'sec_instal_amt', 'new_accts_in_last_six_months',\n", "       'delinquent_accts_in_last_six_months', 'average_acct_age',\n", "       'credit_history_length', 'no_of_inquiries', ''],\n", "      dtype='object')"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": 16, "id": "1522aa91", "metadata": {}, "outputs": [], "source": ["# Drop column with blank name\n", "df = df.loc[:, df.columns != '']\n"]}, {"cell_type": "code", "execution_count": 17, "id": "149106e6", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['credt_rpt_id', 'los_app_id', 'customer_id_mbr_id', 'status', 'error',\n", "       'perform_cns_score', 'perform_cns_score_description', 'pri_no_of_accts',\n", "       'pri_active_accts', 'pri_overdue_accts', 'pri_current_balance',\n", "       'pri_sanctioned_amount', 'pri_disbursed_amount', 'sec_no_of_accts',\n", "       'sec_active_accts', 'sec_overdue_accts', 'sec_current_balance',\n", "       'sec_sanctioned_amount', 'sec_disbursed_amount', 'primary_instal_amt',\n", "       'sec_instal_amt', 'new_accts_in_last_six_months',\n", "       'delinquent_accts_in_last_six_months', 'average_acct_age',\n", "       'credit_history_length', 'no_of_inquiries'],\n", "      dtype='object')"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": 18, "id": "196ee111", "metadata": {}, "outputs": [], "source": ["# def read_file(file, extension):\n", "#     if extension == '.csv':\n", "#         return pd.read_csv(file, delimiter='|')  # <- IMPORTANT\n", "#     elif extension == '.xlsx':\n", "#         return pd.read_excel(file)\n"]}, {"cell_type": "code", "execution_count": 19, "id": "45ae42dd", "metadata": {}, "outputs": [], "source": ["# df['BankMstID'] = 9"]}, {"cell_type": "code", "execution_count": 20, "id": "93235e52", "metadata": {}, "outputs": [], "source": ["query = \"select * from summary\""]}, {"cell_type": "code", "execution_count": 21, "id": "4569f5ae", "metadata": {}, "outputs": [], "source": ["from sqlalchemy import text"]}, {"cell_type": "code", "execution_count": 22, "id": "75747e70", "metadata": {}, "outputs": [], "source": ["df_db = pd.read_sql(text(query), engine)"]}, {"cell_type": "code", "execution_count": 23, "id": "4b0dd943", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['credt_rpt_id', 'los_app_id', 'customer_id_mbr_id', 'status', 'error',\n", "       'perform_cns_score', 'perform_cns_score_description', 'pri_no_of_accts',\n", "       'pri_active_accts', 'pri_overdue_accts', 'pri_current_balance',\n", "       'pri_sanctioned_amount', 'pri_disbursed_amount', 'sec_no_of_accts',\n", "       'sec_active_accts', 'sec_overdue_accts', 'sec_current_balance',\n", "       'sec_sanctioned_amount', 'sec_disbursed_amount', 'primary_instal_amt',\n", "       'sec_instal_amt', 'new_accts_in_last_six_months',\n", "       'delinquent_accts_in_last_six_months', 'average_acct_age',\n", "       'credit_history_length', 'no_of_inquiries', 'BankMstID'],\n", "      dtype='object')"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["df_db.columns"]}, {"cell_type": "code", "execution_count": 24, "id": "790ec34b", "metadata": {}, "outputs": [], "source": ["df.columns = df.columns.str.replace('|', ',', regex=False)\n"]}, {"cell_type": "code", "execution_count": 25, "id": "cac53918", "metadata": {}, "outputs": [], "source": ["expected_columns = [\n", "    \"credt_rpt_id\",\n", "    \"los_app_id\",\n", "    \"customer_id_mbr_id\",\n", "    \"status\",\n", "    \"error\",\n", "    \"perform_cns_score\",\n", "    \"perform_cns_score_description\",\n", "    \"pri_no_of_accts\",\n", "    \"pri_active_accts\",\n", "    \"pri_overdue_accts\",\n", "    \"pri_current_balance\",\n", "    \"pri_sanctioned_amount\",\n", "    \"pri_disbursed_amount\",\n", "    \"sec_no_of_accts\",\n", "    \"sec_active_accts\",\n", "    \"sec_overdue_accts\",\n", "    \"sec_current_balance\",\n", "    \"sec_sanctioned_amount\",\n", "    \"sec_disbursed_amount\",\n", "    \"primary_instal_amt\",\n", "    \"sec_instal_amt\",\n", "    \"new_accts_in_last_six_months\",\n", "    \"delinquent_accts_in_last_six_months\",\n", "    \"average_acct_age\",\n", "    \"credit_history_length\",\n", "    \"no_of_inquiries\",\n", "    \"bankmstid\"\n", "]\n"]}, {"cell_type": "code", "execution_count": 26, "id": "23393ccf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['credt_rpt_id', 'los_app_id', 'customer_id_mbr_id', 'status', 'error', 'perform_cns_score', 'perform_cns_score_description', 'pri_no_of_accts', 'pri_active_accts', 'pri_overdue_accts', 'pri_current_balance', 'pri_sanctioned_amount', 'pri_disbursed_amount', 'sec_no_of_accts', 'sec_active_accts', 'sec_overdue_accts', 'sec_current_balance', 'sec_sanctioned_amount', 'sec_disbursed_amount', 'primary_instal_amt', 'sec_instal_amt', 'new_accts_in_last_six_months', 'delinquent_accts_in_last_six_months', 'average_acct_age', 'credit_history_length', 'no_of_inquiries']\n"]}], "source": ["print(df.columns.tolist())\n"]}, {"cell_type": "code", "execution_count": 27, "id": "42494dee", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['credt_rpt_id', 'los_app_id', 'customer_id_mbr_id', 'status', 'error',\n", "       'perform_cns_score', 'perform_cns_score_description', 'pri_no_of_accts',\n", "       'pri_active_accts', 'pri_overdue_accts', 'pri_current_balance',\n", "       'pri_sanctioned_amount', 'pri_disbursed_amount', 'sec_no_of_accts',\n", "       'sec_active_accts', 'sec_overdue_accts', 'sec_current_balance',\n", "       'sec_sanctioned_amount', 'sec_disbursed_amount', 'primary_instal_amt',\n", "       'sec_instal_amt', 'new_accts_in_last_six_months',\n", "       'delinquent_accts_in_last_six_months', 'average_acct_age',\n", "       'credit_history_length', 'no_of_inquiries'],\n", "      dtype='object')"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": 28, "id": "0e080ee1", "metadata": {}, "outputs": [], "source": ["# insert_query = f\"\"\"\n", "#             INSERT INTO summary (\n", "#                 {', '.join(expected_columns)}\n", "#             ) VALUES ({', '.join(['?' for _ in expected_columns])})\n", "#         \"\"\""]}, {"cell_type": "code", "execution_count": 29, "id": "553d2973", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['credt_rpt_id', 'los_app_id', 'customer_id_mbr_id', 'status', 'error',\n", "       'perform_cns_score', 'perform_cns_score_description', 'pri_no_of_accts',\n", "       'pri_active_accts', 'pri_overdue_accts', 'pri_current_balance',\n", "       'pri_sanctioned_amount', 'pri_disbursed_amount', 'sec_no_of_accts',\n", "       'sec_active_accts', 'sec_overdue_accts', 'sec_current_balance',\n", "       'sec_sanctioned_amount', 'sec_disbursed_amount', 'primary_instal_amt',\n", "       'sec_instal_amt', 'new_accts_in_last_six_months',\n", "       'delinquent_accts_in_last_six_months', 'average_acct_age',\n", "       'credit_history_length', 'no_of_inquiries'],\n", "      dtype='object')"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": 30, "id": "a6246bab", "metadata": {}, "outputs": [], "source": ["query = \"select * from summary\" "]}, {"cell_type": "code", "execution_count": 31, "id": "14b1debf", "metadata": {}, "outputs": [], "source": ["df_summary = pd.read_sql(query, engine)"]}, {"cell_type": "code", "execution_count": 32, "id": "faa14ebf", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['credt_rpt_id', 'los_app_id', 'customer_id_mbr_id', 'status', 'error',\n", "       'perform_cns_score', 'perform_cns_score_description', 'pri_no_of_accts',\n", "       'pri_active_accts', 'pri_overdue_accts', 'pri_current_balance',\n", "       'pri_sanctioned_amount', 'pri_disbursed_amount', 'sec_no_of_accts',\n", "       'sec_active_accts', 'sec_overdue_accts', 'sec_current_balance',\n", "       'sec_sanctioned_amount', 'sec_disbursed_amount', 'primary_instal_amt',\n", "       'sec_instal_amt', 'new_accts_in_last_six_months',\n", "       'delinquent_accts_in_last_six_months', 'average_acct_age',\n", "       'credit_history_length', 'no_of_inquiries', 'BankMstID'],\n", "      dtype='object')"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["df_summary.columns"]}, {"cell_type": "code", "execution_count": 33, "id": "3ebdb174", "metadata": {}, "outputs": [], "source": ["df.columns = [\n", "    f\"column_{i}\" if not col or str(col).strip() == \"\" else col\n", "    for i, col in enumerate(df.columns)\n", "]\n"]}, {"cell_type": "code", "execution_count": 34, "id": "7c278045", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['credt_rpt_id', 'los_app_id', 'customer_id_mbr_id', 'status', 'error',\n", "       'perform_cns_score', 'perform_cns_score_description', 'pri_no_of_accts',\n", "       'pri_active_accts', 'pri_overdue_accts', 'pri_current_balance',\n", "       'pri_sanctioned_amount', 'pri_disbursed_amount', 'sec_no_of_accts',\n", "       'sec_active_accts', 'sec_overdue_accts', 'sec_current_balance',\n", "       'sec_sanctioned_amount', 'sec_disbursed_amount', 'primary_instal_amt',\n", "       'sec_instal_amt', 'new_accts_in_last_six_months',\n", "       'delinquent_accts_in_last_six_months', 'average_acct_age',\n", "       'credit_history_length', 'no_of_inquiries'],\n", "      dtype='object')"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": 35, "id": "22fdf027", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_14368\\3617632999.py:2: FutureWarning: DataFrame.applymap has been deprecated. Use DataFrame.map instead.\n", "  df = df.applymap(lambda x: str(x).strip().strip('\"') if isinstance(x, str) else x)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_14368\\3617632999.py:3: FutureWarning: Downcasting behavior in `replace` is deprecated and will be removed in a future version. To retain the old behavior, explicitly call `result.infer_objects(copy=False)`. To opt-in to the future behavior, set `pd.set_option('future.no_silent_downcasting', True)`\n", "  df = df.replace(r'^\\s*$', np.nan, regex=True)\n"]}, {"data": {"text/plain": ["20"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["# Clean columns\n", "df = df.applymap(lambda x: str(x).strip().strip('\"') if isinstance(x, str) else x)\n", "df = df.replace(r'^\\s*$', np.nan, regex=True)\n", "df = df.infer_objects(copy=False)\n", "\n", "# Ensure column names match SQL table exactly (case-sensitive!)\n", "df.columns = [col.strip() for col in df.columns]  # remove stray whitespaces\n", "# Optionally fix a specific column\n", "df.rename(columns={\"bankmstid\": \"BankMstID\"}, inplace=True)\n", "\n", "# Drop unnecessary column\n", "df.drop(columns=[\"column_26\"], errors=\"ignore\", inplace=True)\n", "\n", "# Insert into PostgreSQL\n", "df.to_sql(\n", "    'summary',\n", "    engine,\n", "    if_exists='append',\n", "    index=False,\n", "    method='multi'  # improves performance\n", ")\n"]}, {"cell_type": "code", "execution_count": 36, "id": "5719e7a0", "metadata": {}, "outputs": [], "source": ["# df.to_sql(\n", "#     'summary',\n", "#     engine,\n", "#     if_exists='append',\n", "#     index=False,\n", "#     method='multi'\n", "# )\n"]}, {"cell_type": "code", "execution_count": 37, "id": "7ff811a3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Defaulting to user installation because normal site-packages is not writeable\n", "Requirement already satisfied: pandas in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (2.2.3)\n", "Requirement already satisfied: sqlalchemy in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (2.0.38)\n", "Collecting pymysql\n", "  Downloading PyMySQL-1.1.1-py3-none-any.whl.metadata (4.4 kB)\n", "Requirement already satisfied: numpy>=1.26.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from pandas) (1.26.4)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from pandas) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from pandas) (2025.1)\n", "Requirement already satisfied: tzdata>=2022.7 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from pandas) (2025.1)\n", "Requirement already satisfied: greenlet!=0.4.17 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from sqlalchemy) (3.1.1)\n", "Requirement already satisfied: typing-extensions>=4.6.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from sqlalchemy) (4.12.2)\n", "Requirement already satisfied: six>=1.5 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from python-dateutil>=2.8.2->pandas) (1.17.0)\n", "Downloading PyMySQL-1.1.1-py3-none-any.whl (44 kB)\n", "Installing collected packages: pymysql\n", "Successfully installed pymysql-1.1.1\n", "Note: you may need to restart the kernel to use updated packages.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "[notice] A new release of pip is available: 25.1.1 -> 25.2\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}], "source": ["pip install pandas sqlalchemy pymysql\n"]}, {"cell_type": "code", "execution_count": 38, "id": "6949a5c0", "metadata": {}, "outputs": [], "source": ["# from sqlalchemy import create_engine, text\n", "\n", "# # Ensure unique_keys is defined\n", "# unique_keys = df[[\"credt_rpt_id\"]].drop_duplicates()\n", "\n", "# # Setup engine\n", "# engine = create_engine('***************************************************************')\n", "\n", "# if not unique_keys.empty:\n", "#     with engine.begin() as connection:\n", "#         connection.execute(text(\"DROP TABLE IF EXISTS temp_keys\"))\n", "#         connection.execute(text(\"\"\"\n", "#             CREATE TEMP TABLE temp_keys (\n", "#                 credt_rpt_id VARCHAR(50)\n", "#             ) ON COMMIT DROP\n", "#         \"\"\"))\n", "\n", "#         insert_temp_query = text(\"\"\"\n", "#             INSERT INTO temp_keys (credt_rpt_id)\n", "#             VALUES (:credt_rpt_id)\n", "#         \"\"\")\n", "#         connection.execute(\n", "#             insert_temp_query,\n", "#             [{\"credt_rpt_id\": row[\"credt_rpt_id\"]} for _, row in unique_keys.iterrows()]\n", "#         )\n", "\n", "#         delete_query = text(\"\"\"\n", "#             DELETE FROM summary\n", "#             USING temp_keys\n", "#             WHERE summary.credt_rpt_id = temp_keys.credt_rpt_id\n", "#         \"\"\")\n", "#         result = connection.execute(delete_query)\n", "\n", "#         print(f\"✅ Deleted {result.rowcount} existing records from summary.\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.6"}}, "nbformat": 4, "nbformat_minor": 5}
@import url("../../../../index.css");

$light-blue: #E3F5F6;

.card_container{
    background-color: $light-blue;
    border-top: 6.32px solid var(--dark-blue);
    border-radius: 10px;
    padding:0 1rem;
    margin-top: 1rem;
    opacity: 0.5;
    cursor: pointer;

    .image_container{
        width: 120px;
        height: 100px;
        img{
            object-fit: contain;
            width: 100%;
        }
    }
    .title{
        margin: 0;
        color: #113c9bc4;
        font-weight: 400;
        font-size: 1rem;
        font-family: 'Kanit','Courier New', Courier, monospace;

    }
    .desc{
        color: #0F20508A;
        font-weight: 600;
        font-size: 16px;
        margin: 0;
    }

    .icon_img{
        width: 15px;
        height: 15px;
        cursor: pointer;
        img{
            object-fit: contain;
            width: 100%;
        }
    }
    &.active_card,
    &:hover{
        background-color: #d9f3f5;
        opacity: 1;
        transform: scale(1.03);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.321);
        transition: all 0.3s ease;

        .title{
            color: var(--dark-blue);
        }
    }
}

// Responsive view
@media only screen and (max-width:768px){
.card_container{
    padding:0 0.5rem;
    .title{
        font-size: 14px;
    }
    
    .image_container{
        width: 80px;
        height: 68px;
    }
    
    .desc{
        color: #0F20508A;
        font-weight: 600;
        font-size: 16px;
        margin: 0;
    }

    .icon_img{
        width: 15px;
        height: 15px;
        cursor: pointer;
        img{
            object-fit: contain;
            width: 100%;
        }
    }
}
}
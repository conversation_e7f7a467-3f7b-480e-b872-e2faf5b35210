import CryptoJS from "crypto-js";

const ENCRYPTION_SECRET = import.meta.env.VITE_SAMRT_COLLECT_CACHE_CRYPTO_KEY;

export function encryptBody(body) {
    const stringBody = JSON.stringify(body);
    return CryptoJS.AES.encrypt(stringBody, ENCRYPTION_SECRET).toString();
}

export function decryptBody(encrypted) {
    try {
        const bytes = CryptoJS.AES.decrypt(encrypted, ENCRYPTION_SECRET);
        const decrypted = bytes.toString(CryptoJS.enc.Utf8);
        return JSON.parse(decrypted);
    } catch (e) {
        console.error("Decryption failed:", e);
        return null;
    }
}

export function isSameEncryptedBody({ newBody, encryptedOldBody }) {
    const decryptedOld = decryptBody(encryptedOldBody);
    return decryptedOld && JSON.stringify(decryptedOld) === JSON.stringify(newBody)
}
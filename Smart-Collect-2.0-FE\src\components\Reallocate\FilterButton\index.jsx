import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Flex,
  Input,
  Typography,
  message,
} from "antd";
import CROSS_IMG from "../../../assets/Images/cross.svg";
import FILTER_IMG from "../../../assets/Images/solar_filter-broken.svg";
import ACTIVE_FILTER_IMG from "../../../assets/Images/activefilter.svg";
import Style from "./_filter-button.module.scss";
import { AXIOS } from "../../../apis/ho-Instance";
import PropTypes from "prop-types";
const { Text } = Typography;

export default function FilterButton({
  visible,
  setVisible,
  setFilterData,
  originalData,
}) {  
  const userDetails = JSON.parse(localStorage.getItem("user"));
  const [loading, setLoading] = useState(false);
  const [filterActive, setFilterActive] = useState(false);
  const [filterKeys, setFilterKeys] = useState({
    branch:'',
    collectionOfficerID:'',
    disbursementID:'',
    collectionOfficerName:'',
    customerID:'',
    customerName:'',
    loanType:'',
  })

  // Handle change filter keys
  const handleFilterKeysChange = ({type, key})=>{
    setFilterKeys((prev)=>({...prev, [type]:key}));
  }

  // Handle apply filters
  const handleApplyFilters = async () => { 
    setLoading(true);
    try {
      const response = await AXIOS.get("filter_officers/", {
        params: {
           loanmstid: "",
           bankmstid: userDetails?.BankMstID,
           branch: filterKeys.branch || "",
           collectionofficerid: filterKeys.collectionOfficerID || "",
           disbursementid: filterKeys.disbursementID || "",
           collectionofficer_name: filterKeys.collectionOfficerName || "",
           customername: filterKeys.customerName || "",
           customer_id: filterKeys.customerID || "",
           loan_type: filterKeys.loanType || "",
        },
      });
      if(response.status === 200 && response.data?.loans?.length > 0){
        setFilterData(response.data.loans);
      }else{
        setFilterData(originalData)
      }
      setVisible(false);
      setFilterActive(true);
    } catch (error) {
      console.log("Error in apply filters", error);
      setFilterData(originalData)
    }finally{
       setLoading(false);
    }
  };

  // Clear all filters
  const handleClearFilters = () => {
    setFilterKeys({
      branch:'',
      collectionOfficerID:'',
      disbursementID:'',  
      collectionOfficerName:'',
      customerID:'',
      customerName:'',
      loanType:'',
    })
    setFilterData(originalData); // Reset to original data
    setFilterActive(false); // Reset filter active state
    message.success("Filters cleared successfully");
    // close modal
    setVisible(false);
  };

  return (
    <div className={Style.container}>
      <Flex className={Style.button_container}>
        <input
          type="image"
          src={filterActive ? ACTIVE_FILTER_IMG : FILTER_IMG}
          alt="filter"
          onClick={() => setVisible(true)} // Open modal on click
        />
      </Flex>

      {visible && (
        <Flex className={Style.modal} vertical>
          <Flex justify="space-between">
            <Flex justify="center" style={{ flex: 1 }}>
              <Text className={Style.text}>Filter</Text>
            </Flex>
            <input 
             type="image"
              src={CROSS_IMG}
              alt="cross"
              style={{ cursor: "pointer" }}
              onClick={() => setVisible(false)}
            />
          </Flex>

          <Divider style={{ backgroundColor: "#0F2050", margin: "1rem 0" }} />

          <Flex gap={15} vertical>
            <Flex vertical>
              <Text className={Style.fields}>Branch Name:</Text>
              <Input
                className={Style.custom_input}
                placeholder="Enter branch name"
                value={filterKeys.branch || ""}
                onChange={(e) => handleFilterKeysChange({type:"branch", key:e.target.value})}
              />
            </Flex>

            <Flex vertical>
              <Text className={Style.fields}>Collection Officer Name:</Text>
              <Input
                className={Style.custom_input}
                placeholder="Enter collection officer name"
                value={filterKeys.collectionOfficerName || ""}
                onChange={(e) => handleFilterKeysChange({type:"collectionOfficerName", key:e.target.value})}
              />
            </Flex>
            <Flex vertical>
              <Text className={Style.fields}>Collection Officer ID:</Text>
              <Input
                className={Style.custom_input}
                placeholder="Enter collection officer ID"
                value={filterKeys.collectionOfficerID || ""}
                onChange={(e) => handleFilterKeysChange({type:"collectionOfficerID", key:e.target.value})}
              />
            </Flex>

            <Flex vertical>
              <Text className={Style.fields}>Loan Type:</Text>
              <Input
                className={Style.custom_input}
                placeholder="Enter Loan Type" 
                value={filterKeys.loanType || ""}
                onChange={(e) => handleFilterKeysChange({type:"loanType", key:e.target.value})}
              />
            </Flex>

            <Flex vertical>
              <Text className={Style.fields}>Customer Name:</Text>
              <Input
                className={Style.custom_input}
                placeholder="Enter customer name"
                value={filterKeys.customerName || ""}
                onChange={(e) => handleFilterKeysChange({type:"customerName", key:e.target.value})}
              />
            </Flex>
            
            <Flex vertical>
              <Text className={Style.fields}>Disbursement ID:</Text>
              <Input
                className={Style.custom_input}
                placeholder="Enter disbursement ID"
                value={filterKeys.disbursementID || ""}
                onChange={(e) => handleFilterKeysChange({type:"disbursementID", key:e.target.value})}
              />
            </Flex>


            <Flex justify="space-evenly">
              <Button
                className={[Style.apply, filterActive ? Style.active : ""].join(" ")}
                onClick={handleApplyFilters}
                loading={loading}
              >
                Apply
              </Button>
              <Button className={Style.clear} onClick={handleClearFilters}>
                Clear
              </Button>
            </Flex>
          </Flex>
        </Flex>
      )}
    </div>
  );
}

FilterButton.propTypes={
  visible: PropTypes.bool,
  setVisible: PropTypes.func,
  setFilterData: PropTypes.func,
  originalData: PropTypes.any,
}
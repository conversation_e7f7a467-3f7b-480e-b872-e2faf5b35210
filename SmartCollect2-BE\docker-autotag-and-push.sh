#!/bin/bash

# CONFIGURE THESE
IMAGE_NAME="smartcollect2"
REGISTRY="praveenchordia"  # e.g., docker.io/username or ghcr.io/username

# Get current datetime or Git commit hash for tagging
# Uncomment the one you prefer
TAG=$(date +%Y%m%d%H%M%S)
# TAG=$(git rev-parse --short HEAD)

# Full image name
FULL_IMAGE_NAME="$REGISTRY/$IMAGE_NAME"

echo "Auto-tagging Docker image..."

# Pull the current latest image (if exists) and tag it with a versioned tag
docker pull "$FULL_IMAGE_NAME:latest" && docker tag "$FULL_IMAGE_NAME:latest" "$FULL_IMAGE_NAME:$TAG"

# Build the new image
docker build -t "$FULL_IMAGE_NAME:latest" .

# Push versioned tag (previous latest)
if docker image inspect "$FULL_IMAGE_NAME:$TAG" > /dev/null 2>&1; then
    docker push "$FULL_IMAGE_NAME:$TAG"
fi

# Push the new latest image
docker push "$FULL_IMAGE_NAME:latest"

echo "✅ Image tagged and pushed: $TAG and latest"

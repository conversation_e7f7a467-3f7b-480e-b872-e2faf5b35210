import React from "react";
import Style from "./_refused.module.scss";
import { Flex, Input, Select, Typography } from "antd";
import PropTypes from "prop-types";

const { Text } = Typography;
export function RefusedToPay({ latestFeedBack }) {
  return (
    <div className={Style.refused_to_pay}>
      <Flex vertical gap={20} justify="center">
        {/* reason */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Reason for Customer Denial:</Text>
          <Select
            className={Style.refused_select}
            defaultValue={latestFeedBack?.ReasonForDenial}
            disabled
          />
        </Flex>

        {/* custom */}
        {latestFeedBack?.ReasonForDenial !== "holiday" &&
          latestFeedBack?.ReasonForDenial !== "death" && (
            <Flex justify="space-between" align="center">
              <Text className={Style.text}>Description for Denial:</Text>
              <Input
                placeholder="Enter"
                className={Style.custom_input}
                value={latestFeedBack?.ReasonForDenial}
                disabled
              />
            </Flex>
          )}
      </Flex>
    </div>
  );
}

RefusedToPay.propTypes={
  latestFeedBack: PropTypes.object
}
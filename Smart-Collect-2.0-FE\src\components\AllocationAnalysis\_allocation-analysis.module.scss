@import url("../../index.css");

$light-gray: #d9d9d9;
$tab-height: 3px;
$tab-width: 525px; // Increased the tab width

.tabs {
  margin-top: 0rem;

  &:global(.ant-tabs) {
    width: 100%;

    :global(.ant-tabs-nav-wrap) {
      padding: 0 0.9rem;
    }

    :global(.ant-tabs-nav) {
      :global(.ant-tabs-nav-list) {
        width: 100%;
        justify-content: space-between;

        :global(.ant-tabs-tab) {
          font-size: 18px;
          font-weight: 400;
          color: #97ace6;
          padding-bottom: 9px;
          margin: 0;
          width: $tab-width;
          display: flex;
          justify-content: center;
          align-items: center;
          position: relative;
          z-index: 2;
          font-family: "Kanit", "Courier New", Courier, monospace;
          &:hover {
            color: #97ace6;
          }

          &:global(.ant-tabs-tab-active) {
            :global(.ant-tabs-tab-btn) {
              color: white;
            }
          }
        }
      }

      &::before {
        border-bottom: $tab-height solid $light-gray;
        border-radius: 20px;
        margin: 0 0.9rem;
      }

      :global(.ant-tabs-ink-bar) {
        background-color: var(--dark-blue);
        height: 50px;
        border-start-start-radius: 8px;
        border-start-end-radius: 8px;
        z-index: 1;

        &::before {
          content: "";
          position: absolute;
          bottom: 0px;
          left: -15px;
          width: 34px;
          height: 50px;
          background: var(--dark-blue);
          transform: translateX(0%);
          clip-path: polygon(100% 55%, 0 100%, 100% 100%);
        }

        &::after {
          content: "";
          position: absolute;
          bottom: 0px;
          right: -15px;
          width: 34px;
          height: 50px;
          background: var(--dark-blue);
          transform: translateX(0%);
          clip-path: polygon(0 55%, 0 100%, 100% 100%);
        }
      }
    }
  }
}

// Responsive view
@media screen and (max-width: 768px) {
  .tabs {
    &:global(.ant-tabs) {
      :global(.ant-tabs-nav) {
        :global(.ant-tabs-nav-list) {
          :global(.ant-tabs-tab) {
            font-size: 10px;
            &:global(.ant-tabs-tab-active) {
              :global(.ant-tabs-tab-btn) {
                color: var(--dark-blue);
              }
            }
          }
        }
        &::before {
          border-width: 2px;
        }
        :global(.ant-tabs-ink-bar) {
          height: 2px;

          &::before,
          &::after {
            content: none;
          }
        }
      }
    }
  }
}

@import url("../../../../../index.css");

.button_container{
    width: 100%;
    margin-bottom: 0.5rem;
    button{
        background-color: #E4F8F9;
        color: var(--dark-blue);
        box-shadow: 0px 4px 4px 0px #00000040;
        flex:1;
        font-weight: 400;
        cursor: pointer;
        border: none;
        padding: 0.6rem 1rem;
        border-radius: 10px;
        font-size: 15px;
        font-family: 'Kanit','Courier New', Courier, monospace;

    &:hover{
        background-color: #E4F8F9;
    }
    }
}

$light-gray:#D9D9D9;
$tab-height: 7px;

:global(.ant-modal-wrap):has(.queue_modal){
z-index: 1200 !important;
}

.queue_modal{

    .img_div{
        width: 120px;
        img{
            width: 100%;
            object-fit: contain;
        }
    }
    .queue_title{
        margin: 0;
        color: var(--dark-blue);
        font-size:16px; 
        font-family: 'Kanit','Courier New', Courier, monospace;
        font-weight: 400;
        text-align: center;
    }
    .yes_btn, 
    .no_btn{
        border: none;
        outline: none;
        background-color: #3BA944 !important;
        color: white;
        &:hover{
            background-color: #3BA944;
            color: white !important;
        }
    }

    .no_btn{
        background-color: white !important;
        color: black;

        &:hover{
            background-color: white;
            color: black !important;
        }
    }

    .queue_button{
        background-color: var(--dark-blue);
        color: white;
        padding: 1rem 2rem;
        border: none;
        font-family: 'Kanit','Courier New', Courier, monospace;

        &:hover{
            background-color: var(--dark-blue) !important;
            color: white !important;
            border: none;
            box-shadow:none;
        }
    }

    :global(.ant-modal-close){
        right: 14px;
        top: 10px;
        color: black;
        &:hover{
          color:black !important;
          background-color: transparent;
        }
        :global(.ant-modal-close-x){
          font-size:10px;
          color: black;
        }
    }  

    :global(.ant-modal-header){
        background: var(--dark-blue);
        padding: 0.5rem 0px;
        text-align: center;
        border-radius: 8px;
        
        :global(.ant-modal-title){
            color: white;
            font-weight: 500;
        }
    }

    :global(.ant-modal-content){
       padding: 6px;
       background-color: #E4F8F9;
       border: 1px solid var(--dark-blue);


       :global(.ant-modal-body){                                                                                                                                                                                                                                                                                                                                                                                                                                                          
        padding: 1rem;

        :global(.ant-tabs){
            width: 100%;

            :global(.ant-tabs-ink-bar){
                background-color:var(--dark-blue);
                height: $tab-height;
                border-radius: 20px;
            }

            :global(.ant-tabs-nav){
                :global(.ant-tabs-tab){
                    font-size: 15px;
                    font-weight: 400;
                    color: #00000094;
                    padding-bottom: 9px;
                    margin: 0;
                    font-family: 'Kanit','Courier New', Courier, monospace;

                   &:global(.ant-tabs-tab-active){  
                    :global(.ant-tabs-tab-btn){
                        color: var(--dark-blue);
                    }
                   } 
                }
                :global(.ant-tabs-nav-list){
                    width: 100%;
                    justify-content: space-between;
                }
                &::before{
                    border-bottom: $tab-height solid $light-gray;
                    border-radius: 20px;
                }
            }
        }
       }
    }
}

// Responsive view
@media screen and (max-width:768px) {
    .queue_modal{
        .icon_img{
            width: 10px;
            height: 24px;
        }
        .queue_title{
            font-size: 12px;
        }
        .queue_button{
            padding: 0rem 1rem;
            font-size: 11px;
        }
      :global(.ant-modal-content){
        padding: 4px;

        :global(.ant-modal-body){  
            padding: 0.3rem;
            :global(.ant-tabs){
                :global(.ant-tabs-ink-bar){
                    height: 4px;
                }

                :global(.ant-tabs-nav){
                    :global(.ant-tabs-tab){
                        font-size: 10px;
                    }
                    &::before{
                        border-bottom: 4px solid $light-gray;
                    }
                }
            }
        }
      }
    }
}

// Responsive view
@media screen and (max-width:568px) {
    .button_container{
        flex-direction: column;
        gap: 15px !important;
        button{
            width: 100%;
            padding: 0.5rem;
            font-size: 11px;
        }
    }
}
import { Col, Flex, Row, Typography } from "antd";
import React, { useEffect, useState } from "react";
import { Card } from "./Card";
import DataLoading from "../Loaders/DataLoading";
import PropTypes from "prop-types";

const { Title } = Typography;

export function DashboardEngagementAllCards({ data }) {
  console.log(data);
  const DISPLAY_COUNT = 20;
  const [displayedData, setDisplayedData] = useState([]);
  const [isCustomerDataLoading, setIsCustomerDataLoading] = useState(false);

  // Handle Load more data
  const handleLoadMoreData = () => {
    setIsCustomerDataLoading(true);
    setTimeout(() => {
      const nextChunk = data.slice(
        displayedData.length,
        displayedData.length + DISPLAY_COUNT
      );
      setDisplayedData((prev) => [...prev, ...nextChunk]);
      setIsCustomerDataLoading(false);
    }, 80);
  };

  // Add data if user scroll
  useEffect(() => {
    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } =
        document.documentElement;
      const scrolled = scrollTop / (scrollHeight - clientHeight);
      if (
        scrolled >= 0.5 &&
        displayedData.length < data.length &&
        !isCustomerDataLoading
      ) {
        handleLoadMoreData();
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [displayedData.length, data.length, isCustomerDataLoading]);

  // Scroll the window if display data changed
  useEffect(() => {
    window.scrollBy(0, 1);
  }, [displayedData.length]);

  // Load initial data when data changes
  useEffect(() => {
    if (data?.length) {
      setDisplayedData(data.slice(0, DISPLAY_COUNT));
    }
  }, [data]);

  return (
    <Row
      gutter={[30, 30]}
      align={"middle"}
      wrap
      style={{ paddingBlockStart: "2rem" }}
    >
      {data?.length ? (
        displayedData?.map((data, index) => {
          return (
            <Col xl={8} md={12} xs={24} key={`${index}-${data.LoanMstID}`}>
              <Card
                loanMstId={data.LoanMstID}
                status={data?.status || "red"}
                name={data?.CustomerName || "Customer Name"}
                customerId={data?.CustomerID}
                branchName={data?.BranchName}
                disbursementId={data?.DisbursementId}
                loanType={data?.LoanType}
                overdueAmount={data?.Overdue_Amount || 0}
              />
            </Col>
          );
        })
      ) : (
        <Col span={24}>
          <Flex justify="center">
            <Title level={4}>No data found!</Title>
          </Flex>
        </Col>
      )}
      {/* Show the loader */}
      {isCustomerDataLoading && <DataLoading />}
    </Row>
  );
}

DashboardEngagementAllCards.propTypes = {
  data: PropTypes.array,
};

import {
  Mo<PERSON>,
  <PERSON>lex,
  <PERSON><PERSON><PERSON>,
  Button,
  InputNumber,
  message,
  Select,
} from "antd";
import React, { useEffect, useState } from "react";
import PHONE_IMG from "../../../assets/Images/solar_phone-linear.png";
import Style from "./_style.module.scss";
import { AXIOS } from "../../../apis/ho-Instance";
import PropTypes from "prop-types";

const { Text, Title } = Typography;

export function LogsCustomDialerModal({ modalStatus = false, setModalStatus }) {
  const [messageApi, contextHolder] = message.useMessage();
  const [number, setNumber] = useState("");
  const [searchCustomerValue, setSearchCustomerValue] = useState("");
  const [selectCustomerLoanMstId, setSelectCustomerLoanMstId] = useState(null);
  const [options, setOptions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const isValidIndianPhoneNumber = (num) => {
    const indianPhoneRegex = /^[6-9]\d{9}$/;
    return indianPhoneRegex.test(num);
  };

  const handleCancel = () => {
    setModalStatus(false);
    setSearchCustomerValue("");
    setSelectCustomerLoanMstId(null);
    setNumber("");
    setOptions([]);
  };

  const handleFetchCustomerOptions = async (query) => {
    if (!query) return;
    try {
      const res = await AXIOS.post("customdialer/", {
        details: query,
      });
      if (res.status === 200 && res.data.data.length > 0) {
        setOptions(
          res.data.data.map((item) => ({
            label: item.CustomerName,
            value: item.LoanMstID,
          }))
        );
      } else {
        setOptions([]);
      }
    } catch (error) {
      console.log("Error in fetching customers", error?.message);
    }
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      await AXIOS.post("dialertrigger/", {
        bm_number: number,
        LoanMstID: selectCustomerLoanMstId,
      });
      message.success("Call initiated successfully!");
    } catch (error) {
      console.log("Error in Logs custom dialer", error?.message);
      message.error("Please, try again!");
    } finally {
      setIsLoading(false);
    }
  };

  const handleOk = () => {
    if (!isValidIndianPhoneNumber(number)) {
      messageApi.error("Please enter a valid Indian phone number.");
      return;
    }
    if (!selectCustomerLoanMstId) {
      message.error("Please, select customer before initiate call!");
      return;
    }
    handleSubmit();
  };

  // Handle user input with debounce using setTimeout
  useEffect(() => {
    if (!searchCustomerValue) {
      setOptions([]); // Clear options when input is empty
      return;
    }

    const timeOut = setTimeout(() => {
      if (searchCustomerValue) {
        handleFetchCustomerOptions(searchCustomerValue);
      }
    }, 500);

    return () => clearTimeout(timeOut); //Clear the time out
  }, [searchCustomerValue]);

  return (
    <>
      {contextHolder}
      <Modal
        centered
        className={Style.custom_dialer_modal}
        title={
          <Flex align="center" gap={5} justify="center">
            <div className={Style.icon_img}>
              <img src={PHONE_IMG} alt="phone" />
            </div>
            <Title level={4} className={Style.feedback_title}>
              Custom Dialer
            </Title>
          </Flex>
        }
        open={modalStatus}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={null}
      >
        <Flex vertical gap={20}>
          <Flex gap={20} justify="space-between" align="center">
            <Text className={Style.text}>Initiate Number:</Text>
            <InputNumber
              className={Style.custom_number}
              value={number}
              onChange={(value) => setNumber(value)}
              placeholder="Enter Number"
            />
          </Flex>

          <Flex gap={20} justify="space-between" align="center">
            <Text className={Style.text}>Search Customer:</Text>
            <Select
              allowClear
              showSearch
              options={options}
              filterOption={false}
              value={selectCustomerLoanMstId}
              onChange={setSelectCustomerLoanMstId}
              onSearch={(value) => setSearchCustomerValue(value)}
              onClear={() => {
                setSelectCustomerLoanMstId(null); // Clear selected value
                setSearchCustomerValue(""); // Clear search input
              }}
              placeholder="Enter disbursement id, customer id, name"
              className={Style.custom_select}
            />
          </Flex>

          <Flex justify="center">
            <Button
              loading={isLoading}
              className={Style.custom_button}
              onClick={handleOk}
            >
              Initiate call
            </Button>
          </Flex>
        </Flex>
      </Modal>
    </>
  );
}

LogsCustomDialerModal.propTypes = {
  modalStatus: PropTypes.bool,
  setModalStatus: PropTypes.func,
};

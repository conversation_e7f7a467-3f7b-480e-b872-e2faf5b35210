import { Table, Typography } from "antd";
import React, { useState, useEffect } from "react";
import { formatAmount, handleDateFormatter } from "../../../constant";
import Style from "./_style.module.scss";
import PropTypes from "prop-types";

const { Text } = Typography;

export default function DateWiseResponse({ datewiseResponseData }) {
  const [data, setData] = useState([]);

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      render: (_, data) => <Text>{data.key}.</Text>,
    },
    {
      title: "As On Date",
      dataIndex: "as_on_date",
      sorter: (a, b) => a.as_on_date?.localeCompare(b.as_on_date),
      render: (AsOnDate) => (
        <Text>{AsOnDate ? handleDateFormatter(AsOnDate) : "--"}</Text>
      ),
    },
    {
      title: "Branch Name",
      dataIndex: "branchname",
      sorter: (a, b) => a.branchname?.localeCompare(b.branchname),
      render: (branchname) => <Text>{branchname}</Text>,
    },
    {
      title: "Promised Amount",
      dataIndex: "promised_amount",
      sorter: (a, b) => (a.promised_amount || 0) - (b.promised_amount ||0),
      render: (_, { promised_amount }) => (
        <Text className={Style.blue_text}>
          ₹ {formatAmount(promised_amount)}
        </Text>
      ),
    },
    {
      title:"Paid as Promised",
      dataIndex: "paid_as_promised",
      sorter: (a, b) => (a.paid_as_promised || 0) - (b.paid_as_promised ||0),
      render: (_, { paid_as_promised }) => <Text>{paid_as_promised}</Text>,
    },
    {
      title:"Promised to Pay",
      dataIndex: "promised_to_pay",
      sorter: (a, b) => (a.promised_to_pay || 0) - (b.promised_to_pay ||0),
      render: (_, { promised_to_pay }) => <Text>{promised_to_pay}</Text>,
    },
    {
      title: "Refuse to pay",
      dataIndex: "refuse_to_pay",
      sorter: (a, b) => (a.refuse_to_pay || 0) - (b.refuse_to_pay ||0),
      render: (_, { refuse_to_pay }) => <Text>{refuse_to_pay}</Text>,
    },
    {
      title:"Dispute Accounts",
      dataIndex: "dispute_accounts",
      sorter: (a, b) => (a.dispute_accounts || 0) - (b.dispute_accounts ||0),
      render: (_, { dispute_accounts }) => <Text>{dispute_accounts}</Text>,
    },
    {
      title: "Total Dispute Amount",
      dataIndex: "totalDisputeAmount",
      sorter: (a, b) => (a.totalDisputeAmount || 0) - (b.totalDisputeAmount ||0),
      render: (_, { totalDisputeAmount }) => (
        <Text className={Style.blue_text}>
          ₹ {formatAmount(totalDisputeAmount|| 0)}
        </Text>
      ),
    },
  ];

  const dataSource = Array.isArray(data)? data.map((d, i) => ({key: i + 1,...d,})): [];

  useEffect(()=>{
    setData(datewiseResponseData)
  },[datewiseResponseData]);

  return (
    <Table
      virtual
      className={Style.customTable}
      columns={columns}
      dataSource={dataSource}
      scroll={{
        x: 1800,
        y: 300,
      }}
      pagination={{
        showSizeChanger: false,
      }}
    />
  );
}

DateWiseResponse.propTypes={
  datewiseResponseData: PropTypes.array
}
import { Flex } from 'antd';
import React, { useEffect, useId, useState } from 'react'
import { STATUS_IDS } from '../../constant';
import Style from "./_recent.module.scss";
import PropTypes from 'prop-types';

export function RecentEngagement({recentEngagementAppsData, indicatorGap = 9}) {
  const id= useId();
  return (
    <Flex vertical className={Style.recent}>
      <div>
        <Flex gap={indicatorGap} className={Style.indicator_box} justify='center'>
        {recentEngagementAppsData?.map((app, index)=> <Indicator indicateStatus={app?.status} key={`${id}-${index}`}/>)}
        </Flex>
      </div>

      <Flex gap={indicatorGap} className={Style.images} justify="center">
        {recentEngagementAppsData?.map((app, index)=>{
          return <div className={Style.icon_img} key={`${index}-${id}`}>
                {app?.imgSrc &&<img src={app?.imgSrc} alt='enagegement'/>}
              </div>})}
      </Flex>  
      
  </Flex>
  )
}

const Indicator =({indicateStatus=STATUS_IDS.SUCCESS})=>{
  const colors = [
    {status:STATUS_IDS.SUCCESS, value: "#02951a"},
    {status:STATUS_IDS.REJECTED, value:"#ec3939"},
    {status:STATUS_IDS.PENDING,value:"#cacdca"}
  ];
  const [color, setColor] = useState("gray");

  useEffect(()=>{
    const selectedIndicate = colors.find((c)=> c.status === indicateStatus).value
    setColor(selectedIndicate);
  },[indicateStatus]);

  return <Flex align="center"><span className={`${Style.dot}`} style={{ backgroundColor: color }}></span></Flex>
}

RecentEngagement.propTypes={
  recentEngagementAppsData: PropTypes.array, 
  indicatorGap: PropTypes.number
}

Indicator.propTypes={
  indicateStatus: PropTypes.string,
}
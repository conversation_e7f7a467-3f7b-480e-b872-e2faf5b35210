import React from "react";
import { Flex, Typography } from "antd";
import IVR_IMG from "../../../../assets/Images/ivr.svg";
import Style from "./_style.module.scss";
import { ChatComponent } from "./ChatComponent";
import { handleDateFormatter } from "../../../../constant";
import PropTypes from "prop-types";

const { Text } = Typography;

export function IVR({ ivrData }) {
  if(ivrData.length === 0) return;

  // Group messages by conversation_date
  const groupedData = (ivrData ?? []).reduce((acc, conversation) => {
    const { conversation_date, conversation_json } = conversation;
    
    // Skip if conversation_json is null or not an array
    if (!conversation_json) return acc

    if (!acc[conversation_date]) {
      acc[conversation_date] = [];
    }
    acc[conversation_date] = acc[conversation_date].concat(conversation_json);
    return acc;
  }, {});

  return (
    <Flex className={Style.ivr_container} vertical gap={10}>
      {Object.entries(groupedData).map(([date, messages], index) => (
        <Flex key={`${index}-${date}`} vertical gap={10}>
          {/* Display conversation date once */}
          <Flex justify="center">
            <Text className={Style.date}>{handleDateFormatter(date)}</Text>
          </Flex>

          {/* AI Chats */}
          <Flex className={Style.what_chat_box} vertical gap={10}>
            <Flex justify="center" gap={5} align="center">
              <img src={IVR_IMG} className={Style.img} alt="Ivr Icon" />
              <Text className={Style.what_text}>IVR</Text>
            </Flex>

            <Flex className={Style.response_container} justify="center">
              {/* Pass combined messages to ChatComponent */}
              <ChatComponent messages={messages} />
            </Flex>
          </Flex>
        </Flex>
      ))}
    </Flex>
  );
}

IVR.propTypes={
  ivrData: PropTypes.array
}
import React, { useEffect, useState } from "react";
import {
  ALL_CACHE_NAMES,
  LOCAL_KEYS,
  SMART_COLLECT_MENU_IDS,
} from "../../constant";
import { Flex } from "antd";
import AppHeader from "../AppHeader";
import AppEngagements from "../AppEngagements";
import CustomerAllCards from "../CustomerAllCards";
import CustomersTable from "../CustomersTable";
import { AXIOS } from "../../apis/ho-Instance";
import Style from "./_wrong-number.module.scss";
import ApplicationLoader from "../ApplicationLoader";
import {
  getCacheKey,
  getFromCache,
  getPostCacheKey,
  storeToCache,
} from "../../utils/cacheHelper";
import { encryptBody, isSameEncryptedBody } from "../../utils/crypto";

const CACHE_NAME = ALL_CACHE_NAMES.DASHBOARD_CACHE;

export default function WrongNumber() {
  const [isSwitchOn, setIsSwitchOn] = useState(false);
  const [customerData, setCustomerData] = useState([]);
  const [loading, setLoading] = useState(false);
  // Retrieve dates from localStorage
  const storedFilter = localStorage.getItem("filterDate");
  const storedDpd = localStorage.getItem("DashboardDpdCategory");

  // Get wrong number data
  const handleGetWrongNumberData = async () => {
    setLoading(true);
    const cacheKey = getCacheKey({ endpoint: "wrongnumber/" });
    // Check cache
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setCustomerData(cachedData);
      setLoading(false);
    }
    try {
      const response = await AXIOS.get("wrongnumber/");
      if (response.status === 200) {
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
        setCustomerData(response.data);
      }
    } catch (error) {
      console.log("Error in wrong number", error?.message);
    } finally {
      setLoading(false);
    }
  };

  const handleWrongNumber = async ({
    fromDate,
    toDate,
    BranchName,
    dpdRange,
    disbursementID,
  }) => {
    setLoading(true);
    const body = {
      from_date: fromDate,
      to_date: toDate,
      branch_id: BranchName,
      dpdfrom: dpdRange?.start ?? -1,
      dpdto: dpdRange?.end ?? -1,
      disbursementids: disbursementID,
    };
    const cacheKey = getPostCacheKey({ endpoint: "wrongnumber/", body });
    const encryptedOldBody = localStorage.getItem(LOCAL_KEYS.HO_PAY_FILTER);
    const sameBody = isSameEncryptedBody({
      newBody: body,
      encryptedOldBody,
    });

    if (sameBody) {
      const cachedData = await getFromCache({
        cacheName: CACHE_NAME,
        key: cacheKey,
      });
      if (cachedData) {
        setCustomerData(cachedData);
        setLoading(false);
      }
    }
    try {
      const response = await AXIOS.post("wrongnumber/", body);
      // Check the response
      if (response.status === 200) {
        const encrypted = encryptBody(body);
        if (encrypted) {
          localStorage.setItem(LOCAL_KEYS.HO_PAY_FILTER, encrypted);
        }
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
        setCustomerData(response.data);
      }
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoading(false);
    }
  };

  // Apply filter and fetch data
  const applyFilters = async ({
    fromDate,
    toDate,
    BranchName,
    disbursementID,
  }) => {
    await handleWrongNumber({
      fromDate: fromDate,
      toDate: toDate,
      BranchName,
      dpdRange: JSON.parse(storedDpd),
      disbursementID,
    });
  };

  // Clear filters and fetch data
  const ClearFilters = () => {
    handleGetWrongNumberData();
    localStorage.removeItem("filterDate");
    localStorage.removeItem(LOCAL_KEYS.HO_PAY_FILTER);
  };

  useEffect(() => {
    if (storedFilter || storedDpd) {
      const { fromDate, toDate, BranchName, disbursementID } = storedFilter
        ? JSON.parse(storedFilter)
        : { fromDate: null, toDate: null, BranchName: null };
      const parsedDpd = storedDpd
        ? JSON.parse(storedDpd)
        : { start: -1, end: -1 };
      handleWrongNumber({
        fromDate,
        toDate,
        BranchName,
        dpdRange: parsedDpd,
        disbursementID,
      });
    } else {
      handleGetWrongNumberData();
    }
  }, []);
  return (
    <Flex gap={20} vertical className={Style.wrong_number}>
      {/* Header */}
      <AppHeader
        title={`Wrong Number: ${customerData.length}`}
        isDashboard={false}
        isSwitchOn={isSwitchOn}
        setIsSwitchOn={setIsSwitchOn}
        isDashboardOtherPages={true}
        applyFilters={applyFilters}
        ClearFilters={ClearFilters}
      />

      <Flex gap={15} vertical>
        {/* app engagements */}
        <AppEngagements />

        {/* Details */}
        {loading ? (
          <ApplicationLoader />
        ) : (
          <div className={Style.details}>
            {!isSwitchOn ? (
              <CustomerAllCards
                pageId={SMART_COLLECT_MENU_IDS.WRONG_NUMBER}
                customerData={customerData}
                modalButtonText={"Update mobile number"}
                isModal={true}
              />
            ) : (
              <CustomersTable
                title={"Wrong Number"}
                customerData={customerData}
                pageId={SMART_COLLECT_MENU_IDS.WRONG_NUMBER}
                modalButtonText={"Update mobile number"}
              />
            )}
          </div>
        )}
      </Flex>
    </Flex>
  );
}

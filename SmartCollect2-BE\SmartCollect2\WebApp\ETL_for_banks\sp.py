import psycopg2
from datetime import datetime
import logging
import smtplib
import os
import sys
from email.mime.text import MI<PERSON><PERSON>ext
from email.mime.multipart import MIME<PERSON>ultipart
from django.conf import settings
from DB.db_manager import db_manager

# Configure SP-specific logging
def setup_sp_logger():
    """
    Setup dedicated logger for stored procedure execution
    """
    # Create logs directory if it doesn't exist
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'logs')
    os.makedirs(log_dir, exist_ok=True)
    
    # Create sp_logs file path
    log_file = os.path.join(log_dir, 'sp_logs.log')
    
    # Create logger
    sp_logger = logging.getLogger('sp_logger')
    sp_logger.setLevel(logging.INFO)
    
    # Remove existing handlers to avoid duplicates
    sp_logger.handlers.clear()
    
    # Create file handler
    file_handler = logging.FileHandler(log_file, mode='a', encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    
    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # Add handlers to logger
    sp_logger.addHandler(file_handler)
    sp_logger.addHandler(console_handler)
    
    return sp_logger

# Initialize SP logger
logger = setup_sp_logger()

# Bank mapping for display names
BANK_MAPPING = {
    13: "Artha-Siddhi Bank",
    21: "Pavana Bank", 
    23: "Vinaya Finance",
    36: "Warna",
    27: "Ghoti Finance",
    28: "Shakti Finance",
    29: "Rajapur Bank",
    30: "Sindhu Dhurg Bank",
    32: "Bss Bihar Bank",
    35: "Share India Fin Cap PVT LTD",
    37: "Shrimant Malojiraje",
    369: "Swami Samarth Bank",
    31: "Satya",
    39: "CredFair"
}

def get_email_recipients(bank_id, is_failure=False):
    """
    Get email recipients based on bank_id and failure status
    
    Args:
        bank_id: Bank ID to determine recipients
        is_failure: Boolean indicating if this is a failure notification
    
    Returns:
        list: List of email addresses
    """
    # Base emails that are always included
    base_emails = {
        "goldy": "<EMAIL>",
        "utkarsh": "<EMAIL>",
        "aniket": "<EMAIL>",
        "prateek": "<EMAIL>",
        "gourav": "<EMAIL>",
        "customer_support": "<EMAIL>",
        "rashmi": "<EMAIL>",
        "rajvardhini": "<EMAIL>",
        "praveen": "<EMAIL>"
    }
    
    # If it's a failure, send to specific failure recipients
    if is_failure:
        failure_recipients = [
            base_emails["prateek"],
            base_emails["goldy"],
            base_emails["gourav"],
            base_emails["aniket"]
        ]
        logger.info(f"Failure notification - sending to: {', '.join(failure_recipients)}")
        return failure_recipients
    
    # Success notifications - bank-specific recipients
    if bank_id in (21, 13,30):  # Pavana Bank (21), Artha-Siddhi Bank (13)
        recipients = [
            base_emails["gourav"],
            base_emails["goldy"],
            base_emails["customer_support"],
            base_emails["rashmi"],
            base_emails["rajvardhini"],
        ]
        bank_names = "Pavana Bank, Artha-Siddhi Bank and Sindhudurg Bank"
        
    elif bank_id in [27, 29, 37]:  # Ghoti Finance (27), Rajapur Bank (29), Shrimant Malojiraje (37)
        recipients = [
            base_emails["aniket"],
            base_emails["goldy"],
            base_emails["customer_support"],
            base_emails["rashmi"],
            base_emails["rajvardhini"],
        ]
        bank_names = "Ghoti Finance, Rajapur Bank & Shrimant Malojiraje"
        
    elif bank_id == 36:  # Warna (36)
        recipients = [
            base_emails["goldy"],
            base_emails["gourav"],
            base_emails["customer_support"],
            base_emails["rashmi"],
            base_emails["rajvardhini"],
        ]
        bank_names = "Warna"
    elif bank_id in (32,369):
        recipients = [
            base_emails["goldy"],
            base_emails["customer_support"],
            base_emails["rashmi"],
            base_emails["rajvardhini"],
            base_emails["praveen"]
        ]
        bank_names = "Bss Bihar and Swami Samarath"
    elif bank_id in (35,31,39,23):
        recipients = [base_emails["goldy"],
            base_emails['utkarsh'],
            base_emails["customer_support"],
            base_emails["rashmi"],
            base_emails["rajvardhini"],
        ]
        bank_names = "Share India, Staya and CredFair"
    
    bank_name = BANK_MAPPING.get(bank_id, f"Bank ID {bank_id}")
    logger.info(f"Success notification for {bank_name} ({bank_names}) - sending to: {', '.join(recipients)}")
    return recipients

def send_email_notification(subject, body, bank_id, is_failure=False):
    """
    Send email notification using SMTP settings from Django settings with bank-specific recipients
    
    Args:
        subject: Email subject
        body: Email body content
        bank_id: Bank ID to determine recipients
        is_failure: Boolean indicating if this is a failure notification
    """
    try:
        logger.info(f"Attempting to send email notification: {subject}")
        
        # Get email settings from Django settings
        smtp_server = settings.EMAIL_HOST
        smtp_port = settings.EMAIL_PORT
        smtp_user = settings.EMAIL_HOST_USER
        smtp_password = settings.EMAIL_HOST_PASSWORD
        use_tls = settings.EMAIL_USE_TLS
        
        if not smtp_user or not smtp_password:
            logger.error("Email credentials not configured in settings")
            return
        
        # Get bank-specific recipients
        recipients = get_email_recipients(bank_id, is_failure)
        
        # Create message
        msg = MIMEMultipart()
        msg['From'] = smtp_user
        msg['To'] = ", ".join(recipients)  # Join recipients with comma
        msg['Subject'] = subject
        
        # Attach body to email
        msg.attach(MIMEText(body, 'html' if '<' in body else 'plain'))
        
        # Create SMTP session
        server = smtplib.SMTP(smtp_server, smtp_port)
        if use_tls:
            server.starttls()  # Enable security
        server.login(smtp_user, smtp_password)
        
        # Send email to all recipients
        text = msg.as_string()
        server.sendmail(smtp_user, recipients, text)
        server.quit()
        
        bank_name = BANK_MAPPING.get(bank_id, f"Bank ID {bank_id}")
        logger.info(f"Email notification sent successfully to {', '.join(recipients)} for {bank_name}: {subject}")
        
    except Exception as e:
        logger.error(f"Failed to send email notification: {str(e)}")


def execute_stored_procedure(connection, procedure_name, bank_id, procedure_description=None, additional_params=None):
    """
    Execute a single stored procedure with error handling
    
    Args:
        connection: Database connection object (raw connection or SQLAlchemy connection)
        procedure_name: Name of the stored procedure to execute
        bank_id: Bank ID parameter
        procedure_description: Optional description for logging
        additional_params: List of additional parameters to pass to the procedure
    
    Returns:
        dict: Result with success status and details
    
    Raises:
        Exception: If the stored procedure fails
    """
    try:
        # Handle both SQLAlchemy connection and raw connection
        if hasattr(connection, 'execute'):
            # SQLAlchemy connection
            cursor = connection
            raw_connection = connection.connection if hasattr(connection, 'connection') else None
        else:
            # Raw database connection
            cursor = connection.cursor()
            raw_connection = connection
        
        # Prepare parameters - bank_id first, then any additional parameters
        params = [bank_id]
        if additional_params:
            params.extend(additional_params)
        
        # Create parameter placeholders
        placeholders = ', '.join(['%s'] * len(params))
        
        # Log the procedure execution
        desc = procedure_description or procedure_name
        logger.info(f"Executing stored procedure: {desc} with parameters: {params}")
        
        start_time = datetime.now()
        
        # Execute the stored procedure
        if hasattr(cursor, 'execute') and not hasattr(cursor, 'fetchone'):
            # SQLAlchemy style execution
            result = cursor.execute(f"SELECT {procedure_name}({placeholders});", params)
            result_value = result.fetchone()
        else:
            # Raw cursor execution
            cursor.execute(f"SELECT {procedure_name}({placeholders});", params)
            result_value = cursor.fetchone()
        
        # Commit the transaction
        if hasattr(connection, 'commit'):
            connection.commit()
        elif raw_connection and hasattr(raw_connection, 'commit'):
            raw_connection.commit()
        
        end_time = datetime.now()
        execution_time = end_time - start_time
        
        logger.info(f"Successfully executed: {desc} in {execution_time.total_seconds():.2f} seconds")
        
        return {
            'success': True,
            'procedure': procedure_name,
            'description': desc,
            'result': result_value[0] if result_value else None,
            'message': f"Successfully executed {desc}",
            'execution_time': execution_time.total_seconds()
        }
        
    except Exception as e:
        # Rollback in case of error
        try:
            if hasattr(connection, 'rollback'):
                connection.rollback()
            elif raw_connection and hasattr(raw_connection, 'rollback'):
                raw_connection.rollback()
        except Exception as rollback_error:
            logger.error(f"Failed to rollback transaction: {str(rollback_error)}")
        
        error_msg = f"Failed to execute {procedure_description or procedure_name}: {str(e)}"
        logger.error(error_msg)
        
        # Raise the exception to stop execution
        raise Exception(error_msg)

def execute_campaign_data_query(connection, bank_id):
    """
    Execute the campaign data query and return results
    
    Args:
        connection: Database connection object
        bank_id: Bank ID parameter
    
    Returns:
        list: Query results as list of dictionaries
    """
    try:
        logger.info(f"Executing campaign data query for bank_id: {bank_id}")
        
        campaign_query = """
        SELECT
            cm."CampaignMstID",
            cm."Name" AS "CampaignName",
            cq."Type",
            COUNT(cq."CampaignMstID") AS "RecordCount"
        FROM
            public."CampaignMst" cm
        LEFT JOIN
            "CommunicationQueue" cq ON cm."CampaignMstID" = cq."CampaignMstID"
            AND cq."BankMstID" = %s
            AND cq."CreatedDate" = CURRENT_DATE
        WHERE
            cm."BankMstID" = %s
        GROUP BY
            cm."CampaignMstID",
            cm."Name",
            cq."Type"
        ORDER BY
            cm."CampaignMstID",
            cq."Type";
        """
        
        # Handle both SQLAlchemy connection and raw connection
        if hasattr(connection, 'execute') and not hasattr(connection, 'fetchall'):
            # SQLAlchemy connection
            result = connection.execute(campaign_query, (bank_id, bank_id))
            rows = result.fetchall()
            # Get column names
            columns = result.keys() if hasattr(result, 'keys') else ['CampaignMstID', 'CampaignName', 'Type', 'RecordCount']
        else:
            # Raw database connection
            cursor = connection.cursor()
            cursor.execute(campaign_query, (bank_id, bank_id))
            rows = cursor.fetchall()
            # Get column names from cursor description
            columns = [desc[0] for desc in cursor.description]
        
        # Convert rows to list of dictionaries
        campaign_data = []
        for row in rows:
            row_dict = {}
            for i, column in enumerate(columns):
                row_dict[column] = row[i]
            campaign_data.append(row_dict)
        
        logger.info(f"Successfully retrieved {len(campaign_data)} campaign data records")
        return campaign_data
        
    except Exception as e:
        logger.error(f"Failed to execute campaign data query: {str(e)}")
        # Return empty list on error to avoid breaking the main process
        return []

def format_campaign_data_html(campaign_data, bank_id):
    """
    Format campaign data as HTML table for email
    Filter out campaigns with zero records
    
    Args:
        campaign_data: List of dictionaries containing campaign data
        bank_id: Bank ID for getting bank name
    
    Returns:
        str: HTML formatted table
    """
    if not campaign_data:
        return "<p><strong>No campaign data available</strong></p>"
    
    # Filter out campaigns with zero or None record counts
    filtered_data = []
    for row in campaign_data:
        record_count = row.get('RecordCount', 0)
        # Handle None values and convert to int
        if record_count is None:
            record_count = 0
        try:
            record_count = int(record_count)
        except (ValueError, TypeError):
            record_count = 0
        
        # Only include campaigns with record count > 0
        if record_count > 0:
            row['RecordCount'] = record_count  # Store the cleaned record count
            filtered_data.append(row)
    
    if not filtered_data:
        bank_name = BANK_MAPPING.get(bank_id, f"Bank ID {bank_id}")
        return f"<p><strong>No active campaigns with records found for {bank_name}</strong></p>"
    
    bank_name = BANK_MAPPING.get(bank_id, f"Bank ID {bank_id}")
    
    html = f"""
    <h3>Campaign Data Summary for {bank_name}:</h3>
    <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
        <thead style="background-color: #f2f2f2;">
            <tr>
                <th>Campaign ID</th>
                <th>Campaign Name</th>
                <th>Type</th>
                <th>Record Count</th>
            </tr>
        </thead>
        <tbody>
    """
    
    total_records = 0
    for row in filtered_data:
        campaign_id = row.get('CampaignMstID', 'N/A')
        campaign_name = row.get('CampaignName', 'N/A')
        campaign_type = row.get('Type', 'N/A')
        record_count = row.get('RecordCount', 0)
        
        total_records += record_count
        
        html += f"""
            <tr>
                <td>{campaign_id}</td>
                <td>{campaign_name}</td>
                <td>{campaign_type}</td>
                <td>{record_count}</td>
            </tr>
        """
    
    html += """
        </tbody>
    </table>
    """
    
    # Add summary
    html += f"""
    <p><strong>Total Active Records Across All Campaigns for {bank_name}:</strong> {total_records}</p>
    <p><strong>Active Campaigns:</strong> {len(filtered_data)}</p>
    """
    
    return html

def get_database_connection():
    """
    Get a proper database connection that supports cursor operations
    
    Returns:
        Database connection object
    """
    try:
        # Try to get a raw connection from SQLAlchemy engine
        engine = db_manager.get_engine()
        
        # If it's a SQLAlchemy engine, get raw connection
        if hasattr(engine, 'raw_connection'):
            return engine.raw_connection()
        elif hasattr(engine, 'connect'):
            # Get SQLAlchemy connection
            return engine.connect()
        else:
            # Assume it's already a raw connection
            return engine
            
    except Exception as e:
        logger.error(f"Failed to get database connection: {str(e)}")
        raise

def close_database_connection(connection):
    """
    Properly close database connection
    
    Args:
        connection: Database connection to close
    """
    try:
        if hasattr(connection, 'close'):
            connection.close()
        logger.info("Database connection closed successfully")
    except Exception as e:
        logger.error(f"Failed to close database connection: {str(e)}")

def execute_post_processing_procedures(bank_id, connection=None):
    """
    Execute all post-processing stored procedures in sequence
    Sends email notifications on success or failure
    Raises exception and stops execution on first failure
    
    For Bank ID 32: Executes all procedures but uses 'process_communication_queuev5_bss' instead of 'process_communication_queuev5'
                   Also runs 'public.whatsapp_opted' after 'insert_into_loanmst' procedure with additional parameter 323
    
    Args:
        bank_id: Bank ID parameter
        connection: Optional database connection (if None, will create new one)
    
    Raises:
        Exception: If any stored procedure fails
    """
    
    bank_name = BANK_MAPPING.get(bank_id, f"Bank ID {bank_id}")
    
    logger.info("="*80)
    logger.info(f"STARTING POST-PROCESSING PROCEDURES FOR {bank_name} (BANK_ID: {bank_id})")
    logger.info("="*80)
    
    # Define all stored procedures to execute in order
    procedures = [
        {
            'name': 'public.migrate_to_disbursement_file',
            'description': 'Migrate to disbursement file'
        },
        {
            'name': 'public.insert_into_branchmst',
            'description': 'Insert into branch master'
        },
        {
            'name': 'public.insert_into_customermst',
            'description': 'Insert into customer master'
        },
        {
            'name': 'public.insert_into_loanmst',
            'description': 'Insert into loan master'
        },
        {
            'name': 'public.whatsapp_opted',
            'description': 'Process WhatsApp opted customers',
            'bank_id_condition': 32,  # Only run for bank_id 32
            'additional_params': [323]  # Pass 323 as additional parameter
        },
        {
            'name': 'public.insert_into_loaninstallmentmst4',
            'description': 'Insert into loan installment master'
        },
        {
            'name': 'public.migrate_to_collection_file',
            'description': 'Migrate to collection file',
        },
        {
            'name': 'public.insert_into_transactions',
            'description': 'Insert into transactions'
        },
        {
            'name': 'public.insert_secondary_customers_and_coapplicants',
            'description': 'Insert secondary customers and co-applicants'
        },
        {
            'name': 'public.insert_into_account_summaryv1_temptbl',
            'description': 'Insert into account summary temp table'
        },
        {
            'name': 'public.process_bank_data',
            'description': 'Process bank data'
        },
        {
            'name': 'public.process_communication_queuev5_bss' if bank_id == 32 else 'public.process_communication_queuev5',
            'description': 'Process communication queue (BSS version)' if bank_id == 32 else 'Process communication queue'
        },
        {
            'name': 'public.process_communication_queue_periodic',
            'description': 'Process Periodic Campaign',
            'bank_id_condition': 369  # Only run for bank_id 369
        },
        {
            'name': 'public.insert_in_blaster_queue2',
            'description': 'Insert in blaster queue'
        },
        {
            'name': 'public.insert_in_voicebot_queue2',
            'description': 'Insert in voicebot queue'
        },
        {
            'name': 'public.insert_in_whatsapp_queue2',
            'description': 'Insert in WhatsApp queue'
        }
    ]
    
    # Log special handling for Bank ID 32
    if bank_id == 32:
        logger.info(f"Bank ID 32 ({bank_name}) detected - using process_communication_queuev5_bss and including whatsapp_opted procedure with parameter 323")
    
    execution_start_time = datetime.now()
    successful_procedures = 0
    executed_procedures = []
    campaign_data = []
    
    # Use provided connection or get one from db_manager
    should_close_connection = False
    if connection is None:
        logger.info("Creating new database connection")
        connection = get_database_connection()
        should_close_connection = True
    else:
        logger.info("Using provided database connection")
    
    try:
        logger.info(f"Total procedures to execute: {len(procedures)}")
        
        for i, procedure in enumerate(procedures):
            # Check if this procedure has a bank_id condition
            if 'bank_id_condition' in procedure:
                if bank_id != procedure['bank_id_condition']:
                    logger.info(f"Skipping Step {i + 1}/{len(procedures)}: {procedure['description']} - Bank ID {bank_id} does not match required Bank ID {procedure['bank_id_condition']}")
                    # Add skipped procedure to executed_procedures for reporting
                    executed_procedures.append({
                        'success': True,
                        'procedure': procedure['name'],
                        'description': procedure['description'],
                        'result': 'SKIPPED - Bank ID condition not met',
                        'message': f"Skipped {procedure['description']} (Only runs for Bank ID {procedure['bank_id_condition']})",
                        'execution_time': 0,
                        'skipped': True
                    })
                    successful_procedures += 1
                    continue
            
            # Use override bank_id if specified, otherwise use the provided bank_id
            proc_bank_id = procedure.get('bank_id_override', bank_id)
            
            # Get additional parameters if specified
            additional_params = procedure.get('additional_params', None)
            
            logger.info(f"Step {i + 1}/{len(procedures)}: Starting {procedure['description']}")
            
            result = execute_stored_procedure(
                connection=connection,
                procedure_name=procedure['name'],
                bank_id=proc_bank_id,
                procedure_description=procedure['description'],
                additional_params=additional_params
            )
            
            executed_procedures.append(result)
            successful_procedures += 1
            
            logger.info(f"✓ Step {i + 1}/{len(procedures)} COMPLETED: {procedure['description']}")
        
        # All procedures executed successfully - now get campaign data
        logger.info("All stored procedures completed successfully. Fetching campaign data...")
        campaign_data = execute_campaign_data_query(connection, bank_id)
        
        execution_end_time = datetime.now()
        execution_time = execution_end_time - execution_start_time
        
        logger.info("="*80)
        logger.info(f"ALL POST-PROCESSING PROCEDURES COMPLETED SUCCESSFULLY FOR {bank_name} (BANK_ID: {bank_id})")
        logger.info(f"Total execution time: {execution_time}")
        logger.info(f"Successfully executed: {successful_procedures}/{len(procedures)} procedures")
        logger.info(f"Retrieved {len(campaign_data)} campaign data records")
        logger.info("="*80)
        
        # Format campaign data for email
        campaign_html = format_campaign_data_html(campaign_data, bank_id)
        
        # Send success email with campaign data
        success_subject = f"Post-Processing COMPLETED Successfully for {bank_name} (Bank ID: {bank_id})"
        
        success_body = f"""
<html>
<body>
<h2>Post-Processing Execution Completed Successfully</h2>
<p><strong>Bank:</strong> {bank_name}</p>
<p><strong>Bank ID:</strong> {bank_id}</p>
{"<p><strong>Special Configuration:</strong> Using BSS communication queue processor and WhatsApp opted procedure with parameter 323</p>" if bank_id == 32 else ""}
<p><strong>Execution Start Time:</strong> {execution_start_time.strftime('%Y-%m-%d %H:%M:%S')}</p>
<p><strong>Execution End Time:</strong> {execution_end_time.strftime('%Y-%m-%d %H:%M:%S')}</p>
<p><strong>Total Execution Time:</strong> {execution_time}</p>

<h3>Execution Summary:</h3>
<p><strong>Total Procedures:</strong> {len(procedures)}</p>
<p><strong>Successfully Completed:</strong> {successful_procedures}/{len(procedures)} procedures</p>
<p><strong>Status:</strong> ALL PROCEDURES COMPLETED SUCCESSFULLY</p>

<h3>Executed Procedures:</h3>
<ul>
"""
        
        for i, proc_result in enumerate(executed_procedures):
            execution_time_str = f" ({proc_result['execution_time']:.2f}s)" if 'execution_time' in proc_result else ""
            # Add special info for any additional data
            extra_info = ""
            if 'rows_affected' in proc_result:
                extra_info = f" - {proc_result['rows_affected']} rows affected"
            
            # Check if procedure was skipped
            if proc_result.get('skipped', False):
                success_body += f"<li>{i + 1}. {proc_result['description']} - ⚠️ SKIPPED (Bank ID condition not met)</li>"
            else:
                success_body += f"<li>{i + 1}. {proc_result['description']} - ✓ SUCCESS{execution_time_str}{extra_info}</li>"
        
        success_body += f"""
</ul>

{campaign_html}

</body>
</html>
        """
        
        send_email_notification(success_subject, success_body, bank_id, is_failure=False)
        
    except Exception as e:
        execution_end_time = datetime.now()
        execution_time = execution_end_time - execution_start_time
        
        logger.error("="*80)
        logger.error(f"POST-PROCESSING FAILED FOR {bank_name} (BANK_ID: {bank_id})")
        logger.error(f"Error: {str(e)}")
        logger.error(f"Successfully completed: {successful_procedures}/{len(procedures)} procedures")
        logger.error(f"Failed at step: {successful_procedures + 1}")
        logger.error(f"Total execution time before failure: {execution_time}")
        logger.error("="*80)
        
        # Send failure email
        failure_subject = f"Post-Processing FAILED for {bank_name} (Bank ID: {bank_id})"
        
        failure_body = f"""
<html>
<body>
<h2>Post-Processing Execution Failed</h2>
<p><strong>Bank:</strong> {bank_name}</p>
<p><strong>Bank ID:</strong> {bank_id}</p>
{"<p><strong>Special Configuration:</strong> Using BSS communication queue processor and WhatsApp opted procedure with parameter 323</p>" if bank_id == 32 else ""}
<p><strong>Execution Start Time:</strong> {execution_start_time.strftime('%Y-%m-%d %H:%M:%S')}</p>
<p><strong>Execution End Time:</strong> {execution_end_time.strftime('%Y-%m-%d %H:%M:%S')}</p>
<p><strong>Total Execution Time:</strong> {execution_time}</p>

<h3>Failure Details:</h3>
<p><strong>Error:</strong> {str(e)}</p>
<p><strong>Failed At:</strong> Step {successful_procedures + 1} of {len(procedures)}</p>

<h3>Execution Summary:</h3>
<p><strong>Successfully Completed:</strong> {successful_procedures}/{len(procedures)} procedures</p>
<p><strong>Status:</strong> FAILED</p>

<h3>Executed Procedures:</h3>
<ul>
"""
        
        for i, proc_result in enumerate(executed_procedures):
            execution_time_str = f" ({proc_result['execution_time']:.2f}s)" if 'execution_time' in proc_result else ""
            # Add special info for any additional data
            extra_info = ""
            if 'rows_affected' in proc_result:
                extra_info = f" - {proc_result['rows_affected']} rows affected"
            
            # Check if procedure was skipped
            if proc_result.get('skipped', False):
                failure_body += f"<li>{i + 1}. {proc_result['description']} - ⚠️ SKIPPED (Bank ID condition not met)</li>"
            else:
                failure_body += f"<li>{i + 1}. {proc_result['description']} - ✓ SUCCESS{execution_time_str}{extra_info}</li>"
        
        if successful_procedures < len(procedures):
            failure_body += f"<li>{successful_procedures + 1}. {procedures[successful_procedures]['description']} - ✗ FAILED</li>"
        
        failure_body += """
</ul>
</body>
</html>
        """
        
        send_email_notification(failure_subject, failure_body, bank_id, is_failure=True)
        
        # Re-raise the exception to stop execution
        raise e
        
    finally:
        if should_close_connection and connection:
            logger.info("Closing database connection")
            close_database_connection(connection)
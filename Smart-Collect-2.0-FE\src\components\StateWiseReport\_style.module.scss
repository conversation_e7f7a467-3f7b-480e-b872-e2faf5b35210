@import url("../../index.css");

$light-blue: #e4f8f9;
$blue: #becfff;
$disable: #787777;
$table-radius: 22px;
$attempts-bg: #bdd1ff;
$body: #e4f8f9;
$green: #1cae4d;

.add_bank {
  padding-top: 1rem !important;
  padding-bottom: 2rem !important;
  width: 100%;
  .text,
  .remark {
    color: var(--dark-blue);
    font-weight: 400;
    white-space: nowrap;
    width: 100px;
    font-family: "Kanit", "Courier New", Courier, monospace;
  }
  .img {
    width: 15px;
    img {
      width: 100%;
      object-fit: contain;
    }
  }

  .title {
    color: var(--dark-blue);
    font-weight: 400;
    white-space: nowrap;
    font-size: 1rem;
    font-family: "Kanit", "Courier New", Courier, monospace;
  }
  .table_container {
    background-color: $light-blue;
    padding: 1.5rem 1rem;
    border-radius: 5px;

    .button {
      background-color: var(--dark-blue) !important;
      color: white;
      font-family: "Kanit", "Courier New", Courier, monospace;
      border-radius: 4px;
      padding: 0.5rem 1rem;
      margin-left: 1rem;
    }
  }

  .customTable {
    padding-top: 1.5rem;
    .name {
      font-weight: 500;
      color: var(--dark-blue);
    }

    .icon_div {
      display: flex;
      justify-content: center;
      width: 100%;
      .icon_img {
        width: 15px;
        height: 15px;
        img {
          width: 100%;
          object-fit: contain;
          cursor: pointer;
        }
      }
    }

    .status {
      font-weight: 600;
    }

    //Global ant design classes
    // Pagination
    :global(.ant-pagination) {
      justify-content: center;
      margin: 0 !important;

      :global(.ant-pagination-prev),
      :global(.ant-pagination-next) {
        color: var(--dark-blue);
        border: 0 solid var(--blue);
        background: #e4f8f9;
        height: 25px;
        min-width: 15px;
        border-radius: 0px;
        margin: 0;
        button {
          border-radius: 0px;
        }
      }
      :global(.ant-pagination-item) {
        margin-right: 0;
        a {
          color: $disable;
          font-size: 0.9rem;
          line-height: 23px;
          font-family: "Kanit", "Courier New", Courier, monospace;
          font-weight: normal;
        }
        &:hover {
          background-color: transparent;
        }
      }
      :global(.ant-pagination-item-active) {
        border: none;
        background-color: transparent;
        a {
          color: var(--dark-blue);
          font-size: 1.2rem;
          padding: 0;
        }
      }
    }

    //Table container
    :global(.ant-table-container) {
      padding: 0;
      margin-bottom: 0.5rem;
      border-start-start-radius: $table-radius;
      border-start-end-radius: $table-radius;

      // Table header
      :global(.ant-table-header) {
        position: relative;
        margin: 0 auto;
        top: -21px;
        border-radius: $table-radius;
        :global(.ant-table-column-has-sorters) {
          background-color: var(--dark-blue);
          &:hover {
            background-color: var(--dark-blue);
          }

          :global(.ant-table-column-sorter-up),
          :global(.ant-table-column-sorter-down) {
            svg {
              fill: white;
            }
            &:global(.active) {
              svg {
                fill: rgb(24, 155, 249);
              }
            }
          }
        }
      }

      // Table virtual body
      :global(.ant-table-tbody-virtual) {
        margin-top: -8px;
      }

      &::before {
        box-shadow: none !important;
      }
    }

    // Table rows header
    :global(.ant-table-thead > tr) {
      th {
        border-start-end-radius: 0 !important;
        background-color: var(--dark-blue);
        border-inline-end: none !important;
        color: white;
        border-bottom: none;
        font-family: "Kanit", "Courier New", Courier, monospace;
        font-weight: normal;

        &:not(:last-child):not(.ant-table-selection-column):not(
            .ant-table-row-expand-icon-cell
          ):not([colspan])::before {
          width: 0;
        }
      }
    }

    // Table body
    :global(.ant-table-tbody) {
      // Body rows
      :global(.ant-table-row) {
        :global(.ant-table-cell-row-hover) {
          background-color: transparent;
        }
        // Cols
        :global(.ant-table-cell) {
          font-weight: 600;
          border-bottom: 2px solid white;

          :global(.ant-typography) {
            color: var(--dark-blue);
            font-family: "Kanit", "Courier New", Courier, monospace;
            font-weight: normal;
          }

          &:global(.ant-table-cell-fix-right) {
            border-bottom: none !important;
          }
          &:global(.ant-table-cell-fix-right-first) {
            border-left: 2px solid white;
          }
        }
      }
    }

    // Fixed Cols
    :global(.ant-table-cell-fix-right) {
      background-color: $body;
    }

    &.details_table {
      // Table body
      :global(.ant-table-tbody) {
        // Body rows
        :global(.ant-table-row) {
          &:nth-child(even) {
            background-color: var(--light-green);
            border-radius: 8px;
          }
        }
      }
    }
  }
}

.label_container {
  .label_text {
    color: var(--dark-blue);
    font-weight: 400;
    font-family: "Kanit", "Courier New", Courier, monospace;
    position: absolute;
    top: -10px;
    left: 20px;
    z-index: 2;
    background-color: white;
    padding: 0 1rem;
  }
  .custom_number {
    :global(.ant-input-wrapper) {
      background-color: $light-blue;
      border-radius: 8px;
      padding: 0.5rem;
      position: relative;
    }
    :global(.ant-input-outlined) {
      background-color: transparent;
      border: none !important;
      padding-top: 10px;
      color: var(--dark-blue);
      font-weight: 500;
      &:focus-within {
        border-color: $light-blue;
        box-shadow: none;
      }
      &::placeholder {
        font-weight: 500;
      }
    }

    :global(.ant-input-group-addon) {
      background-color: transparent;
      border: none;
      padding: 0;

      &:last-child {
        :global(.ant-input-search-button) {
          border-start-start-radius: 4px !important;
          border-end-start-radius: 4px !important;
        }
      }
    }
    :global(.ant-input-search-button) {
      background-color: var(--dark-blue) !important;
      width: 60px;
      border: none;
      border-radius: 4px;

      :global(.ant-btn-icon) {
        color: white !important;
      }
    }
  }
}

:global(.ant-select-dropdown) {
  margin-top: 1.2rem;
  background-color: $light-blue;

  :global(.ant-select-item-option) {
    color: var(--dark-blue);
    font-weight: 600;

    &:global(.ant-select-item-option-disabled) {
      color: rgba(0, 0, 0, 0.56);
    }
  }
  :global(.ant-select-item-option-active) {
    &:not(.ant-select-item-option-disabled) {
      background-color: $blue;
      font-weight: 600;
    }
  }
}

// Responsive View
@media screen and (max-width: 768px) {
  .add_bank {
    row-gap: 30px !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    :global(.ant-col) {
      padding: 0 !important;
    }
    .text,
    .remark {
      font-size: 11px;
      width: 100%;
    }
    .remark {
      width: 110px;
    }
    .custom_input {
      font-size: 10px;
    }
    .custom_select {
      :global(.ant-select-selection-item) {
        font-size: 10px;
      }
      :global(.ant-select-selection-placeholder) {
        font-size: 10px;
      }
    }
  }
  :global(.ant-select-dropdown) {
    :global(.ant-select-item-option) {
      font-size: 10px;
    }
  }
}

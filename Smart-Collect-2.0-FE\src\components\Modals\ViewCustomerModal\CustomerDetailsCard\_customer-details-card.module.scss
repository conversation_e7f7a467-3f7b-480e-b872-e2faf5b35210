@import url("../../../../index.css");

$blue: #3b67c7;
$light-blue: #e4f8f9;
$border-color: #acc5fc;
$bg-error: #ec393929;
$error: #ec3939;
$success: #087b1a;
$sky-blue: #e8efff;
$top-gap:30px;

.card_container {
  background-color: $light-blue;
  box-shadow: 0px 4px 4px 0px #00000040;
  border-radius: 20px;
  position: relative;
  padding-top: 3rem;
  margin: $top-gap 0 0;

  .customer_details {
    padding:0.8rem 1rem;
    background: var(--dark-blue);
    color: white;
    border-radius: 50px;
    position: absolute;
    top: -$top-gap;
    left: 8px;
    width: 91%;

    .profile_img {
      width: 20px;
      height: 20px;
      overflow: hidden;
      cursor: pointer;
      input[type='image']{
        width: 20px;
      }
      img {
        width: 100%;
        object-fit: contain;
      }
    }

    .name {
      font-weight: 400;
      margin: 0;
      line-height: 1;
      color: white;
      font-family: 'Kanit','Courier New', Courier, monospace;
      font-size:14px;
    }

    .customer_id {
      font-size: 13px;
      color: white;
      font-weight: 300;
      font-family: 'Kanit','Courier New', Courier, monospace;
    }
  }

  .payment_details,
  .date_details,
  .number_status {
    padding: 1rem 0;
    margin: 0 1rem;
    border-bottom: 1.05px solid $border-color;

    .icon_img {
      width: 15px;
      img {
        width: 100%;
        object-fit: contain;
      }
    }

    .title {
      font-size: 14px;
      font-family: 'Kanit','Courier New', Courier, monospace;
      text-transform: capitalize;
    }

    .value {
      font-size: 14px;
      font-weight: 400;
      font-family: 'Kanit','Courier New', Courier, monospace;
      text-transform: capitalize;
    }

    .attempts {
      background-color: white;
      padding: 0.2rem 1rem;
      border-radius: 10px;
      margin-bottom: 1rem;
      border: 1.04px solid #AAA8A8;

      .attempts_value {
        color: $blue;
        font-weight: 400;
        font-size: 0.9rem;
        font-family: 'Kanit','Courier New', Courier, monospace;
        span {
          font-size: 1rem;
          font-family: 'Kanit','Courier New', Courier, monospace;
        }
      }
    }
  }

  .amount_details {
    padding: 1rem 0;
    margin: 0 1rem;
    border-bottom: 1.05px solid $border-color;

    .text {
      font-size: 15px;
      font-weight: 400;
      font-family: 'Kanit','Courier New', Courier, monospace;
    }
    .amount_button {
      margin: 0;
      cursor: pointer;
    }
    .amount {
      margin: 0;
      font-weight: 600;
      color: #0F2050;
      font-family: 'Kanit','Courier New', Courier, monospace;
    }
    .error {
      color: $error;
    }
    .success {
      color: $success;
    }
  }

  .date_details {
    border: none;
  }

  .number_status{
    border-top: 1.05px solid $border-color;
    border-bottom: none;
    font-family: 'Kanit','Courier New', Courier, monospace;

    .status{
      background-color: #FFE2E2;
      color: $error;
      font-weight: 500;
      padding: 0.3rem 0.7rem;
      border-radius: 5px;
      font-family: 'Kanit','Courier New', Courier, monospace;
    }
  }

  .view_link,
  .feedback {
    display: flex;
    justify-content: end;
    span {
      font-size: 14px;
      font-weight: 400;
      color: $blue;
      text-decoration: underline;
      cursor: pointer;
      font-family: 'Kanit','Courier New', Courier, monospace;
    }
  }

  .modal_button_container {
    justify-content: center;
    width: 100%;
    .modal_button {
      width: 50%;
      padding: 8px;
      background-color:white;
      color: $blue;
      font-weight: 600;
      text-decoration: underline;
      cursor: pointer;
      font-size: 13px;
      clip-path: polygon(
        15% 0%,
        85% 0%,
        100% 100%,
        0% 100%
      ); /* Trapezium shape */
      margin: 0 auto;
      padding-left: 10px;
      padding-right: 10px;
      border: 1px solid #00000026;
      font-family: 'Kanit','Courier New', Courier, monospace;
    }
  }
}

// Responsive view
@media screen and (max-width:1042px) {
  .card_container{
    padding-top: 1rem;

    .customer_details{
     padding:0.5rem 0.8rem;
     left: 15px;

     .profile_container{
      align-items: center;
     }
     .name{
      font-size: 14px;
     }
     .customer_id{
      font-size: 10px;
    }
    }

    .payment_details,
    .date_details,
    .number_status {
        .icon_img {
        width: 16px;
        height: 16px;
      }
  
      .title {
        font-size: 12px;
      }
  
      .value {
        font-size: 12px;
        font-weight: 500;
      }
    }

    .amount_details {  
      .text {
        font-size: 12px;
      }
      .amount {
        font-size: 14px;
      }
    }

    .view_link,
    .feedback {
      span {
        font-size: 12px;
      }
    }
    
    .modal_button_container{
      .modal_button{
        font-size: 12px;
      }
    }
  }
}

@media screen and (max-width:768px){
 .card_container{
  padding-top: 0;
  margin-top: 0;
  .customer_details{
    width: 100%;
    position: sticky;
    border-radius: 20px 20px 0 0;
  }
  .number_status{
    padding: 0.5rem;

    :global(.ant-typography){
      font-size: 11px;
    }

    .status{
      padding: 0.3rem;
      font-size: 9px;
    }
  }
 }
}

@media screen and (max-width:568px) {
  .card_container{
    .customer_details{
     .name{
      font-size: 12px;
     }
     .customer_id{
      font-size: 8px;
     }

     .profile_img{
        width: 14px;
     }
    }

    .payment_details,
    .date_details,
    .number_status {
      .icon_img {
        width: 14px;
        height: 14px;
      }
  
      .title {
        font-size: 10px;
      }
  
      .value {
        font-size: 10px;
      }
      .attempts {
        padding: 0.2rem 0.5rem;
  
        .attempts_value {
          font-size: 0.7rem;
          span {
            font-size: 0.8rem;
          }
        }
      }
    }

    .amount_details {  
      .text {
        font-size: 10px;
      }
      .amount {
        font-size: 10px;
      }
    }

    .view_link,
    .feedback {
      span {
        font-size: 10px;
      }
    }
    
    .modal_button_container{
      .modal_button{
        font-size: 12px;
      }
    }
  }
}
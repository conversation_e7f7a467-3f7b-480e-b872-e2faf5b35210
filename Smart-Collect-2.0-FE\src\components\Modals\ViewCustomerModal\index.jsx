import React from 'react';
import Style from "./_style.module.scss";
import { Modal } from 'antd';
import { CustomerDetailsCard } from './CustomerDetailsCard';
import PropTypes from 'prop-types';

export function ViewCustomerModal({
    modalStatus, 
    data, 
    pageId, 
    modalButtonText, 
    handleCancel}) {
  return (
    <Modal 
    centered
    className={Style.view_modal} 
    title={null}
    open={modalStatus} 
    onOk={handleCancel} 
    onCancel={handleCancel}
    footer={null}
    >
        <CustomerDetailsCard 
            loanmstid={data?.loanmstid}
            branchMstId={data?.branchmstid}
            communicationCount={data?.communication_count}
            last5CommunicationTypes={data?.last_5_communication_types}
            status={data?.status}
            name={data?.customername || "Customer Name"}
            customerId={data?.customerid}
            branchName={data?.branchname}
            disbursementId={data?.disbursementid}
            dpdAmount={data?.dpd || 0}
            feedbackDate={data?.feedbackdate}
            loanType={data?.loantype}
            overdueAmount={data?.overdue_amount || 0}
            promiseAmount={data?.promise_amount || 0}
            promiseDate={data?.promise_date}
            modalButtonText={modalButtonText}
            isModal={true}
            pageId={pageId}/>
    </Modal>
  )
}

ViewCustomerModal.propTypes={
  modalStatus: PropTypes.bool, 
  data: PropTypes.object, 
  pageId: PropTypes.string, 
  modalButtonText: PropTypes.string, 
  handleCancel: PropTypes.func
}
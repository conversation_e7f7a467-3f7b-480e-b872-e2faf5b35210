import { Flex, Typography } from "antd";
import React from "react";
import Style from "./_style.module.scss";
import HISTORY_ICON from "../../../assets/Images/small-history-icon.svg";
import ARROW_ICON from "../../../assets/Images/history-arrow.svg";
import BlueCross from "../../../assets/SVGs/BlueCross";
import ApplicationLoader from "../../ApplicationLoader";
import PropTypes from "prop-types";

const { Text } = Typography;

export function HistoryChat({ showHistory, toggleHistory, Data,historyLoader }) {
  return (showHistory && 
    <div className={Style.history_modal}>
        {/* Header with <PERSON> Button */}
        <Flex justify="center" align="center" className={Style.header}>
          <Flex align="center" gap={5}>
              <img src={HISTORY_ICON} alt="history" />
              <Text className={Style.header_text}>History</Text>
          </Flex>
          <button onClick={toggleHistory} className={Style.close_btn}>
            <BlueCross/>
          </button>
        </Flex>
        {
          historyLoader && <ApplicationLoader/>
        }
        {/* History Items */}
        <Flex vertical gap={12} className={Style.history_list}>
          {Data?.active?.length > 0 ? (
            Data.active.map((item, index) => (
              <div key={item.uuid || index} className={Style.history_item}>
                <Flex justify="space-between">
                  <Text style={{ overflowWrap: "break-word", inlineSize: "300px", color:"#5C5C5C"}}>
                    <span style={{fontSize: "1.5rem", lineHeight: 0, marginInlineEnd: "10px"}}>&#x2022;</span>
                    {item.title || "Untitled Chat"}
                  </Text>
                </Flex>

                <Flex gap={10} align="center">
                  <div style={{ width: "10px" }}>
                    <img
                      src={ARROW_ICON}
                      alt="arrow"
                      style={{ width: "100%", objectFit: "contain" }}
                    />
                  </div>
                  <Text className={Style.history_message}>
                    {item.campaign_name || "No description"}
                  </Text>
                </Flex>
              </div>
            ))
          ) : (
            <Flex className={Style.no_history}>No chat history available</Flex>
          )}
        </Flex>
    </div>
  );
}

HistoryChat.propTypes={
  showHistory: PropTypes.bool,
  toggleHistory: PropTypes.func,
  Data: PropTypes.object,
  historyLoader: PropTypes.bool
}
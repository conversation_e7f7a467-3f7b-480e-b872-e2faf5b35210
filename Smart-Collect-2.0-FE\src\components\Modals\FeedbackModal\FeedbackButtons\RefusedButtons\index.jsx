import { Button, <PERSON>lex, Modal, Typography } from "antd";
import React, { useState } from "react";
import QUEUE_ICON_IMG from "../../../../../assets/Images/queue.svg";
import Style from "./_refused-button.module.scss";

const { Text } = Typography;

export function RefusedButtons() {
  const [showQueueModal, setShowQueueModal] = useState(false);

  const handleAddPriorityQueue = () => {
    setShowQueueModal(true);
  };
  return (
    <Flex
      justify="space-between"
      align="center"
      gap={20}
      className={Style.button_container}
    >
      {/* <button onClick={handleAddPriorityQueue}>Add to priority Queue</button> */}

      <Modal
        centered
        className={Style.queue_modal}
        title={null}
        open={showQueueModal}
        onOk={() => {
          setShowQueueModal(false);
        }}
        onCancel={() => {
          setShowQueueModal(false);
        }}
        footer={null}
        width={350}
      >
        <Flex justify="center" align="center" vertical gap={10}>
          <div className={Style.img_div}>
            <img src={QUEUE_ICON_IMG} alt="queue" />
          </div>
          <Text className={Style.queue_title}>
            Are You Sure You Want to Add this Customers to Priority Queue?
          </Text>
          <Flex gap={10} align="center">
            <Button className={Style.yes_btn}>Yes</Button>
            <Button className={Style.no_btn}>No</Button>
          </Flex>
        </Flex>
      </Modal>
    </Flex>
  );
}

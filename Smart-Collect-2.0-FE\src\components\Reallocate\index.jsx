import { Button, Flex, message, Table, Tooltip, Typography } from "antd";
import { useEffect, useState } from "react";
import AppHeader from "../AppHeader/index";
import Style from "./_style.module.scss";
import { AXIOS } from "../../apis/ho-Instance";
import { ReallocateModal } from "../Modals/ReallocateModal";
import { Link } from "react-router";
import { SMART_COLLECT_MENU_IDS, ALL_CACHE_NAMES } from "../../constant";
import ApplicationLoader from "../ApplicationLoader";
import ReallocationIcon from "../../assets/SVGs/streamline_new-file";
import FilterButton from "./FilterButton";
import {
  getCacheKey,
  getFromCache,
  storeToCache,
} from "../../utils/cacheHelper";

const { Text } = Typography;
const CACHE_NAME = ALL_CACHE_NAMES.REALLOCATE_CACHE;

export default function Reallocate() {
  const userDetails = JSON.parse(localStorage.getItem("user"));
  // State variables
  const [data, setData] = useState([]);
  const [filterData, setFilterData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [isReallocateLoading, setIsReallocateLoading] = useState(false);
  const [modalState, setModalState] = useState({ isOpen: false, data: null });
  const [filterVisible, setFilterVisible] = useState(false);

  const onSelectChange = (newSelectedRowKeys) =>
    setSelectedRowKeys(newSelectedRowKeys);

  // Fetch API data and set state
  const getAllTableData = async () => {
    setIsLoading(true);
    // Build cache key for GET request (with params)
    const cacheKey = getCacheKey({
      endpoint: "reallocate_customers/",
      params: {
        Designation: userDetails?.designation,
        bankmstid: userDetails?.BankMstID,
        branchmstid: userDetails?.BranchID,
      },
    });
    // Try to get cached data for instant UI update
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setData(cachedData);
      setFilterData(cachedData);
      setIsLoading(false);
    }
    try {
      const response = await AXIOS.get("customers/", {
        params: {
          Designation: userDetails?.designation,
          bankmstid: userDetails?.BankMstID,
          branchmstid: userDetails?.BranchID,
        },
      });
      if (response.data.loans?.length > 0 || response.status === 200) {
        setData(response.data.loans);
        setFilterData(response.data.loans);
        // Store the latest data in cache
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data.loans,
        });
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleModalClose = () => {
    setModalState({ isOpen: false, data: null });
  };

  const handleAddReAllocation = async ({
    selectedCollectOfficer,
    actionType,
    rangeKey,
    customerData,
  }) => {
    // Check customerData type
    const isBulk = Array.isArray(customerData);

    // Prepare the payload
    const startDate = rangeKey.fromDate
      ? rangeKey.fromDate.format("YYYY-MM-DD")
      : null;
    const endDate = rangeKey.toDate
      ? rangeKey.toDate.format("YYYY-MM-DD")
      : null;
    const firstItem = isBulk ? customerData[0] : customerData;
    let body;

    if (isBulk && customerData.length > 0) {
      body = {
        loans: customerData.map((c) => ({
          loanmstid: c.LoanMstID,
          customermstid: c.CustomerMstID,
          customername: c.CustomerName,
          disbursementid: c.DisbursementID,
          branchname: c.Branch,
        })),
        old_officer_id: firstItem.CollectionOfficerID,
        old_officer_name: firstItem.CollectionOfficerName,
        new_officer_id: selectedCollectOfficer.value,
        new_officer_name: selectedCollectOfficer.label,
        action_type: actionType,
        start_date: startDate,
        end_date: endDate,
        status: "Pending",
        created_by: "admin_user",
      };
    } else {
      body = {
        loanmstid: firstItem.LoanMstID,
        customermstid: firstItem.CustomerMstID,
        old_officer_id: firstItem.CollectionOfficerID,
        old_officer_name: firstItem.CollectionOfficerName,
        new_officer_id: selectedCollectOfficer.value,
        new_officer_name: selectedCollectOfficer.label,
        action_type: actionType,
        start_date: startDate,
        end_date: endDate,
        branch_name: firstItem.Branch,
        customer_name: firstItem.CustomerName,
        disbursement_id: firstItem.DisbursementID,
        status: "Pending",
        created_by: "admin_user",
      };
    }

    // Call the API to handle reallocation
    setIsReallocateLoading(true);
    try {
      const response = await AXIOS.post("customers/reallocate/", body);
      if (response.status === 200) {
        message.success("Reallocation successful!");
        handleModalClose();
        getAllTableData();
      } else {
        message.error("Reallocation failed. Please try again.");
      }
    } catch (error) {
      console.error("Error during reallocation:", error);
      message.error("An error occurred while reallocating. Please try again.");
    } finally {
      setIsReallocateLoading(false);
      setSelectedRowKeys([]);
    }
  };

  const handleBulkReallocate = () => {
    const selectedCustomers = data.filter((item) =>
      selectedRowKeys.includes(item.CustomerMstID)
    );

    const sameCO = selectedCustomers.every(
      (c) => c.CollectionOfficerID === selectedCustomers[0].CollectionOfficerID
    );

    if (!sameCO) {
      message.error(
        "Please select customers with the same collection officer for bulk reallocation.",
        5
      );
      return;
    }

    setModalState({
      isOpen: true,
      data: selectedCustomers,
    });
  };

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      width: 90,
      sorter: (a, b) => (a?.key ?? 0) - (b?.key ?? 0),
      render: (_, data) => <Text>{data?.key ?? "-"}</Text>,
    },
    {
      title: "Customer Id",
      dataIndex: "CustomerMstID",
      sorter: (a, b) =>
        String(a?.CustomerMstID ?? "").localeCompare(
          String(b?.CustomerMstID ?? "")
        ),
      render: (value) => <Text>{value ?? "-"}</Text>,
    },
    {
      title: "Customer Name",
      dataIndex: "CustomerName",
      sorter: (a, b) =>
        String(a?.CustomerName ?? "").localeCompare(
          String(b?.CustomerName ?? "")
        ),
      render: (value) => <Text className={Style.name}>{value ?? "-"}</Text>,
    },
    {
      title: "Disbursement Id",
      dataIndex: "DisbursementID",
      sorter: (a, b) =>
        String(a?.DisbursementID ?? "").localeCompare(
          String(b?.DisbursementID ?? "")
        ),
      render: (value) => <Text>{value ?? "-"}</Text>,
    },
    {
      title: "Branch Name",
      dataIndex: "Branch",
      sorter: (a, b) =>
        String(a?.Branch ?? "").localeCompare(String(b?.Branch ?? "")),
      render: (value) => <Text>{value ?? "-"}</Text>,
    },
    {
      title: "Current Collection Officer",
      dataIndex: "CollectionOfficerName",
      sorter: (a, b) =>
        String(a?.CollectionOfficerName ?? "").localeCompare(
          String(b?.CollectionOfficerName ?? "")
        ),
      render: (value) => <Text>{value ?? "-"}</Text>,
    },
    {
      title: "Reallocate",
      dataIndex: "reallocate",
      render: (_, data) => (
        <Flex justify="center">
          <Button
            className={Style.btn}
            onClick={() => setModalState({ isOpen: true, data })}
          >
            <ReallocationIcon strokeColor="black" width={15} height={15} />
          </Button>
        </Flex>
      ),
    },
  ];

  // Row selection configuration
  const rowSelection = { selectedRowKeys, onChange: onSelectChange };

  // Prepare data source for the table
  const dataSource = filterData.map((item, index) => ({
    ...item,
    key: index + 1,
  }));

  // Invoke the get api
  useEffect(() => {
    getAllTableData();
  }, []);
  return (
    <Flex gap={20} vertical className={Style.dnd_container}>
      <AppHeader title={"Reallocate"} />

      <Flex vertical gap={30}>
        <Flex justify="end" align="center" gap={15}>
          {selectedRowKeys.length > 1 && (
            <Button onClick={handleBulkReallocate} className={Style.bulk_btn}>
              Reallocate all customers
            </Button>
          )}
          <Tooltip title="Reallocation Action List">
            <Link
              to={`/ho/${SMART_COLLECT_MENU_IDS.REALLOCATE_ACTION_LIST}`}
              className={Style.action_list}
            >
              <ReallocationIcon />
            </Link>
          </Tooltip>
          <FilterButton
            setVisible={() => setFilterVisible(!filterVisible)}
            visible={filterVisible}
            setFilterData={setFilterData}
            originalData={data}
          />
        </Flex>

        {/* Table */}
        {isLoading ? (
          <ApplicationLoader />
        ) : (
          <Table
            rowKey={"CustomerMstID"}
            rowSelection={rowSelection}
            bordered
            virtual
            className={Style.customTable}
            columns={columns}
            dataSource={dataSource}
            scroll={{ x: 1200, y: 300 }}
            pagination={{
              pageSize: 10,
              showSizeChanger: false,
            }}
          />
        )}
      </Flex>
      <ReallocateModal
        modalStatus={modalState.isOpen}
        customerData={modalState.data}
        handleCancel={handleModalClose}
        handleOK={handleAddReAllocation}
        loading={isReallocateLoading}
      />
    </Flex>
  );
}

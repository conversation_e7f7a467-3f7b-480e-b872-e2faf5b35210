@import url("../../index.css");

$light-blue: #e3f5f6;

// Header
.app_container_header {
  background-image: url("../../assets/Images/header-bg.png");
  background-size: cover; /* Adjust to fit */
  background-position: center;
  background-repeat: no-repeat;
  width: 100%;
  position: fixed;
  top: 0;
  z-index: 111;
  padding: 0px;
  height: 64px;

  .menu_icon {
    cursor: pointer;
    object-fit: contain;
    margin: 0 20px 0 25px;
  }

  .logo,
  .logo_title {
    width: 60px;
    height: 60px;
    img {
      width: 100%;
      object-fit: contain;
    }
  }

  .logo_title {
    width: 180px;
  }

  .header_icon {
    display: flex;
    justify-content: center;
    input[type="image"] {
      background-color: #ffffff80;
      border-radius: 50px;
      padding: 5px;
      cursor: pointer;
      width: 32px;
    }
    img {
      background-color: #ffffff80;
      border-radius: 50px;
      padding: 5px;
      cursor: pointer;
      width: 32px;
    }
  }

  .filter_container {
    width: 40%;
    padding: 0 15px 0 0;

    .custom_search_input {
      :global(.ant-input-wrapper) {
        background-color: #ffffff80;
        border-radius: 50px;
      }
      :global(.ant-input) {
        font-family: "Kanit", "Courier New", Courier, monospace;
        font-size: 14px;
        line-height: 0px;
      }
      :global(.ant-input-group-addon) {
        border-left: 1px solid white;
      }
      :global(.ant-input),
      :global(.ant-input-group-addon .ant-btn) {
        width: 100%;
        background-color: transparent;
        border: none;
      }
      :global(.ant-input-group-addon .ant-btn) {
        padding: 0 10px;
        color: var(--dark-blue);
        box-shadow: none;
        outline: none;
        font-size: 18px;
        &:hover {
          color: var(--dark-blue);
          box-shadow: none;
        }
      }
    }
  }

  .menu_container {
    position: relative;
    cursor: pointer;

    .custom_menubar {
      position: absolute;
      z-index: 3;
      left: 8px;
      top: 64px;
      width: 50px;
    }

    :global(.ant-menu) {
      background-color: black;
      margin-top: 0;
      padding-top: 0;
      padding-bottom: 1rem;
      color: white;
      border-end-start-radius: 50px;
      border-end-end-radius: 50px;
      overflow: hidden;
      max-height: var(--container-height);
      transition: max-height 1s ease-in-out;
    }

    :global(.ant-menu-item),
    :global(.ant-menu-submenu) {
      margin-bottom: 10px;
      border-radius: 1px;
      width: 100%;
      margin-inline: 0;
      &:hover {
        background-color: rgba(255, 255, 255, 0.422);
      }
    }

    :global(.ant-menu-submenu-title) {
      width: 100%;
      margin: 0 auto;
      border-radius: 0;
    }

    :global(.ant-menu-item-selected),
    :global(.ant-menu-submenu-selected) {
      background-color: rgba(255, 255, 255, 0.422);
    }
  }

  :global(.ant-divider-vertical) {
    height: 4rem;
    border-color: white;
  }

  .modal {
    position: absolute;
    right: 0;
    top: 0px;
    background-color: $light-blue;
    box-shadow: 4px 4px 4px 0px #00000040;
    width: 300px;
    padding: 1rem;
    z-index: 1;
    border-radius: 10px;

    .text {
      text-align: center;
      color: var(--dark-blue);
      font-family: "Kanit", "Courier New", Courier, monospace;
      font-size: 16px;
      font-weight: 700;
    }

    .fields {
      color: var(--dark-blue);
      font-family: "Kanit", "Courier New", Courier, monospace;
    }

    .value {
      color: #0f2050;
      font-family: "Kanit", "Courier New", Courier, monospace;
      border-bottom: 1px solid #0f2050;
      border-radius: 10px;
      padding: 0.5rem;
    }
  }
}

:global(.ant-tooltip) {
  z-index: 12345;
}

// Responsive View
@media only screen and (max-width: 768px) {
  .app_container_header {
    .logo_container {
      align-items: center;
      gap: 0 !important;
    }
    .logo {
      width: 40px;
      height: 40px;
    }
    .logo_title {
      width: 110px;
      height: auto;
    }
    .header_icon {
      img {
        width: 16px !important;
      }
    }
    .menu_header_icon {
      img {
        width: 24px !important;
      }
    }
    .menu_icon {
      width: 11px !important;
      margin: 0 0px 0 15px;
    }
    .menu_container {
      .custom_menubar {
        left: 3px;
        width: 40px;
      }
    }
    .filter_container {
      padding: 0 4px 0 0;
      gap: 5px !important;
    }
  }
}

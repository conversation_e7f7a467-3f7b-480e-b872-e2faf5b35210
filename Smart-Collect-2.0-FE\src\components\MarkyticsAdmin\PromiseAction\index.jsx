import {
  Button,
  Col,
  Flex,
  Input,
  message,
  Row,
  Select,
  Typography,
} from "antd";
import React, { useEffect, useState } from "react";
import AppHeader from "../../AppHeader";
import Style from "../_global.module.scss";
import { AXIOS } from "../../../apis/ho-Instance";
import PromiseDetailsTable from "./PromiseDetailsTable";
const { Text } = Typography;

export default function PromiseAction() {
  const [isLoading, setIsLoading] = useState(false);
  const [responseData, setResponseData] = useState([]);
  const [allBanks, setAllBanks] = useState([]);
  const [configData, setConfigData] = useState({
    bank: null,
    disbursementID: "",
  });

  // Fetch banks
  const handleFetchBanks = async () => {
    try {
      const res = await AXIOS.get("getbanks/");
      if (res.status === 200 && res.data.Banks.length > 0) {
        setAllBanks(res.data.Banks);
      } else {
        setAllBanks([]);
      }
    } catch (error) {
      console.log("Error in fetching all banks", error?.message);
      setAllBanks([]);
    }
  };

  // Handle promise deletion
  const handleGetResponseData = async () => {
    setIsLoading(true);
    try {
      const res = await AXIOS.get("delete-disbursement/", {
        params: {
          bank_id: configData.bank,
          disbursement_id: configData.disbursementID,
        },
      });
      if (res.status === 200 && Array.isArray(res.data?.data?.responses)) {
        setResponseData(res.data?.data?.responses);
        message.success(res?.data?.message);
      } else {
        setResponseData([]);
      }
    } catch (error) {
      console.error("Error in Promise response", error);
      message.info("No related data found");
      setResponseData([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Invoking the fetch banks api
  useEffect(() => {
    handleFetchBanks();
  }, []);

  return (
    <Flex vertical gap={10}>
      <AppHeader title={"Promise Action"} />
      <Row
        gutter={[20, 30]}
        className={Style.add_bank}
        justify={"space-between"}
      >
        {/* bank */}
        <Col span={24}>
          <Flex gap={20} align="center">
            <Flex gap={5} align="center">
              <Text className={Style.text} style={{ width: "150px" }}>
                Select Bank:
              </Text>
            </Flex>
            <Select
              showSearch
              className={Style.custom_select}
              placeholder="Select bank"
              options={allBanks?.map((bank) => ({
                label: bank.BankName,
                value: bank.BankMstID,
              }))}
              value={configData.bank}
              onChange={(value) =>
                setConfigData((prev) => ({ ...prev, bank: value }))
              }
              filterOption={(input, option) =>
                (option?.label ?? "")
                  ?.toLowerCase()
                  .includes(input?.toLowerCase())
              }
            />
          </Flex>
        </Col>

        {/* search disbursement ids */}
        <Col span={24}>
          <Flex gap={20} align="center">
            <Flex gap={5} align="center">
              <Text className={Style.text} style={{ width: "150px" }}>
                Enter Disbursement Id:
              </Text>
            </Flex>
            <Input
              className={Style.custom_input}
              placeholder="Enter disbursement id"
              value={configData.disbursementID}
              onChange={(e) =>
                setConfigData((prev) => ({
                  ...prev,
                  disbursementID: e.target.value,
                }))
              }
            />
          </Flex>
        </Col>

        {/* get customer  */}
        <Col span={24}>
          <Flex align="center" justify="center">
            <Button
              className={Style.add_button}
              loading={isLoading}
              onClick={handleGetResponseData}
            >
              Get Customers Details
            </Button>
          </Flex>
        </Col>

        {Array.isArray(responseData) && responseData.length > 0 && (
          <Col span={24}>
            <PromiseDetailsTable data={responseData} />
          </Col>
        )}
      </Row>
    </Flex>
  );
}

import { Modal, Flex, Typography } from 'antd'
import Style from "./_style.module.scss";
import PropTypes from 'prop-types';

const {Title} = Typography;

export function ReceiptModal({ uri, modalStatus, handleCancel,}) {
 return (
  <Modal 
    centered
    className={Style.dialer_modal} 
    title={
      <Flex align='center' gap={5} justify='center' >
        <Title level={4} className={Style.feedback_title}>Receipt</Title>
      </Flex>
    }
    open={modalStatus} 
    onOk={handleCancel} 
    onCancel={handleCancel}
    footer={null}
    >
      <Flex vertical gap={20}>
        {uri && (
          <iframe
            src={uri}
            width="100%"
            height="600px"
            title="Collection Receipt"
          />
        )}
      </Flex>
  </Modal>
  )
}

ReceiptModal.propTypes={
  uri: PropTypes.any, 
  modalStatus: PropTypes.bool,
  handleCancel: PropTypes.func
}
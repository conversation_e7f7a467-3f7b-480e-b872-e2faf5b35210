import { Input, InputN<PERSON>ber, Select, DatePicker, Flex, Typography } from "antd";
import dayjs from "dayjs";
import Style from "./_style.module.scss";
import PropTypes from "prop-types";

const { Text } = Typography;

export function FilterValueInput({
  filter,
  index,
  parameterTypes,
  columnOptionsMap,
  handleInputChange,
}) {
  const isDropdown = ["in", "not in"].includes(filter.comparison);
  const isDate = parameterTypes[filter.parameterName] === "date";

  const getLabel = () => {
    console.log(filter.comparisonType);
    if (filter.comparisonType === "column") return "Column:";
    if (filter.comparison === "range") return "Range:";
    if (filter.comparison === "list") return "List:";
    if (isDate) return "Date:";
    return "Value:";
  };

  const handleChange = ({ field, value }) => {
    handleInputChange({ index, field, value });
  };

  const renderRangeInput = () => (
    <>
      <InputNumber
        placeholder="From"
        className={Style.custom_input_number}
        value={filter.rangeFrom}
        onChange={(value) => handleChange({ field: "rangeFrom", value })}
        disabled={filter?.isNonEditable}
      />
      <InputNumber
        placeholder="To"
        className={Style.custom_input_number}
        value={filter.rangeTo}
        onChange={(value) => handleChange({ field: "rangeTo", value })}
        disabled={filter?.isNonEditable}
      />
    </>
  );

  const renderListInput = () => (
    <Input
      className={Style.custom_input}
      placeholder="Enter"
      value={filter.list}
      onChange={(e) => handleChange({ field: "list", value: e.target.value })}
      disabled={filter?.isNonEditable}
    />
  );

  const renderDateInput = () => (
    <DatePicker
      format="DD-MM-YYYY"
      className={Style.date_picker}
      onChange={(date) =>
        handleChange({
          field: "value",
          value: dayjs(date).format("YYYY-MM-DD"),
        })
      }
      value={filter.value ? dayjs(filter.value, "YYYY-MM-DD") : null}
      disabled={filter?.isNonEditable}
    />
  );

  const getParsedDropdownValue = () => {
    if (Array.isArray(filter.value)) return filter.value;
    if (typeof filter.value === "string" && filter.value.startsWith("(")) {
      return (filter.value.match(/'(.*?)'/g) || []).map((item) =>
        item.replace(/(^')|('$)/g, "")
      );
    }
    return filter.value ? [filter.value] : [];
  };

  const renderDropdownInput = () => (
    <Select
      mode="multiple"
      value={getParsedDropdownValue()}
      className={Style.custom_select}
      placeholder="Select"
      options={columnOptionsMap[index] || []}
      onChange={(value) => handleChange({ field: "value", value })}
      disabled={filter?.isNonEditable}
    />
  );

  const renderDefaultInput = () => (
    <Input
      className={Style.custom_input}
      placeholder="Enter"
      value={filter.value}
      onChange={(e) => handleChange({ field: "value", value: e.target.value })}
      disabled={filter?.isNonEditable}
    />
  );

  const renderColumnSelector = () => (
    <Select
      showSearch
      placeholder="Select column"
      value={filter.value}
      className={Style.custom_select}
      options={
        parameterTypes
          ? Object.entries(parameterTypes).map(([key, val]) => ({
              label: val?.verbose_name ?? key,
              value: key,
            }))
          : []
      }
      onChange={(val) => handleChange({ field: "value", value: val })}
      disabled={filter?.isNonEditable}
    />
  );

  const renderInput = () => {
    if (filter.comparisonType === "column") return renderColumnSelector();
    if (filter.comparison === "range") return renderRangeInput();
    if (filter.comparison === "list") return renderListInput();
    if (isDate) return renderDateInput();
    if (isDropdown) return renderDropdownInput();
    return renderDefaultInput();
  };

  return (
    <Flex align="center" gap={10}>
      <Text className={Style.input_text}>{getLabel()}</Text>
      {renderInput()}
    </Flex>
  );
}

// Props types
FilterValueInput.propTypes = {
  filter: PropTypes.shape({
    parameterName: PropTypes.string,
    comparison: PropTypes.string,
    rangeFrom: PropTypes.number,
    rangeTo: PropTypes.number,
    list: PropTypes.string,
    comparisonType: PropTypes.string,
    value: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number,
      PropTypes.arrayOf(PropTypes.string),
      PropTypes.any,
    ]),
    isNonEditable: PropTypes.bool,
  }),
  index: PropTypes.number,
  parameterTypes: PropTypes.any,
  columnOptionsMap: PropTypes.objectOf(
    PropTypes.arrayOf(
      PropTypes.shape({
        label: PropTypes.string,
        value: PropTypes.string.isRequired,
      })
    )
  ),
  handleInputChange: PropTypes.func,
};

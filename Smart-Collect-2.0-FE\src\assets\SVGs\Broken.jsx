import React from "react";

export default function Broken() {
  return (
    <svg
      width={15}
      height={15}
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.4999 6.18915C9.78583 6.18915 10.0601 6.07556 10.2622 5.87337C10.4644 5.67118 10.578 5.39696 10.578 5.11102C10.578 4.82508 10.4644 4.55086 10.2622 4.34867C10.0601 4.14648 9.78583 4.0329 9.4999 4.0329C9.21396 4.0329 8.93974 4.14648 8.73755 4.34867C8.53536 4.55086 8.42177 4.82508 8.42177 5.11102C8.42177 5.39696 8.53536 5.67118 8.73755 5.87337C8.93974 6.07556 9.21396 6.18915 9.4999 6.18915ZM10.5579 1.09321C10.2585 0.850933 9.88504 0.71875 9.4999 0.71875C9.11476 0.71875 8.74129 0.850933 8.4419 1.09321L1.25152 6.93665C0.377523 7.64965 0.880648 9.06415 2.00765 9.06415H2.3124V15.1016C1.45852 15.5372 0.874898 16.4241 0.874898 17.4491V18.4079C0.874898 18.5985 0.950623 18.7813 1.08541 18.9161C1.22021 19.0509 1.40302 19.1266 1.59365 19.1266H5.67184C5.45603 18.6719 5.3097 18.1873 5.23771 17.6891H2.3124V17.4491C2.3124 16.7878 2.84859 16.2516 3.50984 16.2516H5.23771C5.39057 15.1936 5.87645 14.2118 6.6249 13.4485V9.06415H8.78115V12.1476C9.24771 12.0089 9.73192 11.9387 10.2186 11.9391V9.06415H12.3749V11.9391H13.8124V9.06415H15.2499V11.9391H15.9686C16.213 11.9391 16.4526 11.9559 16.6874 11.9895V9.06558H16.9921C18.1191 9.06558 18.6223 7.64965 17.7468 6.93808L10.5579 1.09321ZM9.3504 2.20871C9.39345 2.1749 9.4466 2.15652 9.50134 2.15652C9.55607 2.15652 9.60922 2.1749 9.65227 2.20871L16.3165 7.62665H2.6804L9.3504 2.20871ZM5.1874 14.8141H3.7499V9.06415H5.1874V14.8141ZM10.2186 13.3766C9.26553 13.3766 8.35144 13.7553 7.67748 14.4292C7.00352 15.1032 6.6249 16.0173 6.6249 16.9704C6.6249 17.9235 7.00352 18.8376 7.67748 19.5116C8.35144 20.1855 9.26553 20.5641 10.2186 20.5641H10.9374C11.128 20.5641 11.3108 20.4884 11.4456 20.3536C11.5804 20.2188 11.6561 20.036 11.6561 19.8454C11.6561 19.6548 11.5804 19.472 11.4456 19.3372C11.3108 19.2024 11.128 19.1266 10.9374 19.1266H10.2186C9.64678 19.1266 9.09832 18.8995 8.69395 18.4951C8.28957 18.0907 8.0624 17.5423 8.0624 16.9704C8.0624 16.3985 8.28957 15.8501 8.69395 15.4457C9.09832 15.0413 9.64678 14.8141 10.2186 14.8141H10.9374C11.128 14.8141 11.3108 14.7384 11.4456 14.6036C11.5804 14.4688 11.6561 14.286 11.6561 14.0954C11.6561 13.9048 11.5804 13.722 11.4456 13.5872C11.3108 13.4524 11.128 13.3766 10.9374 13.3766H10.2186ZM15.2499 13.3766C15.0593 13.3766 14.8765 13.4524 14.7417 13.5872C14.6069 13.722 14.5311 13.9048 14.5311 14.0954C14.5311 14.286 14.6069 14.4688 14.7417 14.6036C14.8765 14.7384 15.0593 14.8141 15.2499 14.8141H15.9686C16.2518 14.8141 16.5322 14.8699 16.7938 14.9783C17.0554 15.0866 17.2931 15.2455 17.4933 15.4457C17.6936 15.6459 17.8524 15.8836 17.9608 16.1452C18.0691 16.4068 18.1249 16.6872 18.1249 16.9704C18.1249 17.2536 18.0691 17.5339 17.9608 17.7956C17.8524 18.0572 17.6936 18.2949 17.4933 18.4951C17.2931 18.6953 17.0554 18.8541 16.7938 18.9625C16.5322 19.0709 16.2518 19.1266 15.9686 19.1266H15.2499C15.0593 19.1266 14.8765 19.2024 14.7417 19.3372C14.6069 19.472 14.5311 19.6548 14.5311 19.8454C14.5311 20.036 14.6069 20.2188 14.7417 20.3536C14.8765 20.4884 15.0593 20.5641 15.2499 20.5641H15.9686C16.9218 20.5641 17.8359 20.1855 18.5098 19.5116C19.1838 18.8376 19.5624 17.9235 19.5624 16.9704C19.5624 16.0173 19.1838 15.1032 18.5098 14.4292C17.8359 13.7553 16.9218 13.3766 15.9686 13.3766H15.2499ZM9.4999 16.9704C9.4999 16.7798 9.57562 16.597 9.71041 16.4622C9.84521 16.3274 10.028 16.2516 10.2186 16.2516H15.9686C16.1593 16.2516 16.3421 16.3274 16.4769 16.4622C16.6117 16.597 16.6874 16.7798 16.6874 16.9704C16.6874 17.161 16.6117 17.3438 16.4769 17.4786C16.3421 17.6134 16.1593 17.6891 15.9686 17.6891H10.2186C10.028 17.6891 9.84521 17.6134 9.71041 17.4786C9.57562 17.3438 9.4999 17.161 9.4999 16.9704Z"
        fill="#0F2050"
      />
    </svg>
  );
}

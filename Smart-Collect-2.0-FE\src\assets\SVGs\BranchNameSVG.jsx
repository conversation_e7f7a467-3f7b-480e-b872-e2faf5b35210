import PropTypes from "prop-types"

export default function BranchNameSVG({width = 14, height = 15, ...props}) {
  return (
    <svg width={width} height={height} viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M7.40363 5.9076C8.6339 5.9076 9.63123 4.91027 9.63123 3.67999C9.63123 2.44972 8.6339 1.45239 7.40363 1.45239C6.17336 1.45239 5.17603 2.44972 5.17603 3.67999C5.17603 4.91027 6.17336 5.9076 7.40363 5.9076Z" stroke="black" strokeWidth="1.27292" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M1.03931 13.2269C1.03931 10.4147 3.60391 8.13525 6.76743 8.13525M9.63149 13.5451L12.8138 10.3629L11.5409 9.08994L8.35857 12.2722V13.5451H9.63149Z" stroke="black" strokeWidth="1.27292" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  )
}

BranchNameSVG.propTypes ={
  width: PropTypes.number,
  height: PropTypes.number,
}
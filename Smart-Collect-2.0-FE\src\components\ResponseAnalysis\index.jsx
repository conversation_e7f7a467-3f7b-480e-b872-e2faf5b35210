import React, { useEffect, useState } from "react";
import AppHeader from "../AppHeader";
import { Flex, message, Table, Typography } from "antd";
import Style from "./_style.module.scss";
import {
  ALL_CACHE_NAMES,
  SMART_COLLECT_MENU_IDS,
  handleDownloadTable,
} from "../../constant";
import ApplicationLoader from "../ApplicationLoader";
import { AXIOS } from "../../apis/ho-Instance";
import { Link } from "react-router";
import DOWNLOAD_IMG from "../../assets/Images/download.svg";
import {
  getCacheKey,
  getFromCache,
  getPostCacheKey,
  storeToCache,
} from "../../utils/cacheHelper";

const { Text } = Typography;
const CACHE_NAME = ALL_CACHE_NAMES.ANALYSIS_CACHE;

export default function ResponseAnalysis() {
  const [data, setData] = useState([]);
  const [prevData, setPrevData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  // Get state
  const handleGetState = async () => {
    setIsLoading(true);
    const cacheKey = getCacheKey({ endpoint: "response-analysis/" });
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setData(cachedData);
      setPrevData(cachedData);
      setIsLoading(false);
    }
    try {
      const response = await AXIOS.post("responseanalysis/");
      if (
        response.status === 200 &&
        Array.isArray(response.data) &&
        response.data?.length
      ) {
        setData(response.data);
        setPrevData(response.data);
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
      } else {
        setData([]);
        setPrevData([]);
      }
    } catch (error) {
      console.log("Error in EMI", error);
      setData([]);
      setPrevData([]);
    } finally {
      setIsLoading(false);
    }
  };

  //Get Region, branch, CO.
  const handleGetOtherColsData = async ({ BUType, BUName }) => {
    setIsLoading(true);
    const body = { BUType, BUName };
    const cacheKey = getPostCacheKey({ endpoint: "response-analysis/", body });
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setData(cachedData);
      setPrevData(cachedData);
      setIsLoading(false);
    }
    try {
      const res = await AXIOS.post("responseanalysis/", body);
      if (res.status === 200 && Array.isArray(res.data) && res.data?.length) {
        setData(res.data);
        setPrevData(res.data);
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: res.data,
        });
      } else {
        message.warning(`Sorry, there is not data related to ${BUName}`);
        setData(prevData);
      }
    } catch (error) {
      console.log("Error in Promise table", error?.message);
      message.warning(`No data found for ${BUName}`);
      setData(prevData);
    } finally {
      setIsLoading(false);
    }
  };

  // Extract dynamic keys from the first item of the API response (excluding lan & amount)
  const dynamicKeys = data?.length
    ? Object.keys(data[0]).filter(
        (key) =>
          ![
            "AlreadyPaid",
            "BMAllocationCount",
            "Ready_to_pay",
            "RefusedToPay",
            "WrongNumber",
            "NonResponsive",
            "NonContactable",
          ].includes(key)
      )
    : [];

  // Create dynamic column definitions
  const dynamicColumns = dynamicKeys.map((key) => ({
    title: key,
    dataIndex: key,
    sorter: (a, b) => a[key]?.localeCompare?.(b[key]),
    render: (_, record) => (
      <Text
        className={Style.blueText}
        onClick={() =>
          handleGetOtherColsData({ BUType: key, BUName: record[key] })
        }
      >
        {record[key] || "--"}
      </Text>
    ),
  }));

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      sorter: (a, b) => a.key - b.key,
      render: (_, data) => <Text>{data?.key}.</Text>,
    },
    ...dynamicColumns,
    {
      title: "Response Categories",
      dataIndex: "ResponseCategories",
      children: [
        {
          title: "Promised to pay",
          dataIndex: "Ready_to_pay",
          sorter: (a, b) => a.Ready_to_pay - b.Ready_to_pay,
          render: (value) => (
            <Link to={`/ho/dashboard/${SMART_COLLECT_MENU_IDS.READY_TO_PAY}`}>
              <Text className={Style.lan}>{value || 0}</Text>
            </Link>
          ),
        },
        {
          title: "Refused to pay",
          dataIndex: "RefusedToPay",
          sorter: (a, b) => a.RefusedToPay - b.RefusedToPay,
          render: (value) => (
            <Link to={`/ho/dashboard/${SMART_COLLECT_MENU_IDS.REFUSED_TO_PAY}`}>
              <Text className={Style.lan}>{value || 0}</Text>
            </Link>
          ),
        },
        {
          title: "Already paid",
          dataIndex: "AlreadyPaid",
          sorter: (a, b) => a.AlreadyPaid - b.AlreadyPaid,
          render: (value) => (
            <Link to={`/ho/dashboard/${SMART_COLLECT_MENU_IDS.ALREADY_PAY}`}>
              <Text className={Style.lan}>{value || 0}</Text>
            </Link>
          ),
        },
        {
          title: "Wrong Number",
          dataIndex: "WrongNumber",
          sorter: (a, b) => a.WrongNumber - b.WrongNumber,
          render: (value) => (
            <Link to={`/ho/dashboard/${SMART_COLLECT_MENU_IDS.WRONG_NUMBER}`}>
              <Text className={Style.lan}>{value || 0}</Text>
            </Link>
          ),
        },
        {
          title: "Non-Responsive",
          dataIndex: "NonResponsive",
          sorter: (a, b) => a.BMAllocationCount - b.BMAllocationCount,
          render: (value) => (
            <Link to={`/ho/dashboard/${SMART_COLLECT_MENU_IDS.BMI_ALLOCATION}`}>
              <Text className={Style.lan}>{value || 0}</Text>
            </Link>
          ),
        },
        {
          title: "Non-Contactable",
          dataIndex: "NonContactable",
          sorter: (a, b) => a.NonContactable - b.NonContactable,
          render: (value) => (
            <Link
              to={`/ho/dashboard/${SMART_COLLECT_MENU_IDS.NON_CONTACTABLE}`}
            >
              <Text className={Style.lan}>{value || 0}</Text>
            </Link>
          ),
        },
      ],
    },
  ];

  const dataSource = Array.isArray(data)
    ? data.map((data, i) => ({ key: i + 1, ...data }))
    : [];

  useEffect(() => {
    handleGetState();
  }, []);

  // Handle downloading
  const handleDownload = async () => {
    if (data.length === 0) {
      return message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: "Response Analysis",
        worksheetName: "Response-Analysis",
        tableData: data,
      });
    }
  };
  return (
    <Flex vertical gap={20} className={Style.container}>
      <AppHeader title={"Response Analysis"} />
      <Flex vertical gap={10}>
        {/* <Spin tip={"Loading..."} spinning={isLoading}> */}
        {isLoading ? (
          <ApplicationLoader />
        ) : (
          <Flex gap={10} vertical>
            <Flex justify="end" gap={10}>
              <button
                className={Style.download_button}
                onClick={handleDownload}
              >
                <img src={DOWNLOAD_IMG} alt="download-button" />
              </button>
            </Flex>
            <Table
              bordered
              virtual
              className={Style.custom_table}
              columns={columns}
              dataSource={dataSource}
              scroll={{
                x: 1600,
                y: 460,
              }}
              pagination={{
                showSizeChanger: false,
              }}
            />
          </Flex>
        )}
        {/* </Spin> */}
      </Flex>
    </Flex>
  );
}

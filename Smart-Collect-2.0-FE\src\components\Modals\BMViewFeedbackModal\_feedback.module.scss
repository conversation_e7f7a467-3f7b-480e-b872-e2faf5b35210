@import url("../../../index.css");

$light-gray:#D9D9D9;
$tab-height: 7px;

.feedback_modal{
    .icon_img{
        width: 13px;
        height: 20px;
        img{
            width: 100%;
            object-fit: contain;
        }
    }

    .feedback_title{
        margin: 0;
        color: white;
        font-size:16px; 
        font-family: 'Kanit','Courier New', Courier, monospace;
        font-weight: 400;
    }

    .feedback_button{
        background-color: var(--dark-blue);
        color: white;
        padding: 1rem 2rem;
        border: none;
        font-family: 'Kanit','Courier New', Courier, monospace;

        &:hover{
            background-color: var(--dark-blue) !important;
            color: white !important;
            border: none;
            box-shadow:none;
        }
    }

    :global(.ant-modal-close){
        right: 14px;
        top: 10px;
        color: white;
        &:hover{
          color: white !important;
        }
        :global(.ant-modal-close-x){
          font-size:10px;
          color: white;
        }
    }  

    :global(.ant-modal-header){
        background: var(--dark-blue);
        padding: 0.5rem 0px;
        text-align: center;
        border-radius: 8px;
        
        :global(.ant-modal-title){
            color: white;
            font-weight: 500;
        }
    }

    :global(.ant-modal-content){
       padding: 6px;

       :global(.ant-modal-body){                                                                                                                                                                                                                                                                                                                                                                                                                                                          
        padding: 1rem;

        :global(.ant-tabs){
            width: 100%;

            :global(.ant-tabs-ink-bar){
                background-color:var(--dark-blue);
                height: $tab-height;
                border-radius: 20px;
            }

            :global(.ant-tabs-nav){
                :global(.ant-tabs-tab){
                    font-size: 15px;
                    font-weight: 400;
                    color: #00000094;
                    padding-bottom: 9px;
                    margin: 0;
                    font-family: 'Kanit','Courier New', Courier, monospace;

                   &:global(.ant-tabs-tab-active){  
                    :global(.ant-tabs-tab-btn){
                        color: var(--dark-blue);
                    }
                   } 
                }
                :global(.ant-tabs-nav-list){
                    width: 100%;
                    justify-content: space-between;
                }
                &::before{
                    border-bottom: $tab-height solid $light-gray;
                    border-radius: 20px;
                }
            }
        }
       }
    }
}

// Responsive view
@media screen and (max-width:768px) {
    .feedback_modal{
        .icon_img{
            width: 10px;
            height: 24px;
        }
        .feedback_title{
            font-size: 12px;
        }
        .feedback_button{
            padding: 0rem 1rem;
            font-size: 11px;
        }
      :global(.ant-modal-content){
        padding: 4px;

        :global(.ant-modal-body){  
            padding: 0.3rem;
            :global(.ant-tabs){
                :global(.ant-tabs-ink-bar){
                    height: 4px;
                }

                :global(.ant-tabs-nav){
                    :global(.ant-tabs-tab){
                        font-size: 10px;
                    }
                    &::before{
                        border-bottom: 4px solid $light-gray;
                    }
                }
            }
        }
      }
    }
}
import { Col, Flex, Row } from "antd";
import { useEffect, useState } from "react";
import AppHeader from "../AppHeader";
import { DetailsCard } from "./DetailsCard";
import { DateCard } from "./DateCard";
import FOR_DAY_IMG from "../../assets/Images/day.png";
import FOR_WEEK_IMG from "../../assets/Images/week.png";
import FOR_MONTH_IMG from "../../assets/Images/month.png";
import READY_IMG from "../../assets/Images/ready.png";
import EMI_IMG from "../../assets/Images/emi.png";
import Style from "./_field.module.scss";
import { AXIOS } from "../../apis/ho-Instance";
import ApplicationLoader from "../ApplicationLoader";
import PropTypes from "prop-types";
import { ALL_CACHE_NAMES } from "../../constant";
import {
  getCacheKey,
  getFromCache,
  storeToCache,
} from "../../utils/cacheHelper";

const CACHE_NAME = ALL_CACHE_NAMES.FIELD_DASHBOARD_CACHE;

export default function FieldDashboard() {
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState();

  const getFoDashboardData = async () => {
    setIsLoading(true);
    // Build cache key for GET request (no params)
    const cacheKey = getCacheKey({ endpoint: "field_officer_dashboard/" });
    // Try to get cached data for instant UI update
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setData(cachedData);
      setIsLoading(false);
    }
    try {
      const response = await AXIOS.get("field_officer_dashboard/");
      setData(response.data);
      // Store the latest data in cache
      await storeToCache({
        cacheName: CACHE_NAME,
        key: cacheKey,
        data: response.data,
      });
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    getFoDashboardData();
  }, []);

  return (
    <Flex gap={20} vertical>
      <AppHeader title={"Dashboard"} isDashboard={true} />
      <div>
        {isLoading ? (
          <ApplicationLoader />
        ) : (
          <Row
            justify="space-evenly"
            gutter={[50, 20]}
            className={Style.details_row}
          >
            <Col md={11} xs={24}>
              {" "}
              <DashboardCards data={data} />
            </Col>

            <Col
              style={{ borderRight: "1px dashed black", padding: "0px" }}
            ></Col>

            <Col md={11} xs={24}>
              <DashboardDateCards data={data} />
            </Col>
          </Row>
        )}
      </div>
    </Flex>
  );
}

const DashboardCards = ({ data }) => (
  <Row gutter={[0, 60]} justify="space-between" className={Style.details_row}>
    <Col span={24}>
      <DetailsCard
        cardName="Ready to Pay"
        percentValue={data?.ReadyToPay?.R_percentage || 0}
        customerCount={data?.ReadyToPay?.TotalCustomer || 0}
        totalAmount={data?.ReadyToPay?.TotalAmount || 0}
        amountCollected={data?.ReadyToPay?.AmountCollected || 0}
        image={READY_IMG}
      />
    </Col>
    <Col span={24}>
      <DetailsCard
        cardName="EMI Collection"
        percentValue={data?.EMIcollection?.E_percentage || 0}
        customerCount={data?.EMIcollection?.TotalCustomer || 0}
        totalAmount={data?.EMIcollection?.TotalAmount || 0}
        amountCollected={data?.EMIcollection?.AmountCollected || 0}
        image={EMI_IMG}
      />
    </Col>
  </Row>
);
// Define the cards props
DashboardCards.propTypes = {
  data: PropTypes.shape({
    ReadyToPay: PropTypes.shape({
      R_percentage: PropTypes.number,
      TotalCustomer: PropTypes.number,
      TotalAmount: PropTypes.number,
      AmountCollected: PropTypes.number,
    }),
    EMIcollection: PropTypes.shape({
      E_percentage: PropTypes.number,
      TotalCustomer: PropTypes.number,
      TotalAmount: PropTypes.number,
      AmountCollected: PropTypes.number,
    }),
  }),
};

const DashboardDateCards = ({ data }) => (
  <Row justify="space-between" gutter={[0, 55]} className={Style.details_row}>
    <DateCard
      emiCollectionAmount={data?.ForDay?.EMIcollection || 0}
      readyToPayAmount={data?.ForDay?.ReadyToPay || 0}
      typeName="For Day"
      image={FOR_DAY_IMG}
      imageWidth="170px"
      routeKey="day"
    />
    <DateCard
      emiCollectionAmount={data?.ForWeek?.EMIcollection || 0}
      readyToPayAmount={data?.ForWeek?.ReadyToPay || 0}
      typeName="For week"
      image={FOR_WEEK_IMG}
      imageWidth="180px"
      routeKey="week"
    />
    <DateCard
      emiCollectionAmount={data?.ForMonth?.EMIcollection || 0}
      readyToPayAmount={data?.ForMonth?.ReadyToPay || 0}
      typeName="For Month"
      image={FOR_MONTH_IMG}
      imageWidth="120px"
      routeKey="month"
    />
  </Row>
);
// Define the date cards props
DashboardDateCards.propTypes = {
  data: PropTypes.shape({
    ForDay: PropTypes.shape({
      EMIcollection: PropTypes.number,
      ReadyToPay: PropTypes.number,
    }),
    ForWeek: PropTypes.shape({
      EMIcollection: PropTypes.number,
      ReadyToPay: PropTypes.number,
    }),
    ForMonth: PropTypes.shape({
      EMIcollection: PropTypes.number,
      ReadyToPay: PropTypes.number,
    }),
  }),
};

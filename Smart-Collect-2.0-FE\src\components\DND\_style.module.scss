@import url("../../index.css");

$table-radius: 22px;
$attempts-bg: #bdd1ff;
$disable: #787777;
$body: #e4f8f9;

.download_button {
  background-color: var(--dark-blue);
  padding: 0.2rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  margin-right: 1.3rem;
  img {
    width: 20px;
  }
  input[type="image"] {
    width: 20px;
  }
}
.dnd_container {
  .box {
    background-color: #e3f5f6;
    width: 100%;
    border-radius: 10px;
    padding: 1rem;
  }

  .fields_container {
    gap: 10px;
    flex-wrap: wrap;
  }

  .text {
    color: var(--dark-blue);
    font-family: "Kanit", "Courier New", Courier, monospace;
    white-space: nowrap;
  }

  .custom_select {
    width: 200px;
    border-radius: 10px;
    box-shadow: 0px 4px 4px 0px #00000040;

    :global(.ant-select-selector) {
      color: var(--dark-blue);
      border: none !important;
      box-shadow: none !important;
      font-family: "Kanit", "Courier New", Courier, monospace;
    }
    :global(.ant-select-selection-item) {
      font-weight: 400;
      background-color: var(--dark-blue);
      color: white;
      font-size: 10px;
    }
    :global(.ant-select-selection-item-remove) {
      color: white;
      font-size: 9px;
    }
    :global(.ant-select-selection-placeholder) {
      color: #0f205052;
      font-weight: 400;
    }
  }

  .custom_button,
  .add_button {
    background: var(--dark-blue) !important;
    color: white !important;
    font-family: "Kanit", "Courier New", Courier, monospace;
    font-weight: normal;
    box-shadow: 5px 5px 5px 0px #00000040;
    border: none;
    outline: none;
  }

  .add_button {
    padding: 1.2rem 4rem;
  }

  :global(.ant-checkbox) {
    :global(.ant-checkbox-inner) {
      border: 1px solid black;
      border-radius: 0px;

      &::after {
        background-color: white;
      }
    }
  }
  :global(.ant-checkbox-checked) {
    :global(.ant-checkbox-inner) {
      background-color: white !important;
      border: 1px solid black !important;
      border-radius: 0px;
      &::after {
        border-color: #02951a;
        background-color: white;
      }
      &:hover {
        border: 1px solid black;
      }
    }
  }
}

// Set the icon
:global(.ant-select-item-option-state) {
  :global(.anticon-check) {
    svg {
      fill: var(--dark-blue);
    }
  }
}

.customTable {
  padding-top: 1.5rem;
  .name {
    font-weight: 500;
    white-space: nowrap;
    color: var(--dark-blue);
  }

  .icon_div {
    display: flex;
    justify-content: center;
    width: 100%;
    .icon_img {
      width: 15px;
      height: 15px;
      img {
        width: 100%;
        object-fit: contain;
        cursor: pointer;
      }
    }
  }

  .status {
    font-weight: 600;
  }

  //Global ant design classes
  // Pagination
  :global(.ant-pagination) {
    justify-content: center;
    margin: 0 !important;

    :global(.ant-pagination-prev),
    :global(.ant-pagination-next) {
      color: var(--dark-blue);
      border: 0 solid var(--blue);
      background: #e4f8f9;
      height: 25px;
      min-width: 15px;
      border-radius: 0px;
      margin: 0;
      button {
        border-radius: 0px;
      }
    }
    :global(.ant-pagination-item) {
      margin-right: 0;
      a {
        color: $disable;
        font-size: 0.9rem;
        line-height: 23px;
        font-family: "Kanit", "Courier New", Courier, monospace;
        font-weight: normal;
      }
      &:hover {
        background-color: transparent;
      }
    }
    :global(.ant-pagination-item-active) {
      border: none;
      a {
        color: var(--dark-blue);
        font-size: 1.2rem;
        padding: 0;
      }
    }
  }

  //Table container
  :global(.ant-table-container) {
    padding: 0;
    margin-bottom: 0.5rem;
    border-start-start-radius: $table-radius;
    border-start-end-radius: $table-radius;

    // Table header
    :global(.ant-table-header) {
      position: relative;
      margin: 0 auto;
      top: -21px;
      border-radius: $table-radius;
      :global(.ant-table-column-has-sorters) {
        background-color: var(--dark-blue);
        &:hover {
          background-color: var(--dark-blue);
        }

        :global(.ant-table-column-sorter-up),
        :global(.ant-table-column-sorter-down) {
          svg {
            fill: white;
          }
          &:global(.active) {
            svg {
              fill: rgb(24, 155, 249);
            }
          }
        }
      }
    }

    // Table virtual body
    :global(.ant-table-tbody-virtual) {
      margin-top: -8px;
    }

    &::before {
      box-shadow: none !important;
    }
  }

  // Table rows header
  :global(.ant-table-thead > tr) {
    th {
      border-start-end-radius: 0 !important;
      background-color: var(--dark-blue);
      border-inline-end: none !important;
      color: white;
      border-bottom: none;
      text-align: center;
      font-family: "Kanit", "Courier New", Courier, monospace;
      font-weight: normal;

      &:not(:last-child):not(.ant-table-selection-column):not(
          .ant-table-row-expand-icon-cell
        ):not([colspan])::before {
        width: 0;
      }
    }
  }

  // Table body
  :global(.ant-table-tbody) {
    // Body rows
    :global(.ant-table-row) {
      &:nth-child(even) {
        background-color: var(--light-green);
        border-radius: 8px;
      }
      :global(.ant-table-cell-row-hover) {
        background-color: transparent;
      }
      // Cols
      :global(.ant-table-cell) {
        font-weight: 600;
        text-align: center;
        border-bottom: 2px solid white;

        :global(.ant-typography) {
          color: var(--dark-blue);
          font-family: "Kanit", "Courier New", Courier, monospace;
          font-weight: normal;
        }

        &:global(.ant-table-cell-fix-right) {
          border-bottom: none !important;
        }
        &:global(.ant-table-cell-fix-right-first) {
          border-left: 2px solid white;
        }
      }
    }
  }

  // Fixed Cols
  :global(.ant-table-cell-fix-right) {
    background-color: $body;
  }
}

// Responsive View
@media only screen and (max-width: 768px) {
  .dnd_container {
    .box {
      display: flex;
      flex-direction: column;
      align-items: start;

      .fields_container {
        flex-direction: column;
        width: 100%;
      }
      .custom_select {
        width: 100%;
        font-size: 11px;
      }
      .custom_button {
        font-size: 11px;
        height: auto;
        padding: 0.3rem 0.6rem;
      }
    }

    .text {
      font-size: 11px;
    }

    :global(.ant-checkbox) {
      :global(.ant-checkbox-inner) {
        width: 14px;
        height: 14px;
      }
    }
    :global(.ant-checkbox-checked) {
      :global(.ant-checkbox-inner) {
        &::after {
          height: 8.142857px;
        }
      }
    }
  }

  .download_button {
    img,
    input[type="image"] {
      width: 11px;
    }
  }

  .customTable {
    // Table rows header
    :global(.ant-table-thead > tr) {
      th {
        font-size: 12px;
        padding: 0.5rem;
      }
    }
    :global(.ant-table-tbody) {
      :global(.ant-table-row) {
        :global(.ant-table-cell) {
          padding: 0.6rem;
          font-size: 12px !important;
          :global(.ant-typography) {
            font-size: 12px !important;
          }
        }
      }
    }

    .icon_div {
      .icon_img {
        width: 13px;
        height: 13px;
      }
    }
  }
}

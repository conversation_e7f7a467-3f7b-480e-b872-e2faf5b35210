import { Flex, Modal, Typography } from 'antd'
import React from 'react'
import SUCCESS_ICON_IMG from "../../../../assets/Images/success.svg";
import Style from './_ready.module.scss';
import PropTypes from 'prop-types';

const {Text} = Typography;

export default function SuccessModal({modalStatus, setModalStatus}) {
    return (
  <Modal
      centered
      className={Style.success_modal}
      title={null}
      open={modalStatus}
      onOk={()=>{ setModalStatus(false)}}
      onCancel={()=>{ setModalStatus(false)}}
      footer={null}
      width={350}
    >
      <Flex justify='center' align='center' vertical gap={10}>
        <div className={Style.img_div}>
          <img src={SUCCESS_ICON_IMG} alt="queue" />
        </div>
        <Text className={Style.queue_title} style={{color:"#02951A"}}>Payment Info Send and Promise Created Successfully!</Text>
      </Flex>
  </Modal>
    )
  }

SuccessModal.propTypes={
  modalStatus: PropTypes.bool,
  setModalStatus: PropTypes.func
}
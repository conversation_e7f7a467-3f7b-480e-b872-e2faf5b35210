import { Table, Typography } from 'antd';
import React, { useState,useEffect } from 'react'
import { handleDateFormatter } from '../../../constant';
import Style from "./_style.module.scss";
import PropTypes from 'prop-types';

const {Text} = Typography;

export default function DateWiseCommunication({datewiseCommData}) { 
  const [data, setData] = useState([]);

  const columns = [
    {
        title: 'Sr. No.',
        dataIndex: 'key',
        rowScope: 'row',
        width:90,
        render:(_, data)=> <Text>{data.key}.</Text>
    },
    {
        title:'Communication Date',
        dataIndex: 'CommunicationDate',
        sorter: (a, b) => a.CommunicationDate?.localeCompare(b.CommunicationDate),
        render:(CommunicationDate)=> <Text>{CommunicationDate? handleDateFormatter(CommunicationDate):"--"}</Text>,
    },
    {
        title:'Branch Name',
        dataIndex: 'BranchName',
        sorter: (a, b) => a.BranchName?.localeCompare(b.<PERSON><PERSON>ame),
        render:(BranchName)=> <Text>{BranchName || "--"}</Text>
    },
    {
        title:'Customer ID',
        dataIndex: 'CustomerID',
        sorter: (a, b) => (a.CustomerID || 0)- (b.CustomerID ||0),
        render:(CustomerID)=> <Text>{CustomerID}</Text>
    },
    {
        title: 'Disbursement ID',
        dataIndex: 'DisbursementID', 
        sorter: (a, b) => (a.DisbursementID || 0) - (b.DisbursementID || 0),
        render:(DisbursementID)=> <Text>{DisbursementID}</Text>
    },
    {
        title:'Loan Type',
        dataIndex: 'LoanType',
        sorter: (a, b) => a.LoanType?.localeCompare(b.LoanType),
        render:(LoanType)=> <Text>{LoanType || "--"}</Text>
    },
    {
        title: 'Whatsapp Sent',
        dataIndex: 'WhatsAppCount',
        sorter: (a, b) => (a.WhatsAppCount || 0) - (b.WhatsAppCount || 0),
        render:(WhatsAppCount)=> <Text>{WhatsAppCount}</Text>
    },
    {
        title: 'Blaster Sent',
        dataIndex: 'BlasterCount',
        sorter: (a, b) => (a.BlasterCount ||0) - (b.BlasterCount ||0),
        render:(BlasterCount)=> <Text>{BlasterCount}</Text>
    },
    {
        title:'AI Call Sent',
        dataIndex: 'aicount',
        sorter: (a, b) => (a.aicount || 0) - (b.aicount || 0),
        render:(aicount)=> <Text>{aicount}</Text>
    },
    {
        title:'IVR Sent',
        dataIndex: 'IVRCount',
        sorter: (a, b) => (a.IVRCount || 0) - (b.IVRCount || 0),
        render:(IVRCount)=> <Text>{IVRCount}</Text>
    },
    {
        title: 'SMS Sent',
        dataIndex: 'SMSCount',
        sorter: (a, b) =>( a.SMSCount|| 0) - (b.SMSCount|| 0),
        render:(SMSCount)=> <Text>{SMSCount}</Text>
    },
  ];      

  const dataSource = Array.isArray(data)? data.map((d, i) => ( {
    key: i+1,
    ...d
  })):[];

  useEffect(()=>{
    setData(datewiseCommData);
  },[datewiseCommData])
  return (
    <Table
    virtual
    className={Style.customTable}
    columns={columns}
    dataSource={dataSource}
    scroll={{
    x: 2000,
    y: 300,
    }}
    pagination={{
      showSizeChanger:false
    }}
    />
  )
}

DateWiseCommunication.propTypes={
  datewiseCommData: PropTypes.array
}
import React, { useState } from "react";
import { But<PERSON>, Flex, message, Segmented, Table, Typography } from "antd";
import { ALL_CACHE_NAMES, handleDownloadTable } from "../../../constant";
import ApplicationLoader from ".././../ApplicationLoader";
import { AXIOS } from "../../../apis/ho-Instance";
import Style from "./_style.module.scss";
import DOWNLOAD_IMG from "../../../assets/Images/download.svg";
import {
  getCacheKey,
  getFromCache,
  getPostCacheKey,
  storeToCache,
} from "../../../utils/cacheHelper";
import PropTypes from "prop-types";
import { LeftOutlined } from "@ant-design/icons";

const { Text } = Typography;
const CACHE_NAME = ALL_CACHE_NAMES.REPORTS_CACHE;

export function ChannelDetailsTable({ setShowDetailsTable }) {
  const [data, setData] = useState([]);
  const [prevData, setPrevData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [detailsView, setDetailsView] = useState("Date wise");

  // Get state
  const handleGetState = async () => {
    setIsLoading(true);
    const cacheKey = getCacheKey({
      endpoint: "collection-analysis/",
    });
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setData(cachedData);
      setPrevData(cachedData);
      setIsLoading(false);
    }
    try {
      const response = await AXIOS.get("collection_analysis/", {
        params: params,
      });
      if (
        response.status === 200 &&
        Array.isArray(response.data?.data) &&
        response.data?.data?.length
      ) {
        setData(response.data?.data);
        setPrevData(response.data?.data);
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data?.data,
        });
      } else {
        setData([]);
        setPrevData([]);
      }
    } catch (error) {
      console.log("Error in EMI", error?.message);
      setData([]);
      setPrevData([]);
    } finally {
      setIsLoading(false);
    }
  };

  //Get Region, branch, CO.
  const handleGetOtherColsData = async ({ BUType, BUName }) => {
    setIsLoading(true);
    const cacheKey = getPostCacheKey({
      endpoint: "collection-analysis/",
    });
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setData(cachedData);
      setPrevData(cachedData);
      setIsLoading(false);
    }
    try {
      const res = await AXIOS.post("collection_analysis/", body);
      if (res.status === 200 && res.data?.length && Array.isArray(res.data)) {
        setData(res.data);
        setPrevData(res.data);
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: res.data,
        });
      } else {
        message.warning(`Sorry, there is not data related to ${BUName}`);
        setData(prevData);
      }
    } catch (error) {
      console.log("Error in Promise table", error?.message);
      message.warning(`No data found for ${BUName}`);
      setData(prevData);
    } finally {
      setIsLoading(false);
    }
  };

  // Extract dynamic keys from the first item of the API response (excluding lan & amount)
  const dynamicKeys = data?.length
    ? Object.keys(data[0]).filter(
        (key) =>
          ![
            "lan",
            "amount",
            "total_whatsapp",
            "total_AI_Call",
            "total_IVR",
            "total_Email",
            "total_SMS",
            "total_Dialers",
            "total_Blaster_Call",
          ].includes(key)
      )
    : [];

  // Create dynamic column definitions
  const dynamicColumns = dynamicKeys.map((key) => ({
    title: key,
    dataIndex: key,
    sorter: (a, b) => a[key]?.localeCompare?.(b[key]),
    render: (_, record) => (
      <Text
        className={Style.blueText}
        onClick={() =>
          handleGetOtherColsData({ BUType: key, BUName: record[key] })
        }
      >
        {record[key] || "--"}
      </Text>
    ),
  }));

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      render: (_, data) => <Text>{data?.key}.</Text>,
    },
    ...dynamicColumns,
    {
      title: "Promise",
      dataIndex: "promise",
      render: (value) => <Text>{value || 0}</Text>,
    },
    {
      title: "Denial",
      dataIndex: "denial",
      render: (value) => <Text>{value ?? 0}</Text>,
    },
    {
      title: "Claims",
      dataIndex: "claims",
      render: (value) => <Text>{value ?? 0}</Text>,
    },
    {
      title: "Wrong Number",
      dataIndex: "wrongNumber",
      render: (value) => <Text>{value ?? 0}</Text>,
    },
  ];

  const dataSource = Array.isArray(data)
    ? data.map((data, i) => ({ key: i + 1, ...data }))
    : [];

  // Handle downloading
  const handleDownload = async () => {
    if (data.length === 0) {
      return message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: "Collection Analysis",
        worksheetName: "Collection-Analysis",
        tableData: data,
      });
    }
  };

  return (
    <Flex vertical gap={20} className={Style.container}>
      <Flex vertical gap={10}>
        <Flex justify="space-between" align="center">
          <Button
            type="link"
            className={Style.back_btn}
            onClick={() => setShowDetailsTable(false)}
            icon={<LeftOutlined />}
          >
            Summary
          </Button>
          <Segmented
            value={detailsView}
            onChange={(value) => setDetailsView(value)}
            options={["Date wise", "As on date"]}
          />
          <button className={Style.download_button} onClick={handleDownload}>
            <img src={DOWNLOAD_IMG} alt="download-button" />
          </button>
        </Flex>

        {isLoading ? (
          <ApplicationLoader />
        ) : (
          <Table
            bordered
            virtual
            className={Style.custom_table}
            columns={columns}
            dataSource={dataSource}
            scroll={{
              x: 1600,
              y: 460,
            }}
            pagination={{
              showSizeChanger: false,
            }}
          />
        )}
      </Flex>
    </Flex>
  );
}

ChannelDetailsTable.propTypes = {
  setShowDetailsTable: PropTypes.func,
};

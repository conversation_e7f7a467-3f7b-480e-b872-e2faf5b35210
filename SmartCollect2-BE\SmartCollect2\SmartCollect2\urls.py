"""
URL configuration for SmartCollect2 project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import path
from django.urls import path, include
from WebApp.urls import urlpatterns as webapp_urls
from WebApp.urls import swaggerurls as webapp_swaggerurls
from campaign.urls import urlpatterns as campaign_urls
from Channels.urls import urlpatterns as channel_urls
from WebApp.views import GenerateTokenForUser
from rest_framework_simplejwt import views as jwt_views
from drf_spectacular.views import SpectacularAPIView, SpectacularRedocView, SpectacularSwaggerView


PREFIX_CONSTANT = 'backend/api/'

swaggerurls = campaign_urls + webapp_swaggerurls

unencrypted_urls = [
    # YOUR PATTERNS
    path(f"{PREFIX_CONSTANT}schema/", SpectacularAPIView.as_view(urlconf=swaggerurls), name='schema'),
    path(f"{PREFIX_CONSTANT}schema/swagger-ui/", SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    path(f"{PREFIX_CONSTANT}schema/redoc/", SpectacularRedocView.as_view(url_name='schema'), name='redoc'),
]


# Add urlpatterns to urlpatterns
urlpatterns = [
    path(f"{PREFIX_CONSTANT}token/generate/", GenerateTokenForUser.as_view(), name="token_obtain_pair"),
    path(f"{PREFIX_CONSTANT}token/refresh/", jwt_views.TokenRefreshView.as_view(), name="token_refresh"),
    path(f"{PREFIX_CONSTANT}token/verify/", jwt_views.TokenVerifyView.as_view(), name="token_verify"),
    path(f"{PREFIX_CONSTANT}", include(campaign_urls)),
    path(f"{PREFIX_CONSTANT}", include(webapp_urls)),
    path(f"{PREFIX_CONSTANT}", include(channel_urls)),
] + unencrypted_urls

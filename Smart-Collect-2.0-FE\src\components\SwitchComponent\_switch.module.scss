@import url("../../index.css");

.switch {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 120px;
  height: 35px;
  cursor: pointer;

  input[type="checkbox"] {
    display: none;
  }

  .switch_label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 50px;
    background-color: var(--off-color, #0f2050);
    transition: background-color 0.3s ease-in-out;
    overflow: hidden;
    cursor: pointer;

    &:global(.on) {
      background-color: var(--on-color, #e4f8f9);
    }

    .switch_track {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: 100%;
      height: 100%;
      color: var(--on-color);
      font-size: 1rem;
      font-weight: 500;
      margin-right: 15px;
      font-family: 'Kanit','Courier New', Courier, monospace;

      &:global(.on) {
        color: var(--off-color);
        justify-content: flex-start;
        margin-left: 15px;
      }
    }

    .switch_thumb {
      cursor: pointer;
      position: absolute;
      left: 6px;
      width: 30px;
      height: 30px;
      background-color: var(--on-color);
      border-radius: 50%;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      transition: transform 0.3s ease-in-out;

      &:global(.on) {
        transform: translateX(80px);
        background-color: var(--off-color);
      }
    }
  }
}

.snap{
  width: 130px;

  .switch_label{
    .switch_track{
      font-size: 14px;
    }
    .switch_thumb{
      &:global(.on) {
        transform: translateX(90px);
        background-color: var(--off-color);
      }
    }
  }
}

// Responsive view
@media screen and (max-width:768px) {
  .switch{
    width: 90px;
    height: 30px;

    .switch_label{
      .switch_track{
        font-size: 14px;
        margin-right: 10px;

        &:global(.on) {
          margin-left: 9px;
        }
      }

      .switch_thumb{
        left: 1px;
        width: 30px;
        height: 30px;

        &:global(.on){
          transform: translateX(58px);
        }
      }
    }
    
  }
}

@media screen and (max-width:768px) {
  .switch{
    width: 49px;
    height: 15px;

    .switch_label{
      .switch_track{
        font-size: 9px;
        margin-right: 9px;

        &:global(.on) {
          margin-left: 6px;
        }
      }

      .switch_thumb{
        left: 1px;
        width: 14px;
        height: 14px;

        &:global(.on){
          transform: translateX(33px);
        }
      }
    }
    
  }
}
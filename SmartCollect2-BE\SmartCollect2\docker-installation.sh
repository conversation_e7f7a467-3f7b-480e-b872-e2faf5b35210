#!/bin/bash
# Docker Installation sh

# Exit on error
set -e

echo "Removing old Docker packages..."
for pkg in docker.io docker-doc docker-compose docker-compose-v2 podman-docker containerd runc; do
  if dpkg -l | grep -q "^ii\s\+$pkg"; then
    echo "Removing $pkg..."
    sudo apt-get remove -y "$pkg"
  else
    echo "$pkg is not installed, skipping."
  fi
done

echo "Updating system and installing prerequisites..."
sudo apt-get update
sudo apt-get install -y ca-certificates curl

echo "Adding Docker's official GPG key..."
sudo install -m 0755 -d /etc/apt/keyrings
sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
sudo chmod a+r /etc/apt/keyrings/docker.asc

echo "Adding Docker repository to Apt sources..."
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu \
  $(. /etc/os-release && echo "${UBUNTU_CODENAME:-$VERSION_CODENAME}") stable" | \
  sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

echo "Updating package index and installing Docker components..."
sudo apt-get update
sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

echo "Running Docker hello-world test..."
sudo docker run hello-world

echo "Adding current user to the docker group..."
sudo groupadd -f docker
sudo usermod -aG docker "$USER"

echo "Applying new group membership..."
newgrp docker << END
docker run hello-world
END

echo "Docker installation complete."

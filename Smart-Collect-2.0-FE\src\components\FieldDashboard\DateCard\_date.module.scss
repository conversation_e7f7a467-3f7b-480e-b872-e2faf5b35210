@import url("../../../index.css");

$light-blue: #E3F5F6;
$sky-blue: #407BFF;

.date_container{
    background-color: $light-blue;
    padding: 0rem 2rem 0rem 1rem!important;
    border-radius: 10px;
    box-shadow: 0px 4px 4px 0px #00000040;
    .name{
        margin: 0;
        text-align: end;
        font-weight: 500;
        color: var(--dark-blue);
        font-family: 'Kanit','Courier New', Courier, monospace;
    }
    .amount{
        color: var(--dark-blue);
        font-weight: 600;
        font-size: 17px;
        font-family: 'Kanit','Courier New', Courier, monospace;
    }
    .text{
        a{
            text-decoration: underline;
            color: $sky-blue;
            font-weight: 400;
            font-size: 16px;
            font-family: 'Kanit','Courier New', Courier, monospace;
        }
    }
    .image_container{
        img{
            object-fit: cover;
            width: 100%;
            height: 150px;
        }
    }
}

// Responsive view
@media screen and (max-width:1024px) {
    .date_container{
        .name{
            font-size: 20px;
        }

        .amount{
            font-size: 14px;
        }

        .text{
            a{
              font-size: 14px; 
              text-align: center;  
            }
        }

        .image_container{
            width: 100px;
            display: flex;
            justify-content: center;
            align-items: center;

            img{
                height: 100px;
            }
        }
    }
}

@media screen and (max-width:768px) {
    .date_container{
        .name{
            font-size: 16px;
        }

        .amount{
            font-size: 12px;
        }

        .text{
            a{
              font-size: 11px; 
              text-align: center; 
              white-space: nowrap; 
            }
        }

        .image_container{
            width: 160px;
            display: flex;
            justify-content: center;
            align-items: center;
            img{
                object-fit: cover;
            }
        }
    }
}
# Dependencies
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log

# Testing
coverage
.nyc_output

# Environment
# .env
.env.local
.env.*.local

# Editor directories and files
.vscode
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Build output
dist
build

# Version control
.git
.gitignore

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log
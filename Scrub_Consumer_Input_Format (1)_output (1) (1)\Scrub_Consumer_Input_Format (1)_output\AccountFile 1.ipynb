{"cells": [{"cell_type": "code", "execution_count": 1, "id": "e38aa0c3", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "id": "310ba126", "metadata": {}, "outputs": [], "source": ["from sqlalchemy import create_engine"]}, {"cell_type": "code", "execution_count": 3, "id": "763b83a8", "metadata": {}, "outputs": [], "source": ["engine = create_engine('***************************************************************')"]}, {"cell_type": "code", "execution_count": 4, "id": "196b194b", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv('AccountCorrect 1.csv')"]}, {"cell_type": "code", "execution_count": 5, "id": "84aa7518", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>CREDT-RPT-ID</th>\n", "      <th>LOS-APP-ID</th>\n", "      <th>CANDIDATE - ID</th>\n", "      <th>CUSTOMER ID/MBR ID</th>\n", "      <th>BRANCH</th>\n", "      <th>KENDRA</th>\n", "      <th>SELF-INDICATOR</th>\n", "      <th>MATCH-TYPE</th>\n", "      <th>ACC-NUM</th>\n", "      <th>CREDIT-GRANTOR</th>\n", "      <th>...</th>\n", "      <th>ASSET CLASS - HIST</th>\n", "      <th>HIGH CRD - HIST</th>\n", "      <th>CUR BAL - HIST</th>\n", "      <th>DAS - HIST</th>\n", "      <th>AMT OVERDUE - HIST</th>\n", "      <th>AMT PAID - HIST</th>\n", "      <th>INCOME</th>\n", "      <th>INCOME INDICATOR</th>\n", "      <th>TENURE</th>\n", "      <th>OCCUPATION</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>**********</td>\n", "      <td>NaN</td>\n", "      <td>THASRA MML</td>\n", "      <td>ML0493S15C0410</td>\n", "      <td>False</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>MUTHOOT MICROFIN LTD</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>39044,39044,39044,39044,39044,39044,40739,4073...</td>\n", "      <td>S06S06S06S05S05S05S05S05S05S04S04S04S04XXXS04S...</td>\n", "      <td>21360,18690,16020,13350,10680,8010,8010,5340,2...</td>\n", "      <td>NaN</td>\n", "      <td>25000.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>**********</td>\n", "      <td>NaN</td>\n", "      <td>444</td>\n", "      <td>*********</td>\n", "      <td>False</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>SATYA MICROCAPITAL LIMITED</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>33029,33029,33029,33029,33029,33029,34496,3593...</td>\n", "      <td>S05S05S05S05S05S04S04S04S04S04S04</td>\n", "      <td>5579,4013,3015,3015,1494,0,0,0,0,0,0,</td>\n", "      <td>NaN</td>\n", "      <td>25000.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>9252668405</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>XXXX</td>\n", "      <td>...</td>\n", "      <td>L02L01L01L01L01L01L01L01L01L01</td>\n", "      <td>50000,50000,50000,50000,50000,50000,50000,5000...</td>\n", "      <td>41938,41938,41938,41938,43765,56951,59631,6231...</td>\n", "      <td>S05S05S05S04S04S04S04S04S04S04</td>\n", "      <td>8040,5360,2680,0,0,0,0,0,0,0,</td>\n", "      <td>13400,13400,13400,13400,10720,8040,5360,2680,0,0,</td>\n", "      <td>12000.0</td>\n", "      <td>M</td>\n", "      <td>24.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>6794813831</td>\n", "      <td>NaN</td>\n", "      <td>291</td>\n", "      <td>29100152</td>\n", "      <td>False</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>MIDLAND MICROFIN LTD</td>\n", "      <td>...</td>\n", "      <td>XXXXXXXXX</td>\n", "      <td>,</td>\n", "      <td>0,411,840,</td>\n", "      <td>S07S04S04</td>\n", "      <td>,,0,</td>\n", "      <td>,</td>\n", "      <td>16000.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>6794813831</td>\n", "      <td>NaN</td>\n", "      <td>291</td>\n", "      <td>29100152</td>\n", "      <td>False</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>MIDLAND MICROFIN LTD</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>20446,20446,20446,20446,20446,20446,20446,2044...</td>\n", "      <td>S05S05S05S05S05S05S05S05S05S05S04S04S04S04S04S...</td>\n", "      <td>22817,21799,19619,17439,15259,13080,10900,8720...</td>\n", "      <td>NaN</td>\n", "      <td>16000.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>219</th>\n", "      <td>HIND250222CR5496706210843</td>\n", "      <td>1004</td>\n", "      <td>4978124343</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>XXXX</td>\n", "      <td>...</td>\n", "      <td>XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX...</td>\n", "      <td>73827,73827,73827,73827,73827,73827,73827,7382...</td>\n", "      <td>54389,54389,54389,54389,54389,54389,54389,5438...</td>\n", "      <td>S05S05S05S05S05S05S05S05S05S05S05S05S05S05XXXS...</td>\n", "      <td>63986,61204,58422,55640,52858,50076,47294,4451...</td>\n", "      <td>,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>36.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>220</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>*********</td>\n", "      <td>NaN</td>\n", "      <td>DAKOR</td>\n", "      <td>DAKOR</td>\n", "      <td>False</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>HDFC BANK LTD</td>\n", "      <td>...</td>\n", "      <td>XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX...</td>\n", "      <td>,</td>\n", "      <td>0,0,1659,1659,1659,1659,1659,1659,3283,4875,64...</td>\n", "      <td>S07S04S05S05S05S05S05S05S05S05S05S05S05S05S05S...</td>\n", "      <td>0,0,1693,1693,1693,1693,1693,1693,1693,1693,16...</td>\n", "      <td>,</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>221</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>**********</td>\n", "      <td>NaN</td>\n", "      <td>30</td>\n", "      <td>324</td>\n", "      <td>False</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>HMPL NIDHI LIMITED</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>24272,24272,24272,24272,24272,24272,24272,2427...</td>\n", "      <td>S05S05S05S05S05S05S05S05S05S05S05S05S05S05S05S...</td>\n", "      <td>45751,45174,44597,44039,43462,42904,42327,4175...</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>222</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>*********</td>\n", "      <td>NaN</td>\n", "      <td>016023-TFSL</td>\n", "      <td>002013_0200000374366</td>\n", "      <td>False</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>YES BANK</td>\n", "      <td>...</td>\n", "      <td>XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX...</td>\n", "      <td>,</td>\n", "      <td>0,5664,7525,11123,11123,12861,12861,14568,1456...</td>\n", "      <td>S07S05S05S05S05S05S05S05S04S04S04S05S05S05S04S...</td>\n", "      <td>0,5875,5900,7875,5900,5900,3925,3925,0,0,0,197...</td>\n", "      <td>,</td>\n", "      <td>8333.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>223</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>*********</td>\n", "      <td>NaN</td>\n", "      <td>3084</td>\n", "      <td>3084</td>\n", "      <td>False</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>LNT FINANCE</td>\n", "      <td>...</td>\n", "      <td>XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX...</td>\n", "      <td>,</td>\n", "      <td>0,,1885,1904,3534,5130,4915,8154,14304,9204,92...</td>\n", "      <td>S07XXXS05S04S04S04S04S04S04S04S04S04S04S04S04S...</td>\n", "      <td>0,,1642,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,...</td>\n", "      <td>,</td>\n", "      <td>165000.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>224 rows × 41 columns</p>\n", "</div>"], "text/plain": ["                  CREDT-RPT-ID  LOS-APP-ID  CANDIDATE - ID  \\\n", "0    HIND250222CR5536706210843        1008      **********   \n", "1    HIND250222CR5536706210843        1008      **********   \n", "2    HIND250222CR5536706210843        1008      9252668405   \n", "3    HIND250222CR5536706210843        1008      6794813831   \n", "4    HIND250222CR5536706210843        1008      6794813831   \n", "..                         ...         ...             ...   \n", "219  HIND250222CR5496706210843        1004      4978124343   \n", "220  HIND250222CR5396706210843        1002       *********   \n", "221  HIND250222CR5396706210843        1002      **********   \n", "222  HIND250222CR5396706210843        1002       *********   \n", "223  HIND250222CR5396706210843        1002       *********   \n", "\n", "     CUSTOMER ID/MBR ID       BRANCH                KENDRA  SELF-INDICATOR  \\\n", "0                   NaN   THASRA MML        ML0493S15C0410           False   \n", "1                   NaN          444             *********           False   \n", "2                   NaN          NaN                   NaN           False   \n", "3                   NaN          291              29100152           False   \n", "4                   NaN          291              29100152           False   \n", "..                  ...          ...                   ...             ...   \n", "219                 NaN          NaN                   NaN           False   \n", "220                 NaN        DAKOR                 DAKOR           False   \n", "221                 NaN           30                   324           False   \n", "222                 NaN  016023-TFSL  002013_0200000374366           False   \n", "223                 NaN         3084                  3084           False   \n", "\n", "    MATCH-TYPE ACC-NUM              CREDIT-GRANTOR  ...  \\\n", "0      PRIMARY    XXXX        MUTHOOT MICROFIN LTD  ...   \n", "1      PRIMARY    XXXX  SATYA MICROCAPITAL LIMITED  ...   \n", "2      PRIMARY    XXXX                        XXXX  ...   \n", "3      PRIMARY    XXXX        MIDLAND MICROFIN LTD  ...   \n", "4      PRIMARY    XXXX        MIDLAND MICROFIN LTD  ...   \n", "..         ...     ...                         ...  ...   \n", "219    PRIMARY    XXXX                        XXXX  ...   \n", "220    PRIMARY    XXXX               HDFC BANK LTD  ...   \n", "221    PRIMARY    XXXX          HMPL NIDHI LIMITED  ...   \n", "222    PRIMARY    XXXX                    YES BANK  ...   \n", "223    PRIMARY    XXXX                 LNT FINANCE  ...   \n", "\n", "                                   ASSET CLASS - HIST   \\\n", "0                                                  NaN   \n", "1                                                  NaN   \n", "2                       L02L01L01L01L01L01L01L01L01L01   \n", "3                                            XXXXXXXXX   \n", "4                                                  NaN   \n", "..                                                 ...   \n", "219  XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX...   \n", "220  XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX...   \n", "221                                                NaN   \n", "222  XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX...   \n", "223  XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX...   \n", "\n", "                                      HIGH CRD - HIST   \\\n", "0                                                  NaN   \n", "1                                                  NaN   \n", "2    50000,50000,50000,50000,50000,50000,50000,5000...   \n", "3                                                    ,   \n", "4                                                  NaN   \n", "..                                                 ...   \n", "219  73827,73827,73827,73827,73827,73827,73827,7382...   \n", "220                                                  ,   \n", "221                                                NaN   \n", "222                                                  ,   \n", "223                                                  ,   \n", "\n", "                                       CUR BAL - HIST   \\\n", "0    39044,39044,39044,39044,39044,39044,40739,4073...   \n", "1    33029,33029,33029,33029,33029,33029,34496,3593...   \n", "2    41938,41938,41938,41938,43765,56951,59631,6231...   \n", "3                                           0,411,840,   \n", "4    20446,20446,20446,20446,20446,20446,20446,2044...   \n", "..                                                 ...   \n", "219  54389,54389,54389,54389,54389,54389,54389,5438...   \n", "220  0,0,1659,1659,1659,1659,1659,1659,3283,4875,64...   \n", "221  24272,24272,24272,24272,24272,24272,24272,2427...   \n", "222  0,5664,7525,11123,11123,12861,12861,14568,1456...   \n", "223  0,,1885,1904,3534,5130,4915,8154,14304,9204,92...   \n", "\n", "                                           DAS - HIST   \\\n", "0    S06S06S06S05S05S05S05S05S05S04S04S04S04XXXS04S...   \n", "1                    S05S05S05S05S05S04S04S04S04S04S04   \n", "2                       S05S05S05S04S04S04S04S04S04S04   \n", "3                                            S07S04S04   \n", "4    S05S05S05S05S05S05S05S05S05S05S04S04S04S04S04S...   \n", "..                                                 ...   \n", "219  S05S05S05S05S05S05S05S05S05S05S05S05S05S05XXXS...   \n", "220  S07S04S05S05S05S05S05S05S05S05S05S05S05S05S05S...   \n", "221  S05S05S05S05S05S05S05S05S05S05S05S05S05S05S05S...   \n", "222  S07S05S05S05S05S05S05S05S04S04S04S05S05S05S04S...   \n", "223  S07XXXS05S04S04S04S04S04S04S04S04S04S04S04S04S...   \n", "\n", "                                   AMT OVERDUE - HIST   \\\n", "0    21360,18690,16020,13350,10680,8010,8010,5340,2...   \n", "1                5579,4013,3015,3015,1494,0,0,0,0,0,0,   \n", "2                        8040,5360,2680,0,0,0,0,0,0,0,   \n", "3                                                 ,,0,   \n", "4    22817,21799,19619,17439,15259,13080,10900,8720...   \n", "..                                                 ...   \n", "219  63986,61204,58422,55640,52858,50076,47294,4451...   \n", "220  0,0,1693,1693,1693,1693,1693,1693,1693,1693,16...   \n", "221  45751,45174,44597,44039,43462,42904,42327,4175...   \n", "222  0,5875,5900,7875,5900,5900,3925,3925,0,0,0,197...   \n", "223  0,,1642,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,...   \n", "\n", "                                      AMT PAID - HIST    INCOME   \\\n", "0                                                  NaN   25000.0   \n", "1                                                  NaN   25000.0   \n", "2    13400,13400,13400,13400,10720,8040,5360,2680,0,0,   12000.0   \n", "3                                                    ,   16000.0   \n", "4                                                  NaN   16000.0   \n", "..                                                 ...       ...   \n", "219               ,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,       0.0   \n", "220                                                  ,       NaN   \n", "221                                                NaN       0.0   \n", "222                                                  ,    8333.0   \n", "223                                                  ,  165000.0   \n", "\n", "     INCOME INDICATOR  TENURE   OCCUPATION  \n", "0                    M     NaN         NaN  \n", "1                    M     NaN         NaN  \n", "2                    M    24.0         NaN  \n", "3                    M     NaN         NaN  \n", "4                    M     NaN         NaN  \n", "..                 ...     ...         ...  \n", "219                NaN    36.0         NaN  \n", "220                NaN     NaN         NaN  \n", "221                  M     NaN         NaN  \n", "222                  M     NaN         NaN  \n", "223                  M     NaN         NaN  \n", "\n", "[224 rows x 41 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 6, "id": "059a86cb", "metadata": {}, "outputs": [], "source": ["total_records = len(df)"]}, {"cell_type": "code", "execution_count": 7, "id": "4189f5da", "metadata": {}, "outputs": [], "source": ["\n", "def check_columns_credit_summary(df) :\n", "    df = df.loc[:, ~df.columns.str.contains('^Unnamed')]\n", "    def clean_column(col):\n", "        return col.strip().lower().replace('-', '_').replace(' ', '_').replace('/', '_')\n", "    df.columns = [clean_column(col) for col in df.columns]\n", "    df = df.rename(columns={\n", "        'candidate___id': 'candidate_id',\n", "        'branch': 'Branch_name',\n", "        'reported_date___hist': 'reported_date_hist',\n", "        'dpd___hist': 'dpd_hist',\n", "        'asset_class___hist': 'asset_class_hist',\n", "        'high_crd___hist': 'high_crd_hist',\n", "        'cur_bal___hist': 'cur_bal_hist',\n", "        'das___hist': 'das_hist',\n", "        'amt_overdue___hist': 'amt_overdue_hist',\n", "        'amt_paid___hist': 'amt_paid_hist'\n", "    })\n", " \n", "    required_columns = ['credt_rpt_id', 'los_app_id', 'candidate_id', 'customer_id_mbr_id',\n", "       'Branch_name', 'kendra', 'self_indicator', 'match_type', 'acc_num',\n", "       'credit_grantor', 'acct_type', 'contributor_type', 'date_reported',\n", "       'ownership_ind', 'account_status', 'disbursed_dt', 'close_dt',\n", "       'last_payment_date', 'credit_limit_sanc_amt',\n", "       'disbursed_amt_high_credit', 'installment_amt', 'current_bal',\n", "       'installment_frequency', 'write_off_date', 'overdue_amt',\n", "       'write_off_amt', 'asset_class', 'account_remarks', 'linked_accounts',\n", "       'reported_date_hist', 'dpd_hist', 'asset_class_hist', 'high_crd_hist',\n", "       'cur_bal_hist', 'das_hist', 'amt_overdue_hist', 'amt_paid_hist',\n", "       'income', 'income_indicator', 'tenure', 'occupation']\n", "    missing_columns = []\n", "    for column in required_columns:\n", "        if column not in df.columns:\n", "            missing_columns.append(column)\n", "    return missing_columns, df\n", "  "]}, {"cell_type": "code", "execution_count": 8, "id": "cb279356", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['CREDT-RPT-ID', 'LOS-APP-ID', 'CANDIDATE - ID', 'CUSTOMER ID/MBR ID',\n", "       'BRANCH', '<PERSON><PERSON><PERSON><PERSON>', 'SELF-INDICATOR', 'MATCH-TYPE', 'ACC-NUM',\n", "       'CREDIT-G<PERSON>NT<PERSON>', 'ACCT-TYPE', 'CONTRIBUTOR-TYPE', 'DATE-REPORTED',\n", "       'OWNERSHIP-IND', 'ACCOUNT-STATUS', 'DISBURSED-DT', 'CLOSE-DT',\n", "       'LAST-PAYMENT-DATE', 'CREDIT-LIMIT/SANC AMT',\n", "       ' DISBURSED-AMT/HIGH CREDIT', ' INSTALLMENT-AMT', ' CURRENT-BAL',\n", "       'INSTALLMENT-FREQUENCY', 'WRITE-OFF-DATE', ' OVERDUE-AMT',\n", "       ' WRITE-OFF-AMT', 'ASSET_CLASS', ' ACCOUNT-REMARKS', 'LINKED-ACCOUNTS',\n", "       'REPORTED DATE - HIST ', 'DPD - HIST', 'ASSET CLASS - HIST ',\n", "       'HIGH CRD - HIST ', 'CUR BAL - HIST ', 'DAS - HIST ',\n", "       'AMT OVERDUE - HIST ', 'AMT PAID - HIST ', 'INCOME ',\n", "       ' INCOME INDICATOR ', 'TENURE ', ' OCCUPATION'],\n", "      dtype='object')"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": 9, "id": "eb59bb75", "metadata": {}, "outputs": [], "source": ["missing, df = check_columns_credit_summary(df)"]}, {"cell_type": "code", "execution_count": 10, "id": "06be521a", "metadata": {}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["missing"]}, {"cell_type": "code", "execution_count": 11, "id": "383f44a7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>credt_rpt_id</th>\n", "      <th>los_app_id</th>\n", "      <th>candidate_id</th>\n", "      <th>customer_id_mbr_id</th>\n", "      <th>Branch_name</th>\n", "      <th>kendra</th>\n", "      <th>self_indicator</th>\n", "      <th>match_type</th>\n", "      <th>acc_num</th>\n", "      <th>credit_grantor</th>\n", "      <th>...</th>\n", "      <th>asset_class_hist</th>\n", "      <th>high_crd_hist</th>\n", "      <th>cur_bal_hist</th>\n", "      <th>das_hist</th>\n", "      <th>amt_overdue_hist</th>\n", "      <th>amt_paid_hist</th>\n", "      <th>income</th>\n", "      <th>income_indicator</th>\n", "      <th>tenure</th>\n", "      <th>occupation</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>**********</td>\n", "      <td>NaN</td>\n", "      <td>THASRA MML</td>\n", "      <td>ML0493S15C0410</td>\n", "      <td>False</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>MUTHOOT MICROFIN LTD</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>39044,39044,39044,39044,39044,39044,40739,4073...</td>\n", "      <td>S06S06S06S05S05S05S05S05S05S04S04S04S04XXXS04S...</td>\n", "      <td>21360,18690,16020,13350,10680,8010,8010,5340,2...</td>\n", "      <td>NaN</td>\n", "      <td>25000.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>**********</td>\n", "      <td>NaN</td>\n", "      <td>444</td>\n", "      <td>*********</td>\n", "      <td>False</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>SATYA MICROCAPITAL LIMITED</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>33029,33029,33029,33029,33029,33029,34496,3593...</td>\n", "      <td>S05S05S05S05S05S04S04S04S04S04S04</td>\n", "      <td>5579,4013,3015,3015,1494,0,0,0,0,0,0,</td>\n", "      <td>NaN</td>\n", "      <td>25000.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>9252668405</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>XXXX</td>\n", "      <td>...</td>\n", "      <td>L02L01L01L01L01L01L01L01L01L01</td>\n", "      <td>50000,50000,50000,50000,50000,50000,50000,5000...</td>\n", "      <td>41938,41938,41938,41938,43765,56951,59631,6231...</td>\n", "      <td>S05S05S05S04S04S04S04S04S04S04</td>\n", "      <td>8040,5360,2680,0,0,0,0,0,0,0,</td>\n", "      <td>13400,13400,13400,13400,10720,8040,5360,2680,0,0,</td>\n", "      <td>12000.0</td>\n", "      <td>M</td>\n", "      <td>24.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>6794813831</td>\n", "      <td>NaN</td>\n", "      <td>291</td>\n", "      <td>29100152</td>\n", "      <td>False</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>MIDLAND MICROFIN LTD</td>\n", "      <td>...</td>\n", "      <td>XXXXXXXXX</td>\n", "      <td>,</td>\n", "      <td>0,411,840,</td>\n", "      <td>S07S04S04</td>\n", "      <td>,,0,</td>\n", "      <td>,</td>\n", "      <td>16000.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>6794813831</td>\n", "      <td>NaN</td>\n", "      <td>291</td>\n", "      <td>29100152</td>\n", "      <td>False</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>MIDLAND MICROFIN LTD</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>20446,20446,20446,20446,20446,20446,20446,2044...</td>\n", "      <td>S05S05S05S05S05S05S05S05S05S05S04S04S04S04S04S...</td>\n", "      <td>22817,21799,19619,17439,15259,13080,10900,8720...</td>\n", "      <td>NaN</td>\n", "      <td>16000.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>219</th>\n", "      <td>HIND250222CR5496706210843</td>\n", "      <td>1004</td>\n", "      <td>4978124343</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>XXXX</td>\n", "      <td>...</td>\n", "      <td>XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX...</td>\n", "      <td>73827,73827,73827,73827,73827,73827,73827,7382...</td>\n", "      <td>54389,54389,54389,54389,54389,54389,54389,5438...</td>\n", "      <td>S05S05S05S05S05S05S05S05S05S05S05S05S05S05XXXS...</td>\n", "      <td>63986,61204,58422,55640,52858,50076,47294,4451...</td>\n", "      <td>,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>36.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>220</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>*********</td>\n", "      <td>NaN</td>\n", "      <td>DAKOR</td>\n", "      <td>DAKOR</td>\n", "      <td>False</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>HDFC BANK LTD</td>\n", "      <td>...</td>\n", "      <td>XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX...</td>\n", "      <td>,</td>\n", "      <td>0,0,1659,1659,1659,1659,1659,1659,3283,4875,64...</td>\n", "      <td>S07S04S05S05S05S05S05S05S05S05S05S05S05S05S05S...</td>\n", "      <td>0,0,1693,1693,1693,1693,1693,1693,1693,1693,16...</td>\n", "      <td>,</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>221</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>**********</td>\n", "      <td>NaN</td>\n", "      <td>30</td>\n", "      <td>324</td>\n", "      <td>False</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>HMPL NIDHI LIMITED</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>24272,24272,24272,24272,24272,24272,24272,2427...</td>\n", "      <td>S05S05S05S05S05S05S05S05S05S05S05S05S05S05S05S...</td>\n", "      <td>45751,45174,44597,44039,43462,42904,42327,4175...</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>222</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>*********</td>\n", "      <td>NaN</td>\n", "      <td>016023-TFSL</td>\n", "      <td>002013_0200000374366</td>\n", "      <td>False</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>YES BANK</td>\n", "      <td>...</td>\n", "      <td>XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX...</td>\n", "      <td>,</td>\n", "      <td>0,5664,7525,11123,11123,12861,12861,14568,1456...</td>\n", "      <td>S07S05S05S05S05S05S05S05S04S04S04S05S05S05S04S...</td>\n", "      <td>0,5875,5900,7875,5900,5900,3925,3925,0,0,0,197...</td>\n", "      <td>,</td>\n", "      <td>8333.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>223</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>*********</td>\n", "      <td>NaN</td>\n", "      <td>3084</td>\n", "      <td>3084</td>\n", "      <td>False</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>LNT FINANCE</td>\n", "      <td>...</td>\n", "      <td>XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX...</td>\n", "      <td>,</td>\n", "      <td>0,,1885,1904,3534,5130,4915,8154,14304,9204,92...</td>\n", "      <td>S07XXXS05S04S04S04S04S04S04S04S04S04S04S04S04S...</td>\n", "      <td>0,,1642,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,...</td>\n", "      <td>,</td>\n", "      <td>165000.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>224 rows × 41 columns</p>\n", "</div>"], "text/plain": ["                  credt_rpt_id  los_app_id  candidate_id  customer_id_mbr_id  \\\n", "0    HIND250222CR5536706210843        1008    **********                 NaN   \n", "1    HIND250222CR5536706210843        1008    **********                 NaN   \n", "2    HIND250222CR5536706210843        1008    9252668405                 NaN   \n", "3    HIND250222CR5536706210843        1008    6794813831                 NaN   \n", "4    HIND250222CR5536706210843        1008    6794813831                 NaN   \n", "..                         ...         ...           ...                 ...   \n", "219  HIND250222CR5496706210843        1004    4978124343                 NaN   \n", "220  HIND250222CR5396706210843        1002     *********                 NaN   \n", "221  HIND250222CR5396706210843        1002    **********                 NaN   \n", "222  HIND250222CR5396706210843        1002     *********                 NaN   \n", "223  HIND250222CR5396706210843        1002     *********                 NaN   \n", "\n", "     Branch_name                kendra  self_indicator match_type acc_num  \\\n", "0     THASRA MML        ML0493S15C0410           False    PRIMARY    XXXX   \n", "1            444             *********           False    PRIMARY    XXXX   \n", "2            NaN                   NaN           False    PRIMARY    XXXX   \n", "3            291              29100152           False    PRIMARY    XXXX   \n", "4            291              29100152           False    PRIMARY    XXXX   \n", "..           ...                   ...             ...        ...     ...   \n", "219          NaN                   NaN           False    PRIMARY    XXXX   \n", "220        DAKOR                 DAKOR           False    PRIMARY    XXXX   \n", "221           30                   324           False    PRIMARY    XXXX   \n", "222  016023-TFSL  002013_0200000374366           False    PRIMARY    XXXX   \n", "223         3084                  3084           False    PRIMARY    XXXX   \n", "\n", "                 credit_grantor  ...  \\\n", "0          MUTHOOT MICROFIN LTD  ...   \n", "1    SATYA MICROCAPITAL LIMITED  ...   \n", "2                          XXXX  ...   \n", "3          MIDLAND MICROFIN LTD  ...   \n", "4          MIDLAND MICROFIN LTD  ...   \n", "..                          ...  ...   \n", "219                        XXXX  ...   \n", "220               HDFC BANK LTD  ...   \n", "221          HMPL NIDHI LIMITED  ...   \n", "222                    YES BANK  ...   \n", "223                 LNT FINANCE  ...   \n", "\n", "                                      asset_class_hist  \\\n", "0                                                  NaN   \n", "1                                                  NaN   \n", "2                       L02L01L01L01L01L01L01L01L01L01   \n", "3                                            XXXXXXXXX   \n", "4                                                  NaN   \n", "..                                                 ...   \n", "219  XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX...   \n", "220  XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX...   \n", "221                                                NaN   \n", "222  XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX...   \n", "223  XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX...   \n", "\n", "                                         high_crd_hist  \\\n", "0                                                  NaN   \n", "1                                                  NaN   \n", "2    50000,50000,50000,50000,50000,50000,50000,5000...   \n", "3                                                    ,   \n", "4                                                  NaN   \n", "..                                                 ...   \n", "219  73827,73827,73827,73827,73827,73827,73827,7382...   \n", "220                                                  ,   \n", "221                                                NaN   \n", "222                                                  ,   \n", "223                                                  ,   \n", "\n", "                                          cur_bal_hist  \\\n", "0    39044,39044,39044,39044,39044,39044,40739,4073...   \n", "1    33029,33029,33029,33029,33029,33029,34496,3593...   \n", "2    41938,41938,41938,41938,43765,56951,59631,6231...   \n", "3                                           0,411,840,   \n", "4    20446,20446,20446,20446,20446,20446,20446,2044...   \n", "..                                                 ...   \n", "219  54389,54389,54389,54389,54389,54389,54389,5438...   \n", "220  0,0,1659,1659,1659,1659,1659,1659,3283,4875,64...   \n", "221  24272,24272,24272,24272,24272,24272,24272,2427...   \n", "222  0,5664,7525,11123,11123,12861,12861,14568,1456...   \n", "223  0,,1885,1904,3534,5130,4915,8154,14304,9204,92...   \n", "\n", "                                              das_hist  \\\n", "0    S06S06S06S05S05S05S05S05S05S04S04S04S04XXXS04S...   \n", "1                    S05S05S05S05S05S04S04S04S04S04S04   \n", "2                       S05S05S05S04S04S04S04S04S04S04   \n", "3                                            S07S04S04   \n", "4    S05S05S05S05S05S05S05S05S05S05S04S04S04S04S04S...   \n", "..                                                 ...   \n", "219  S05S05S05S05S05S05S05S05S05S05S05S05S05S05XXXS...   \n", "220  S07S04S05S05S05S05S05S05S05S05S05S05S05S05S05S...   \n", "221  S05S05S05S05S05S05S05S05S05S05S05S05S05S05S05S...   \n", "222  S07S05S05S05S05S05S05S05S04S04S04S05S05S05S04S...   \n", "223  S07XXXS05S04S04S04S04S04S04S04S04S04S04S04S04S...   \n", "\n", "                                      amt_overdue_hist  \\\n", "0    21360,18690,16020,13350,10680,8010,8010,5340,2...   \n", "1                5579,4013,3015,3015,1494,0,0,0,0,0,0,   \n", "2                        8040,5360,2680,0,0,0,0,0,0,0,   \n", "3                                                 ,,0,   \n", "4    22817,21799,19619,17439,15259,13080,10900,8720...   \n", "..                                                 ...   \n", "219  63986,61204,58422,55640,52858,50076,47294,4451...   \n", "220  0,0,1693,1693,1693,1693,1693,1693,1693,1693,16...   \n", "221  45751,45174,44597,44039,43462,42904,42327,4175...   \n", "222  0,5875,5900,7875,5900,5900,3925,3925,0,0,0,197...   \n", "223  0,,1642,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,...   \n", "\n", "                                         amt_paid_hist    income  \\\n", "0                                                  NaN   25000.0   \n", "1                                                  NaN   25000.0   \n", "2    13400,13400,13400,13400,10720,8040,5360,2680,0,0,   12000.0   \n", "3                                                    ,   16000.0   \n", "4                                                  NaN   16000.0   \n", "..                                                 ...       ...   \n", "219               ,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,       0.0   \n", "220                                                  ,       NaN   \n", "221                                                NaN       0.0   \n", "222                                                  ,    8333.0   \n", "223                                                  ,  165000.0   \n", "\n", "    income_indicator tenure occupation  \n", "0                  M    NaN        NaN  \n", "1                  M    NaN        NaN  \n", "2                  M   24.0        NaN  \n", "3                  M    NaN        NaN  \n", "4                  M    NaN        NaN  \n", "..               ...    ...        ...  \n", "219              NaN   36.0        NaN  \n", "220              NaN    NaN        NaN  \n", "221                M    NaN        NaN  \n", "222                M    NaN        NaN  \n", "223                M    NaN        NaN  \n", "\n", "[224 rows x 41 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 12, "id": "65d0be35", "metadata": {}, "outputs": [], "source": ["df['BankMstID'] = 9"]}, {"cell_type": "code", "execution_count": 13, "id": "58af9b79", "metadata": {}, "outputs": [], "source": ["expected_columns = [\n", "            \"credt_rpt_id\",\n", "            \"los_app_id\",\n", "            \"candidate_id\",\n", "            \"customer_id_mbr_id\",\n", "            \"Branch_name\",\n", "            \"kendra\",\n", "            \"self_indicator\",\n", "            \"match_type\",\n", "            \"acc_num\",\n", "            \"credit_grantor\",\n", "            \"acct_type\",\n", "            \"contributor_type\",\n", "            \"date_reported\",\n", "            \"ownership_ind\",\n", "            \"account_status\",\n", "            \"disbursed_dt\",\n", "            \"close_dt\",\n", "            \"last_payment_date\",\n", "            \"credit_limit_sanc_amt\",\n", "            \"disbursed_amt_high_credit\",\n", "            \"installment_amt\",\n", "            \"current_bal\",\n", "            \"installment_frequency\",\n", "            \"write_off_date\",\n", "            \"overdue_amt\",\n", "            \"write_off_amt\",\n", "            \"asset_class\",\n", "            \"account_remarks\",\n", "            \"linked_accounts\",\n", "            \"income\",\n", "            \"income_indicator\",\n", "            \"tenure\",\n", "            \"occupation\",\n", "            \"BankMstID\",\n", "        ]\n", "missing_columns = [\n", "    col for col in expected_columns if col not in df.columns\n", "]\n", "if missing_columns:\n", "    error_msg = f\"Missing required columns: {missing_columns}\"\n", "    \n", "    raise ValueError(error_msg)\n"]}, {"cell_type": "code", "execution_count": 14, "id": "2bf9a1bc", "metadata": {}, "outputs": [], "source": ["if \"self_indicator\" in df.columns:\n", "            df[\"self_indicator\"] = df[\"self_indicator\"].map(\n", "                {True: \"1\", False: \"0\", \"True\": \"1\", \"False\": \"0\"}\n", "            )\n", "            df[\"self_indicator\"] = df[\"self_indicator\"].astype(str)\n"]}, {"cell_type": "code", "execution_count": 15, "id": "b2a71a04", "metadata": {}, "outputs": [], "source": ["df = df.where(pd.notnull(df), None)\n", "df[\"tenure\"] = df[\"tenure\"].astype(float)\n", "df[\"candidate_id\"] = df[\"candidate_id\"].astype(str)\n", "df[\"customer_id_mbr_id\"] = df[\"customer_id_mbr_id\"].astype(\n", "    str\n", ")"]}, {"cell_type": "code", "execution_count": 16, "id": "9e10b8a8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>credt_rpt_id</th>\n", "      <th>los_app_id</th>\n", "      <th>candidate_id</th>\n", "      <th>customer_id_mbr_id</th>\n", "      <th>Branch_name</th>\n", "      <th>kendra</th>\n", "      <th>self_indicator</th>\n", "      <th>match_type</th>\n", "      <th>acc_num</th>\n", "      <th>credit_grantor</th>\n", "      <th>...</th>\n", "      <th>high_crd_hist</th>\n", "      <th>cur_bal_hist</th>\n", "      <th>das_hist</th>\n", "      <th>amt_overdue_hist</th>\n", "      <th>amt_paid_hist</th>\n", "      <th>income</th>\n", "      <th>income_indicator</th>\n", "      <th>tenure</th>\n", "      <th>occupation</th>\n", "      <th>BankMstID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>**********</td>\n", "      <td>nan</td>\n", "      <td>THASRA MML</td>\n", "      <td>ML0493S15C0410</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>MUTHOOT MICROFIN LTD</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>39044,39044,39044,39044,39044,39044,40739,4073...</td>\n", "      <td>S06S06S06S05S05S05S05S05S05S04S04S04S04XXXS04S...</td>\n", "      <td>21360,18690,16020,13350,10680,8010,8010,5340,2...</td>\n", "      <td>None</td>\n", "      <td>25000.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>**********</td>\n", "      <td>nan</td>\n", "      <td>444</td>\n", "      <td>*********</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>SATYA MICROCAPITAL LIMITED</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>33029,33029,33029,33029,33029,33029,34496,3593...</td>\n", "      <td>S05S05S05S05S05S04S04S04S04S04S04</td>\n", "      <td>5579,4013,3015,3015,1494,0,0,0,0,0,0,</td>\n", "      <td>None</td>\n", "      <td>25000.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>9252668405</td>\n", "      <td>nan</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>XXXX</td>\n", "      <td>...</td>\n", "      <td>50000,50000,50000,50000,50000,50000,50000,5000...</td>\n", "      <td>41938,41938,41938,41938,43765,56951,59631,6231...</td>\n", "      <td>S05S05S05S04S04S04S04S04S04S04</td>\n", "      <td>8040,5360,2680,0,0,0,0,0,0,0,</td>\n", "      <td>13400,13400,13400,13400,10720,8040,5360,2680,0,0,</td>\n", "      <td>12000.0</td>\n", "      <td>M</td>\n", "      <td>24.0</td>\n", "      <td>NaN</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>6794813831</td>\n", "      <td>nan</td>\n", "      <td>291</td>\n", "      <td>29100152</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>MIDLAND MICROFIN LTD</td>\n", "      <td>...</td>\n", "      <td>,</td>\n", "      <td>0,411,840,</td>\n", "      <td>S07S04S04</td>\n", "      <td>,,0,</td>\n", "      <td>,</td>\n", "      <td>16000.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>6794813831</td>\n", "      <td>nan</td>\n", "      <td>291</td>\n", "      <td>29100152</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>MIDLAND MICROFIN LTD</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>20446,20446,20446,20446,20446,20446,20446,2044...</td>\n", "      <td>S05S05S05S05S05S05S05S05S05S05S04S04S04S04S04S...</td>\n", "      <td>22817,21799,19619,17439,15259,13080,10900,8720...</td>\n", "      <td>None</td>\n", "      <td>16000.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>219</th>\n", "      <td>HIND250222CR5496706210843</td>\n", "      <td>1004</td>\n", "      <td>4978124343</td>\n", "      <td>nan</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>XXXX</td>\n", "      <td>...</td>\n", "      <td>73827,73827,73827,73827,73827,73827,73827,7382...</td>\n", "      <td>54389,54389,54389,54389,54389,54389,54389,5438...</td>\n", "      <td>S05S05S05S05S05S05S05S05S05S05S05S05S05S05XXXS...</td>\n", "      <td>63986,61204,58422,55640,52858,50076,47294,4451...</td>\n", "      <td>,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>36.0</td>\n", "      <td>NaN</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>220</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>*********</td>\n", "      <td>nan</td>\n", "      <td>DAKOR</td>\n", "      <td>DAKOR</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>HDFC BANK LTD</td>\n", "      <td>...</td>\n", "      <td>,</td>\n", "      <td>0,0,1659,1659,1659,1659,1659,1659,3283,4875,64...</td>\n", "      <td>S07S04S05S05S05S05S05S05S05S05S05S05S05S05S05S...</td>\n", "      <td>0,0,1693,1693,1693,1693,1693,1693,1693,1693,16...</td>\n", "      <td>,</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>221</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>**********</td>\n", "      <td>nan</td>\n", "      <td>30</td>\n", "      <td>324</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>HMPL NIDHI LIMITED</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>24272,24272,24272,24272,24272,24272,24272,2427...</td>\n", "      <td>S05S05S05S05S05S05S05S05S05S05S05S05S05S05S05S...</td>\n", "      <td>45751,45174,44597,44039,43462,42904,42327,4175...</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>222</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>*********</td>\n", "      <td>nan</td>\n", "      <td>016023-TFSL</td>\n", "      <td>002013_0200000374366</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>YES BANK</td>\n", "      <td>...</td>\n", "      <td>,</td>\n", "      <td>0,5664,7525,11123,11123,12861,12861,14568,1456...</td>\n", "      <td>S07S05S05S05S05S05S05S05S04S04S04S05S05S05S04S...</td>\n", "      <td>0,5875,5900,7875,5900,5900,3925,3925,0,0,0,197...</td>\n", "      <td>,</td>\n", "      <td>8333.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>223</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>*********</td>\n", "      <td>nan</td>\n", "      <td>3084</td>\n", "      <td>3084</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>LNT FINANCE</td>\n", "      <td>...</td>\n", "      <td>,</td>\n", "      <td>0,,1885,1904,3534,5130,4915,8154,14304,9204,92...</td>\n", "      <td>S07XXXS05S04S04S04S04S04S04S04S04S04S04S04S04S...</td>\n", "      <td>0,,1642,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,...</td>\n", "      <td>,</td>\n", "      <td>165000.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>9</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>224 rows × 42 columns</p>\n", "</div>"], "text/plain": ["                  credt_rpt_id  los_app_id candidate_id customer_id_mbr_id  \\\n", "0    HIND250222CR5536706210843        1008   **********                nan   \n", "1    HIND250222CR5536706210843        1008   **********                nan   \n", "2    HIND250222CR5536706210843        1008   9252668405                nan   \n", "3    HIND250222CR5536706210843        1008   6794813831                nan   \n", "4    HIND250222CR5536706210843        1008   6794813831                nan   \n", "..                         ...         ...          ...                ...   \n", "219  HIND250222CR5496706210843        1004   4978124343                nan   \n", "220  HIND250222CR5396706210843        1002    *********                nan   \n", "221  HIND250222CR5396706210843        1002   **********                nan   \n", "222  HIND250222CR5396706210843        1002    *********                nan   \n", "223  HIND250222CR5396706210843        1002    *********                nan   \n", "\n", "     Branch_name                kendra self_indicator match_type acc_num  \\\n", "0     THASRA MML        ML0493S15C0410              0    PRIMARY    XXXX   \n", "1            444             *********              0    PRIMARY    XXXX   \n", "2           None                  None              0    PRIMARY    XXXX   \n", "3            291              29100152              0    PRIMARY    XXXX   \n", "4            291              29100152              0    PRIMARY    XXXX   \n", "..           ...                   ...            ...        ...     ...   \n", "219         None                  None              0    PRIMARY    XXXX   \n", "220        DAKOR                 DAKOR              0    PRIMARY    XXXX   \n", "221           30                   324              0    PRIMARY    XXXX   \n", "222  016023-TFSL  002013_0200000374366              0    PRIMARY    XXXX   \n", "223         3084                  3084              0    PRIMARY    XXXX   \n", "\n", "                 credit_grantor  ...  \\\n", "0          MUTHOOT MICROFIN LTD  ...   \n", "1    SATYA MICROCAPITAL LIMITED  ...   \n", "2                          XXXX  ...   \n", "3          MIDLAND MICROFIN LTD  ...   \n", "4          MIDLAND MICROFIN LTD  ...   \n", "..                          ...  ...   \n", "219                        XXXX  ...   \n", "220               HDFC BANK LTD  ...   \n", "221          HMPL NIDHI LIMITED  ...   \n", "222                    YES BANK  ...   \n", "223                 LNT FINANCE  ...   \n", "\n", "                                         high_crd_hist  \\\n", "0                                                 None   \n", "1                                                 None   \n", "2    50000,50000,50000,50000,50000,50000,50000,5000...   \n", "3                                                    ,   \n", "4                                                 None   \n", "..                                                 ...   \n", "219  73827,73827,73827,73827,73827,73827,73827,7382...   \n", "220                                                  ,   \n", "221                                               None   \n", "222                                                  ,   \n", "223                                                  ,   \n", "\n", "                                          cur_bal_hist  \\\n", "0    39044,39044,39044,39044,39044,39044,40739,4073...   \n", "1    33029,33029,33029,33029,33029,33029,34496,3593...   \n", "2    41938,41938,41938,41938,43765,56951,59631,6231...   \n", "3                                           0,411,840,   \n", "4    20446,20446,20446,20446,20446,20446,20446,2044...   \n", "..                                                 ...   \n", "219  54389,54389,54389,54389,54389,54389,54389,5438...   \n", "220  0,0,1659,1659,1659,1659,1659,1659,3283,4875,64...   \n", "221  24272,24272,24272,24272,24272,24272,24272,2427...   \n", "222  0,5664,7525,11123,11123,12861,12861,14568,1456...   \n", "223  0,,1885,1904,3534,5130,4915,8154,14304,9204,92...   \n", "\n", "                                              das_hist  \\\n", "0    S06S06S06S05S05S05S05S05S05S04S04S04S04XXXS04S...   \n", "1                    S05S05S05S05S05S04S04S04S04S04S04   \n", "2                       S05S05S05S04S04S04S04S04S04S04   \n", "3                                            S07S04S04   \n", "4    S05S05S05S05S05S05S05S05S05S05S04S04S04S04S04S...   \n", "..                                                 ...   \n", "219  S05S05S05S05S05S05S05S05S05S05S05S05S05S05XXXS...   \n", "220  S07S04S05S05S05S05S05S05S05S05S05S05S05S05S05S...   \n", "221  S05S05S05S05S05S05S05S05S05S05S05S05S05S05S05S...   \n", "222  S07S05S05S05S05S05S05S05S04S04S04S05S05S05S04S...   \n", "223  S07XXXS05S04S04S04S04S04S04S04S04S04S04S04S04S...   \n", "\n", "                                      amt_overdue_hist  \\\n", "0    21360,18690,16020,13350,10680,8010,8010,5340,2...   \n", "1                5579,4013,3015,3015,1494,0,0,0,0,0,0,   \n", "2                        8040,5360,2680,0,0,0,0,0,0,0,   \n", "3                                                 ,,0,   \n", "4    22817,21799,19619,17439,15259,13080,10900,8720...   \n", "..                                                 ...   \n", "219  63986,61204,58422,55640,52858,50076,47294,4451...   \n", "220  0,0,1693,1693,1693,1693,1693,1693,1693,1693,16...   \n", "221  45751,45174,44597,44039,43462,42904,42327,4175...   \n", "222  0,5875,5900,7875,5900,5900,3925,3925,0,0,0,197...   \n", "223  0,,1642,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,...   \n", "\n", "                                         amt_paid_hist    income  \\\n", "0                                                 None   25000.0   \n", "1                                                 None   25000.0   \n", "2    13400,13400,13400,13400,10720,8040,5360,2680,0,0,   12000.0   \n", "3                                                    ,   16000.0   \n", "4                                                 None   16000.0   \n", "..                                                 ...       ...   \n", "219               ,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,       0.0   \n", "220                                                  ,       NaN   \n", "221                                               None       0.0   \n", "222                                                  ,    8333.0   \n", "223                                                  ,  165000.0   \n", "\n", "    income_indicator tenure occupation BankMstID  \n", "0                  M    NaN        NaN         9  \n", "1                  M    NaN        NaN         9  \n", "2                  M   24.0        NaN         9  \n", "3                  M    NaN        NaN         9  \n", "4                  M    NaN        NaN         9  \n", "..               ...    ...        ...       ...  \n", "219             None   36.0        NaN         9  \n", "220             None    NaN        NaN         9  \n", "221                M    NaN        NaN         9  \n", "222                M    NaN        NaN         9  \n", "223                M    NaN        NaN         9  \n", "\n", "[224 rows x 42 columns]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 17, "id": "e1b6e5a8", "metadata": {}, "outputs": [], "source": ["df[\"occupation\"] = df[\"occupation\"].astype(str)\n", "# df['kendra'] = df['kendra'].apply(clean_kendra\n", "date_columns = [\n", "    \"date_reported\",\n", "    \"disbursed_dt\",\n", "    \"close_dt\",\n", "    \"last_payment_date\",\n", "    \"write_off_date\",\n", "]"]}, {"cell_type": "code", "execution_count": 18, "id": "d1fd4e64", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>credt_rpt_id</th>\n", "      <th>los_app_id</th>\n", "      <th>candidate_id</th>\n", "      <th>customer_id_mbr_id</th>\n", "      <th>Branch_name</th>\n", "      <th>kendra</th>\n", "      <th>self_indicator</th>\n", "      <th>match_type</th>\n", "      <th>acc_num</th>\n", "      <th>credit_grantor</th>\n", "      <th>...</th>\n", "      <th>high_crd_hist</th>\n", "      <th>cur_bal_hist</th>\n", "      <th>das_hist</th>\n", "      <th>amt_overdue_hist</th>\n", "      <th>amt_paid_hist</th>\n", "      <th>income</th>\n", "      <th>income_indicator</th>\n", "      <th>tenure</th>\n", "      <th>occupation</th>\n", "      <th>BankMstID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>**********</td>\n", "      <td>nan</td>\n", "      <td>THASRA MML</td>\n", "      <td>ML0493S15C0410</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>MUTHOOT MICROFIN LTD</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>39044,39044,39044,39044,39044,39044,40739,4073...</td>\n", "      <td>S06S06S06S05S05S05S05S05S05S04S04S04S04XXXS04S...</td>\n", "      <td>21360,18690,16020,13350,10680,8010,8010,5340,2...</td>\n", "      <td>None</td>\n", "      <td>25000.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>**********</td>\n", "      <td>nan</td>\n", "      <td>444</td>\n", "      <td>*********</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>SATYA MICROCAPITAL LIMITED</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>33029,33029,33029,33029,33029,33029,34496,3593...</td>\n", "      <td>S05S05S05S05S05S04S04S04S04S04S04</td>\n", "      <td>5579,4013,3015,3015,1494,0,0,0,0,0,0,</td>\n", "      <td>None</td>\n", "      <td>25000.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>9252668405</td>\n", "      <td>nan</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>XXXX</td>\n", "      <td>...</td>\n", "      <td>50000,50000,50000,50000,50000,50000,50000,5000...</td>\n", "      <td>41938,41938,41938,41938,43765,56951,59631,6231...</td>\n", "      <td>S05S05S05S04S04S04S04S04S04S04</td>\n", "      <td>8040,5360,2680,0,0,0,0,0,0,0,</td>\n", "      <td>13400,13400,13400,13400,10720,8040,5360,2680,0,0,</td>\n", "      <td>12000.0</td>\n", "      <td>M</td>\n", "      <td>24.0</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>6794813831</td>\n", "      <td>nan</td>\n", "      <td>291</td>\n", "      <td>29100152</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>MIDLAND MICROFIN LTD</td>\n", "      <td>...</td>\n", "      <td>,</td>\n", "      <td>0,411,840,</td>\n", "      <td>S07S04S04</td>\n", "      <td>,,0,</td>\n", "      <td>,</td>\n", "      <td>16000.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>6794813831</td>\n", "      <td>nan</td>\n", "      <td>291</td>\n", "      <td>29100152</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>MIDLAND MICROFIN LTD</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>20446,20446,20446,20446,20446,20446,20446,2044...</td>\n", "      <td>S05S05S05S05S05S05S05S05S05S05S04S04S04S04S04S...</td>\n", "      <td>22817,21799,19619,17439,15259,13080,10900,8720...</td>\n", "      <td>None</td>\n", "      <td>16000.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>219</th>\n", "      <td>HIND250222CR5496706210843</td>\n", "      <td>1004</td>\n", "      <td>4978124343</td>\n", "      <td>nan</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>XXXX</td>\n", "      <td>...</td>\n", "      <td>73827,73827,73827,73827,73827,73827,73827,7382...</td>\n", "      <td>54389,54389,54389,54389,54389,54389,54389,5438...</td>\n", "      <td>S05S05S05S05S05S05S05S05S05S05S05S05S05S05XXXS...</td>\n", "      <td>63986,61204,58422,55640,52858,50076,47294,4451...</td>\n", "      <td>,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>36.0</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>220</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>*********</td>\n", "      <td>nan</td>\n", "      <td>DAKOR</td>\n", "      <td>DAKOR</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>HDFC BANK LTD</td>\n", "      <td>...</td>\n", "      <td>,</td>\n", "      <td>0,0,1659,1659,1659,1659,1659,1659,3283,4875,64...</td>\n", "      <td>S07S04S05S05S05S05S05S05S05S05S05S05S05S05S05S...</td>\n", "      <td>0,0,1693,1693,1693,1693,1693,1693,1693,1693,16...</td>\n", "      <td>,</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>221</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>**********</td>\n", "      <td>nan</td>\n", "      <td>30</td>\n", "      <td>324</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>HMPL NIDHI LIMITED</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>24272,24272,24272,24272,24272,24272,24272,2427...</td>\n", "      <td>S05S05S05S05S05S05S05S05S05S05S05S05S05S05S05S...</td>\n", "      <td>45751,45174,44597,44039,43462,42904,42327,4175...</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>222</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>*********</td>\n", "      <td>nan</td>\n", "      <td>016023-TFSL</td>\n", "      <td>002013_0200000374366</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>YES BANK</td>\n", "      <td>...</td>\n", "      <td>,</td>\n", "      <td>0,5664,7525,11123,11123,12861,12861,14568,1456...</td>\n", "      <td>S07S05S05S05S05S05S05S05S04S04S04S05S05S05S04S...</td>\n", "      <td>0,5875,5900,7875,5900,5900,3925,3925,0,0,0,197...</td>\n", "      <td>,</td>\n", "      <td>8333.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>223</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>*********</td>\n", "      <td>nan</td>\n", "      <td>3084</td>\n", "      <td>3084</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>LNT FINANCE</td>\n", "      <td>...</td>\n", "      <td>,</td>\n", "      <td>0,,1885,1904,3534,5130,4915,8154,14304,9204,92...</td>\n", "      <td>S07XXXS05S04S04S04S04S04S04S04S04S04S04S04S04S...</td>\n", "      <td>0,,1642,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,...</td>\n", "      <td>,</td>\n", "      <td>165000.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>224 rows × 42 columns</p>\n", "</div>"], "text/plain": ["                  credt_rpt_id  los_app_id candidate_id customer_id_mbr_id  \\\n", "0    HIND250222CR5536706210843        1008   **********                nan   \n", "1    HIND250222CR5536706210843        1008   **********                nan   \n", "2    HIND250222CR5536706210843        1008   9252668405                nan   \n", "3    HIND250222CR5536706210843        1008   6794813831                nan   \n", "4    HIND250222CR5536706210843        1008   6794813831                nan   \n", "..                         ...         ...          ...                ...   \n", "219  HIND250222CR5496706210843        1004   4978124343                nan   \n", "220  HIND250222CR5396706210843        1002    *********                nan   \n", "221  HIND250222CR5396706210843        1002   **********                nan   \n", "222  HIND250222CR5396706210843        1002    *********                nan   \n", "223  HIND250222CR5396706210843        1002    *********                nan   \n", "\n", "     Branch_name                kendra self_indicator match_type acc_num  \\\n", "0     THASRA MML        ML0493S15C0410              0    PRIMARY    XXXX   \n", "1            444             *********              0    PRIMARY    XXXX   \n", "2           None                  None              0    PRIMARY    XXXX   \n", "3            291              29100152              0    PRIMARY    XXXX   \n", "4            291              29100152              0    PRIMARY    XXXX   \n", "..           ...                   ...            ...        ...     ...   \n", "219         None                  None              0    PRIMARY    XXXX   \n", "220        DAKOR                 DAKOR              0    PRIMARY    XXXX   \n", "221           30                   324              0    PRIMARY    XXXX   \n", "222  016023-TFSL  002013_0200000374366              0    PRIMARY    XXXX   \n", "223         3084                  3084              0    PRIMARY    XXXX   \n", "\n", "                 credit_grantor  ...  \\\n", "0          MUTHOOT MICROFIN LTD  ...   \n", "1    SATYA MICROCAPITAL LIMITED  ...   \n", "2                          XXXX  ...   \n", "3          MIDLAND MICROFIN LTD  ...   \n", "4          MIDLAND MICROFIN LTD  ...   \n", "..                          ...  ...   \n", "219                        XXXX  ...   \n", "220               HDFC BANK LTD  ...   \n", "221          HMPL NIDHI LIMITED  ...   \n", "222                    YES BANK  ...   \n", "223                 LNT FINANCE  ...   \n", "\n", "                                         high_crd_hist  \\\n", "0                                                 None   \n", "1                                                 None   \n", "2    50000,50000,50000,50000,50000,50000,50000,5000...   \n", "3                                                    ,   \n", "4                                                 None   \n", "..                                                 ...   \n", "219  73827,73827,73827,73827,73827,73827,73827,7382...   \n", "220                                                  ,   \n", "221                                               None   \n", "222                                                  ,   \n", "223                                                  ,   \n", "\n", "                                          cur_bal_hist  \\\n", "0    39044,39044,39044,39044,39044,39044,40739,4073...   \n", "1    33029,33029,33029,33029,33029,33029,34496,3593...   \n", "2    41938,41938,41938,41938,43765,56951,59631,6231...   \n", "3                                           0,411,840,   \n", "4    20446,20446,20446,20446,20446,20446,20446,2044...   \n", "..                                                 ...   \n", "219  54389,54389,54389,54389,54389,54389,54389,5438...   \n", "220  0,0,1659,1659,1659,1659,1659,1659,3283,4875,64...   \n", "221  24272,24272,24272,24272,24272,24272,24272,2427...   \n", "222  0,5664,7525,11123,11123,12861,12861,14568,1456...   \n", "223  0,,1885,1904,3534,5130,4915,8154,14304,9204,92...   \n", "\n", "                                              das_hist  \\\n", "0    S06S06S06S05S05S05S05S05S05S04S04S04S04XXXS04S...   \n", "1                    S05S05S05S05S05S04S04S04S04S04S04   \n", "2                       S05S05S05S04S04S04S04S04S04S04   \n", "3                                            S07S04S04   \n", "4    S05S05S05S05S05S05S05S05S05S05S04S04S04S04S04S...   \n", "..                                                 ...   \n", "219  S05S05S05S05S05S05S05S05S05S05S05S05S05S05XXXS...   \n", "220  S07S04S05S05S05S05S05S05S05S05S05S05S05S05S05S...   \n", "221  S05S05S05S05S05S05S05S05S05S05S05S05S05S05S05S...   \n", "222  S07S05S05S05S05S05S05S05S04S04S04S05S05S05S04S...   \n", "223  S07XXXS05S04S04S04S04S04S04S04S04S04S04S04S04S...   \n", "\n", "                                      amt_overdue_hist  \\\n", "0    21360,18690,16020,13350,10680,8010,8010,5340,2...   \n", "1                5579,4013,3015,3015,1494,0,0,0,0,0,0,   \n", "2                        8040,5360,2680,0,0,0,0,0,0,0,   \n", "3                                                 ,,0,   \n", "4    22817,21799,19619,17439,15259,13080,10900,8720...   \n", "..                                                 ...   \n", "219  63986,61204,58422,55640,52858,50076,47294,4451...   \n", "220  0,0,1693,1693,1693,1693,1693,1693,1693,1693,16...   \n", "221  45751,45174,44597,44039,43462,42904,42327,4175...   \n", "222  0,5875,5900,7875,5900,5900,3925,3925,0,0,0,197...   \n", "223  0,,1642,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,...   \n", "\n", "                                         amt_paid_hist    income  \\\n", "0                                                 None   25000.0   \n", "1                                                 None   25000.0   \n", "2    13400,13400,13400,13400,10720,8040,5360,2680,0,0,   12000.0   \n", "3                                                    ,   16000.0   \n", "4                                                 None   16000.0   \n", "..                                                 ...       ...   \n", "219               ,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,       0.0   \n", "220                                                  ,       NaN   \n", "221                                               None       0.0   \n", "222                                                  ,    8333.0   \n", "223                                                  ,  165000.0   \n", "\n", "    income_indicator tenure occupation BankMstID  \n", "0                  M    NaN        nan         9  \n", "1                  M    NaN        nan         9  \n", "2                  M   24.0        nan         9  \n", "3                  M    NaN        nan         9  \n", "4                  M    NaN        nan         9  \n", "..               ...    ...        ...       ...  \n", "219             None   36.0        nan         9  \n", "220             None    NaN        nan         9  \n", "221                M    NaN        nan         9  \n", "222                M    NaN        nan         9  \n", "223                M    NaN        nan         9  \n", "\n", "[224 rows x 42 columns]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 19, "id": "1b439a1f", "metadata": {}, "outputs": [], "source": ["def clean_date(value):\n", "    if pd.isna(value) or value is None or str(value).lower() == \"nan\":\n", "        return None\n", "    try:\n", "        for fmt in [\"%Y-%m-%d\", \"%d/%m/%Y\", \"%m/%d/%Y\", \"%Y%m%d\", \"%d-%m-%Y\"]:\n", "            try:\n", "                parsed_date = pd.to_datetime(value, format=fmt, errors=\"raise\")\n", "                return parsed_date.strftime(\"%Y-%m-%d\")\n", "            except ValueError:\n", "                continue\n", "        parsed_date = pd.to_datetime(value, errors=\"raise\")\n", "        return parsed_date.strftime(\"%Y-%m-%d\")\n", "    except (ValueError, TypeError) as e:\n", "        print(f\"Failed to parse date: {value}, error: {str(e)}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 20, "id": "2d7d5fe4", "metadata": {}, "outputs": [], "source": ["for col in date_columns:\n", "    if col in df.columns:\n", "        df[col] = df[col].apply(clean_date)"]}, {"cell_type": "code", "execution_count": 21, "id": "7636e892", "metadata": {}, "outputs": [], "source": ["\n", "def clean_numeric(value):\n", "    if pd.isna(value) or value is None or str(value).lower() == \"nan\":\n", "        return None\n", "    try:\n", "        return float(\n", "            value.replace(\",\", \"\") if isinstance(value, str) else value\n", "        )\n", "    except (ValueError, TypeError) as e:\n", "        print(f\"Invalid numeric value: {value}, error: {str(e)}\")\n", "        return None\n"]}, {"cell_type": "code", "execution_count": 22, "id": "ccf15dbd", "metadata": {}, "outputs": [], "source": ["numeric_columns = [\n", "    \"credit_limit_sanc_amt\",\n", "    \"disbursed_amt_high_credit\",\n", "    \"current_bal\",\n", "    \"overdue_amt\",\n", "    \"write_off_amt\",\n", "    \"income\",\n", "    \"tenure\",\n", "]\n", "for col in numeric_columns:\n", "    if col in df.columns:\n", "        df[col] = df[col].apply(clean_numeric)"]}, {"cell_type": "code", "execution_count": 23, "id": "8e141562", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>credt_rpt_id</th>\n", "      <th>los_app_id</th>\n", "      <th>candidate_id</th>\n", "      <th>customer_id_mbr_id</th>\n", "      <th>Branch_name</th>\n", "      <th>kendra</th>\n", "      <th>self_indicator</th>\n", "      <th>match_type</th>\n", "      <th>acc_num</th>\n", "      <th>credit_grantor</th>\n", "      <th>...</th>\n", "      <th>high_crd_hist</th>\n", "      <th>cur_bal_hist</th>\n", "      <th>das_hist</th>\n", "      <th>amt_overdue_hist</th>\n", "      <th>amt_paid_hist</th>\n", "      <th>income</th>\n", "      <th>income_indicator</th>\n", "      <th>tenure</th>\n", "      <th>occupation</th>\n", "      <th>BankMstID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>**********</td>\n", "      <td>nan</td>\n", "      <td>THASRA MML</td>\n", "      <td>ML0493S15C0410</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>MUTHOOT MICROFIN LTD</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>39044,39044,39044,39044,39044,39044,40739,4073...</td>\n", "      <td>S06S06S06S05S05S05S05S05S05S04S04S04S04XXXS04S...</td>\n", "      <td>21360,18690,16020,13350,10680,8010,8010,5340,2...</td>\n", "      <td>None</td>\n", "      <td>25000.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>**********</td>\n", "      <td>nan</td>\n", "      <td>444</td>\n", "      <td>*********</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>SATYA MICROCAPITAL LIMITED</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>33029,33029,33029,33029,33029,33029,34496,3593...</td>\n", "      <td>S05S05S05S05S05S04S04S04S04S04S04</td>\n", "      <td>5579,4013,3015,3015,1494,0,0,0,0,0,0,</td>\n", "      <td>None</td>\n", "      <td>25000.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>9252668405</td>\n", "      <td>nan</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>XXXX</td>\n", "      <td>...</td>\n", "      <td>50000,50000,50000,50000,50000,50000,50000,5000...</td>\n", "      <td>41938,41938,41938,41938,43765,56951,59631,6231...</td>\n", "      <td>S05S05S05S04S04S04S04S04S04S04</td>\n", "      <td>8040,5360,2680,0,0,0,0,0,0,0,</td>\n", "      <td>13400,13400,13400,13400,10720,8040,5360,2680,0,0,</td>\n", "      <td>12000.0</td>\n", "      <td>M</td>\n", "      <td>24.0</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>6794813831</td>\n", "      <td>nan</td>\n", "      <td>291</td>\n", "      <td>29100152</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>MIDLAND MICROFIN LTD</td>\n", "      <td>...</td>\n", "      <td>,</td>\n", "      <td>0,411,840,</td>\n", "      <td>S07S04S04</td>\n", "      <td>,,0,</td>\n", "      <td>,</td>\n", "      <td>16000.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>6794813831</td>\n", "      <td>nan</td>\n", "      <td>291</td>\n", "      <td>29100152</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>MIDLAND MICROFIN LTD</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>20446,20446,20446,20446,20446,20446,20446,2044...</td>\n", "      <td>S05S05S05S05S05S05S05S05S05S05S04S04S04S04S04S...</td>\n", "      <td>22817,21799,19619,17439,15259,13080,10900,8720...</td>\n", "      <td>None</td>\n", "      <td>16000.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>219</th>\n", "      <td>HIND250222CR5496706210843</td>\n", "      <td>1004</td>\n", "      <td>4978124343</td>\n", "      <td>nan</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>XXXX</td>\n", "      <td>...</td>\n", "      <td>73827,73827,73827,73827,73827,73827,73827,7382...</td>\n", "      <td>54389,54389,54389,54389,54389,54389,54389,5438...</td>\n", "      <td>S05S05S05S05S05S05S05S05S05S05S05S05S05S05XXXS...</td>\n", "      <td>63986,61204,58422,55640,52858,50076,47294,4451...</td>\n", "      <td>,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>36.0</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>220</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>*********</td>\n", "      <td>nan</td>\n", "      <td>DAKOR</td>\n", "      <td>DAKOR</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>HDFC BANK LTD</td>\n", "      <td>...</td>\n", "      <td>,</td>\n", "      <td>0,0,1659,1659,1659,1659,1659,1659,3283,4875,64...</td>\n", "      <td>S07S04S05S05S05S05S05S05S05S05S05S05S05S05S05S...</td>\n", "      <td>0,0,1693,1693,1693,1693,1693,1693,1693,1693,16...</td>\n", "      <td>,</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>221</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>**********</td>\n", "      <td>nan</td>\n", "      <td>30</td>\n", "      <td>324</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>HMPL NIDHI LIMITED</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>24272,24272,24272,24272,24272,24272,24272,2427...</td>\n", "      <td>S05S05S05S05S05S05S05S05S05S05S05S05S05S05S05S...</td>\n", "      <td>45751,45174,44597,44039,43462,42904,42327,4175...</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>222</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>*********</td>\n", "      <td>nan</td>\n", "      <td>016023-TFSL</td>\n", "      <td>002013_0200000374366</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>YES BANK</td>\n", "      <td>...</td>\n", "      <td>,</td>\n", "      <td>0,5664,7525,11123,11123,12861,12861,14568,1456...</td>\n", "      <td>S07S05S05S05S05S05S05S05S04S04S04S05S05S05S04S...</td>\n", "      <td>0,5875,5900,7875,5900,5900,3925,3925,0,0,0,197...</td>\n", "      <td>,</td>\n", "      <td>8333.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>223</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>*********</td>\n", "      <td>nan</td>\n", "      <td>3084</td>\n", "      <td>3084</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>LNT FINANCE</td>\n", "      <td>...</td>\n", "      <td>,</td>\n", "      <td>0,,1885,1904,3534,5130,4915,8154,14304,9204,92...</td>\n", "      <td>S07XXXS05S04S04S04S04S04S04S04S04S04S04S04S04S...</td>\n", "      <td>0,,1642,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,...</td>\n", "      <td>,</td>\n", "      <td>165000.0</td>\n", "      <td>M</td>\n", "      <td>NaN</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>224 rows × 42 columns</p>\n", "</div>"], "text/plain": ["                  credt_rpt_id  los_app_id candidate_id customer_id_mbr_id  \\\n", "0    HIND250222CR5536706210843        1008   **********                nan   \n", "1    HIND250222CR5536706210843        1008   **********                nan   \n", "2    HIND250222CR5536706210843        1008   9252668405                nan   \n", "3    HIND250222CR5536706210843        1008   6794813831                nan   \n", "4    HIND250222CR5536706210843        1008   6794813831                nan   \n", "..                         ...         ...          ...                ...   \n", "219  HIND250222CR5496706210843        1004   4978124343                nan   \n", "220  HIND250222CR5396706210843        1002    *********                nan   \n", "221  HIND250222CR5396706210843        1002   **********                nan   \n", "222  HIND250222CR5396706210843        1002    *********                nan   \n", "223  HIND250222CR5396706210843        1002    *********                nan   \n", "\n", "     Branch_name                kendra self_indicator match_type acc_num  \\\n", "0     THASRA MML        ML0493S15C0410              0    PRIMARY    XXXX   \n", "1            444             *********              0    PRIMARY    XXXX   \n", "2           None                  None              0    PRIMARY    XXXX   \n", "3            291              29100152              0    PRIMARY    XXXX   \n", "4            291              29100152              0    PRIMARY    XXXX   \n", "..           ...                   ...            ...        ...     ...   \n", "219         None                  None              0    PRIMARY    XXXX   \n", "220        DAKOR                 DAKOR              0    PRIMARY    XXXX   \n", "221           30                   324              0    PRIMARY    XXXX   \n", "222  016023-TFSL  002013_0200000374366              0    PRIMARY    XXXX   \n", "223         3084                  3084              0    PRIMARY    XXXX   \n", "\n", "                 credit_grantor  ...  \\\n", "0          MUTHOOT MICROFIN LTD  ...   \n", "1    SATYA MICROCAPITAL LIMITED  ...   \n", "2                          XXXX  ...   \n", "3          MIDLAND MICROFIN LTD  ...   \n", "4          MIDLAND MICROFIN LTD  ...   \n", "..                          ...  ...   \n", "219                        XXXX  ...   \n", "220               HDFC BANK LTD  ...   \n", "221          HMPL NIDHI LIMITED  ...   \n", "222                    YES BANK  ...   \n", "223                 LNT FINANCE  ...   \n", "\n", "                                         high_crd_hist  \\\n", "0                                                 None   \n", "1                                                 None   \n", "2    50000,50000,50000,50000,50000,50000,50000,5000...   \n", "3                                                    ,   \n", "4                                                 None   \n", "..                                                 ...   \n", "219  73827,73827,73827,73827,73827,73827,73827,7382...   \n", "220                                                  ,   \n", "221                                               None   \n", "222                                                  ,   \n", "223                                                  ,   \n", "\n", "                                          cur_bal_hist  \\\n", "0    39044,39044,39044,39044,39044,39044,40739,4073...   \n", "1    33029,33029,33029,33029,33029,33029,34496,3593...   \n", "2    41938,41938,41938,41938,43765,56951,59631,6231...   \n", "3                                           0,411,840,   \n", "4    20446,20446,20446,20446,20446,20446,20446,2044...   \n", "..                                                 ...   \n", "219  54389,54389,54389,54389,54389,54389,54389,5438...   \n", "220  0,0,1659,1659,1659,1659,1659,1659,3283,4875,64...   \n", "221  24272,24272,24272,24272,24272,24272,24272,2427...   \n", "222  0,5664,7525,11123,11123,12861,12861,14568,1456...   \n", "223  0,,1885,1904,3534,5130,4915,8154,14304,9204,92...   \n", "\n", "                                              das_hist  \\\n", "0    S06S06S06S05S05S05S05S05S05S04S04S04S04XXXS04S...   \n", "1                    S05S05S05S05S05S04S04S04S04S04S04   \n", "2                       S05S05S05S04S04S04S04S04S04S04   \n", "3                                            S07S04S04   \n", "4    S05S05S05S05S05S05S05S05S05S05S04S04S04S04S04S...   \n", "..                                                 ...   \n", "219  S05S05S05S05S05S05S05S05S05S05S05S05S05S05XXXS...   \n", "220  S07S04S05S05S05S05S05S05S05S05S05S05S05S05S05S...   \n", "221  S05S05S05S05S05S05S05S05S05S05S05S05S05S05S05S...   \n", "222  S07S05S05S05S05S05S05S05S04S04S04S05S05S05S04S...   \n", "223  S07XXXS05S04S04S04S04S04S04S04S04S04S04S04S04S...   \n", "\n", "                                      amt_overdue_hist  \\\n", "0    21360,18690,16020,13350,10680,8010,8010,5340,2...   \n", "1                5579,4013,3015,3015,1494,0,0,0,0,0,0,   \n", "2                        8040,5360,2680,0,0,0,0,0,0,0,   \n", "3                                                 ,,0,   \n", "4    22817,21799,19619,17439,15259,13080,10900,8720...   \n", "..                                                 ...   \n", "219  63986,61204,58422,55640,52858,50076,47294,4451...   \n", "220  0,0,1693,1693,1693,1693,1693,1693,1693,1693,16...   \n", "221  45751,45174,44597,44039,43462,42904,42327,4175...   \n", "222  0,5875,5900,7875,5900,5900,3925,3925,0,0,0,197...   \n", "223  0,,1642,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,...   \n", "\n", "                                         amt_paid_hist    income  \\\n", "0                                                 None   25000.0   \n", "1                                                 None   25000.0   \n", "2    13400,13400,13400,13400,10720,8040,5360,2680,0,0,   12000.0   \n", "3                                                    ,   16000.0   \n", "4                                                 None   16000.0   \n", "..                                                 ...       ...   \n", "219               ,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,       0.0   \n", "220                                                  ,       NaN   \n", "221                                               None       0.0   \n", "222                                                  ,    8333.0   \n", "223                                                  ,  165000.0   \n", "\n", "    income_indicator tenure  occupation  BankMstID  \n", "0                  M    NaN         nan          9  \n", "1                  M    NaN         nan          9  \n", "2                  M   24.0         nan          9  \n", "3                  M    NaN         nan          9  \n", "4                  M    NaN         nan          9  \n", "..               ...    ...         ...        ...  \n", "219             None   36.0         nan          9  \n", "220             None    NaN         nan          9  \n", "221                M    NaN         nan          9  \n", "222                M    NaN         nan          9  \n", "223                M    NaN         nan          9  \n", "\n", "[224 rows x 42 columns]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 24, "id": "40a71492", "metadata": {}, "outputs": [], "source": ["bool_cols = df.select_dtypes(include=\"bool\").columns\n", "df[bool_cols] = df[bool_cols].astype(int)\n", "# Replace NaN with None for SQL compatibility\n", "df = df.where(pd.notnull(df), None)\n", "# Step 3: Validate data integrity\n", "invalid_rows = df[df[\"credt_rpt_id\"].isna()]\n", "if not invalid_rows.empty:\n", "    error_msg = f\"Found {len(invalid_rows)} rows with missing credt_rpt_id\"\n", "    print(error_msg)\n", "    raise ValueError(error_msg)"]}, {"cell_type": "code", "execution_count": 25, "id": "1b01c051", "metadata": {}, "outputs": [], "source": ["import numpy as np"]}, {"cell_type": "code", "execution_count": 26, "id": "d1ad3118", "metadata": {}, "outputs": [], "source": ["df.replace(np.nan, None, inplace=True)\n", "# Validate date columns\n", "invalid_date_info = []\n", "for col in date_columns:\n", "    if col in df.columns:\n", "        invalid_date_rows = df[\n", "            df[col].notna()\n", "            & ~df[col].str.match(r\"^\\d{4}-\\d{2}-\\d{2}$\", na=False)\n", "        ]\n", "        if not invalid_date_rows.empty:\n", "            invalid_data = (\n", "                invalid_date_rows[[\"credt_rpt_id\", col]]\n", "                .head(5)\n", "                .to_dict(\"records\")\n", "            )\n", "            invalid_date_info.append(\n", "                f\"Invalid {col} values in {len(invalid_date_rows)} rows: {invalid_data}\"\n", "            )\n", "\n", "if invalid_date_info:\n", "    error_msg = \"; \".join(invalid_date_info)\n", "    print(error_msg)\n", "    raise ValueError(error_msg)\n", "records = [\n", "    tuple(row)\n", "    for row in df[expected_columns].itertuples(index=False, name=None)\n", "]\n", "unique_keys = df[[\"credt_rpt_id\"]].drop_duplicates()"]}, {"cell_type": "code", "execution_count": 27, "id": "c6ecdd64", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>credt_rpt_id</th>\n", "      <th>los_app_id</th>\n", "      <th>candidate_id</th>\n", "      <th>customer_id_mbr_id</th>\n", "      <th>Branch_name</th>\n", "      <th>kendra</th>\n", "      <th>self_indicator</th>\n", "      <th>match_type</th>\n", "      <th>acc_num</th>\n", "      <th>credit_grantor</th>\n", "      <th>...</th>\n", "      <th>high_crd_hist</th>\n", "      <th>cur_bal_hist</th>\n", "      <th>das_hist</th>\n", "      <th>amt_overdue_hist</th>\n", "      <th>amt_paid_hist</th>\n", "      <th>income</th>\n", "      <th>income_indicator</th>\n", "      <th>tenure</th>\n", "      <th>occupation</th>\n", "      <th>BankMstID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>**********</td>\n", "      <td>nan</td>\n", "      <td>THASRA MML</td>\n", "      <td>ML0493S15C0410</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>MUTHOOT MICROFIN LTD</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>39044,39044,39044,39044,39044,39044,40739,4073...</td>\n", "      <td>S06S06S06S05S05S05S05S05S05S04S04S04S04XXXS04S...</td>\n", "      <td>21360,18690,16020,13350,10680,8010,8010,5340,2...</td>\n", "      <td>None</td>\n", "      <td>25000.0</td>\n", "      <td>M</td>\n", "      <td>None</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>**********</td>\n", "      <td>nan</td>\n", "      <td>444</td>\n", "      <td>*********</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>SATYA MICROCAPITAL LIMITED</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>33029,33029,33029,33029,33029,33029,34496,3593...</td>\n", "      <td>S05S05S05S05S05S04S04S04S04S04S04</td>\n", "      <td>5579,4013,3015,3015,1494,0,0,0,0,0,0,</td>\n", "      <td>None</td>\n", "      <td>25000.0</td>\n", "      <td>M</td>\n", "      <td>None</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>9252668405</td>\n", "      <td>nan</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>XXXX</td>\n", "      <td>...</td>\n", "      <td>50000,50000,50000,50000,50000,50000,50000,5000...</td>\n", "      <td>41938,41938,41938,41938,43765,56951,59631,6231...</td>\n", "      <td>S05S05S05S04S04S04S04S04S04S04</td>\n", "      <td>8040,5360,2680,0,0,0,0,0,0,0,</td>\n", "      <td>13400,13400,13400,13400,10720,8040,5360,2680,0,0,</td>\n", "      <td>12000.0</td>\n", "      <td>M</td>\n", "      <td>24.0</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>6794813831</td>\n", "      <td>nan</td>\n", "      <td>291</td>\n", "      <td>29100152</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>MIDLAND MICROFIN LTD</td>\n", "      <td>...</td>\n", "      <td>,</td>\n", "      <td>0,411,840,</td>\n", "      <td>S07S04S04</td>\n", "      <td>,,0,</td>\n", "      <td>,</td>\n", "      <td>16000.0</td>\n", "      <td>M</td>\n", "      <td>None</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>6794813831</td>\n", "      <td>nan</td>\n", "      <td>291</td>\n", "      <td>29100152</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>MIDLAND MICROFIN LTD</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>20446,20446,20446,20446,20446,20446,20446,2044...</td>\n", "      <td>S05S05S05S05S05S05S05S05S05S05S04S04S04S04S04S...</td>\n", "      <td>22817,21799,19619,17439,15259,13080,10900,8720...</td>\n", "      <td>None</td>\n", "      <td>16000.0</td>\n", "      <td>M</td>\n", "      <td>None</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>219</th>\n", "      <td>HIND250222CR5496706210843</td>\n", "      <td>1004</td>\n", "      <td>4978124343</td>\n", "      <td>nan</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>XXXX</td>\n", "      <td>...</td>\n", "      <td>73827,73827,73827,73827,73827,73827,73827,7382...</td>\n", "      <td>54389,54389,54389,54389,54389,54389,54389,5438...</td>\n", "      <td>S05S05S05S05S05S05S05S05S05S05S05S05S05S05XXXS...</td>\n", "      <td>63986,61204,58422,55640,52858,50076,47294,4451...</td>\n", "      <td>,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>36.0</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>220</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>*********</td>\n", "      <td>nan</td>\n", "      <td>DAKOR</td>\n", "      <td>DAKOR</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>HDFC BANK LTD</td>\n", "      <td>...</td>\n", "      <td>,</td>\n", "      <td>0,0,1659,1659,1659,1659,1659,1659,3283,4875,64...</td>\n", "      <td>S07S04S05S05S05S05S05S05S05S05S05S05S05S05S05S...</td>\n", "      <td>0,0,1693,1693,1693,1693,1693,1693,1693,1693,16...</td>\n", "      <td>,</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>221</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>**********</td>\n", "      <td>nan</td>\n", "      <td>30</td>\n", "      <td>324</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>HMPL NIDHI LIMITED</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>24272,24272,24272,24272,24272,24272,24272,2427...</td>\n", "      <td>S05S05S05S05S05S05S05S05S05S05S05S05S05S05S05S...</td>\n", "      <td>45751,45174,44597,44039,43462,42904,42327,4175...</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>M</td>\n", "      <td>None</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>222</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>*********</td>\n", "      <td>nan</td>\n", "      <td>016023-TFSL</td>\n", "      <td>002013_0200000374366</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>YES BANK</td>\n", "      <td>...</td>\n", "      <td>,</td>\n", "      <td>0,5664,7525,11123,11123,12861,12861,14568,1456...</td>\n", "      <td>S07S05S05S05S05S05S05S05S04S04S04S05S05S05S04S...</td>\n", "      <td>0,5875,5900,7875,5900,5900,3925,3925,0,0,0,197...</td>\n", "      <td>,</td>\n", "      <td>8333.0</td>\n", "      <td>M</td>\n", "      <td>None</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>223</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>*********</td>\n", "      <td>nan</td>\n", "      <td>3084</td>\n", "      <td>3084</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>LNT FINANCE</td>\n", "      <td>...</td>\n", "      <td>,</td>\n", "      <td>0,,1885,1904,3534,5130,4915,8154,14304,9204,92...</td>\n", "      <td>S07XXXS05S04S04S04S04S04S04S04S04S04S04S04S04S...</td>\n", "      <td>0,,1642,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,...</td>\n", "      <td>,</td>\n", "      <td>165000.0</td>\n", "      <td>M</td>\n", "      <td>None</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>224 rows × 42 columns</p>\n", "</div>"], "text/plain": ["                  credt_rpt_id  los_app_id candidate_id customer_id_mbr_id  \\\n", "0    HIND250222CR5536706210843        1008   **********                nan   \n", "1    HIND250222CR5536706210843        1008   **********                nan   \n", "2    HIND250222CR5536706210843        1008   9252668405                nan   \n", "3    HIND250222CR5536706210843        1008   6794813831                nan   \n", "4    HIND250222CR5536706210843        1008   6794813831                nan   \n", "..                         ...         ...          ...                ...   \n", "219  HIND250222CR5496706210843        1004   4978124343                nan   \n", "220  HIND250222CR5396706210843        1002    *********                nan   \n", "221  HIND250222CR5396706210843        1002   **********                nan   \n", "222  HIND250222CR5396706210843        1002    *********                nan   \n", "223  HIND250222CR5396706210843        1002    *********                nan   \n", "\n", "     Branch_name                kendra self_indicator match_type acc_num  \\\n", "0     THASRA MML        ML0493S15C0410              0    PRIMARY    XXXX   \n", "1            444             *********              0    PRIMARY    XXXX   \n", "2           None                  None              0    PRIMARY    XXXX   \n", "3            291              29100152              0    PRIMARY    XXXX   \n", "4            291              29100152              0    PRIMARY    XXXX   \n", "..           ...                   ...            ...        ...     ...   \n", "219         None                  None              0    PRIMARY    XXXX   \n", "220        DAKOR                 DAKOR              0    PRIMARY    XXXX   \n", "221           30                   324              0    PRIMARY    XXXX   \n", "222  016023-TFSL  002013_0200000374366              0    PRIMARY    XXXX   \n", "223         3084                  3084              0    PRIMARY    XXXX   \n", "\n", "                 credit_grantor  ...  \\\n", "0          MUTHOOT MICROFIN LTD  ...   \n", "1    SATYA MICROCAPITAL LIMITED  ...   \n", "2                          XXXX  ...   \n", "3          MIDLAND MICROFIN LTD  ...   \n", "4          MIDLAND MICROFIN LTD  ...   \n", "..                          ...  ...   \n", "219                        XXXX  ...   \n", "220               HDFC BANK LTD  ...   \n", "221          HMPL NIDHI LIMITED  ...   \n", "222                    YES BANK  ...   \n", "223                 LNT FINANCE  ...   \n", "\n", "                                         high_crd_hist  \\\n", "0                                                 None   \n", "1                                                 None   \n", "2    50000,50000,50000,50000,50000,50000,50000,5000...   \n", "3                                                    ,   \n", "4                                                 None   \n", "..                                                 ...   \n", "219  73827,73827,73827,73827,73827,73827,73827,7382...   \n", "220                                                  ,   \n", "221                                               None   \n", "222                                                  ,   \n", "223                                                  ,   \n", "\n", "                                          cur_bal_hist  \\\n", "0    39044,39044,39044,39044,39044,39044,40739,4073...   \n", "1    33029,33029,33029,33029,33029,33029,34496,3593...   \n", "2    41938,41938,41938,41938,43765,56951,59631,6231...   \n", "3                                           0,411,840,   \n", "4    20446,20446,20446,20446,20446,20446,20446,2044...   \n", "..                                                 ...   \n", "219  54389,54389,54389,54389,54389,54389,54389,5438...   \n", "220  0,0,1659,1659,1659,1659,1659,1659,3283,4875,64...   \n", "221  24272,24272,24272,24272,24272,24272,24272,2427...   \n", "222  0,5664,7525,11123,11123,12861,12861,14568,1456...   \n", "223  0,,1885,1904,3534,5130,4915,8154,14304,9204,92...   \n", "\n", "                                              das_hist  \\\n", "0    S06S06S06S05S05S05S05S05S05S04S04S04S04XXXS04S...   \n", "1                    S05S05S05S05S05S04S04S04S04S04S04   \n", "2                       S05S05S05S04S04S04S04S04S04S04   \n", "3                                            S07S04S04   \n", "4    S05S05S05S05S05S05S05S05S05S05S04S04S04S04S04S...   \n", "..                                                 ...   \n", "219  S05S05S05S05S05S05S05S05S05S05S05S05S05S05XXXS...   \n", "220  S07S04S05S05S05S05S05S05S05S05S05S05S05S05S05S...   \n", "221  S05S05S05S05S05S05S05S05S05S05S05S05S05S05S05S...   \n", "222  S07S05S05S05S05S05S05S05S04S04S04S05S05S05S04S...   \n", "223  S07XXXS05S04S04S04S04S04S04S04S04S04S04S04S04S...   \n", "\n", "                                      amt_overdue_hist  \\\n", "0    21360,18690,16020,13350,10680,8010,8010,5340,2...   \n", "1                5579,4013,3015,3015,1494,0,0,0,0,0,0,   \n", "2                        8040,5360,2680,0,0,0,0,0,0,0,   \n", "3                                                 ,,0,   \n", "4    22817,21799,19619,17439,15259,13080,10900,8720...   \n", "..                                                 ...   \n", "219  63986,61204,58422,55640,52858,50076,47294,4451...   \n", "220  0,0,1693,1693,1693,1693,1693,1693,1693,1693,16...   \n", "221  45751,45174,44597,44039,43462,42904,42327,4175...   \n", "222  0,5875,5900,7875,5900,5900,3925,3925,0,0,0,197...   \n", "223  0,,1642,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,...   \n", "\n", "                                         amt_paid_hist    income  \\\n", "0                                                 None   25000.0   \n", "1                                                 None   25000.0   \n", "2    13400,13400,13400,13400,10720,8040,5360,2680,0,0,   12000.0   \n", "3                                                    ,   16000.0   \n", "4                                                 None   16000.0   \n", "..                                                 ...       ...   \n", "219               ,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,       0.0   \n", "220                                                  ,      None   \n", "221                                               None       0.0   \n", "222                                                  ,    8333.0   \n", "223                                                  ,  165000.0   \n", "\n", "    income_indicator tenure occupation  BankMstID  \n", "0                  M   None        nan          9  \n", "1                  M   None        nan          9  \n", "2                  M   24.0        nan          9  \n", "3                  M   None        nan          9  \n", "4                  M   None        nan          9  \n", "..               ...    ...        ...        ...  \n", "219             None   36.0        nan          9  \n", "220             None   None        nan          9  \n", "221                M   None        nan          9  \n", "222                M   None        nan          9  \n", "223                M   None        nan          9  \n", "\n", "[224 rows x 42 columns]"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 28, "id": "4c4e6970", "metadata": {}, "outputs": [], "source": ["query = \"select * from credit_report\""]}, {"cell_type": "code", "execution_count": 29, "id": "c6f7f268", "metadata": {}, "outputs": [], "source": ["from sqlalchemy import text"]}, {"cell_type": "code", "execution_count": 30, "id": "77ce7e92", "metadata": {}, "outputs": [], "source": ["df_db = pd.read_sql(text(query), engine)"]}, {"cell_type": "code", "execution_count": 31, "id": "683e4cae", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['credt_rpt_id', 'los_app_id', 'candidate_id', 'customer_id_mbr_id',\n", "       'Branch_name', 'kendra', 'self_indicator', 'match_type', 'acc_num',\n", "       'credit_grantor', 'acct_type', 'contributor_type', 'date_reported',\n", "       'ownership_ind', 'account_status', 'disbursed_dt', 'close_dt',\n", "       'last_payment_date', 'credit_limit_sanc_amt',\n", "       'disbursed_amt_high_credit', 'installment_amt', 'current_bal',\n", "       'installment_frequency', 'write_off_date', 'overdue_amt',\n", "       'write_off_amt', 'asset_class', 'account_remarks', 'linked_accounts',\n", "       'reported_date_hist', 'dpd_hist', 'asset_class_hist', 'high_crd_hist',\n", "       'cur_bal_hist', 'das_hist', 'amt_overdue_hist', 'amt_paid_hist',\n", "       'income', 'income_indicator', 'tenure', 'occupation', 'BankMstID'],\n", "      dtype='object')"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["df_db.columns"]}, {"cell_type": "code", "execution_count": 32, "id": "5ffbfedf", "metadata": {}, "outputs": [], "source": ["expected_columns = ['credt_rpt_id', 'los_app_id', 'candidate_id', 'customer_id_mbr_id',\n", "       'Branch_name', 'kendra', 'self_indicator', 'match_type', 'acc_num',\n", "       'credit_grantor', 'acct_type', 'contributor_type', 'date_reported',\n", "       'ownership_ind', 'account_status', 'disbursed_dt', 'close_dt',\n", "       'last_payment_date', 'credit_limit_sanc_amt',\n", "       'disbursed_amt_high_credit', 'installment_amt', 'current_bal',\n", "       'installment_frequency', 'write_off_date', 'overdue_amt',\n", "       'write_off_amt', 'asset_class', 'account_remarks', 'linked_accounts',\n", "       'reported_date_hist', 'dpd_hist', 'asset_class_hist', 'high_crd_hist',\n", "       'cur_bal_hist', 'das_hist', 'amt_overdue_hist', 'amt_paid_hist',\n", "       'income', 'income_indicator', 'tenure', 'occupation', 'BankMstID']"]}, {"cell_type": "code", "execution_count": 33, "id": "cc76ccff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['credt_rpt_id', 'los_app_id', 'candidate_id', 'customer_id_mbr_id', 'Branch_name', 'kendra', 'self_indicator', 'match_type', 'acc_num', 'credit_grantor', 'acct_type', 'contributor_type', 'date_reported', 'ownership_ind', 'account_status', 'disbursed_dt', 'close_dt', 'last_payment_date', 'credit_limit_sanc_amt', 'disbursed_amt_high_credit', 'installment_amt', 'current_bal', 'installment_frequency', 'write_off_date', 'overdue_amt', 'write_off_amt', 'asset_class', 'account_remarks', 'linked_accounts', 'reported_date_hist', 'dpd_hist', 'asset_class_hist', 'high_crd_hist', 'cur_bal_hist', 'das_hist', 'amt_overdue_hist', 'amt_paid_hist', 'income', 'income_indicator', 'tenure', 'occupation', 'BankMstID']\n"]}], "source": ["print(df.columns.tolist())\n"]}, {"cell_type": "code", "execution_count": 34, "id": "b9361dbc", "metadata": {}, "outputs": [], "source": ["df['credt_rpt_id'].drop_duplicates(inplace=True)"]}, {"cell_type": "code", "execution_count": 35, "id": "f4801d9d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>credt_rpt_id</th>\n", "      <th>los_app_id</th>\n", "      <th>candidate_id</th>\n", "      <th>customer_id_mbr_id</th>\n", "      <th>Branch_name</th>\n", "      <th>kendra</th>\n", "      <th>self_indicator</th>\n", "      <th>match_type</th>\n", "      <th>acc_num</th>\n", "      <th>credit_grantor</th>\n", "      <th>...</th>\n", "      <th>high_crd_hist</th>\n", "      <th>cur_bal_hist</th>\n", "      <th>das_hist</th>\n", "      <th>amt_overdue_hist</th>\n", "      <th>amt_paid_hist</th>\n", "      <th>income</th>\n", "      <th>income_indicator</th>\n", "      <th>tenure</th>\n", "      <th>occupation</th>\n", "      <th>BankMstID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>**********</td>\n", "      <td>nan</td>\n", "      <td>THASRA MML</td>\n", "      <td>ML0493S15C0410</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>MUTHOOT MICROFIN LTD</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>39044,39044,39044,39044,39044,39044,40739,4073...</td>\n", "      <td>S06S06S06S05S05S05S05S05S05S04S04S04S04XXXS04S...</td>\n", "      <td>21360,18690,16020,13350,10680,8010,8010,5340,2...</td>\n", "      <td>None</td>\n", "      <td>25000.0</td>\n", "      <td>M</td>\n", "      <td>None</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>**********</td>\n", "      <td>nan</td>\n", "      <td>444</td>\n", "      <td>*********</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>SATYA MICROCAPITAL LIMITED</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>33029,33029,33029,33029,33029,33029,34496,3593...</td>\n", "      <td>S05S05S05S05S05S04S04S04S04S04S04</td>\n", "      <td>5579,4013,3015,3015,1494,0,0,0,0,0,0,</td>\n", "      <td>None</td>\n", "      <td>25000.0</td>\n", "      <td>M</td>\n", "      <td>None</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>9252668405</td>\n", "      <td>nan</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>XXXX</td>\n", "      <td>...</td>\n", "      <td>50000,50000,50000,50000,50000,50000,50000,5000...</td>\n", "      <td>41938,41938,41938,41938,43765,56951,59631,6231...</td>\n", "      <td>S05S05S05S04S04S04S04S04S04S04</td>\n", "      <td>8040,5360,2680,0,0,0,0,0,0,0,</td>\n", "      <td>13400,13400,13400,13400,10720,8040,5360,2680,0,0,</td>\n", "      <td>12000.0</td>\n", "      <td>M</td>\n", "      <td>24.0</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>6794813831</td>\n", "      <td>nan</td>\n", "      <td>291</td>\n", "      <td>29100152</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>MIDLAND MICROFIN LTD</td>\n", "      <td>...</td>\n", "      <td>,</td>\n", "      <td>0,411,840,</td>\n", "      <td>S07S04S04</td>\n", "      <td>,,0,</td>\n", "      <td>,</td>\n", "      <td>16000.0</td>\n", "      <td>M</td>\n", "      <td>None</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>6794813831</td>\n", "      <td>nan</td>\n", "      <td>291</td>\n", "      <td>29100152</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>MIDLAND MICROFIN LTD</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>20446,20446,20446,20446,20446,20446,20446,2044...</td>\n", "      <td>S05S05S05S05S05S05S05S05S05S05S04S04S04S04S04S...</td>\n", "      <td>22817,21799,19619,17439,15259,13080,10900,8720...</td>\n", "      <td>None</td>\n", "      <td>16000.0</td>\n", "      <td>M</td>\n", "      <td>None</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>219</th>\n", "      <td>HIND250222CR5496706210843</td>\n", "      <td>1004</td>\n", "      <td>4978124343</td>\n", "      <td>nan</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>XXXX</td>\n", "      <td>...</td>\n", "      <td>73827,73827,73827,73827,73827,73827,73827,7382...</td>\n", "      <td>54389,54389,54389,54389,54389,54389,54389,5438...</td>\n", "      <td>S05S05S05S05S05S05S05S05S05S05S05S05S05S05XXXS...</td>\n", "      <td>63986,61204,58422,55640,52858,50076,47294,4451...</td>\n", "      <td>,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>36.0</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>220</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>*********</td>\n", "      <td>nan</td>\n", "      <td>DAKOR</td>\n", "      <td>DAKOR</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>HDFC BANK LTD</td>\n", "      <td>...</td>\n", "      <td>,</td>\n", "      <td>0,0,1659,1659,1659,1659,1659,1659,3283,4875,64...</td>\n", "      <td>S07S04S05S05S05S05S05S05S05S05S05S05S05S05S05S...</td>\n", "      <td>0,0,1693,1693,1693,1693,1693,1693,1693,1693,16...</td>\n", "      <td>,</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>221</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>**********</td>\n", "      <td>nan</td>\n", "      <td>30</td>\n", "      <td>324</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>HMPL NIDHI LIMITED</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>24272,24272,24272,24272,24272,24272,24272,2427...</td>\n", "      <td>S05S05S05S05S05S05S05S05S05S05S05S05S05S05S05S...</td>\n", "      <td>45751,45174,44597,44039,43462,42904,42327,4175...</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>M</td>\n", "      <td>None</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>222</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>*********</td>\n", "      <td>nan</td>\n", "      <td>016023-TFSL</td>\n", "      <td>002013_0200000374366</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>YES BANK</td>\n", "      <td>...</td>\n", "      <td>,</td>\n", "      <td>0,5664,7525,11123,11123,12861,12861,14568,1456...</td>\n", "      <td>S07S05S05S05S05S05S05S05S04S04S04S05S05S05S04S...</td>\n", "      <td>0,5875,5900,7875,5900,5900,3925,3925,0,0,0,197...</td>\n", "      <td>,</td>\n", "      <td>8333.0</td>\n", "      <td>M</td>\n", "      <td>None</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>223</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>*********</td>\n", "      <td>nan</td>\n", "      <td>3084</td>\n", "      <td>3084</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>LNT FINANCE</td>\n", "      <td>...</td>\n", "      <td>,</td>\n", "      <td>0,,1885,1904,3534,5130,4915,8154,14304,9204,92...</td>\n", "      <td>S07XXXS05S04S04S04S04S04S04S04S04S04S04S04S04S...</td>\n", "      <td>0,,1642,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,...</td>\n", "      <td>,</td>\n", "      <td>165000.0</td>\n", "      <td>M</td>\n", "      <td>None</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>224 rows × 42 columns</p>\n", "</div>"], "text/plain": ["                  credt_rpt_id  los_app_id candidate_id customer_id_mbr_id  \\\n", "0    HIND250222CR5536706210843        1008   **********                nan   \n", "1    HIND250222CR5536706210843        1008   **********                nan   \n", "2    HIND250222CR5536706210843        1008   9252668405                nan   \n", "3    HIND250222CR5536706210843        1008   6794813831                nan   \n", "4    HIND250222CR5536706210843        1008   6794813831                nan   \n", "..                         ...         ...          ...                ...   \n", "219  HIND250222CR5496706210843        1004   4978124343                nan   \n", "220  HIND250222CR5396706210843        1002    *********                nan   \n", "221  HIND250222CR5396706210843        1002   **********                nan   \n", "222  HIND250222CR5396706210843        1002    *********                nan   \n", "223  HIND250222CR5396706210843        1002    *********                nan   \n", "\n", "     Branch_name                kendra self_indicator match_type acc_num  \\\n", "0     THASRA MML        ML0493S15C0410              0    PRIMARY    XXXX   \n", "1            444             *********              0    PRIMARY    XXXX   \n", "2           None                  None              0    PRIMARY    XXXX   \n", "3            291              29100152              0    PRIMARY    XXXX   \n", "4            291              29100152              0    PRIMARY    XXXX   \n", "..           ...                   ...            ...        ...     ...   \n", "219         None                  None              0    PRIMARY    XXXX   \n", "220        DAKOR                 DAKOR              0    PRIMARY    XXXX   \n", "221           30                   324              0    PRIMARY    XXXX   \n", "222  016023-TFSL  002013_0200000374366              0    PRIMARY    XXXX   \n", "223         3084                  3084              0    PRIMARY    XXXX   \n", "\n", "                 credit_grantor  ...  \\\n", "0          MUTHOOT MICROFIN LTD  ...   \n", "1    SATYA MICROCAPITAL LIMITED  ...   \n", "2                          XXXX  ...   \n", "3          MIDLAND MICROFIN LTD  ...   \n", "4          MIDLAND MICROFIN LTD  ...   \n", "..                          ...  ...   \n", "219                        XXXX  ...   \n", "220               HDFC BANK LTD  ...   \n", "221          HMPL NIDHI LIMITED  ...   \n", "222                    YES BANK  ...   \n", "223                 LNT FINANCE  ...   \n", "\n", "                                         high_crd_hist  \\\n", "0                                                 None   \n", "1                                                 None   \n", "2    50000,50000,50000,50000,50000,50000,50000,5000...   \n", "3                                                    ,   \n", "4                                                 None   \n", "..                                                 ...   \n", "219  73827,73827,73827,73827,73827,73827,73827,7382...   \n", "220                                                  ,   \n", "221                                               None   \n", "222                                                  ,   \n", "223                                                  ,   \n", "\n", "                                          cur_bal_hist  \\\n", "0    39044,39044,39044,39044,39044,39044,40739,4073...   \n", "1    33029,33029,33029,33029,33029,33029,34496,3593...   \n", "2    41938,41938,41938,41938,43765,56951,59631,6231...   \n", "3                                           0,411,840,   \n", "4    20446,20446,20446,20446,20446,20446,20446,2044...   \n", "..                                                 ...   \n", "219  54389,54389,54389,54389,54389,54389,54389,5438...   \n", "220  0,0,1659,1659,1659,1659,1659,1659,3283,4875,64...   \n", "221  24272,24272,24272,24272,24272,24272,24272,2427...   \n", "222  0,5664,7525,11123,11123,12861,12861,14568,1456...   \n", "223  0,,1885,1904,3534,5130,4915,8154,14304,9204,92...   \n", "\n", "                                              das_hist  \\\n", "0    S06S06S06S05S05S05S05S05S05S04S04S04S04XXXS04S...   \n", "1                    S05S05S05S05S05S04S04S04S04S04S04   \n", "2                       S05S05S05S04S04S04S04S04S04S04   \n", "3                                            S07S04S04   \n", "4    S05S05S05S05S05S05S05S05S05S05S04S04S04S04S04S...   \n", "..                                                 ...   \n", "219  S05S05S05S05S05S05S05S05S05S05S05S05S05S05XXXS...   \n", "220  S07S04S05S05S05S05S05S05S05S05S05S05S05S05S05S...   \n", "221  S05S05S05S05S05S05S05S05S05S05S05S05S05S05S05S...   \n", "222  S07S05S05S05S05S05S05S05S04S04S04S05S05S05S04S...   \n", "223  S07XXXS05S04S04S04S04S04S04S04S04S04S04S04S04S...   \n", "\n", "                                      amt_overdue_hist  \\\n", "0    21360,18690,16020,13350,10680,8010,8010,5340,2...   \n", "1                5579,4013,3015,3015,1494,0,0,0,0,0,0,   \n", "2                        8040,5360,2680,0,0,0,0,0,0,0,   \n", "3                                                 ,,0,   \n", "4    22817,21799,19619,17439,15259,13080,10900,8720...   \n", "..                                                 ...   \n", "219  63986,61204,58422,55640,52858,50076,47294,4451...   \n", "220  0,0,1693,1693,1693,1693,1693,1693,1693,1693,16...   \n", "221  45751,45174,44597,44039,43462,42904,42327,4175...   \n", "222  0,5875,5900,7875,5900,5900,3925,3925,0,0,0,197...   \n", "223  0,,1642,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,...   \n", "\n", "                                         amt_paid_hist    income  \\\n", "0                                                 None   25000.0   \n", "1                                                 None   25000.0   \n", "2    13400,13400,13400,13400,10720,8040,5360,2680,0,0,   12000.0   \n", "3                                                    ,   16000.0   \n", "4                                                 None   16000.0   \n", "..                                                 ...       ...   \n", "219               ,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,       0.0   \n", "220                                                  ,      None   \n", "221                                               None       0.0   \n", "222                                                  ,    8333.0   \n", "223                                                  ,  165000.0   \n", "\n", "    income_indicator tenure occupation  BankMstID  \n", "0                  M   None        nan          9  \n", "1                  M   None        nan          9  \n", "2                  M   24.0        nan          9  \n", "3                  M   None        nan          9  \n", "4                  M   None        nan          9  \n", "..               ...    ...        ...        ...  \n", "219             None   36.0        nan          9  \n", "220             None   None        nan          9  \n", "221                M   None        nan          9  \n", "222                M   None        nan          9  \n", "223                M   None        nan          9  \n", "\n", "[224 rows x 42 columns]"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "id": "********", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 36, "id": "3893cbf1", "metadata": {}, "outputs": [], "source": ["expected_columns = [\n", "            \"credt_rpt_id\",\n", "            \"los_app_id\",\n", "            \"candidate_id\",\n", "            \"customer_id_mbr_id\",\n", "            \"Branch_name\",\n", "            \"kendra\",\n", "            \"self_indicator\",\n", "            \"match_type\",\n", "            \"acc_num\",\n", "            \"credit_grantor\",\n", "            \"acct_type\",\n", "            \"contributor_type\",\n", "            \"date_reported\",\n", "            \"ownership_ind\",\n", "            \"account_status\",\n", "            \"disbursed_dt\",\n", "            \"close_dt\",\n", "            \"last_payment_date\",\n", "            \"credit_limit_sanc_amt\",\n", "            \"disbursed_amt_high_credit\",\n", "            \"installment_amt\",\n", "            \"current_bal\",\n", "            \"installment_frequency\",\n", "            \"write_off_date\",\n", "            \"overdue_amt\",\n", "            \"write_off_amt\",\n", "            \"asset_class\",\n", "            \"account_remarks\",\n", "            \"linked_accounts\",\n", "            \"income\",\n", "            \"income_indicator\",\n", "            \"tenure\",\n", "            \"occupation\",\n", "            \"BankMstID\",\n", "            \n", "        ]"]}, {"cell_type": "code", "execution_count": 37, "id": "8047deb2", "metadata": {}, "outputs": [], "source": ["df= df[expected_columns]"]}, {"cell_type": "code", "execution_count": 38, "id": "f259143d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>credt_rpt_id</th>\n", "      <th>los_app_id</th>\n", "      <th>candidate_id</th>\n", "      <th>customer_id_mbr_id</th>\n", "      <th>Branch_name</th>\n", "      <th>kendra</th>\n", "      <th>self_indicator</th>\n", "      <th>match_type</th>\n", "      <th>acc_num</th>\n", "      <th>credit_grantor</th>\n", "      <th>...</th>\n", "      <th>overdue_amt</th>\n", "      <th>write_off_amt</th>\n", "      <th>asset_class</th>\n", "      <th>account_remarks</th>\n", "      <th>linked_accounts</th>\n", "      <th>income</th>\n", "      <th>income_indicator</th>\n", "      <th>tenure</th>\n", "      <th>occupation</th>\n", "      <th>BankMstID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>**********</td>\n", "      <td>nan</td>\n", "      <td>THASRA MML</td>\n", "      <td>ML0493S15C0410</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>MUTHOOT MICROFIN LTD</td>\n", "      <td>...</td>\n", "      <td>21360.0</td>\n", "      <td>39044.0</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>N</td>\n", "      <td>25000.0</td>\n", "      <td>M</td>\n", "      <td>None</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>**********</td>\n", "      <td>nan</td>\n", "      <td>444</td>\n", "      <td>*********</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>SATYA MICROCAPITAL LIMITED</td>\n", "      <td>...</td>\n", "      <td>5579.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>N</td>\n", "      <td>25000.0</td>\n", "      <td>M</td>\n", "      <td>None</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>9252668405</td>\n", "      <td>nan</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>XXXX</td>\n", "      <td>...</td>\n", "      <td>8040.0</td>\n", "      <td>0.0</td>\n", "      <td>SubStandard</td>\n", "      <td>None</td>\n", "      <td>N</td>\n", "      <td>12000.0</td>\n", "      <td>M</td>\n", "      <td>24.0</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>6794813831</td>\n", "      <td>nan</td>\n", "      <td>291</td>\n", "      <td>29100152</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>MIDLAND MICROFIN LTD</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>N</td>\n", "      <td>16000.0</td>\n", "      <td>M</td>\n", "      <td>None</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>HIND250222CR5536706210843</td>\n", "      <td>1008</td>\n", "      <td>6794813831</td>\n", "      <td>nan</td>\n", "      <td>291</td>\n", "      <td>29100152</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>MIDLAND MICROFIN LTD</td>\n", "      <td>...</td>\n", "      <td>22817.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>N</td>\n", "      <td>16000.0</td>\n", "      <td>M</td>\n", "      <td>None</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>219</th>\n", "      <td>HIND250222CR5496706210843</td>\n", "      <td>1004</td>\n", "      <td>4978124343</td>\n", "      <td>nan</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>XXXX</td>\n", "      <td>...</td>\n", "      <td>63986.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>N</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>36.0</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>220</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>*********</td>\n", "      <td>nan</td>\n", "      <td>DAKOR</td>\n", "      <td>DAKOR</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>HDFC BANK LTD</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>N</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>221</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>**********</td>\n", "      <td>nan</td>\n", "      <td>30</td>\n", "      <td>324</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>HMPL NIDHI LIMITED</td>\n", "      <td>...</td>\n", "      <td>45751.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>N</td>\n", "      <td>0.0</td>\n", "      <td>M</td>\n", "      <td>None</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>222</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>*********</td>\n", "      <td>nan</td>\n", "      <td>016023-TFSL</td>\n", "      <td>002013_0200000374366</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>YES BANK</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>N</td>\n", "      <td>8333.0</td>\n", "      <td>M</td>\n", "      <td>None</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>223</th>\n", "      <td>HIND250222CR5396706210843</td>\n", "      <td>1002</td>\n", "      <td>*********</td>\n", "      <td>nan</td>\n", "      <td>3084</td>\n", "      <td>3084</td>\n", "      <td>0</td>\n", "      <td>PRIMARY</td>\n", "      <td>XXXX</td>\n", "      <td>LNT FINANCE</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>N</td>\n", "      <td>165000.0</td>\n", "      <td>M</td>\n", "      <td>None</td>\n", "      <td>nan</td>\n", "      <td>9</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>224 rows × 34 columns</p>\n", "</div>"], "text/plain": ["                  credt_rpt_id  los_app_id candidate_id customer_id_mbr_id  \\\n", "0    HIND250222CR5536706210843        1008   **********                nan   \n", "1    HIND250222CR5536706210843        1008   **********                nan   \n", "2    HIND250222CR5536706210843        1008   9252668405                nan   \n", "3    HIND250222CR5536706210843        1008   6794813831                nan   \n", "4    HIND250222CR5536706210843        1008   6794813831                nan   \n", "..                         ...         ...          ...                ...   \n", "219  HIND250222CR5496706210843        1004   4978124343                nan   \n", "220  HIND250222CR5396706210843        1002    *********                nan   \n", "221  HIND250222CR5396706210843        1002   **********                nan   \n", "222  HIND250222CR5396706210843        1002    *********                nan   \n", "223  HIND250222CR5396706210843        1002    *********                nan   \n", "\n", "     Branch_name                kendra self_indicator match_type acc_num  \\\n", "0     THASRA MML        ML0493S15C0410              0    PRIMARY    XXXX   \n", "1            444             *********              0    PRIMARY    XXXX   \n", "2           None                  None              0    PRIMARY    XXXX   \n", "3            291              29100152              0    PRIMARY    XXXX   \n", "4            291              29100152              0    PRIMARY    XXXX   \n", "..           ...                   ...            ...        ...     ...   \n", "219         None                  None              0    PRIMARY    XXXX   \n", "220        DAKOR                 DAKOR              0    PRIMARY    XXXX   \n", "221           30                   324              0    PRIMARY    XXXX   \n", "222  016023-TFSL  002013_0200000374366              0    PRIMARY    XXXX   \n", "223         3084                  3084              0    PRIMARY    XXXX   \n", "\n", "                 credit_grantor  ... overdue_amt write_off_amt  asset_class  \\\n", "0          MUTHOOT MICROFIN LTD  ...     21360.0       39044.0         None   \n", "1    SATYA MICROCAPITAL LIMITED  ...      5579.0           0.0         None   \n", "2                          XXXX  ...      8040.0           0.0  SubStandard   \n", "3          MIDLAND MICROFIN LTD  ...         0.0           0.0         None   \n", "4          MIDLAND MICROFIN LTD  ...     22817.0           0.0         None   \n", "..                          ...  ...         ...           ...          ...   \n", "219                        XXXX  ...     63986.0           0.0         None   \n", "220               HDFC BANK LTD  ...         0.0           0.0         None   \n", "221          HMPL NIDHI LIMITED  ...     45751.0           0.0         None   \n", "222                    YES BANK  ...         0.0           0.0         None   \n", "223                 LNT FINANCE  ...         0.0           0.0         None   \n", "\n", "    account_remarks linked_accounts    income income_indicator tenure  \\\n", "0              None               N   25000.0                M   None   \n", "1              None               N   25000.0                M   None   \n", "2              None               N   12000.0                M   24.0   \n", "3              None               N   16000.0                M   None   \n", "4              None               N   16000.0                M   None   \n", "..              ...             ...       ...              ...    ...   \n", "219            None               N       0.0             None   36.0   \n", "220            None               N      None             None   None   \n", "221            None               N       0.0                M   None   \n", "222            None               N    8333.0                M   None   \n", "223            None               N  165000.0                M   None   \n", "\n", "    occupation  BankMstID  \n", "0          nan          9  \n", "1          nan          9  \n", "2          nan          9  \n", "3          nan          9  \n", "4          nan          9  \n", "..         ...        ...  \n", "219        nan          9  \n", "220        nan          9  \n", "221        nan          9  \n", "222        nan          9  \n", "223        nan          9  \n", "\n", "[224 rows x 34 columns]"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 39, "id": "bf5bbe35", "metadata": {}, "outputs": [{"data": {"text/plain": ["224"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["df.to_sql(\n", "    'credit_report',           \n", "    engine,                    \n", "    if_exists='append',        \n", "    index=False,               \n", "    method='multi'             \n", ")"]}, {"cell_type": "code", "execution_count": 40, "id": "6e61d8a2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                credt_rpt_id los_app_id candidate_id customer_id_mbr_id  \\\n", "0  HIND250222CR5536706210843       1008   **********                nan   \n", "1  HIND250222CR5536706210843       1008   **********                nan   \n", "2  HIND250222CR5536706210843       1008   9252668405                nan   \n", "3  HIND250222CR5536706210843       1008   6794813831                nan   \n", "4  HIND250222CR5536706210843       1008   6794813831                nan   \n", "\n", "  Branch_name          kendra self_indicator match_type acc_num  \\\n", "0  THASRA MML  ML0493S15C0410              0    PRIMARY    XXXX   \n", "1         444       *********              0    PRIMARY    XXXX   \n", "2        None            None              0    PRIMARY    XXXX   \n", "3         291        29100152              0    PRIMARY    XXXX   \n", "4         291        29100152              0    PRIMARY    XXXX   \n", "\n", "               credit_grantor  ... high_crd_hist cur_bal_hist das_hist  \\\n", "0        MUTHOOT MICROFIN LTD  ...          None         None     None   \n", "1  SATYA MICROCAPITAL LIMITED  ...          None         None     None   \n", "2                        XXXX  ...          None         None     None   \n", "3        MIDLAND MICROFIN LTD  ...          None         None     None   \n", "4        MIDLAND MICROFIN LTD  ...          None         None     None   \n", "\n", "  amt_overdue_hist amt_paid_hist   income income_indicator tenure  occupation  \\\n", "0             None          None  25000.0                M    NaN         nan   \n", "1             None          None  25000.0                M    NaN         nan   \n", "2             None          None  12000.0                M   24.0         nan   \n", "3             None          None  16000.0                M    NaN         nan   \n", "4             None          None  16000.0                M    NaN         nan   \n", "\n", "   BankMstID  \n", "0          9  \n", "1          9  \n", "2          9  \n", "3          9  \n", "4          9  \n", "\n", "[5 rows x 42 columns]\n"]}], "source": ["import pandas as pd\n", "\n", "# Read the table from database\n", "result_df = pd.read_sql(\"SELECT * FROM credit_report LIMIT 10;\", engine)\n", "\n", "# Show first few rows\n", "print(result_df.head())\n"]}, {"cell_type": "code", "execution_count": null, "id": "0f0fb018", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 41, "id": "a3973e3f", "metadata": {}, "outputs": [], "source": ["# from sqlalchemy import text\n", " \n", "# if not unique_keys.empty:\n", "#     with engine.begin() as connection:  # auto-commits transactions\n", "#         # Step 1: Create TEMPORARY table\n", "#         connection.execute(text(\"\"\"\n", "#             CREATE TEMP TABLE temp_keys (\n", "#                 credt_rpt_id VARCHAR(50)\n", "#             )\n", "#         \"\"\"))\n", " \n", "#         # Step 2: Insert into temp table\n", "#         temp_insert_query = text(\"\"\"\n", "#             INSERT INTO temp_keys (credt_rpt_id)\n", "#             VALUES (:credt_rpt_id)\n", "#         \"\"\")\n", "#         connection.execute(\n", "#             temp_insert_query,\n", "#             [{\"credt_rpt_id\": row[\"credt_rpt_id\"]} for _, row in unique_keys.iterrows()]\n", "#         )\n", " \n", "#         # Step 3: Delete from main table using temp join\n", "#         delete_query = text(\"\"\"\n", "#             DELETE FROM credit_report\n", "#             USING temp_keys\n", "#             WHERE credit_report.credt_rpt_id = temp_keys.credt_rpt_id\n", "#         \"\"\")\n", "#         result = connection.execute(delete_query)\n", "#         print(f\"Deleted {result.rowcount} existing records from credit_report\")"]}, {"cell_type": "code", "execution_count": 42, "id": "e51ed075", "metadata": {}, "outputs": [], "source": ["# with engine.begin() as conn:\n", "#     conn.execute(text(\"\"\" DROP TABLE IF EXISTS los_to_disb_map; \"\"\"))\n", "#     conn.execute(text(\"\"\" \n", "#         CREATE TEMP TABLE los_to_disb_map AS\n", "#         SELECT los_app_id, \"DisbursementID\"\n", "#         FROM (\n", "#             SELECT DISTINCT los_app_id\n", "#             FROM summary\n", "#             ORDER BY los_app_id\n", "#             LIMIT 21\n", "#         ) AS los\n", "#         JOIN (\n", "#             SELECT \"DisbursementID\"\n", "#             FROM \"AccountSummary\"\n", "#             WHERE \"BankMstID\" = 9\n", "#             ORDER BY \"DisbursementID\"\n", "#             LIMIT 21\n", "#         ) AS disb\n", "#         ON TRUE;\n", "#     \"\"\"))\n", "#     conn.execute(text(\"\"\"\n", "#         UPDATE summary s\n", "#         SET los_app_id = l.\"DisbursementID\"::BIGINT\n", "#         FROM los_to_disb_map l\n", "#         WHERE s.los_app_id = l.los_app_id;\n", "#     \"\"\"))\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.6"}}, "nbformat": 4, "nbformat_minor": 5}
@import url("../../index.css");

$light-blue: #e4f8f9;
$light-gray: #d9d9d9;
$tab-height: 3px;
$tab-width: 525px; // Increased the tab width
$table-radius: 22px;
$attempts-bg: #bdd1ff;
$disable: #787777;
$body: #e4f8f9;

.download_button {
  background-color: var(--dark-blue);
  padding: 0.2rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 1.3rem;
  border: none;
  img {
    width: 20px;
  }
}

.customTable {
  padding-top: 1.5rem;
  .name {
    font-weight: 500;
    color: #1651d7;
    font-size: 0.8rem;
    text-transform: uppercase;
  }

  //Global ant design classes
  // Pagination
  :global(.ant-pagination) {
    justify-content: center;
    margin: 0 !important;

    :global(.ant-pagination-prev),
    :global(.ant-pagination-next) {
      color: var(--dark-blue);
      border: 0 solid var(--blue);
      background: #e4f8f9;
      height: 25px;
      min-width: 15px;
      border-radius: 0px;
      margin: 0;
      button {
        border-radius: 0px;
      }
    }
    :global(.ant-pagination-item) {
      margin-right: 0;
      height: 0;
      a {
        color: $disable;
        font-size: 0.9rem;
        line-height: 23px;
        font-family: "Kanit", "Courier New", Courier, monospace;
        font-weight: normal;
      }
      &:hover {
        background-color: transparent;
      }
    }
    :global(.ant-pagination-item-active) {
      border: none;
      a {
        color: var(--dark-blue);
        font-size: 1.2rem;
        padding: 0;
      }
    }
  }

  //Table container
  :global(.ant-table-container) {
    padding: 0;
    margin-bottom: 0.5rem;
    border-start-start-radius: $table-radius;
    border-start-end-radius: $table-radius;

    // Table header
    :global(.ant-table-header) {
      position: relative;
      margin: 0 auto;
      top: -21px;
      border-radius: $table-radius;
    }

    // Table virtual body
    :global(.ant-table-tbody-virtual) {
      margin-top: -8px;
    }

    &::before {
      box-shadow: none !important;
    }
  }

  // Table rows header
  :global(.ant-table-thead > tr) {
    th {
      border-start-end-radius: 0 !important;
      background-color: var(--dark-blue);
      border-inline-end: none !important;
      color: white;
      border-bottom: none;
      font-weight: normal;
      font-family: "Kanit", "Courier New", Courier, monospace;
      text-align: center;
      padding: 0.9rem;

      &:not(:last-child):not(.ant-table-selection-column):not(
          .ant-table-row-expand-icon-cell
        ):not([colspan])::before {
        width: 0;
      }
      &:nth-child(3) {
        text-align: start;
      }
    }
  }

  // Table body
  :global(.ant-table-tbody) {
    // Body rows
    :global(.ant-table-row) {
      background-color: white;

      &:nth-child(even) {
        background-color: var(--light-green) !important;
        :global(.ant-table-cell-row-hover) {
          background-color: var(--light-green);
        }
      }
      // Cols
      :global(.ant-table-cell) {
        font-family: "Kanit", "Courier New", Courier, monospace;
        border-bottom: 2px solid white;
        padding: 1rem;
        font-weight: 500;
        text-align: center;
        padding: 0.9rem;

        &:nth-child(3) {
          text-align: start;
        }
      }

      :global(.ant-table-cell-row-hover) {
        background-color: white;
      }
    }
  }

  // Fixed Cols
  :global(.ant-table-cell-fix-right) {
    background-color: $body;
  }

  // Footer
  :global(.ant-table-summary) {
    text-align: center;
    box-shadow: 0px 4px 4px 0px #0000001a inset;
    background-color: white;

    :global(.ant-table-cell) {
      font-family: "Kanit", "Courier New", Courier, monospace;
      font-weight: 600;
      padding: 0.6rem;
    }
  }

  &.childrenTable {
    // Table rows header
    :global(.ant-table-thead > tr) {
      th {
        border-inline-end: 1px solid #f0f0f0 !important;
        padding: 0.5rem 1rem;
      }
      &:nth-child(2) {
        th {
          &:nth-child(3) {
            text-align: center;
          }
        }
      }
    }
  }
}

.tabs {
  margin-top: 0rem;

  &:global(.ant-tabs) {
    width: 100%;

    :global(.ant-tabs-nav-wrap) {
      padding: 0 0.9rem;
    }

    :global(.ant-tabs-nav) {
      :global(.ant-tabs-nav-list) {
        width: 100%;
        justify-content: space-between;

        :global(.ant-tabs-tab) {
          font-size: 15px;
          font-weight: 400;
          color: #97ace6;
          margin: 0;
          width: $tab-width;
          display: flex;
          justify-content: center;
          align-items: center;
          position: relative;
          z-index: 2;
          font-family: "Kanit", "Courier New", Courier, monospace;
          &:hover {
            color: #97ace6;
          }

          &:global(.ant-tabs-tab-active) {
            :global(.ant-tabs-tab-btn) {
              color: white;
            }
          }
        }
      }

      &::before {
        border-bottom: $tab-height solid $light-gray;
        border-radius: 20px;
        margin: 0 0.9rem;
      }

      :global(.ant-tabs-ink-bar) {
        background-color: var(--dark-blue);
        height: 50px;
        border-start-start-radius: 10px;
        border-start-end-radius: 10px;
        z-index: 1;

        &::before {
          content: "";
          position: absolute;
          bottom: 0px;
          left: -15px;
          width: 34px;
          height: 50px;
          background: var(--dark-blue);
          transform: translateX(0%);
          clip-path: polygon(100% 55%, 0 100%, 100% 100%);
        }

        &::after {
          content: "";
          position: absolute;
          bottom: 0px;
          right: -15px;
          width: 34px;
          height: 50px;
          background: var(--dark-blue);
          transform: translateX(0%);
          clip-path: polygon(0 55%, 0 100%, 100% 100%);
        }
      }
    }
  }
}

.date_text {
  font-family: "Kanit", "Courier New", Courier, monospace;
  font-size: 17px;
  font-weight: 500;
  color: var(--dark-blue);
}

.date_picker {
  border: none;
  outline: none;
  box-shadow: none;
  border-bottom: 1px solid var(--dark-blue);
  background-color: $light-blue;
  :global(.ant-picker-input > input) {
    font-weight: 300;
    color: var(--dark-blue);
    font-family: "Kanit", "Courier New", Courier, monospace;

    &::placeholder {
      font-weight: 600;
    }
  }
  &::placeholder {
    color: #0f20504a;
    font-weight: 600;
  }
}

// Responsive view
@media screen and (max-width: 768px) {
  .tabs {
    &:global(.ant-tabs) {
      :global(.ant-tabs-nav) {
        :global(.ant-tabs-nav-list) {
          :global(.ant-tabs-tab) {
            font-size: 10px;
            &:global(.ant-tabs-tab-active) {
              :global(.ant-tabs-tab-btn) {
                color: var(--dark-blue);
              }
            }
          }
        }
        &::before {
          border-width: 2px;
        }
        :global(.ant-tabs-ink-bar) {
          height: 2px;

          &::before,
          &::after {
            content: none;
          }
        }
      }
    }
  }
  .customTable {
    .name {
      font-size: 11px;
    }

    :global(.ant-table-thead > tr) {
      th {
        font-size: 10px;
        padding: 0.5rem;
      }
    }

    :global(.ant-table-tbody) {
      // Body rows
      :global(.ant-table-row) {
        :global(.ant-table-cell) {
          padding: 0.5rem;
          font-size: 10px;
          :global(.ant-typography) {
            font-size: 10px;
          }
        }
      }
    }
  }
}

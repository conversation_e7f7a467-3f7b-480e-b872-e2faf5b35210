import { Flex } from 'antd'
import AppHeader from "../AppHeader";
import React from 'react'
import TabsComponent from '../TabsComponent';
import { Analysis } from './Analysis';
import { Summary } from './Summary';
import { RetentionFailure } from './RetentionFailure';


export default function Scrub() {
   const onChange =(key)=>{};
   const items = [
      {
         key: 'Summary',
         label: 'Summary',
         children: <Summary/>
      },
      {
         key: 'analysis',
         label: 'Analysis',
         children: <Flex gap={20} vertical>
            <Analysis/>
         </Flex>,
      },
      {
         key: 'retentionFailure',
         label: 'Retention Failure',
         children: <Flex gap={20} vertical>
            <RetentionFailure/>
         </Flex>,
      },
   ];
    
  return (
    <Flex vertical gap={20} style={{width:"100%", overflowX:"hidden"}}>
      <AppHeader title={"Scrub"}/>
      <TabsComponent items={items} onChange={onChange}/>
    </Flex>
  )
}

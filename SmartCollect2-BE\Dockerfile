# Use Python 3.12.1-slim for a smaller base image
FROM python:3.12.1-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    netcat-traditional \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Set the working directory
WORKDIR /app

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt \
    && pip install gunicorn

# Copy project files
COPY ./SmartCollect2 .

# Create directory for static files
RUN mkdir -p static

# Create and set permissions for entrypoint script
COPY entrypoint.sh .
RUN chmod +x entrypoint.sh

# Expose port 8000
EXPOSE 8000

# Run entrypoint script
CMD ["./entrypoint.sh"]
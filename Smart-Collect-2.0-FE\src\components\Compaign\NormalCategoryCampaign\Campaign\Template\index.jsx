import { <PERSON><PERSON>, <PERSON>, Flex, message, Row, Select, Typography } from "antd";
import React, { useEffect, useState } from "react";
import { TemplateScripting } from "./TemplateScripting";
import {
  CAMPAIGN_DATA_PROP_TYPES,
  PERIODIC_TEMPLATE_SCRIPTING_DATA,
} from "../../../../../constant";
import ADD_ICON_IMG from "../../../../../assets/Images/add-icon.png";
import { CreateAiNewTemplate } from "./CreateAiNewTemplate";
import Style from "./_style.module.scss";
import { AXIOS } from "../../../../../apis/ho-Instance";
import PropTypes from "prop-types";

const { Text } = Typography;

export default function TemplateComponent({
  commFlowData,
  campaignData,
  setCampaignData,
}) {
  const [messageApi, contextHolder] = message.useMessage();
  const [whatsappOptions, setWhatsappOptions] = useState([]);
  const [scriptsData, setScriptsData] = useState([]);
  const [blasterOptions, setBlasterOptions] = useState([]);
  const [aiOptions, setAiOptions] = useState([]);
  const [ivrOptions, setIvrOptions] = useState([]);
  const [callingOptions, setCallingOptions] = useState([]);
  const [smsOptions, setSmsOptions] = useState([]);
  const campaignLanguage = localStorage.getItem("campaignLanguage");

  // Compute available communication types
  const usedCommTypes = new Set(
    campaignData?.templates
      ?.filter((template) => template.commFlowID !== null)
      ?.map((template) => template.commFlowID)
  );
  const allCommTypesUsed = commFlowData?.every((flow) =>
    usedCommTypes.has(flow.CommFlowID)
  ); // Check if all flowIds are used

  const communicationTypeOptions = commFlowData?.map((flow) => ({
    value: flow.CommFlowID, // Unique ID
    label: `${flow.CommFlowID} - ${flow.CommunicationType} - ${flow.BeforeAfter}`,
    disabled: campaignData?.templates.some(
      (template) => template.commFlowID === flow.CommFlowID
    ), // Disable if already selected
  }));

  // Handle Fetch Whatsapp Scripts
  const handleFetchWhatsAppScript = async ({ templateId }) => {
    try {
      const response = await AXIOS.get(`v1/getWhatsappflows/${templateId}/`);
      if (response.status === 200) {
        const updatedScripts = response.data?.whatsappflowmapping_set.map(
          (flow) => {
            let templateBody = flow.WhatsAppTemplateMappingID.TemplateBody;
            let variableMappings =
              flow.WhatsAppTemplateMappingID.whatsappvariablemapping_set;

            // Create a mapping of VariableNo to VariableField
            let variableMap = {};
            variableMappings.forEach(({ VariableNo, VariableField }) => {
              variableMap[VariableNo] = VariableField;
            });

            // Replace {1}, {2}, etc., with actual field names
            templateBody = templateBody.replace(
              /\{(\d+)\}/g,
              (match, variableNo) => {
                return variableMap[variableNo] || match; // Replace if found, else keep original
              }
            );

            return templateBody;
          }
        );
        return updatedScripts;
      }
    } catch (error) {
      console.log("Error in Whatsapp script", error?.message);
    }
  };

  // Handle Fetch IVR Scripts
  const handleFetchIVRScript = async ({ templateId }) => {
    try {
      const response = await AXIOS.get(`v1/getIVRflows/${templateId}/`);
      if (response.status === 200) {
        const updatedScripts = response.data?.ivrflowmapping_set.map((flow) => {
          let templateBody = flow.IVRTemplateMappingID.TemplateBody;
          let variableMappings =
            flow.IVRTemplateMappingID.ivrvariablemapping_set;

          // Create a mapping of VariableNo to VariableField
          let variableMap = {};
          variableMappings.forEach(({ VariableNo, VariableField }) => {
            variableMap[VariableNo] = VariableField;
          });

          // Replace {1}, {2}, etc., with actual field names
          templateBody = templateBody.replace(
            /\{(\d+)\}/g,
            (match, variableNo) => {
              return variableMap[variableNo] || match; // Replace if found, else keep original
            }
          );

          return templateBody;
        });
        return updatedScripts;
      }
    } catch (error) {
      console.log("Error in IVR script", error?.message);
    }
  };

  // Handle Fetch Whatsapp Scripts
  const handleFetchAIAppScript = async ({ templateId }) => {
    try {
      const response = await AXIOS.get(`v1/getVoiceBotflows/${templateId}/`);
      if (response.status === 200) {
        let StartMsg = response.data.StartMsg;
        let EndMsg = response.data.EndMsg;
        let templateBody = response.data.TemplateBody;
        let variableMappings = response.data.voicebotusertemplate_set;

        // Create a mapping of VariableNo to VariableField
        let variableMap = {};
        variableMappings.forEach(({ VariableNumber, VariableField }) => {
          variableMap[VariableNumber] = VariableField;
        });

        // Replace {1}, {2}, etc., with actual field names
        templateBody = templateBody.replace(
          /\{(\d+)\}/g,
          (match, VariableNumber) => {
            return variableMap[VariableNumber] || match; // Replace if found, else keep original
          }
        );
        let formattedTemplateBody = `
                          ${StartMsg}
                          ${templateBody}
                          ${EndMsg}`;
        return [formattedTemplateBody];
      }
    } catch (error) {
      console.log("Error in Ai script", error?.message);
    }
  };

  // Handle Fetch Whatsapp Scripts
  const handleFetchBlasterAppScript = async ({ templateId }) => {
    try {
      const response = await AXIOS.get(`v1/getBlasterflows/${templateId}/`);
      if (response.status === 200) {
        let templateBody = response.data.TemplateBody;
        let variableMappings = response.data.blasterusertemplate_set;

        // Create a mapping of VariableNo to VariableField
        let variableMap = {};
        variableMappings.forEach(({ VariableNumber, VariableField }) => {
          variableMap[VariableNumber] = VariableField;
        });

        // Replace {1}, {2}, etc., with actual field names
        templateBody = templateBody.replace(
          /\{(\d+)\}/g,
          (match, VariableNumber) => {
            return variableMap[VariableNumber] || match; // Replace if found, else keep original
          }
        );
        return [templateBody];
      }
    } catch (error) {
      console.log("Error in Blaster script", error?.message);
    }
  };

  // Fetch scripts
  const handleFetchTemplateScrips = async ({
    templateId,
    communicationTypeId,
  }) => {
    const matchingCommFlow = commFlowData?.find(
      (flow) => flow.CommunicationType === communicationTypeId
    );
    let scripts;
    switch (matchingCommFlow?.CommunicationType) {
      case "whatsapp":
        scripts = await handleFetchWhatsAppScript({ templateId });
        break;
      case "ai":
        scripts = await handleFetchAIAppScript({ templateId });
        break;
      case "blaster":
        scripts = await handleFetchBlasterAppScript({ templateId });
        break;
      case "ivr":
        scripts = await handleFetchIVRScript({ templateId });
        break;
      case "calling":
        scripts = await handleFetchCallingScript({ templateId });
        break;
      default:
        scripts = PERIODIC_TEMPLATE_SCRIPTING_DATA;
    }
    setScriptsData(scripts);
  };
    
  const handleFetchCallingScript = async({templateId}) => {
    try {
            const response = await AXIOS.get(`v1/getCallingflows/${templateId}/`);
            if(response.status === 200){
                let templateBody = response.data.TemplateBody;
                // Calling templates don't have variables, so just return the template body
                return [templateBody];
            }
        } catch (error) {
            console.log("Error in Calling script", error?.message);
        }
  }
    
  // communication type
  const handleCommunicationTypeChange = ({ index, value }) => {
    // Find selected flow object by CommFlowID
    const selectedFlow = commFlowData.find((flow) => flow.CommFlowID === value);
    setCampaignData((prevData) => {
      const updatedTemplates = prevData.templates.map((template, i) =>
        i === index
          ? {
              ...template,
              commFlowID: value,
              communicationType: selectedFlow.CommunicationType,
              selectedTemplateName: null,
              scriptValue: "",
              variablesMapping: [],
              buttonsMapping: [],
              intents: [{ name: "", response: "" }],
              response: [{ buttonName: "", text: "" }],
            }
          : template
      );
      return { ...prevData, templates: updatedTemplates };
    });
  };

  // template
  const handleTemplateChange = async ({ index, value, communicationType }) => {
    setCampaignData((prevData) => {
      const templates = [...prevData.templates];
      const template = templates[index];

      const options = handleShowRelatedOptions(template.communicationType);
      const selectedOption = options.find((option) => option.value === value);
      const labelName = value !== "new" ? selectedOption?.label || "" : "";

      const updatedTemplate = {
        ...template,
        name: labelName,
        selectedTemplateName: value,
        scriptValue: "",
        variablesMapping: [],
        buttonsMapping: [],
        intents: [{ name: "", response: "" }],
        response: [{ buttonName: "", text: "" }],
        showScripting: true,
      };

      templates[index] = updatedTemplate;

      return { ...prevData, templates };
    });

    await handleFetchTemplateScrips({
      templateId: value,
      communicationTypeId: communicationType,
    });
  };

  // show/hide template script
  const handleTemplateScriptChange = async ({ index, value }) => {
    setCampaignData((prevData) => {
      const updatedTemplates = prevData.templates.map((template, i) => {
        if (i !== index) return template; // Keep other templates unchanged
        return {
          ...template,
          showScripting: value,
        };
      });

      return { ...prevData, templates: updatedTemplates };
    });
  };

  // Handle add new templates
  const handleAddTemplates = () => {
    setCampaignData((prevData) => {
      // Prevent adding if all communication types are used
      if (allCommTypesUsed) {
        messageApi.error("All communication types are already used.");
        return prevData;
      }

      // Check if any existing template has missing required fields
      const incompleteTemplate = prevData.templates.some((template) => {
        // Fail if either communicationType OR selectedTemplateName is missing
        if (!template.communicationType || !template.selectedTemplateName) {
          return true;
        }
        if (template.selectedTemplateName === "new") {
          return template.showScripting;
        }
        return false;
      });

      // If any required field is missing, show an alert and prevent adding new template
      if (incompleteTemplate) {
        messageApi.error(
          "Please complete all fields, before adding a new one."
        );
        return prevData; // No changes applied
      }

      // All fields are filled, add the new template
      return {
        ...prevData,
        templates: [
          ...prevData.templates.map((t) => ({ ...t, showScripting: false })), // Hide scripting for existing ones
          {
            name: "",
            approvedTemplateId: null,
            language: null,
            showScripting: true,
            communicationType: null,
            commFlowID: null,
            selectedTemplateName: null,
            scriptValue: "",
            interactionPlans: "",
            rules: "",
            description: "",
            initialTexts: "",
            endingTexts: "",
            variablesMapping: [],
            buttonsMapping: [],
            initialTextVariableMapping: [],
            endingTextVariableMapping: [],
            intents: [{ name: "", response: "" }],
            response: [{ buttonName: "", text: "" }],
            nodes: [],
            nodeButtonMapping: [],
            isIvrFlowApproved: false,
          },
        ],
      };
    });
  };

  // Handle whatsapp options
  const handleFetchWhatsappOptions = async () => {
    try {
      const response = await AXIOS.get("v1/getWhatsappflows/", {
        params: {
          Language: campaignLanguage,
        },
      });
      if (response?.data) {
        setWhatsappOptions([
          {
            label: (
              <Button className={Style.create_new_template}>
                Create New Template
              </Button>
            ),
            value: "new",
          },
          ...response.data.map((item) => ({
            label: item.FlowName,
            value: item.WhatsAppFlowMstID,
          })),
        ]);
      }
    } catch (error) {
      console.error("Error fetching WhatsApp flows:", error);
    }
  };

  // Handle blaster options
  const handleFetchBlasterOptions = async () => {
    try {
      const response = await AXIOS.get("v1/getBlasterflows/", {
        params: {
          Language: campaignLanguage,
        },
      });
      if (response?.data) {
        setBlasterOptions([
          {
            label: (
              <Button className={Style.create_new_template}>
                Create New Template
              </Button>
            ),
            value: "new",
          },
          ...response.data.map((item) => ({
            label: item.TemplateName,
            value: item.BlasterTemplateMappingID,
          })),
        ]);
      }
    } catch (error) {
      console.error("Error fetching Blaster flows:", error);
    }
  };

  // Handle AI options
  const handleFetchAIOptions = async () => {
    try {
      const response = await AXIOS.get("v1/getVoiceBotflows/", {
        params: {
          Language: campaignLanguage,
        },
      });
      if (response?.data) {
        setAiOptions([
          {
            label: (
              <Button className={Style.create_new_template}>
                Create New Template
              </Button>
            ),
            value: "new",
          },
          ...response.data.map((item) => ({
            label: item.TemplateName,
            value: item.VoiceBotTemplateMappingID,
          })),
        ]);
      }
    } catch (error) {
      console.error("Error fetching Ai flows:", error);
    }
  };

  // Handle IVR options
  const handleFetchIvrOptions = async () => {
    try {
      const response = await AXIOS.get("v1/getIVRflows/", {
        params: {
          Language: campaignLanguage,
        },
      });
      if (response?.data) {
        setIvrOptions([
          {
            label: (
              <Button className={Style.create_new_template}>
                Create New Template
              </Button>
            ),
            value: "new",
          },
          ...response.data.map((item) => ({
            label: item?.FlowName,
            value: item?.IVRFlowMstID,
          })),
        ]);
      }
    } catch (error) {
      console.error("Error fetching Ivr flows:", error);
    }
  };

  // Handle sms options
  const handleFetchSmsOptions = async () => {
    try {
      const response = await AXIOS.get("v1/getSmsflows/", {
        params: {
          Language: campaignLanguage,
        },
      });
      if (response?.data) {
        setSmsOptions([
          {
            label: (
              <Button className={Style.create_new_template}>
                Create New Template
              </Button>
            ),
            value: "new",
          },
          ...response.data.map((item) => ({
            label: item?.TemplateName,
            value: item?.SMSTemplateMappingID,
          })),
        ]);
      }
    } catch (error) {
      console.error("Error fetching Sms flows:", error);
    }
  };

  // Handle to show related options
  const handleShowRelatedOptions = (keyName) => {
    // Find the matching commFlowData entry
    const matchingCommFlow = commFlowData?.find(
      (flow) => flow.CommunicationType === keyName
    );

    if (matchingCommFlow?.CommunicationType === "whatsapp") {
      return whatsappOptions;
    } else if (matchingCommFlow?.CommunicationType === "blaster") {
      return blasterOptions;
    } else if (matchingCommFlow?.CommunicationType === "ai") {
      return aiOptions;
    } else if (matchingCommFlow?.CommunicationType === "ivr") {
      return ivrOptions;
    } else if (matchingCommFlow?.CommunicationType === "calling") {
      return callingOptions;
    } else {
      return smsOptions;
    }
  };
    // Handle calling options
    const handleFetchCallingOptions = async () => {
        try {
            const response = await AXIOS.get("v1/getCallingflows/", {
                params: {
                    Language: campaignLanguage
                }
            });
            if (response?.data) {
                setCallingOptions([
                    ...response.data.map((item) => ({
                        label: item?.TemplateName,
                        value: item?.CallingTemplateMappingID,
                    })),
                    { 
                        label: <Button className={Style.create_new_template}>Create New Template</Button>, 
                        value: "new" 
                    },
                ]);
            }
        } catch (error) {
            console.error("Error fetching Calling flows:", error);
        }
    };

  useEffect(() => {
    handleFetchWhatsappOptions();
    handleFetchBlasterOptions();
    handleFetchAIOptions();
    handleFetchIvrOptions();
    handleFetchSmsOptions();
    handleFetchCallingOptions();
  },[]);

  return (
    <>
      {contextHolder}
      <Flex vertical className={Style.templates}>
        {campaignData?.templates.map((template, index) => {
          return (
            <Flex key={`${index}-${template.commFlowID}`} gap={20} vertical>
              <Row gutter={[20, 20]}>
                <Col md={12} xs={24}>
                  <Flex gap={10} align="center" className={Style.container}>
                    <Text className={Style.text}>Communication Type:</Text>
                    <Select
                      value={template.commFlowID}
                      className={Style.custom_select}
                      placeholder={"Select"}
                      onChange={(value) =>
                        handleCommunicationTypeChange({ index, value })
                      }
                      options={communicationTypeOptions}
                      allowClear
                      disabled={!template?.showScripting}
                    />
                  </Flex>
                </Col>

                <Col md={12} xs={24}>
                  <Flex align="center" gap={10} className={Style.container}>
                    <Text className={Style.text}>Select Template:</Text>
                    <Select
                      value={
                        template.selectedTemplateName === "new"
                          ? "Create new template"
                          : template.selectedTemplateName
                      }
                      className={Style.custom_select}
                      placeholder={"Select"}
                      onChange={(value) =>
                        handleTemplateChange({
                          index,
                          value,
                          communicationType: template.communicationType,
                        })
                      }
                      optionLabelProp="label"
                      options={handleShowRelatedOptions(
                        template.communicationType
                      )}
                      allowClear
                      disabled={!template?.showScripting}
                    />
                  </Flex>
                </Col>
              </Row>

              {/* Show scripting only for the latest template */}
              {template.showScripting &&
                template.communicationType &&
                template.selectedTemplateName &&
                (template.selectedTemplateName !== "new" ? (
                  <TemplateScripting
                    visible={template.showScripting}
                    setVisible={() =>
                      handleTemplateScriptChange({ index, value: false })
                    }
                    scriptData={scriptsData}
                  />
                ) : (
                  <CreateAiNewTemplate
                    campaignData={campaignData}
                    setCampaignData={setCampaignData}
                    template={template}
                    index={index}
                    commFlowData={commFlowData}
                  />
                ))}
            </Flex>
          );
        })}

        {/* Add button */}
        <Flex justify="end">
          <button className={Style.add_button} onClick={handleAddTemplates}>
            <div className={Style.icon_img}>
              <img src={ADD_ICON_IMG} alt="add-icon" />
            </div>
          </button>
        </Flex>
      </Flex>
    </>
  )
}
// Define the prop types
TemplateComponent.propTypes = {
  commFlowData: PropTypes.arrayOf(
    PropTypes.shape({
      CommFlowID: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      CommunicationType: PropTypes.string,
      BeforeAfter: PropTypes.string,
    })
  ),
  campaignData: CAMPAIGN_DATA_PROP_TYPES,
  setCampaignData: PropTypes.func,
};

import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Typography } from "antd";
import { formatDigits } from "../../../constant";
import Style from "./_style.module.scss";
import { CommunicationCard } from "./CommunicationCard";
import TOTAL_SVG from "../../../assets/Images/comm-total.svg";
import PROMISE_SVG from "../../../assets/Images/promise.svg";
import CLAIM_SVG from "../../../assets/Images/claims.svg";
import DENIAL_SVG from "../../../assets/Images/denial.svg";
import Wrong_Number_SVG from "../../../assets/Images/wrongNumber.svg";
import { CommunicationsChart } from "./CommunicationsChart";
import { CommunicationChannelChart } from "./CommunicationChannelChart";
import { useState } from "react";
import { ChannelDetailsTable } from "./ChannelDetailsTable";

const { Text } = Typography;

export default function ResponseSummary() {
  const [showDetailsTable, setShowDetailsTable] = useState(false);

  return (
    <Flex vertical gap={15} className={Style.communication_history}>
      {showDetailsTable ? (
        <ChannelDetailsTable setShowDetailsTable={setShowDetailsTable} />
      ) : (
        <>
          {/* button */}
          <Flex justify="end">
            <Button
              className={Style.btn}
              onClick={() => setShowDetailsTable(true)}
            >
              Details
            </Button>
          </Flex>

          {/* Total communications */}
          <Flex align="center" gap={20}>
            {/* Total */}
            <CommunicationCard
              count={formatDigits(2000)}
              icon={TOTAL_SVG}
              title={"Total Responses"}
            />
            {/* Divider */}
            <Divider
              variant="solid"
              type="vertical"
              size="large"
              className={Style.divider_container}
            />
            <Flex
              gap={20}
              align="center"
              justify="space-between"
              style={{ flex: 1 }}
            >
              <CommunicationCard
                count={formatDigits(2000)}
                icon={PROMISE_SVG}
                title={"Promises"}
                size={34}
              />
              <CommunicationCard
                count={formatDigits(2000)}
                icon={CLAIM_SVG}
                title={"Claims"}
                size={35}
              />
              <CommunicationCard
                count={formatDigits(2000)}
                icon={DENIAL_SVG}
                title={"Denials"}
                size={32}
              />
              <CommunicationCard
                count={formatDigits(2000)}
                icon={Wrong_Number_SVG}
                title={"Wrong Number"}
              />
            </Flex>
          </Flex>

          {/* Charts */}
          <Row gutter={[30, 10]}>
            <Col md={12} xs={24}>
              <CommunicationsChart />
            </Col>
            <Col md={12} xs={24}>
              <CommunicationChannelChart />
            </Col>
          </Row>
        </>
      )}
    </Flex>
  );
}

@import url("../../../../../../index.css");

.clear{
   background-color: #DB3939;
   color: white;
   border: none;
   outline: none;
   &:hover{
    background-color: #DB3939 !important;
    color: white !important;
   }
}

.text_container{
    background-color: white;
    padding:0.5rem;
    border-radius: 8px;
    
    .avatar{
          padding: 1px 2px;
          background-color: #E3F5F6;
            img{
              object-fit: contain;
            }
    }
  
    .custom_textarea{
      border: none;
      padding:0 0.3rem;
      font-size: 14px;
      font-family: 'Kanit','Courier New', Courier, monospace;
      box-shadow: none;
      color: var(--dark-blue);
      &::-webkit-scrollbar {
        display: none;
      }
    }
  
    .add_variable{
      background-color: var(--dark-blue);
      font-weight:normal;
      border: none;
      padding: 0rem 1.5rem;
      font-family: 'Kanit','Courier New', Courier, monospace;
      color: white;
      font-size: 12px;
        &:hover{
          background-color: var(--dark-blue) !important;
          border: none;
          outline: none;
          color: white !important;
        }  
    }
}

.filters_container{
  background-color:#E3F5F6;
  border-radius: 8px;
  padding: 1rem 0;

  .custom_flex{
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .input_text{
      font-family: 'Kanit','Courier New', Courier, monospace;
      font-size: 14px;
      color: var(--dark-blue);
      white-space: nowrap;
  }

  .custom_input{
      caret-color: #407BFF;
      padding:0.3rem 0.5rem;

      &:global(.ant-input-outlined){
          border:none;
          color: var(--dark-blue);
          border-radius: 6px;
          box-shadow: 0px 2px 2px 0px #00000040 inset;
          font-family: 'Kanit','Courier New', Courier, monospace;
      }
  }

  .custom_select{
    width:100%;
    border-radius: 10px;
    box-shadow: 0px 4px 4px 0px #00000040;

    :global(.ant-select-selector){
        color: var(--dark-blue);
        border:none !important;
        padding: 1.1rem;
        box-shadow: none !important;
        font-family: 'Kanit','Courier New', Courier, monospace;
    }     
    :global(.ant-select-selection-item){
        font-weight: 400;
        color: var(--dark-blue);
    }
    :global(.ant-select-selection-placeholder){
        color: #0F205052;
        font-weight: 400;
    }
  }
}

.variables_container{  
    &::-webkit-scrollbar {
        display: none;
    }
  
    .container{
      background-color: white;
      padding: 1rem;
      border-radius: 8px;
      
      .custom_input_number{
        width: 300px;
        outline: none;
        background-color: #E3F5F6;
        border: none;
        box-shadow: 0px 2px 2px 0px #00000040 inset;

        :global(.ant-picker-outlined),
        :global(.ant-input-number),
        :global(.ant-input-number-group-addon){
            background-color: var(--dark-blue);
            border:none !important;
            width: 200px;
        }
    
        :global(.ant-input-number-group-addon){
           background-color: #E3F5F6; 
           width: 0;
           padding: 0 0 0 5px;
        }
    
        :global(.ant-input-number){
            width: 186px;
        }
        :global(.ant-input-number-input){
           color: var(--dark-blue);
           font-weight: 500;
           font-family: 'Kanit','Courier New', Courier, monospace;
        }
        
        :global(.ant-input-number-outlined){
            &:focus-within{
               box-shadow:none !important;
            }
        }
    
        :global(.ant-input-number-handler-wrap){
            display: none;
        }
      }
      
      .custom_select{
        width:300px;
        border-radius: 10px;
        box-shadow: 0px 4px 4px 0px #00000040;
  
        :global(.ant-select-selector){
            color: var(--dark-blue);
            border:none !important;
            padding: 1.1rem;
            box-shadow: none !important;
            font-family: 'Kanit','Courier New', Courier, monospace;
            background-color: #E3F5F6;
        }     
        :global(.ant-select-selection-item){
            font-weight: 400;
            color: var(--dark-blue);
        }
        :global(.ant-select-selection-placeholder){
            color: #0F205052;
            font-weight: 400;
        }
      }
  
      .custom_input{
        caret-color: #407BFF;
        padding:0.3rem 0.5rem;
        width:300px;
        &:global(.ant-input-outlined){
            border:none;
            color: var(--dark-blue);
            background-color: #E3F5F6;
            font-weight: 400;
            border-radius: 6px;
            box-shadow: 0px 2px 2px 0px #00000040 inset;
            font-family: 'Kanit','Courier New', Courier, monospace;
        }
      }
      .submit_variable{
          background-color: var(--dark-blue);
          font-weight:normal;
          border: none;
          padding: 0rem 1.5rem;
          font-family: 'Kanit','Courier New', Courier, monospace;
          color: white !important;
          font-size: 12px;
            &:hover{
              background-color: var(--dark-blue) !important;
              border: none;
              outline: none;
              color: white !important;
            } 
      }
    }
}

.intent_container{
    background-color: white;
    padding: 1rem;
    border-radius: 8px; 

    .intent_variables_container{
        &::-webkit-scrollbar {
            display: none;
        }
      
        .custom_input{
            caret-color: #407BFF;
            padding:0.3rem 0.5rem;
            width:300px;
            &:global(.ant-input-outlined){
                border:none;
                color: var(--dark-blue);
                background-color: #E3F5F6;
                font-weight: 400;
                border-radius: 6px;
                box-shadow: 0px 2px 2px 0px #00000040 inset;
                font-family: 'Kanit','Courier New', Courier, monospace;
            }
        }

        :global(.ant-picker-outlined),
        :global(.ant-input-number){
              background-color: #E4F8F9;
              border:none !important;
              width: 200px;
              box-shadow: 0px 2px 2px 0px #00000040 inset;
        }
            
        :global(.ant-input-number){
            width: 186px;
        }

        :global(.ant-input-number-input){
          color: var(--dark-blue);
          font-weight: 500;
          font-family: 'Kanit','Courier New', Courier, monospace;
        }
          
        :global(.ant-input-number-outlined){
          &:focus-within{
            box-shadow:none !important;
          }
        }
      
        :global(.ant-input-number-handler-wrap){
          display: none;
        }
        
    }

    .submit_variable{
        background-color: var(--dark-blue);
        font-weight:normal;
        border: none;
        padding: 0rem 1.5rem;
        font-family: 'Kanit','Courier New', Courier, monospace;
        color: white !important;
        font-size: 12px;
          &:hover{
            background-color: var(--dark-blue) !important;
            border: none;
            outline: none;
            color: white !important;
          } 
    }
}

.text{
    color: var(--dark-blue);
    font-family: 'Kanit','Courier New', Courier, monospace;
    white-space: nowrap;
}

.submit_variable{
    background-color: var(--dark-blue);
    font-weight:normal;
    border: none;
    padding: 0rem 1.5rem;
    font-family: 'Kanit','Courier New', Courier, monospace;
    color: white !important;
    font-size: 12px;
      &:hover{
        background-color: var(--dark-blue) !important;
        border: none;
        outline: none;
        color: white !important;
      } 
}

.date_picker{
  border:none;
  outline: none;
  width: 300px;
  caret-color: #407BFF;
  box-shadow: 0px 2px 2px 0px #00000040 inset;
  background-color: #E3F5F6;
  padding: 0.4rem;
  :global(.ant-picker-input>input){
      font-weight: 400;
      color:var(--dark-blue);
      font-family: 'Kanit','Courier New', Courier, monospace;

      &::placeholder{
      font-weight: 400;
      }
      
  }
  &:focus-within,
  &:hover{
    border: none;
    outline: none;
    box-shadow: 0px 2px 2px 0px #00000040 inset;
    background-color: #E3F5F6;
  }
  &::placeholder{
      color: #0F20504A;
      font-weight: 400;
  }
}
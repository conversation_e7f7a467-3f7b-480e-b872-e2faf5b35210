import React, { useState,useEffect } from 'react'
import AppHeader from '../AppHeader'
import ApplicationLoader from '../ApplicationLoader'
import { Col, Flex, Row, Typography } from 'antd'
import { useParams } from 'react-router'
import { CustomerDetailsCard } from './CustomerDetailsCard';
import DataLoading from '../Loaders/DataLoading';
import CustomersTable from './CustomersTable'
import { AXIOS } from '../../apis/ho-Instance';
const { Title } = Typography;

export default function CallingAgentsSummary() {
    const params = useParams();
    const [isSwitchOn, setIsSwitchOn] = useState(false);
    const [displayedData, setDisplayedData] = useState([]);
    const [isCustomerDataLoading, setIsCustomerDataLoading] = useState(false);

    // Function to get the appropriate endpoint based on URL parameter
    const getEndpointFromParams = () => {
        const summaryName = params.summaryName?.toLowerCase().replace(/\s+/g, '-');
        
        switch(summaryName) {
            case 'total-tasks':
                return 'total_task/';
            case 'pending-tasks':
                return 'pending_task/';
            case 'completed-tasks':
                return 'completed_task/';
            default:
                return 'total_task/';
        }
    };

    // Function to transform API data to component format
    const transformApiData = (apiData) => {
        if (!apiData?.loan_details) return [];
        
        return apiData.loan_details.map(loan => ({
            loanmstid: loan.LoanMstID,
            branchmstid: loan.BranchMstID || 0,
            communication_count: 0, // Not available in API response
            last_5_communication_types: [], // Not available in API response
            status: loan.FeedbackStatus || "Pending",
            customername: loan.CustomerName,
            customerid: loan.CustomerID,
            branchname: loan.BranchName,
            disbursementid: loan.DisbursementID,
            dpd: loan.DPD,
            feedbackdate: loan.FeedbackDate,
            loantype: loan.LoanType,
            overdue_amount: loan.OverDueAmt,
            promise_amount: loan.promise_amount || 0,
            promise_date: loan.promise_date,
            responsedatetime: loan.FeedbackDate, // Using FeedbackDate as response date
            first_time_arrer: false, // Not available in API response
            modalButtonText: "Feedback",
            isModal: true,
        }));
    };

    // Function to get display title based on URL parameter
    const getDisplayTitle = () => {
        const summaryName = params.summaryName?.toLowerCase().replace(/\s+/g, '-');
        
        switch(summaryName) {
            case 'total-tasks':
                return 'Total Tasks Summary';
            case 'pending-tasks':
                return 'Pending Tasks Summary';
            case 'completed-tasks':
                return 'Completed Tasks Summary';
            default:
                return summaryName || "Summary";
        }
    };

    useEffect(() => {
        const fetchData = async () => {
            setIsCustomerDataLoading(true);
            try {
                const endpoint = getEndpointFromParams();
                const response = await AXIOS.get(endpoint);
                
                if (response.status === 200 && response.data.status === 'success') {
                    const transformedData = transformApiData(response.data);
                    setDisplayedData(transformedData);
                }
            } catch (error) {
                console.error("Error fetching data:", error);
                setDisplayedData([]);
            } finally {
                setIsCustomerDataLoading(false);
            }
        };

        fetchData();
    }, [params.summaryName]);
  return (
        <Flex vertical>
            <AppHeader 
              title={getDisplayTitle()}
              isSwitchOn={isSwitchOn}
              setIsSwitchOn={setIsSwitchOn}
              isDashboard={false}
            />
            {/* Details */}
            {isCustomerDataLoading ? (
            <ApplicationLoader />
            ) : (
            <div style={{paddingTop:"3rem"}}>
                {!isSwitchOn ? (
                 <Row gutter={[30, 30]} align={"stretch"} wrap>
                    {displayedData?.length ? (
                        displayedData?.map((data, index) => {
                        return (
                            <Col xl={8} md={12} xs={24} key={`${index}-${data.loanmstid}`}>
                                <CustomerDetailsCard
                                    loanmstid={data?.loanmstid}
                                    branchMstId={data?.branchmstid}
                                    communicationCount={data?.communication_count}
                                    last5CommunicationTypes={data?.last_5_communication_types}
                                    status={data?.status}
                                    name={data?.customername || "Customer Name"}
                                    customerId={data?.customerid}
                                    branchName={data?.branchname}
                                    disbursementId={data?.disbursementid}
                                    dpdAmount={data?.dpd || 0}
                                    feedbackDate={data?.feedbackdate}
                                    loanType={data?.loantype}
                                    overdueAmount={data?.overdue_amount || 0}
                                    promiseAmount={data?.promise_amount || 0}
                                    promiseDate={data?.promise_date}
                                    responseDate={data?.responsedatetime}
                                    firstTimeDefaulter={data?.first_time_arrer}
                                    isModal={true}
                                />
                            </Col>
                        );
                        })
                    ) : (
                        <Col span={24}>
                        <Flex justify="center">
                            <Title level={4}>No data found!</Title>
                        </Flex>
                        </Col>
                    )}

                    {/* Show the loader */}
                    {isCustomerDataLoading && <DataLoading/>}
                </Row>
                ) : (
                <CustomersTable
                    title={"Wrong Number"}
                    customerData={displayedData}
                    modalButtonText={"Update mobile number"}
                />
                )}
            </div>
            )}
        </Flex>
  )
}

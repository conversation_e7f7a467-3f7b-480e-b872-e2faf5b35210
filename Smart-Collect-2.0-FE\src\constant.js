import LANDING_UPLOAD_IMG from "./assets/Images/tablet-with-upload-data-button-screen 1.png";
import WHATSAPP_IMG from "./assets/Images/whatsapp.png";
import AI_CALLING_IMG from "./assets/Images/ai.png";
import BLASTER_IMG from "./assets/Images/blaster.png";
import IVR_IMG from "./assets/Images/customer-care.png";
import MESSAGE_IMG from "./assets/Images/message_icon.png";
import TODAY_IMG from "./assets/Images/today.png";
import YESTERDAY_IMG from "./assets/Images/yesterday.png";
import ACTIVE_IMG from "./assets/Images/active.png";
import FIELD_IMG from "./assets/Images/field.png";
import DASHBOARD_IMG from "./assets/Images/dash.png";
import ENGAGEMENT_SUMMARY_IMG from "./assets/Images/engagement.png";
import DASHBOARD__IMG from "./assets/Images/Frame.png";
import HO_DASHBOARD_IMG from "./assets/Images/dashboard.svg";
import HO_DPD_IMG from "./assets/Images/dpd-ho.png";
import HO_ALLOCATION_IMG from "./assets/Images/allocation.svg";
import HO_COLLECTION_IMG from "./assets/Images/collection.svg";
import HO_DPD_RESPONSE_IMG from "./assets/Images/dpdResponse.svg";
import HO_RESPONSE_IMG from "./assets/Images/response.svg";
import HO_FIELD_IMG from "./assets/Images/field-officer.png";
import HO_REPORT_IMG from "./assets/Images/report-ho.png";
import HO_CAMPAIGN_IMG from "./assets/Images/campaign.png";
import FIELD_DASHBOARD_IMG from "./assets/Images/dahboard-field.png";
import FIELD_PENDING_IMG from "./assets/Images/pending-field.png";
import FIELD_TODAY_IMG from "./assets/Images/today-field.png";
import HO_REPORTS_IMG from "./assets/Images/reports-page.png";
import ANALYSIS_1_IMG from "./assets/Images/analysis_1.png";
import SMART_COLLECT_AI_IMG from "./assets/Images/hugeicons_ai-chat-02.svg";
import CAMPAIGN_SUMMARY_IMG from "./assets/Images/campaign-summary.svg";
import ADMIN_PROMISE_ACTION_IMG from "./assets/Images/promise-action.svg";
import ADMIN_BANK_TRACKING_IMG from "./assets/Images/banking-tracking.svg";
import ADMIN_STATE_WISE_IMG from "./assets/Images/state-wise.svg";
import DPD_ICON_IMG from "./assets/Images/icon-park-twotone_market-analysis.svg";
import PORTFOLIO_IMG from "./assets/Images/Vector (11).svg";
import DND_IMG from "./assets/Images/dnd.svg";
import ADMIN_CLIENT_TABLE_IMG from "./assets/Images/bank.svg";
import ADMIN_COMPONENT_IMG from "./assets/Images/admin-add.svg";
import ADMIN_ADD_BANK_IMG from "./assets/Images/add-bank.svg";
import ADMIN_VARIABLE_BANK_IMG from "./assets/Images/variable-config.svg";
import UPLOAD_IMG from "./assets/Images/admin-upload.svg";
import SMART_COLLECT_LANDING_IMG from "./assets/Images/smart-collect-landing.png";
import BANK_ASSIST_IMG from "./assets/Images/bank-assist.svg";
import MARKETING_IMG from "./assets/Images/marketing.svg";
import dayjs from "dayjs";
import ExcelJS from "exceljs";
import { saveAs } from "file-saver";
import { message } from "antd";
import PropTypes from "prop-types";

// Local Filter Key
export const SUMMARY_FILTERS = {
  summaryFilter: 'summaryFilter',
  summaryDpdCategory: "summaryDpdCategory",
  summaryEngagementStartDate: "summaryEngagementStartDate",
  selectedCampaignId: "selectedCampaignId",
  summaryBranchIds: "summaryBranchIds",
  summaryCampaignIds: "summaryCampaignIds"
};

// Local Storage Keys
export const LOCAL_KEYS = {
  HO_DASHBOARD_FILTER: "hoDashboardFilterBody",
  HO_CHANNEL_FILTER: "hoChannelsFilterBody",
  HO_PAY_FILTER: "hoPayFilter",
  SUMMARY_FILTER: 'summaryFilterBody',
  SUMMARY_PAY_FILTER: "summaryPayFilter",
}
// Cache names
export const ALL_CACHE_NAMES = {
  // HO Caches
  DASHBOARD_CACHE: "ho-dashboard-cache",
  ACTIVITY_CACHE: "activity-cache",
  ANALYSIS_CACHE: "analysis-cache",
  REPORTS_CACHE: "reports-cache",
  CAMPAIGN_MANAGEMENT_CACHE: "campaign-management-cache",
  H_FIELD_CACHE: "ho-field-cache",
  HO_CUSTOMER_PROFILE_TABLE_CACHE: "ho-customer-profile-table-cache",
  DND_CACHE: "dnd-cache",
  REALLOCATE_CACHE: "reallocate-cache",

  // Field Officer Caches
  FIELD_DASHBOARD_CACHE: "field-dashboard-cache",
  FIELD_TODAY_ALLOCATION_CACHE: "field-today-allocation-cache",
  FIELD_PENDING_ALLOCATION_CACHE: "field-pending-allocation-cache",

  // Admin Caches
  ADMIN_CLIENT_TABLE_CACHE: "admin-client-table-cache",

  // Campaign 
  CAMPAIGN_SUMMARY_DASHBOARD: 'campaign_summary_dashboard'
};

// Date formatter
export function handleDateFormatter(date) {
  // Check if the date is already in DD-MM-YYYY format
  const isFormatted = dayjs(date, "DD-MM-YYYY", true).isValid();

  if (isFormatted) {
    return date;
  } else {
    // Otherwise, format it to DD-MM-YYYY
    return dayjs(date).format("DD-MM-YYYY");
  }
}

// Rupee formatter
export function formatAmount(amount) {
  return new Intl.NumberFormat("en-IN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
}

//Number formatter
export function formatDigits(number) {
  return new Intl.NumberFormat("en-IN").format(number);
}

// Format the text
export const formattedTextToCapitalized = (value) => {
  return value?.toLowerCase().replace(/\b\w/g, (char) => char?.toUpperCase());
};

// Format date time
export const formatTime = (dateTime) => {
  return dayjs(dateTime)
    .format("h:mma")
    .replace("am", "a.m")
    .replace("pm", "p.m");
};

export const getResponsiveFontSize = (amount) => {
  const amountStr = String(amount).replace(/,/g, "");
  const baseFontSize = 15; // ideal size for small amounts
  const length = amountStr.length;

  // Scale down proportionally if length exceeds base
  if (length <= 6) return baseFontSize;

  const scaledFontSize = baseFontSize - (length - 6) * 2;
  return Math.max(scaledFontSize, 14); // Prevent too small fonts
};

// Handle Table downloading
export const handleDownloadTable = async ({
  excelName,
  worksheetName,
  tableData,
  fromDate = null,
  toDate = null
}) => {
  // If both fromDate & toDate are present, format and use them
  const reportDate =
    fromDate && toDate
      ? `${dayjs(fromDate).format("DD-MM-YYYY")}_to_${dayjs(toDate).format("DD-MM-YYYY")}`
      : dayjs().format("DD-MM-YYYY");
  const fileName = `${excelName}-${reportDate}.xlsx`;
  try {
    // 🟢 Create Workbook & Worksheet
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet(`${worksheetName}`);

    // 🟢 Add Table Headers
    const headers = Object.keys(tableData[0]); // Get headers from data object keys
    const headerRow = worksheet.addRow(headers);

    // 🟢 Style Header Row
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "3B6E9F" }, // Background Color (Dark Blue)
      };
      cell.font = { bold: true, color: { argb: "FFFFFF" } }; // White Text
      cell.alignment = { horizontal: "center", vertical: "middle" };
    });

    // 🟢 Add Table Data
    tableData.forEach((row) => {
      worksheet.addRow(Object.values(row));
    });

    // 🟢 Auto-fit Column Widths
    worksheet.columns.forEach((column) => {
      column.width = 20;
    });

    // 🟢 Generate & Save Excel File
    const buffer = await workbook.xlsx.writeBuffer();
    const fileData = new Blob([buffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });

    saveAs(fileData, fileName);
    message.success("Data downloaded successfully!");
  }
  catch (error) {
    console.error("Download failed:", error);
    message.error("Download failed!");
  }
};

export const formatDate = (dateString) => {
  const [year, month, day] = dateString.split("-");
  return `${day}-${month}-${year}`;
};

export const SMART_COLLECT_MENU_IDS = {
  // HO 
  DASHBOARD: "dashboard",
  ALLOCATION_ANALYSIS: "allocation-analysis",
  COLLECTION_ANALYSIS: "collection-analysis",
  RESPONSE_ANALYSIS: "response-analysis",
  DPD_ANALYSIS: "dpd-analysis",
  PORTFOLIO: "portfolio",
  FIELD_OFFICER: "field-officer",
  PERIODIC: "periodic",
  NORMAL: "normal",
  REPORTS: "reports",
  DND: "dnd",
  DPD_WISE_RESPONSE: "dpd-wise-response",
  SMART_COLLECT_AI: "smart-collect-ai",
  ACTIVITY: "activity",
  ACTIVITY_LOGS: "activity-logs",
  SCRUB: "scrub",
  REALLOCATE: "reallocate",
  REALLOCATE_ACTION_LIST: "reallocate-action-list",
  CAMPAIGN_SUMMARY: "campaign-summary",
  CAMPAIGN_MANAGEMENT: "campaign-management",
  CAMPAIGN_MANAGEMENT_AI: "campaign-management-ai",
  CALLING: "calling",
  MARKETING: "marketing",
  BANK_ASSIST: "bank-assist",

  // Admin
  ADD_BANK: "add-bank",
  COMPONENT_CONFIGURATION: "component-configuration",
  VARIABLE_CONFIGURATION: "variable-configuration",
  CLIENT_TABLE: "client-table",
  CLIENT_DETAILS: "client-details",
  UPLOAD_FILE: "upload-file",
  PROMISE_ACTION: "promise-action",
  BANK_TRACKING: "bank-Tracking",
  STATE_WISE_REPORT: "state-wise-report",
  ADMIN: "admin",

  // Field Officer
  TODAY_ALLOCATION: "today-allocation",
  PENDING_ALLOCATION: "pending-allocation",
  ENGAGEMENT_SUMMARY: "engagement-summary",

  // Agent Calling
  AGENT_CALLING_DASHBOARD: "agent-calling",
  AGENT_CALLING_SUMMARY: "summary",
  AGENT_CALLING_REPORTS: "reports",


  // Other Common IDs
  EMI_COLLECTION: "emi-collection",
  READY_TO_PAY: "ready-to-pay",
  REFUSED_TO_PAY: "refused-to-pay",
  ALREADY_PAY: "already-pay",
  WRONG_NUMBER: "wrong-number",
  BMI_ALLOCATION: "bmi-allocation",
  NON_CONTACTABLE: "non-contactable",
  TOTAL_CUSTOMERS: "total-customer",
  TOTAL_AMOUNT_PROMISED: "total-amount-promised",
  TOTAL_CUSTOMERS_CONNECTED: "total-customers-connected",
  TOTAL_AMOUNT_COLLECTED: "total-amount-collected",
  WHATSAPP: "whatsapp",
  AI_CALLS: "ai-calls",
  BLASTER: "blaster",
  DIALERS: "dialers",
  SMS: "sms",
  IVR_CALLS: "ivr-calls",
  EMAIL: "email",
};

// Smart collect menu items
export const SMART_COLLECT_MENU_DATA = {
  HO: {
    menuItems: [
      {
        key: SMART_COLLECT_MENU_IDS.DASHBOARD,
        icon: HO_DASHBOARD_IMG,
        label: "Dashboard",
        link: `/ho/${SMART_COLLECT_MENU_IDS.DASHBOARD}`,
        screenKey: "Dashboard",
      },
      {
        key: "Analysis",
        icon: ANALYSIS_1_IMG,
        label: "Analysis",
        link: "#",
        screenKey: "Analysis",
        children: [
          {
            key: SMART_COLLECT_MENU_IDS.ALLOCATION_ANALYSIS,
            icon: HO_ALLOCATION_IMG,
            label: "Allocation Analysis",
            link: `/ho/${SMART_COLLECT_MENU_IDS.ALLOCATION_ANALYSIS}`,
          },
          {
            key: SMART_COLLECT_MENU_IDS.COLLECTION_ANALYSIS,
            icon: HO_COLLECTION_IMG,
            label: "Collection Analysis",
            link: `/ho/${SMART_COLLECT_MENU_IDS.COLLECTION_ANALYSIS}`,
          },
          {
            key: SMART_COLLECT_MENU_IDS.RESPONSE_ANALYSIS,
            icon: HO_RESPONSE_IMG,
            label: "Response Analysis",
            link: `/ho/${SMART_COLLECT_MENU_IDS.RESPONSE_ANALYSIS}`,
          },
          {
            key: SMART_COLLECT_MENU_IDS.DPD_WISE_RESPONSE,
            icon: HO_DPD_RESPONSE_IMG,
            label: "Dpd Wise Response",
            link: `/ho/${SMART_COLLECT_MENU_IDS.DPD_WISE_RESPONSE}`,
          },
        ],
      },
      {
        key: "Insight",
        icon: HO_DPD_IMG,
        label: "Insight",
        link: "#",
        screenKey: "Insight",
        children: [
          {
            key: SMART_COLLECT_MENU_IDS.DPD_ANALYSIS,
            icon: DPD_ICON_IMG,
            label: "DPD Analysis",
            link: `/ho/${SMART_COLLECT_MENU_IDS.DPD_ANALYSIS}`,
          },
          {
            key: SMART_COLLECT_MENU_IDS.SCRUB,
            icon: HO_REPORT_IMG,
            label: "Scrub Analysis",
            link: `/ho/${SMART_COLLECT_MENU_IDS.SCRUB}`,
          },
          {
            key: SMART_COLLECT_MENU_IDS.PORTFOLIO,
            icon: PORTFOLIO_IMG,
            label: "Portfolio",
            link: `/ho/${SMART_COLLECT_MENU_IDS.PORTFOLIO}`,
          },
        ],
      },
      {
        key: "Activity",
        icon: HO_DPD_IMG, //TODO: Change icon
        label: "Activity",
        link: "#",
        screenKey: "Activity",
        children: [
          {
            key: SMART_COLLECT_MENU_IDS.ACTIVITY,
            icon: HO_DPD_IMG, //TODO: Change icon
            label: "Activity",
            link: `/ho/${SMART_COLLECT_MENU_IDS.ACTIVITY}`,
          },
          {
            key: SMART_COLLECT_MENU_IDS.ACTIVITY_LOGS,
            icon: DPD_ICON_IMG, // TODO: change icon
            label: "Logs",
            link: `/ho/${SMART_COLLECT_MENU_IDS.ACTIVITY_LOGS}`,
          },
        ],
      },
      {
        key: SMART_COLLECT_MENU_IDS.FIELD_OFFICER,
        icon: HO_FIELD_IMG,
        label: "Field Officer",
        link: `/ho/${SMART_COLLECT_MENU_IDS.FIELD_OFFICER}`,
        screenKey: "Field Officer",
      },
      {
        key: "Campaign Management",
        icon: HO_CAMPAIGN_IMG,
        label: "Campaign",
        link: "#",
        screenKey: "Campaign Management",
        children: [
          {
            key: SMART_COLLECT_MENU_IDS.CAMPAIGN_MANAGEMENT,
            icon: HO_CAMPAIGN_IMG,
            label: "Campaign Management",
            link: `/ho/${SMART_COLLECT_MENU_IDS.CAMPAIGN_MANAGEMENT}`,
          },
          {
            key: SMART_COLLECT_MENU_IDS.CAMPAIGN_MANAGEMENT_AI,
            icon: SMART_COLLECT_AI_IMG,
            label: "Campaign AI",
            link: `/ho/${SMART_COLLECT_MENU_IDS.CAMPAIGN_MANAGEMENT_AI}`,
          },
          {
            key: SMART_COLLECT_MENU_IDS.CAMPAIGN_SUMMARY,
            icon: CAMPAIGN_SUMMARY_IMG,
            label: "Campaign Summary",
            link: `/ho/${SMART_COLLECT_MENU_IDS.CAMPAIGN_SUMMARY}`,
          },
        ],
      },
      {
        key: SMART_COLLECT_MENU_IDS.REPORTS,
        icon: HO_REPORT_IMG,
        label: "Reports",
        link: `/ho/${SMART_COLLECT_MENU_IDS.REPORTS}`,
        screenKey: "Report",
      },
      {
        key: "Reallocate",
        icon: HO_DPD_IMG,
        label: "Reallocate",
        link: "#",
        screenKey: "Reallocate",
        children: [
          {
            key: SMART_COLLECT_MENU_IDS.REALLOCATE,
            icon: HO_REPORT_IMG,
            label: "Reallocate",
            link: `/ho/${SMART_COLLECT_MENU_IDS.REALLOCATE}`,
            screenKey: "Reallocate",
          },
          {
            key: SMART_COLLECT_MENU_IDS.REALLOCATE_ACTION_LIST,
            icon: HO_REPORT_IMG,
            label: "Reallocate Action List",
            link: `/ho/${SMART_COLLECT_MENU_IDS.REALLOCATE_ACTION_LIST}`,
            screenKey: "Reallocation-action-list",
          },
        ],
      },
      {
        key: SMART_COLLECT_MENU_IDS.BANK_ASSIST,
        icon: BANK_ASSIST_IMG,
        label: "Bank Assist AI",
      },
      {
        key: SMART_COLLECT_MENU_IDS.MARKETING,
        icon: MARKETING_IMG,
        label: "Marketing AI",
      },
      {
        key: SMART_COLLECT_MENU_IDS.STATE_WISE_REPORT,
        icon: ADMIN_STATE_WISE_IMG,
        label: "State Wise Report",
        link: `/ho/${SMART_COLLECT_MENU_IDS.STATE_WISE_REPORT}`,
      },
      {
        key: SMART_COLLECT_MENU_IDS.UPLOAD_FILE,
        icon: UPLOAD_IMG,
        label: "Upload File",
        link: `/ho/${SMART_COLLECT_MENU_IDS.UPLOAD_FILE}`,
        screenKey: "File Upload"
      },
      {
        key: SMART_COLLECT_MENU_IDS.DND,
        icon: DND_IMG,
        label: "DND",
        link: `/ho/${SMART_COLLECT_MENU_IDS.DND}`,
        screenKey: "DND",
      },
      {
        key: SMART_COLLECT_MENU_IDS.SMART_COLLECT_AI,
        icon: SMART_COLLECT_AI_IMG,
        label: "Smart Collect AI",
        link: `/ho/${SMART_COLLECT_MENU_IDS.SMART_COLLECT_AI}`,
        screenKey: "AI",
      },
    ],
  },
  FIELD_OFFICER: {
    menuItems: [
      {
        key: SMART_COLLECT_MENU_IDS.DASHBOARD,
        icon: FIELD_DASHBOARD_IMG,
        label: "Dashboard",
        link: `/field/${SMART_COLLECT_MENU_IDS.DASHBOARD}`,
      },
      {
        key: SMART_COLLECT_MENU_IDS.TODAY_ALLOCATION,
        icon: FIELD_TODAY_IMG,
        label: "Today allocation",
        link: `/field/${SMART_COLLECT_MENU_IDS.TODAY_ALLOCATION}`,
      },
      {
        key: SMART_COLLECT_MENU_IDS.PENDING_ALLOCATION,
        icon: FIELD_PENDING_IMG,
        label: "Pending allocation",
        link: `/field/${SMART_COLLECT_MENU_IDS.PENDING_ALLOCATION}`,
      },
      {
        key: SMART_COLLECT_MENU_IDS.ENGAGEMENT_SUMMARY,
        icon: FIELD_PENDING_IMG,
        label: "Engagement summary",
        link: `/field/${SMART_COLLECT_MENU_IDS.ENGAGEMENT_SUMMARY}`,
      },
    ],
  },
  ADMIN: {
    menuItems: [
      {
        key: SMART_COLLECT_MENU_IDS.ADMIN,
        icon: ADMIN_CLIENT_TABLE_IMG,
        label: "Client Table",
        link: `/admin`,
      },
      {
        key: SMART_COLLECT_MENU_IDS.ADD_BANK,
        icon: ADMIN_ADD_BANK_IMG,
        label: "Add Bank",
        link: `/admin/${SMART_COLLECT_MENU_IDS.ADD_BANK}`,
      },
      {
        key: SMART_COLLECT_MENU_IDS.COMPONENT_CONFIGURATION,
        icon: ADMIN_COMPONENT_IMG,
        label: "Component Configuration",
        link: `/admin/${SMART_COLLECT_MENU_IDS.COMPONENT_CONFIGURATION}`,
      },
      {
        key: SMART_COLLECT_MENU_IDS.VARIABLE_CONFIGURATION,
        icon: ADMIN_VARIABLE_BANK_IMG,
        label: "Variable Configuration",
        link: `/admin/${SMART_COLLECT_MENU_IDS.VARIABLE_CONFIGURATION}`,
      },
      {
        key: SMART_COLLECT_MENU_IDS.PROMISE_ACTION,
        icon: ADMIN_PROMISE_ACTION_IMG,
        label: "Promise Action",
        link: `/admin/${SMART_COLLECT_MENU_IDS.PROMISE_ACTION}`,
      },
      {
        key: SMART_COLLECT_MENU_IDS.BANK_TRACKING,
        icon: ADMIN_BANK_TRACKING_IMG,
        label: "Bank Tracking",
        link: `/admin/${SMART_COLLECT_MENU_IDS.BANK_TRACKING}`,
      },
      {
        key: SMART_COLLECT_MENU_IDS.UPLOAD_FILE,
        icon: UPLOAD_IMG,
        label: "Upload File",
        link: `/admin/${SMART_COLLECT_MENU_IDS.UPLOAD_FILE}`,
      },
    ],
  },
  AGENT_CALLING: {
    menuItems: [
      {
        key: SMART_COLLECT_MENU_IDS.AGENT_CALLING_DASHBOARD,
        icon: DASHBOARD__IMG,
        label: "Dashboard",
        link: `/${SMART_COLLECT_MENU_IDS.AGENT_CALLING_DASHBOARD}`,
      },
      {
        key: SMART_COLLECT_MENU_IDS.AGENT_CALLING_REPORTS,
        icon: DASHBOARD__IMG,
        label: "reports",
        link: `/agent-calling/${SMART_COLLECT_MENU_IDS.AGENT_CALLING_REPORTS}`,
      },
    ],
  },
};

// change message img
export const APP_ENGAGEMENTS_DATA = [
  {
    name: "Whatsapp Message",
    imgSrc: WHATSAPP_IMG,
  },
  {
    name: "AI Calling",
    imgSrc: AI_CALLING_IMG,
  },
  {
    name: "Blaster Calling",
    imgSrc: BLASTER_IMG,
  },
  {
    name: "IVR Calling",
    imgSrc: IVR_IMG,
  },
  {
    name: "Message",
    imgSrc: MESSAGE_IMG,
  },
];

// Status
export const STATUS_IDS = {
  REJECTED: "rejected",
  SUCCESS: "success",
};

// Customer Channels data
export const CUSTOMER_CHANNEL_DATA = [
  {
    name: "Whatsapp Message",
    imgSrc: WHATSAPP_IMG,
    id: "whatsapp",
  },
  {
    name: "AI Calling",
    imgSrc: AI_CALLING_IMG,
    id: "voicebot",
  },
  {
    name: "Blaster Calling",
    imgSrc: BLASTER_IMG,
    id: "blaster",
  },
  {
    name: "IVR Calling",
    imgSrc: IVR_IMG,
    id: "ivr",
  },
  {
    name: "Message",
    imgSrc: MESSAGE_IMG,
    id: "sms",
  },
];

// Recent data
export const RECENT_ENGAGEMENT_APPS_DATA = [
  {
    name: "Whatsapp Message",
    imgSrc: WHATSAPP_IMG,
    id: "whatsapp",
  },
  {
    name: "AI Calling",
    imgSrc: AI_CALLING_IMG,
    id: "voicebot",
  },
  {
    name: "Blaster Calling",
    imgSrc: BLASTER_IMG,
    id: "blaster",
  },
  {
    name: "IVR Calling",
    imgSrc: IVR_IMG,
    id: "ivr",
  },
  {
    name: "Message",
    imgSrc: MESSAGE_IMG,
    id: "sms",
  },
];

// Refused pay options
export const REFUSED_TO_PAY_OPTIONS_DATA = [
  {
    value: "holiday",
    label: "Customer is on holiday",
  },
  {
    value: "death",
    label: "Death in customer house",
  },
  {
    value: "custom",
    label: "Custom",
  },
];

export const CUSTOMER_DATA = {
  status: "Switched Off",
  name: "Sunita Datta Gaikwad",
  customerId: "234000033",
  disbursementId: "042-090909090",
  branchName: "SSK Gandhi Nagar",
  loanType: "Pavanraje Renewal 18k",
  overdueAmount: "23,000",
  promiseAmount: "23,000",
  promiseDate: "09-01-2025",
  feedbackDate: "02-01-2025",
  dpdAmount: 192,
  attempts: 24,
};

// Ho landing data
export const HO_LANDING_PAGE_DATA = [
  {
    title: "Dashboard",
    link: "/ho/dashboard",
    image: DASHBOARD_IMG,
    key: 'Dashboard',
  },
  {
    title: "Allocation Analysis",
    link: "/ho/allocation-analysis",
    image: TODAY_IMG,
    key: "Analysis",
  },
  {
    title: "Collection Analysis",
    link: "/ho/collection-analysis",
    image: YESTERDAY_IMG,
    key: "Analysis",
  },
  {
    title: "Campaign Management",
    link: "/ho/campaign-management",
    image: ACTIVE_IMG,
    key: "Campaign Management",
  },
  {
    title: "Field Officer",
    link: "/ho/field-officer",
    image: FIELD_IMG,
    key: "Field Officer",
  },
  {
    title: "Reports",
    link: "/ho/reports",
    image: HO_REPORTS_IMG,
    key: "Report",
  },
  {
    title: "Upload File",
    link: "/ho/upload-file",
    image: LANDING_UPLOAD_IMG,
    key: "Upload File",
  },
  {
    title: "Smart Collect AI",
    link: "/ho/smart-collect-ai",
    image: SMART_COLLECT_LANDING_IMG,
    key: "AI",
  },
];

// Fields landing data
export const FIELD_OFFICER_LANDING_PAGE_DATA = [
  {
    title: "Today Allocation",
    link: "/field/today-allocation",
    image: TODAY_IMG,
  },
  {
    title: "Pending Allocation",
    link: "/field/pending-allocation",
    image: YESTERDAY_IMG,
  },
  {
    title: "Dashboard",
    link: "/field/dashboard",
    image: DASHBOARD_IMG,
  },
  {
    title: "Engagement Summary",
    link: "/field/engagement-summary",
    image: ENGAGEMENT_SUMMARY_IMG,
  },
];

export const FIELD_DEFAULTER_DATA = [
  {
    region: "pune",
    hub: "hub1",
    branch: "branch 1",
    date: "12-05-2024",
    lanAmount: 24,
    amount: "₹12,495",
  },
  {
    region: "pune",
    hub: "hub1",
    branch: "branch 1",
    date: "12-05-2024",
    lanAmount: 24,
    amount: "₹12,495",
  },
  {
    region: "Mumbai",
    hub: "hub1",
    branch: "branch 1",
    date: "12-05-2024",
    lanAmount: 24,
    amount: "₹12,495",
  },
  {
    region: "Mumbai",
    hub: "hub1",
    branch: "branch 1",
    date: "12-05-2024",
    lanAmount: 24,
    amount: "₹12,495",
  },
  {
    region: "Datia",
    hub: "hub1",
    branch: "branch 1",
    date: "12-05-2024",
    lanAmount: 24,
    amount: "₹12,495",
  },
];

// Periodic Template scripting data
export const PERIODIC_TEMPLATE_SCRIPTING_DATA = [
  "Script mode is a defined set of steps that need to be followed in order for the computer to understand and carry out the instructions. Interactive mode, on the other hand, is where the user can type in commands and see the results straight away.",
  "Script mode is a defined set of steps that need to be followed in order for the computer to understand and carry out the instructions. Interactive mode, on the other hand, is where the user can type in commands and see the results straight away.",
  "Script mode is a defined set of steps that need to be followed in order for the computer to understand and carry out the instructions. Interactive mode, on the other hand, is where the user can type in commands and see the results straight away.",
  "Script mode is a defined set of steps that need to be followed in order for the computer to understand and carry out the instructions. Interactive mode, on the other hand, is where the user can type in commands and see the results straight away.",
];

// Campaign Dropdown
export const CAMPAIGN_DROPDOWN_DATA = [
  { value: "Dpd", label: "DPD" },
  { value: "Age", label: "Age" },
  { value: "State", label: "State" },
  { value: "DisbursementDate", label: "Disbursement Date" },
  { value: "DisbursementAmt", label: "Disbursement Amount" },
  { value: "LoanType", label: "Loan Type" },
  { value: "ClosingDate", label: "Closing Date" },
  { value: "InstallmentStartDate", label: "Installment Start Date" },
  { value: "RateOfInterest", label: "Rate of Interest" },
  { value: "RepaymentTenure", label: "Repayment Tenure" },
  { value: "EMIAmount", label: "EMI Amount" },
  { value: "PaymentFrequency", label: "Payment Frequency" },
  { value: "BMID", label: "BM ID" },
  { value: "CollectionOfficerID", label: "Collection Officer ID" },
  { value: "CollectionOfficerName", label: "Collection Officer Name" },
  { value: "DND", label: "DND" },
  { value: "groupid", label: "Group ID" },
  { value: "groupname", label: "Group Name" },
  { value: "Gender", label: "Gender" },
  { value: "DateOfBirth", label: "Date of Birth" },
  { value: "IsDeath", label: "Is Death" },
  { value: "DeathDate", label: "Death Date" },
  { value: "Pincode", label: "Pincode" },
  { value: "BranchName", label: "Branch Name" },
  { value: "LoanMstID", label: "Loan Master ID" },
  { value: "CustomerMstID", label: "Customer Master ID" },
  { value: "DisbursementID", label: "Disbursement ID" },
  { value: "BranchMstID", label: "Branch Master ID" },
  { value: "LastModifiedDate", label: "Last Modified Date" },
  { value: "IsActive", label: "Is Active" },
  { value: "BankMstID", label: "Bank Master ID" },
  { value: "CustomerID", label: "Customer ID" },
  { value: "CustomerCode", label: "Customer Code" },
  { value: "CustomerName", label: "Customer Name" },
  { value: "MobileNumber", label: "Mobile Number" },
  { value: "CreatedDate", label: "Created Date" },
  { value: "UpdatedDate", label: "Updated Date" },
  { value: "EmailID", label: "Email ID" },
  { value: "Latitude", label: "Latitude" },
  { value: "Longitude", label: "Longitude" },
  { value: "PanNo", label: "PAN Number" },
  { value: "CustomerAddress", label: "Customer Address" },
  { value: "BankName", label: "Bank Name" },
  { value: "ScheduleDate", label: "Schedule Date" },
  { value: "PrincipleAmt", label: "Principle Amount" },
  { value: "InterestAmt", label: "Interest Amount" },
  { value: "Demand", label: "Demand" },
];

export const CAMPAIGN_PARAMETERS_DROPDOWN_DATA = [
  { value: "DateOfBirth", label: "Date Of Birth" },
  { value: "Gender", label: "Gender" },
  { value: "DisbursementAmt", label: "Disbursement Amount" },
  { value: "LoanType", label: "Loan Type" },
  { value: "DPD", label: "DPD" },
  { value: "LoanClassification", label: "Loan Classification" },
  { value: "CurrentBalance", label: "Current Balance" },
  { value: "OverDueAmt", label: "Overdue Amount" },
  { value: "EMIAmount", label: "EMI Amount" },
  { value: "Branch", label: "Branch" },
  { value: "Region", label: "Region" },
  { value: "State", label: "State" },
];

// Common Prop Types.
export const CAMPAIGN_DATA_PROP_TYPES = PropTypes.shape({
  categories: PropTypes.shape({
    name: PropTypes.string,
    filters: PropTypes.array,
  }),
  templates: PropTypes.array,
  communicationFlows: PropTypes.shape({
    name: PropTypes.string,
    commFlow: PropTypes.array
  }),
  campaign: PropTypes.shape({
    priority: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    name: PropTypes.string,
  }),
})
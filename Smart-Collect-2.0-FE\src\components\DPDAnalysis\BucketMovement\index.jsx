import { Flex, Typography } from 'antd'
import React, { useEffect, useState } from 'react'
import AllCards from './AllCards'
import { BucketTable } from './BucketTable';
import Style from "./_style.module.scss";
import {AXIOS} from "../../../apis/ho-Instance";
import ApplicationLoader from '../../ApplicationLoader';

const {Title}  = Typography;

export function BucketMovement() {
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState({});

  // Fetch all bucket data
  const handleFetchBucketData = async()=>{
    setIsLoading(true);
    try {
      const res = await AXIOS.post('getbucketmovement/');
      if (res.status === 200){
        setData(res.data.data);
      }
    } catch (error) {
      console.log("Error in bucket", error?.message);
    }finally{
      setIsLoading(false)
    }
  }

  // Invock the get method for bucket movement
  useEffect(()=>{
    handleFetchBucketData();
  },[]);

  return (
   <Flex vertical gap={20}>
      <AllCards cardData={Array.isArray(data.cards_data) ? data?.cards_data[0] : {}}/>
      <Flex className={Style.box_container} vertical gap={30}>
       {isLoading
       ?<ApplicationLoader/> 
       :<BucketTable data={data}/>}
      </Flex>
   </Flex>
  )
}

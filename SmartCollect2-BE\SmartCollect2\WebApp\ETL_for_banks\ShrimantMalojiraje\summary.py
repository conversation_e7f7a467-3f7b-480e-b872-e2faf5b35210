import pandas as pd
import numpy as np
from ..utils import clean_mobile_number
from datetime import datetime
from ..queries import insert_data_in_raw_table
import threading
from ..sp import execute_post_processing_procedures

def run_post_processing_in_background(bank_id):
    """
    Run post-processing procedures in a background thread
    """
    try:
        print(f"Starting background post-processing for bank_id: {bank_id}")
        execute_post_processing_procedures(bank_id)
        print(f"Background post-processing initiated for bank_id: {bank_id}")
    except Exception as e:
        print(f"Error starting background post-processing for bank_id {bank_id}: {str(e)}")

def safe_abs_convert(series, debug_column_name=None):
    """
    Safely convert series to numeric and apply abs(), handling string values
    """
    try:
        # Convert to string first, strip whitespace, then convert to numeric
        string_series = series.astype(str).str.strip()
        
        # Debug print for disbursementamount
        if debug_column_name:
            print(f"DEBUG {debug_column_name}: Original values sample: {list(series.head().values) if hasattr(series, 'values') else 'No values attribute'}")
            print(f"DEBUG {debug_column_name}: String values sample: {list(string_series.head().values)}")
        
        # Replace common non-numeric indicators
        string_series = string_series.replace({
            'nan': '0',
            'NaN': '0',
            'None': '0',
            'null': '0',
            'NULL': '0',
            '': '0'
        })
        
        numeric_series = pd.to_numeric(string_series, errors='coerce')
        
        if debug_column_name:
            print(f"DEBUG {debug_column_name}: Numeric values sample: {list(numeric_series.head().values)}")
            print(f"DEBUG {debug_column_name}: NaN count: {numeric_series.isna().sum()}")
        
        # Apply abs() and fill NaN with 0
        result = abs(numeric_series).fillna(0)
        
        if debug_column_name:
            print(f"DEBUG {debug_column_name}: Final values sample: {list(result.head().values)}")
            print(f"DEBUG {debug_column_name}: Non-zero count: {(result != 0).sum()}")
        
        return result
    except Exception as e:
        print(f"Error in safe_abs_convert for {debug_column_name}: {e}")
        # If conversion fails, return series of zeros
        return pd.Series([0] * len(series))

def safe_string_process(series):
    """
    Safely convert series to string and strip whitespace
    """
    try:
        return series.astype(str).str.strip()
    except:
        return series.fillna('').astype(str)

def safe_date_convert(series):
    """
    Safely convert series to datetime, handling various date formats and NaT values
    """
    try:
        # First convert to string and clean
        date_series = series.astype(str).str.strip()
        
        # Replace common invalid date indicators with None
        date_series = date_series.replace({
            'NaT': None,
            'nat': None,
            'NAN': None,
            'nan': None,
            '': None,
            'None': None,
            'null': None,
            'NULL': None
        })
        
        # Convert to datetime with multiple format attempts
        result = pd.to_datetime(date_series, errors='coerce', dayfirst=True)
        
        # Convert to date objects for database compatibility
        result = result.dt.date
        
        return result
    except Exception as e:
        print(f"Error in date conversion: {e}")
        # Return series of None values if conversion fails
        return pd.Series([None] * len(series))
    """
    Safely convert series to datetime, handling various date formats and NaT values
    """
    try:
        # First convert to string and clean
        date_series = series.astype(str).str.strip()
        
        # Replace common invalid date indicators with None
        date_series = date_series.replace({
            'NaT': None,
            'nat': None,
            'NAN': None,
            'nan': None,
            '': None,
            'None': None,
            'null': None,
            'NULL': None
        })
        
        # Convert to datetime with multiple format attempts
        result = pd.to_datetime(date_series, errors='coerce', dayfirst=True)
        
        # Convert to date objects for database compatibility
        result = result.dt.date
        
        return result
    except Exception as e:
        print(f"Error in date conversion: {e}")
        # Return series of None values if conversion fails
        return pd.Series([None] * len(series))

def process_shrimantmalojiraje_raw_data(df1):
    db_operation_success = False
    db_error_message = None
    frequency_map = {
        'DAILY': 1,
        'WEEKLY': 7,
        'MONTHLY': 30,
        "M": 30
    }
    df = pd.DataFrame()
    
    # Use safe string processing for columns that should be strings
    df1.columns = df1.columns.str.strip().str.replace(r'\s+', ' ', regex=True)
    df['customerid'] = pd.to_numeric(df1['customer_id'], errors='coerce').fillna(0).astype(int).astype(str)
    df['disbursementid'] = safe_string_process(df1['acct_cd'])
    df['branchcode'] = safe_string_process(df1['branch_cd'])
    df['branchname'] = (df1['branch_nm']
                       .astype(str).str.upper()
                       .str.strip()                    # Remove leading/trailing spaces
                       .str.replace(r'\s+', '', regex=True)   # Remove ALL spaces
                       .str.title())
    df['loantype'] = safe_string_process(df1['type_nm'])
    df['customername'] = df1['acct_nm'].astype(str).str.upper().str.strip()
    df['mobileno'] = df1['contact2'].apply(clean_mobile_number).astype(str).str.strip()
    
    # Use safe conversion for numeric columns
    df['overdueamount'] = safe_abs_convert(df1['overdue'])
    df['totaloutstanding'] = safe_abs_convert(df1['os_balance'])
    df['emiamount'] = safe_abs_convert(df1['inst_rs'])
    df['numberofdayspastdue'] = safe_abs_convert(df1['days_past_due'])
    df['principlecollected'] = safe_abs_convert(df1['pri_outstandind'])
    df['interestcollected'] = safe_abs_convert(df1['int_outstanding'])
    
    # Debug the disbursementamount column specifically
    print(f"DEBUG: Checking sanctioned_amt column...")
    print(f"DEBUG: Column exists: {'sanctioned_amt' in df1.columns}")
    if 'sanctioned_amt' in df1.columns:
        try:
            # Handle potential duplicate columns or indexing issues
            sanctioned_col = df1['sanctioned_amt']
            if hasattr(sanctioned_col, 'iloc'):
                # If it's a DataFrame, take the first column
                sanctioned_col = sanctioned_col.iloc[:, 0] if sanctioned_col.shape[1] > 0 else sanctioned_col
            
            print(f"DEBUG: sanctioned_amt sample values: {sanctioned_col.head().values.tolist()}")
            print(f"DEBUG: sanctioned_amt data type: {sanctioned_col.dtype}")
            print(f"DEBUG: sanctioned_amt shape: {sanctioned_col.shape}")
            print(f"DEBUG: sanctioned_amt unique values: {sanctioned_col.unique()[:10].tolist()}")
        except Exception as e:
            print(f"DEBUG: Error accessing sanctioned_amt: {e}")
            print(f"DEBUG: sanctioned_amt type: {type(df1['sanctioned_amt'])}")
    else:
        print(f"DEBUG: Available columns: {df1.columns.tolist()}")
        # Look for similar column names
        sanctioned_cols = [col for col in df1.columns if 'sanction' in col.lower()]
        disbursement_cols = [col for col in df1.columns if 'disbur' in col.lower()]
        amount_cols = [col for col in df1.columns if 'amt' in col.lower()]
        print(f"DEBUG: Columns with 'sanction': {sanctioned_cols}")
        print(f"DEBUG: Columns with 'disbur': {disbursement_cols}")
        print(f"DEBUG: Columns with 'amt': {amount_cols}")
    
    
    # Safe extraction of sanctioned_amt column
    try:
        sanctioned_amt_series = df1['sanctioned_amt']
        # Handle case where column selection returns DataFrame instead of Series
        if hasattr(sanctioned_amt_series, 'iloc') and hasattr(sanctioned_amt_series, 'shape'):
            if len(sanctioned_amt_series.shape) > 1 and sanctioned_amt_series.shape[1] > 0:
                sanctioned_amt_series = sanctioned_amt_series.iloc[:, 0]
        
        df['disbursementamount'] = safe_abs_convert(sanctioned_amt_series, debug_column_name='disbursementamount')
    except Exception as e:
        print(f"ERROR: Could not process sanctioned_amt column: {e}")
        # Fallback to zeros if column processing fails
        df['disbursementamount'] = pd.Series([0] * len(df1))
    
    df['paymentfrequency'] = df1['installment_type'].astype(str).str.upper().map(frequency_map).astype('Int64')
    df['inserted_date'] = datetime.now().date()
    df['bankmstid'] = 37
    
    # FIX: Use safe date conversion instead of string processing
    df['inststartdate'] = safe_date_convert(df1['ins_start_dt'])
    
    df['guarantor'] = safe_string_process(df1['name_of_gurentor'])
    df['repaymenttenure'] = pd.to_numeric(df1['inst_no'], errors='coerce').fillna(0).astype(int)
    df['originaldisbursementid'] = safe_string_process(df1['acct_cd'])
    
    # Additional data validation before database insertion
    print(f"Data validation summary:")
    print(f"- Total records: {len(df)}")
    print(f"- Records with valid inststartdate: {df['inststartdate'].notna().sum()}")
    print(f"- Records with null inststartdate: {df['inststartdate'].isna().sum()}")
    
    # Additional validation for disbursementamount
    print(f"- Disbursement amount summary:")
    print(f"  - Non-zero disbursement amounts: {(df['disbursementamount'] != 0).sum()}")
    print(f"  - Zero disbursement amounts: {(df['disbursementamount'] == 0).sum()}")
    print(f"  - Min disbursement amount: {df['disbursementamount'].min()}")
    print(f"  - Max disbursement amount: {df['disbursementamount'].max()}")
    print(f"  - Mean disbursement amount: {df['disbursementamount'].mean()}")
    if (df['disbursementamount'] != 0).sum() > 0:
        print(f"  - Sample non-zero values: {df[df['disbursementamount'] != 0]['disbursementamount'].head().tolist()}")
    
    try:
        result = insert_data_in_raw_table(df, bank_id=37)
        db_operation_success = result['success']
        if not result['success']:
            db_error_message = result['message']
    except Exception as e:
        db_operation_success = False
        db_error_message = str(e)
        print(f"Database update failed for shrimantmalojiraje data: {e}")
    
    if db_operation_success:
        # Start post-processing in background thread
        background_thread = threading.Thread(
            target=run_post_processing_in_background,
            args=(37,),
            daemon=True
        )
        background_thread.start()
        print(f"Post-processing started in background thread for bank_id: {37}")
    else:
        print(f"Skipping post-processing due to database operation failure: {db_error_message}")
    
    return {
        "Total records processed": len(df),
        "New records inserted": result.get('inserted_count', 0) if db_operation_success else 0,
        "Duplicate records found": result.get('duplicate_count', 0) if db_operation_success else 0,
        "db_operation_success": db_operation_success,
        "db_error_message": db_error_message,
        "message": result.get('message', '') if db_operation_success else db_error_message,
    }
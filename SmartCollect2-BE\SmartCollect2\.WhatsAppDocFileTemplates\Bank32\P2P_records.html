<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>P2P Reports</title>
    <style>
        @page {
            size: A4 landscape;
            margin: 1cm;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .header {
            background: #001a45;
            color: white;
            padding: 10px;
            text-align: center;
            background-image: url('header-bg.png');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            width: 100%;
            position: fixed;
            top: 0;
        }

        .box {
            display: flex;
            gap: 10px;
            justify-content: center;
            align-items: center;
        }

        h2 {
            font-size: 13px;
        }

        .content {
            padding: 20px;
            text-align: center;
            margin-top: 120px;
        }

        .content h3 {
            font-size: 20px;
            margin-bottom: 20px;
            color: #0f2050;
        }

        .allocation-table {
            width: 100%;
            border-collapse: collapse;
            border: none;
        }

        .allocation-table th,
        .allocation-table td {
            padding: 10px;
            text-align: center;
            font-size: 14px;
            border: 1px solid #a7a7a7;
            word-wrap: break-word;
            word-break: break-word;
            white-space: normal;
            max-width: 150px;
        }

        .allocation-table th {
            background-color: #e2ebff;
            color: #0f2050;
        }

        .footer {
            background-color: #001a45;
            color: white;
            text-align: left;
            padding: 10px;
            display: flex;
            align-items: center;
            position: fixed;
            bottom: 0;
            width: 100%;
        }

        .footer img {
            width: 50px;
            height: 50px;
            margin-right: 10px;
        }

        .footer .title-img {
            width: 150px;
        }
    </style>
</head>

<body>
    <div class="header">
        <div class="box">
            <!-- <img src="image-79.svg" alt="logo" /> -->
            <h2> BSS Micro Finance - Branch: {{ BranchName }} - {{ BranchCode }} </h2>
        </div>
    </div>
    <div class="content">
        <h3>Total Promises</h3>
        <div style="padding: 0.5rem; border-radius: 5px; border: 1px solid black">
            <table class="allocation-table">
                <thead>
                    <tr>
                        <th>Customer ID</th>
                        <th>Disbursement ID</th>
                        <th>Customer Name</th>
                        <th>Amount (₹)</th>
                        <th>Collection Officer Name</th>
                        <th>Collection Officer ID</th>
                        <th>Center Name</th>
                    </tr>
                </thead>
                <tbody>
                    {% for row in ptp_records %}
                    <tr>
                        <td>{{ row.CustomerID }}</td>
                        <td>{{ row.DisbursementID }}</td>
                        <td>{{ row.CustomerName }}</td>
                        <td>{{ row.Amount }}</td>
                        <td>{{ row.CollectionOfficerName or 'NA' }}</td>
                        <td>{{ row.CollectionOfficerID or 'NA' }}</td>
                        <td>{{ row.centername or 'NA' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    <div class="footer">
        <!-- <img src="logo.svg" alt="Smart Collect Icon" />
        <img src="logo-title.svg" class="title-img" alt="footer" /> -->
    </div>
</body>

</html>
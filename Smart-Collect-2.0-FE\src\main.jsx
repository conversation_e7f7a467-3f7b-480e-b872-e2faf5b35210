import { createRoot } from "react-dom/client";
import { create<PERSON><PERSON><PERSON><PERSON><PERSON>er, Outlet, RouterProvider } from "react-router";
import { SMART_COLLECT_MENU_IDS, SMART_COLLECT_MENU_DATA } from "./constant";
import ReadyToPay from "./components/ReadyToPay";
import RefusedToPay from "./components/RefusedToPay";
import App from "./App";
import AlreadyPay from "./components/AlreadyPay";
import WrongNumber from "./components/WrongNumber";
import BmiAllocation from "./components/BmiAllocation";
import NonContactable from "./components/NonContactable";
import Login from "./components/Login";
import ForgetPassword from "./components/ForgetPassword";
import LandingScreen from "./components/LandingScreen";
import AppBlock from "./components/AppBlock";
import FieldDashboard from "./components/FieldDashboard";
import FieldReadyToPay from "./components/FieldReadyToPay";
import AllocationAnalysis from "./components/AllocationAnalysis";
import CollectionAnalysis from "./components/CollectionAnalysis";
import FieldEmiCollection from "./components/FieldEmiCollection";
import DPDAnalysis from "./components/DPDAnalysis";
import FelidOfficer from "./components/FelidOfficer";
import AdminAddBank from "./components/MarkyticsAdmin/AdminAddBank";
import AdminClientTable from "./components/MarkyticsAdmin/AdminClientTable";
import AdminClientDetails from "./components/MarkyticsAdmin/AdminClientDetails";
import AdminUploadFile from "./components/MarkyticsAdmin/AdminUploadFile";
import CampaignManagement from "./components/Compaign/CampaignManagement";
import ReportsHO from "./components/ReportsHo";
import FieldTodayAllocation from "./components/FieldTodayAllocation";
import FieldPendingAllocation from "./components/FieldPendingAllocation";
import UploadFileHo from "./components/UploadFileHo";
import DND from "./components/DND";
import UploadFile from "./components/DND/UploadFileHo";
import TotalCustomersDashboard from "./components/TotalCustomersDashboard";
import TotalAmountPromisedDashboard from "./components/TotalAmountPromisedDashboard";
import TotalCustomersConnectedDashboard from "./components/TotalCustomersConnectedDashboard";
import TotalAmountCollectedDashboard from "./components/TotalAmountCollectedDashboard";
import WhatsappDashboard from "./components/WhatsappDashboard";
import AiCallsDashboard from "./components/AiCallsDashboard";
import BlasterDashboard from "./components/BlasterDashboard";
import DialersDashboard from "./components/DialersDashboard";
import IvrCallsDashboard from "./components/IvrCallsDashboard";
import SmsDashboard from "./components/SmsDashboard";
import EmailDashboard from "./components/EmailDashboard";
import Portfolio from "./components/Portfolio";
import ResponseAnalysis from "./components/ResponseAnalysis";
import CollectHoBmDashboard from "./components/CollectHoBmDashboard";
import DPDWiseResponse from "./components/DPDWiseResponse";
import SmartCollectAI from "./components/SmartCollectAI";
import ActivityLogs from "./components/ActivityLogs";
import LogA from "./components/LogA";
import NormalCampaignView from "./components/Compaign/NormalCategoryCampaign/NormalCampaignView";
import NormalCategoryCampaign from "./components/Compaign/NormalCategoryCampaign/Campaign";
import AdminComponentConfiguration from "./components/MarkyticsAdmin/AdminComponentConfiguration";
import CampaignManagementAI from "./components/CampaignManagementAI";
import Scrub from "./components/Scrup";
import AdminVariableConfiguration from "./components/MarkyticsAdmin/AdminVariableConfiguration";
import Reallocate from "./components/Reallocate";
import ReallocateActionList from "./components/ReallocateActionList";
import CallingDashboard from "./components/CallingDashboard";
import CallingAgentsReports from "./components/CallingAgentsReports";
import CallingAgentsSummary from "./components/CallingAgentsSummary";
import PromiseAction from "./components/MarkyticsAdmin/PromiseAction";
import PrivacyPolicy from "./components/PrivacyPolicy";
import BankTracking from "./components/BankTracking";
import CampaignSummaryDashboard from "./components/CampaignSummaryDashboard";
import SummaryReadyToPay from "./components/SummaryReadyToPay";
import SummaryRefusedToPay from "./components/SummaryRefusedToPay";
import StateWiseReport from "./components/StateWiseReport";
import SummaryAlreadyPaid from "./components/SummaryAlreadyPaid";
import SummaryAiDash from "./components/SummaryAiDash";
import SummaryWhatsappDash from "./components/SummaryWhatsappDash";
import SummaryBlasterDash from "./components/SummaryBlasterDash";
import SummaryIvrDash from "./components/SummaryIvrDash";
import SummarySmsDash from "./components/SummarySmsDash";
import SummaryTotalCustomersConnected from "./components/SummaryTotalCustomersConnected";
import SummaryTotalAmountPromised from "./components/SummaryTotalAmountPromised";

const router = createBrowserRouter([
  {
    path: "/",
    element: <App />,
    children: [
      {
        path: "/",
        element: <Login />,
      },
      {
        path: "/forget-password",
        element: <ForgetPassword />,
      },
      {
        path: "/privacy-policy",
        element: <PrivacyPolicy />,
      },
      {
        path: "/ho",
        element: (
          <LandingScreen
            role={"HO"}
            menuItems={SMART_COLLECT_MENU_DATA.HO.menuItems}
          />
        ),
        children: [
          {
            path: SMART_COLLECT_MENU_IDS.DASHBOARD,
            element: <Outlet />,
            children: [
              {
                path: "",
                element: <CollectHoBmDashboard role="HO" title="Dashboard" />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.WHATSAPP,
                element: <WhatsappDashboard />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.AI_CALLS,
                element: <AiCallsDashboard />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.BLASTER,
                element: <BlasterDashboard />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.DIALERS,
                element: <DialersDashboard />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.IVR_CALLS,
                element: <IvrCallsDashboard />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.SMS,
                element: <SmsDashboard />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.EMAIL,
                element: <EmailDashboard />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.TOTAL_CUSTOMERS,
                element: <TotalCustomersDashboard />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.TOTAL_AMOUNT_PROMISED,
                element: <TotalAmountPromisedDashboard />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.TOTAL_CUSTOMERS_CONNECTED,
                element: <TotalCustomersConnectedDashboard />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.TOTAL_AMOUNT_COLLECTED,
                element: <TotalAmountCollectedDashboard />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.READY_TO_PAY,
                element: <ReadyToPay />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.REFUSED_TO_PAY,
                element: <RefusedToPay />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.ALREADY_PAY,
                element: <AlreadyPay />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.WRONG_NUMBER,
                element: <WrongNumber />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.BMI_ALLOCATION,
                element: <BmiAllocation />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.NON_CONTACTABLE,
                element: <NonContactable />,
              },
            ],
          },
          {
            path: SMART_COLLECT_MENU_IDS.ALLOCATION_ANALYSIS,
            element: <AllocationAnalysis />,
          },
          {
            path: SMART_COLLECT_MENU_IDS.DPD_WISE_RESPONSE,
            element: <DPDWiseResponse />,
          },
          {
            path: SMART_COLLECT_MENU_IDS.COLLECTION_ANALYSIS,
            element: <CollectionAnalysis />,
          },
          {
            path: SMART_COLLECT_MENU_IDS.RESPONSE_ANALYSIS,
            element: <ResponseAnalysis />,
          },
          {
            path: SMART_COLLECT_MENU_IDS.DPD_ANALYSIS,
            element: <DPDAnalysis />,
          },
          {
            path: SMART_COLLECT_MENU_IDS.SCRUB,
            element: <Scrub />,
          },
          {
            path: SMART_COLLECT_MENU_IDS.PORTFOLIO,
            element: <Portfolio />,
          },
          {
            path: SMART_COLLECT_MENU_IDS.FIELD_OFFICER,
            element: <FelidOfficer />,
          },
          {
            path: SMART_COLLECT_MENU_IDS.CAMPAIGN_MANAGEMENT,
            element: <CampaignManagement />,
            children: [
              {
                path: SMART_COLLECT_MENU_IDS.PERIODIC,
                element: <div>Periodic</div>,
              },
              {
                path: `${SMART_COLLECT_MENU_IDS.NORMAL}/:campaignName/view`,
                element: <NormalCampaignView />,
              },
              {
                path: `${SMART_COLLECT_MENU_IDS.NORMAL}/:campaignName/:status?`,
                element: <NormalCategoryCampaign />,
              },
              {
                path: `${SMART_COLLECT_MENU_IDS.CALLING}/:campaignName/view`,
                element: <NormalCampaignView />,
              },
              {
                path: `${SMART_COLLECT_MENU_IDS.CALLING}/:campaignName/:status?`,
                element: <NormalCategoryCampaign />,
              },
            ],
          },
          {
            path: SMART_COLLECT_MENU_IDS.CAMPAIGN_MANAGEMENT_AI,
            element: <CampaignManagementAI />,
          },
          {
            path: SMART_COLLECT_MENU_IDS.CAMPAIGN_SUMMARY,
            element: <Outlet />,
            children: [
              {
                path: "",
                element: (
                  <CampaignSummaryDashboard title={"Campaign Summary"} />
                ),
              },
              {
                path: SMART_COLLECT_MENU_IDS.WHATSAPP,
                element: <SummaryWhatsappDash />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.AI_CALLS,
                element: <SummaryAiDash />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.BLASTER,
                element: <SummaryBlasterDash />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.IVR_CALLS,
                element: <SummaryIvrDash />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.SMS,
                element: <SummarySmsDash />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.TOTAL_AMOUNT_PROMISED,
                element: <SummaryTotalAmountPromised />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.TOTAL_CUSTOMERS_CONNECTED,
                element: <SummaryTotalCustomersConnected />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.READY_TO_PAY,
                element: <SummaryReadyToPay />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.REFUSED_TO_PAY,
                element: <SummaryRefusedToPay />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.ALREADY_PAY,
                element: <SummaryAlreadyPaid />,
              },
            ],
          },
          {
            path: SMART_COLLECT_MENU_IDS.REPORTS,
            element: <ReportsHO />,
          },
          {
            path: SMART_COLLECT_MENU_IDS.UPLOAD_FILE,
            element: <UploadFileHo />,
          },
          {
            path: SMART_COLLECT_MENU_IDS.SMART_COLLECT_AI,
            element: <SmartCollectAI />,
          },
          {
            path: SMART_COLLECT_MENU_IDS.ACTIVITY,
            element: <ActivityLogs />,
          },
          {
            path: SMART_COLLECT_MENU_IDS.ACTIVITY_LOGS,
            element: <LogA />,
          },
          {
            path: SMART_COLLECT_MENU_IDS.REALLOCATE,
            element: <Reallocate />,
          },
          {
            path: SMART_COLLECT_MENU_IDS.REALLOCATE_ACTION_LIST,
            element: <ReallocateActionList />,
          },
          {
            path: SMART_COLLECT_MENU_IDS.STATE_WISE_REPORT,
            element: <StateWiseReport />,
          },
          {
            path: SMART_COLLECT_MENU_IDS.DND,
            element: <DND />,
            children: [
              {
                path: "upload-file",
                element: <UploadFile />,
              },
            ],
          },
        ],
      },
      {
        path: "/field",
        element: (
          <LandingScreen
            role={"Field"}
            menuItems={SMART_COLLECT_MENU_DATA.FIELD_OFFICER.menuItems}
          />
        ),
        children: [
          {
            path: SMART_COLLECT_MENU_IDS.DASHBOARD,
            element: <Outlet />,
            children: [
              {
                path: "",
                element: <FieldDashboard />,
              },
              {
                path: `${SMART_COLLECT_MENU_IDS.READY_TO_PAY}/:type`,
                element: <FieldReadyToPay />,
              },
              {
                path: `${SMART_COLLECT_MENU_IDS.EMI_COLLECTION}/:type`,
                element: <FieldEmiCollection />,
              },
            ],
          },
          {
            path: SMART_COLLECT_MENU_IDS.TODAY_ALLOCATION,
            element: <FieldTodayAllocation />,
          },
          {
            path: SMART_COLLECT_MENU_IDS.PENDING_ALLOCATION,
            element: <FieldPendingAllocation />,
          },
          {
            path: SMART_COLLECT_MENU_IDS.ENGAGEMENT_SUMMARY,
            element: <Outlet />,
            children: [
              {
                path: "",
                element: (
                  <CollectHoBmDashboard
                    role={"Field"}
                    title="Engagement Summary"
                  />
                ),
              },
              {
                path: SMART_COLLECT_MENU_IDS.WHATSAPP,
                element: <WhatsappDashboard />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.AI_CALLS,
                element: <AiCallsDashboard />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.BLASTER,
                element: <BlasterDashboard />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.DIALERS,
                element: <DialersDashboard />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.IVR_CALLS,
                element: <IvrCallsDashboard />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.SMS,
                element: <SmsDashboard />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.EMAIL,
                element: <EmailDashboard />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.TOTAL_CUSTOMERS,
                element: <TotalCustomersDashboard />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.TOTAL_AMOUNT_PROMISED,
                element: <TotalAmountPromisedDashboard />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.TOTAL_CUSTOMERS_CONNECTED,
                element: <TotalCustomersConnectedDashboard />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.TOTAL_AMOUNT_COLLECTED,
                element: <TotalAmountCollectedDashboard />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.READY_TO_PAY,
                element: <ReadyToPay />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.REFUSED_TO_PAY,
                element: <RefusedToPay />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.ALREADY_PAY,
                element: <AlreadyPay />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.WRONG_NUMBER,
                element: <WrongNumber />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.BMI_ALLOCATION,
                element: <BmiAllocation />,
              },
              {
                path: SMART_COLLECT_MENU_IDS.NON_CONTACTABLE,
                element: <NonContactable />,
              },
            ],
          },
        ],
      },
      {
        path: "/admin",
        element: (
          <AppBlock menuItems={SMART_COLLECT_MENU_DATA.ADMIN.menuItems} />
        ),
        children: [
          {
            path: "",
            element: <AdminClientTable />,
          },
          {
            path: "client-details/:bankname",
            element: <AdminClientDetails />,
          },
          {
            path: SMART_COLLECT_MENU_IDS.ADD_BANK,
            element: <AdminAddBank />,
          },
          {
            path: SMART_COLLECT_MENU_IDS.COMPONENT_CONFIGURATION,
            element: <AdminComponentConfiguration />,
          },
          {
            path: SMART_COLLECT_MENU_IDS.VARIABLE_CONFIGURATION,
            element: <AdminVariableConfiguration />,
          },
          {
            path: SMART_COLLECT_MENU_IDS.PROMISE_ACTION,
            element: <PromiseAction />,
          },
          {
            path: SMART_COLLECT_MENU_IDS.BANK_TRACKING,
            element: <BankTracking />,
          },
          {
            path: SMART_COLLECT_MENU_IDS.UPLOAD_FILE,
            element: <AdminUploadFile />,
          },
        ],
      },
      {
        path: `/${SMART_COLLECT_MENU_IDS.AGENT_CALLING_DASHBOARD}`,
        element: (
          <AppBlock
            menuItems={SMART_COLLECT_MENU_DATA.AGENT_CALLING.menuItems}
          />
        ),
        children: [
          {
            path: "",
            element: <CallingDashboard />,
          },
          {
            path: `${SMART_COLLECT_MENU_IDS.AGENT_CALLING_SUMMARY}/:summaryName`,
            element: <CallingAgentsSummary />,
          },
          {
            path: SMART_COLLECT_MENU_IDS.AGENT_CALLING_REPORTS,
            element: <CallingAgentsReports />,
          },
        ],
      },
    ],
  },
]);

createRoot(document.getElementById("root")).render(
  <RouterProvider router={router} />
);

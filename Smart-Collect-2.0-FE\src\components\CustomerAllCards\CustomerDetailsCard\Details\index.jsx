import React from "react";
import { Flex, Typography } from "antd";
import { formattedTextToCapitalized } from "../../../../constant";
import Style from "../_customer-details-card.module.scss";
import PropTypes from "prop-types";

const { Text } = Typography;
export function Details({ title, image, value, width = "14px" }) {
  return (
    <Flex justify="space-between">
      <Flex gap={10} align="center">
        <div className={Style.icon_img} style={{ width: width }}>
          {typeof image === "string" ? <img src={image} alt={title} /> : image}
        </div>
        <Text className={Style.title}>
          {formattedTextToCapitalized(title)}:
        </Text>
      </Flex>
      <Text className={Style.value}>{value}</Text>
    </Flex>
  );
}

Details.propTypes = {
  title: PropTypes.string,
  image: PropTypes.oneOfType([PropTypes.string, PropTypes.any]),
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  width: PropTypes.string,
};

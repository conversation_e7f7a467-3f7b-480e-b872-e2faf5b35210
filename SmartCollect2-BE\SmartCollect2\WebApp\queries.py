import WebApp.utilities as util
from sqlalchemy.sql import text

def generate_filter_sql_query(request):
    data = request.data
    conditions = []
    params = {}
    for key, value in data.items():
        if value == "all":
            continue  
        elif isinstance(value, list) and value:
            value = tuple(str(v) if isinstance(v, int) else v for v in value)
            conditions.append(f'acs."{key}" IN %({key})s')
            params[key] = value 
        else:
            conditions.append("TRUE") 
    where_clause = " AND ".join(conditions) if conditions else "TRUE"
    query = f"""
        SELECT DISTINCT 
            COALESCE(acs."State", 'N/A') AS "State", 
            COALESCE(acs."State_id", 'N/A') AS "State_id",
            COALESCE(acs."Region", 'N/A') AS "Region", 
            COALESCE(acs."Region_id", 'N/A') AS "Region_id",
            COALESCE(acs."Branch", 'N/A') AS "Branch", 
            COALESCE(acs."Branch_id", 'N/A') AS "Branch_id", 
            COALESCE(acs."CollectionOfficerName", 'N/A') AS "CollectionOfficerName",
            COALESCE(acs."CollectionOfficerID", 'N/A') AS "CollectionOfficerID",
            COALESCE(acs."LoanType", 'N/A') AS "LoanType"
        FROM "AccountSummary" acs
        WHERE {where_clause}
    """
    return query, params

def dpd_top_cards_query(where_clause=None) :
    return f"""
            SELECT * FROM (
                SELECT "State", "State_id", "Region", "Region_id", "Branch", "Branch_id", ds."DPD"::TEXT,
                    count(ds."DisbursementID")::INT AS accounts,
                    sum(ds."OverDueAmt")::INT AS amount,
                    sum(ds."PrincipleOutstanding")::INT as current_pos
                FROM "AccountSummary" ds
                WHERE ds."IsActive" = true 
                    -- AND ds."DPD" > 30 
                    AND "Branch_id" is not null
                    {f"AND {where_clause}" if where_clause else ""}
                GROUP BY "Region_id", "Branch_id", ds."DPD","Branch", "Region", "State", "State_id"
            ) t;
        """     
        
def get_portfolio_filtered_accounts(where_clause=None, search_term=None, page=1, page_size=10):
    offset = (page - 1) * page_size  # Calculate offset
    query = f"""
        SELECT * FROM (
            SELECT "State", "State_id", "Region", "Region_id", "Branch", "Branch_id", ds."DPD"::TEXT,
                count(ds."DisbursementID")::INT AS accounts,
                sum(ds."OverDueAmt")::INT AS amount,
                sum(ds."PrincipleOutstanding")::INT as current_pos
            FROM "AccountSummary" ds
            WHERE ds."IsActive" = true 
                AND "Branch_id" is not null
                {f"AND {where_clause}" if where_clause else ""}
                {f"AND (''State'' ILIKE '%{search_term}%' OR ''Region'' ILIKE '%{search_term}%' OR ''Branch'' ILIKE '%{search_term}%')" if search_term else ""}
            GROUP BY "Region_id", "Branch_id", ds."DPD", "Branch", "Region", "State", "State_id"
        ) t
        LIMIT {page_size} OFFSET {offset};
    """
    return query

def generate_loan_customers_query(search_term=None, page_size=10, offset=1, where_clause=None, dpd_condition=None, BankMstID=None, State_id=None):
    search_filter = ""
    if search_term:
        search_filter = f"AND (acs.\"DisbursementID\" ILIKE '%{search_term}%' " \
                         f"OR acs.\"Region\" ILIKE '%{search_term}%' " \
                         f"OR acs.\"Branch\" ILIKE '%{search_term}%')"
    state_filter = ""
    if State_id:
        if isinstance(State_id, list):
            state_filter = f"AND acs.\"State_id\" IN %({State_id})s"
        else:
            state_filter = f"AND acs.\"State_id\" = %({State_id})s"
    
    query = f'''
        WITH cte AS (
                SELECT 
                    lim."DisbursementID", 
                    lim."LoanMstID",
                    lim."BankMstID",
                    SUM(t."CollectedAmt") AS total_collection,
                    MAX(lim."Demand") AS inst_amt,
                    SUM(lim."PrincipleAmt" + lim."InterestAmt") AS Demand_upto,
                    COUNT(DISTINCT CASE 
                        WHEN lim."Demand" - COALESCE(t."CollectedAmt", 0) > 1 
                            AND lim."ScheduleDate" < CURRENT_DATE 
                        THEN lim."ScheduleID" 
                    END) AS overdue_installments
                FROM "LoanInstallmentMst" lim
                LEFT JOIN "Transactions" t 
                    ON lim."LoanMstID" = t."LoanMstID"
                WHERE lim."BankMstID" = {BankMstID}
                GROUP BY lim."DisbursementID", lim."LoanMstID",lim."BankMstID"
            ) 
            SELECT  
                acs."State"::text,
                acs."State_id"::TEXT,
                acs."Region"::text,
                acs."Region_id"::TEXT,
                acs."Branch"::text,
                acs."Branch_id"::TEXT,
                acs."DisbursementID"::text,
                acs."LastPaymentDate"::date, 
                lmst."DisbursementDate"::date,
                lmst."DisbursementAmt"::int,
                li."Demand"::int,
                acs."CurrentBalance"::int,
                acs."OverDueAmt"::int,
                acs."DPD"::int,
                acs."LoanType",
                c.*
            FROM "AccountSummary" acs  
            JOIN cte c ON acs."DisbursementID" = c."DisbursementID" and acs."BankMstID" = {BankMstID} and acs."LoanMstID" = c."LoanMstID"
            JOIN "LoanMst" lmst ON lmst."DisbursementID" = acs."DisbursementID" and lmst."LoanMstID" = c."LoanMstID"
            JOIN "LoanInstallmentMst" li ON li."LoanMstID" = c."LoanMstID" and li."ScheduleID" = 1 
            and c."DisbursementID" = li."DisbursementID"
            WHERE acs."IsActive" = true
            and acs."BankMstID" = {BankMstID}
            {f"AND {where_clause}" if where_clause else ""}
            {f"AND {dpd_condition}" if dpd_condition else ""}
            {state_filter}
            {search_filter}
            LIMIT {page_size} OFFSET {((offset - 1) * page_size)};
    '''
    return query

def generate_loan_customers_query_for_total_count(search_term=None, where_clause=None, dpd_condition=None, BankMstID=None):
    search_filter = ""
    if search_term:
        search_filter = f"AND (acs.\"DisbursementID\" ILIKE '%{search_term}%' " \
                         f"OR acs.\"Region\" ILIKE '%{search_term}%' " \
                         f"OR acs.\"Branch\" ILIKE '%{search_term}%')"
    
    query = f'''
        WITH cte AS (
                SELECT 
                    lim."DisbursementID", 
                    lim."LoanMstID",
                    lim."BankMstID",
                    SUM(t."CollectedAmt") AS total_collection,
                    MAX(lim."Demand") AS inst_amt,
                    SUM(lim."PrincipleAmt" + lim."InterestAmt") AS Demand_upto,
                    COUNT(DISTINCT CASE 
                        WHEN lim."Demand" - COALESCE(t."CollectedAmt", 0) > 1 
                            AND lim."ScheduleDate" < CURRENT_DATE 
                        THEN lim."ScheduleID" 
                    END) AS overdue_installments
                FROM "LoanInstallmentMst" lim
                LEFT JOIN "Transactions" t 
                    ON lim."LoanMstID" = t."LoanMstID"
                where lim."BankMstID" = {BankMstID}
                GROUP BY lim."DisbursementID", lim."LoanMstID",lim."BankMstID"
            ) 
            SELECT  
                Count(*)
            FROM "AccountSummary" acs  
            JOIN cte c ON acs."DisbursementID" = c."DisbursementID" and acs."BankMstID" = {BankMstID} and acs."LoanMstID" = c."LoanMstID"
            JOIN "LoanMst" lmst ON lmst."DisbursementID" = acs."DisbursementID" and lmst."LoanMstID" = c."LoanMstID"
            JOIN "LoanInstallmentMst" li ON li."LoanMstID" = c."LoanMstID" and li."ScheduleID" = 1 
            and c."DisbursementID" = li."DisbursementID"
            WHERE acs."IsActive" = true
            and acs."BankMstID" = {BankMstID}
                {f"AND {where_clause}" if where_clause else ""}
            {f"AND {dpd_condition}" if dpd_condition else ""}
            {f"AND {search_filter}" if search_filter else ""}
    '''
    return query
       
def generate_portfolio_cards_query(where_clause) :
    return f"""
        SELECT 
            COUNT(DISTINCT "State_id") AS states,
            COUNT(DISTINCT "Region_id") AS regions,
            COUNT(DISTINCT "Branch_id") AS branches,
            COUNT(DISTINCT "CollectionOfficerID") AS collectionOfficers,
            COUNT(DISTINCT "groupid") AS groupnames,
            COUNT(DISTINCT "DisbursementID") AS totalAccounts,
            COUNT(DISTINCT CASE WHEN "IsActive" = 'True' THEN "DisbursementID" END) AS activeAccounts,
            SUM(CASE when "IsActive" = 'True' THEN "DisbursementAmt" END) as activeAmountDisbursed,
            SUM(CASE when "IsActive" = 'True' THEN "OverDueAmt" END) as activeAmountOverdue,
            SUM(CASE when "IsActive" = 'True' THEN "PrincipleOutstanding" END) as activeAmountPos
        FROM "AccountSummary"
        {"WHERE" + where_clause if where_clause else "" }
        """
          
def generate_dynamic_dpd_distribution(where_clause, group_by_columns, dpd_ranges, sum_types):
    group_by_clause = ", ".join(group_by_columns)
    count_case_statements = []
    sum_case_statements = []
    for label, (dpd_min, dpd_max) in dpd_ranges.items():
        count_case = f"""
            COUNT(CASE
                WHEN "DPD" BETWEEN {dpd_min} AND {dpd_max} AND "IsActive" = 'True' THEN 1
                ELSE NULL
            END) AS {label.lower()}_customers
        """
        count_case_statements.append(count_case)
        for sum_type in sum_types:
            sum_case = f"""
                SUM(CASE
                    WHEN "DPD" BETWEEN {dpd_min} AND {dpd_max} AND "IsActive" = 'True' THEN "{sum_type}"
                    ELSE 0
                END) AS {label.lower()}_{sum_type.lower()}_outstanding
            """
            sum_case_statements.append(sum_case)
    count_case_statements.append("""
        COUNT(CASE WHEN "IsActive" = 'True' THEN 1 ELSE NULL END) AS active_account_count
    """)
    for sum_type in sum_types:
        sum_case_statements.append(f"""
            SUM(CASE WHEN "IsActive" = 'True' THEN "{sum_type}" ELSE NULL END) AS active_{sum_type.lower()}_sum
        """)
    query = f"""
        SELECT 
            {group_by_clause},
            {', '.join(count_case_statements)},
            {', '.join(sum_case_statements)}
        FROM "AccountSummary"
        {"WHERE " + where_clause if where_clause else ""}
        GROUP BY {group_by_clause}
    """
    return query

def generate_dynamic_dpd_distribution_with_pagination(where_clause, group_by_columns, dpd_ranges, sum_types, search_terms=None, page=1, per_page=10):
    # Dynamically build the GROUP BY clause based on input columns
    group_by_clause = ", ".join(group_by_columns)

    # Initialize the dynamic parts of the SELECT query
    count_case_statements = []
    sum_case_statements = []

    # Dynamically generate COUNT and SUM conditions for each DPD range
    for label, (dpd_min, dpd_max) in dpd_ranges.items():
        count_case = f"""
            COUNT(CASE
                WHEN "DPD" BETWEEN {dpd_min} AND {dpd_max} AND "IsActive" = 'True' THEN 1
                ELSE NULL
            END) AS {label.lower()}_customers
        """
        count_case_statements.append(count_case)

        for sum_type in sum_types:
            sum_case = f"""
                SUM(CASE
                    WHEN "DPD" BETWEEN {dpd_min} AND {dpd_max} AND "IsActive" = 'True' THEN "{sum_type}"
                    ELSE 0
                END) AS {label.lower()}_{sum_type.lower()}_outstanding
            """
            sum_case_statements.append(sum_case)

    # Add the active account count and the active amount sum for all types
    count_case_statements.append("""
        COUNT(CASE WHEN "IsActive" = 'True' THEN 1 ELSE NULL END) AS active_account_count
    """)
    for sum_type in sum_types:
        sum_case_statements.append(f"""
            SUM(CASE WHEN "IsActive" = 'True' THEN "{sum_type}" ELSE NULL END) AS active_{sum_type.lower()}_sum
        """)

    # Prepare the WHERE clause with search terms
    where_clause_with_search = where_clause
    if search_terms:
        search_conditions = []
        for column, term in search_terms.items():
            search_conditions.append(f'"{column}" LIKE %s')
        search_clause = " AND ".join(search_conditions)
        where_clause_with_search += (" AND " if where_clause else " WHERE ") + search_clause

    # Prepare the pagination part
    limit_offset = f"LIMIT {per_page} OFFSET {(page - 1) * per_page}"

    # Combine all parts to form the full query
    query = f"""
        SELECT 
            {group_by_clause},
            {', '.join(count_case_statements)},
            {', '.join(sum_case_statements)}
        FROM "AccountSummary"
        {where_clause_with_search}
        GROUP BY {group_by_clause}
        {limit_offset}
    """
    
    return query

def generate_portfolio_cards_query(where_clause) :
    return f"""
        SELECT 
            COUNT(DISTINCT "State_id") AS states,
            COUNT(DISTINCT "Region_id") AS regions,
            COUNT(DISTINCT "Branch_id") AS branches,
            COUNT(DISTINCT "CollectionOfficerID") AS collectionOfficers,
            COUNT(DISTINCT "groupid") AS groupnames,
            COUNT(DISTINCT "DisbursementID") AS totalAccounts,
            COUNT(DISTINCT CASE WHEN "IsActive" = 'True' THEN "DisbursementID" END) AS activeAccounts,
            SUM(CASE when "IsActive" = 'True' THEN "DisbursementAmt" END) as activeAmountDisbursed,
            SUM(CASE when "IsActive" = 'True' THEN "OverDueAmt" END) as activeAmountOverdue,
            SUM(CASE when "IsActive" = 'True' THEN "PrincipleOutstanding" END) as activeAmountPos
        FROM "AccountSummary"
        {"WHERE" + where_clause if where_clause else "" }
        """
              
def generate_dpd_distribution_with_pagination(where_clause, group_by_columns, dpd_ranges, sum_types, limit=10, offset=0, search_column=None, search_value=None): 
    group_by_clause = ", ".join(f'"{col}"' for col in group_by_columns)
    count_case_statements = []
    sum_case_statements = []

    for label, (dpd_min, dpd_max) in dpd_ranges.items():
        count_case = f"""
            COUNT(CASE
                WHEN "DPD" BETWEEN {dpd_min} AND {dpd_max} AND "IsActive" = 'True' THEN 1
                ELSE NULL
            END) AS {label.lower()}_customers
        """
        count_case_statements.append(count_case)

        for sum_type in sum_types:
            sum_case = f"""
                SUM(CASE
                    WHEN "DPD" BETWEEN {dpd_min} AND {dpd_max} AND "IsActive" = 'True' THEN "{sum_type}"
                    ELSE 0
                END) AS {label.lower()}_{sum_type.lower()}_outstanding
            """
            sum_case_statements.append(sum_case)

    count_case_statements.append("""
        COUNT(CASE WHEN "IsActive" = 'True' THEN 1 ELSE NULL END) AS active_account_count
    """)

    for sum_type in sum_types:
        sum_case_statements.append(f"""
            SUM(CASE WHEN "IsActive" = 'True' THEN "{sum_type}" ELSE NULL END) AS active_{sum_type.lower()}_sum
        """)
        sum_case_statements.append(sum_case)

    # Apply searching logic
    search_clause = ""
    if search_column and search_value:
        search_clause = f'AND "{search_column}"::TEXT ILIKE \'%{search_value}%\''

    # Query to fetch total count before applying pagination
    count_query = f"""
        SELECT COUNT(*) AS total_records
        FROM (
            SELECT {group_by_clause}
            FROM "AccountSummary"
            WHERE {"1=1" if not where_clause else where_clause} {search_clause}
            GROUP BY {group_by_clause}
        ) AS subquery
    """
    # Query with pagination
    pagination_clause = f"OFFSET {((offset - 1) * limit)} ROWS FETCH NEXT {limit} ROWS ONLY" if limit is not None and offset is not None else ""
    data_query = f"""
        SELECT 
            {group_by_clause},
            {', '.join(count_case_statements)},
            {', '.join(sum_case_statements)}
        FROM "AccountSummary"
        WHERE {"1=1" if not where_clause else where_clause} {search_clause}
        GROUP BY {group_by_clause}
        {pagination_clause}
    """

    return count_query, data_query

def generate_bucket_movement_summary_query(where_clause=None):
    dpd_categories = util.master_dpd_names_portfolio()  
    current_dpd_case = []
    prev_dpd_case = []
    for category in dpd_categories:
        dpd_range_start = category['dpd_range_start']
        dpd_range_end = category['dpd_range_end']
        dpd_category_name = category['dpd_category_name']
        if dpd_range_start == dpd_range_end:
            current_dpd_case.append(f"WHEN CAST(\"CurrentDPD\" AS INTEGER) = {dpd_range_start} THEN '{dpd_category_name}'")
        else:
            current_dpd_case.append(f"WHEN CAST(\"CurrentDPD\" AS INTEGER) BETWEEN {dpd_range_start} AND {dpd_range_end} THEN '{dpd_category_name}'")
        if dpd_range_start == dpd_range_end:
            prev_dpd_case.append(f"WHEN CAST(\"LastMonthDPD\" AS INTEGER) = {dpd_range_start} THEN '{dpd_category_name}'")
        else:
            prev_dpd_case.append(f"WHEN CAST(\"LastMonthDPD\" AS INTEGER) BETWEEN {dpd_range_start} AND {dpd_range_end} THEN '{dpd_category_name}'")
    current_dpd_case_sql = " ".join(current_dpd_case)
    prev_dpd_case_sql = " ".join(prev_dpd_case)
    where_part = f" WHERE {where_clause}" if where_clause else ""
    return text(f"""
       SELECT 
            "State",
            "State_id",
            "Region",
            "Region_id",
            "Branch",
            "Branch_id",
            "BankMstID",
            "CollectionOfficerName",
            "CollectionOfficerID",
            "DisbursementID", 
            "OverDueAmt", 
            "PrincipleOutstanding", 
            "LastMonthDPD", 
            "CurrentDPD",
            CASE 
                {current_dpd_case_sql}
            END AS currentDPDBucket,
            CASE 
                {prev_dpd_case_sql}
            END AS prevDPDBucket
        FROM "AccountSummary" as2{where_part}
    """)
    
def generate_bucket_movement_cards_query(where_clause=None):
    dpd_categories = util.master_dpd_names_portfolio()  
    current_dpd_case = []
    previous_dpd_case = []
    for index, category in enumerate(dpd_categories):
        dpd_range_start = category['dpd_range_start']
        dpd_range_end = category['dpd_range_end']
        if dpd_range_start == dpd_range_end:
            current_dpd_case.append(f"WHEN CAST(as2.\"CurrentDPD\" AS INTEGER) = {dpd_range_start} THEN {index}")
        else:
            current_dpd_case.append(f"WHEN CAST(as2.\"CurrentDPD\" AS INTEGER) BETWEEN {dpd_range_start} AND {dpd_range_end} THEN {index}")
        if dpd_range_start == dpd_range_end:
            previous_dpd_case.append(f"WHEN CAST(as2.\"LastMonthDPD\" AS INTEGER) = {dpd_range_start} THEN {index}")
        else:
            previous_dpd_case.append(f"WHEN CAST(as2.\"LastMonthDPD\" AS INTEGER) BETWEEN {dpd_range_start} AND {dpd_range_end} THEN {index}")
    current_dpd_case_sql = " ".join(current_dpd_case)
    previous_dpd_case_sql = " ".join(previous_dpd_case)
    where_part = f" WHERE {where_clause}" if where_clause else ""
    return text(f"""
        WITH accounts AS (
            SELECT 
                as2."DisbursementID" AS account_number,
                CASE 
                    {current_dpd_case_sql}
                END AS current_dpd,
                CASE 
                    {previous_dpd_case_sql}
                END AS previous_dpd
            FROM public."AccountSummary" as2
            {where_part}
        )
        SELECT 
            COUNT(CASE WHEN current_dpd = previous_dpd THEN account_number ELSE NULL END) AS same_bucket,
            COUNT(CASE WHEN current_dpd > previous_dpd THEN account_number ELSE NULL END) AS upper_bucket,
            COUNT(CASE WHEN current_dpd < previous_dpd THEN account_number ELSE NULL END) AS lower_bucket
        FROM accounts;
    """)   
    
def generate_scrub_summary_data(where_clause=None):
    query = f""" 
        WITH summary_status AS (
            SELECT 
                credit_report_id, 
                COUNT(los_app_id) AS total_accounts,
                COUNT(CASE WHEN account_status IN (
                    'Current Account', 'New Account', 'Doubtful', 'Loss', 'Active',
                    'Restructured Due to COVID19', 'Sub-standard', 'Special Mention', 'Standard'
                ) THEN los_app_id END) AS active_accounts,
                COUNT(CASE WHEN account_status IN (
                    'Post Written Off Settled', 'Restructured & Closed', 'Closed Account', 
                    'Restructured and Closed', 'Settled', 'Willful Default', 'Adjustment Pending', 
                    'Closed', 'Post Write Off Settled', 'Post Write Off Closed'
                ) THEN los_app_id END) AS closed_accounts,
                COUNT(CASE WHEN account_status LIKE '%days past due%' 
                        OR account_status IN ('Delinquent', 'Willful Default') 
                        THEN los_app_id END) AS delinquent_accounts,
                COUNT(CASE WHEN account_status IN ('Written Off', 'Charge Off/Written Off')
                        THEN los_app_id END) AS writeoff_accounts,
                COUNT(CASE WHEN account_status IN ('Suit Filed', 'Cancelled', 'Sold/Purchased')
                        THEN los_app_id END) AS others
            FROM summary s
            GROUP BY credit_report_id
        )
        SELECT 
            md.*,
            s.credit_report_id AS reference_no, 
            s.perform_cns_score AS score, 
            ds."DPD", 
            ds."DisbursementID" , 
            ds."BankMstID",
            ds."State",
            ds."State_id",
            ds."Region",
            ds."Region_id",
            ds."Branch",
            ds."Branch_id",
            ds."BranchMstID",
            ds."OverDueAmt",
            CASE 
                WHEN s.perform_cns_score = 0 THEN 'New to Credit'
                WHEN s.perform_cns_score BETWEEN 1 AND 600 OR s.perform_cns_score < 0 THEN 'Very High Risk'
                WHEN s.perform_cns_score BETWEEN 601 AND 650 THEN 'High Risk'
                WHEN s.perform_cns_score BETWEEN 651 AND 700 THEN 'Medium Risk'
                WHEN s.perform_cns_score BETWEEN 701 AND 750 THEN 'Low Risk'
                WHEN s.perform_cns_score >= 751 THEN 'Very Low Risk'
            END AS risk_category
        FROM summary s 
        JOIN "AccountSummary" ds 
            ON ds."DisbursementID" = (
                SUBSTRING(s.los_app_id, 1, 8) || '1' || 
                SUBSTRING(s.los_app_id, 9, LENGTH(s.los_app_id))
            )
        LEFT JOIN summary_status md 
            ON s.credit_report_id = md.credit_report_id
        """
    if where_clause:
        query += f" WHERE {where_clause};"
    else:
        query += ";"
    return query



def generate_scrub_summary_customer_query(
    where_clause=None,
    dpd_condition=None, 
    risk_category_condition=None,
    limit=10,
    offset=0,
    search_value=None  
):
    base_query = """
        WITH summary_status AS (
            SELECT 
                credit_report_id, 
                COUNT(los_app_id) AS total_accounts,
                COUNT(CASE WHEN account_status IN (
                    'Current Account', 'New Account', 'Doubtful', 'Loss', 'Active',
                    'Restructured Due to COVID19', 'Sub-standard', 'Special Mention', 'Standard'
                ) THEN los_app_id END) AS active_accounts,
                COUNT(CASE WHEN account_status IN (
                    'Post Written Off Settled', 'Restructured & Closed', 'Closed Account', 
                    'Restructured and Closed', 'Settled', 'Willful Default', 'Adjustment Pending', 
                    'Closed', 'Post Write Off Settled', 'Post Write Off Closed'
                ) THEN los_app_id END) AS closed_accounts,
                COUNT(CASE WHEN account_status LIKE '%%days past due%%' 
                        OR account_status IN ('Delinquent', 'Willful Default') 
                        THEN los_app_id END) AS delinquent_accounts,
                COUNT(CASE WHEN account_status IN ('Written Off', 'Charge Off/Written Off')
                        THEN los_app_id END) AS writeoff_accounts,
                COUNT(CASE WHEN account_status IN ('Suit Filed', 'Cancelled', 'Sold/Purchased')
                        THEN los_app_id END) AS others,
                CASE 
                    WHEN perform_cns_score = 0 THEN 'New to Credit'
                    WHEN perform_cns_score BETWEEN 1 AND 600 OR perform_cns_score < 0 THEN 'Very High Risk'
                    WHEN perform_cns_score BETWEEN 601 AND 650 THEN 'High Risk'
                    WHEN perform_cns_score BETWEEN 651 AND 700 THEN 'Medium Risk'
                    WHEN perform_cns_score BETWEEN 701 AND 750 THEN 'Low Risk'
                    WHEN perform_cns_score >= 751 THEN 'Very Low Risk'
                END AS risk_category
            FROM summary s
            GROUP BY credit_report_id, perform_cns_score 
        )
        SELECT 
            md.*, 
            s.credit_report_id AS reference_no, 
            s.perform_cns_score AS score, 
            ds."CustomerName",
            ds."DisbursementID",
            ds."State",
            ds."State_id",
            ds."BankMstID",
            ds."BranchMstID",
            ds."DPD",
            ds."TotalOutstanding",
            (SELECT lm."EMIAmount" 
             FROM "LoanMst" lm 
             WHERE lm."DisbursementID" = ds."DisbursementID") AS Demand
        FROM "AccountSummary" ds 
        JOIN summary s 
            ON ds."DisbursementID" = (
                SUBSTRING(s.los_app_id, 1, 8) || '1' || 
                SUBSTRING(s.los_app_id, 9, LENGTH(s.los_app_id))
            )
        LEFT JOIN summary_status md 
            ON s.credit_report_id = md.credit_report_id
        LEFT JOIN "CustomerMst" ci 
            ON ds."DisbursementID" = ci."CustomerID"
    """
    conditions = []
    if where_clause:
        conditions.append(where_clause)
    if dpd_condition:
        conditions.append(dpd_condition)
    if risk_category_condition:
        conditions.append(risk_category_condition)

    if search_value:
        like_pattern = f"'%%{search_value}%%'"
        search_conditions = [
            f"s.credit_report_id::text ILIKE {like_pattern}",
            f"s.perform_cns_score::text ILIKE {like_pattern}",
            f'ds."DisbursementID"::text ILIKE {like_pattern}',
            f'ds."State" ILIKE {like_pattern}',
            f'ds."State_id"::text ILIKE {like_pattern}',
            f'ds."BankMstID"::text ILIKE {like_pattern}',
            f'ds."BranchMstID"::text ILIKE {like_pattern}',
            f'ds."DPD"::text ILIKE {like_pattern}',
            f"risk_category ILIKE {like_pattern}"
        ]
        conditions.append("(" + " OR ".join(search_conditions) + ")")

    if conditions:
        base_query += "\nWHERE " + " AND ".join(conditions)
    if limit and offset :
        base_query += f"\nLIMIT {limit} OFFSET {limit * offset}"

    return base_query

    
import math

def generate_scrub_summary_customer_count_query(
    where_clause=None,
    dpd_condition=None, 
    risk_category_condition=None,
    search_value=None
):
    count_query = """
        WITH summary_status AS (
            SELECT 
                credit_report_id, 
                COUNT(los_app_id) AS total_accounts,
                COUNT(CASE WHEN account_status IN (
                    'Current Account', 'New Account', 'Doubtful', 'Loss', 'Active',
                    'Restructured Due to COVID19', 'Sub-standard', 'Special Mention', 'Standard'
                ) THEN los_app_id END) AS active_accounts,
                COUNT(CASE WHEN account_status IN (
                    'Post Written Off Settled', 'Restructured & Closed', 'Closed Account', 
                    'Restructured and Closed', 'Settled', 'Willful Default', 'Adjustment Pending', 
                    'Closed', 'Post Write Off Settled', 'Post Write Off Closed'
                ) THEN los_app_id END) AS closed_accounts,
                COUNT(CASE WHEN account_status LIKE '%%days past due%%' 
                        OR account_status IN ('Delinquent', 'Willful Default') 
                        THEN los_app_id END) AS delinquent_accounts,
                COUNT(CASE WHEN account_status IN ('Written Off', 'Charge Off/Written Off')
                        THEN los_app_id END) AS writeoff_accounts,
                COUNT(CASE WHEN account_status IN ('Suit Filed', 'Cancelled', 'Sold/Purchased')
                        THEN los_app_id END) AS others,
                CASE 
                    WHEN perform_cns_score = 0 THEN 'New to Credit'
                    WHEN perform_cns_score BETWEEN 1 AND 600 OR perform_cns_score < 0 THEN 'Very High Risk'
                    WHEN perform_cns_score BETWEEN 601 AND 650 THEN 'High Risk'
                    WHEN perform_cns_score BETWEEN 651 AND 700 THEN 'Medium Risk'
                    WHEN perform_cns_score BETWEEN 701 AND 750 THEN 'Low Risk'
                    WHEN perform_cns_score >= 751 THEN 'Very Low Risk'
                END AS risk_category
            FROM summary s
            GROUP BY credit_report_id, perform_cns_score 
        )
        SELECT COUNT(*) AS total_records
        FROM "AccountSummary" ds 
        JOIN summary s 
            ON ds."DisbursementID" = (
                SUBSTRING(s.los_app_id, 1, 8) || '1' || 
                SUBSTRING(s.los_app_id, 9, LENGTH(s.los_app_id))
            )
        LEFT JOIN summary_status md 
            ON s.credit_report_id = md.credit_report_id
        LEFT JOIN "CustomerMst" ci 
            ON ds."DisbursementID" = ci."CustomerID"
    """

    conditions = []
    if where_clause:
        conditions.append(where_clause)
    if dpd_condition:
        conditions.append(dpd_condition)
    if risk_category_condition:
        conditions.append(risk_category_condition)

    if search_value:
        like_pattern = f"'%%{search_value}%%'"
        search_conditions = [
            f"s.credit_report_id::text ILIKE {like_pattern}",
            f"s.perform_cns_score::text ILIKE {like_pattern}",
            f'ds."DisbursementID"::text ILIKE {like_pattern}',
            f'ds."State" ILIKE {like_pattern}',
            f'ds."State_id"::text ILIKE {like_pattern}',
            f'ds."BankMstID"::text ILIKE {like_pattern}',
            f'ds."BranchMstID"::text ILIKE {like_pattern}',
            f'ds."DPD"::text ILIKE {like_pattern}',
            f"risk_category ILIKE {like_pattern}"
        ]
        conditions.append("(" + " OR ".join(search_conditions) + ")")

    if conditions:
        count_query += "\nWHERE " + " AND ".join(conditions)

    return count_query
 
 
# def generate_scrub_analysis_query(where_clause=None) :
#     return  f"""
#             WITH hmpl_mfi AS (
#                 SELECT 
#                     s.credit_report_id, s.perform_cns_score ,
#                     ds."State", ds."State_id",
#                     ds."Region", ds."Region_id",
#                     ds."Branch", ds."Branch_id" , ds."BankMstID", ds."LastCollectedAmount", 
#                     MAX(ds."LastPaymentDate") AS last_payed_hmpl,
#                     COUNT(s.los_app_id) AS total_accounts,
#                     COUNT(CASE WHEN s.account_status IN (
#                         'Current Account', 'New Account', 'Doubtful', 'Loss', 'Active',
#                         'Restructured Due to COVID19', 'Sub-standard', 'Special Mention', 'Standard'
#                     ) THEN s.los_app_id END) AS active_accounts,
#                     COUNT(CASE WHEN s.account_status IN (
#                         'Post Written Off Settled', 'Restructured & Closed', 'Closed Account', 
#                         'Restructured and Closed', 'Settled', 'Willful Default', 'Adjustment Pending', 
#                         'Closed', 'Post Write Off Settled', 'Post Write Off Closed'
#                     ) THEN s.los_app_id END) AS closed_accounts,
#                     COUNT(CASE WHEN s.account_status LIKE '%days past due%' 
#                             OR s.account_status IN ('Delinquent', 'Willful Default') 
#                             THEN s.los_app_id END) AS delinquent_accounts,
#                     COUNT(CASE WHEN s.account_status IN ('Written Off', 'Charge Off/Written Off')
#                             THEN s.los_app_id END) AS writeoff_accounts,
#                     COUNT(CASE WHEN s.account_status IN ('Suit Filed', 'Cancelled', 'Sold/Purchased')
#                             THEN s.los_app_id END) AS others
#                 FROM summary s
#                 JOIN account i 
#                     ON i.credit_report_id = s.credit_report_id
#                 JOIN "AccountSummary" ds 
#                     ON ds."DisbursementID" = (
#                         SUBSTRING(s.los_app_id, 1, 8) || '1' || 
#                         SUBSTRING(s.los_app_id, 9, LENGTH(s.los_app_id))
#                     )
#                 WHERE i.credit_grantor IN ('HMPL NIDHI LIMITED', 'Hindusthan Microfinance Pvt. Ltd.')
#                 GROUP BY s.credit_report_id,   ds."State", ds."State_id",
#                     ds."Region", ds."Region_id",
#                     ds."Branch", ds."Branch_id" ,ds."BankMstID", ds."LastCollectedAmount", s.perform_cns_score 
#             ), other_mfi AS (
#                 SELECT 
#                     s.credit_report_id,
#                     MAX(ds."LastPaymentDate") AS last_payed_other,
#                     COUNT(s.los_app_id) AS total_accounts,
#                     COUNT(CASE WHEN s.account_status IN (
#                         'Current Account', 'New Account', 'Doubtful', 'Loss', 'Active',
#                         'Restructured Due to COVID19', 'Sub-standard', 'Special Mention', 'Standard'
#                     ) THEN s.los_app_id END) AS active_accounts,
#                     COUNT(CASE WHEN s.account_status IN (
#                         'Post Written Off Settled', 'Restructured & Closed', 'Closed Account', 
#                         'Restructured and Closed', 'Settled', 'Willful Default', 'Adjustment Pending', 
#                         'Closed', 'Post Write Off Settled', 'Post Write Off Closed'
#                     ) THEN s.los_app_id END) AS closed_accounts,
#                     COUNT(CASE WHEN s.account_status LIKE '%days past due%' 
#                             OR s.account_status IN ('Delinquent', 'Willful Default') 
#                             THEN s.los_app_id END) AS delinquent_accounts,
#                     COUNT(CASE WHEN s.account_status IN ('Written Off', 'Charge Off/Written Off')
#                             THEN s.los_app_id END) AS writeoff_accounts,
#                     COUNT(CASE WHEN s.account_status IN ('Suit Filed', 'Cancelled', 'Sold/Purchased')
#                             THEN s.los_app_id END) AS others
#                 FROM summary s
#                 JOIN account i 
#                     ON i.credit_report_id = s.credit_report_id
#                 JOIN "AccountSummary" ds 
#                     ON ds."DisbursementID" = (
#                         SUBSTRING(s.los_app_id, 1, 8) || '1' || 
#                         SUBSTRING(s.los_app_id, 9, LENGTH(s.los_app_id))
#                     )
#                 WHERE i.credit_grantor NOT IN ('HMPL NIDHI LIMITED', 'Hindusthan Microfinance Pvt. Ltd.')
#                 GROUP BY s.credit_report_id
#             )
#             SELECT 
#                 hm.*, 
#                 om.last_payed_other,
#                 CASE 
#                     WHEN hm.last_payed_hmpl >= CURRENT_DATE - INTERVAL '3 months' 
#                     AND om.last_payed_other >= CURRENT_DATE - INTERVAL '3 months' THEN 1
#                     ELSE 0
#                 END AS both_within_3_months,
#                 CASE 
#                     WHEN hm.last_payed_hmpl >= CURRENT_DATE - INTERVAL '3 months' 
#                     AND om.last_payed_other < CURRENT_DATE - INTERVAL '3 months' THEN 1
#                     ELSE 0
#                 END AS hmpl_within_3_months,
#                 CASE 
#                     WHEN om.last_payed_other >= CURRENT_DATE - INTERVAL '3 months' 
#                     AND hm.last_payed_hmpl < CURRENT_DATE - INTERVAL '3 months' THEN 1
#                     ELSE 0
#                 END AS other_mfi_within_3_months,
#                 CASE 
#                     WHEN (hm.last_payed_hmpl IS NULL OR om.last_payed_other IS NULL)
#                     OR (hm.last_payed_hmpl < CURRENT_DATE - INTERVAL '3 months' 
#                     AND om.last_payed_other < CURRENT_DATE - INTERVAL '3 months') THEN 1
#                     ELSE 0
#                 END AS neither_within_3_months,
#                 CASE 
#             WHEN hm.perform_cns_score = 0 THEN 'New to Credit'
#             WHEN hm.perform_cns_score BETWEEN 1 AND 600 OR hm.perform_cns_score < 0 THEN 'Very High Risk'
#             WHEN hm.perform_cns_score BETWEEN 601 AND 650 THEN 'High Risk'
#             WHEN hm.perform_cns_score BETWEEN 651 AND 700 THEN 'Medium Risk'
#             WHEN hm.perform_cns_score BETWEEN 701 AND 750 THEN 'Low Risk'
#             WHEN hm.perform_cns_score >= 751 THEN 'Very Low Risk'
#         END AS risk_category
#             from hmpl_mfi hm 
#             JOIN other_mfi om 
#             ON hm.credit_report_id = om.credit_report_id
#             {where_clause if where_clause  else ";"}
#     """
  


def generate_scrub_analysis_customer_query(where_clause=None, limit=10, offset=0, search_value=None):
    query = f"""
        WITH original_scrub AS (
            WITH hmpl_mfi AS (
                SELECT 
                    s.credit_report_id, s.perform_cns_score,
                    ds."CustomerName",ds."TotalOutstanding",
                    MAX(ds."LastPaymentDate") AS last_payed_hmpl,
                    ds."DPD", 
                    ds."DisbursementID", 
                    ds."BankMstID",
                    ds."State",
                    ds."State_id",
                    ds."Region",
                    ds."Region_id",
                    ds."Branch",
                    ds."Branch_id",
                    ds."BranchMstID",
                    COUNT(s.los_app_id) AS total_accounts,
                    COUNT(CASE WHEN s.account_status IN (
                        'Current Account', 'New Account', 'Doubtful', 'Loss', 'Active',
                        'Restructured Due to COVID19', 'Sub-standard', 'Special Mention', 'Standard'
                    ) THEN s.los_app_id END) AS active_accounts,
                    COUNT(CASE WHEN s.account_status IN (
                        'Post Written Off Settled', 'Restructured & Closed', 'Closed Account', 
                        'Restructured and Closed', 'Settled', 'Willful Default', 'Adjustment Pending', 
                        'Closed', 'Post Write Off Settled', 'Post Write Off Closed'
                    ) THEN s.los_app_id END) AS closed_accounts,
                    COUNT(CASE WHEN s.account_status LIKE '%days past due%' 
                            OR s.account_status IN ('Delinquent', 'Willful Default') 
                            THEN s.los_app_id END) AS delinquent_accounts,
                    COUNT(CASE WHEN s.account_status IN ('Written Off', 'Charge Off/Written Off')
                            THEN s.los_app_id END) AS writeoff_accounts,
                    COUNT(CASE WHEN s.account_status IN ('Suit Filed', 'Cancelled', 'Sold/Purchased')
                            THEN s.los_app_id END) AS others,
                    (SELECT lm."EMIAmount" 
                     FROM "LoanMst" lm 
                     WHERE lm."DisbursementID" = ds."DisbursementID") AS Demand
                FROM summary s
                JOIN account i 
                    ON i.credit_report_id = s.credit_report_id
                JOIN "AccountSummary" ds 
                    ON ds."DisbursementID" = (
                        SUBSTRING(s.los_app_id, 1, 8) || '1' || 
                        SUBSTRING(s.los_app_id, 9, LENGTH(s.los_app_id))
                    )
                WHERE i.credit_grantor IN ('HMPL NIDHI LIMITED', 'Hindusthan Microfinance Pvt. Ltd.')
                GROUP BY s.credit_report_id, s.perform_cns_score, ds."DPD", 
                    ds."DisbursementID", ds."BankMstID",
                    ds."State", ds."State_id", ds."Region", ds."Region_id",
                    ds."Branch", ds."Branch_id", ds."BranchMstID",ds."CustomerName",ds."TotalOutstanding"
            ),
            other_mfi AS (
                SELECT 
                    s.credit_report_id, 
                    MAX(ds."LastPaymentDate") AS last_payed_other,
                    COUNT(s.los_app_id) AS total_accounts,
                    COUNT(CASE WHEN s.account_status IN (
                        'Current Account', 'New Account', 'Doubtful', 'Loss', 'Active',
                        'Restructured Due to COVID19', 'Sub-standard', 'Special Mention', 'Standard'
                    ) THEN s.los_app_id END) AS active_accounts,
                    COUNT(CASE WHEN s.account_status IN (
                        'Post Written Off Settled', 'Restructured & Closed', 'Closed Account', 
                        'Restructured and Closed', 'Settled', 'Willful Default', 'Adjustment Pending', 
                        'Closed', 'Post Write Off Settled', 'Post Write Off Closed'
                    ) THEN s.los_app_id END) AS closed_accounts,
                    COUNT(CASE WHEN s.account_status LIKE '%days past due%' 
                            OR s.account_status IN ('Delinquent', 'Willful Default') 
                            THEN s.los_app_id END) AS delinquent_accounts,
                    COUNT(CASE WHEN s.account_status IN ('Written Off', 'Charge Off/Written Off')
                            THEN s.los_app_id END) AS writeoff_accounts,
                    COUNT(CASE WHEN s.account_status IN ('Suit Filed', 'Cancelled', 'Sold/Purchased')
                            THEN s.los_app_id END) AS others
                FROM summary s
                JOIN account i 
                    ON i.credit_report_id = s.credit_report_id
                JOIN "AccountSummary" ds 
                    ON ds."DisbursementID" = (
                        SUBSTRING(s.los_app_id, 1, 8) || '1' || 
                        SUBSTRING(s.los_app_id, 9, LENGTH(s.los_app_id))
                    )
                WHERE i.credit_grantor NOT IN ('HMPL NIDHI LIMITED', 'Hindusthan Microfinance Pvt. Ltd.')
                GROUP BY s.credit_report_id
            )
            SELECT 
                hm.*, 
                om.last_payed_other,
                CASE 
                    WHEN hm.last_payed_hmpl >= CURRENT_DATE - INTERVAL '3 months' 
                    AND om.last_payed_other >= CURRENT_DATE - INTERVAL '3 months' THEN 1
                    ELSE 0
                END AS both_within_3_months,
                CASE 
                    WHEN hm.last_payed_hmpl >= CURRENT_DATE - INTERVAL '3 months' 
                    AND om.last_payed_other < CURRENT_DATE - INTERVAL '3 months' THEN 1
                    ELSE 0
                END AS hmpl_within_3_months,
                CASE 
                    WHEN om.last_payed_other >= CURRENT_DATE - INTERVAL '3 months' 
                    AND hm.last_payed_hmpl < CURRENT_DATE - INTERVAL '3 months' THEN 1
                    ELSE 0
                END AS other_mfi_within_3_months,
                CASE 
                    WHEN (hm.last_payed_hmpl IS NULL OR om.last_payed_other IS NULL)
                    OR (hm.last_payed_hmpl < CURRENT_DATE - INTERVAL '3 months' 
                    AND om.last_payed_other < CURRENT_DATE - INTERVAL '3 months') THEN 1
                    ELSE 0
                END AS neither_within_3_months,
                CASE 
                    WHEN hm.perform_cns_score = 0 THEN 'New to Credit'
                    WHEN hm.perform_cns_score BETWEEN 1 AND 600 OR hm.perform_cns_score < 0 THEN 'Very High Risk'
                    WHEN hm.perform_cns_score BETWEEN 601 AND 650 THEN 'High Risk'
                    WHEN hm.perform_cns_score BETWEEN 651 AND 700 THEN 'Medium Risk'
                    WHEN hm.perform_cns_score BETWEEN 701 AND 750 THEN 'Low Risk'
                    WHEN hm.perform_cns_score >= 751 THEN 'Very Low Risk'
                END AS risk_category
            FROM hmpl_mfi hm 
            JOIN other_mfi om 
                ON hm.credit_report_id = om.credit_report_id
        )
        SELECT * 
        FROM original_scrub os
        {f"WHERE {where_clause}" if where_clause else ""}
        ORDER BY os.credit_report_id
        LIMIT {limit} OFFSET {limit * offset};
    """
    return query

def generate_scrub_analysis_customer_count_query(where_clause=None):
    query = f"""
        WITH original_scrub AS (
            WITH hmpl_mfi AS (
                SELECT 
                    s.credit_report_id, s.perform_cns_score,
                    MAX(ds."LastPaymentDate") AS last_payed_hmpl,
                    ds."DPD", ds."DisbursementID", ds."BankMstID",
                    ds."State", ds."State_id", ds."Region", ds."Region_id",
                    ds."Branch", ds."Branch_id", ds."BranchMstID",
                    COUNT(s.los_app_id) AS total_accounts,
                    COUNT(CASE WHEN s.account_status IN (
                        'Current Account', 'New Account', 'Doubtful', 'Loss', 'Active',
                        'Restructured Due to COVID19', 'Sub-standard', 'Special Mention', 'Standard'
                    ) THEN s.los_app_id END) AS active_accounts,
                    COUNT(CASE WHEN s.account_status IN (
                        'Post Written Off Settled', 'Restructured & Closed', 'Closed Account', 
                        'Restructured and Closed', 'Settled', 'Willful Default', 'Adjustment Pending', 
                        'Closed', 'Post Write Off Settled', 'Post Write Off Closed'
                    ) THEN s.los_app_id END) AS closed_accounts,
                    COUNT(CASE WHEN s.account_status LIKE '%days past due%' 
                            OR s.account_status IN ('Delinquent', 'Willful Default') 
                            THEN s.los_app_id END) AS delinquent_accounts,
                    COUNT(CASE WHEN s.account_status IN ('Written Off', 'Charge Off/Written Off')
                            THEN s.los_app_id END) AS writeoff_accounts,
                    COUNT(CASE WHEN s.account_status IN ('Suit Filed', 'Cancelled', 'Sold/Purchased')
                            THEN s.los_app_id END) AS others,
                    (SELECT lm."EMIAmount" 
                     FROM "LoanMst" lm 
                     WHERE lm."DisbursementID" = ds."DisbursementID") AS Demand
                FROM summary s
                JOIN account i 
                    ON i.credit_report_id = s.credit_report_id
                JOIN "AccountSummary" ds 
                    ON ds."DisbursementID" = (
                        SUBSTRING(s.los_app_id, 1, 8) || '1' || 
                        SUBSTRING(s.los_app_id, 9, LENGTH(s.los_app_id))
                    )
                WHERE i.credit_grantor IN ('HMPL NIDHI LIMITED', 'Hindusthan Microfinance Pvt. Ltd.')
                GROUP BY s.credit_report_id, s.perform_cns_score, ds."DPD", 
                    ds."DisbursementID", ds."BankMstID",
                    ds."State", ds."State_id", ds."Region", ds."Region_id",
                    ds."Branch", ds."Branch_id", ds."BranchMstID"
            ),
            other_mfi AS (
                SELECT 
                    s.credit_report_id, 
                    MAX(ds."LastPaymentDate") AS last_payed_other,
                    COUNT(s.los_app_id) AS total_accounts,
                    COUNT(CASE WHEN s.account_status IN (
                        'Current Account', 'New Account', 'Doubtful', 'Loss', 'Active',
                        'Restructured Due to COVID19', 'Sub-standard', 'Special Mention', 'Standard'
                    ) THEN s.los_app_id END) AS active_accounts,
                    COUNT(CASE WHEN s.account_status IN (
                        'Post Written Off Settled', 'Restructured & Closed', 'Closed Account', 
                        'Restructured and Closed', 'Settled', 'Willful Default', 'Adjustment Pending', 
                        'Closed', 'Post Write Off Settled', 'Post Write Off Closed'
                    ) THEN s.los_app_id END) AS closed_accounts,
                    COUNT(CASE WHEN s.account_status LIKE '%days past due%' 
                            OR s.account_status IN ('Delinquent', 'Willful Default') 
                            THEN s.los_app_id END) AS delinquent_accounts,
                    COUNT(CASE WHEN s.account_status IN ('Written Off', 'Charge Off/Written Off')
                            THEN s.los_app_id END) AS writeoff_accounts,
                    COUNT(CASE WHEN s.account_status IN ('Suit Filed', 'Cancelled', 'Sold/Purchased')
                            THEN s.los_app_id END) AS others
                FROM summary s
                JOIN account i 
                    ON i.credit_report_id = s.credit_report_id
                JOIN "AccountSummary" ds 
                    ON ds."DisbursementID" = (
                        SUBSTRING(s.los_app_id, 1, 8) || '1' || 
                        SUBSTRING(s.los_app_id, 9, LENGTH(s.los_app_id))
                    )
                WHERE i.credit_grantor NOT IN ('HMPL NIDHI LIMITED', 'Hindusthan Microfinance Pvt. Ltd.')
                GROUP BY s.credit_report_id
            )
            SELECT 
                hm.*, 
                om.last_payed_other,
                CASE 
                    WHEN hm.last_payed_hmpl >= CURRENT_DATE - INTERVAL '3 months' 
                    AND om.last_payed_other >= CURRENT_DATE - INTERVAL '3 months' THEN 1
                    ELSE 0
                END AS both_within_3_months,
                CASE 
                    WHEN hm.last_payed_hmpl >= CURRENT_DATE - INTERVAL '3 months' 
                    AND om.last_payed_other < CURRENT_DATE - INTERVAL '3 months' THEN 1
                    ELSE 0
                END AS hmpl_within_3_months,
                CASE 
                    WHEN om.last_payed_other >= CURRENT_DATE - INTERVAL '3 months' 
                    AND hm.last_payed_hmpl < CURRENT_DATE - INTERVAL '3 months' THEN 1
                    ELSE 0
                END AS other_mfi_within_3_months,
                CASE 
                    WHEN (hm.last_payed_hmpl IS NULL OR om.last_payed_other IS NULL)
                    OR (hm.last_payed_hmpl < CURRENT_DATE - INTERVAL '3 months' 
                    AND om.last_payed_other < CURRENT_DATE - INTERVAL '3 months') THEN 1
                    ELSE 0
                END AS neither_within_3_months,
                CASE 
                    WHEN hm.perform_cns_score = 0 THEN 'New to Credit'
                    WHEN hm.perform_cns_score BETWEEN 1 AND 600 OR hm.perform_cns_score < 0 THEN 'Very High Risk'
                    WHEN hm.perform_cns_score BETWEEN 601 AND 650 THEN 'High Risk'
                    WHEN hm.perform_cns_score BETWEEN 651 AND 700 THEN 'Medium Risk'
                    WHEN hm.perform_cns_score BETWEEN 701 AND 750 THEN 'Low Risk'
                    WHEN hm.perform_cns_score >= 751 THEN 'Very Low Risk'
                END AS risk_category
            FROM hmpl_mfi hm 
            JOIN other_mfi om 
                ON hm.credit_report_id = om.credit_report_id
        )
        SELECT COUNT(*) AS total_rows 
        FROM original_scrub os
        {f"WHERE {where_clause}" if where_clause else ""};
    """
    return query

def generate_retention_failure_cards_query(where_clause = None, active_inactive_flag=True) : 
    return f"""
        SELECT
        COUNT(s.credit_report_id) AS cnt
        FROM account ac
            JOIN summary s ON ac.credit_report_id = s.credit_report_id
            JOIN "AccountSummary" acs 
                ON acs."DisbursementID" = (
                SUBSTRING(s.los_app_id, 1, 8) || '1' || 
                SUBSTRING(s.los_app_id, 9, LENGTH(s.los_app_id))
            )
        WHERE ac.account_type IN ('Property Loan', 'Gold Loan', 'Housing Loan')
        AND acs."IsActive" = {active_inactive_flag} { f"AND {where_clause}" if where_clause else ";"}
    """    
    
def generate_retention_failure_graph_query(var, where_clause = None, active_inactive_flag=True) :
    return f"""
        SELECT 
            count(case when ac.account_type ='Property Loan' 
                    or ac.account_type='Housing Loan' then s.los_app_id END 
                ) AS house_loan_count,
                count(case when ac.account_type='Gold Loan' then  s.los_app_id END ) AS gold_loan_count,
            acs."{var[0]}" ,
            acs."{var[1]}" 
        FROM account ac
            JOIN summary s ON ac.credit_report_id  = s.credit_report_id
            JOIN "AccountSummary" acs 
                ON acs."DisbursementID" = (
                    SUBSTRING(s.los_app_id, 1, 8) || '1' || 
                    SUBSTRING(s.los_app_id, 9, LENGTH(s.los_app_id))
                )
            WHERE ac.account_type  IN ('Property Loan', 'Gold Loan', 'Housing Loan')
        AND acs."IsActive"  = {active_inactive_flag} {f"AND {where_clause}" if where_clause else ""}
        group by acs."{var[0]}", acs."{var[1]}" 
    """

def generate_loan_cycle_id_graph_query_retention(where_clause = None) : 
    return f"""
        with cte as (
            select a.credit_report_id ,a."LOS-APP-ID" ,a.credit_grantor,ROW_NUMBER() OVER ( PARTITION by a.credit_report_id ORDER BY  a."DATE-REPORTED") AS row_no ,
            a."DATE-REPORTED" ,a."ACCOUNT-STATUS" ,a.account_type ,a."CONTRIBUTOR-TYPE"
            from account a 
        )
        select DISTINCT row_no as loan_cycle,COUNT(acs."DisbursementID") as accounts
        FROM "AccountSummary" acs     
        JOIN cte s  ON acs."DisbursementID" = (
                    SUBSTRING(s."LOS-APP-ID" , 1, 8) || '1' || 
                    SUBSTRING(s."LOS-APP-ID" , 9, LENGTH(s."LOS-APP-ID"))
                )
        where credit_grantor in ('HMPL NIDHI LIMITED','HINDUSTHAN MICROFINANCE') {f"AND {where_clause}" if where_clause else ""}
        group by row_no
        order by row_no
    """    

def generate_retention_failure_hierarchy_table_queries(var, where_clause=None, active_inactive_flag=True) :
    return f"""
        SELECT 
            count(case when ac.account_type ='Property Loan' 
                    or ac.account_type='Housing Loan' then s.los_app_id END 
                ) AS house_loan_count,
                count(case when ac.account_type='Gold Loan' then  s.los_app_id END ) AS gold_loan_count,
            {var}
        FROM account ac
            JOIN summary s ON ac.credit_report_id  = s.credit_report_id
            JOIN "AccountSummary" acs 
                ON acs."DisbursementID" = (
                    SUBSTRING(s.los_app_id, 1, 8) || '1' || 
                    SUBSTRING(s.los_app_id, 9, LENGTH(s.los_app_id))
                )
            WHERE ac.account_type  IN ('Property Loan', 'Gold Loan', 'Housing Loan')
        AND acs."IsActive"  = {active_inactive_flag} {f"AND {where_clause}" if where_clause else ""}
        group by {var}
    """


def generate_scrub_summary_sp(where_clause) :
    return f"""SELECT * FROM public.scrub_summary(
        'ds."BankMstID"::TEXT AS bankmstid',
        '{where_clause}'
    );"""



def generate_scrub_summary_sp_hierarchy(var, where_clause) :
    return f"""SELECT * FROM public.scrub_summary(
        '{var}, ds."BankMstID"::TEXT AS bankmstid, ',
        '{where_clause}'
    );"""

def generate_scrub_analysis_sp(where_clause, company_names="''HMPL NIDHI LIMITED'', ''ABC FIN''"):
    return f"""
        SELECT * FROM scrub_analysis(
            's.credt_rpt_id',
             {where_clause},
            '{company_names}'
        );
    """


def generate_scrub_analysis_sp_hierarchy(var, where_clause) :
    return f"""SELECT * FROM public.scrub_summary(
        '{var}, ds."BankMstID"::TEXT AS bankmstid, ',
        '{where_clause}'
    );"""
 
 
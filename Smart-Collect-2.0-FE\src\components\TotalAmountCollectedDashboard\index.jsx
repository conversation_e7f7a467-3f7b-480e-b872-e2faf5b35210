import { Flex, message, Table, Typography } from "antd";
import React, { useEffect, useState } from "react";
import AppHeader from "../AppHeader";
import {
  ALL_CACHE_NAMES,
  formatAmount,
  formattedTextToCapitalized,
  handleDateFormatter,
  handleDownloadTable,
  LOCAL_KEYS,
} from "../../constant";
import DOWNLOAD_IMG from "../../assets/Images/download.svg";
import Style from "./_style.module.scss";
import { AXIOS } from "../../apis/ho-Instance";
import ApplicationLoader from "../ApplicationLoader";
import {
  getCacheKey,
  getFromCache,
  getPostCacheKey,
  storeToCache,
} from "../../utils/cacheHelper";
import { encryptBody, isSameEncryptedBody } from "../../utils/crypto";

const { Text } = Typography;
const CACHE_NAME = ALL_CACHE_NAMES.DASHBOARD_CACHE;

export default function TotalAmountCollectedDashboard() {
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const storedFilter = localStorage.getItem("filterDate");
  const storedDpd = localStorage.getItem("DashboardDpdCategory");

  const getTotalAmountCollected = async () => {
    setIsLoading(true);
    const cacheKey = getCacheKey({ endpoint: "total_amount_collected/" });
    // Check cache
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setData(cachedData.total_amount_collected);
      setIsLoading(false);
    }
    try {
      const response = await AXIOS.get("total_amount_collected/");
      if (
        response.status === 200 &&
        response.data.total_amount_collected?.length
      ) {
        // Store data in cache
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
        setData(response.data.total_amount_collected);
      } else {
        setData([]);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // function for the post api for filtered
  const getTotalFilteredAmountCollected = async ({
    fromDate,
    toDate,
    BranchName,
    dpdRange,
    disbursementID,
  }) => {
    setIsLoading(true);
    const body = {
      fromDate,
      toDate,
      BranchName,
      dpdfrom: dpdRange?.start ?? -1,
      dpdto: dpdRange?.end ?? -1,
      DisbursementID: disbursementID,
    };
    const cacheKey = getPostCacheKey({
      endpoint: "total_amount_collected/",
      body,
    });
    const encryptedOldBody = localStorage.getItem(LOCAL_KEYS.HO_CHANNEL_FILTER);
    const sameBody = isSameEncryptedBody({ newBody: body, encryptedOldBody });

    if (sameBody) {
      const cachedData = await getFromCache({
        cacheName: CACHE_NAME,
        key: cacheKey,
      });
      if (cachedData) {
        setData(cachedData.total_amount_collected);
        setIsLoading(false);
      }
    }
    try {
      const response = await AXIOS.post("total_amount_collected/", body);
      console.log(response);
      if (
        response.data.total_amount_collected?.length &&
        response.status === 200
      ) {
        const encrypted = encryptBody(body);
        if (encrypted) {
          localStorage.setItem(LOCAL_KEYS.HO_CHANNEL_FILTER, encrypted);
        }
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
        setData(response.data.total_amount_collected);
      } else {
        setData([]);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Apply filter and fetch data
  const applyFilters = async ({
    fromDate,
    toDate,
    BranchName,
    disbursementID,
  }) => {
    await getTotalFilteredAmountCollected({
      fromDate: fromDate,
      toDate: toDate,
      BranchName,
      dpdRange: JSON.parse(storedDpd),
      disbursementID,
    });
  };

  // Handle clear filters
  const ClearFilters = () => {
    getTotalAmountCollected();
    localStorage.removeItem("filterDate");
    localStorage.removeItem(LOCAL_KEYS.HO_CHANNEL_FILTER);
  };

  // Handle downloading
  const handleDownload = async () => {
    if (data.length === 0 || !data) {
      return message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: "TotalAmountCollected",
        worksheetName: "Dashboard-Total-Amount-Collected",
        tableData: data,
      });
    }
  };

  // On mount, check localStorage for saved filters
  useEffect(() => {
    if (storedFilter || storedDpd) {
      const { fromDate, toDate, BranchName, disbursementID } = storedFilter
        ? JSON.parse(storedFilter)
        : {
            fromDate: null,
            toDate: null,
            BranchName: null,
            disbursementID: "",
          };
      const parsedDpd = storedDpd
        ? JSON.parse(storedDpd)
        : { start: -1, end: -1 };
      getTotalFilteredAmountCollected({
        fromDate,
        toDate,
        BranchName,
        dpdRange: parsedDpd,
        disbursementID,
      });
    } else {
      getTotalAmountCollected();
    }
  }, []);

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      sorter: (a, b) => (a.key ?? 0) - (b.key ?? 0),
      render: (_, { key }) => <Text>{key}.</Text>,
    },
    {
      title: "Customer Id",
      dataIndex: "CustomerID",
      sorter: (a, b) => Number(a.CustomerID) - Number(b.CustomerID),
      render: (CustomerId) => <Text>{CustomerId}</Text>,
    },
    {
      title: "Customer Name",
      dataIndex: "CustomerName",
      sorter: (a, b) =>
        String(a.CustomerName ?? "").localeCompare(
          String(b.CustomerName ?? "")
        ),
      render: (CustomerName) => (
        <Text className={Style.text}>
          {CustomerName ? formattedTextToCapitalized(CustomerName) : "-"}
        </Text>
      ),
    },
    {
      title: "Branch Name",
      dataIndex: "BranchName",
      sorter: (a, b) =>
        String(a.BranchName ?? "").localeCompare(String(b.BranchName ?? "")),
      render: (value) => <Text>{value || "-"}</Text>,
    },
    {
      title: "Disbursement ID",
      dataIndex: "DisbursementID",
      sorter: (a, b) =>
        String(a.DisbursementID ?? "").localeCompare(
          String(b.DisbursementID ?? "")
        ),
      render: (value) => <Text>{value || "-"}</Text>,
    },
    {
      title: "Loan Type​",
      dataIndex: "LoanType",
      sorter: (a, b) =>
        String(a.LoanType ?? "").localeCompare(String(b.LoanType ?? "")),
      render: (value) => <Text>{value || "-"}</Text>,
    },
    {
      title: "DPD​",
      dataIndex: "DPD",
      sorter: (a, b) => Number(a.DPD ?? 0) - Number(b.DPD ?? 0),
      render: (value) => <Text>{value || 0}</Text>,
    },
    {
      title: "Total Engagement ​",
      dataIndex: "total_engagements ​",
      sorter: (a, b) =>
        Number(a.total_engagements ?? 0) - Number(b.total_engagements ?? 0),
      render: (_, { total_engagements }) => (
        <Text>{total_engagements || 0}</Text>
      ),
    },
    {
      title: "Total Collection Amount",
      dataIndex: "CollectedAmt",
      sorter: (a, b) =>
        Number(a.CollectedAmt ?? 0) - Number(b.CollectedAmt ?? 0),
      render: (TotalCollectionAmount) => (
        <Text className={Style.blueText}>
          {formatAmount(TotalCollectionAmount || 0)}
        </Text>
      ),
    },
    {
      title: "Latest Collected Date",
      dataIndex: "CollectedDate",
      sorter: (a, b) => {
        const dateA = a.CollectedDate ? new Date(a.CollectedDate).getTime() : 0;
        const dateB = b.CollectedDate ? new Date(b.CollectedDate).getTime() : 0;
        return dateA - dateB;
      },
      render: (value) => (
        <Text>{value ? handleDateFormatter(value) : "--"}</Text>
      ),
    },
  ];

  const dataSource = Array.isArray(data)
    ? data.map((data, i) => ({
        key: i + 1,
        ...data,
      }))
    : [];
  console.log(data);
  return (
    <Flex gap={20} vertical>
      <AppHeader
        title={"Total Amount Collected from Customers"}
        applyFilters={applyFilters}
        ClearFilters={ClearFilters}
        isDashboardOtherPages={true}
      />

      <Flex gap={20} vertical>
        <Flex justify="end">
          <button className={Style.download_button} onClick={handleDownload}>
            <img src={DOWNLOAD_IMG} alt="download-button" />
          </button>
        </Flex>

        {/* <Spin tip={"Loading..."} spinning={isLoading}> */}
        {isLoading ? (
          <ApplicationLoader />
        ) : (
          <Table
            bordered
            virtual
            className={Style.CustomTable}
            columns={columns}
            dataSource={dataSource}
            scroll={{
              x: 800,
              y: 360,
            }}
            pagination={{
              showSizeChanger: false,
            }}
          />
        )}
        {/* </Spin> */}
      </Flex>
    </Flex>
  );
}

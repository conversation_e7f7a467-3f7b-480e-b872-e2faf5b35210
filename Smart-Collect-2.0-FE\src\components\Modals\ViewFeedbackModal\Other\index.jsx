import React from "react";
import Style from "./_other.module.scss";
import { Flex, Input, Switch, Typography } from "antd";
import PropTypes from "prop-types";

const { Text } = Typography;

export function Other({ latestFeedBack }) {
  return (
    <div className={Style.other}>
      <Flex vertical gap={20} justify="center">
        {/* reply */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Customer Not Reply:</Text>
          <Switch value={latestFeedBack?.CustomerNotReply} />
        </Flex>

        {/* wrong number */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Wrong Number:</Text>
          <Switch
            className={Style.switch}
            value={latestFeedBack?.WrongNumber}
          />
        </Flex>

        {/* wrong number */}
        {latestFeedBack?.WrongNumber && (
          <Flex justify="space-between" align="center">
            <Text className={Style.text}>Alternate number:</Text>
            <Input
              placeholder="Optional"
              className={Style.custom_input}
              value={latestFeedBack?.number || ""}
              disabled
            />
          </Flex>
        )}

        {/* /remark */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Remark:</Text>
          <Input
            placeholder="Enter"
            className={Style.custom_input}
            value={latestFeedBack?.remark || ""}
            disabled
          />
        </Flex>
      </Flex>
    </div>
  );
}
Other.propTypes={
  latestFeedBack: PropTypes.object
}

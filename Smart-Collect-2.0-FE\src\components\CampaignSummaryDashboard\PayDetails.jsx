import React from "react";
import Slider from "react-slick";
import READY_IMG from "../../assets/Images/ready.svg";
import REFUSED_IMG from "../../assets/Images/refused.svg";
import ALREADY_IMG from "../../assets/Images/already.svg";
import { PayCard } from "./PayCard";
import { Link } from "react-router";
import {
  SMART_COLLECT_MENU_IDS,
  formatAmount,
  formatDigits,
} from "../../constant";
import { useTour } from "../../ContextApis/TourProvider";
import Style from "./_style.module.scss";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import PropTypes from "prop-types";

export function PayDetails({ dashboardData }) {
  const { payDetailsRef } = useTour();
  const settings = {
    dots: true,
    infinite: false,
    speed: 500,
    slidesToShow: 3,
    slidesToScroll: 3,
    // variableWidth: true,
    arrows: true,
    draggable: true, // Allows dragging with the mouse
    swipe: true,
    touchMove: true, // Allows touchpad & touchscreen scrolling
    cssEase: "linear",
    adaptiveHeight: true,
    responsive: [
      {
        breakpoint: 1200,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 3,
        },
      },
      {
        breakpoint: 992,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 3,
        },
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 3,
        },
      },
      {
        breakpoint: 576,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
    ],
  };

  return (
    <div className={Style.pay_container} ref={payDetailsRef}>
      <Slider {...settings}>
        {/* Ready to pay */}
        <Link to={SMART_COLLECT_MENU_IDS.READY_TO_PAY || "#"}>
          <PayCard
            title={"Promised to Pay"}
            accounts={formatDigits(dashboardData?.promised_to_pay || 0)}
            amount={formatAmount(dashboardData?.promised_amount || 0)}
            color={"#02951A"}
            icon={READY_IMG}
          />
        </Link>

        {/*Refused to Pay */}
        <Link to={SMART_COLLECT_MENU_IDS.REFUSED_TO_PAY || "#"}>
          <PayCard
            title={"Refused to Pay"}
            accounts={formatDigits(dashboardData?.refuse_to_pay || 0)}
            amount={formatAmount(dashboardData?.denied_amount || 0)}
            color={"#05989F"}
            icon={REFUSED_IMG}
            isVisible={true}
          />
        </Link>

        {/*Already to Pay */}
        <Link to={SMART_COLLECT_MENU_IDS.ALREADY_PAY || "#"}>
          <PayCard
            title={"Already Paid"}
            accounts={formatDigits(dashboardData?.already_paid || 0)}
            amount={formatAmount(dashboardData?.already_paid_amount || 0)}
            color={"#CB4848"}
            icon={ALREADY_IMG}
          />
        </Link>
      </Slider>
    </div>
  );
}

// define the types
PayDetails.propTypes = {
  dashboardData: PropTypes.object,
};

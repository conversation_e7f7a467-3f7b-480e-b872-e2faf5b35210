import PropTypes from "prop-types";
import React from "react";
import ApplicationLoader from "../ApplicationLoader";
import Style from "./_style.module.scss";
import { Flex, Table, Typography } from "antd";
import { handleDownloadTable } from "../../constant";
import DOWNLOAD_IMG from "../../assets/Images/download.svg";

const { Text } = Typography;
export default function Blaster({ data, isLoading, dates }) {
  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      render: (_, data) => <Text>{data.key}.</Text>,
    },
    {
      title: "Bank ID",
      dataIndex: "Bank ID",
      width: 200,
      render: (value) => <Text>{value ?? "--"}</Text>,
    },
    {
      title: "Bank Name",
      dataIndex: "Bank Name",
      render: (value) => <Text>{value ?? "--"}</Text>,
    },
    {
      title: "Total Blasters",
      dataIndex: "Total Blasters",
      render: (value) => <Text>{value ?? 0}</Text>,
    },
    {
      title: "Blaster Calls Made",
      dataIndex: "Blaster Calls Made",
      render: (value) => <Text>{value ?? 0}</Text>,
    },
    {
      title: "Blaster Calls Connected",
      dataIndex: "Blaster Calls Connected",
      render: (value) => <Text>{value ?? 0}</Text>,
    },
    {
      title: "Blaster Calls Not Connected",
      dataIndex: "Blaster Calls Not Connected",
      render: (value) => <Text>{value ?? 0}</Text>,
    },
  ];

  const dataSource = Array.isArray(data)
    ? data.map((data, i) => ({
        key: i + 1,
        ...data,
      }))
    : [];

  // Handle downloading
  const handleDownload = async () => {
    if (data.length === 0) {
      return message.error("There is no data!");
    } else {
      // Get list of visible column keys
      const visibleColumns = columns.map((col) => col.dataIndex);
      // Filter only those keys from each row
      const filteredData = dataSource.map((row) => {
        const filteredRow = {};
        visibleColumns.forEach((key) => {
          filteredRow[key] = row[key];
        });
        return filteredRow;
      });
      await handleDownloadTable({
        excelName: "Blaster",
        worksheetName: "Blaster-Allocation",
        tableData: filteredData,
        fromDate: dates?.fromDate,
        toDate: dates?.toDate,
      });
    }
  };

  function getSummaryRow({ columns, dataSource }) {
    return (
      <Table.Summary fixed>
        <Table.Summary.Row>
          {columns.map((col, index) => {
            const columnTitle = col.title;

            if (index === 0) {
              return (
                <Table.Summary.Cell key={`${index}-${columnTitle}`}>
                  <Text strong>Total</Text>
                </Table.Summary.Cell>
              );
            }

            // Show "--" for Bank ID and Bank Name in summary
            if (columnTitle === "Bank ID" || columnTitle === "Bank Name") {
              return (
                <Table.Summary.Cell key={`${index}-${columnTitle}`}>
                  {"--"}
                </Table.Summary.Cell>
              );
            }

            const total = dataSource.reduce((acc, row) => {
              const value = row[col.dataIndex];
              return typeof value === "number" ? acc + value : acc;
            }, 0);

            return (
              <Table.Summary.Cell key={`${index}-${columnTitle}`}>
                {typeof total === "number" ? total : "--"}
              </Table.Summary.Cell>
            );
          })}
        </Table.Summary.Row>
      </Table.Summary>
    );
  }

  return isLoading ? (
    <ApplicationLoader />
  ) : (
    <Flex vertical gap={10}>
      <Flex justify="end">
        <button className={Style.download_button} onClick={handleDownload}>
          <img src={DOWNLOAD_IMG} alt="download-button" />
        </button>
      </Flex>
      <Table
        virtual
        className={Style.customTable}
        columns={columns}
        dataSource={dataSource}
        scroll={{
          x: 800,
          y: 350,
        }}
        pagination={{
          showSizeChanger: false,
        }}
        summary={getSummaryRow.bind(null, { columns, dataSource })}
      />
    </Flex>
  );
}

Blaster.propTypes = {
  data: PropTypes.array,
  isLoading: PropTypes.bool,
  dates: PropTypes.object,
};

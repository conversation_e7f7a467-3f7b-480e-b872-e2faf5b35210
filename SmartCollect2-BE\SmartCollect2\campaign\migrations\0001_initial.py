# Generated by Django 5.1.5 on 2025-02-18 06:54

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="BlasterTemplateMapping",
            fields=[
                (
                    "BlasterTemplateMappingID",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                ("CreatedDate", models.DateField(auto_now_add=True)),
                ("Status", models.Char<PERSON>ield(max_length=50)),
                ("TemplateName", models.CharField(max_length=255)),
                ("IsActive", models.BooleanField(default=True)),
                ("FileName", models.CharField(max_length=255)),
                ("IsAdminCreated", models.BooleanField()),
                ("RecordingURL", models.CharField(max_length=255)),
            ],
            options={
                "db_table": "BlasterTemplateMapping",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="CampaignMst",
            fields=[
                (
                    "CampaignMstID",
                    models.AutoField(
                        db_column="CampaignMstID", primary_key=True, serialize=False
                    ),
                ),
                ("Name", models.CharField(max_length=255)),
                ("IsActive", models.BooleanField(default=True)),
                ("CreatedDate", models.DateField(blank=True, null=True)),
                ("Priority", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "Status",
                    models.CharField(
                        blank=True, default="Saved", max_length=255, null=True
                    ),
                ),
                (
                    "CampaignType",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
            ],
            options={
                "db_table": "CampaignMst",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="CommFlow",
            fields=[
                (
                    "CommFlowID",
                    models.AutoField(
                        db_column="CommFlowID", primary_key=True, serialize=False
                    ),
                ),
                ("CommunicationType", models.CharField(max_length=100)),
                ("Days", models.CharField(max_length=255)),
                ("BeforeAfter", models.CharField(max_length=10)),
                ("FlowID", models.IntegerField(null=True)),
                (
                    "ExtraColumn1",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn2",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn3",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn4",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn5",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "BasedOnTable",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("BasedOn", models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                "db_table": "CommFlow",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="CommFlowMst",
            fields=[
                (
                    "CommFlowMstID",
                    models.AutoField(
                        db_column="CommFlowMstID", primary_key=True, serialize=False
                    ),
                ),
                ("ColumnName", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "BasedOnTable",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("BankName", models.CharField(max_length=255)),
                ("CreatedDate", models.DateTimeField(auto_now_add=True)),
                ("IsActive", models.BooleanField(default=True)),
                ("Type", models.CharField(max_length=50)),
                (
                    "BasedOnColumn",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("FlowName", models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                "db_table": "CommFlowMst",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="FilterMst",
            fields=[
                ("ID", models.AutoField(primary_key=True, serialize=False)),
                ("Name", models.CharField(max_length=255)),
                ("IsActive", models.BooleanField(default=True)),
            ],
            options={
                "db_table": "FilterMst",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Filters",
            fields=[
                ("FiltersID", models.AutoField(primary_key=True, serialize=False)),
                ("Column", models.CharField(max_length=255)),
                ("Comparison", models.CharField(max_length=50)),
                ("Value", models.CharField(max_length=255)),
            ],
            options={
                "db_table": "Filters",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="WhatsAppFlowMapping",
            fields=[
                ("WhatsAppFlowID", models.AutoField(primary_key=True, serialize=False)),
                ("CreatedDate", models.DateTimeField(auto_now_add=True)),
                ("FlowID", models.IntegerField()),
                ("FlowName", models.CharField(max_length=255)),
                ("Response", models.TextField()),
                ("IsActive", models.BooleanField(default=True)),
                (
                    "ExtraColumn1",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn2",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn3",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn4",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn5",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
            ],
            options={
                "db_table": "WhatsAppFlowMapping",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="WhatsAppFlowMst",
            fields=[
                (
                    "WhatsAppFlowMstID",
                    models.AutoField(
                        db_column="WhatsAppFlowMstID", primary_key=True, serialize=False
                    ),
                ),
                ("FlowName", models.CharField(max_length=255)),
                ("IsActive", models.BooleanField(default=True)),
                (
                    "ExtraColumn1",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn2",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn3",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn4",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ExtraColumn5",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
            ],
            options={
                "db_table": "WhatsAppFlowMst",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="WhatsAppTemplateMapping",
            fields=[
                (
                    "WhatsAppTemplateMappingID",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                (
                    "MetaTemplateID",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("CreatedDate", models.DateTimeField(auto_now_add=True)),
                ("Status", models.CharField(max_length=50)),
                ("TemplateName", models.CharField(max_length=255)),
                ("IsActive", models.BooleanField(default=True)),
                ("FileName", models.CharField(max_length=255)),
                ("IsAdminCreated", models.BooleanField(default=False)),
                (
                    "Extra_Column1",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "Extra_Column2",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "Extra_Column3",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "Extra_Column4",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "Extra_Column5",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
            ],
            options={
                "db_table": "WhatsAppTemplateMapping",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="WhatsAppUserTemplate",
            fields=[
                (
                    "WhatsAppUserTemplateID",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                ("TemplateName", models.CharField(max_length=255)),
                ("TemplateBody", models.TextField()),
                ("Variable1", models.CharField(blank=True, max_length=255, null=True)),
                ("Variable2", models.CharField(blank=True, max_length=255, null=True)),
                ("Variable3", models.CharField(blank=True, max_length=255, null=True)),
                ("Variable4", models.CharField(blank=True, max_length=255, null=True)),
                ("Variable5", models.CharField(blank=True, max_length=255, null=True)),
                ("Variable6", models.CharField(blank=True, max_length=255, null=True)),
                ("Variable7", models.CharField(blank=True, max_length=255, null=True)),
                ("Variable8", models.CharField(blank=True, max_length=255, null=True)),
                ("Variable9", models.CharField(blank=True, max_length=255, null=True)),
                ("Variable10", models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                "db_table": "WhatsAppUserTemplate",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="BlasterUserTemplate",
            fields=[
                (
                    "BlasterUserTemplateID",
                    models.AutoField(primary_key=True, serialize=False),
                ),
                ("TemplateName", models.CharField(max_length=255)),
                ("TemplateBody", models.TextField()),
                ("BankMstID", models.IntegerField()),
                ("EmployeeMstID", models.IntegerField()),
                ("VariableNumber", models.IntegerField()),
                ("VariableField", models.CharField(max_length=255)),
            ],
        ),
    ]

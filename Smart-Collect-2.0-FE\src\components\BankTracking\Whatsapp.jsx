import PropTypes from "prop-types";
import React from "react";
import ApplicationLoader from "../ApplicationLoader";
import Style from "./_style.module.scss";
import { Flex, Table, Typography } from "antd";
import DOWNLOAD_IMG from "../../assets/Images/download.svg";
import { handleDownloadTable } from "../../constant";

const { Text } = Typography;

export default function Whatsapp({ data, isLoading, dates }) {
  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      render: (_, data) => <Text>{data.key}.</Text>,
    },
    {
      title: "Bank ID",
      dataIndex: "Bank ID",
      width: 200,
      render: (value) => <Text>{value ?? "--"}</Text>,
    },
    {
      title: "Bank Name",
      dataIndex: "Bank Name",
      render: (value) => <Text>{value ?? "--"}</Text>,
    },
    {
      title: "Total Whatsapp",
      dataIndex: "Total WhatsApps",
      render: (value) => <Text>{value ?? 0}</Text>,
    },
    {
      title: "Messages Sent",
      dataIndex: "Messages Sent",
      render: (value) => <Text>{value ?? 0}</Text>,
    },
    {
      title: "Status",
      dataIndex: "Status",
      children: [
        {
          title: "Delivered",
          dataIndex: "Delivered",
          render: (value) => <Text>{value ?? 0}</Text>,
        },
        {
          title: "Failed",
          dataIndex: "Failed",
          render: (value) => <Text>{value ?? 0}</Text>,
        },
        {
          title: "Read",
          dataIndex: "Read",
          render: (value) => <Text>{value ?? 0}</Text>,
        },
        {
          title: "Sent",
          dataIndex: "Sent",
          render: (value) => <Text>{value ?? 0}</Text>,
        },
        {
          title: "Received",
          dataIndex: "Received",
          render: (value) => <Text>{value ?? 0}</Text>,
        },
      ],
    },
  ];

  const dataSource = Array.isArray(data)
    ? data.map((data, i) => ({
        key: i + 1,
        ...data,
      }))
    : [];
  // Handle downloading
  const handleDownload = async () => {
    if (data.length === 0) {
      return message.error("There is no data!");
    } else {
      // Get list of visible column keys
      const visibleColumns = columns.map((col) => col.dataIndex);
      // Filter only those keys from each row
      const filteredData = dataSource.map((row) => {
        const filteredRow = {};
        visibleColumns.forEach((key) => {
          filteredRow[key] = row[key];
        });
        return filteredRow;
      });
      await handleDownloadTable({
        excelName: "Whatsapp",
        worksheetName: "Whatsapp-Allocation",
        tableData: filteredData,
        fromDate: dates?.fromDate,
        toDate: dates?.toDate,
      });
    }
  };

  function getSummaryRow({ columns, dataSource }) {
    const getTotal = (key) =>
      dataSource.reduce((sum, row) => sum + (Number(row[key]) || 0), 0);
    return (
      <Table.Summary fixed>
        <Table.Summary.Row>
          <Table.Summary.Cell index={0}>Total</Table.Summary.Cell>
          <Table.Summary.Cell index={1}>--</Table.Summary.Cell>
          <Table.Summary.Cell index={2}>--</Table.Summary.Cell>
          <Table.Summary.Cell index={3}>
            <Text>{getTotal("Total WhatsApps")}</Text>
          </Table.Summary.Cell>
          <Table.Summary.Cell index={4}>
            <Text>{getTotal("Messages Sent")}</Text>
          </Table.Summary.Cell>
          <Table.Summary.Cell index={5}>
            <Text>{getTotal("Delivered")}</Text>
          </Table.Summary.Cell>
          <Table.Summary.Cell index={6}>
            <Text>{getTotal("Failed")}</Text>
          </Table.Summary.Cell>
          <Table.Summary.Cell index={7}>
            <Text>{getTotal("Read")}</Text>
          </Table.Summary.Cell>
          <Table.Summary.Cell index={8}>
            <Text>{getTotal("Sent")}</Text>
          </Table.Summary.Cell>
          <Table.Summary.Cell index={9}>
            <Text>{getTotal("Received")}</Text>
          </Table.Summary.Cell>
        </Table.Summary.Row>
      </Table.Summary>
    );
  }
  return isLoading ? (
    <ApplicationLoader />
  ) : (
    <Flex vertical gap={10}>
      <Flex justify="end">
        <button className={Style.download_button} onClick={handleDownload}>
          <img src={DOWNLOAD_IMG} alt="download-button" />
        </button>
      </Flex>
      <Table
        bordered
        virtual
        className={[Style.customTable, Style.childrenTable]}
        columns={columns}
        dataSource={dataSource}
        scroll={{
          x: 800,
          y: 350,
        }}
        pagination={{
          showSizeChanger: false,
        }}
        summary={getSummaryRow.bind(null, { columns, dataSource })}
      />
    </Flex>
  );
}

Whatsapp.propTypes = {
  data: PropTypes.array,
  isLoading: PropTypes.bool,
  dates: PropTypes.object,
};

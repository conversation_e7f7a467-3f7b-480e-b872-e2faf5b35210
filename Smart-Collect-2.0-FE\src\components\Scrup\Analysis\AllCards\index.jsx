import React from 'react';
import Style from "./_card.module.scss";
import { Col, Flex, Row, Typography } from 'antd';
import TOTAL_CUSTOMER_IMG from "../../../../assets/Images/customer.png";
import PENDING_IMG from "../../../../assets/Images/pending.png";
import COMPLETED_IMG from "../../../../assets/Images/complete.png";
import PropTypes from 'prop-types';

const {Title}  = Typography;
export default function AllCards({data, selectedAnalysisCard, handleUpdateAnalysisData}) {
  return (
    <Row gutter={[20,10]} align={'stretch'} justify={"space-between"}>
        <Col lg={6} md={12} xs={24} 
          onClick={()=> handleUpdateAnalysisData({type:"both_within_3_months_card"})}>
            <Flex 
            className={`${Style.card_container} ${
                selectedAnalysisCard === "both_within_3_months_card" ? Style.active_card : ""
              }`}
            vertical gap={20}>
                <Flex justify='space-between' align='center' style={{marginBlockStart:"1rem"}}>
                    <Title level={3} className={Style.title}>Good in XYZ Bank Paying to others</Title>
                </Flex>

                <Flex gap={3} justify='space-between' align='center'>
                    <Title level={3} className={Style.title}>
                        {data?.top_cards?.both_within_3_months_card || 0}
                    </Title>
                    <div className={Style.image_container}>
                        <img src={TOTAL_CUSTOMER_IMG} alt='customer'/>
                    </div>       
                </Flex>
            </Flex>
        </Col>
        <Col lg={6} md={12} xs={24} 
          onClick={()=> handleUpdateAnalysisData({type:"hmpl_within_3_months_card"})}>
            <Flex 
            className={`${Style.card_container} ${
                selectedAnalysisCard === "hmpl_within_3_months_card" ? Style.active_card : ""
              }`}
            vertical gap={20}>
                <Flex justify='space-between' align='center' style={{marginBlockStart:"1rem"}}>
                    <Title level={3} className={Style.title}>Good in XYZ not Paying </Title>
                </Flex>

                <Flex gap={3} justify='space-between' align='center'>
                    <Title level={3} className={Style.title}>
                        {data?.top_cards?.hmpl_within_3_months_card || 0}
                    </Title>
                    <div className={Style.image_container}>
                        <img src={PENDING_IMG} alt='customer'/>
                    </div>       
                </Flex>
            </Flex>
        </Col>
        <Col lg={6} md={12} xs={24} 
          onClick={()=> handleUpdateAnalysisData({type:"neither_within_3_months_card"})}>
            <Flex 
            className={`${Style.card_container} ${
                selectedAnalysisCard === "neither_within_3_months_card" ? Style.active_card : ""
            }`} 
            vertical gap={20}>
                <Flex justify='space-between' align='center' style={{marginBlockStart:"1rem"}}>
                    <Title level={3} className={Style.title}>Default in XYZ Bank Paying to Others </Title>
                </Flex>

                <Flex gap={3} justify='space-between' align='center'>
                    <Title level={3} className={Style.title}>
                        {data?.top_cards?.neither_within_3_months_card || 0}
                    </Title>
                    <div className={Style.image_container}>
                        <img src={COMPLETED_IMG} alt='customer'/>
                    </div>       
                </Flex>
            </Flex>
        </Col>
        <Col lg={6} md={12} xs={24}
          onClick={()=> handleUpdateAnalysisData({type:"other_mfi_within_3_months_card"})}>
            <Flex 
            className={`${Style.card_container} ${
                selectedAnalysisCard === "other_mfi_within_3_months_card" ? Style.active_card : ""
            }`} 
            vertical gap={20}>
                <Flex justify='space-between' align='center' style={{marginBlockStart:"1rem"}}>
                    <Title level={3} className={Style.title}>Default in XYZ Bank not Paying to Others </Title>
                </Flex>

                <Flex gap={3} justify='space-between' align='center'>
                    <Title level={3} className={Style.title}>
                        {data?.top_cards?.other_mfi_within_3_months_card || 0}
                    </Title>
                    <div className={Style.image_container}>
                        <img src={COMPLETED_IMG} alt='customer'/>
                    </div>       
                </Flex>
            </Flex>
        </Col>
   </Row>
  )
}

AllCards.propTypes={
    data: PropTypes.object, 
    selectedAnalysisCard: PropTypes.string, 
    handleUpdateAnalysisData: PropTypes.func
}
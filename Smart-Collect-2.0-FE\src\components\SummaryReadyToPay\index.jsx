import { Flex } from "antd";
import React, { useEffect, useState } from "react";
import AppHeader from "../AppHeader";
import AppEngagements from "../AppEngagements";
import CustomersTable from "../CustomersTable";
import CustomerAllCards from "../CustomerAllCards";
import {
  ALL_CACHE_NAMES,
  LOCAL_KEYS,
  SMART_COLLECT_MENU_IDS,
  SUMMARY_FILTERS,
} from "../../constant";
import Style from "./_style.module.scss";
import { AXIOS } from "../../apis/ho-Instance";
import ApplicationLoader from "../ApplicationLoader";
import {
  getCacheKey,
  getFromCache,
  getPostCacheKey,
  storeToCache,
} from "../../utils/cacheHelper";
import { encryptBody, isSameEncryptedBody } from "../../utils/crypto";

const CACHE_NAME = ALL_CACHE_NAMES.CAMPAIGN_SUMMARY_DASHBOARD;

export default function SummaryReadyToPay() {
  const [isSwitchOn, setIsSwitchOn] = useState(false);
  const [customerData, setCustomerData] = useState([]);
  const [loading, setLoading] = useState(false);
  // Retrieve dates from localStorage
  const storedFilter = localStorage.getItem(SUMMARY_FILTERS.summaryFilter);
  const storedCampaign = localStorage.getItem(
    SUMMARY_FILTERS.selectedCampaignId
  );

  const handleFetchCampsAndBranchIds = async () => {
    try {
      const storedCampaigns = localStorage.getItem(
        SUMMARY_FILTERS.summaryCampaignIds
      );
      const storedBranches = localStorage.getItem(
        SUMMARY_FILTERS.summaryBranchIds
      );

      const campaignIds = storedCampaigns ? JSON.parse(storedCampaigns) : [];
      const branchIds = storedBranches ? JSON.parse(storedBranches) : [];

      return { campaignIds, branchIds };
    } catch (error) {
      console.error(
        "Error reading campaign/branch IDs from localStorage",
        error
      );
      return { campaignIds: [], branchIds: [] };
    }
  };

  const handleGetPromiseData = async () => {
    setLoading(true);
    const cacheKey = getCacheKey({ endpoint: "campaignpromise/" });
    // Check cache
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setCustomerData(cachedData);
      setLoading(false);
    }

    // get all campaign ids & branch ids
    const { campaignIds, branchIds } = await handleFetchCampsAndBranchIds();
    try {
      const response = await AXIOS.get("campaignpromise/", {
        params: {
          campaign_mstids: campaignIds,
          branch_id: branchIds,
        },
      });
      if (response.status === 200) {
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data.data,
        });
        setCustomerData(response.data.data);
      }
    } catch (error) {
      console.log("Error in Ready to pay", error?.message);
    } finally {
      setLoading(false);
    }
  };

  const handleReadytoPay = async ({
    fromDate,
    toDate,
    BranchName,
    disbursementID,
    campaignMstIds,
  }) => {
    setLoading(true);
    const { campaignIds, branchIds } = await handleFetchCampsAndBranchIds();
    const body = {
      from_date: fromDate,
      to_date: toDate,
      branch_id: BranchName?.length ? BranchName : branchIds,
      dpdfrom: 0,
      dpdto: 10000,
      disbursementids: disbursementID,
      campaign_mstids: campaignMstIds ? [campaignMstIds] : campaignIds,
    };

    const cacheKey = getPostCacheKey({ endpoint: "campaignpromise/", body });
    const encryptedOldBody = localStorage.getItem(
      LOCAL_KEYS.SUMMARY_PAY_FILTER
    );
    const sameBody = isSameEncryptedBody({
      newBody: body,
      encryptedOldBody,
    });

    if (sameBody) {
      const cachedData = await getFromCache({
        cacheName: CACHE_NAME,
        key: cacheKey,
      });
      if (cachedData) {
        setCustomerData(cachedData);
        setLoading(false);
      }
    }
    try {
      const response = await AXIOS.post("campaignpromise/", body);
      // Check the response
      if (response.status === 200) {
        const encrypted = encryptBody(body);
        if (encrypted) {
          localStorage.setItem(LOCAL_KEYS.SUMMARY_FILTER, encrypted);
        }
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data?.data,
        });
        setCustomerData(response.data?.data);
      }
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoading(false);
    }
  };

  // Apply filter and fetch data
  const applyFilters = async ({
    fromDate,
    toDate,
    BranchName,
    disbursementID,
  }) => {
    await handleReadytoPay({
      fromDate: fromDate,
      toDate: toDate,
      BranchName,
      disbursementID,
      campaignMstIds: parseInt(storedCampaign),
    });
  };

  // Clear filters and fetch data
  const ClearFilters = () => {
    handleGetPromiseData();
    localStorage.removeItem(SUMMARY_FILTERS.summaryFilter);
    localStorage.removeItem(LOCAL_KEYS.SUMMARY_PAY_FILTER);
  };

  useEffect(() => {
    if (storedFilter || storedCampaign) {
      const { fromDate, toDate, BranchName, disbursementID } = storedFilter
        ? JSON.parse(storedFilter)
        : {
            fromDate: null,
            toDate: null,
            BranchName: [],
          };
      const parsedCamp = storedCampaign ? parseInt(storedCampaign) : null;
      handleReadytoPay({
        fromDate,
        toDate,
        BranchName,
        disbursementID,
        campaignMstIds: parsedCamp,
      });
    } else {
      handleGetPromiseData();
    }
  }, []);

  return (
    <Flex gap={15} vertical className={Style.pay_container}>
      {/* Header */}
      <AppHeader
        title={`Promised to Pay: ${customerData?.length ?? 0}`}
        isDashboard={false}
        isSwitchOn={isSwitchOn}
        setIsSwitchOn={setIsSwitchOn}
        isDashboardOtherPages={true}
        applyFilters={applyFilters}
        ClearFilters={ClearFilters}
      />

      <Flex vertical gap={15}>
        {/* app engagements */}
        <AppEngagements />
        {/* Details */}
        {loading ? (
          <ApplicationLoader />
        ) : (
          <div className={Style.details}>
            {!isSwitchOn ? (
              <CustomerAllCards
                pageId={SMART_COLLECT_MENU_IDS.READY_TO_PAY}
                customerData={customerData}
                modalButtonText={"Feedback "}
                isModal={true}
              />
            ) : (
              <CustomersTable
                title={"Promised to pay"}
                customerData={customerData}
                pageId={SMART_COLLECT_MENU_IDS.READY_TO_PAY}
                modalButtonText={"Feedback "}
              />
            )}
          </div>
        )}
      </Flex>
    </Flex>
  );
}

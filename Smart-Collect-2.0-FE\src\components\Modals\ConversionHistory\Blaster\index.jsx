import { Flex, Typography } from "antd";
import AI_IMG from "../../../../assets/Images/blaster.svg";
import React, { forwardRef, useImperativeHandle, useRef } from "react";
import Style from "./_style.module.scss";
import { handleDateFormatter } from "../../../../constant";
import PropTypes from "prop-types";

const { Text } = Typography;

export const Blaster = forwardRef(({ blasterCallData }, ref) => {
  const audioRefs = useRef([]);

  // Expose a method to stop all audio when modal closes
  useImperativeHandle(ref, () => ({
    stopAllAudio: () => {
      audioRefs.current.forEach((audio) => {
        if (audio) {
          audio.pause();
          audio.currentTime = 0; // Reset to beginning
        }
      });
    },
  }));

  // Grouping AI calls by conversation_date
  const groupedCalls = blasterCallData.reduce((acc, call) => {
    const { conversation_date, person } = call;
    if (!acc[conversation_date]) {
      acc[conversation_date] = {
        blasters: [],
        person,
      };
    }
    acc[conversation_date].blasters.push(call);
    return acc;
  }, {});
  return (
    <Flex className={Style.ai_container} vertical gap={20}>
      {Object.entries(groupedCalls).map(([date, calls]) => {
        const modifyPersonSpell =
          calls?.person?.toLowerCase() === "Gaurantator"
            ? "Guarantor"
            : calls?.person;
        return (
          <Flex key={date} vertical gap={10} className={Style.group_container}>
            {/* Show Date Once */}
            <Flex justify="center">
              <Text className={Style.date}>{handleDateFormatter(date)}</Text>
            </Flex>

            {/* Show AI Call Heading Once */}
            <Flex className={Style.ai_chat_box} vertical gap={10}>
              <Flex vertical>
                <Flex justify="center" gap={5} align="baseline">
                  <img src={AI_IMG} className={Style.img} alt="AI Icon" />
                  <Text className={Style.ai_text}>Blaster Call</Text>
                </Flex>
                <Flex justify="center">
                  <Text className={Style.belongs_text}>
                    ({modifyPersonSpell || "Unknown"})
                  </Text>
                </Flex>
              </Flex>

              {/* List all recordings for that date */}
              <Flex className={Style.response_container} vertical gap={5}>
                {calls.blasters.map((call, index) => {
                  return (
                    <Flex key={`${index}-${call.recording}`} justify="center">
                      {call.recording ? (
                        <audio
                          ref={(el) => (audioRefs.current[index] = el)}
                          controls
                        >
                          <source src={call.recording} type="audio/ogg" />
                          <track
                            kind="captions"
                            label="No captions available"
                          />
                        </audio>
                      ) : (
                        <Text className={Style.response_text}>
                          Call Attempted, Customer Did Not Pick Up
                        </Text>
                      )}
                    </Flex>
                  );
                })}
              </Flex>
            </Flex>
          </Flex>
        );
      })}
    </Flex>
  );
});

Blaster.propTypes = {
  blasterCallData: PropTypes.array,
};

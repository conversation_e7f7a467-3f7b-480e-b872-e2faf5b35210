def get_arthasidhi_recovery_columns():
        """Get required columns for Arthasidhi recovery file type"""
        return [
            'Member No', '<PERSON>an No', 'Member', 'Loan Type', 'Loan Amount', 
            'EMI Amount', 'Loan Date', 'Collection Amount', 'POS', 
            'Received Date', 'Repayment Frequency', 'Principle Collection Amount', 
            'Interest Collection Amount', 'Receipt No', 'Branch Name'
        ]
    
def get_arthasidhi_od_columns():
    """Get required columns for Arthasidhi overdue file type"""
    return [
        'MemberNo', 'LoanNo', 'MemberName', 'Mobile No.', 'OdDays', 
        'LoanDt', 'LoanAmt', 'Scheme', 'LastCollDt', 'LastCollAmt', 
        'ODAmount', 'EMI Amount', 'Branch'
    ]

def get_arthasidhi_demand_columns():
    """Get required columns for Arthasidhi demand file type"""
    return [
        'Member No', 'Loan No', 'Member', 'Mobile Number', 'Loan Date', 
        'Loan Amount', 'Loan Scheme', 'Branch', 'Demand Date', 
        'Savings Demand Amount', 'Total All Demand', 'EMI Amount'
    ]

def get_arthasiddhi_od_sheet_columns():
     return [
                    'Demand Date', 'Member', 'Contact No', 'Last Receive Date',
                    'Last Receive Amount', 'Loan No', 'EMI Amount', 'Total All Demand',
                    'POS', 'Loan Scheme', 'Loan Date', 'Loan Amount', 'Branch', 'Interest Rate'
                ]

def get_pavan_over_due_columns():
    """Get required columns for Pavana loan file type"""
    return [
        'Main Cust No','Ac No','Prd Cd','Disbursed Date','Expiry Date','Instal Start Date','Report Date','Br Name','Br Name','Mobile No','Mobile No2',
        'Guarantor Tel No1','Guarantor Tel No2','Guarantor Tel No3','Sanction Amount','Interest Rate','Instal Amount','Int Overdue','Total OVerdue','Ln Inst Freq','Over Due Age',

    ]

def get_vinayana_based_data_columns():
    """ Get required columns for vinayana loan file type"""
    return ["CustomerID","DisbursementID","BranchID","BranchName","LoanType","CustomerName","Mobile Number","OD AMOUNT","TOTAL OUTSTANDING",
            "Next_EMI_Amount","Next_EMI_Date","End Payment Date","DPD"
            ]

def get_warna_loan_data_columns():
     """ Get required columns for Warna loan file type"""
     return [
          "customer_id","disbursement_id","account_type","disbursed_dt","date_closed","date_of_last_payment","date_reported",
          "ins_start_dt","npa_dt","cc_review_limit_expire","loan_sanction_limit","disbursed_amount","rate_of_interest","repayment_tenure",
          "emi_amount","latest_collected_amt","amount_overdue","no_of_days_past_dues","payment_frequency","loanclassification"
     ]

def get_warna_gurantor_columns():
     return [
          "disbursement_id","guarantor_name","guarantor_contact","relation"
     ]

def get_warna_negative_columns():
     return [
          'disbursementid','Reply Message'
     ]

def get_warna_customer_columns():
     return [
          "customer_name","mobile","customer_id","branch_cd","branch_nm"
     ]
def get_ghoti_loan_data_columns():
     return [
          "LOAN CODE","LOAN TYPE NAME","BR CODE","BR NAME"
     ]

def get_ghoti_od_data_columns():
     return [
       "BR", "Br Name", "AH", "LOAN TYPE", "AC", "NAME", "SAN AMT", "SAN DATE", "INSTL AMT", "OVERDUE", "BALANCE", "MOBILE"
    ]

def get_shakti_finance_data_columns():
     return [
          "CustomerID","DisbursementID","LoanType","CustomerName","EMIAmount","NumberOfDaysPastDue","MobileNo.","BranchName","BranchCode","OverdueAmount","Live / Expired","NextEMIDate yyyy-mm-dd"
     ]

def get_sindhudhurg_data_columns():
     return [
          "CUSTOMERNAME","CUSTOMERID","DISBURSEMENTID","MOBILENO","EMIAMOUNT","PAYMENTFREEQUENCY","NUMBEROFDAYSPASTDUE","INSTSTARTDATE","BRANCHNAME","BRANCHCODE","APPLICANTTYPE","OVERDUEAMOUNT","TOTALOUTSTANDING","SANCTIONAMOUNT"
     ]

def get_bss_bihar_data_columns():
     return [
         "branchcode","branchname","customerid","customername","mobileno","disbursementid","emiamount","disbursementamount","disbursementdate","closingdate","overdueamount",
          "numberofdayspastdue","pending_emis","nextemidate","lastpaymentdate","loantype","extracolumn1"
     ]

def get_bss_bihar_collection_data_columns():
     return [
          "branchcode","branchname","customerid","customername","mobileno","disbursementid","emiamount","disbursementamount","disbursementdate","closingdate","overdueamount",
          "numberofdayspastdue","pending_emis","nextemidate","lastpaymentdate","loantype","extracolumn1"
     ]

def get_rajapur_collection_data_columns():
     return [
          "Account_Name","CUST_CODE","DisbursementID","Scheme_Description","MOBILE_NO","Disbursements_AMOUNT","Interest_rate_applied","installment_EMI_amount",
          "PaymentFreequency","NumberOfDaysPastDue","INSTSTARTDATE","Branch_Code","Branch_Name","ApplicantType",
          "OverdueAmount","Total_Outstanding","princple_cr","int_cr"
     ]

def get_shareindia_data_columns():
     return [
          "CustomerID","DisbursementID","LoanType","CustomerName","DisbursementAmount","Rateofinterest","RepaymentTenure","EMIAmount","PaymentFreequency","NumberOfDaysPastDue",
          "MobileNo.","InstStartDate","CollectionOfficerID","CollectionOfficerName","Loan Officer Contact No","BranchName","BranchCode","ApplicantType",
          "OverdueAmount","Total Outstanding","CollectedAmount","CollectionDate","POS","Primary Language"
     ]

def get_shrimantmalojiraje_data_columns():
     return [
          "acct_nm","customer_id","type_nm","contact2","acct_cd","ins_start_dt",
          "installment_type","branch_nm","branch_cd","days_past_due","overdue",
          "name_of_gurantor","pri_outstanding","int_outstanding","sanctioned_amt",
          "os_balance","inst_rs","inst_no"
     ]

def get_swami_smarth_base_data_columns():
     return [
          "Client  id","GL_Code","Acc_no","branch_id","Branch Name","name","mobile"
     ]

def get_swami_smarth_daily_data_columns():
     return [
          'Branch_Id',"Branch_Name","Customer_Id","Loan_GL_No","Loan_Acc_No","Loan_Type","Collected_Amt","Collection _Date","Current_Balance"
     ]

def get_tulja_data_columns():
     return [
          'GLHead','Customer Name','CustomerId','DisbursementID','LoanType','Mobile No',"DisburseAmount","RateOfInterest","RepaymentTenure","EMIAMOUNT",
          "PaymentFrequency","NumberOfDaysPastDue","InstStartDate","CollectionOfficerID","CollectionOfficerName","BranchId","AmountOverdue","CurrentBalance",
          "PrincipleCollected","InterestCollected",'LatestCollectedAmount','DateofLastPayment','Account Status','DateOpen','GLHead'
     ]
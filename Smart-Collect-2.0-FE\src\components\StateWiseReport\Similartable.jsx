import { Flex, Table, Typography } from "antd";
import React, { useEffect } from "react";
import Style from "./_style.module.scss";
import PropTypes from "prop-types";

const { Text } = Typography;

export default function SimilarTable({ data }) {
  const [tableData, setTableData] = React.useState([]);

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      render: () => <Text>1</Text>,
    },
    {
      title: "Name",
      dataIndex: "name",
      render: (value) => <Text>{value !== null ? value : "-"}</Text>,
    },
    {
      title: "Address",
      dataIndex: "address",
      render: (value) => <Text>{value !== null ? value : "-"}</Text>,
    },
    {
      title: "State",
      dataIndex: "Folder_Name",
      render: (value) => <Text>{value ?? "-"}</Text>,
    },
    {
      title: "Phone",
      dataIndex: "phone_number",
      render: (value) => <Text>{value ?? "-"}</Text>,
    },
  ];

  const dataSource = Array.isArray(tableData)
    ? tableData.map((data, i) => ({ key: i + 1, ...data }))
    : [];

  // Set te data
  useEffect(() => {
    setTableData(data);
  }, [data]);

  return (
    <Flex vertical gap={15}>
      <Text className={Style.title}>Similar Phone Numbers:</Text>
      {/* Table */}
      <Table
        bordered
        virtual
        className={[Style.customTable, Style.details_table]}
        columns={columns}
        dataSource={dataSource}
        scroll={{
          x: 1000,
          y: 220,
        }}
        pagination={{
          pageSize: 10,
          showSizeChanger: false,
        }}
      />
    </Flex>
  );
}

SimilarTable.propTypes = {
  data: PropTypes.array,
};

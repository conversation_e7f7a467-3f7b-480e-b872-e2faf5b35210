import { Button, Flex } from 'antd';
import Style from "../_style.module.scss";
import PropTypes from 'prop-types';

export function PayDetailsTour({handleNext, handleSkip}) {
  return (
    <Flex vertical gap={30}>
        description 
        <Flex justify='end' gap={10}>
            <Button className={Style.skip_btn} onClick={handleSkip}>Finish</Button>
        </Flex>
    </Flex>
  )
}

// Define the props
PayDetailsTour.propTypes = {
  handleNext: PropTypes.func.isRequired,
  handleSkip: PropTypes.func.isRequired,
};
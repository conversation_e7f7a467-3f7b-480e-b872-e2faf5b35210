import pandas as pd
import numpy as np
from datetime import datetime, date
import threading
from ..utils import clean_mobile_number,clean_column
from ..queries import insert_data_in_raw_table
from ..sp import execute_post_processing_procedures

def find_latest_datetime_column(df):
    """
    Find the latest datetime column when columns are datetime strings
    """
    datetime_columns = []
    
    # Check each column to see if it can be parsed as datetime
    for col in df.columns:
        # print(col, "checking_column")
        
        # Try to parse string column names as datetime
        if isinstance(col, str):
            try:
                # Parse the datetime string
                parsed_date = datetime.strptime(col, '%Y-%m-%d %H:%M:%S')
                # print(col, "parsed_successfully")
                
                # Validate it's a reasonable date
                if 2020 <= parsed_date.year <= 2030:
                    datetime_columns.append((col, parsed_date))
                    
            except ValueError:
                # Not a datetime string, skip
                continue
        elif isinstance(col, datetime):
            # Already a datetime object
            if 2020 <= col.year <= 2030:
                datetime_columns.append((col, col))
    
    # print(datetime_columns, "found_datetime_columns")
    
    if not datetime_columns:
        print("No datetime columns found")
        return None, None
    
    # Find the latest date by comparing the parsed datetime objects
    latest_col, latest_date = max(datetime_columns, key=lambda x: x[1])
    print(f"Latest datetime column: {latest_col} ({latest_date.strftime('%Y-%m-%d')})")
    
    return latest_col, latest_date

def run_post_processing_in_background(bank_id):
    """
    Run post-processing procedures in a background thread
    """
    try:
        print(f"Starting background post-processing for bank_id: {bank_id}")
        execute_post_processing_procedures(bank_id)
        print(f"Background post-processing initiated for bank_id: {bank_id}")
    except Exception as e:
        print(f"Error starting background post-processing for bank_id {bank_id}: {str(e)}")

def debug_dataframe_dtypes(df, label="DataFrame"):
    """
    Debug function to print all date-related columns and their types
    """
    print(f"\n=== {label} DEBUG INFO ===")
    for col in df.columns:
        if any(date_word in col.lower() for date_word in ['date', 'time', 'inserted']):
            sample_val = df[col].iloc[0] if len(df) > 0 else None
            print(f"  {col}: dtype={df[col].dtype}, type={type(sample_val)}, sample={sample_val}")
    print("=== END DEBUG INFO ===\n")

def process_vinayana_raw_data(df1, bank_id):
    db_operation_success = False
    db_error_message = None
    df = pd.DataFrame()
    
    try:
        print("STARTING VINAYANA DATA PROCESSING WITH ENHANCED DEBUGGING")
        
        # Standard column mappings
        df['customerid'] = df1['CustomerID'].astype(str).str.replace(r"['-]", '', regex=True)
        df['disbursementid'] = df1['DisbursementID'].astype(str).str.replace(r"['-]", '', regex=True)
        df['branchcode'] = df1['BranchID'].astype(str).str.replace('.0', '', regex=False)
        
        # ========== ENHANCED BRANCH NAME NORMALIZATION ==========
        # Remove extra spaces, convert to title case for consistency
        df['branchname'] = (df1['BranchName']
                           .astype(str)
                           .str.strip()                    # Remove leading/trailing spaces
                           .str.replace(r'\s+', '', regex=True)   # Remove ALL spaces
                           .str.title())
        
        df['loantype'] = df1['LoanType']
        df['customername'] = df1['CustomerName']
        df['mobileno'] = df1['Mobile Number'].apply(clean_mobile_number)
        df['overdueamount'] = df1['OD AMOUNT']
        df['totaloutstanding'] = df1['TOTAL OUTSTANDING']
        df['emiamount'] = df1['Next_EMI_Amount']
        
        # ========== CAREFUL DATE HANDLING FOR NEXTEMIDATE ==========
        print("Processing nextemidate field...")
        try:
            next_emi_raw = df1['Next_EMI_Date']
            print(f"DEBUG: Next_EMI_Date sample values: {next_emi_raw.head().tolist()}")
            print(f"DEBUG: Next_EMI_Date dtype: {next_emi_raw.dtype}")
            
            # Convert to date objects carefully
            def safe_date_convert(date_val):
                if pd.isna(date_val) or date_val is None or date_val == '':
                    return None
                if isinstance(date_val, datetime):
                    return date_val.date()
                if isinstance(date_val, date):
                    return date_val
                if isinstance(date_val, str):
                    try:
                        parsed = pd.to_datetime(date_val, errors='coerce')
                        return parsed.date() if not pd.isna(parsed) else None
                    except:
                        return None
                try:
                    parsed = pd.to_datetime(date_val, errors='coerce')
                    return parsed.date() if not pd.isna(parsed) else None
                except:
                    return None
            
            df['nextemidate'] = next_emi_raw.apply(safe_date_convert)
            print(f"DEBUG: Processed nextemidate sample: {df['nextemidate'].head().tolist()}")
            
        except Exception as e:
            print(f"Error processing nextemidate: {e}")
            df['nextemidate'] = None
        
        # ========== CAREFUL DATE HANDLING FOR OTHER DATE FIELDS ==========
        try:
            end_payment_raw = df1['End Payment Date']
            df['extracolumn1'] = end_payment_raw.apply(safe_date_convert)
        except:
            df['extracolumn1'] = None
            
        df['numberofdayspastdue'] = pd.to_numeric(df1['DPD'], errors='coerce').fillna(0).astype('int64')
        df['originaldisbursementid'] = df1['DisbursementID']
        
        frequency_map = {
            'DAILY LOAN': 1,
            'WEEKLY LOAN': 7,
            'MONTHLY LOAN': 30
        }
        df['paymentfrequency'] = df1['LoanType'].str.upper().map(frequency_map).astype('Int64')
        
        # ========== SUPER CAREFUL INSERTED_DATE HANDLING ==========
        today = date.today()  # Use date.today() instead of datetime.now().date()
        df['inserted_date'] = today
        print(f"DEBUG: inserted_date set to: {today} (type: {type(today)})")
        
        df['bankmstid'] = 23
        
        # ========== DATETIME COLUMN HANDLING (SUPER CAREFUL) ==========
        latest_column, latest_date = find_latest_datetime_column(df1)
        
        if latest_column is not None:
            print(f"Using datetime column: {latest_column} for date: {latest_date.strftime('%Y-%m-%d')}")
            
            # Access the column using the datetime object directly
            try:
                df['collectedamount'] = df1[latest_column]
                
                # SUPER CAREFUL: Convert to date object
                collection_date = latest_date.date()
                df['collectiondate'] = collection_date
                print(f"DEBUG: collectiondate set to: {collection_date} (type: {type(collection_date)})")
                
                print(f"Successfully mapped {len(df)} records from datetime column {latest_date.strftime('%Y-%m-%d')}")
                
            except KeyError as e:
                print(f"Error accessing datetime column {latest_column}: {e}")
                # Fallback
                df['collectedamount'] = 0
                df['collectiondate'] = None
                
        else:
            print("No datetime columns found")
            df['collectedamount'] = 0
            df['collectiondate'] = None
        
        # ========== COMPREHENSIVE DATETIME DEBUGGING ==========
        debug_dataframe_dtypes(df, "PROCESSED DATAFRAME")
        
        # Check for any remaining datetime objects in the entire dataframe
        print("\n=== SCANNING FOR DATETIME OBJECTS ===")
        for col in df.columns:
            sample_values = df[col].head(3).tolist()
            for i, val in enumerate(sample_values):
                if isinstance(val, datetime):
                    print(f"WARNING: Found datetime object in column '{col}' at index {i}: {val}")
                    # Convert to date if it's a datetime
                    df[col] = df[col].apply(lambda x: x.date() if isinstance(x, datetime) else x)
                    print(f"FIXED: Converted column '{col}' datetime objects to dates")
        print("=== END DATETIME SCAN ===\n")
        
        # ========== DEBUG: Show branch name normalization results ==========
        if not df1['BranchName'].empty:
            print("Branch name normalization:")
            unique_original = df1['BranchName'].unique()
            unique_normalized = df['branchname'].unique()
            for orig, norm in zip(unique_original, unique_normalized):
                if orig != norm:
                    print(f"  '{orig}' -> '{norm}'")
        
        # Final check: Print first row of all date-related columns
        print("\n=== FINAL DATE COLUMNS CHECK ===")
        date_related_cols = [col for col in df.columns if any(word in col.lower() for word in ['date', 'time', 'inserted'])]
        if date_related_cols:
            first_row_dates = df[date_related_cols].iloc[0].to_dict()
            for col, val in first_row_dates.items():
                print(f"  {col}: {val} (type: {type(val)})")
        print("=== END FINAL CHECK ===\n")
        
        # Save to CSV for debugging
        # df.to_csv("vinayana_debug_output.csv", index=False)
        # print("DEBUG: Dataframe saved to 'vinayana_debug_output.csv'")
        
    except Exception as e:
        print(f"Error during data processing: {str(e)}")
        return {
            "Total records processed": 0,
            "New records inserted": 0,
            "Duplicate records found": 0,
            "db_operation_success": False,
            "db_error_message": f"Data processing error: {str(e)}",
            "message": f"Data processing failed: {str(e)}",
            "latest_collection_date": None,
            "latest_collection_date_original": None,
            "post_processing_initiated": False,
            "status": "error"  # Added proper status
        }
    
    # Database operation
    try:
        print("ATTEMPTING DATABASE INSERTION...")
        result = insert_data_in_raw_table(df, bank_id=23)
        db_operation_success = result.get('success', False)
        
        if not db_operation_success:
            db_error_message = result.get('message', 'Unknown database error')
            print(f"Database insertion failed: {db_error_message}")
        else:
            print(f"Database insertion successful: {result.get('message', 'Data inserted successfully')}")
            
    except Exception as e:
        db_operation_success = False
        db_error_message = str(e)
        print(f"Database update failed for vinayana data: {e}")
        result = {'inserted_count': 0, 'duplicate_count': 0, 'message': str(e)}
    
    # ========== START BACKGROUND POST-PROCESSING ==========
    if db_operation_success:
        try:
            # Start post-processing in background thread
            background_thread = threading.Thread(
                target=run_post_processing_in_background,
                args=(bank_id,),
                daemon=True
            )
            background_thread.start()
            print(f"Post-processing started in background thread for bank_id: {bank_id}")
        except Exception as e:
            print(f"Warning: Could not start background post-processing: {str(e)}")
    else:
        print(f"Skipping post-processing due to database operation failure: {db_error_message}")
    
    # ========== RETURN RESPONSE WITH PROPER STATUS ==========
    return {
        "Total records processed": len(df) if not df.empty else 0,
        "New records inserted": result.get('inserted_count', 0) if db_operation_success else 0,
        "Duplicate records found": result.get('duplicate_count', 0) if db_operation_success else 0,
        "db_operation_success": db_operation_success,
        "db_error_message": db_error_message,
        "message": result.get('message', 'Operation completed') if db_operation_success else f"Failed to insert data for Vinaya Finance: {db_error_message}",
        "latest_collection_date": latest_date.strftime('%Y-%m-%d') if latest_date else None,
        "latest_collection_date_original": latest_date.strftime('%d/%m/%Y') if latest_date else None,
        "post_processing_initiated": db_operation_success,
        "status": "success" if db_operation_success else "error"  # Fixed: Proper status based on operation success
    }
import { Flex } from 'antd'
import React from 'react'
import Style from "./_already-button.module.scss";

export default function AlreadyPayButtons() {
    const handleMarkPaymentDone =()=>{};
    const handleMarkAsFalseClaim =()=>{};

  return (
    <Flex justify='space-between' align='center' gap={20} className={Style.button_container}>
        <button onClick={handleMarkPaymentDone}>Mark Payment as done</button>
        <button onClick={handleMarkAsFalseClaim}>Mark as a false claim</button>
    </Flex>
  )
}

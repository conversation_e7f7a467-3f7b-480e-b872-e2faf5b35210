import pandas as pd
import datetime as datetime

# Function to create records with guarantors
def create_rawfile_records(df, bank_id):
    rawfile_records = []
    
    for index, row in df.iterrows():
        # Create base record data
        base_record = {
            'customerid': str(row['Main Cust No']),
            'disbursementid': row['DisbursementID'],
            'loantype': str(row['Prd Cd']),
            'customername': str(row['Long Name']) if pd.notna(row['Long Name']) else None,
            'disbursementamount': row['Sanction Amount'],
            'rateofinterest': row['Interest Rate'],
            'repaymenttenure': row['RepaymentTenure'],
            'emiamount': row['Instal Amount'],
            'paymentfrequency': row['PaymentFrequency'],
            'numberofdayspastdue': row['NumberOfDaysPastDue'],
            'mobileno': row['Mobile No'],
            'inststartdate': row['Instal Start Date'],
            'collectionofficerid': None,
            'collectionofficername': None,
            'branchname': row['BranchName'],
            'branchcode': str(row['BranchCode']),
            'applicanttype': None,
            'overdueamount': row['Total OVerdue'],
            'totaloutstanding': None,
            'principlecollected': None,
            'interestcollected': None,
            'collectedamount': row['collectedamount'],
            'collectiondate': row['collectiondate'],
            'pos': None,
            'bankmstid': bank_id,
            'inserted_date': datetime.datetime.now().date(),
            'gender': None,
            'dateofbirth': None,
            'disbursementdate': row['Disbursed Date'],
            'loanclassification': None,
            'lastpaymentdate': None,
            'lastcollectedamount': None,
            'currentbalance': None,
            'interestoutstanding': row['Int Overdue'],
            'totalpending': None,
            'principlepending': None,
            'interestpending': None,
            'closingdate': row['Expiry Date'],
            'previousemidate': None,
            'guarantor': None,
            'guarantor_mobile': None,
            'originaldisbursementid':str(row['originaldisbursementid']),
            'secondary_user_type': None
        }
        
        # Collect guarantor information
        guarantors = []
        
        # Check Guarantor1
        if (pd.notna(row['Guarantor1']) and str(row['Guarantor1']).strip() and 
            pd.notna(row['Guarantor Tel No1']) and 
            row['Guarantor Tel No1'] is not None and len(str(row['Guarantor Tel No1'])) >= 10):
            guarantors.append({
                'name': str(row['Guarantor1']).strip(),
                'mobile': str(row['Guarantor Tel No1'])
            })
        
        # Check Guarantor2
        if (pd.notna(row['Guarantor2']) and str(row['Guarantor2']).strip() and 
            pd.notna(row['Guarantor Tel No2']) and 
            row['Guarantor Tel No2'] is not None and len(str(row['Guarantor Tel No2'])) >= 10):
            guarantors.append({
                'name': str(row['Guarantor2']).strip(),
                'mobile': str(row['Guarantor Tel No2'])
            })
        
        # Check Guarantor3
        if (pd.notna(row['Guarantor3']) and str(row['Guarantor3']).strip() and 
            pd.notna(row['Guarantor Tel No3']) and 
            row['Guarantor Tel No3'] is not None and len(str(row['Guarantor Tel No3'])) >= 10):
            guarantors.append({
                'name': str(row['Guarantor3']).strip(),
                'mobile': str(row['Guarantor Tel No3'])
            })
        
        # If no valid guarantors, create one record without guarantor info
        if not guarantors:
            rawfile_records.append(base_record.copy())
        else:
            # Create separate records for each guarantor
            for guarantor in guarantors:
                record = base_record.copy()
                record['guarantor'] = guarantor['name']
                record['guarantor_mobile'] = guarantor['mobile']
                record['secondary_user_type'] = 'guarantor'  # Set to 'guarantor' for guarantor records
                rawfile_records.append(record)
    rawfile_records = pd.DataFrame(rawfile_records)
    # rawfile_records.to_csv("Pavana.csv")
    return rawfile_records
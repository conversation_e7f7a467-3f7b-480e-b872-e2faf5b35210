import React, { useEffect, useState } from "react";
import { Col, Flex, Row } from "antd";
import {
  FIELD_OFFICER_LANDING_PAGE_DATA,
  HO_LANDING_PAGE_DATA,
  SMART_COLLECT_MENU_IDS,
} from "../../constant";
import Style from "./_landing-screen.module.scss";
import { LandingCard } from "./LandingCard";
import AppBlock from "../AppBlock";
import CollectionHeader from "../CollectionHeader";
import PropTypes from "prop-types";
import { useLocation } from "react-router";

export default function LandingScreen({ role, menuItems }) {
  const location = useLocation();
  const [filteredMenuItems, setFilteredMenuItems] = useState([]);
  const [filteredLandingData, setFilteredLandingData] = useState([]);

  // Filter out the screens
  const filterLandingDataByScreens = ({ landingData, screens }) => {
    return landingData.filter((item) => {
      if (item.key === "Upload File" || item.key === "Dashboard") return true;
      const screenValue = String(screens[item.key])?.toLowerCase();
      return screenValue === "true";
    });
  };

  // Filter out the screens
  const filterMenuItemsByScreens = ({
    menuItems,
    screens,
    bankId = null,
    designation = "",
  }) => {
    return menuItems
      .filter((item) => {
        const isMO = designation?.toLowerCase() === "mo";

        // Always show Upload File and Dashboard
        if (
          item.key === SMART_COLLECT_MENU_IDS.UPLOAD_FILE ||
          item.key === SMART_COLLECT_MENU_IDS.DASHBOARD
        ) {
          return true;
        }
        // If bank is not 9, hide all three
        if (
          bankId === 9 &&
          [
            SMART_COLLECT_MENU_IDS.STATE_WISE_REPORT,
            SMART_COLLECT_MENU_IDS.MARKETING,
            SMART_COLLECT_MENU_IDS.BANK_ASSIST,
          ].includes(item.key)
        ) {
          return true;
        }

        const screenValue = String(screens[item.screenKey])?.toLowerCase();
        return screenValue === "true";
      })
      .map((item) => ({ ...item }));
  };

  // Filtered Landing Data
  useEffect(() => {
    const user = JSON.parse(localStorage.getItem("user") || "{}");
    const screens = user?.Screens || {};
    const designation = user?.designation;
    const bankId = user?.BankMstID; // Get BankMstID
    let menu = [];

    if (["ho", "bm"].includes(role?.toLowerCase())) {
      menu = filterMenuItemsByScreens({
        menuItems,
        screens,
        bankId,
        designation,
      });
    } else {
      menu = menuItems; // Show full menu for other roles
    }
    setFilteredMenuItems(menu);

    const landing = filterLandingDataByScreens({
      landingData: HO_LANDING_PAGE_DATA,
      screens,
    }).filter(
      (item) =>
        !(
          item.title === "Campaign Management" &&
          designation?.toLowerCase() === "bm"
        )
    );
    setFilteredLandingData(landing);
  }, [menuItems]);

  return location.pathname === "/ho" || location.pathname === "/field" ? (
    <Flex vertical gap={10} className={Style.landing_screen}>
      {/* header */}
      <CollectionHeader isMenu={false} />

      {/* Cards */}
      <Row
        gutter={[10, 30]}
        align={"middle"}
        wrap
        className={Style.row_container}
      >
        {role == "HO"
          ? filteredLandingData?.map((item, index) => {
              return (
                <Col
                  xs={24}
                  sm={12}
                  md={8}
                  lg={8}
                  key={`${index}-${item.title}`}
                >
                  <LandingCard
                    link={item?.link}
                    title={item?.title}
                    image={item?.image}
                  />
                </Col>
              );
            })
          : FIELD_OFFICER_LANDING_PAGE_DATA?.map((item, index) => {
              return (
                <Col
                  xs={24}
                  sm={12}
                  md={8}
                  lg={8}
                  key={`${index}-${item.title}`}
                >
                  <LandingCard
                    link={item?.link}
                    title={item?.title}
                    image={item?.image}
                  />
                </Col>
              );
            })}
      </Row>
    </Flex>
  ) : (
    <AppBlock menuItems={filteredMenuItems} />
  );
}

LandingScreen.propTypes = {
  role: PropTypes.string,
  menuItems: PropTypes.oneOfType([PropTypes.array, PropTypes.object]),
};

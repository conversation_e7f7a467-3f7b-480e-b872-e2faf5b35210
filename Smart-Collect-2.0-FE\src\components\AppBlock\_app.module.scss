@import url("../../index.css");

// Main App Header zIndex: 111,
// Side Menu zIndex: 110,
// App Header zIndex: 108,
// Blur effect zIndex:110

$gap-value: 1.366rem;

body {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  background: white;
}

.blur > :first-child > :nth-child(1),
.blur > :first-child > :nth-child(2) {
  filter: blur(1px);
  pointer-events: none;
}

.blur {
  transition: all 0.3s ease-in-out;
  position: relative;
  min-height: calc(100vh - 64px);
  &::before {
    width: 100%;
    height: 100%;
    min-height: calc(100vh - 64px);
    overflow-x: hidden;
    background-color: #00000075;
    content: "";
    display: block;
    z-index: 110;
    position: absolute;
  }
}

// App block
.app_container {
  background-color: white;
  // Side bar
  .app_container_side_menu {
    background-color: var(--dark-blue);
    color: var(--white);
    border-radius: 10px;
    height: calc(100vh - 3rem);
    inset-inline-start: 0;
    scrollbar-width: thin;
    scrollbar-gutter: stable;
    position: fixed;
    left: $gap-value;
    top: $gap-value;

    // menu ul
    :global(.ant-layout-sider-children) {
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      // menu li
      :global(.ant-menu-item),
      :global(.ant-menu-submenu) {
        color: var(--white);
        margin-left: 0;
        margin-right: 0;
        width: 100%;
        border-radius: 0;
        margin-bottom: 1rem;
        height: auto;
        font-family: "Kanit", "Courier New", Courier, monospace;
        font-weight: normal;
        &:hover {
          color: var(--white) !important;
        }
      }

      :global(.ant-menu-title-content) {
        a {
          color: white;
          white-space: no-wrap;
        }
      }

      :global(.ant-menu-submenu-arrow) {
        color: white;
      }

      // menu selected li
      :global(.ant-menu-item-selected) {
        background-color: var(--off-white);
      }
    }

    // menu container
    :global(.ant-menu) {
      background-color: transparent;
      height: 500px;
      overflow-y: scroll;
      &::-webkit-scrollbar {
        display: none;
      }
    }

    :global(.ant-menu-sub) {
      height: auto;
    }
  }

  .app_container_content {
    transition: margin-left 0.3s ease;
    flex-grow: 1;
    width: 100%;
    overflow-x: hidden;
    background: white;
    margin-top: 64px; // header height
    min-height: calc(100vh - 64px);
    &::-webkit-scrollbar {
      display: none;
    }
  }

  .app_container_content > :first-child > :nth-child(2) {
    margin-top: 70px; // app header height (64)
    padding: 0 2rem 0 2rem;
    transition: filter 0.3s ease;
  }

  :global(.collapse) {
    max-width: 300px !important;
    min-width: 300px !important;
    width: 300px !important;
    flex: 0 0 300px !important;
  }

  // Menu button
  .menu_button {
    background-color: var(--dark-blue);
    color: var(--white);
    padding: 1.2rem 0;
    cursor: pointer;
    position: absolute;
    top: 50%;
    left: 110px;
    transform: translateY(-50%);
    width: 15px;
    height: 57px;
    border-radius: 0px 15px 15px 0px;
    z-index: 1;
    &:global(.expended) {
      left: 330px;
      border-radius: 15px 0 0 15px;
    }
  }
}

// Fix the modal issue
:global(.ant-modal-mask) {
  background-color: #1111112b !important;
  backdrop-filter: blur(3px);
}

:global(.ant-modal-wrap) {
  z-index: 1111;
}

// remove the shadow
:global(.ant-table-wrapper) {
  :global(.ant-table-ping-right) {
    &:not(.ant-table-has-fix-right) {
      :global(.ant-table-container) {
        &::after {
          box-shadow: none;
        }
      }
    }
  }
}

// Change the select arrow icon color all over the application
:global(.ant-select) {
  :global(.ant-select-arrow) {
    color: #0156a6;
  }
}

// Dropdown style
:global(.ant-dropdown) {
  :global(.ant-dropdown-menu) {
    background-color: #defbff;
    max-height: 400px;
    :global(.ant-dropdown-menu-title-content) {
      color: var(--dark-blue);
      font-weight: 500;
    }
  }
}
// REMINDER: Change the color if using side menubar
// side menu dropdown
:global(.ant-menu-submenu-popup) {
  :global(.ant-menu) {
    background-color: black !important;

    :global(.ant-menu-item-selected) {
      background-color: var(--off-white) !important;
    }
    :global(.ant-menu-title-content) {
      color: white;
      a {
        white-space: no-wrap;
      }
    }
  }
}

:global(.ant-tooltip) {
  :global(.ant-tooltip-inner) {
    background-color: black;
    border-color: black;
  }
}

// Responsive View
@media screen and (max-width: 1024px) {
  .app_container {
    .app_container_side_menu,
    .menu_button_container {
      display: none;
    }
    .app_container_content {
      width: 100%;
      margin-left: 0;
    }
  }
}

@media screen and (max-width: 768px) {
  .app_container {
    width: 100%;
    .app_container_content > :first-child > :nth-child(2) {
      padding: 0 1rem 2rem;
    }
  }
}

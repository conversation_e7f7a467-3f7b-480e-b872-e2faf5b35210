import { useEffect, useState } from "react";
import { useLocation, useNavigate, Outlet } from "react-router";

function App() {
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const user = JSON.parse(localStorage.getItem("user"));
    const token = user?.access;

    // Allow privacy-policy route for everyone
    if (location.pathname === "/privacy-policy") {
      setLoading(false);
      return;
    }

    if (location.pathname !== "/" && location.pathname.endsWith("/")) {
      navigate(location.pathname.slice(0, -1), { replace: true });
      return;
    }
    // User logged out & try to access other routes
    if (
      !token &&
      location.pathname !== "/" &&
      location.pathname !== "/forget-password"
    ) {
      navigate("/", { replace: true });
    }
    // User is logged in
    else if (token) {
      redirectToLandingPage(user); // Always validate role path
    } else {
      // No redirect needed, finish loading
      setLoading(false);
    }
  }, [location]);

  const redirectToLandingPage = (user) => {
    const { designation = "", is_admin = false } = user || {};
    const currentPath = location.pathname;

    if (currentPath === "/privacy-policy") {
      setLoading(false);
      return;
    }

    let rolePath = "/ho"; // default
    if (is_admin) rolePath = "/admin";
    else {
      const lowerDesignation = designation?.toLowerCase();
      if (["fo", "co"].includes(lowerDesignation)) {
        rolePath = "/field";
      } else if (lowerDesignation === "calling") {
        rolePath = "/agent-calling";
      }
    }

    if (!currentPath.startsWith(rolePath)) {
      return navigate(rolePath, { replace: true }); // Replace to block back
    } else {
      setLoading(false);
    }
  };

  return loading ? null : <Outlet />;
}

export default App;

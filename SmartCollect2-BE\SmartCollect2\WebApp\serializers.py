from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
# from WebApp.models import BranchMst, BankMst,LoginHistory,LoanMst,UserFeedback,Response, LanguageMst
from WebApp.models import *
from rest_framework import serializers
from django.contrib.auth.hashers import check_password
from django.contrib.auth.password_validation import validate_password
import logging
from django.utils.text import slugify
from datetime import datetime
from WebApp.signals import jwt_logged_in
import json


class TBLUserMasterTokenSerializer(TokenObtainPairSerializer):
    def validate(self, attrs):
        data = super().validate(attrs)
        data["name"] = self.user.get_full_name()
        data["email"] = self.user.email
        data["username"] = self.user.username
        data["is_admin"] = self.user.is_admin
        data["is_staff"] = self.user.is_staff
        data["is_superuser"] = self.user.is_superuser
        data["is_active"] = self.user.is_active
        data["BankMstID"] = self.user.BankMstID.BankMstID
        data["BankMstName"] = self.user.BankMstID.BankName
        # data["BankCreatedDate"] = self.user.BankMstID.CreatedDate
        if self.user.BranchMstID:
            data["BranchMstID"] = self.user.BranchMstID.BranchMstID
            data["BranchMstName"] = self.user.BranchMstID.BranchName
        data["BranchID"] = self.user.BranchID
        data["Designation"] = self.user.Designation
        data["MobileNumber"] = self.user.MobileNumber
        data["FO_id"] = self.user.FO_id
        data["BUcode"] = self.user.BUcode
        # data["BranchOfficer"] = self.user.BranchOfficer.UserMstID if self.user.BranchOfficer else None
        # data["BranchOfficerName"] = self.user.BranchOfficer.get_full_name() if self.user.BranchOfficer else None

        if self.user.is_admin:
            data["is_admin"] = True
        component_qs = None
        if not self.user.is_admin:
            component_qs = ComponentMst.objects.filter(
                BankMstID=self.user.BankMstID.BankMstID,
                Designation=self.user.Designation
            ).values('Screens').first()
            # data["Screens"] = component_qs["Screens"]
            print(component_qs)

        if not component_qs:
            component_qs = ComponentMst.objects.filter(
                BankMstID=self.user.BankMstID.BankMstID,
                Designation='HO'
            ).values('Screens').first()
            # data["Screens"] = component_qs["Screens"]
        data["Screens"] = component_qs["Screens"] if component_qs else []

        if self.user.BranchMstID:
            is_first = not LoginHistory.objects.filter(user=self.user).exists()
            data["is_first"] = is_first

        jwt_logged_in.send(sender=self.__class__, user=self.user, request=self.context["request"])

        return data


class TBLLoanMasterSerializer(serializers.ModelSerializer):
    CustomerName = serializers.CharField(source="CustomerMstID.CustomerName", read_only=True)
    BranchName = serializers.CharField(source="BranchMstID.BranchName", read_only=True)

    class Meta:
        model = LoanMst
        fields = "__all__"


class LoginHistorySerializer(serializers.ModelSerializer):
    username = serializers.CharField(source="user.username", read_only=True)

    class Meta:
        model = LoginHistory
        # all fields are read only
        fields = [
            'username',
            'user',
            'ip',
            'city',
            'region',
            'region_code',
            'country_code',
            'country_name',
            'currency',
            'user_agent',
            'created_at',
        ]
        read_only_fields = [
            'user',
            'ip',
            'ip_info',
            'city',
            'region',
            'region_code',
            'country_code',
            'country_name',
            'currency',
            'user_agent',
            'created_at',
        ]


class LanguageMasterSerializer(serializers.ModelSerializer):
    class Meta:
        model = LanguageMst
        fields = ['Language', 'IsActive', 'LngMstID']
        read_only_fields = ['LngMstID']

    def create(self, validated_data):
        """Remove BankMstID from request data to prevent manipulation"""
        user = self.context['request'].user
        validated_data['BankMstID'] = user.BankMstID  # Set it from the authenticated user
        return super().create(validated_data)


class BankControlJsonSerializer(serializers.Serializer):
    CampaignCategoryColumns = serializers.ListField(
        child=serializers.CharField(),
        allow_empty=True,
        required=False
    )

    VariableMappingFields = serializers.ListField(
        child=serializers.CharField(),
        allow_empty=True,
        required=False
    )

    BaseOnColumns = serializers.ListField(
        child=serializers.CharField(),
        allow_empty=True,
        required=False
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Enforce required fields only if not partial
        if not self.partial:
            for field in self.fields:
                self.fields[field].required = True

    def validate(self, data):
        # Validate the JSON structure
        if not isinstance(data, dict):
            raise serializers.ValidationError("Invalid JSON format")

        column_names_meta = AccountSummary.column_metadata
        invalid_columns = {
            "CampaignCategoryColumns": [],
            "VariableMappingFields": [],
            "BaseOnColumns": []
        }

        for field in invalid_columns.keys():
            invalid_columns[field] = [
                col for col in data.get(field, []) if col not in column_names_meta
            ]

        errors = {
            field: f"Invalid column names: {', '.join(cols)}"
            for field, cols in invalid_columns.items() if cols
        }

        if errors:
            raise serializers.ValidationError(errors)

        return data


class BankControlSerializer(serializers.ModelSerializer):
    class Meta:
        model = BankControls
        fields = ['ControlID', 'BankMstID', 'Json']
        read_only_fields = ['ControlID']

    def __init__(self, *args, **kwargs):
        # Dynamically declare the Json field
        self.fields['Json'] = BankControlJsonSerializer(partial=kwargs.get('partial', False))

        super().__init__(*args, **kwargs)

        # If instance is not None (i.e., we're updating), make BankMstID read-only
        if not self.instance:
            # If creating, make sure BankMstID is required
            self.fields['BankMstID'].required = True
        else:
            self.fields['BankMstID'].read_only = True

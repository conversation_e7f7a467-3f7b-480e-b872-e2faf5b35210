@import url("../../../../../../index.css");

$table-radius:22px;
$attempts-bg:#BDD1FF;
$disable:#787777;
$body:#E4F8F9;

.text{
 font-family: 'Kanit','Courier New', Courier, monospace;
 text-transform: capitalize;
 margin: 1rem 0 0 0;
 font-weight: 400;
}

.btn{
    border: none;
    outline: none;
    background-color: var(--dark-blue) !important;
    color: white !important;
    font-size: 12px;
    padding: 0rem 0.5rem;
    line-height: 0;
}

.customTable{
    padding-top: 1.5rem;

    .name{
        font-weight: 500;
        white-space: nowrap;
        color:var(--dark-blue)
    }

    .icon_div{
        display: flex;
        justify-content: center;
        width: 100%;  
        .icon_img{
        width: 20px;
        height: 20px;
        img{
            width: 100%;
            object-fit: contain;
            cursor: pointer;
        }
      }
    }
   
    .custom_check{

        :global(.ant-checkbox){
            &:not(.ant-checkbox-disabled){
                &:hover{
                    :global(.ant-checkbox-inner){
                        border-color: #B8CEFF;
                        box-shadow: 0px 4px 4px 0px #00000040;
                        background-color: #B8CEFF;
                    }
                }
            }
            &:global(.ant-checkbox-checked){
                :global(.ant-checkbox-inner){
                    background-color: #B8CEFF; 
                    &::after{
                        content: none;
                    }
                }
            }
        }
        :global(.ant-checkbox-inner){
            border-radius: 50px;
            border-color: #D9D9D9;
        }
    }

    //Global ant design classes

    // Pagination
    :global(.ant-pagination){
        justify-content: center;
        margin: 0 !important;
        
        :global(.ant-pagination-prev),
        :global(.ant-pagination-next){
            color: var(--dark-blue);
            border: 0 solid var(--blue);
            background: #E4F8F9;
            height: 25px;
            min-width: 15px;
            border-radius: 0px;
            margin: 0;
            button{
                border-radius: 0px;
            }
        }
        :global(.ant-pagination-item){
            margin-right: 0;
            height: 0;
            a{
                color:$disable;
                font-size: 0.9rem;
                line-height: 23px;
                font-family: 'Kanit','Courier New', Courier, monospace;
                font-weight: normal;
            }
            &:hover{
                background-color: transparent;
            }
        }
        :global(.ant-pagination-item-active){
            border:none;
           a{
                color:var(--dark-blue);
                font-size:1.2rem;
                padding: 0;
           }
        }
    }

    //Table container
    :global(.ant-table-container){
        padding: 0;
        margin-bottom: 0.5rem;
        border-start-start-radius: $table-radius;
        border-start-end-radius: $table-radius;

        // Table header
        :global(.ant-table-header){
            position: relative;
            margin: 0 auto;
            top: -21px;
            border-radius: $table-radius;
        }

        // Table virtual body
        :global(.ant-table-tbody-virtual){
            margin-top: -8px;
        }

        &::before{
            box-shadow: none !important;
        }
    }

    // Table rows header 
    :global(.ant-table-thead >tr){
        th{
            border-start-end-radius:0 !important;
            background-color: #B8CEFF;
            border-inline-end: none !important;
            color: black;
            border-bottom:none;
            text-align: center;
            font-family: 'Kanit','Courier New', Courier, monospace;
            font-weight: normal;
            padding: 0.6rem;

            &:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before{
                width: 0;
            }
        }
    }

    // Table body
    :global(.ant-table-tbody){
        // Body rows
        :global(.ant-table-row){  
            &:nth-child(even){
                background-color:white;
            }  
        
        :global(.ant-table-cell-row-hover){
            background: transparent;
        }
        // Cols
        :global(.ant-table-cell){
            font-family: 'Kanit','Courier New', Courier, monospace;
            font-weight: normal;
            text-align: center;
            border-bottom: 2px solid white; 

            &:global(.ant-table-cell-fix-right){
                border-bottom: none !important; 
           }
            &:global(.ant-table-cell-fix-right-first){
                border-left: 2px solid white;
            }
        }
        }
    }

    // Fixed Cols
    :global(.ant-table-cell-fix-right){
        background-color: $body;
   }
}

// Responsive view
@media screen and (max-width:768px) {
    .text{
        font-size: 10px !important;
    }
    .btn{
        height: auto;
        font-size: 10px;
        padding: 0.8rem 1rem;
    }
    .customTable{
        :global(.ant-table-thead >tr){
            th{
                padding: 0.5rem;
                font-size: 10px;
            }
        } 
        :global(.ant-table-tbody){
            :global(.ant-table-row){
                :global(.ant-table-cell){
                    padding: 0.5rem;
                    font-size: 10px;
                    :global(.ant-typography){
                        font-size: 10px;
                    }
                }
            }
        }
    }
}
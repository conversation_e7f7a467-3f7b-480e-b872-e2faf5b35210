@import url("../../index.css");

$bg-color: #f3f3f4;

.container {
  padding: 1rem 1rem 1rem;
  position: relative;
  width: calc(100% - 0.6rem);
  height: calc(100vh - 5rem); /* 3rem margin + 2rem padding */
  overflow: hidden;

  .history_container {
    width: 300px;
    .chat_history_container {
      background-color: $bg-color;
      padding: 0.5rem 1rem;
      min-width: 300px;
      border-radius: 5px;
      flex: 1;

      .title {
        font-family: "kanit", "Courier New", Courier, monospace;
        font-size: 18px;
      }

      .title_img_div,
      .new_chat_img {
        width: 6px;
        cursor: pointer;
        img {
          width: 100%;
          object-fit: contain;
        }
      }

      .new_chat_img {
        width: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .img_div {
        width: 12px;
        img {
          width: 100%;
          object-fit: contain;
        }
      }

      .chat_title {
        font-size: 14px;
        font-family: "kanit", "Courier New", Courier, monospace;
      }

      .recent_container,
      .suggestion_container {
        max-height: 300px;
        overflow-y: scroll;

        .history_details {
          background-color: white;
          border-radius: 5px;
          padding: 0.5rem 1rem;
          cursor: pointer;

          :global(.ant-typography) {
            font-family: "kanit", "Courier New", Courier, monospace;
          }
          .history_message {
            font-weight: 300;
          }
          .delete_icon {
            cursor: pointer;
          }
        }

        // scrollbar
        &::-webkit-scrollbar {
          width: 5px;
        }

        /* Track */
        &::-webkit-scrollbar-track {
          box-shadow: inset 0 0 5px grey;
          border-radius: 2px;
        }

        /* Handle */
        &::-webkit-scrollbar-thumb {
          background: #0f2050;
          border-radius: 5px;
        }

        /* Handle on hover */
        &::-webkit-scrollbar-thumb:hover {
          background: #0f2050;
        }
      }

      .suggestion_container {
        max-height: 150px;
      }
    }
  }

  .history_chat_container {
    margin-top: 0 !important;
    flex: 1;
    width: 100%;
    overflow: hidden;
    .history_icon {
      cursor: pointer;
      border: none;
      background-color: transparent;
    }
    .chat_container {
      background-image: url("../../assets/Images/logo.png");
      background-color: rgba(255, 255, 255, 0.886); /* Light overlay */
      background-blend-mode: overlay; /* Adjust blending */
      background-attachment: fixed;
      background-repeat: no-repeat;
      background-size: 350px;
      background-position-y: 50%;
      background-position-x: 60%;
      width: 100%;
      height: 500px;
      overflow-y: auto;

      .icon {
        width: 10px;
        img {
          width: 100%;
          object-fit: contain;
        }
      }

      .chat_message {
        display: flex;
        margin-bottom: 20px;

        &.bot {
          justify-content: flex-start;
        }
        &.user {
          justify-content: flex-end;
        }
        .messages_container {
          background-color: #f8f8f9;
          padding: 0.6rem 0.8rem;
          border-radius: 50px;
          max-width: 500px;
          word-break: break-all;
        }
      }

      // hide the scrollbar
      &::-webkit-scrollbar {
        display: none;
      }
    }

    .input_box {
      border: 1px solid #113c9b;
      border-radius: 6px;
      padding: 0.7rem;
      margin-top: 0.5rem;

      .custom_input {
        width: 100%;
        border: none !important;
        outline: none !important;
        font-family: "kanit", "Courier New", Courier, monospace;
        caret-color: var(--dark-blue);
        resize: none;
        line-height: 1.2;
        white-space: pre-line;

        &::placeholder {
          color: #00000073;
        }
        &::-webkit-scrollbar {
          display: none;
        }
      }
      .sender_btn {
        border: none !important;
        outline: none !important;
        background: var(--dark-blue);
        width: 40px;
        height: 40px;
        border-radius: 50px;
        padding: 0.7rem;
        cursor: pointer;
        img {
          width: 100%;
          object-fit: contain;
        }
      }
    }
  }

  .user_container {
    height: 100%;

    :global(.ant-typography) {
      font-family: "kanit", "Courier New", Courier, monospace;
      font-size: 1.8rem;
      color: #407bff;
      text-transform: capitalize;
    }
  }
}

// Typing animation
.ticontainer {
  .tiblock {
    align-items: center;
    display: flex;
    height: 17px;
  }

  .tidot {
    background-color: #8f66b5;
    -webkit-animation: mercuryTypingAnimation 1.5s infinite ease-in-out;
    animation: mercuryTypingAnimation 1.5s infinite ease-in-out;
    border-radius: 2px;
    display: inline-block;
    height: 6px;
    margin-right: 2px;
    width: 6px;

    &:nth-child(1) {
      -webkit-animation-delay: 200ms;
      animation-delay: 200ms;
    }
    &:nth-child(2) {
      -webkit-animation-delay: 300ms;
      animation-delay: 300ms;
    }
    &:nth-child(3) {
      -webkit-animation-delay: 400ms;
      animation-delay: 400ms;
    }
  }
}

@-webkit-keyframes mercuryTypingAnimation {
  0% {
    -webkit-transform: translateY(0px);
  }
  28% {
    -webkit-transform: translateY(-5px);
  }
  44% {
    -webkit-transform: translateY(0px);
  }
}

@keyframes mercuryTypingAnimation {
  0% {
    -webkit-transform: translateY(0px);
  }
  28% {
    -webkit-transform: translateY(-5px);
  }
  44% {
    -webkit-transform: translateY(0px);
  }
}

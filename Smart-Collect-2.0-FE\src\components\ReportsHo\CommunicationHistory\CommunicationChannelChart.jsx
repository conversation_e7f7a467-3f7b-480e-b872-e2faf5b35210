import { Flex, Typography } from "antd";
import Style from "./_style.module.scss";
import Chart from "react-apexcharts";
import COMM_CHANNEL_IMG from "../../../assets/Images/comm-channel.png";
import PropTypes from "prop-types";

const { Text } = Typography;

export function CommunicationChannelChart({ summary = {} }) {
  // Extract totals from summary
  const whatsapp = summary.whatsapp_total || 0;
  const ai = summary.voicebot_total || 0;
  const blaster = summary.blaster_total || 0;
  const dialer = 0; // If you have dialer_total, use it here
  const ivr = summary.ivr_total || 0;

  const series = [whatsapp, ai, blaster, dialer, ivr];
  const labels = ["Whatsapp", "AI", "Blaster", "Dialer", "IVR"];
  const colors = ["#33AB3C", "#FDBF00", "#FF631A", "#14F3F7", "#113C9B"];

  const options = {
    chart: {
      type: "donut",
      toolbar: { show: false },
    },
    stroke: { show: false },
    labels,
    legend: {
      position: "bottom",
      markers: { colors },
      labels: {
        colors: "#1f3bb3",
        useSeriesColors: false,
      },
    },
    plotOptions: {
      pie: {
        donut: {
          size: "50%",
          offsetY: 5,
          labels: {
            show: true,
            name: { show: false },
            value: { show: false },
            total: { show: false },
          },
        },
        expandOnClick: true,
        offset: 20,
      },
    },
    colors,
    fill: { type: "solid" },
    dataLabels: {
      enabled: true,
      style: {
        fontSize: "10px",
        fontWeight: "bold",
        colors: ["#fff"],
      },
      dropShadow: {
        enabled: true,
        top: 2,
        left: 2,
        blur: 2,
        color: "#000",
        opacity: 0.45,
      },
    },
    states: {
      normal: {
        filter: { type: "none" },
      },
      hover: {
        filter: {
          type: "none",
        },
      },
      active: {
        filter: {
          type: "none",
        },
      },
    },
  };

  return (
    <Flex vertical gap={5} className={Style.chart_container}>
      <Text className={Style.heading}>Communication Channel:</Text>
      <div className={Style.chart}>
        <Chart
          options={options}
          series={series}
          type="donut"
          width={"100%"}
          height={330}
        />
        {/* Center Overlay */}
        <div className={Style.chart_overlay}>
          <img src={COMM_CHANNEL_IMG} alt="icon" />
        </div>
      </div>
    </Flex>
  );
}

CommunicationChannelChart.propTypes = {
  summary: PropTypes.object,
};

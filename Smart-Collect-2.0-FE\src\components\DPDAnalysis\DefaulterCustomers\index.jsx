import { Flex, message, Table, Typography } from "antd";
import React, { useState } from "react";
import Style from "./_defaulter-table.module.scss";
import {
  FIELD_DEFAULTER_DATA,
  handleDateFormatter,
  handleDownloadTable,
} from "../../../constant";
import { HoLanCards } from "../../HoLanCards";
import DOWNLOAD_IMG from "../../../assets/Images/download.svg";

const { Text, Title } = Typography;

export function DefaulterCustomers() {
  const [data] = useState(FIELD_DEFAULTER_DATA);
  const [selectedRegion, setSelectedRegion] = useState(null);
  const [selectedHubIndex, setSelectedHubIndex] = useState(null);
  const [showLan, setShowLan] = useState(false);

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      sorter: (a, b) => a.key - b.key,
      render: (_, data) => <Text>{data?.key}.</Text>,
    },
    {
      title: "Region",
      dataIndex: "region",
      sorter: (a, b) => a.region.localeCompare(b.region),
      render: (_, data) => (
        <Text
          className={Style.blueText}
          onClick={() => handleRegionClick({ region: data.region })}
        >
          {data?.region}
        </Text>
      ),
    },
    ...(selectedRegion
      ? [
          {
            title: "Hub",
            dataIndex: "hub",
            sorter: (a, b) => a.hub?.localeCompare(b.hub),
            render: (_, data, index) => (
              <button
                className={Style.button}
                disabled={data.region !== selectedRegion}
                onClick={() => handleHubClick(index)}
              >
                {data.region === selectedRegion ? data?.hub : "-"}
              </button>
            ),
          },
        ]
      : []),
    ...(selectedHubIndex !== null
      ? [
          {
            title: "Branch",
            dataIndex: "branch",
            sorter: (a, b) => a.branch?.localeCompare(b.branch),
            render: (_, data, index) => (
              <Text className={Style.text}>
                {index === selectedHubIndex ? data.branch : "-"}
              </Text>
            ),
          },
        ]
      : []),
    {
      title: "Date",
      dataIndex: "date",
      sorter: (a, b) => new Date(a.date) - new Date(b.date),
      render: (_, data) => (
        <Text className={Style.text}>
          {data?.date ? handleDateFormatter(data.date) : "-"}
        </Text>
      ),
    },
    {
      title: "#LAN",
      dataIndex: "lanAmount",
      sorter: (a, b) => a.lanAmount - b.lanAmount,
      render: (_, data) => (
        <Text className={Style.lan} onClick={handleLan}>
          {data?.lanAmount ? data.lanAmount : 0}
        </Text>
      ),
    },
    {
      title: "Amount",
      dataIndex: "amount",
      sorter: (a, b) => a.amount - b.amount,
      render: (_, data) => (
        <Text className={Style.text}>{data?.amount ? data.amount : "-"}</Text>
      ),
    },
  ];

  const handleRegionClick = ({ region }) => {
    setSelectedRegion(region);
    setSelectedHubIndex(null);
  };

  const handleHubClick = (index) => {
    if (selectedRegion) {
      setSelectedHubIndex(index);
    } else {
      setSelectedHubIndex(null);
    }
  };

  const handleLan = () => {
    setShowLan(true);
  };

  const handleBack = () => {
    setShowLan(false);
  };

  const sortedData = selectedRegion
    ? [...data].sort((a, b) => (a.region === selectedRegion ? -1 : 1))
    : data;

  const dataSource = Array.isArray(sortedData)
    ? sortedData.map((d, i) => ({
        key: i + 1,
        ...d,
      }))
    : [];

  // Handle downloading
  const handleDownload = async () => {
    if (data.length === 0) {
     return message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: "Defaulter Customers DPD analysis",
        worksheetName: "DPD-Analysis-Defaulte-Customers",
        tableData: data,
      });
    }
  };

  return (
    <Flex vertical gap={20}>
      {!showLan ? (
        <>
          <Flex justify="end" gap="1">
            <button className={Style.download_button} onClick={handleDownload}>
              <img
                src={DOWNLOAD_IMG}
                alt="download-button"
              />
            </button>
          </Flex>
          <Table
            bordered
            virtual
            className={Style.defaulter}
            columns={columns}
            dataSource={dataSource}
            scroll={{
              // x: 2000,
              y: 460,
            }}
            pagination={{
              showSizeChanger: false,
            }}
          />
        </>
      ) : (
        <HoLanCards handleBack={handleBack} />
      )}
    </Flex>
  );
}

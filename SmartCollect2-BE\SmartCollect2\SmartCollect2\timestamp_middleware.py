import re
import json
from django.http import JsonResponse

class TimestampMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        self.timestamp_pattern = re.compile(r'\b\d{10,13}\b')

    def __call__(self, request):
        response = self.get_response(request)
        
        if (hasattr(response, 'content') and 
            response.get('Content-Type', '').startswith('application/json')):
            
            try:
                content = response.content.decode('utf-8')
                
                if self.timestamp_pattern.search(content):
                   
                    data = json.loads(content)
                    cleaned_data = self._remove_timestamps(data)
                    
                    response = JsonResponse(cleaned_data, safe=False)
                    
            except (json.JSONDecodeError, UnicodeDecodeError):
                
                pass
                
        return response
    
    def _remove_timestamps(self, data):
        """Recursively remove timestamps from data structures"""
        if isinstance(data, dict):
            cleaned_dict = {}
            for key, value in data.items():
                if key.lower() in ['timestamp', 'created_at', 'updated_at', 'modified_at', 'last_modified']:
                    continue
                cleaned_dict[key] = self._remove_timestamps(value)
            return cleaned_dict
        elif isinstance(data, list):
            return [self._remove_timestamps(item) for item in data]
        elif isinstance(data, (int, float)):
            if isinstance(data, int) and 1000000000 <= data <= 9999999999:
                return None 
            return data
        else:
            return data
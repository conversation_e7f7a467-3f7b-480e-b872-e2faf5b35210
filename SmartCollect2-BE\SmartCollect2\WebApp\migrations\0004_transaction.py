# Generated by Django 5.1.5 on 2025-02-18 10:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("WebApp", "0003_accountsummary_customermst_languagemst_loanmst_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Transaction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("TransactionID", models.IntegerField(db_column="TransactionID")),
                ("BranchMstID", models.IntegerField(db_column="BranchMstID")),
                ("CreatedDate", models.DateField(db_column="CreatedDate")),
                ("LoanMstID", models.IntegerField(db_column="LoanMstID")),
                (
                    "DisbursementID",
                    models.CharField(db_column="DisbursementID", max_length=255),
                ),
                (
                    "CollectedAmt",
                    models.DecimalField(
                        db_column="CollectedAmt", decimal_places=2, max_digits=10
                    ),
                ),
                ("CollectedDate", models.DateField(db_column="CollectedDate")),
                ("Processed", models.BooleanField(db_column="Processed")),
                (
                    "PrincipleCollected",
                    models.DecimalField(
                        db_column="PrincipleCollected", decimal_places=2, max_digits=10
                    ),
                ),
                (
                    "InterestCollected",
                    models.DecimalField(
                        db_column="InterestCollected", decimal_places=2, max_digits=10
                    ),
                ),
                (
                    "ExtraColumn1",
                    models.CharField(
                        blank=True, db_column="ExtraColumn1", max_length=255, null=True
                    ),
                ),
                (
                    "ExtraColumn2",
                    models.CharField(
                        blank=True, db_column="ExtraColumn2", max_length=255, null=True
                    ),
                ),
                (
                    "ExtraColumn3",
                    models.CharField(
                        blank=True, db_column="ExtraColumn3", max_length=255, null=True
                    ),
                ),
                (
                    "ExtraColumn4",
                    models.CharField(
                        blank=True, db_column="ExtraColumn4", max_length=255, null=True
                    ),
                ),
                (
                    "ExtraColumn5",
                    models.CharField(
                        blank=True, db_column="ExtraColumn5", max_length=255, null=True
                    ),
                ),
                ("BankMstID", models.IntegerField(db_column="BankMstID")),
            ],
            options={
                "db_table": "Transaction",
                "managed": False,
            },
        ),
    ]

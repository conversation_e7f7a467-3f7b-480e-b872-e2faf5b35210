import pandas as pd
from datetime import datetime,date
from ..utils import *
from ..queries import insert_data_in_raw_table,get_branch_names_from_db
import threading
from ..sp import execute_post_processing_procedures

def run_post_processing_in_background(bank_id):
    """
    Run post-processing procedures in a background thread
    """
    try:
        print(f"Starting background post-processing for bank_id: {bank_id}")
        execute_post_processing_procedures(bank_id)
        print(f"Background post-processing initiated for bank_id: {bank_id}")
    except Exception as e:
        print(f"Error starting background post-processing for bank_id {bank_id}: {str(e)}")

def add_branch_names_to_dataframe(df, bank_mst_id, branch_column='branch_cd'):
    """
    Add branch names to dataframe based on branch codes
    
    Args:
        df: DataFrame containing branch codes
        bank_mst_id: Bank Master ID for database lookup
        branch_column: Column name containing branch codes (default: 'branch_cd')
    
    Returns:
        DataFrame with added branch_name column
    """
    if branch_column not in df.columns:
        print(f"Warning: Column '{branch_column}' not found in dataframe")
        df['branch_name'] = None
        return df
    
    # Strip whitespace from branch_cd column first
    df['branch_cd'] = df['branch_cd'].astype(str).str.strip()
    
    # Get unique branch codes (excluding null values)
    unique_branch_codes = df[branch_column].dropna().unique()
    unique_branch_codes = [str(code).strip() for code in unique_branch_codes if str(code).strip()]
    
    print(f"Found {len(unique_branch_codes)} unique branch codes to lookup")
    
    # Get branch names from database
    branch_mapping = get_branch_names_from_db(bank_mst_id)
    
    # Map branch codes to branch names (using the stripped column)
    df['branch_name'] = df[branch_column].astype(str).map(branch_mapping)
    print(df['branch_name'])
    # Count successful mappings
    mapped_count = df['branch_name'].notna().sum()
    total_count = len(df)
    
    print(f"Successfully mapped branch names for {mapped_count}/{total_count} records")
    
    # Show unmapped branch codes for debugging
    unmapped_codes = df[df['branch_name'].isna() & df[branch_column].notna()][branch_column].unique()
    if len(unmapped_codes) > 0:
        print(f"Warning: {len(unmapped_codes)} branch codes could not be mapped: {list(unmapped_codes)[:10]}")
    
    return df

def process_warna_loan_data(df, bank_mst_id=None):
    """
    Updated loan data processing with branch name lookup
    """
    df["customer_id"] = df["customer_id"].astype(str)
    df["disbursement_id"] = df["disbursement_id"].astype(str)
    df["account_type"] = df["account_type"].astype(str)
    df['branch_cd'] = df['branch_cd']
    
    # Process date columns
    date_columns = ['disbursed_dt', 'date_closed', 'date_of_last_payment', 'date_reported', 'ins_start_dt', 'npa_dt', 'cc_review_limit_expire','no_of_days_past_dues']
    for col in date_columns:
        if col in df.columns:
            df[col] = pd.to_datetime(df[col], errors='coerce')
    
    # Process numeric columns
    numeric_columns = ['loan_sanction_limit', 'disbursed_amount', 'rate_of_interest', 'repayment_tenure', 
                  'emi_amount', 'latest_collected_amt', 'amount_overdue']
    for col in numeric_columns:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    df["amount_overdue"] = df["amount_overdue"].fillna(0)
    
    # Add branch names if bank_mst_id is provided
    if bank_mst_id is not None:
        print("Adding branch names to loan data...")
        df = add_branch_names_to_dataframe(df, bank_mst_id, 'branch_cd')
    
    return df

def process_warna_customer_data(customer_df, bank_mst_id=None):
    """
    Updated customer data processing with branch name lookup
    """
    customer_df["customer_id"] = customer_df["customer_id"].astype(str)
    if 'mobile' in customer_df.columns:
        customer_df['mobile'] = customer_df['mobile'].apply(clean_mobile_number)
    customer_df['customer_name'] = customer_df['customer_name'].apply(lambda x: clean_name(x).upper())
    customer_df=customer_df[['customer_id','mobile','customer_name']]
    # Add branch names if bank_mst_id is provided and branch_cd column exists
    # if bank_mst_id is not None and 'branch_cd' in customer_df.columns:
    #     print("Adding branch names to customer data...")
    #     customer_df = add_branch_names_to_dataframe(customer_df, bank_mst_id, 'branch_cd')

    # Return only unique records based on customer_id and customer_name
    customer_df = customer_df.drop_duplicates(subset=['customer_id', 'customer_name'], keep='first')
    
    return customer_df

def merge_warna_data_with_counts(loan_df, customer_df, guarantor_df, negative_df, bank_mst_id=36):
    """
    Updated merge function with branch name lookup
    
    Args:
        loan_df: Processed loan dataframe
        customer_df: Processed customer dataframe  
        guarantor_df: Processed guarantor dataframe
        negative_df: Processed negative dataframe
        bank_mst_id: Bank Master ID for branch name lookup (optional)
    
    Returns:
        dict: Contains merged dataframe and detailed statistics
    """
    
    # Add validation to check which dataframes are None
    dataframe_status = {
        'loan_df': loan_df is not None,
        'customer_df': customer_df is not None,
        'guarantor_df': guarantor_df is not None,
        'negative_df': negative_df is not None
    }
    
    print(f"DataFrame Status Check:")
    print(f"loan_df is available: {dataframe_status['loan_df']}")
    print(f"customer_df is available: {dataframe_status['customer_df']}")
    print(f"guarantor_df is available: {dataframe_status['guarantor_df']}")
    print(f"negative_df is available: {dataframe_status['negative_df']}")
    print(f"bank_mst_id provided: {bank_mst_id is not None}")
    print("-" * 50)
    
    # Handle single file scenarios with branch name lookup
    if loan_df is not None and customer_df is None and guarantor_df is None:
        print("Processing single loan file only")
        if bank_mst_id is not None:
            loan_df = add_branch_names_to_dataframe(loan_df, bank_mst_id, 'branch_cd')
        return loan_df
    
    if customer_df is not None and loan_df is None and guarantor_df is None:
        print("Processing single customer file only")
        if bank_mst_id is not None and 'branch_cd' in customer_df.columns:
            customer_df = add_branch_names_to_dataframe(customer_df, bank_mst_id, 'branch_cd')
        return customer_df
        
    if guarantor_df is not None and loan_df is None and customer_df is None:
        print("Processing single guarantor file only")
        if bank_mst_id is not None and 'branch_cd' in guarantor_df.columns:
            guarantor_df = add_branch_names_to_dataframe(guarantor_df, bank_mst_id, 'branch_cd')
        return guarantor_df
    
    # Check for required dataframes
    if loan_df is None:
        raise ValueError("loan_df is None - Loan dataframe is required for merging")
    
    if customer_df is None:
        raise ValueError("customer_df is None - Customer dataframe is required for merging")
    
    # Initialize statistics dictionary with safe length checks
    stats = {
        'loan_records': len(loan_df) if loan_df is not None else 0,
        'customer_records': len(customer_df) if customer_df is not None else 0,
        'guarantor_records': len(guarantor_df) if guarantor_df is not None else 0,
        'negative_records': len(negative_df) if negative_df is not None else 0
    }
    
    print(f"Initial Data Counts:")
    print(f"Loan records: {stats['loan_records']:,}")
    print(f"Customer records: {stats['customer_records']:,}")
    print(f"Guarantor records: {stats['guarantor_records']:,}")
    print(f"Negative records: {stats['negative_records']:,}")
    print("-" * 50)
    print(f"columns:",loan_df.columns)
    print(f"columns:",customer_df.columns)
    print(f"columns:",guarantor_df.columns)
    print(f"columns:",negative_df.columns)
    
    # Step 1: Merge loan data with customer data on customer_id
    print("Step 1: Merging Loan data with Customer data...")
    
    # Check unique customer_ids in both datasets
    loan_customers = set(loan_df['customer_id'].unique())
    customer_ids = set(customer_df['customer_id'].unique())
    
    stats['unique_customers_in_loan'] = len(loan_customers)
    stats['unique_customers_in_customer_df'] = len(customer_ids)
    stats['customer_match_overlap'] = len(loan_customers.intersection(customer_ids))
    stats['customers_only_in_loan'] = len(loan_customers - customer_ids)
    stats['customers_only_in_customer_df'] = len(customer_ids - loan_customers)
    
    print(f"Unique customers in loan data: {stats['unique_customers_in_loan']:,}")
    print(f"Unique customers in customer data: {stats['unique_customers_in_customer_df']:,}")
    print(f"Customer ID overlap: {stats['customer_match_overlap']:,}")
    print(f"Customers only in loan data: {stats['customers_only_in_loan']:,}")
    print(f"Customers only in customer data: {stats['customers_only_in_customer_df']:,}")
    
    # Perform the merge
    loan_customer_merged = pd.merge(
        loan_df, 
        customer_df, 
        on='customer_id', 
        how='left',  # Keep all loan records
        indicator='customer_merge_indicator'
    )
    print(f"columns:",loan_customer_merged.columns)
    # Count merge results
    customer_merge_counts = loan_customer_merged['customer_merge_indicator'].value_counts()
    stats['loan_with_customer_match'] = customer_merge_counts.get('both', 0)
    stats['loan_without_customer_match'] = customer_merge_counts.get('left_only', 0)
    
    print(f"Loan records with customer match: {stats['loan_with_customer_match']:,}")
    print(f"Loan records without customer match: {stats['loan_without_customer_match']:,}")
    print("-" * 50)
    
    # Step 2: Merge with guarantor data on disbursement_id
    print("Step 2: Merging with Guarantor data...")
    
    # Check if guarantor_df is None before proceeding
    if guarantor_df is None:
        print("WARNING: guarantor_df is None - Skipping guarantor merge")
        print("Proceeding to negative data processing...")
        
        # Clean up merge indicators for intermediate result
        intermediate_result = loan_customer_merged.drop(['customer_merge_indicator'], axis=1)
        
        # Add a flag to indicate no guarantor data
        intermediate_result['has_guarantor'] = False
        
    else:
        # Proceed with guarantor merge if guarantor_df is not None
        try:
            # Check unique disbursement_ids
            loan_disbursements = set(loan_customer_merged['disbursement_id'].unique())
            guarantor_disbursements = set(guarantor_df['disbursement_id'].unique())
            
            stats['unique_disbursements_in_loan'] = len(loan_disbursements)
            stats['unique_disbursements_in_guarantor'] = len(guarantor_disbursements)
            stats['disbursement_match_overlap'] = len(loan_disbursements.intersection(guarantor_disbursements))
            stats['disbursements_only_in_loan'] = len(loan_disbursements - guarantor_disbursements)
            stats['disbursements_only_in_guarantor'] = len(guarantor_disbursements - loan_disbursements)
            
            print(f"Unique disbursements in loan data: {stats['unique_disbursements_in_loan']:,}")
            print(f"Unique disbursements in guarantor data: {stats['unique_disbursements_in_guarantor']:,}")
            print(f"Disbursement ID overlap: {stats['disbursement_match_overlap']:,}")
            print(f"Disbursements only in loan data: {stats['disbursements_only_in_loan']:,}")
            print(f"Disbursements only in guarantor data: {stats['disbursements_only_in_guarantor']:,}")
            
            # Perform final merge
            final_merged = pd.merge(
                loan_customer_merged,
                guarantor_df,
                on='disbursement_id',
                how='left',  # Keep all loan records
                indicator='guarantor_merge_indicator'
            )
            print(f"columns:",final_merged.columns)
            # Count final merge results
            guarantor_merge_counts = final_merged['guarantor_merge_indicator'].value_counts()
            stats['loan_with_guarantor_match'] = guarantor_merge_counts.get('both', 0)
            stats['loan_without_guarantor_match'] = guarantor_merge_counts.get('left_only', 0)
            
            print(f"Loan records with guarantor match: {stats['loan_with_guarantor_match']:,}")
            print(f"Loan records without guarantor match: {stats['loan_without_guarantor_match']:,}")
            print("-" * 50)
            
            # Overall statistics
            stats['final_merged_records'] = len(final_merged)
            stats['complete_records'] = len(final_merged[
                (final_merged['customer_merge_indicator'] == 'both') & 
                (final_merged['guarantor_merge_indicator'] == 'both')
            ])
            stats['loan_customer_only'] = len(final_merged[
                (final_merged['customer_merge_indicator'] == 'both') & 
                (final_merged['guarantor_merge_indicator'] == 'left_only')
            ])
            stats['loan_only'] = len(final_merged[
                (final_merged['customer_merge_indicator'] == 'left_only') & 
                (final_merged['guarantor_merge_indicator'] == 'left_only')
            ])
            
            print("Final Summary:")
            print(f"Total merged records: {stats['final_merged_records']:,}")
            print(f"Complete records (loan + customer + guarantor): {stats['complete_records']:,}")
            print(f"Loan + Customer only: {stats['loan_customer_only']:,}")
            print(f"Loan only: {stats['loan_only']:,}")
            
            # Return ALL loan records (including those without guarantor matches)
            intermediate_result = final_merged[
                final_merged['customer_merge_indicator'] == 'both'
            ].copy()
            
            print(f"columns:",intermediate_result.columns)
            # Clean up merge indicators
            intermediate_result = intermediate_result.drop(['customer_merge_indicator', 'guarantor_merge_indicator'], axis=1)
            
            # Add a flag to indicate which records have guarantor data
            if len(guarantor_df.columns) > 0:
                intermediate_result['has_guarantor'] = intermediate_result.apply(
                    lambda row: not pd.isna(row[guarantor_df.columns[0]]), 
                    axis=1
                )
            else:
                intermediate_result['has_guarantor'] = False
                
        except Exception as e:
            # Provide detailed error information
            error_msg = f"Error during guarantor merge: {str(e)}"
            print(f"ERROR: {error_msg}")
            print(f"guarantor_df type: {type(guarantor_df)}")
            print(f"guarantor_df is None: {guarantor_df is None}")
            if guarantor_df is not None:
                print(f"guarantor_df shape: {guarantor_df.shape}")
                print(f"guarantor_df columns: {list(guarantor_df.columns)}")
            raise ValueError(f"{error_msg}. guarantor_df status: {guarantor_df is not None}")
    
    # Step 3: Process negative data
    print("Step 3: Processing Negative data...")
    
    if negative_df is not None and len(negative_df) > 0:
        # Get unique disbursement IDs from negative data
        negative_disbursements = set(negative_df['DisbursementID'].unique())
        
        # Create a mapping of disbursement_id to reply message
        negative_mapping = negative_df.set_index('DisbursementID')['Reply Message'].to_dict()
        
        # Count matches in intermediate result
        result_disbursements = set(intermediate_result['disbursement_id'].unique())
        negative_matches = negative_disbursements.intersection(result_disbursements)
        
        stats['negative_disbursements'] = len(negative_disbursements)
        stats['negative_matches_in_result'] = len(negative_matches)
        
        print(f"Unique disbursements in negative data: {stats['negative_disbursements']:,}")
        print(f"Negative disbursements found in result: {stats['negative_matches_in_result']:,}")
        
        # Apply negative data processing
        if len(negative_matches) > 0:
            # Create a column to track original overdue amount for reporting
            intermediate_result['original_amount_overdue'] = intermediate_result['amount_overdue'].copy()
            
            # Set amount_overdue to 0 for disbursements in negative data
            mask = intermediate_result['disbursement_id'].isin(negative_disbursements)
            intermediate_result.loc[mask, 'amount_overdue'] = 0
            
            # Add reply message to a new column for negative processing
            intermediate_result['negative_reply_message'] = intermediate_result['disbursement_id'].map(negative_mapping)
            
            print(f"Set overdue amount to 0 for {mask.sum():,} records based on negative data")
            stats['records_with_overdue_zeroed'] = mask.sum()
        else:
            stats['records_with_overdue_zeroed'] = 0
            intermediate_result['negative_reply_message'] = None
            
        print("-" * 50)
    else:
        print("WARNING: negative_df is None or empty - Skipping negative data processing")
        stats['negative_disbursements'] = 0
        stats['negative_matches_in_result'] = 0
        stats['records_with_overdue_zeroed'] = 0
        intermediate_result['negative_reply_message'] = None
        print("-" * 50)
    
    # Step 4: Add branch names to final result if not already present
    if bank_mst_id is not None and 'branch_name' not in intermediate_result.columns:
        print("Step 4: Adding branch names to final merged data...")
        intermediate_result = add_branch_names_to_dataframe(intermediate_result, bank_mst_id)
    
    # Final result preparation
    final_result = intermediate_result
    
    print(f"Returning {len(final_result):,} total loan records:")
    if 'has_guarantor' in final_result.columns:
        print(f"  - With guarantor data: {final_result['has_guarantor'].sum():,}")
        print(f"  - Without guarantor data: {(~final_result['has_guarantor']).sum():,}")
    if 'records_with_overdue_zeroed' in stats:
        print(f"  - With overdue amount zeroed (negative data): {stats['records_with_overdue_zeroed']:,}")
    if 'branch_name' in final_result.columns:
        mapped_branches = final_result['branch_name'].notna().sum()
        print(f"  - With branch names mapped: {mapped_branches:,}")
    
    return final_result

def convert_frequency(freq):
    if pd.isna(freq):
        return 30  # Default to 30 days (Monthly) for null values
    
    freq_str = str(freq).upper().strip()
    
    # Handle numeric frequencies (assume they represent some interval)
    if freq_str.replace('.0', '').replace('.', '').isdigit():
        freq_num = float(freq_str)
        if freq_num == 0 or freq_num == 1:
            return 30  # Default for 0 or 1 (Monthly)
        elif freq_num <= 7:
            return 7   # Weekly
        elif freq_num <= 15:
            return 15  # Fortnightly
        elif freq_num <= 30:
            return 30  # Monthly
        elif freq_num <= 90:
            return 90  # Quarterly
        elif freq_num <= 180:
            return 180 # Half-yearly
        else:
            return 365 # Yearly
    
    # Handle text frequencies
    frequency_mapping = {
        'M': 30,        # Monthly
        'MONTHLY': 30,
        'Q': 90,        # Quarterly
        'QUARTERLY': 90,
        'H': 180,       # Half-yearly
        'HALF-YEARLY': 180,
        'HALF YEARLY':180,
        'Y': 365,       # Yearly
        'YEARLY': 365,
        'W': 7,         # Weekly
        'WEEKLY': 7,
        'F': 15,        # Fortnightly
        'FORTNIGHTLY': 15,
        'U': 30,        # Unknown - default to Monthly
        'UNKNOWN': 30,
        '': 30,         # Empty - default to Monthly
        'NAN': 30,       # NaN - default to Monthly
        "ON EXPIRY":0
    }
    
    return frequency_mapping.get(freq_str)  # Default to 30 if not found

def safe_get_column(df, column_name, default_value=None):
    """
    Safely get column from DataFrame and handle NaT values
    """
    if column_name not in df.columns:
        return default_value
    
    column_data = df[column_name].copy()
    
    # Handle datetime columns specifically
    if column_data.dtype == 'datetime64[ns]' or 'datetime' in str(column_data.dtype):
        # Replace NaT with None
        column_data = column_data.where(pd.notnull(column_data), None)
    elif column_data.dtype == 'object':
        # Handle string representations of NaT
        column_data = column_data.replace({'NaT': None, 'nan': None, 'NaN': None, '': None})
    
    # Special handling for branchcode to remove .0
    if column_name == 'branch_cd' or column_name == 'customer_id':
        column_data = column_data.apply(lambda x: str(x).rstrip('.0') if pd.notnull(x) and str(x).endswith('.0') else x)
    elif column_name == 'branch_nm':
        column_data = column_data.apply(lambda x: x.strip() if isinstance(x, str) else x)
    return column_data


def process_warna_negative_customer_data(df):
    df['DisbursementID'] = df['disbursementid'].astype(str)
    df['Reply Message'] = df['Reply Message'].apply(lambda x: str(x).upper() if pd.notna(x) else '')

    return df

def process_warna_gurantor_data(df):
    df["disbursement_id"] = df["disbursement_id"].astype(str)
    df['guarantor_name'] = df['guarantor_name'].apply(lambda x: clean_name(x).upper())
    if 'guarantor_contact' in df.columns:
        df['guarantor_contact'] = df['guarantor_contact'].apply(clean_mobile_number)
    df['relation'] = df['relation'].astype(str)
    df=df[['disbursement_id','guarantor_name','guarantor_contact']]

    return df


def calculate_days_past_due(date_value):
    """
    Calculate days past due from a date value.
    If date_value is empty/null, return 0.
    If date_value is a valid date, calculate difference from current date.
    """
    if pd.isna(date_value) or date_value == '' or date_value is None:
        return 0
    
    try:
        # Convert to datetime if it's a string
        if isinstance(date_value, str):
            # Handle the format like '2025-05-06'
            past_due_date = pd.to_datetime(date_value, errors='coerce')
        else:
            past_due_date = pd.to_datetime(date_value, errors='coerce')
        
        # If conversion failed, return 0
        if pd.isna(past_due_date):
            return 0
        
        # Calculate difference from current date
        current_date = datetime.now().date()
        if hasattr(past_due_date, 'date'):
            past_due_date = past_due_date.date()
        
        # Calculate days difference
        days_diff = (current_date - past_due_date).days
        
        # Return positive days (past due should be positive)
        return max(0, days_diff)
        
    except Exception as e:
        print(f"Error calculating days past due for value {date_value}: {e}")
        return 0

def process_warna_raw_data(df1):
    db_operation_success = False
    db_error_message = None
    df = pd.DataFrame()
    
    # Use safe_get_column function to handle missing columns
    df['customerid'] = safe_get_column(df1, 'customer_id')
    df['disbursementid'] = safe_get_column(df1, 'disbursement_id')
    df['mobileno'] = safe_get_column(df1, 'mobile')
    df['loantype'] = safe_get_column(df1, 'account_type')
    df['customername'] = safe_get_column(df1, 'customer_name')
    
    # Fix: Use 'disbursed_amount' instead of 'disbursement_amount'
    df['disbursementamount'] = safe_get_column(df1, 'disbursed_amount')
    df['rateofinterest'] = safe_get_column(df1, 'rate_of_interest')
    df['repaymenttenure'] = safe_get_column(df1, 'repayment_tenure')
    df['emiamount'] = safe_get_column(df1, 'emi_amount')
    
    # Handle payment frequency with safe column access
    if 'payment_frequency' in df1.columns:
        df["paymentfrequency"] = df1["payment_frequency"].apply(convert_frequency).astype('Int64')
    else:
        df["paymentfrequency"] = 30
    
    # FIX: Calculate days past due from date values
    past_due_raw = safe_get_column(df1, 'no_of_days_past_dues')
    if past_due_raw is not None:
        df['numberofdayspastdue'] = past_due_raw.apply(calculate_days_past_due)
    else:
        df['numberofdayspastdue'] = 0
    
    df['inststartdate'] = safe_get_column(df1, 'ins_start_dt')
    df['collectionofficerid'] = None
    df['collectionofficername'] = None
    df['branchname'] = safe_get_column(df1, 'branch_name')
    df['branchcode'] = safe_get_column(df1,'branch_cd')
    df['applicanttype'] = None
    
    # Handle overdue amount with negative data processing
    original_overdue = safe_get_column(df1, 'amount_overdue')
    if original_overdue is not None:
        df['overdueamount'] = original_overdue
    else:
        df['overdueamount'] = 0
    
    # Check if negative data processing was applied and use that overdue amount
    if 'amount_overdue' in df1.columns:
        df['overdueamount'] = safe_get_column(df1, 'amount_overdue')
    
    df['totaloutstanding'] = None
    df['principlecollected'] = None
    df['interestcollected'] = None
    df['collectedamount'] = safe_get_column(df1, 'latest_collected_amt')
    df['collectiondate'] = safe_get_column(df1, 'date_of_last_payment')
    df['pos'] = None
    df['bankmstid'] = 36
    df['inserted_date'] = datetime.now().date()
    df['gender'] = safe_get_column(df1, 'customer_gender')
    df['dateofbirth'] = safe_get_column(df1, 'customer_date_of_birth')
    df['disbursementdate'] = safe_get_column(df1, 'disbursed_dt')
    df['loanclassification'] = None
    df['lastpaymentdate'] = safe_get_column(df1, 'date_of_last_payment')
    df['lastcollectedamount'] = safe_get_column(df1, 'latest_collected_amt')
    df['currentbalance'] = None
    df['interestoutstanding'] = None
    df['totalpending'] = None
    df['principlepending'] = None
    df['interestpending'] = None
    df['closingdate'] = safe_get_column(df1, 'date_closed')
    df['previousemidate'] = None
    df['extracolumn1'] = safe_get_column(df1, 'npa_dt')
    df['extracolumn2'] = safe_get_column(df1, 'loan_cc')
    
    # Handle extracolumn3 with NPA status and negative data message
    npa_date_column = safe_get_column(df1, 'npa_dt')
    if npa_date_column is not None:
        df['extracolumn3'] = npa_date_column.apply(lambda x: "NPA" if pd.notna(x) and x != '' else "NOTNPA")
    else:
        df['extracolumn3'] = "NOTNPA"
    
    df['extracolumn4'] = safe_get_column(df1,'negative_reply_message')
    df['extracolumn5'] = pd.Timestamp.now().date() - pd.Timedelta(days=1)
    
    # FIX: Use safe_get_column for guarantor data from df1, not df
    df['guarantor'] = safe_get_column(df1, 'guarantor_name')
    df['guarantor_mobile'] = safe_get_column(df1, 'guarantor_contact')
    
    # Add secondary_user_type column with value 'guarantor' only for customers who have a guarantor
    df['secondary_user_type'] = df['guarantor'].apply(
        lambda x: 'guarantor' if pd.notna(x) and x != '' and x is not None else None
    )
    
    # ENHANCED FIX: Comprehensive NaT/NaN handling
    def clean_datetime_value(value):
        """Clean datetime values to handle NaT, NaN, and invalid formats"""
        if pd.isna(value) or value is pd.NaT:
            return None
        if isinstance(value, str):
            if value.lower() in ['nat', 'nan', 'none', '']:
                return None
            try:
                # Try to parse the string as datetime
                return pd.to_datetime(value)
            except:
                return None
        return value
    
    # Apply datetime cleaning to all datetime columns
    datetime_columns = [
        'inststartdate', 'collectiondate', 'dateofbirth', 'disbursementdate',
        'lastpaymentdate', 'closingdate', 'previousemidate', 'extracolumn1'
    ]
    
    for col in datetime_columns:
        if col in df.columns:
            # Apply the cleaning function
            df[col] = df[col].apply(clean_datetime_value)
    
    # Additional safety: Handle any remaining NaN/NaT values in the entire DataFrame
    df = df.replace({pd.NaT: None, np.nan: None})
    
    # Convert object columns that might contain 'NaT' strings
    for col in df.columns:
        if df[col].dtype == 'object':
            df[col] = df[col].replace({'NaT': None, 'nan': None, 'NaN': None})
    
    # Debug: Print sample of problematic columns before insertion
    print("Sample datetime values before insertion:")
    for col in datetime_columns:
        if col in df.columns:
            sample_values = df[col].head(5).tolist()
            print(f"{col}: {sample_values}")
    
    # Count records with overdue amount set to zero due to negative data
    zero_overdue_count = 0
    if 'negative_reply_message' in df1.columns:
        zero_overdue_count = df1['negative_reply_message'].notna().sum()
    
    try:
        result = insert_data_in_raw_table(df, bank_id=36)
        db_operation_success = result['success']
        if not result['success']:
            db_error_message = result['message']
    except Exception as e:
        db_operation_success = False
        db_error_message = str(e)
        print(f"Database update failed for warna data: {e}")
        
    # Additional debugging: Check for problematic values
    print("\nDebugging datetime columns for NaT values:")
    for col in datetime_columns:
        if col in df.columns:
            nat_count = df[col].isna().sum()
            nat_string_count = (df[col].astype(str) == 'NaT').sum()
            print(f"{col}: {nat_count} NaT/None values, {nat_string_count} 'NaT' strings")

    guarantor_count = len(df[df['guarantor'].notna() & (df['guarantor'] != '') & (df['guarantor'] != None)])
    without_guarantor_count = len(df) - guarantor_count

    # ========== START BACKGROUND POST-PROCESSING ==========
    
    if db_operation_success:
        # Start post-processing in background thread
        bank_id=36
        background_thread = threading.Thread(
            target=run_post_processing_in_background,
            args=(bank_id,),
            daemon=True
        )
        background_thread.start()
        print(f"Post-processing started in background thread for bank_id: {bank_id}")
    else:
        print(f"Skipping post-processing due to database operation failure: {db_error_message}")
    
    response = {
        'total records': len(df),
        'Records with guarantors': guarantor_count,
        'Records without guarantors': without_guarantor_count,
        'Records with overdue amount zeroed': zero_overdue_count,
        'db_operation_success': db_operation_success,
        'db_error_message': db_error_message,
        "New Records": result['inserted_count'] if 'result' in locals() and result else 0,
        "Duplicates in DB": result['duplicate_count'] if 'result' in locals() and result else 0,
        'message': result['message'] if 'result' in locals() and result else db_error_message
    }
    
    return response
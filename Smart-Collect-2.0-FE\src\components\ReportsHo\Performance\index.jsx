import { Table, Typography, Flex } from "antd";
import React, { useState } from "react";
import ASC_IMG from "../../../assets/Images/asce.svg";
import DESC_IMG from "../../../assets/Images/desc.svg";
import { formatAmount, handleDateFormatter } from "../../../constant";
import Style from "./_style.module.scss";
import PropTypes from "prop-types";

const { Text } = Typography;

export default function Performance({ performanceData }) {
  const [sortedData, setSortedData] = useState([]);
  const dataSource = Array.isArray(performanceData)
    ? performanceData.map((d, i) => ({
        key: i + 1,
        ...d,
      }))
    : [];

  const handleSort = ({ key, direction }) => {
    let sortedArray = [...(sortedData.length ? sortedData : dataSource)];

    sortedArray.sort((a, b) => {
      if (direction === "asc") {
        return a[key] > b[key] ? 1 : -1;
      } else {
        return a[key] < b[key] ? 1 : -1;
      }
    });

    setSortedData(sortedArray);
  };

  const renderSortArrow = ({ columnKey, columnName }) => {
    return (
      <Flex justify="center" gap={4}>
        <Text className={Style.text}>{columnName}</Text>
        <Flex gap={3}>
          <button
            className={Style.icon}
            onClick={handleSort.bind(null, {
              key: columnKey,
              direction: "desc",
            })}
          >
            <img src={DESC_IMG} alt="desc" />
          </button>
          <button
            className={Style.icon}
            onClick={handleSort.bind(null, {
              key: columnKey,
              direction: "asc",
            })}
          >
            <img src={ASC_IMG} alt="asc" />
          </button>
        </Flex>
      </Flex>
    );
  };

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      render: (_, data) => <Text>{data.key}.</Text>,
    },
    {
      title: renderSortArrow({
        columnKey: "asOnDate",
        columnName: "As On Date",
      }),
      dataIndex: "asOnDate",
      render: (_, { asOnDate }) => (
        <Text>{asOnDate ? handleDateFormatter(asOnDate) : "--"}</Text>
      ),
    },
    {
      title: renderSortArrow({
        columnKey: "BranchName",
        columnName: "Branch Name",
      }),
      dataIndex: "BranchName",
      render: (_, { BranchName }) => <Text>{BranchName}</Text>,
    },
    {
      title: renderSortArrow({
        columnKey: "customerId",
        columnName: "Customer ID",
      }),
      dataIndex: "customerId",
      render: (_, { customerId }) => (
        <Text>{customerId ?? "-"}</Text>
      ),
    },
    {
      title: renderSortArrow({
        columnKey: "disbursementId",
        columnName: "Disbursement ID",
      }),
      dataIndex: "disbursementId",
      render: (_, { disbursementId }) => (
        <Text>{disbursementId ?? "-"}</Text>
      ),
    },
    {
      title: renderSortArrow({
        columnKey: "overdueAmount",
        columnName: "Overdue Amount",
      }),
      dataIndex: "overdueAmount",
      render: (_, { overdueAmount }) => (
        <Text className={Style.blue_text}>
          ₹ {formatAmount(overdueAmount || 0)}
        </Text>
      ),
    },
    {
      title: renderSortArrow({
        columnKey: "communications",
        columnName: "Communications",
      }),
      dataIndex: "communications",
      render: (_, { communications }) => (
        <Text>{communications ?? "-"}</Text>
      ),
    },
    {
      title: renderSortArrow({
        columnKey: "total_collection",
        columnName: "Total Collection",
      }),
      dataIndex: "total_collection",
      render: (_, { total_collection }) => <Text>{total_collection}</Text>,
    },
    {
      title: renderSortArrow({
        columnKey: "total_ptp",
        columnName: "Total PTP Taken",
      }),
      dataIndex: "total_ptp",
      render: (_, { total_ptp }) => <Text>{total_ptp}</Text>,
    },
  ];

  return (
    <Table
      virtual
      className={Style.customTable}
      columns={columns}
      dataSource={sortedData.length ? sortedData : dataSource}
      scroll={{
        x: 1500,
        y: 300,
      }}
      pagination={{
        showSizeChanger: false,
      }}
    />
  );
}

Performance.propTypes={
  performanceData: PropTypes.array
}
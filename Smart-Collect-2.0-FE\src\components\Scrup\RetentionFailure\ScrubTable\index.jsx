import { Table, Typography } from "antd";
import React, { useEffect, useState } from "react";
import Style from "./_style.module.scss";
import { formatDigits } from "../../../../constant";
import PropTypes from "prop-types";

const {Text} = Typography;

export function ScrubTable({ 
  isGraph, 
  tableData, 
  selectedRetentionCard, 
  selectedState,
  setSelectedState,
  tableLevel, 
  setTableLevel,
  handleUpdateStateTable }) {
  const [data, setData] = useState([]);

  // Handle State Click → Get Regions
  const handleStateClick = (stateId) => {
    setSelectedState(stateId);
    setTableLevel("region");
    handleUpdateStateTable({ 
      stateId, 
      activeInactive: selectedRetentionCard, 
      tablewise:"State" 
    });
  };

  // Handle Region Click → Get Branches
  const handleRegionClick = (regionId) => {
    setTableLevel("branch");
    handleUpdateStateTable({ 
      stateId:selectedState, 
      regionId, 
      tablewise:"Region", 
      activeInactive: selectedRetentionCard
    });
  };

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      render: (_, data) => <Text>{data?.key}.</Text>,
    },
    isGraph
      ? null
      : {
          title: "State",
          dataIndex: "State",
          sorter: (a, b) => a.State?.localeCompare(b.State),
          render: (State, data) => (
            <Text
              className={Style.blueText}
              onClick={() =>handleStateClick(data?.State_id)}
            > {State || "--"} </Text>
          ),
        },
    ...(selectedState
      ? [
          {
            title: "Region",
            dataIndex: "Region",
            sorter: (a, b) => a.Region?.localeCompare(b.Region),
            render: (Region, data) => (
              <Text
                className={Style.blueText}
                onClick={() => handleRegionClick(data?.Region_id)}
              > {Region || "--"} </Text>
            ),
          },
        ]
      : []),
    ...(tableLevel === "branch" && selectedState
      ? [
          {
            title: "Branch",
            dataIndex: "Branch",
            sorter: (a, b) => a.Branch?.localeCompare(b.Branch),
            render: (Branch) => (
              <Text
                className={Style.blueText}
                // onClick={() => handleBranchClick(Branch)}
              >
                {Branch || "--"}
              </Text>
            ),
          },
        ]
      : []),
    {
      title: "Loan",
      children: [
        {
          title: "Gold",
          dataIndex: "gold_loan_count",
          sorter: (a, b) => a.gold_loan_count?.localeCompare(b.gold_loan_count),
          render: (value) => <Text className={Style.lan}>{formatDigits(value || 0)}</Text>,
        },
        {
          title: "House",
          dataIndex: "house_loan_count",
          sorter: (a, b) =>{
            console.log(a, b);
          },
          render: (value) => <Text className={Style.lan}>{formatDigits(value || 0)}</Text>,
        },
      ],
    },
  ].filter(Boolean);

  const dataSource = Array.isArray(data)
  ? data.map((data, i) => ({
      key: i + 1,
      ...data,
    }))
  : [];

  useEffect(()=>{
    setData(tableData);
  },[tableData]);
  return (
      <Table
        bordered
        virtual
        className={Style.custom_table}
        columns={columns}
        dataSource={dataSource}
        scroll={{
          x: 600,
          y: 460,
        }}
        pagination={{
          showSizeChanger: false,
        }}
      />
  );
}

ScrubTable.propTypes={
  isGraph: PropTypes.bool, 
  tableData: PropTypes.array, 
  selectedRetentionCard: PropTypes.string, 
  selectedState: PropTypes.any,
  setSelectedState: PropTypes.func,
  tableLevel: PropTypes.string, 
  setTableLevel: PropTypes.func,
  handleUpdateStateTable: PropTypes.func
}
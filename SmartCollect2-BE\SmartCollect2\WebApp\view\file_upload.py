import logging
import time
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from rest_framework.response import Response
from WebApp.ETL_for_banks.Ghoti.summary import *
from WebApp.ETL_for_banks.shakti_finance.summary import *
from WebApp.ETL_for_banks.sindhudhurg_bank.summary import process_sindhudhurg_raw_data
from WebApp.ETL_for_banks.bssbihar.summary import *
from WebApp.ETL_for_banks.rajapur.summary import process_rajapur_raw_data
from WebApp.ETL_for_banks.SmartFair.summary import process_smartfair_raw_data
from WebApp.ETL_for_banks.shareindia.summary import process_shareindia_raw_data
from WebApp.ETL_for_banks.artha_sidhi.summary import *
from WebApp.ETL_for_banks.pavana.summary import process_pavana_overdue_data
from WebApp.ETL_for_banks.vinayana.summary import process_vinayana_raw_data
from WebApp.ETL_for_banks.required_columns import *
from WebApp.ETL_for_banks.warna.summary import *
from WebApp.ETL_for_banks.generate_summary import generate_summary_response
from WebApp.ETL_for_banks.ShrimantMalojiraje.summary import process_shrimantmalojiraje_raw_data
from WebApp.ETL_for_banks.swami_samarth.summary import *
from WebApp.ETL_for_banks.tuljabhavani.summary import process_tulja_raw_data

# Configure ETL-specific logger
etl_logger = logging.getLogger('ETL')
etl_logger.setLevel(logging.INFO)

# Create file handler for ETL logs if it doesn't exist
if not etl_logger.handlers:
    import os
    
    # Define the logs directory path
    logs_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs')
    
    # Create the logs directory if it doesn't exist
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir)
    
    # Create the full path for the ETL log file
    etl_log_file = os.path.join(logs_dir, 'etl_logs.log')
    
    etl_handler = logging.FileHandler(etl_log_file)
    etl_handler.setLevel(logging.INFO)
    
    # Create formatter
    etl_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    etl_handler.setFormatter(etl_formatter)
    etl_logger.addHandler(etl_handler)
    
    # Log the initialization
    etl_logger.info(f"ETL Logger initialized. Log file: {etl_log_file}")

class CollectionFileUpload(APIView):
    permission_classes = (IsAuthenticated,)
    
    def get(self, request):
        """
        GET endpoint to return available file types based on user's bank ID
        
        Returns:
            Response: JSON response with available file types
        """
        etl_logger.info("ETL GET request initiated")
        try:
            bank_id = request.user.BankMstID_id
            etl_logger.info(f"ETL - Retrieved bank_id: {bank_id} for user: {request.user}")
            
            if not bank_id:
                etl_logger.error("ETL - User bank ID not found")
                raise ValueError("User bank ID not found")
                
            etl_logger.info(f"ETL - Fetching available file types for bank_id: {bank_id}")
            available_file_types = get_available_file_types(bank_id)
            
            if not available_file_types:
                etl_logger.warning(f"ETL - No file types available for bank ID {bank_id}")
                return Response({
                    "message": f"No file types available for bank ID {bank_id}",
                    "status": "info",
                    "data": []
                }, status=200)
            
            etl_logger.info(f"ETL - Successfully retrieved file types for bank_id: {bank_id}, types: {available_file_types}")
            return Response({
                "message": "Available file types retrieved successfully",
                "status": "success",
                "bank_id": bank_id,
                "data": available_file_types
            }, status=200)
            
        except ValueError as e:
            etl_logger.error(f"ETL - ValueError in GET request: {str(e)}")
            return Response({
                "message": f"Invalid user data: {str(e)}",
                "status": "error",
                "error_code": "INVALID_USER_DATA"
            }, status=400)
        except Exception as e:
            etl_logger.error(f"ETL - Unexpected error in GET request: {str(e)}")
            return Response({
                "message": "Error retrieving available file types",
                "error": str(e),
                "status": "error",
                "error_code": "INTERNAL_SERVER_ERROR"
            }, status=500)

    def post(self, request):
        """
        POST endpoint to handle single or multiple file uploads
        """
        etl_logger.info("ETL POST request initiated - File upload started")
        start_time = time.time()
        
        try:
            # Step 1: Validate and get required parameters
            etl_logger.info("ETL - Step 1: Validating and getting file type")
            type_of_file = self._validate_and_get_file_type(request)
            etl_logger.info(f"ETL - File type validated: {type_of_file}")
            
            etl_logger.info("ETL - Step 1: Validating and getting bank ID")
            bank_id = self._validate_and_get_bank_id(request)
            etl_logger.info(f"ETL - Bank ID validated: {bank_id}")
            
            print(f"Processing files for bank: (ID: {bank_id}), file type: {type_of_file}")
            etl_logger.info(f"ETL - Processing files for bank: (ID: {bank_id}), file type: {type_of_file}")
            
            # Step 2: Validate file type for this bank
            etl_logger.info("ETL - Step 2: Validating file type for bank")
            self._validate_file_type_for_bank(bank_id, type_of_file)
            etl_logger.info(f"ETL - File type {type_of_file} validated for bank {bank_id}")
            
            # Step 3: Get and validate uploaded files
            etl_logger.info("ETL - Step 3: Getting and validating uploaded files")
            files = self._validate_and_get_files(request)
            etl_logger.info(f"ETL - Files validated: {len(files)} files received")
            
            # Step 4: Process all files
            etl_logger.info("ETL - Step 4: Processing all files")
            processing_results = self._process_all_files(files, bank_id, type_of_file)
            etl_logger.info("ETL - All files processed successfully")
            
            # Step 5: Generate and return response
            etl_logger.info("ETL - Step 5: Generating final response")
            response = self._generate_final_response(processing_results, type_of_file, bank_id)
            
            end_time = time.time()
            processing_time = end_time - start_time
            etl_logger.info(f"ETL - POST request completed successfully in {processing_time:.2f} seconds")
            
            return response
            
        except (FileValidationError, UnsupportedFileTypeError, ColumnMissingError, 
                EmptyFileError, DataProcessingError) as e:
            etl_logger.error(f"ETL - Known error occurred: {type(e).__name__}: {str(e)}")
            return Response({
                "message": str(e),
                "status": "error",
                "error_code": e.error_code
            }, status=e.status_code)
        except ValueError as e:
            etl_logger.error(f"ETL - ValueError in POST request: {str(e)}")
            return Response({
                "message": f"Invalid input data: {str(e)}",
                "status": "error",
                "error_code": "INVALID_INPUT"
            }, status=400)
        except Exception as e:
            error_message = str(e)
            etl_logger.error(f"ETL - Unexpected error in POST request: {error_message}")
            print(f"DEBUG: Unexpected error: {error_message}")
            import traceback
            etl_logger.error(f"ETL - Traceback: {traceback.format_exc()}")
            print(f"DEBUG: Traceback: {traceback.format_exc()}")
            
            # Check if it's a pandas-related error
            if any(keyword in error_message.lower() for keyword in ['not in index', 'keyerror', 'column', 'dataframe']):
                etl_logger.error(f"ETL - Pandas/DataFrame error detected: {error_message}")
                return Response({
                    "message": f"Data processing error - column or structure issue: {error_message}",
                    "status": "error",
                    "error_code": "COLUMN_STRUCTURE_ERROR"
                }, status=422)
            
            return Response({
                "message": f"An unexpected error occurred during file processing: {error_message}",
                "status": "error",
                "error_code": "UNEXPECTED_ERROR"
            }, status=500)

    def _validate_and_get_file_type(self, request):
        """Validate and return file type from request"""
        etl_logger.info("ETL - Validating file type from request")
        try:
            type_of_file = request.data.get('type_of_file')
            etl_logger.info(f"ETL - Raw file type from request: {type_of_file}")
            
            if not type_of_file:
                etl_logger.error("ETL - type_of_file parameter is missing")
                raise FileValidationError(
                    "type_of_file is required!",
                    error_code="MISSING_PARAMETER"
                )
            
            type_of_file_lower = type_of_file.lower()
            etl_logger.info(f"ETL - File type processed: {type_of_file_lower}")
            return type_of_file_lower
        except Exception as e:
            etl_logger.error(f"ETL - Error processing file type parameter: {str(e)}")
            if isinstance(e, FileValidationError):
                raise
            raise ValueError(f"Error processing file type parameter: {str(e)}")

    def _validate_and_get_bank_id(self, request):
        """Validate and return bank ID from user"""
        etl_logger.info("ETL - Validating bank ID from user")
        try:
            bank_id = request.user.BankMstID_id
            etl_logger.info(f"ETL - Bank ID retrieved from user: {bank_id}")
            
            if not bank_id:
                etl_logger.error("ETL - User bank ID not found or invalid")
                raise ValueError("User bank ID not found or invalid")
            return bank_id
        except Exception as e:
            etl_logger.error(f"ETL - Error retrieving user bank information: {str(e)}")
            raise ValueError(f"Error retrieving user bank information: {str(e)}")

    def _validate_file_type_for_bank(self, bank_id, type_of_file):
        """Validate if file type is allowed for the given bank"""
        etl_logger.info(f"ETL - Validating file type '{type_of_file}' for bank {bank_id}")
        try:
            available_file_types_response = get_available_file_types(bank_id)
            etl_logger.info(f"ETL - Available file types response: {available_file_types_response}")
            
            if available_file_types_response is None:
                etl_logger.error(f"ETL - No file types configured for bank ID {bank_id}")
                raise FileValidationError(
                    f"No file types configured for bank ID {bank_id}",
                    error_code="NO_FILE_TYPES_CONFIGURED"
                )
            
            # Handle different response formats from get_available_file_types
            available_type_names = []
            if isinstance(available_file_types_response, dict):
                if "file_types" in available_file_types_response:
                    available_type_names = [ft.lower() for ft in available_file_types_response["file_types"]]
                elif "file_type" in available_file_types_response:
                    available_type_names = [ft.lower() for ft in available_file_types_response["file_type"]]
            
            etl_logger.info(f"ETL - Available type names: {available_type_names}")
            print(f"Available type names: {available_type_names}")
            
            # For combined type, allow any specific file types
            if available_type_names and "combined" in available_type_names:
                allowed_types = available_type_names
            else:
                allowed_types = available_type_names
            
            if allowed_types and type_of_file not in allowed_types:
                etl_logger.error(f"ETL - File type '{type_of_file}' not allowed for bank {bank_id}. Allowed types: {allowed_types}")
                raise UnsupportedFileTypeError(type_of_file, bank_id, allowed_types)
                
            etl_logger.info(f"ETL - File type '{type_of_file}' validated successfully for bank {bank_id}")
                
        except (FileValidationError, UnsupportedFileTypeError):
            raise
        except Exception as e:
            etl_logger.error(f"ETL - Error validating file type for bank {bank_id}: {str(e)}")
            raise DataProcessingError(
                f"Error validating file type for bank {bank_id}: {str(e)}",
                error_code="FILE_TYPE_VALIDATION_ERROR"
            )

    def _validate_and_get_files(self, request):
        """Validate and return uploaded files"""
        etl_logger.info("ETL - Validating and getting uploaded files")
        try:
            files = None
            
            # Try to get multiple files first
            if 'file' in request.FILES:
                file_data = request.FILES.getlist('file')
                etl_logger.info(f"ETL - Found {len(file_data)} files in request")
                
                if len(file_data) == 1:
                    files = file_data  # Keep as list for consistent processing
                    etl_logger.info(f"ETL - Processing single file: {file_data[0].name}")
                    print(f"Processing single file: {file_data[0].name}")
                else:
                    files = file_data  # Multiple files
                    etl_logger.info(f"ETL - Processing {len(file_data)} files")
                    print(f"Processing {len(file_data)} files")
            
            if not files:
                etl_logger.error("ETL - No files uploaded in request")
                raise FileValidationError(
                    "No files uploaded! Please upload at least one file.",
                    error_code="NO_FILES_UPLOADED"
                )
            
            etl_logger.info(f"ETL - Successfully validated {len(files)} files")
            return files
        except FileValidationError:
            raise
        except Exception as e:
            etl_logger.error(f"ETL - Error processing uploaded files: {str(e)}")
            raise FileValidationError(
                f"Error processing uploaded files: {str(e)}",
                error_code="FILE_UPLOAD_ERROR"
            )
    
    def _process_all_files(self, files, bank_id, type_of_file):
        """Process all uploaded files and return results"""
        etl_logger.info(f"ETL - Starting to process {len(files)} files for bank {bank_id}")
        try:
            processing_results = {
                'successful_files': [],
                'failed_files': [],
                'processed_dataframes': {
                    'overdue': None,
                    'demand': None,
                    'recovery': None,
                    'warna_loan': None,
                    'warna_gurantor': None,
                    'warna_customer': None,
                    "ghoti_loan_type": None,
                    "ghoti_od_type": None,
                    'warna_negative':None,
                    'swami_daily':None,
                    'swami_base':None,
                },
                'final_result_data': None
            }
            
            for idx, single_file in enumerate(files):
                etl_logger.info(f"ETL - Processing file {idx + 1}/{len(files)}: {single_file.name}")
                try:
                    print(f"Processing file {idx + 1}/{len(files)}: {single_file.name}")
                    
                    # Determine the file type for this specific file
                    current_file_type = self._determine_current_file_type(single_file, bank_id, type_of_file)
                    etl_logger.info(f"ETL - File type determined: {current_file_type}")
                    print(f"File type determined: {current_file_type}")
                    
                    # Process single file
                    etl_logger.info(f"ETL - Calling process_single_file for {single_file.name}")
                    result = process_single_file(single_file, bank_id, current_file_type)
                    
                    # Check if result is None or doesn't have expected structure
                    if result is None:
                        etl_logger.error(f"ETL - process_single_file returned None for file {single_file.name}")
                        raise DataProcessingError(
                            f"process_single_file returned None for file {single_file.name}",
                            error_code="PROCESS_SINGLE_FILE_FAILED"
                        )
                    
                    # Check if processing was successful
                    if not result.get('success', False):
                        error_msg = result.get('error', 'Unknown error occurred during file processing')
                        etl_logger.error(f"ETL - Failed to process file {single_file.name}: {error_msg}")
                        raise DataProcessingError(
                            f"Failed to process file {single_file.name}: {error_msg}",
                            error_code="FILE_PROCESSING_FAILED"
                        )
                    
                    etl_logger.info(f"ETL - Successfully processed file {single_file.name}, proceeding to dataframe processing")
                    
                    # Process the dataframe based on bank and file type
                    self._process_dataframe_by_bank(result, bank_id, current_file_type, processing_results)
                    
                    processing_results['successful_files'].append(result)
                    etl_logger.info(f"ETL - Successfully processed: {result.get('filename', 'unknown')} as {current_file_type}")
                    print(f"Successfully processed: {result.get('filename', 'unknown')} as {current_file_type}")
                    
                except Exception as file_error:
                    error_message = f"Error processing file {single_file.name}: {str(file_error)}"
                    etl_logger.error(f"ETL - {error_message}")
                    print(error_message)
                    
                    # Check if it's a critical error that should stop processing
                    if any(keyword in str(file_error).lower() for keyword in ['not in index', 'keyerror', 'column', 'dataframe']):
                        etl_logger.error(f"ETL - Critical column/data structure error in file '{single_file.name}': {str(file_error)}")
                        raise DataProcessingError(
                            f"Column or data structure error in file '{single_file.name}': {str(file_error)}",
                            error_code="COLUMN_ERROR"
                        )
                    
                    # For other errors, add to failed files but continue processing
                    failed_result = {
                        'success': False,
                        'filename': single_file.name,
                        'error': str(file_error),
                        'error_code': 'FILE_PROCESSING_ERROR'
                    }
                    processing_results['failed_files'].append(failed_result)
                    etl_logger.warning(f"ETL - Added file {single_file.name} to failed files list, continuing with other files")
            
            # Check if we have any successful files
            if not processing_results['successful_files']:
                if processing_results['failed_files']:
                    first_failed = processing_results['failed_files'][0]
                    etl_logger.error(f"ETL - All {len(processing_results['failed_files'])} files failed to process")
                    raise DataProcessingError(
                        f"All {len(processing_results['failed_files'])} files failed to process. First error: {first_failed.get('error')}",
                        error_code="ALL_FILES_FAILED"
                    )
                else:
                    etl_logger.error("ETL - No files were processed successfully")
                    raise DataProcessingError(
                        "No files were processed successfully",
                        error_code="NO_SUCCESSFUL_PROCESSING"
                    )
            
            etl_logger.info(f"ETL - File processing completed. Successful: {len(processing_results['successful_files'])}, Failed: {len(processing_results['failed_files'])}")
            
            # Merge processed data if needed
            etl_logger.info("ETL - Starting data merging process")
            self._merge_processed_data(bank_id, processing_results)
            etl_logger.info("ETL - Data merging completed")
            
            return processing_results
            
        except (DataProcessingError, FileValidationError):
            raise
        except Exception as e:
            etl_logger.error(f"ETL - Error during file processing: {str(e)}")
            raise DataProcessingError(
                f"Error during file processing: {str(e)}",
                error_code="FILE_PROCESSING_ERROR"
            )
    

    def _determine_current_file_type(self, single_file, bank_id, type_of_file):
        """Determine the current file type based on bank ID and filename"""
        etl_logger.info(f"ETL - Determining file type for {single_file.name}, bank {bank_id}, type {type_of_file}")
        try:
            current_file_type = type_of_file
            filename_lower = single_file.name.lower()
            etl_logger.info(f"ETL - Filename (lowercase): {filename_lower}")
            
            if bank_id == 13:
                etl_logger.info("ETL - Processing file type determination for bank 13 (ArthaSiddhi)")
                if type_of_file == 'combined':
                    if 'demand' in filename_lower:
                        current_file_type = 'demand'
                        etl_logger.info("ETL - File type determined as 'demand' based on filename")
                    elif 'overdue' in filename_lower or 'od' in filename_lower:
                        current_file_type = 'over due'
                        etl_logger.info("ETL - File type determined as 'over due' based on filename")
                    elif 'loan recovery' in filename_lower or 'loanrecoveryreport' in filename_lower:
                        current_file_type = 'loan recovery'
                        etl_logger.info("ETL - File type determined as 'loan recovery' based on filename")
                    else:
                        current_file_type = 'combined'
                        etl_logger.info("ETL - File type remains as 'combined'")
                elif type_of_file == '2pm demand':
                    current_file_type = '2pm demand'
                    etl_logger.info("ETL - File type determined as '2pm demand'")
            elif bank_id == 21:
                current_file_type = 'over due'
                etl_logger.info("ETL - Bank 21 (Pavana): File type set to 'over due'")
            elif bank_id == 23:
                current_file_type = 'basedata'
                etl_logger.info("ETL - Bank 23 (Vinayana): File type set to 'basedata'")
            elif bank_id == 36:
                etl_logger.info("ETL - Processing file type determination for bank 36 (Warna)")
                if type_of_file == 'combined':
                    if 'guarantor' in filename_lower or "gurantor" in filename_lower or 'guaranter' in filename_lower:
                        current_file_type = 'guarantor'
                        etl_logger.info("ETL - File type determined as 'guarantor' based on filename")
                    elif 'customer details' in filename_lower or 'cutomer details' in filename_lower:
                        current_file_type = "customer details"
                        etl_logger.info("ETL - File type determined as 'customer details' based on filename")
                    elif 'loan' in filename_lower:
                        current_file_type = 'loan'
                        etl_logger.info("ETL - File type determined as 'loan' based on filename")
                    elif 'negative' in filename_lower:
                        current_file_type = 'negative'
                        etl_logger.info("ETL - File type determined as 'negative' based on filename")
                    else:
                        current_file_type = "combined"
                        etl_logger.info("ETL - File type remains as 'combined'")
            elif bank_id == 27:
                etl_logger.info("ETL - Processing file type determination for bank 27 (Ghoti)")
                if 'loan type' in filename_lower:
                    current_file_type = 'loan'
                    etl_logger.info("ETL - File type determined as 'loan' based on filename")
                elif "od" in filename_lower or "overdue" in filename_lower or 'over due' in filename_lower:
                    current_file_type = 'over due'
                    etl_logger.info("ETL - File type determined as 'over due' based on filename")
            elif bank_id == 28:
                current_file_type = 'over due'
                etl_logger.info("ETL - Bank 28 (Shakti Finance): File type set to 'over due'")
            elif bank_id == 30 and type_of_file == 'combined':
                current_file_type = 'ATL'
                etl_logger.info("ETL - Bank 30 (Sindhudhurg): File type set to 'ATL'")
            elif bank_id == 32 and type_of_file == 'combined':
                etl_logger.info("ETL - Processing file type determination for bank 32 (BSS Bihar)")
                if 'collection' in filename_lower:
                    current_file_type = 'collection'
                    etl_logger.info("ETL - File type determined as 'collection' based on filename")
                elif 'disbursement' in filename_lower or 'bss data' in filename_lower:
                    current_file_type = 'over due'
                    etl_logger.info("ETL - File type determined as 'over due' based on filename")
            elif bank_id == 369:
                etl_logger.info("ETL - Processing file type determination for bank 369 (Swami Samarth)")
                if type_of_file == 'base':
                    current_file_type = 'base'
                    etl_logger.info("ETL - File type determined as 'base'")
                elif type_of_file == 'daily':
                    current_file_type = 'daily'
                    etl_logger.info("ETL - File type determined as 'daily'")
            elif bank_id == 378:
                current_file_type = 'default'
                etl_logger.info("ETL - Bank 378 (Tulja Bhavani): File type set to 'default'")
            
            etl_logger.info(f"ETL - Final file type determined: {current_file_type}")
            return current_file_type
        except Exception as e:
            etl_logger.error(f"ETL - Error determining file type for {single_file.name}: {str(e)}")
            raise DataProcessingError(
                f"Error determining file type for {single_file.name}: {str(e)}",
                error_code="FILE_TYPE_DETERMINATION_ERROR"
            )

    def _process_dataframe_by_bank(self, result, bank_id, current_file_type, processing_results):
        """Process dataframe based on bank ID and file type"""
        etl_logger.info(f"ETL - Processing dataframe for bank {bank_id}, file type {current_file_type}")
        try:
            df = result.get('dataframe')
            if df is None:
                etl_logger.error("ETL - No dataframe found in processing result")
                raise DataProcessingError(
                    "No dataframe found in processing result",
                    error_code="MISSING_DATAFRAME"
                )
            
            etl_logger.info(f"ETL - Dataframe shape: {df.shape if hasattr(df, 'shape') else 'N/A'}")
            processed_dataframes = processing_results['processed_dataframes']
            
            if bank_id == 13:
                etl_logger.info("ETL - Processing data for bank 13 (ArthaSiddhi)")
                self._process_bank_13_data(df, current_file_type, processed_dataframes, processing_results, result)
            elif bank_id == 21:
                etl_logger.info("ETL - Processing PAVANA OVERDUE data...")
                print("Processing PAVANA OVERDUE data...")
                result_data = process_pavana_overdue_data(df)
                processing_results['final_result_data'] = result_data
                etl_logger.info("ETL - PAVANA OVERDUE data processed")
            elif bank_id == 23:
                etl_logger.info("ETL - Processing VINAYANA data...")
                print("Processing VINAYANA data...")
                result_data = process_vinayana_raw_data(df, bank_id=23)
                processing_results['final_result_data'] = result_data
                etl_logger.info("ETL - VINAYANA data processed")
            elif bank_id == 36:
                etl_logger.info("ETL - Processing data for bank 36 (Warna)")
                self._process_bank_36_data(df, current_file_type, processed_dataframes, result)
            elif bank_id == 27:
                etl_logger.info("ETL - Processing data for bank 27 (Ghoti)")
                self._process_bank_27_data(df, current_file_type, processed_dataframes, result)
            elif bank_id == 28:
                etl_logger.info("ETL - Processing SHAKTI FINANCE data...")
                print("Processing SHAKTI FINANCE data...")
                processing_results['final_result_data'] = process_shakti_finance_raw_data(df)
                etl_logger.info("ETL - SHAKTI FINANCE data processed")
            elif bank_id == 29:
                etl_logger.info("ETL - Processing RAJAPUR data...")
                print("Processing RAJAPUR data...")
                processing_results['final_result_data'] = process_rajapur_raw_data(df)
                etl_logger.info("ETL - RAJAPUR data processed")
            elif bank_id == 30:
                etl_logger.info("ETL - Processing SINDHUDHURG data...")
                print("Processing SINDHUDHURG data...")
                processing_results['final_result_data'] = process_sindhudhurg_raw_data(df)
                etl_logger.info("ETL - SINDHUDHURG data processed")
            elif bank_id == 32:
                etl_logger.info("ETL - Processing data for bank 32 (BSS Bihar)")
                self._process_bank_32_data(df, current_file_type, processed_dataframes, result)
            elif bank_id == 34:
                etl_logger.info("ETL - Processing SMARTFAIR data...")
                print("Processing SMARTFAIR data...")
                processing_results['final_result_data'] = process_smartfair_raw_data(df)
                etl_logger.info("ETL - SMARTFAIR data processed")
            elif bank_id == 35:
                etl_logger.info("ETL - Processing ShareIndia data...")
                print("Processing ShareIndia data...")
                processing_results['final_result_data'] = process_shareindia_raw_data(df)
                etl_logger.info("ETL - ShareIndia data processed")
            elif bank_id == 37:
                etl_logger.info("ETL - Processing Shrimant Malojirajya...")
                print("Processing Shrimant Malojirajya..")
                processing_results['final_result_data'] = process_shrimantmalojiraje_raw_data(df)
                etl_logger.info("ETL - Shrimant Malojirajya data processed")
            elif bank_id == 369:
                etl_logger.info("ETL - Processing Swami Samarth data separately")
                print("Processing Swami Samarth data separately")
                # Process base data if available
                if current_file_type == 'base':
                    etl_logger.info("ETL - Processing base data for Swami Samarth")
                    print("Processing base data for Swami Samarth")
                    processing_results['final_result_data']= process_swami_samarth_base_raw_data(df)
                    etl_logger.info("ETL - Swami Samarth base data processed")
                    
                # Process daily data if available
                elif current_file_type =='daily':
                    etl_logger.info("ETL - Processing daily data for Swami Samarth")
                    print("Processing daily data for Swami Samarth")
                    processing_results['final_result_data'] = process_swami_samarth_daily_raw_data(df)
                    etl_logger.info("ETL - Swami Samarth daily data processed")
                else:
                    etl_logger.warning("ETL - No specific data type found for Swami Samarth")
                    processing_results['final_result_data'] = {
                        'total_records': 0,
                        'db_operation_success': False,
                        'db_error_message': 'No data available for processing',
                        'message': 'No data available for processing'
                    }
            elif bank_id == 378:
                etl_logger.info("ETL - Processing daily data for Tulja Bhavani")
                print("Processing daily data for Tulja Bhavani")
                processing_results['final_result_data'] = process_tulja_raw_data(df)
                etl_logger.info("ETL - Tulja Bhavani data processed")

            else:
                etl_logger.error(f"ETL - Unsupported bank ID: {bank_id}")
                raise DataProcessingError(
                    f"Unsupported bank ID: {bank_id}",
                    error_code="UNSUPPORTED_BANK_ID"
                )
                
            etl_logger.info(f"ETL - Dataframe processing completed for bank {bank_id}")
                
        except DataProcessingError:
            raise
        except Exception as e:
            etl_logger.error(f"ETL - Error processing dataframe for bank {bank_id}: {str(e)}")
            raise DataProcessingError(
                f"Error processing dataframe for bank {bank_id}: {str(e)}",
                error_code="DATAFRAME_PROCESSING_ERROR"
            )

    def _process_bank_13_data(self, df, current_file_type, processed_dataframes, processing_results, result):
        """Process data for bank ID 13 (ArthaSiddhi)"""
        etl_logger.info(f"ETL - Processing bank 13 data for file type: {current_file_type}")
        try:
            if current_file_type in ['over due', 'overdue', 'od'] and "Branch" in df.columns:
                etl_logger.info("ETL - Processing OVERDUE data for ArthaSiddhi")
                print("Processing OVERDUE data...")
                overdue_data = process_overdue_data(df)
                processed_dataframes['overdue'] = overdue_data
                result['processed_data_type'] = 'overdue'
                etl_logger.info("ETL - ArthaSiddhi OVERDUE data processed successfully")
                print("Processed as OVERDUE data")
            elif current_file_type == 'demand':
                etl_logger.info("ETL - Processing DEMAND data for ArthaSiddhi")
                print("Processing DEMAND data...")
                demand_data = process_demand_data(df)
                processed_dataframes['demand'] = demand_data
                result['processed_data_type'] = 'demand'
                etl_logger.info("ETL - ArthaSiddhi DEMAND data processed successfully")
                print("Processed as DEMAND data")
            elif current_file_type == 'loan recovery':
                etl_logger.info("ETL - Processing RECOVERY data for ArthaSiddhi")
                print("Processing RECOVERY data...")
                collection_data = process_recovery_data(df)
                processed_dataframes['recovery'] = collection_data
                result['processed_data_type'] = 'recovery'
                etl_logger.info("ETL - ArthaSiddhi RECOVERY data processed successfully")
                print("Processed as RECOVERY data")
            elif current_file_type == '2pm demand':
                etl_logger.info("ETL - Processing 2PM DEMAND data for ArthaSiddhi")
                print("Processing 2PM DEMAND data...")
                final_result_data = process_arthasidhi_demand_data(df, bank_id=13)
                processing_results['final_result_data'] = final_result_data
                result['processed_data_type'] = '2pm demand'
                etl_logger.info("ETL - ArthaSiddhi 2PM DEMAND data processed successfully")
                print("2pm Demand Data processed")
            else:
                result['processed_data_type'] = f'unprocessed_{current_file_type}'
                etl_logger.warning(f"ETL - No specific processing applied for ArthaSiddhi file type: {current_file_type}")
                print(f"No specific processing applied for {current_file_type}")
        except Exception as e:
            etl_logger.error(f"ETL - Error processing bank 13 data for file type {current_file_type}: {str(e)}")
            raise DataProcessingError(
                f"Error processing bank 13 data for file type {current_file_type}: {str(e)}",
                error_code="BANK_13_PROCESSING_ERROR"
            )

    def _process_bank_36_data(self, df, current_file_type, processed_dataframes, result):
        """Process data for bank ID 36 (Warna)"""
        etl_logger.info(f"ETL - Processing bank 36 data for file type: {current_file_type}")
        try:
            if current_file_type == 'loan':
                etl_logger.info("ETL - Processing WARNA LOAN data")
                print("Processing WARNA LOAN data...")
                loan = process_warna_loan_data(df)
                processed_dataframes['warna_loan'] = loan
                result['processed_data_type'] = 'loan'
                etl_logger.info("ETL - Warna LOAN data processed successfully")
                print("Processed as Warna Loan data")
            elif current_file_type == 'guarantor':
                etl_logger.info("ETL - Processing WARNA GUARANTOR data")
                print("Processing WARNA GUARANTOR data...")
                gurantor = process_warna_gurantor_data(df)
                processed_dataframes['warna_gurantor'] = gurantor
                result['processed_data_type'] = 'gurantor'
                etl_logger.info("ETL - Warna GUARANTOR data processed successfully")
                print("Processed as Warna Gurantor data")
            elif current_file_type == 'customer details':
                etl_logger.info("ETL - Processing WARNA CUSTOMER data")
                print("Processing WARNA CUSTOMER data...")
                customer = process_warna_customer_data(df)
                processed_dataframes['warna_customer'] = customer
                result['processed_data_type'] = 'warna_customer'
                etl_logger.info("ETL - Warna CUSTOMER data processed successfully")
                print("Processed as Warna Customer data")
            elif current_file_type == 'negative':
                etl_logger.info("ETL - Processing WARNA NEGATIVE CUSTOMER data")
                customer = process_warna_negative_customer_data(df)
                processed_dataframes['warna_negative'] = customer
                result['processed_data_type'] = 'warna_negative'
                etl_logger.info("ETL - Warna NEGATIVE CUSTOMER data processed successfully")
                print("Processed as Warna Negative Customer data")
        except Exception as e:
            etl_logger.error(f"ETL - Error processing bank 36 data for file type {current_file_type}: {str(e)}")
            raise DataProcessingError(
                f"Error processing bank 36 data for file type {current_file_type}: {str(e)}",
                error_code="BANK_36_PROCESSING_ERROR"
            )

    def _process_bank_27_data(self, df, current_file_type, processed_dataframes, result):
        """Process data for bank ID 27 (Ghoti)"""
        etl_logger.info(f"ETL - Processing bank 27 data for file type: {current_file_type}")
        try:
            if current_file_type == 'loan':
                etl_logger.info("ETL - Processing GHOTI LOAN data")
                print("Processing GHOTI LOAN data...")
                if isinstance(df, dict):
                    processed_dataframes['ghoti_loan_type'] = df
                    result['processed_data_type'] = 'loan'
                    etl_logger.info("ETL - Ghoti LOAN data processed successfully (dictionary format)")
                    print("Processed as Ghoti Loan data (dictionary)")
                else:
                    etl_logger.error("ETL - Expected dictionary for bank 27 loan data but got dataframe")
                    raise DataProcessingError(
                        "Expected dictionary for bank 27 loan data but got dataframe",
                        error_code="INVALID_DATA_FORMAT"
                    )
            elif current_file_type == 'over due':
                etl_logger.info("ETL - Processing GHOTI OD data")
                print("Processing GHOTI OD data...")
                overdue_data = process_ghoti_od_data(df)
                processed_dataframes['ghoti_od_type'] = overdue_data
                result['processed_data_type'] = 'overdue'
                etl_logger.info("ETL - Ghoti OD data processed successfully")
                print("Processed as Ghoti OD data")
        except DataProcessingError:
            raise
        except Exception as e:
            etl_logger.error(f"ETL - Error processing bank 27 data for file type {current_file_type}: {str(e)}")
            raise DataProcessingError(
                f"Error processing bank 27 data for file type {current_file_type}: {str(e)}",
                error_code="BANK_27_PROCESSING_ERROR"
            )

    def _process_bank_32_data(self, df, current_file_type, processed_dataframes, result):
        """Process data for bank ID 32 (BSS Bihar)"""
        etl_logger.info(f"ETL - Processing bank 32 data for file type: {current_file_type}")
        try:
            if current_file_type == 'over due':
                etl_logger.info("ETL - Processing BSS BIHAR OVERDUE data")
                print("Processing BSS BIHAR OVERDUE data...")
                overdue_data = process_bssbihar_disbursement_data(df)
                processed_dataframes['overdue'] = overdue_data
                result['processed_data_type'] = 'overdue'
                etl_logger.info("ETL - BSS Bihar OVERDUE data processed successfully")
                print("Processed as OVERDUE data")
            elif current_file_type == 'collection':
                etl_logger.info("ETL - Processing BSS BIHAR COLLECTION data")
                print("Processing BSS BIHAR COLLECTION data...")
                collection_data = process_bssbihar_collection_data(df)
                processed_dataframes['recovery'] = collection_data
                result['processed_data_type'] = 'recovery'
                etl_logger.info("ETL - BSS Bihar COLLECTION data processed successfully")
                print("Processed as RECOVERY data")
        except Exception as e:
            etl_logger.error(f"ETL - Error processing bank 32 data for file type {current_file_type}: {str(e)}")
            raise DataProcessingError(
                f"Error processing bank 32 data for file type {current_file_type}: {str(e)}",
                error_code="BANK_32_PROCESSING_ERROR"
            )
    
    def _process_bank_369_data(self, df, current_file_type, processed_dataframes, result):
        """Process data for bank ID 369 (Swami Samarth)"""
        etl_logger.info(f"ETL - Processing bank 369 data for file type: {current_file_type}")
        try:
            if current_file_type == 'base':
                etl_logger.info("ETL - Processing Swami Samarth BASE data")
                print("Processing Swami Samarath LOAN data...")
                loan = prcoess_base_data(df)
                processed_dataframes['swami_base'] = loan
                result['processed_data_type'] = 'base'
                etl_logger.info("ETL - Swami Samarth BASE data processed successfully")
                print("Processed as Swami Samarath Loan data")
            elif current_file_type == 'daily':
                etl_logger.info("ETL - Processing Swami Samarth DAILY data")
                print("Processing Swami Samarath Daily data...")
                daily = process_daily_data(df)
                processed_dataframes['swami_daily'] = daily
                result['processed_data_type'] = 'daily'
                etl_logger.info("ETL - Swami Samarth DAILY data processed successfully")
                print("Processed as Swami Samarath data")
        except Exception as e:
            etl_logger.error(f"ETL - Error processing bank 369 data for file type {current_file_type}: {str(e)}")
            raise DataProcessingError(
                f"Error processing bank 369 data for file type {current_file_type}: {str(e)}",
                error_code="BANK_369_PROCESSING_ERROR"
            )

    def _merge_processed_data(self, bank_id, processing_results):
        """Merge all processed data based on bank ID"""
        etl_logger.info(f"ETL - Starting data merging for bank {bank_id}")
        try:
            processed_dataframes = processing_results['processed_dataframes']
            merged_final_data = None
            
            # Check if we have any data to merge
            if not any(df is not None for df in processed_dataframes.values()):
                etl_logger.warning("ETL - No data available for merging")
                print("No data available for merging")
                return
            
            if bank_id == 13:
                etl_logger.info("ETL - Merging data for ArthaSiddhi (bank 13)")
                merged_final_data = merge_all_data(
                    processed_dataframes['overdue'],
                    processed_dataframes['demand'], 
                    processed_dataframes['recovery']
                )
                if merged_final_data is not None and not merged_final_data.empty:
                    etl_logger.info("ETL - Creating final rawfile data for ArthaSiddhi")
                    processing_results['final_result_data'] = create_final_rawfile_data(merged_final_data)
                    etl_logger.info("ETL - Final rawfile data processing completed for ArthaSiddhi")
                    print("Final rawfile data processing completed for ArthaSiddhi")
            elif bank_id == 36:
                etl_logger.info("ETL - Merging data for Warna (bank 36)")
                merged_final_data = merge_warna_data_with_counts(
                    processed_dataframes['warna_loan'],
                    processed_dataframes['warna_customer'],
                    processed_dataframes['warna_gurantor'],
                    processed_dataframes['warna_negative']
                )
                if merged_final_data is not None and not merged_final_data.empty:
                    etl_logger.info("ETL - Processing final rawfile data for Warna")
                    processing_results['final_result_data'] = process_warna_raw_data(merged_final_data)
                    etl_logger.info("ETL - Final rawfile data processing completed for Warna")
                    print("Final rawfile data processing completed for Warna")
            elif bank_id == 27:
                etl_logger.info("ETL - Merging data for Ghoti (bank 27)")
                print("Going inside merging for Ghoti")
                merged_final_data = merge_ghoti_data(
                    processed_dataframes['ghoti_loan_type'],
                    processed_dataframes['ghoti_od_type']
                )
                if merged_final_data is not None and not merged_final_data.empty:
                    etl_logger.info("ETL - Processing final rawfile data for Ghoti")
                    print("Going inside the processing raw file for Ghoti")
                    processing_results['final_result_data'] = process_raw_data_for_ghoti(merged_final_data, bank_id)
                    etl_logger.info("ETL - Final rawfile data processing completed for Ghoti")
                    print("Final rawfile data processing completed for Ghoti")
            elif bank_id == 32:
                etl_logger.info("ETL - Merging data for BSS Bihar (bank 32)")
                merged_final_data = merged_bssbihar_data(
                    processed_dataframes['overdue'],
                    processed_dataframes['recovery']
                )
                if merged_final_data is not None and not merged_final_data.empty:
                    etl_logger.info("ETL - Processing final rawfile data for BSS Bihar")
                    print("Going inside the processing raw file for BSS Bihar")
                    processing_results['final_result_data'] = process_raw_data_for_bssbihar(merged_final_data)
                    etl_logger.info("ETL - Final rawfile data processing completed for BSS Bihar")
                    print("Final rawfile data processing completed for BSS Bihar")
            elif bank_id == 369:
                etl_logger.info("ETL - Processing Swami Samarth data in merge phase")
                print("Processing Swami Samarth data in merge phase")
                # Since Swami Samarth data is already processed in _process_dataframe_by_bank,
                # we just need to ensure the final_result_data is set if it wasn't already
                if processing_results['final_result_data'] is None:
                    etl_logger.warning("ETL - No final result data available for Swami Samarth, creating default response")
                    # If no final result data was set, create a default response
                    processing_results['final_result_data'] = {
                        'total_records': 0,
                        'db_operation_success': False,
                        'db_error_message': 'No final result data available',
                        'message': 'Swami Samarth data processed but no final result generated'
                    }
                etl_logger.info("ETL - Final rawfile data processing completed for Swami Samarth")
                print("Final rawfile data processing completed for Swami Samarth")
        
            if merged_final_data is None or (hasattr(merged_final_data, 'empty') and merged_final_data.empty):
                if bank_id != 369 and bank_id != 378:  # Skip this for Swami Samarth and Tulja Bhavani since it's handled separately
                    etl_logger.warning("ETL - No data available for final processing")
                    print("No data available for final processing")
                    processing_results['final_result_data'] = {
                        'total_records': 0,
                        'db_operation_success': False,
                        'db_error_message': 'No data available for processing',
                        'message': 'No data available for final processing'
                    }
                
            etl_logger.info(f"ETL - Data merging completed for bank {bank_id}")
                
        except Exception as e:
            error_message = str(e)
            etl_logger.error(f"ETL - Error during data merging/processing for bank {bank_id}: {error_message}")
            print(f"Error during data merging/processing: {error_message}")
            
            # Check if it's a pandas-related error
            if any(keyword in error_message.lower() for keyword in ['not in index', 'keyerror', 'column', 'dataframe']):
                etl_logger.error(f"ETL - Data merging error - missing or incorrect columns: {error_message}")
                raise DataProcessingError(
                    f"Data merging error - missing or incorrect columns: {error_message}",
                    error_code="DATA_MERGE_ERROR"
                )
            else:
                etl_logger.error(f"ETL - Error during data processing: {error_message}")
                raise DataProcessingError(
                    f"Error during data processing: {error_message}",
                    error_code="DATA_PROCESSING_ERROR"
                )

    def _generate_final_response(self, processing_results, type_of_file, bank_id):
        """Generate the final response based on processing results"""
        etl_logger.info(f"ETL - Generating final response for bank {bank_id}, file type {type_of_file}")
        try:
            # Use the appropriate result data for response generation
            response_result_data = None
            
            # First, try to use final_result_data
            if processing_results['final_result_data'] is not None:
                response_result_data = processing_results['final_result_data']
                etl_logger.info("ETL - Using final_result_data for response generation")
            # If that's not available, try to use the first successful file
            elif processing_results['successful_files']:
                response_result_data = processing_results['successful_files'][0]
                etl_logger.info("ETL - Using first successful file for response generation")
            # If neither is available, create a default response structure
            else:
                etl_logger.warning("ETL - No data available for response generation, creating default response")
                response_result_data = {
                    'total_records': 0,
                    'db_operation_success': False,
                    'db_error_message': 'No data available for processing',
                    'message': 'No files were processed successfully'
                }
            
            # Make sure response_result_data is not None before calling generate_summary_response
            if response_result_data is None:
                etl_logger.error("ETL - Response data is None, creating default response")
                response_result_data = {
                    'total_records': 0,
                    'db_operation_success': False,
                    'db_error_message': 'Response data is None',
                    'message': 'Unable to generate response data'
                }
            
            etl_logger.info("ETL - Calling generate_summary_response")
            response_data = generate_summary_response(response_result_data, type_of_file, bank_id)
            etl_logger.info(f"ETL - Response data generated: {type(response_data)}")
            print(f"DEBUG: Response data generated: {type(response_data)}")
            
            if response_data is None:
                etl_logger.error("ETL - Error generating summary response - response data is None")
                raise DataProcessingError(
                    "Error generating summary response - response data is None",
                    error_code="RESPONSE_GENERATION_ERROR"
                )
            
            # Add processing summary to response
            response_data.update({
                "total_files_processed": len(processing_results['successful_files']) + len(processing_results['failed_files']),
                "successful_files": len(processing_results['successful_files']),
                "failed_files": len(processing_results['failed_files']),
                "file_type_validated": True
            })
            
            etl_logger.info(f"ETL - Processing summary added: successful={len(processing_results['successful_files'])}, failed={len(processing_results['failed_files'])}")
            
            # Add failed files info if any
            if processing_results['failed_files']:
                response_data["failed_file_errors"] = [
                    {"filename": f.get('filename'), "error": f.get('error')} 
                    for f in processing_results['failed_files']
                ]
                etl_logger.warning(f"ETL - Added {len(processing_results['failed_files'])} failed file errors to response")
            
            etl_logger.info("ETL - Final response generated successfully")
            return Response(response_data, status=200)
            
        except DataProcessingError:
            raise
        except Exception as e:
            etl_logger.error(f"ETL - Error generating final response: {str(e)}")
            raise DataProcessingError(
                f"Error generating final response: {str(e)}",
                error_code="RESPONSE_GENERATION_ERROR"
            )
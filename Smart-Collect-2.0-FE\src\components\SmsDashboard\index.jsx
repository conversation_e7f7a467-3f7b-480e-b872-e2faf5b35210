import { Flex, message, Typography } from "antd";
import React, { useEffect, useState } from "react";
import AppHeader from "../AppHeader";
import {
  handleDownloadTable,
  formatAmount,
  formattedTextToCapitalized,
  ALL_CACHE_NAMES,
  LOCAL_KEYS,
} from "../../constant";
import DOWNLOAD_IMG from "../../assets/Images/download.svg";
import { DashboardEngagementAllCards } from "../DashboardEngagementAllCards";
import { DashboardEngagementTable } from "../DashboardEngagementTable";
import Style from "./_style.module.scss";
import { AXIOS } from "../../apis/ho-Instance";
import ApplicationLoader from "../ApplicationLoader";
import {
  getCacheKey,
  getFromCache,
  getPostCacheKey,
  storeToCache,
} from "../../utils/cacheHelper";
import { encryptBody, isSameEncryptedBody } from "../../utils/crypto";

const { Text } = Typography;
const CACHE_NAME = ALL_CACHE_NAMES.DASHBOARD_CACHE;

export default function SmsDashboard() {
  const [tableData, setTableData] = useState([]);
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSwitchOn, setIsSwitchOn] = useState(false);
  const storedFilter = localStorage.getItem("filterDate");
  const storedDpd = localStorage.getItem("DashboardDpdCategory");

  // Download table data
  const handleDownload = async () => {
    if (tableData.length === 0 || !tableData) {
      return message.error("There is no tableData!");
    } else {
      await handleDownloadTable({
        excelName: "SMS",
        worksheetName: "Dashboard-sms",
        tableData: tableData,
      });
    }
  };

  // Apply data
  const applyFetchedData = (data) => {
    setData(data);
    setTableData(data.sms_history || []);
  };

  const getSmsData = async () => {
    setIsLoading(true);
    const cacheKey = getCacheKey({ endpoint: "sms_history/" });
    // Check cache
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      applyFetchedData(cachedData);
      setIsLoading(false);
    }
    try {
      const response = await AXIOS.get("sms_history/");
      if (response.status === 200) {
        // Store data in cache
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
        applyFetchedData(response.data);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // filtered post api
  const getSmsFitredData = async ({
    fromDate,
    toDate,
    BranchName,
    dpdRange,
    disbursementID,
  }) => {
    setIsLoading(true);
    const body = {
      fromDate,
      toDate,
      BranchName,
      dpdfrom: dpdRange?.start ?? -1,
      dpdto: dpdRange?.end ?? -1,
      DisbursementID: disbursementID,
    };
    const cacheKey = getPostCacheKey({ endpoint: "sms_history/", body });
    const encryptedOldBody = localStorage.getItem(LOCAL_KEYS.HO_CHANNEL_FILTER);
    const sameBody = isSameEncryptedBody({ newBody: body, encryptedOldBody });

    if (sameBody) {
      const cachedData = await getFromCache({
        cacheName: CACHE_NAME,
        key: cacheKey,
      });
      if (cachedData) {
        applyFetchedData(cachedData);
        setIsLoading(false);
      }
    }
    try {
      const response = await AXIOS.post("sms_history/", body);
      if (response.status === 200) {
        const encrypted = encryptBody(body);
        if (encrypted) {
          localStorage.setItem(LOCAL_KEYS.HO_CHANNEL_FILTER, encrypted);
        }
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
        applyFetchedData(response.data);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Apply filter and fetch data
  const applyFilters = async ({
    fromDate,
    toDate,
    BranchName,
    disbursementID,
  }) => {
    await getSmsFitredData({
      fromDate: fromDate,
      toDate: toDate,
      BranchName,
      dpdRange: JSON.parse(storedDpd),
      disbursementID,
    });
  };

  const ClearFilters = () => {
    getSmsData();
    localStorage.removeItem("filterDate");
    localStorage.removeItem(LOCAL_KEYS.HO_CHANNEL_FILTER);
  };

  // On mount, check localStorage for saved filters
  useEffect(() => {
    if (storedFilter || storedDpd) {
      const { fromDate, toDate, BranchName, disbursementID } = storedFilter
        ? JSON.parse(storedFilter)
        : {
            fromDate: null,
            toDate: null,
            BranchName: null,
            disbursementID: "",
          };
      const parsedDpd = storedDpd
        ? JSON.parse(storedDpd)
        : { start: -1, end: -1 };
      getSmsFitredData({
        fromDate,
        toDate,
        BranchName,
        dpdRange: parsedDpd,
        disbursementID,
      });
    } else {
      getSmsData();
    }
  }, []);
  return (
    <Flex gap={10} vertical>
      <AppHeader
        title={
          <Flex gap={5} className={Style.title}>
            <Text>
              SMS: <span style={{ color: "#0F2050" }}>{data.SMS}</span>
            </Text>
            <Text>
              Distinct Customers :{" "}
              <span style={{ color: "#0F2050" }}>
                {data.distinct_customers}
              </span>
            </Text>
          </Flex>
        }
        isDashboard={false}
        isSwitchOn={isSwitchOn}
        setIsSwitchOn={setIsSwitchOn}
        ClearFilters={ClearFilters}
        applyFilters={applyFilters}
        isDashboardOtherPages={true}
      />
      <div>
        {isLoading ? (
          <ApplicationLoader />
        ) : (
          <div className={Style.details}>
            {!isSwitchOn ? (
              <DashboardEngagementAllCards data={tableData} />
            ) : (
              <Flex vertical gap={20} style={{ paddingBlockStart: "1rem" }}>
                <Flex justify="end">
                  <button
                    className={Style.download_button}
                    onClick={handleDownload}
                  >
                    <img src={DOWNLOAD_IMG} alt="download-button" />
                  </button>
                </Flex>
                <DashboardEngagementTable data={tableData} />
              </Flex>
            )}
          </div>
        )}
      </div>
    </Flex>
  );
}

import { Flex, Typography, Select } from "antd";
import { useState, useMemo } from "react";
import Style from "./_style.module.scss";
import Chart from "react-apexcharts";
import PropTypes from "prop-types";

const { Text } = Typography;

export function CommunicationsChart({ data = [] }) {
  const [selectedRange, setSelectedRange] = useState("7");

  const sortedData = [...data].sort(
    (a, b) => new Date(a.communication_date) - new Date(b.communication_date)
  );

  // Filter data based on selected range
  const filteredData = useMemo(() => {
    if (!sortedData.length) {
      return sortedData;
    }

    const days = parseInt(selectedRange);
    const totalDays = sortedData.length;
    const startIndex = Math.max(0, totalDays - days);
    
    return sortedData.slice(startIndex);
  }, [sortedData, selectedRange]);

  const categories = filteredData.map(row => row.communication_date || "");
  const whatsappData = filteredData.map(row => row.WhatsAppCount || 0);
  const aiData = filteredData.map(row => row.VoicebotCount || 0);
  const blasterData = filteredData.map(row => row.BlasterCount || 0);
  const ivrData = filteredData.map(row => row.IVRCount || 0);

  const series = [
    { name: "Whatsapp", data: whatsappData },
    { name: "AI", data: aiData },
    { name: "Blaster", data: blasterData },
    { name: "IVR", data: ivrData },
  ];

  const options = {
    chart: { type: "bar", stacked: true, toolbar: { show: false } },
    xaxis: {
      categories: categories.length ? categories : ["No Data"],
      title: {
        text: "Days",
        style: { fontSize: "16px", color: "#407BFF", fontWeight: "normal", fontFamily: "Kanit" },
      },
      labels: {
        rotate: -45,
        style: { colors: "#407BFF", fontWeight: 500, fontSize: "9px" },
      },
      axisBorder: { show: true, color: "#0F2050" },
      axisTicks: { show: false },
    },
    yaxis: {
      labels: { style: { colors: "#407BFF", fontWeight: 500, fontSize: "9px" } },
      axisBorder: { show: true, color: "#0F2050" },
      axisTicks: { show: false },
    },
    legend: { position: "top", horizontalAlign: "right" },
    plotOptions: {
      bar: {
        columnWidth: "30px",
        horizontal: false,
        borderRadius: 0,
        dataLabels: { position: "center" },
      },
    },
    dataLabels: {
      enabled: true,
      style: { fontSize: "9px", colors: ["#fff"] },
    },
    colors: ["#33AB3C", "#FDBF00", "#FF631A", "#113C9B"],
    grid: {
      show: true,
      xaxis: { lines: { show: false } },
      yaxis: { lines: { show: true } },
      borderColor: "#C6D1ED",
      strokeDashArray: 2,
    },
    tooltip: { shared: true, intersect: false },
  };

  return (
    <Flex className={Style.chart_container} vertical gap={5}>
      <Flex justify="space-between" align="center">
        <Text className={Style.heading}>Communications:</Text>
        <Select
          value={selectedRange}
          onChange={setSelectedRange}
          style={{ width: 120 }}
          options={[
            { label: "Last 7 Days", value: "7" },
            { label: "Last 15 Days", value: "15" },
          ]}
        />
      </Flex>
      <Chart options={options} series={series} type="bar" height={320} />
    </Flex>
  );
}

CommunicationsChart.propTypes = {
  data: PropTypes.array,
};

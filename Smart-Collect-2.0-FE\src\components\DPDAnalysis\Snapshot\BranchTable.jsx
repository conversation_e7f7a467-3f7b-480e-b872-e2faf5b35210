import { Spin, Table, Typography } from 'antd'
import React from 'react'
import Style from "./_style.module.scss";
import PropTypes from 'prop-types';

const {Text} = Typography;

export function BranchTable({isBranchSwitchOn, data, isLoading, onRangeClick}) {
    const columns = [
        {
            title: 'Branch',
            dataIndex: 'Branch',
            render: (Branch, data) => <Text
            style={{cursor:"pointer"}}>{ Branch || "-"}</Text>
        },
        {
            title: 'Regular',
            dataIndex: '0',
            render:(value, {Branch_id}) => <Text 
            style={{cursor:"pointer"}} 
            onClick={()=>{
                onRangeClick({branchId:Branch_id, tablewise:"Branch", range:"0" })
            }}>{value}</Text>
        },
        {
            title: '1 to 30',
            dataIndex: "1-30",
            render: (value, {Branch_id}) => <Text 
            style={{cursor:"pointer"}} 
            onClick={()=>{
                onRangeClick({branchId:Branch_id, tablewise:"Branch", range:"1-30" })
            }}>{value}</Text>
           
        },
        {
            title: '31 to 60',
            dataIndex: "31-60",
            render: (value, {Branch_id}) => <Text 
            style={{cursor:"pointer"}} 
            onClick={()=>{
                onRangeClick({branchId:Branch_id, tablewise:"Branch", range:"31-60" })
            }}>{value}</Text>
        },
        {
            title: '61 to 90',
            dataIndex: "61-90",
            render: (value, {Branch_id}) => <Text 
            style={{cursor:"pointer"}} 
            onClick={()=>{
                onRangeClick({branchId:Branch_id, tablewise:"Branch", range:"61-90" })
            }}>{value}</Text>
        },
        {
            title: '91 to 180',
            dataIndex: "91-180",
            render: (value, {Branch_id}) => <Text 
            style={{cursor:"pointer"}} 
            onClick={()=>{
                onRangeClick({branchId:Branch_id, tablewise:"Branch", range:"91-180" })
            }}>{value}</Text>
        },
        {
            title: '181 to 360',
            dataIndex:"181-360",
            render: (value, {Branch_id}) => <Text 
            style={{cursor:"pointer"}} 
            onClick={()=>{
                onRangeClick({branchId:Branch_id, tablewise:"Branch", range:"181-360" })
            }}>{value}</Text>
        },
        {
            title: '360+',
            dataIndex: "360+",
            render: (value, {Branch_id}) => <Text 
            style={{cursor:"pointer"}} 
            onClick={()=>{
                onRangeClick({branchId:Branch_id, tablewise:"Branch", range:"360+" })
            }}>{value}</Text>
        },
        {
            title: 'Total',
            dataIndex: "Total",
            render: (value) => <Text>{value}</Text>
        },
    ].filter(Boolean);;

    const dataSource = Array.isArray(data)? data.filter((d)=> d.Branch !==0)?.map((data, i) => ({
        key: i+1,
        ...data
    })):[];
  return (
    <Spin tip={"Loading..."} spinning={isLoading}>
       <Table
           bordered
           virtual
           className={Style.CustomTable}
           columns={columns}
           dataSource={dataSource}
           scroll={{
               x:800,
               y: 360,
           }}
           pagination={false}
       />
   </Spin>
 )}

BranchTable.propTypes={
    isBranchSwitchOn: PropTypes.bool,
    data: PropTypes.array,
    isLoading: PropTypes.bool,
    onRangeClick: PropTypes.func
}
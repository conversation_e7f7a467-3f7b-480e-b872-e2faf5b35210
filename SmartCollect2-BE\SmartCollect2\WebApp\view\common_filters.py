import logging
import time
from django.utils import timezone
from DB.db_manager import db_manager
import WebApp.models as webmodels

logger = logging.getLogger(__name__)

def get_common_filters(user, request=None):
    
    print("UUUUUUUUUUUU", user)
    print(f"User Designation: {user.Designation}")  # Debug line
    print(f"User BUcode: {getattr(user, 'BUcode', None)}")  # Debug line
    print(f"User BankMstID: {user.BankMstID_id}")  # Fixed: was showing BUcode instead of BankMstID_id
   
    from_date = (
        request.data.get("from_date") if request else None
    ) or webmodels.BankMst.objects.filter(BankMstID=user.BankMstID_id).values_list(
        "OnBoardingDate", flat=True
    ).first()
 
    to_date = (
        request.data.get("to_date") if request else None
    ) or timezone.now().date()
 
    # branchls = None
    branchls = request.data.get('branchls') if request else None
    # branchls = user.BranchMstID_id
    print(f"Branchessssssssssss: {branchls}")
 
    # Check BOTH BO designation AND BO_id exists
    if not branchls:
        try:
            print("Fetching branches from stored procedure")
            print(f"Calling SP with BUcode: {user.BUcode}, BankMstID: {user.BankMstID_id}")
            
            # Using db_manager instead of direct Django connection
            query = "SELECT public.hierarchy_based_access_control_system(:bu_code, :bank_mst_id);"
            params = {"bu_code": user.BUcode, "bank_mst_id": user.BankMstID_id}
            
            result = db_manager.execute_query_with_retry(query, params)
            print(f"Raw SP result: {result}")
            
            if result and len(result) > 0:
                # Extract branch IDs from result - assuming SP returns array of branch IDs
                # The SP might return a single row with an array, or multiple rows
                if len(result) == 1 and hasattr(result[0][0], '__iter__') and not isinstance(result[0][0], str):
                    # Single row with array result
                    branchls = list(result[0][0])
                else:
                    # Multiple rows, each containing a branch ID
                    branchls = [row[0] for row in result if row[0] is not None]
                    
                print("Branches from hierarchy_based_access_control_system:", branchls)
            else:
                print("SP returned no branches")
                branchls = []

        except Exception as e:
            print(f"Error fetching branches from SP: {e}")
            branchls = []  # Ensure branchls is defined even on error
            
    # Fallback logic for HO users if no branches found
    if not branchls and user.Designation == "HO":
        try:
            print("Using HO fallback - fetching all branches for bank")
            branchls = list(
                webmodels.BranchMst.objects.filter(BankMstID=user.BankMstID_id)
                .values_list("BranchMstID", flat=True)
                .distinct()
            )
            print(f"HO fallback branches: {branchls}")
        except Exception as e:
            print(f"Error in HO fallback: {e}")
            branchls = []

    print(from_date, to_date, branchls, "DETAILSSSSSSSSSSSSSSSS")
    return from_date, to_date, branchls
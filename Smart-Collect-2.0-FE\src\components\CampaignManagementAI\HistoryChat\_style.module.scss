$bg-color: #f3f3f4;
$dark-blue: #0f2050;

.history_modal {
  position: fixed;
  right: 20px; // Positioned to the right
  width: 20rem;
  height: 25rem;
  background-color: #c6d7fc;
  box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.2);
  padding: 0.5rem 0;
  border-radius: 8px;

  .modal_content {
    display: flex;
    flex-direction: column;
    max-height: 400px;
    overflow-y: auto;
  }

  .header {
    .header_text{
      color: #1551d7;
      font-weight: 500;
      font-family:'Kanit','Courier New', Courier, monospace;
      font-size: 1.1rem;
    }
    
    img {
      width: 14px;
    }

    .close_btn {
      position: absolute;
      right: 10px;
      background: none;
      border: none;
      font-size: 1.5rem;
      cursor: pointer;
      color: #1551d7 !important;
    }
  }

  .history_list {
    max-height: 300px;
    overflow-y: auto;
    margin: 1rem 0.5rem 1rem 1.5rem;
      // Custom scrollbar
      &::-webkit-scrollbar {
        width: 5px;
      }

      &::-webkit-scrollbar-thumb {
        background: $dark-blue;
        border-radius: 5px;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: var(--blue);
      }
  }

  .history_item {
    border-radius: 5px;
    margin-bottom: 0.5rem;
    color: #5c5c5c;
  }

  .history_message {
    color: #5C5C5C;
  }
}

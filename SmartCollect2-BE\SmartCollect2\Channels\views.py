from django.shortcuts import render
from rest_framework.views import APIView
from rest_framework.response import Response
from django.db import connection
import pandas as pd
import Channels.models as chmodels
import WebApp.models as webmodels
from twilio.rest import Client
import json
from datetime import datetime
from django.http import HttpResponse
from django.utils import timezone
import logging
from threading import Thread
from django.db.models import Subquery, Window, F, Q, Count
from django.db.models.functions import RowNumber
from vertexai.generative_models import (
        GenerationConfig,
        GenerativeModel,
        Part
    )
from datetime import date
from django.conf import settings
from WebApp.models import LoanMst,CustomerMst,AccountSummary
# import Channels.models as Dialer
from .models import Dialer
import urllib.parse
import os
from asterisk.manager import Manager
from rest_framework.permissions import IsAuthenticated
import uuid
from WebApp.serializers import TBLLoanMasterSerializer
from rest_framework.viewsets import ModelViewSet
from campaign.models import WhatsAppFlowMapping,WhatsAppUserTemplate
from django.utils.timezone import now
import razorpay
from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
import time
import hmac
import hashlib
import requests
from google.cloud import storage
from datetime import timedelta
# Create your views here.
from django.db.models.functions import RowNumber, TruncDate
import re
import uuid
import threading
from time import sleep
from jinja2 import Environment, FileSystemLoader
from concurrent.futures import ThreadPoolExecutor, as_completed
import traceback
from django.db import close_old_connections
from confluent_kafka import Producer


class WhatsAppEngine(APIView):
    authentication_classes = []
    permission_classes = []

    def get(self, request):
        '''
        This function starts the WhatsApp Engine Immediately on request and returns a response.
        '''
        thread = Thread(target=self.wh_engine)
        thread.start()
        return Response({"message": "WhatsApp Engine started!"})

    def twilio_message_api_with_params(self, to, db_creds, item, template_data_values):
        '''
        This function sends a message using the Twilio API. With parameters.
        :param to: The recipient's phone number
        :param twilio_params: The parameters
        :param template_name: The template name
        :param db_creds: The database credentials
        :return: The SID of the message
        '''
        # Set up Twilio client
        template_body, template_name, twilio_params = self.get_template_data(template_data_values[0], template_data_values[1], template_data_values[2], template_data_values[3])
        client = Client(db_creds.APIVersion, db_creds.AuthorizationKey)
        # Send message using Twilio
        try:
            message = client.messages.create(
                from_=db_creds.PhoneNumberID,
                content_sid=template_name,
                content_variables=json.dumps(twilio_params),
                to=f"whatsapp:+91{to}"
            )
            self.save_message(item, template_name, message.sid,template_body)
            return message.sid
        except Exception as e:
            logging.error(f"Failed to send message to +91{to}: {e}")
            return None

    def smartping_message_api_with_params(self, to, db_creds, item, template_data_values):

        template_body, template_name, params = self.get_template_data(template_data_values[0], template_data_values[1], template_data_values[2], template_data_values[3])

        '''
        This function sends a message using the SmartPing API. With parameters.
        :param to: The recipient's phone number
        :param twilio_params: The parameters
        :param template_name: The template name
        :param db_creds: The database credentials
        :return: The SID of the message
        '''
        language_map = {
            "Hindi": "hi",
            "Marathi": "mr",
            "Tamil": "ta",
            "Telugu": "te",
            "Malayalam": "ml",
            "Kannada": "kn"
        }

        if item.Language is None:
            language_str = "Marathi"
        else:
            language_str = item.Language.strip()

        language_code = language_map.get(language_str)

        # fake_message_id = str(uuid.uuid4())
        # print(f"Simulated message sent to {to}. Fake message ID: {fake_message_id}")
        # self.save_message(item, template_name, fake_message_id, template_body)
        # return fake_message_id

        print("IN SEND SMARTPING")
        headers = {
            "X-AiSensy-Project-API-Pwd": db_creds.SP_Password,
            "Content-Type": "application/json"
        }

        var = [params[key] for key in sorted(params.keys(), key=int)]
        print(var)
        payload = {
            "to": f"91{to}",
            "type": "template",
            "template": {
                "language": {
                    "policy": "deterministic",
                    "code": language_code
                },
                "name": template_name,
                "components": [
                    {
                        "type": "body",
                        "parameters": [{"type": "text", "text": val} for val in var]
                    }
                ]
            }
        }

        url = f"https://apis.aisensy.com/project-apis/v1/project/{db_creds.SP_ProjectID}/messages"

        try:
            response = requests.post(url, headers=headers, json=payload)
            print("Status Code:", response.status_code)
            print("Response Text:", response.text)
            response.raise_for_status()
            res_json = response.json()
            message_id = None
            if "messages" in res_json and len(res_json["messages"]) > 0:
                message_id = res_json["messages"][0].get("id")

            if message_id:
                self.save_message(item, template_name, message_id, template_body)
                return message_id
            else:
                print("Message ID not found in response")
                return None
        except requests.exceptions.RequestException as e:
            print(f"Error sending message: {e}")
            return None

    def smartping_message_api_with_params_Legal_notice(self, to, param, template_name, db_creds,item,template_body):
        '''
        This function sends a message using the SmartPing API. With parameters.
        :param to: The recipient's phone number
        :param twilio_params: The parameters
        :param template_name: The template name
        :param db_creds: The database credentials
        :return: The SID of the message
        '''
        language_map = {
            "Hindi": "hi",
            "Marathi": "mr",
            "Tamil": "ta",
            "Telugu": "te",
            "Malayalam": "ml",
            "Kannada": "kn"
        }

        if item.Language is None:
            language_str = "Marathi"
        else:
            language_str = item.Language.strip()

        language_code = language_map.get(language_str)

        # fake_message_id = str(uuid.uuid4())
        # print(f"Simulated message sent to {to}. Fake message ID: {fake_message_id}")
        # self.save_message(item, template_name, fake_message_id, template_body)
        # return fake_message_id

        print("IN SEND SMARTPING")
        headers = {
            "X-AiSensy-Project-API-Pwd": db_creds.SP_Password,
            "Content-Type": "application/json"
        }

        document_link = param.get("5", "").strip()
        filename = document_link.split("/")[-1] or "document.pdf"

        body_keys = sorted([k for k in param.keys() if k != "5"], key=int)
        body_params = [{"type": "text", "text": param[k].strip()} for k in body_keys]

        payload = {
            "to": f"91{to}",
            "type": "template",
            "template": {
                "language": {
                    "policy": "deterministic",
                    "code": language_code
                },
                "name": template_name,
                "components": [
                    {
                        "type": "header",
                        "parameters": [
                            {
                                "type": "document",
                                "document": {
                                    "link": document_link,
                                    "filename": filename
                                }
                            }
                        ]
                    },
                    {
                        "type": "body",
                        "parameters": body_params
                    }
                ]
            }
        }

        url = f"https://apis.aisensy.com/project-apis/v1/project/{db_creds.SP_ProjectID}/messages"


        try:
            response = requests.post(url, headers=headers, json=payload)
            print("Status Code:", response.status_code)
            print("Response Text:", response.text)
            response.raise_for_status()
            res_json = response.json()
            message_id = None
            if "messages" in res_json and len(res_json["messages"]) > 0:
                message_id = res_json["messages"][0].get("id")

            if message_id:
                self.save_message(item, template_name, message_id, template_body)
                return message_id
            else:
                print("Message ID not found in response")
                return None
        except requests.exceptions.RequestException as e:
            print(f"Error sending message: {e}")
            return None

    def opt_in_user(self, contact_number, userid, password):
        url = "https://media.smsgupshup.com/GatewayAPI/rest"
        # z24gzBUA
        # 2000209909
        # payload = f"userid=2000209909&password=z24gzBUA&phone_number={contact_number}&method=OPT_IN&auth_scheme=plain&v=1.1&channel=WHATSAPP&format=json"
        payload = f"userid={userid}&password={password}&phone_number={contact_number}&method=OPT_IN&auth_scheme=plain&v=1.1&channel=WHATSAPP&format=json"
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        response = requests.request("POST", url, headers=headers, data=payload)
        # print(response.text)
        return json.loads(response.text), response.status_code

    def gupshup_message_api_with_params(self, to, db_creds, item, template_data_values):
        template_body, template_name, param = self.get_template_data(template_data_values[0], template_data_values[1], template_data_values[2], template_data_values[3])

        close_old_connections()
        if isinstance(param, dict):
            for key, value in param.items():
                placeholder = f"{{{key}}}"
                template_name = template_name.replace(placeholder, str(value))

        template_name = template_name.replace("{password}",db_creds.SP_Password)
        template_name = template_name.replace("{to}", urllib.parse.quote_plus(to))
        url = template_name
        payload = {}
        # headers = {"Content-Type": "application/x-www-form-urlencoded"}
        headers = {}
        self.opt_in_user(to, db_creds.SP_ProjectID, db_creds.SP_Password)
        response = requests.request("GET", url, headers=headers, data=payload)
        response_data = json.loads(response.text)
        full_id  = response_data.get("response", {}).get("id", None)
        reply_id, message_id = (None, None)
        if full_id and "-" in full_id:
            reply_id, message_id = full_id.split("-", 1)

        return self.save_message_gapshap(item, template_name,message_id, template_body,reply_id)

    def gapshap_api(self , number, template_id, db_creds):
        logging.info("IN GAP Shap Send Messageeeeeeeee")
        # url = f"""https://media.smsgupshup.com/GatewayAPI/rest?userid=**********&password=5ecCKv@F&send_to={number}&v=1.1&format=json&msg_type=TEXT&method=SENDMESSAGE&msg=%E0%A4%86%E0%A4%AA%E0%A4%95%E0%A5%87+%E0%A4%B8%E0%A4%AE%E0%A4%AF+%E0%A4%95%E0%A5%87+%E0%A4%B2%E0%A4%BF%E0%A4%8F+%E0%A4%A7%E0%A4%A8%E0%A5%8D%E0%A4%AF%E0%A4%B5%E0%A4%BE%E0%A4%A6%2C+%E0%A4%86%E0%A4%AA%E0%A4%95%E0%A4%BE+%E0%A4%A6%E0%A4%BF%E0%A4%A8+%E0%A4%B6%E0%A5%81%E0%A4%AD+%E0%A4%B9%E0%A5%8B%E0%A5%A4+%F0%9F%99%8F+%E0%A4%B9%E0%A4%AE%E0%A4%BE%E0%A4%B0%E0%A5%87+%E0%A4%AB%E0%A5%80%E0%A4%B2%E0%A5%8D%E0%A4%A1+%E0%A4%91%E0%A4%AB%E0%A4%BF%E0%A4%B8%E0%A4%B0++%E0%A4%B6%E0%A5%80%E0%A4%98%E0%A5%8D%E0%A4%B0+%E0%A4%B9%E0%A5%80+%E0%A4%86%E0%A4%AA%E0%A4%B8%E0%A5%87+%E0%A4%B8%E0%A4%82%E0%A4%AA%E0%A4%B0%E0%A5%8D%E0%A4%95+%E0%A4%95%E0%A4%B0%E0%A5%87%E0%A4%82%E0%A4%97%E0%A5%87"""
        url = f"""https://mediaapi.smsgupshup.com/GatewayAPI/rest?userid=**********&password=5ecCKv@F&send_to={number}&v=1.1&format=json&msg_type=TEXT&method=SENDMESSAGE&msg=%E0%A4%86%E0%A4%AA%E0%A4%95%E0%A5%87+%E0%A4%B8%E0%A4%AE%E0%A4%AF+%E0%A4%95%E0%A5%87+%E0%A4%B2%E0%A4%BF%E0%A4%8F+%E0%A4%A7%E0%A4%A8%E0%A5%8D%E0%A4%AF%E0%A4%B5%E0%A4%BE%E0%A4%A6%2C+%E0%A4%86%E0%A4%AA%E0%A4%95%E0%A4%BE+%E0%A4%A6%E0%A4%BF%E0%A4%A8+%E0%A4%B6%E0%A5%81%E0%A4%AD+%E0%A4%B9%E0%A5%8B%E0%A5%A4+%F0%9F%99%8F+%E0%A4%B9%E0%A4%AE%E0%A4%BE%E0%A4%B0%E0%A5%87+%E0%A4%AB%E0%A5%80%E0%A4%B2%E0%A5%8D%E0%A4%A1+%E0%A4%91%E0%A4%AB%E0%A4%BF%E0%A4%B8%E0%A4%B0++%E0%A4%B6%E0%A5%80%E0%A4%98%E0%A5%8D%E0%A4%B0+%E0%A4%B9%E0%A5%80+%E0%A4%86%E0%A4%AA%E0%A4%B8%E0%A5%87+%E0%A4%B8%E0%A4%82%E0%A4%AA%E0%A4%B0%E0%A5%8D%E0%A4%95+%E0%A4%95%E0%A4%B0%E0%A5%87%E0%A4%82%E0%A4%97%E0%A5%87"""
        payload = {}
        headers = {}
        self.opt_in_user(number, db_creds.SP_ProjectID, db_creds.SP_Password)
        response = requests.request("GET", url, headers=headers, data=payload)
        response_data = json.loads(response.text)
        full_id  = response_data.get("response", {}).get("id", None)
        reply_id, message_id = (None, None)
        if full_id and "-" in full_id:
            reply_id, message_id = full_id.split("-", 1)
        return message_id


    def twilio_message_api(self, to, template_name, db_creds):
        '''
        This function sends a message using the Twilio API. Without parameters.
        :param to: The recipient's phone number
        :param template_name: The template name
        :param db_creds: The database credentials
        :return: The SID of the message
        '''
        # Set up Twilio client
        client = Client(db_creds.APIVersion, db_creds.AuthorizationKey)
        message = client.messages.create(
            from_=db_creds.PhoneNumberID,
            content_sid=template_name,
            to=f"whatsapp:+91{to}"
        )
        logging.debug(f"Message SID: {message}")
        return message.sid

    def smartping_message_api(self, to, template_name, db_creds,Lang):
        print("IN sending smartping")
        print("NexttemplateID",template_name)
        language_map = {
            "Hindi": "hi",
            "Marathi": "mr",
            "Tamil": "ta",
            "Telugu": "te",
            "Malayalam": "ml",
            "Kannada": "kn"
        }

        if Lang is None:
            language_str = "Marathi"
        else:
            language_str = Lang.strip()

        language_code = language_map.get(language_str.strip())

        headers = {
            "X-AiSensy-Project-API-Pwd": db_creds.SP_Password,
            "Content-Type": "application/json"
        }
        payload = {
            "to": f"91{to}",
            "type": "template",
            "template": {
                "language": {
                    "policy": "deterministic",
                    "code": language_code
                },
                "name": template_name
            }
        }

        url = f"https://apis.aisensy.com/project-apis/v1/project/{db_creds.SP_ProjectID}/messages"

        try:
            response = requests.post(url, headers=headers, json=payload)
            print("Status Code:", response.status_code)
            print("Response Text:", response.text)
            response.raise_for_status()
            res_json = response.json()
            message_id = None
            if "messages" in res_json and len(res_json["messages"]) > 0:
                message_id = res_json["messages"][0].get("id")

            if message_id:
                return message_id
            else:
                print("Message ID not found in response")
                return None
        except requests.exceptions.RequestException as e:
            print(f"Error sending message: {e}")
            return None

    def get_template_variables(self ,WhatsAppQueueID,next_template_id):
        queue=chmodels.WhatsAppQueue.objects.filter(WhatsAppQueueID=WhatsAppQueueID).first()

        template_name = chmodels.WhatsAppTemplateMapping.objects.filter(WhatsAppTemplateMappingID = next_template_id).values_list('MetaTemplateID', flat=True).first()

        template_body = chmodels.WhatsAppTemplateMapping.objects.filter(WhatsAppTemplateMappingID = next_template_id).values_list('TemplateBody', flat=True).first()

        if queue.BranchName:
            template_body = template_body.replace("{1}", queue.BranchName.strip())
        
        to = queue.ContactNumber

        print("IN SEND SMARTPING")
        db_creds = chmodels.WhatsAppKeyMapping.objects.filter(BankMstID=queue.BankMstID).first()
        headers = {
            "X-AiSensy-Project-API-Pwd": db_creds.SP_Password,
            "Content-Type": "application/json"
        }

        
        payload = {
            "to": f"91{to}",
            "type": "template",
            "template": {
                "language": {
                    "policy": "deterministic",
                    "code": 'mr'
                },
                "name": template_name,
                "components": [
                    {
                        "type": "body",
                        "parameters": [
                            {
                                "type": "text",
                                "text": queue.BranchName.strip()
                            }
                        ]
                    }
                ]
            }
        }

        url = f"https://apis.aisensy.com/project-apis/v1/project/{db_creds.SP_ProjectID}/messages"

        try:
            response = requests.post(url, headers=headers, json=payload)
            print("Status Code:", response.status_code)
            print("Response Text:", response.text)
            response.raise_for_status()
            res_json = response.json()
            message_id = None
            if "messages" in res_json and len(res_json["messages"]) > 0:
                message_id = res_json["messages"][0].get("id")

            if message_id:
                self.save_message(queue, template_name, message_id, template_body)
                return message_id
            else:
                print("Message ID not found in response")
                return None
        except requests.exceptions.RequestException as e:
            print(f"Error sending message: {e}")
            return None

    def twilio_custom_message_api(self, to, message, db_creds):
        '''
        This function sends a custom message using the Twilio API.
        :param to: The recipient's phone number
        :param message: The message
        :param db_creds: The database credentials
        :return: The SID of the message
        '''
        # Set up Twilio client
        client = Client(db_creds.APIVersion, db_creds.AuthorizationKey)
        # Send message using Twilio
        message = client.messages.create(
            from_=db_creds.PhoneNumberID,
            body=message,
            to=f"whatsapp:+91{to}"
        )
        logging.debug(f"Message SID: {message}")

        return message.sid
    
    def pinnacle_message_api_with_params(self, to, db_creds, item, template_data_values):
        template_body, template_name, param = self.get_template_data(template_data_values[0], template_data_values[1], template_data_values[2], template_data_values[3])
        print(param,"PPPAAARRAAMM")
        print(f"Template name: {template_name}")
        print(f"Template Body: {template_body}")
        language_map = {
            "Hindi": "hi",
            "Marathi": "mr",
            "Tamil": "ta",
            "Telugu": "te",
            "Malayalam": "ml",
            "Kannada": "kn"
        }

        language_str = (item.Language or "Hindi").strip()
        language_code = language_map.get(language_str, "hi")

        var = [param[key] for key in sorted(param.keys(), key=int)]

        payload = json.dumps({
            "to": f"91{to}",
            "type": "template",
            "template": {
                "language": {
                    "code": language_code
                },
                "name": template_name,
                "components": [
                    {
                        "type": "body",
                        "parameters": [{"type": "text", "text": val} for val in var]
                    }
                ]
            },
            "messaging_product": "whatsapp"
        })

        headers = {
        'apikey': db_creds.AuthorizationKey,
        'Content-Type': 'application/json'
        }

        url = f"https://partnersv1.pinbot.ai/v3/{db_creds.PhoneNumberID}/messages"

        response = requests.post(url, headers=headers, data=payload)
        print("Status Code:", response.status_code)
        print("Response Text:", response.text)

        response.raise_for_status()
        # Optional: save message to DB or logs
        res_json = response.json()
        message_id = res_json.get("messages", [{}])[0].get("id", None)

        if message_id:
            self.save_message(item, template_name, message_id, template_body)
            return message_id
        else:
            print("Message ID not found in response")
            return None
        
    def pinnacle_message_api(self, to, template_name, db_creds,Lang):
        print("IN sending smartping")
        print("NexttemplateID",template_name)
        language_map = {
            "Hindi": "hi",
            "Marathi": "mr",
            "Tamil": "ta",
            "Telugu": "te",
            "Malayalam": "ml",
            "Kannada": "kn"
        }

        if Lang is None:
            language_str = "Hindi"
        else:
            language_str = Lang.strip()

        language_code = language_map.get(language_str.strip())

        headers = {
            'apikey': '9b4db4f0-57cd-11f0-98fc-02c8a5e042bd',
            'Content-Type': 'application/json'
        }
        
        payload = {
            "to": f"91{to}",
            "type": "template",
            "template": {
                "language": {
                    "code": language_code
                },
                "name": template_name
            },
            "messaging_product": "whatsapp"
        }

        url = "https://partnersv1.pinbot.ai/v3/670807389455093/messages"

        try:
            response = requests.post(url, headers=headers, json=payload)
            print("Status Code:", response.status_code)
            print("Response Text:", response.text)
            response.raise_for_status()
            res_json = response.json()
            message_id = None
            if "messages" in res_json and len(res_json["messages"]) > 0:
                message_id = res_json["messages"][0].get("id")

            if message_id:
                return message_id
            else:
                print("Message ID not found in response")
                return None
        except requests.exceptions.RequestException as e:
            print(f"Error sending message: {e}")
            return None
        

    def get_template_data(self, bankmstid, loanmstid, templatemapping_id, secondaryid=None):
        if secondaryid is None:
            query = f"select public.get_whatsapp_template_with_valuesv4({bankmstid},{loanmstid}, {templatemapping_id});"
        else :
            query = f"select public.get_whatsapp_template_with_valuesv4({bankmstid},{loanmstid}, {templatemapping_id},{secondaryid});"

        with connection.cursor() as dbconnection:
            dbconnection.execute(query)
            rows = dbconnection.fetchall()
            columns = [desc[0] for desc in dbconnection.description]
            if not rows:
                logging.error(f"No template data found for BankMstID: {bankmstid}, LoanMstID: {loanmstid}")

            template_data = json.loads(rows[0][0])  # Assuming the first column contains the JSON
            variables = template_data.get("variables", {})
            for key , value in variables.items():
                variables[key] = value.strip()

                # warna bank Masked
                if bankmstid == 36 and key == "2":
                    if len(value) > 4:
                        value = "X" * (len(value) - 4) + value[-4:]

                variables[key] = value
            template_body = template_data.get("TemplateBody", "")
            template_id = template_data.get("MetaTemplateID", "")

            # Replace placeholders {1}, {2}, etc. with actual values
            for key, value in variables.items():
                template_body = template_body.replace(f"{{{key}}}", str(value))

            return template_body, template_id, variables

    def wh_engine(self):
        '''
        This function is the main engine for the WhatsApp bot. It fetches data from TBL_WhatsApp_Queue,
        filters out the data that has already been sent, and sends the first message to the customer.
        '''
        logging.debug('Engine starting...')
        # queuedata = chmodels.WhatsAppQueue.objects.filter(WhatsAppTemplateMappingID__isnull=False, BankMstID=13, CreatedDate__date=date(2025, 5, 12)).annotate(
        #     bank_entry_number=Window(
        #         expression=RowNumber(),
        #         partition_by=[F('BankMstID')],
        #         order_by=F('WhatsAppQueueID').asc()
        #     )
        # ).exclude(
        #     WhatsAppQueueID__in=Subquery(chmodels.WhatsAppHistory.objects.filter(WhatsAppQueueID__isnull=False).values('WhatsAppQueueID'))
        # ).exclude(WhatsAppQueueID__isnull=True).order_by('bank_entry_number', 'WhatsAppQueueID')

        queuedata = chmodels.WhatsAppQueue.objects.annotate(
            created_date_only=TruncDate('CreatedDate')  # Extract date part
        ).filter(
            WhatsAppTemplateMappingID__isnull=False,
            created_date_only=date.today(),
            # created_date_only = date.today() - timedelta(days=1),
            ContactNumber__isnull=False,
            # BankMstID__in=[13,21,29,23,30,28,27,37,36,369,32],
            BankMstID__in=[13,21,29,23,30,28,27,37,36,369],
            # BankMstID__in=[378],
            # BranchMstID__in = []
            # BankMstID__in=[21,30,28,31],
            # WhatsAppTemplateMappingID=656
            # FlowID__in=(276, 278, 279, 281, 234, 266,267,268,269,270,271,272,273),
            # WhatsAppTemplateMappingID=607,
            # Language='Telugu'
            # WhatsAppQueueID__in=[7308221]
            # WhatsAppQueueID__in=[7308222,7308223,7308224,7308225,7308226,7308227,7308228,7308229,7308230]
        ).annotate(
            bank_entry_number=Window(
                expression=RowNumber(),
                partition_by=[F('BankMstID')],
                order_by=F('WhatsAppQueueID').asc()
            )
        ).exclude(
            WhatsAppQueueID__in=Subquery(
                chmodels.WhatsAppHistory.objects.filter(WhatsAppQueueID__isnull=False,
                CreatedDate=datetime.now().date()).values('WhatsAppQueueID')
            )
        ).exclude(
            WhatsAppQueueID__isnull=True
        ).order_by('bank_entry_number', 'WhatsAppQueueID')[:20000]  # Limit to 10000 records for performance
        # print(str(queuedata.query))
        print(f"Data fetched from TBL_WhatsApp_Queue size: {len(queuedata)}")

        if not queuedata:
            print("No data found in TBL_WhatsApp_Queue.")
            return Response({"message": "No data found in TBL_WhatsApp_Queue."})
        # self.iterate_df(queuedata)

        keymapping = {
            item.BankMstID.BankMstID: item for item in chmodels.WhatsAppKeyMapping.objects.all()
        }
        servicemapping = {
            item.BankMstID: item.WhatsappService for item in chmodels.BankMst.objects.all()
        }

        with connection.cursor() as dbconnection:
            with ThreadPoolExecutor(max_workers=10) as executor:
                futures = []
                for item in queuedata:
                    # sleep(1)  # Sleep to avoid overwhelming the database with requests
                    loanmstid = item.LoanMstID_id
                    bankmstid = item.BankMstID_id
                    secondaryid = item.SecondaryCustMstID
                    templatemapping_id = item.WhatsAppTemplateMappingID
                    template_data_values = (bankmstid, loanmstid, templatemapping_id, secondaryid)  # keep the order same as in get_template_data


                    db_creds = keymapping.get(bankmstid)
                    if not db_creds:
                        db_creds = chmodels.WhatsAppKeyMapping.objects.filter(BankMstID=bankmstid).first()
                        if not db_creds:
                            print(f"No credentials found for BankMstID: {bankmstid}")
                            break
                        keymapping[bankmstid] = db_creds


                    whatsapp_service = servicemapping.get(bankmstid)
                    if not whatsapp_service:
                        whatsapp_service = chmodels.BankMst.objects.filter(BankMstID=bankmstid).values_list('WhatsappService', flat=True).first()
                        if not whatsapp_service:
                            print(f"No WhatsappService found for BankMstID: {bankmstid}", flush=True)
                            break
                        servicemapping[bankmstid] = whatsapp_service

                    if not db_creds:
                        print(f"No credentials found for BankMstID: {item.BankMstID}", flush=True)
                        break

                    # if secondaryid is None:
                    #     query = f"select public.get_whatsapp_template_with_valuesv4({bankmstid},{loanmstid}, {templatemapping_id});"
                    # else :
                    #     query = f"select public.get_whatsapp_template_with_valuesv4({bankmstid},{loanmstid}, {templatemapping_id},{secondaryid});"

                    # dbconnection.execute(query)
                    # rows = dbconnection.fetchall()
                    # columns = [desc[0] for desc in dbconnection.description]
                    # if not rows:
                    #     logging.error(f"No template data found for BankMstID: {bankmstid}, LoanMstID: {loanmstid}")

                    # template_data = json.loads(rows[0][0])  # Assuming the first column contains the JSON
                    # variables = template_data.get("variables", {})
                    # for key , value in variables.items():
                    #     variables[key] = value.strip()

                    #     # warna bank Masked
                        # value = value.strip()
                        # if key == "2":
                        #     # Mask all but the last 4 characters
                        #     if len(value) > 4:
                        #         masked = "X" * (len(value) - 4) + value[-4:]
                        #         variables[key] = masked
                        #     else:
                        #         variables[key] = value  # If value too short, don't mask
                        # else:
                        #     variables[key] = value
                    # template_body = template_data.get("TemplateBody", "")
                    # template_id = template_data.get("MetaTemplateID", "")

                    # # Replace placeholders {1}, {2}, etc. with actual values
                    # for key, value in variables.items():
                    #     template_body = template_body.replace(f"{{{key}}}", str(value))

                    # Get recipient's phone number
                    to = item.ContactNumber
                    # Send message via Twilio
                    if whatsapp_service == 'Twilio':
                        print("Sending via Twilio")
                        # self.twilio_message_api_with_params(
                        #     to=to,
                        #     twilio_params=variables,  # Sending raw parameters if needed
                        #     template_name=template_id,  # Using MetaTemplateID for Twilio
                        #     db_creds=db_creds,
                        #     item=item,
                        #     template_body=template_body
                        # )
                        future = executor.submit(
                            self.twilio_message_api_with_params,
                            to,
                            # variables,
                            # template_id,
                            db_creds,
                            item,
                            # template_body
                            template_data_values,
                            # kwargs={
                            #     'to': to,
                            #     # 'twilio_params': variables,  # Sending raw parameters if needed
                            #     # 'template_name': template_id,  # Using MetaTemplateID for Twilio
                            #     'db_creds': db_creds,
                            #     'item': item,
                            #     # 'template_body': template_body,
                            #     'template_data_values': template_data_values
                            # }
                        )
                    elif whatsapp_service == 'Smartping':
                        print("Sending via Smartping")
                        # threading.Thread(target=self.smartping_message_api_with_params, args=(to, variables, template_id, db_creds, item, template_body)).start()
                        future = executor.submit(
                            self.smartping_message_api_with_params,
                            to,
                            # variables,
                            # template_id,
                            db_creds,
                            item,
                            # template_body
                            template_data_values,
                        )
                    # elif whatsapp_service == 'Smartping':
                    #     print("Sending via Smartping")
                    #     self.smartping_message_api_with_params_Legal_notice(
                    #         to=to,
                    #         param=variables,
                    #         template_name=template_id,
                    #         db_creds=db_creds,
                    #         item=item,
                    #         template_body=template_body
                    #     )
                    elif whatsapp_service == 'Gapshap':
                        # print("Sending via Gapshap")
                        future = executor.submit(
                            self.gupshup_message_api_with_params,
                            to,
                            # variables,
                            # template_id,
                            db_creds,
                            item,
                            # template_body
                            template_data_values
                        )
                        futures.append(future)
                        # threading.Thread(target=self.gupshup_message_api_with_params, args=(to, variables, template_id, db_creds, item, template_body)).start()
                    elif whatsapp_service == 'Pinnacle':
                        print("Sending via Pinnacle")
                        # threading.Thread(target=self.pinnacle_message_api_with_params, args=(to, variables, template_id, db_creds, item, template_body)).start()
                        future = executor.submit(
                            self.pinnacle_message_api_with_params,
                            to,
                            db_creds,
                            item,
                            template_data_values
                        )
                        futures.append(future)
                    else:
                        print(f"Unsupported WhatsApp service: {whatsapp_service}")
        # Wait for all futures to complete
        print(f"Total threads submitted: {len(futures)}", flush=True)
        for future in as_completed(futures):
            try:
                print("Future Completed", future.result())  # This will raise an exception if the thread failed
            except Exception as e:
                print(future.exception(), flush=True)
                logging.error(f"Error in thread execution: {e}")

        return Response({"message": "WhatsApp Engine completed successfully!"})

    # def iterate_df(self, data):
    #     '''
    #     This function iterates over the data fetched from WhatsAppQueue and sends the first message to the customer.
    #     :param data: The data fetched from TBL_WhatsApp_Queue
    #     '''
    #     for count, row in enumerate(data):
    #         # get flow type
    #         commflowid = row.CommFlowID
    #         bankmstid=row.BankMstID

    #         if hasattr(row, 'LoanMstID') and row.LoanMstID:
    #             try:
    #                 loan_data = AccountSummary.objects.filter(
    #                     LoanMstID=row.LoanMstID
    #                 ).values('DisbursementAmt', 'DisbursementDate','Branch').first()
                    
    #                 logging.info(f"Retrieved loan data for LoanMstID {row.LoanMstID}: {loan_data}")
    #             except Exception as e:
    #                 logging.error(f"Error retrieving loan data: {str(e)}")

    #         if type(row.LngMstID) == chmodels.LanguageMst:
    #             language = row.LngMstID
    #         else:
    #             language = chmodels.LanguageMst.objects.get(LanguageID=row.LngMstID)
    #         logging.info(f"Processing row {count + 1} with Identifier {row.Identifier}")

    #         flowid=chmodels.CommFlow.objects.filter(CommFlowID=commflowid,BankMstID=bankmstid).values_list('FlowID', flat=True).first()

    #         flow = self.get_flow(flowid, language)
    #         temp=chmodels.WhatsAppTemplateMapping.objects.get(WhatsAppTemplateMappingID=flow.WhatsAppTemplateMappingID, LngMstID=language)

    #         templateobj, variablesls = self.get_template(temp.MetaTemplateID)
            
    #         sid, templatetext = self.fill_template_with_fields(templateobj.MetaTemplateID, variablesls, templateobj.TemplateBody, row)
    #         self.save_message(templateobj, templatetext, row, sid)

    # def get_flow(self, commflow, language):
    #     '''
    #     This function fetches the flow from TBL_whastapp_flow_mapping.
    #     :param flow_type: The flow type
    #     :param language: The language
    #     :return: The flow
    #     '''
    #     # get flow from TBL_whastapp_flow_mapping
    #     obj = chmodels.WhatsAppFlowMapping.objects.get(FlowID=commflow, LanguageID=language, is_start=True)
    #     return obj

    def get_template(self, template_id):
        '''
        This function fetches the template and variables from TBL_whatsapp_template_mapping and TBL_whatsapp_variable_mapping.
        :param template_id: The template ID (given by Twilio)
        :return: The template and variables
        '''
        # get template from TBL_whatsapp_template_mapping
        if type(template_id) == chmodels.WhatsAppTemplateMapping:
            templateobj = template_id
        else:
            templateobj = chmodels.WhatsAppTemplateMapping.objects.get(WhatsAppTemplateMappingID=template_id)
        # variablesls = chmodels.WhatsappVariableMapping.objects.filter(WhatsAppTemplateMappingID=templateobj.WhatsAppTemplateMappingID).order_by('VariableNo')
        # return templateobj, variablesls
        return templateobj

    def fill_template_with_fields(self, templateid, variablesls, templatetext, row):
        """
        Fill the template with the fields from the row.
        :param templateid: The template ID (given by Twilio)
        :param variablesls: The variables list
        :param templatetext: The template text
        :param row: The row from the database
        :return: The SID of the message
        """
        # Get the fields from the row
        params = []
        for variable in variablesls:
            logging.info(f"Processing variable {variable.variable_no}, {variable.variable_field}, {str(getattr(row, variable.VariableField, ''))}")
            if type(getattr(row, variable.VariableField, '')) == type(datetime.now()):
                textvalue = getattr(row, variable.VariableField, '').strftime('%d-%m-%Y %H:%M:%S')
            elif type(getattr(row, variable.VariableField, '')) == type(datetime.now().date()):
                textvalue = getattr(row, variable.VariableField, '').strftime('%d-%m-%Y')
            else:
                textvalue = str(getattr(row, variable.VariableField, '')).strip()
            params.append({
                'type': 'text',
                'text':  textvalue,
            })
            templatetext = templatetext.replace(f"{{{{{variable.VariableNo}}}}}", textvalue)

        if len(params) > 0:
            params = self.convert_params_to_twilio_format(params)
        return self.send_first_whatsapp(templateid, params, row), templatetext

    def convert_params_to_twilio_format(self, params):
        """
        Convert Meta-style params to Twilio template variables.
        :param params: The parameters
        :return: The Twilio format template variables
        """
        twilio_params = {}
        for i, param in enumerate(params, start=1):
            if param['type'] == 'text':
                twilio_params[str(i)] = param['text']
        return json.dumps(twilio_params)

    def send_first_whatsapp(self, templateid, params, row):
        '''
        This function sends the first WhatsApp message to the customer.
        :param templateid: The template ID (given by Twilio)
        :param params: The parameters
        :param row: The row from the database
        :return: The SID of the message
        '''
        # Add your code here to send the first WhatsApp message
        # if row.organization_name:
        #     db_creds = chmodels.TBL_whastapp_key_mapping.objects.filter(organization_name=row.organization_name).first()
        # else:
        db_creds = chmodels.WhatsAppKeyMapping.objects.filter(BankMstID=row.BankMstID).first()
        if not db_creds:
            logging.error(f"No credentials found for bank {row.BankMstID}")
            raise Exception(f"No credentials found for bank {row.BankMstID}")

        if params:
            sid = self.twilio_message_api_with_params(f"91{row.ContactNumber}", params, templateid, db_creds)
        else:
            sid = self.twilio_message_api(f"91{row.ContactNumber}", templateid, db_creds)
        return sid

    def save_message(self, item, template_name, sid,message_content):
        '''
        This function saves the message to the database.
        :param templateid: The template ID (given by Twilio)
        :param message: The message to be saved
        :param row: The row from the database
        :param sid: The SID of the message
        '''
        # Save the message to the database
        histobj = chmodels.WhatsAppHistory.objects.filter(WhatsAppQueueID=item.WhatsAppQueueID).first()
        print(f"{now()}: WhatsAppQueueID: {item.WhatsAppQueueID}")
        if not histobj:
            histobj = chmodels.WhatsAppHistory()
        histobj.BankMstID = item.BankMstID
        histobj.BranchMstID = item.BranchMstID
        # histobj.WhatsAppQueueID = chmodels.WhatsAppQueue.objects.get(pk=item.WhatsAppQueueID)
        histobj.WhatsAppQueueID = item.WhatsAppQueueID
        # histobj.CustomerMstID = item.CustomerMstID
        histobj.LoanMstID = item.LoanMstID
        histobj.BranchCode = item.BranchCode
        histobj.LoanType = item.LoanType
        histobj.CustomerCode = item.CustomerCode
        histobj.CustomerName = item.CustomerName
        histobj.OverdueAmt = item.OverdueAmt
        histobj.Language = item.Language
        histobj.BankName = item.BankName
        histobj.DisbursementID = item.DisbursementID
        histobj.ContactNumber = item.ContactNumber
        # histobj.Identifier = item.Identifier
        if histobj.CreatedDate is None:
            histobj.CreatedDate = timezone.now()
        histobj.DisbursementID = item.DisbursementID
        histobj.Next_EMI_Amount = item.Next_EMI_Amount
        histobj.Next_EMI_Date = item.Next_EMI_Date
        histobj.Total_Collection = item.Total_Collection
        histobj.Latest_CollectedDate = item.Latest_CollectedDate
        histobj.Latest_CollectedAmt = item.Latest_CollectedAmt
        histobj.FlowID = item.FlowID
        histobj.CampaignMstID = item.CampaignMstID

        # histobj.MessageSID = sid  # Store Twilio message SID
        histobj.IsSent = True

        histobj.save()

        #Save in messages
        msg_obj = chmodels.WhatsApp_Messages(
            MessageDate=timezone.now(),
            WhatsAppQueueID=item.WhatsAppQueueID,
            MessageID=sid,
            AssignedTo="System",
            Sender=item.BankName,
            Content=message_content,
            Status='sent',
            CustomerNumber=item.ContactNumber,
            CustomerName=item.CustomerName,
            LoanMstID=item.LoanMstID,
            Button="",  # Add button if applicable
            # Template_ID=template_name,
            Type=item.FlowID,
            BankMstID=item.BankMstID,
            WhatsAppUserTemplateID=item.WhatsAppTemplateMappingID,
            CampaignMstID=item.CampaignMstID
        )
        msg_obj.save()

        logging.debug(f"Message saved to database")

    def save_message_gapshap(self, item, template_name, sid,message_content,reply_id):
        '''
        This function saves the message to the database.
        :param templateid: The template ID (given by Twilio)
        :param message: The message to be saved
        :param row: The row from the database
        :param sid: The SID of the message
        '''
        # Save the message to the database
        histobj = chmodels.WhatsAppHistory.objects.filter(WhatsAppQueueID=item.WhatsAppQueueID).first()
        print(f"WhatsAppQueueID: {item.WhatsAppQueueID}")
        if not histobj:
            histobj = chmodels.WhatsAppHistory()
        histobj.BankMstID = item.BankMstID
        histobj.BranchMstID = item.BranchMstID
        # histobj.WhatsAppQueueID = chmodels.WhatsAppQueue.objects.get(pk=item.WhatsAppQueueID)
        histobj.WhatsAppQueueID = item.WhatsAppQueueID
        # histobj.CustomerMstID = item.CustomerMstID
        histobj.LoanMstID = item.LoanMstID
        histobj.BranchCode = item.BranchCode
        histobj.LoanType = item.LoanType
        histobj.CustomerCode = item.CustomerCode
        histobj.CustomerName = item.CustomerName
        histobj.OverdueAmt = item.OverdueAmt
        histobj.Language = item.Language
        histobj.BankName = item.BankName
        histobj.DisbursementID = item.DisbursementID
        histobj.ContactNumber = item.ContactNumber
        # histobj.Identifier = item.Identifier
        histobj.CreatedDate = timezone.now()
        histobj.DisbursementID = item.DisbursementID
        histobj.Next_EMI_Amount = item.Next_EMI_Amount
        histobj.Next_EMI_Date = item.Next_EMI_Date
        histobj.Total_Collection = item.Total_Collection
        histobj.Latest_CollectedDate = item.Latest_CollectedDate
        histobj.Latest_CollectedAmt = item.Latest_CollectedAmt
        histobj.FlowID = item.FlowID
        histobj.CampaignMstID = item.CampaignMstID

        # histobj.MessageSID = sid  # Store Twilio message SID
        histobj.IsSent = True

        histobj.save()

        #Save in messages
        msg_obj = chmodels.WhatsApp_Messages(
            MessageDate=timezone.now(),
            WhatsAppQueueID=item.WhatsAppQueueID,
            MessageID=sid,
            AssignedTo="System",
            Sender=item.BankName,
            Content=message_content,
            Status='sent',
            CustomerNumber=item.ContactNumber,
            CustomerName=item.CustomerName,
            LoanMstID=item.LoanMstID,
            Button="",  # Add button if applicable
            # Template_ID=template_name,
            Type=item.FlowID,
            WhatsAppUserTemplateID=item.WhatsAppTemplateMappingID,
            GapshapReplyID = reply_id,
            BankMstID=item.BankMstID,
            CampaignMstID=item.CampaignMstID
        )
        msg_obj.save()

        logging.debug(f"Message saved to database")
        return msg_obj.MessageID


class WhatsAppWebHook(APIView):
    authentication_classes = []
    permission_classes = []

    def save_message(self, data):
        origmessageid = data.get('OriginalRepliedMessageSid')
        MessageSid = data.get('MessageSid')
        ButtonPayload = data.get('ButtonPayload')
        ButtonText = data.get('ButtonText')
        Body = data.get('Body')
        SmsStatus = data.get('SmsStatus')
        ProfileName = data.get('ProfileName')
        WaId = data.get('WaId')

        logging.info(f'''call back recived {data}''')
        print(f'''call back recived {data}''')
        origmessageobj = chmodels.WhatsApp_Messages.objects.filter(MessageID=origmessageid).first()
        wanumber = data.get('To')
        bank_id = None
        if wanumber:
            key = chmodels.WhatsAppKeyMapping.objects.filter(
                Extra_Column1=wanumber).first()
            if key:
                bank_id = key.BankMstID
        if origmessageobj:
            custmessage = chmodels.WhatsApp_Messages()
            custmessage.MessageDate = timezone.now()
            custmessage.LoanMstID = origmessageobj.LoanMstID
            custmessage.WhatsAppQueueID = origmessageobj.WhatsAppQueueID
            custmessage.Assigned_to = None
            custmessage.MessageID = MessageSid
            # custmessage.Message_DateTime = timezone.now()
            custmessage.Sender = ProfileName
            custmessage.Content = Body
            custmessage.Button = ButtonPayload
            # custmessage.Template_ID = origmessageobj.Template_ID
            custmessage.Status = SmsStatus
            custmessage.Type = origmessageobj.Type
            custmessage.WhatsAppQueueID = origmessageobj.WhatsAppQueueID
            custmessage.CustomerNumber = WaId[2:]
            custmessage.CustomerName = origmessageobj.CustomerName
            custmessage.BankMstID = bank_id
            custmessage.CampaignMstID = origmessageobj.CampaignMstID
            custmessage.save()

            templateobj = WhatsAppFlowMapping.objects.filter(
                FlowID=origmessageobj.Type, Response=ButtonText, WhatsAppTemplateMappingID=origmessageobj.WhatsAppUserTemplateID).first()

            oldobj = webmodels.Response.objects.filter(
                # WhatsappQueueID=origmessageobj.WhatsAppQueueID
                LoanMstID=origmessageobj.LoanMstID.pk if origmessageobj.LoanMstID else None
            ).order_by('-ResponseDateTime').first()

            if oldobj:
                oldobj.Status = "Terminated"
                oldobj.save()

            next_template_id = templateobj.NextTemplate if templateobj else None
            # if classification:
            logging.info(f'''text template id  {next_template_id}''')

            messagehistory = chmodels.WhatsAppHistory.objects.filter(WhatsAppQueueID=origmessageobj.WhatsAppQueueID).first()

            # Create a new Response object
            new_response = webmodels.Response()
            new_response.ResponseDateTime = datetime.now()
            new_response.WhatsappQueueID = messagehistory.WhatsAppQueueID
            new_response.LoanMstID = messagehistory.LoanMstID.pk if messagehistory.LoanMstID else None

            if ButtonText in ["होय","हाँ","Yes","ஆம்","అవును","ಹೌದು","അതെ"]:
                print("IN Promiseeee")
                # new_response.LoanMstID=origmessageobj.LoanMstID,
                new_response.BlasterQueueID=None
                new_response.IVRQueueID=None
                new_response.VoiceBotQueueID=None
                # new_response.WhatsappQueueID=origmessageobj.WhatsAppQueueID,
                new_response.AllocationID=None
                new_response.FeedbackID=None
                new_response.ModeOfPayment=None
                new_response.PromiseDateTime=datetime.now()
                new_response.Status="Pending"
                new_response.Amount=messagehistory.OverdueAmt
                # new_response.ResponseDateTime=now(),
                new_response.BankMstID=messagehistory.BankMstID.pk if messagehistory.BankMstID else None
                new_response.BranchMstID=messagehistory.BranchMstID.pk if messagehistory.BranchMstID else None
                new_response.CampaignMstID=messagehistory.CampaignMstID
                
                bank = chmodels.BankMst.objects.filter(
                    pk=messagehistory.BankMstID.pk).first() if messagehistory.BankMstID else None
                if bank and bank.Is_UPI:
                    print("HELOOOOOOO")
                    print(messagehistory.BankMstID.pk)
                    print(messagehistory.LoanMstID.pk)
                    print(messagehistory.CustomerName)
                    print(messagehistory.ContactNumber)
                    print(messagehistory.BankName)
                    print(messagehistory.OverdueAmt)
                    
                    bankid=messagehistory.BankMstID.pk
                    loanmstid=messagehistory.LoanMstID.pk
                    customername=messagehistory.CustomerName
                    customernumber=messagehistory.ContactNumber
                    bankname=messagehistory.BankName
                    overdueamt=messagehistory.OverdueAmt
                    create_payment_view(
                        bankid, loanmstid, customername, customernumber, bankname, overdueamt)

            elif ButtonText in ["नाही","नहीं","No","இல்லை","ಇಲ್ಲ","కాదు","ഇല്ല"]:
                print("IN Denialssss")
                new_response.BlasterQueueID=None
                new_response.IVRQueueID=None
                new_response.VoiceBotQueueID=None
                # new_response.WhatsappQueueID=origmessageobj.WhatsAppQueueID,
                new_response.AllocationID=None
                new_response.FeedbackID=None
                new_response.ModeOfPayment=None
                # new_response.PromiseDateTime=datetime.now()
                new_response.Status="Denied"
                new_response.Amount=messagehistory.OverdueAmt
                # new_response.ResponseDateTime=now(),
                new_response.BankMstID=messagehistory.BankMstID.pk if messagehistory.BankMstID else None
                new_response.BranchMstID=messagehistory.BranchMstID.pk if messagehistory.BranchMstID else None
                new_response.CampaignMstID=messagehistory.CampaignMstID

            elif ButtonText in ["आधीच भरले आहे","भुगतान कर दिया है","Already Paid",'मैंने पहले ही भर दिया है','पहले ही कर चुका/चुकी हूँ','रक्कम भरलेली आहे',"ஏற்கனவே செலுத்தியுள்ளேன்","ಈಗಾಗಲೇ ಪಾವತಿಸಿದೆ","ఇప్పటికే చెల్లించాను","ഇതിനകം അടച്ചിട്ടുണ്ട്"]:
                print("IN Claimmm")
                new_response.BlasterQueueID=None
                new_response.IVRQueueID=None
                new_response.VoiceBotQueueID=None
                # new_response.WhatsappQueueID=origmessageobj.WhatsAppQueueID,
                new_response.AllocationID=None
                new_response.FeedbackID=None
                new_response.ModeOfPayment=None
                # new_response.PromiseDateTime=datetime.now()
                new_response.Status="Already Paid"
                new_response.Amount=messagehistory.OverdueAmt
                new_response.Despute="Despute"
                new_response.BankMstID=messagehistory.BankMstID.pk if messagehistory.BankMstID else None
                new_response.BranchMstID=messagehistory.BranchMstID.pk if messagehistory.BranchMstID else None
                new_response.CampaignMstID=messagehistory.CampaignMstID
            print("Saved")
            new_response.mode_of_payment = ButtonText
            new_response.save()
            # if not oldobj:
            #     next_template_id = None

            logging.info(f'''call back recived {messagehistory.WhatsAppQueueID}''')
            print(f'''call back recived {messagehistory.WhatsAppQueueID}''')
            print(f'''next template id  {next_template_id}''')
            return messagehistory.WhatsAppQueueID, next_template_id
        else:
            historymsg = chmodels.WhatsAppHistory.objects.filter(ContactNumber=WaId[2:]).order_by('-CreatedDate').first()
            custmessage = chmodels.WhatsApp_Messages()
            custmessage.MessageDate = timezone.now()
            custmessage.LoanMstID = historymsg.LoanMstID
            custmessage.WhatsAppQueueID = None
            custmessage.Assigned_to = None
            custmessage.MessageID = MessageSid
            # custmessage.Message_DateTime = timezone.now()
            custmessage.Sender = ProfileName
            custmessage.Content = Body
            custmessage.Button = ButtonPayload
            # custmessage.Template_ID = None
            custmessage.Status = SmsStatus
            custmessage.Type = None
            # custmessage.Identifier = None
            custmessage.CustomerNumber = WaId[2:]
            custmessage.CustomerName=historymsg.CustomerName
            custmessage.BankMstID = bank_id
            custmessage.CampaignMstID = historymsg.CampaignMstID
            custmessage.save()
            return None, None
        return origmessageobj.WhatsAppQueueID, None

    def reply_engine(self, request):
        WhatsAppQueueID, next_template_id = self.save_message(request.data)
        logging.info(f'''Request Dataaaa {request.data}''')
        if next_template_id:
            # contact_number = request.data.get('WaId')
            # process_customer_response(contact_number[-10:])
            data = chmodels.WhatsAppHistory.objects.filter(WhatsAppQueueID=WhatsAppQueueID).first()
            engineobj = WhatsAppEngine()
            # templateobj, variablesls = engineobj.get_template(next_template_id)
            templateobj = engineobj.get_template(next_template_id)
            # sid, templatetext = engineobj.fill_template_with_fields(templateobj.MetaTemplateID, variablesls, templateobj.TemplateBody, data)
            db_creds = chmodels.WhatsAppKeyMapping.objects.filter(BankMstID=data.BankMstID).first()
            sid = engineobj.twilio_message_api(data.ContactNumber,templateobj.MetaTemplateID,db_creds)
            engineobj.save_message(data ,templateobj,sid,templateobj.TemplateBody )
            
        else:
            message = request.data.get('Body')
            contact_number = request.data.get('WaId')
            # process_customer_response(contact_number[-10:])
            data = chmodels.WhatsAppHistory.objects.filter(ContactNumber=contact_number[-10:]).last()

            message_history = list(
                chmodels.WhatsApp_Messages.objects.filter(
                    CustomerNumber=contact_number[-10:], LoanMstID=data.LoanMstID.pk if data.LoanMstID else None)
                .order_by('MessageDate')
                .values('Sender', 'Content')
            )

            # Format history as a structured string for LLM context
            formatted_history = "\n".join([f"{msg['Sender']}: {msg['Content']}" for msg in message_history])
            logging.info(f'''formatted_history{formatted_history}''')
            print("formatted_history",formatted_history)
            # llm_response = LLMWhatsApp(
            #     bot_name="CollectionBot",
            #     message=message,
            #     ContactNumber=contact_number,
            #     conversation_history=formatted_history
            # )
            # body = llm_response['res']
            # dbcreds = chmodels.WhatsAppKeyMapping.objects.filter(BankMstID=data.BankMstID).first()
            # engineobj = WhatsAppEngine()
            # # engineobj.send_message(contact_number, llm_response['res'])
            # sid = engineobj.twilio_custom_message_api(contact_number, body, dbcreds)

            # engineobj.save_message(data, body, sid, body)


    def post(self, request):
        logging.debug('Webhook received! with Orginal Message ID: {}'.format(request.data.get('OriginalRepliedMessageSid')))
        Thread(target=self.reply_engine, args=(request,)).start()
        return HttpResponse('Success')

def process_customer_response(contact_number):
    """Analyzes customer response and stores it in the Response table based on detected intent."""

    # Fetch the latest conversation history
    data = chmodels.WhatsAppHistory.objects.filter(ContactNumber=contact_number).order_by('-CreatedDate').first()
    if not data:
        logging.warning(f"No WhatsApp history found for contact: {contact_number}")
        return {"status": "No conversation history found"}
    created_date = data.CreatedDate
    message_history = list(
        chmodels.WhatsApp_Messages.objects.filter(
            CustomerNumber=contact_number, 
            MessageDate__gte=created_date
        )
        .order_by('MessageDate')
        .values('Sender', 'Content')
    )

    # Format chat history for intent analysis
    formatted_history = "\n".join([f"{msg['Sender']}: {msg['Content']}" for msg in message_history])
    logging.info(f"Formatted Historyyyyyyyyyy: {formatted_history}")

    # Step 1: Call Intent Analysis Function
    intent_result = AnalyzeChatConclusion(
        ContactNumber=contact_number,
        conversation_history=formatted_history
    )

    # Extract intent classification
    intent_category = intent_result.get('analysis', 'Unknown').strip()  # Ensure clean extraction
    valid_intents = {"Positive", "Negative", "Already Paid", "Wrong Number"}

    if intent_category not in valid_intents:
        logging.warning(f"Unexpected Intent Received: {intent_category}")
        intent_category = "Unknown"

    status_mapping = {
        "Positive": "Pending",
        "Negative": "Denied",
        "Already Paid": "Resolved",
        "Wrong Number": "Wrong_Number",
    }

    status = status_mapping.get(intent_category, "Unclassified")
    
    wrong_number_value = "Wrong Number" if intent_category == "Wrong Number" else None

    existing_response = webmodels.Response.objects.filter(
        LoanMstID=data.LoanMstID.pk if data.LoanMstID else None,
        WhatsappQueueID=data.WhatsAppQueueID
    ).first()

    if existing_response:
        # Update existing record
        existing_response.Status = status
        existing_response.PromiseDateTime = now() if status == "Pending" else None
        existing_response.ResponseDateTime = now()
        existing_response.wrong_number = wrong_number_value
        existing_response.save()
        logging.info(f"Response Record Updated: {existing_response.ResponseID}")
        return {"status": "Customer response updated."}
    else:
        # Create a new record
        response_entry = webmodels.Response.objects.create(
            LoanMstID=data.LoanMstID.pk if data.LoanMstID else None,
            BlasterQueueID=None,
            IVRQueueID=None,
            VoiceBotQueueID=None,
            WhatsappQueueID=data.WhatsAppQueueID,
            AllocationID=None,
            FeedbackID=None,
            ModeOfPayment="N/A",
            ExtraColumn1="",
            ExtraColumn2="",
            ExtraColumn3="",
            ExtraColumn4="",
            ExtraColumn5="",
            PromiseDateTime=now() if status == "Pending" else None,
            Status=status,
            Amount=data.OverdueAmt,
            ResponseDateTime=now(),
            BankMstID=data.BankMstID.pk if data.BankMstID else None,
            BranchMstID=data.BranchMstID.pk if data.BranchMstID else None,
            wrong_number=wrong_number_value
        )

    logging.info(f"Response Record Created: {response_entry.ResponseID}")
    return {"status": "Customer response analyzed and saved."}


class WhatsAppStatus(APIView):
    authentication_classes = []
    permission_classes = []

    def post(self, request):
        MessageSid = request.data['MessageSid']
        MessageStatus = request.data['MessageStatus']
        logging.debug('Status received for sid: {} with status {}'.format(MessageSid, MessageStatus))
        messageobj = chmodels.WhatsApp_Messages.objects.filter(MessageID=MessageSid).first()
        if messageobj and MessageStatus in ['delivered', 'read']:
            try:
                histobj = chmodels.WhatsAppHistory.objects.filter(WhatsAppQueueID=messageobj.WhatsAppQueueID).first()
                if histobj:
                    if MessageStatus == 'delivered':
                        histobj.IsDelivered = True
                    elif MessageStatus == 'read':
                        histobj.IsRead = True
                    histobj.save()
            except Exception as e:
                logging.error(f"Error while updating status: {e}")
            messageobj.Status = MessageStatus
            messageobj.save()
        return HttpResponse('Success')


class WhatsAppError(APIView):
    authentication_classes = []
    permission_classes = []

    def post(self, request):
        logging.error('Error received!')
        logging.error(request.data)
        return HttpResponse('Success')

    
def LLMWhatsApp(bot_name, message, ContactNumber,conversation_history=None):
    queue = chmodels.WhatsAppHistory.objects.filter(ContactNumber=ContactNumber[-10:]).last()    
    # data = dict(chmodels.WhatsApp_Messages.objects.filter(WhatsAppQueueID=queue.WhatsAppQueueID).order_by('Message_DateTime').values_list('Sender', 'Message_Content'))
    logging.info(f'''Inside LLMMMM''')
    if queue.BankName in ['Vinayana', 'Vinayana finance']:
        language_instruction = "Please use only the Hindi language throughout this conversation. All responses must be polite, formal, and concise. Respond strictly based on what the customer has said—do not repeat their words or make assumptions. Maintain a respectful tone at all times.\n\n"
    else:
        language_instruction = ""

    Collection = f"""{queue.BankName} is a collection bot designed to communicate with customers via WhatsApp for the repayment of outstanding money.

        Launched in 2023, {queue.BankName} utilizes advanced natural language processing and artificial intelligence to manage debt collection interactions efficiently. The bot can handle various customer queries, negotiate repayment plans, and provide account balance information.
        {language_instruction}
        {queue.BankName} operates 24/7, offering a seamless and user-friendly experience for customers. Automating the collection process helps businesses reduce operational costs and improve repayment rates. The system ensures compliance with regulations and maintains professionalism throughout all interactions.

        {queue.BankName} is designed to adapt to different industries, making it a versatile solution for businesses of all sizes. The bot's primary goal is to assist customers in managing their debts while maintaining a respectful and empathetic approach.

        {queue.BankName} is a female bot using normal day-to-day language for communication, focusing on receiving promises to pay.
        Do not ask for inputs like loan numbers or phone numbers—just focus on taking payment promises. Once received, facilitate the next steps with brief responses.
        When concluding the conversation, say, "धन्यवाद, तुमचा दिवस शुभ जावो." Use this only when closing the chat.
        If there is no reply from the customer, repeat the previous question.
        Use polite, respectful female language without assuming the customer's gender.
        Today's date is {datetime.now().date()} and the day is {datetime.today().strftime('%A')}.
        Only generate responses based on what the customer has said. Do not repeat their words in responses, and avoid assumptions.
        Interaction Plan for WhatsApp:

        Handeling Customer Ready to pay Responses: If the customer is ready to pay thank them for conformation .

        Negative Responses: If the customer refuses to pay, explain the importance of repayment and the consequences of non-payment, like a poor credit score and ask if they can make the payment. If they still refuse, acknowledge this and conclude the conversation.

        Outstanding Amount Issues: If the customer disputes the amount, direct them to submit acknowledgment on the bank's website or by contacting the bank directly.

        Uninstructed Responses: If the customer provides information or requests outside of the guidelines, respond by saying, "Our agents will investigate and connect with you."

        Strictly do not give the same response and keep the conversation short
        
        If the customer raises a complaint, assure them that we will take immediate and appropriate action.  
        
        Ensure that you acknowledge their concern and pass it to the appropriate team.

        Final Steps: End the chat by thanking the customer and providing further assistance information.
        
        below given is the customer history happened on whatsapp in the dict use this to give your response 
        
        History : [ {conversation_history} ]
        
    """         
    global modal
    model = GenerativeModel(
        "gemini-2.0-flash-001", system_instruction=[Collection]
    )   
    
    global chat
    chat = model.start_chat()
    
    generation_config = {
        "max_output_tokens": 8192,
        "temperature": 1,
        "top_p": 0.95,
    }

    res = chat.send_message(
        [f"respond to this message considering Customer History , message : {message}"],
        generation_config=generation_config,
    )
    text = res.text
    return {'res': text, 'queue': queue}

def AnalyzeChatConclusion(ContactNumber, conversation_history):
    queue = chmodels.WhatsAppHistory.objects.filter(ContactNumber=ContactNumber[-10:]).last()

    analysis_prompt = f"""{queue.BankName} is a collection bot designed for debt recovery via WhatsApp.  
    Your task is to analyze the given conversation history and determine its conclusion:  

    Possible outcomes:  
    - Positive: The customer agreed to pay or showed willingness.  
    - Negative: The customer refused to pay or showed resistance.  
    - Already Paid: The customer mentioned that they have already made the payment.  
    - Wrong Number: The customer mentioned that the provided contact number is incorrect.  

    **Instructions:**  
    - Review the chat history carefully.  
    - If the customer has committed to a payment, mark it as **Positive**.  
    - If the customer refused, mark it as **Negative**.  
    - If the customer says they have already paid, mark it as **Already Paid**.  
    - The customer mentioned that the provided contact number is incorrect **Wrong Number**.
    - Provide only the conclusion in the response without extra details.  

    **Chat History:**  
    {conversation_history}  

    Respond with just one of the following words: Positive, Negative, Already Paid.
    """

    model = GenerativeModel("gemini-1.5-flash-001", system_instruction=[analysis_prompt])    
    chat = model.start_chat()
    
    generation_config = {
        "max_output_tokens": 1024,
        "temperature": 1,
        "top_p": 0.95,
    }

    res = chat.send_message(["Analyze the chat history and provide a conclusion."], generation_config=generation_config)
    
    extracted_intent = res.text.strip()  # Ensure clean extraction
    return {'analysis': extracted_intent, 'queue': queue}


class DialerView(APIView):
    permission_classes = [IsAuthenticated]
    # authentication_classes = [BasicAuthentication, JWTAuthentication]
    ASTERISK_DETAILS = settings.ASTERISK_DETAILS

    def initiate_call(self, bm_phone, customer_phone, username, bankname):
        print("Initiaitng callll")
        manager = Manager()
        manager.connect(self.ASTERISK_DETAILS['HOST'])
        manager.login(self.ASTERISK_DETAILS['USERNAME'], self.ASTERISK_DETAILS['PASSWORD'])
        channel = f"{self.ASTERISK_DETAILS['ASTERISK_PREFIX']}{bm_phone}@{self.ASTERISK_DETAILS['ASTERISK_EXTENSION']}"
        channel2 = f"{self.ASTERISK_DETAILS['ASTERISK_PREFIX']}{customer_phone}@{self.ASTERISK_DETAILS['ASTERISK_EXTENSION']}"
        myuuid = str(uuid.uuid4())
        action = {
            "Action": "Originate",
            "Channel": f"PJSIP/{channel}",  # Replace with the channel you want to call
            "Exten": f"{self.ASTERISK_DETAILS['ASTERISK_EXTENSION']}",
            "Context": self.ASTERISK_DETAILS['ASTERISK_CONTEXT'],
            "Priority": "1",
            "Timeout": "20000",
            "CallerID": f"{bm_phone}<{username}>",  # Replace with the caller ID you want to use
            "ChannelId": myuuid,
            "Application": "Dial",
            "Data": f"PJSIP/{channel2}",
            "account": f"{bankname}",
            "Variable": f"CDR(rec_url)={self.ASTERISK_DETAILS['GCP_BUCKET_URL'].rstrip('/')}/{self.ASTERISK_DETAILS['GCP_BUCKET_NAME']}/call_{myuuid}.wav"
        }

        filepath = os.path.join(self.ASTERISK_DETAILS['REC_DIR'], f'call_{myuuid}.wav')
        # mix monitor
        mix_monitor = {
            'Action': 'MixMonitor',
            'Channel': myuuid,
            'File': filepath,
            'Command': f"{os.path.join(self.ASTERISK_DETAILS['BASE_DIR'], self.ASTERISK_DETAILS['FILE_NAME'])} {self.ASTERISK_DETAILS['GCP_BUCKET_NAME']} {filepath}"
        }
        response = manager.send_action(action)
        print("Response from Originate Action:", response)
        response = manager.send_action(mix_monitor)
        print("Response from MixMonitor Action:", response)
        manager.logoff()
        return response, myuuid

    def post(self, request):
        print(" IN Dialer API")
        bm_phone = request.data.get('bm_phone')
        loanMstId = request.data.get('loanMstId')
        # bank_name = request.data.get('bank_name')
        # if not bank_name:
        #     bank_name=request.user.BankMstID.BankName
        print(loanMstId,"LLLLLLLLLLLLLLLLL")
        try:
            loanobj = LoanMst.objects.get(LoanMstID=loanMstId)
        except LoanMst.DoesNotExist:
            return Response({'message': 'LoanMstID is not valid'}, status=400)
        print("Request Data:", request.data)
        
        Bankid = request.data.get('Bankid')
        if not Bankid:
            Bankid = request.user.BankMstID.BankName
        
        username = request.data.get('username')
        if not username:
            username = request.user.username
            
        customer_phone = loanobj.CustomerMstID.MobileNumber
        print(customer_phone,"*************")
        if not bm_phone or not customer_phone:
            return Response({'message': 'Please provide bm_phone and customer_phone'}, status=400)
        response, myuuid = self.initiate_call(
            bm_phone, customer_phone, username, Bankid)
        
        Dialer.objects.create(
            BankMstID=loanobj.BankMstID,
            BranchMstID=loanobj.BranchMstID,
            LoanMstID=loanobj,
            CustomerMstID=loanobj.CustomerMstID,
            Call_ID=myuuid,
            DateTime=timezone.now(),
            RecordingURL=f"{self.ASTERISK_DETAILS['GCP_BUCKET_URL'].rstrip('/')}/{self.ASTERISK_DETAILS['GCP_BUCKET_NAME']}/call_{myuuid}.wav",
            InitiatingNumber=bm_phone,
        )
        request.user.MobileNumber = bm_phone
        request.user.save()
        if str(response) == 'Success':
            return Response({'message': 'Call has been initiated', 'call_id': myuuid}, status=200)
        else:
            return Response({'message': 'Failed to initiate call', 'call_id': myuuid}, status=400)


class TBLDialerHistViewSet(ModelViewSet):
    permission_classes = [IsAuthenticated]
    # authentication_classes = [BasicAuthentication, JWTAuthentication]
    serializer_class = TBLLoanMasterSerializer
    search_fields = ['BankMstID__BankName', 'BranchMstID__BranchName', 'CustMstID__CustomerName']
    filter_fields = ['BankMstID', 'BranchMstID', 'LoanMstID', 'CustMstID']
    pagination_class = None

    def get_queryset(self):
        query = LoanMst.objects.none()
        if self.request.user.is_superuser:
            query = LoanMst.objects.filter(tbl_dialer_hist__isnull=False)
        elif self.request.user.Designation == 'HO':
            query = LoanMst.objects.filter(BankMstID=self.request.user.BankMstID, tbl_dialer_hist__isnull=False)
        elif self.request.user.Designation == 'BM':
            query = LoanMst.objects.filter(BranchMstID=self.request.user.BranchMstID, tbl_dialer_hist__isnull=False)
        return query.distinct()
    

# def send_message_to_bu(mobile, officer_name, ptp_records):
    # bank_mst_id = ptp_records[0]["BankMstID"]
    # db_creds = chmodels.WhatsAppKeyMapping.objects.filter(
    #     BankMstID=bank_mst_id).first()
    # client = Client(db_creds.APIVersion, db_creds.AuthorizationKey)
    # message_lines = [f"नमस्ते {officer_name}, आज के लिए PTP रिकॉर्ड्स:"]
    # for record in ptp_records:
    #     message_lines.append(
    #         f"LoanID: {record['LoanMstID']}, ₹{record['Amount']}, "
    #         f"Date: {record['PromiseDateTime'].strftime('%d-%m-%Y')}, "
    #         f"Status: {record['Status']}"
    #     )
    # final_message = "\n".join(message_lines)
    # print(final_message)
    # print(db_creds.PhoneNumberID)
    # print(mobile)
    # print(db_creds.PhoneNumberID,"**********")
    # try:
    #     message = client.messages.create(
    #         body=final_message,
    #         from_=db_creds.PhoneNumberID,
    #         to=f"whatsapp:+91{mobile}"  # assuming Indian numbers
    #     )
    #     print(f"Message sent to {mobile}, SID: {message.sid}")
    # except Exception as e:
    #     print(f"Failed to send message to {mobile}: {str(e)}")


# def send_message_to_bu(mobile, officer_name, ptp_records, total_count, total_amount):
#     import tempfile
#     import os
#     import pandas as pd
#     from twilio.rest import Client
#     import json
#     import time
#     from google.cloud import storage
#     from fpdf import FPDF

#     bank_mst_id = ptp_records[0]["BankMstID"]
#     db_creds = chmodels.WhatsAppKeyMapping.objects.filter(
#         BankMstID=bank_mst_id).first()
#     client = Client(db_creds.APIVersion, db_creds.AuthorizationKey)

#     df = pd.DataFrame(ptp_records)
#     temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
#     pdf_path = temp_file.name

#     pdf = FPDF()
#     pdf.add_page()
#     pdf.set_font("Arial", size=10)

#     # Add table header
#     col_width = pdf.w / (len(df.columns) + 1)
#     row_height = 8
#     for col in df.columns:
#         pdf.cell(col_width, row_height, txt=str(col), border=1)
#     pdf.ln(row_height)

#     # Add data rows
#     for _, row in df.iterrows():
#         for item in row:
#             pdf.cell(col_width, row_height, txt=str(item), border=1)
#         pdf.ln(row_height)

#     pdf.output(pdf_path)
    
#     os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "/home/<USER>/Product/SmartCollect2-BE/SmartCollect2/verdant-lattice-425012-f6-189acb3a46c1.json"

#     try:
#         # Configure GCS client
#         storage_client = storage.Client()

#         # Define bucket name and file name
#         bucket_name = 'newrecordingsvoicebot'
#         file_name = f"ptp_records_{mobile}_{int(time.time())}.pdf"

#         # Get bucket and upload file
#         try:
#             bucket = storage_client.bucket(bucket_name)
#             blob = bucket.blob(file_name)

#             # Upload PDF
#             blob.upload_from_filename(pdf_path)

#             media_url = f"https://storage.googleapis.com/{bucket_name}/{file_name}"
#             print(media_url, "*************")
#             # Send WhatsApp message with attachment
#             message = client.messages.create(
#                 to=f"whatsapp:+91{mobile}",
#                 messaging_service_sid=db_creds.PhoneNumberID,
#                 content_sid="HX17a307c50e2956c12c247186f5e63089",
#                 content_variables=json.dumps({
#                     "1": officer_name,
#                     "2": str(total_count),
#                     "3": f"{total_amount:.2f}",
#                     "5": file_name
#                 }),
#             )
#             print(
#                 f"Template message with Excel attachment sent to {mobile}, SID: {message.sid}")

#         except Exception as e:
#             print(f"GCS upload failed: {str(e)}")

#     except Exception as e:
#         print(f"Failed to send WhatsApp template to {mobile}: {str(e)}")
#     finally:
#         os.remove(pdf_path)

def SendMediaMessageTwilio(mobile, db_creds: chmodels.WhatsAppKeyMapping, officer_name, total_count, total_amount, file_name, media_url):
    # Send WhatsApp message with attachment
    client = Client(db_creds.APIVersion, db_creds.AuthorizationKey)
    message = client.messages.create(
        to=f"whatsapp:+91{mobile}",
        messaging_service_sid=db_creds.PhoneNumberID,
        # content_sid="HX17a307c50e2956c12c247186f5e63089",
        content_sid="HX4911671052c883135f66485e0f2ae117",
        content_variables=json.dumps({
            "1": officer_name,
            "2": str(total_count),
            "3": f"{total_amount:.2f}",
            "5": file_name
        }),
    )
    return message


def SendMediaMessageGapshap(mobile, db_creds: chmodels.WhatsAppKeyMapping, officer_name, total_count, total_amount, file_name, media_url, Identifier=None):
    url = f"""https://mediaapi.smsgupshup.com/GatewayAPI/rest?userid=**********&password={db_creds.SP_Password}&send_to={mobile}&v=1.1&format=json&msg_type=DOCUMENT&method=SENDMEDIAMESSAGE&caption=%2AGreeting+from+BSS+Microfinance%2A%0A%0A%F0%9F%93%A2+%E0%A4%A8%E0%A4%AE%E0%A4%B8%E0%A5%8D%E0%A4%A4%E0%A5%87+{officer_name}%29+%E0%A4%9C%E0%A5%80%2C%0A+%0A%E0%A4%86%E0%A4%AA%E0%A4%95%E0%A5%80+%E0%A4%B6%E0%A4%BE%E0%A4%96%E0%A4%BE+%E0%A4%95%E0%A5%87+{total_count}++%E0%A4%97%E0%A5%8D%E0%A4%B0%E0%A4%BE%E0%A4%B9%E0%A4%95+%E0%A4%85%E0%A4%AA%E0%A4%A8%E0%A5%80+%E0%A4%8B%E0%A4%A3+%E0%A4%95%E0%A5%80+%E0%A4%AC%E0%A4%95%E0%A4%BE%E0%A4%AF%E0%A4%BE+%E0%A4%B0%E0%A4%BE%E0%A4%B6%E0%A4%BF+%E0%A4%9C%E0%A4%AE%E0%A4%BE+%E0%A4%95%E0%A4%B0%E0%A4%A8%E0%A5%87+%E0%A4%95%E0%A5%87+%E0%A4%B2%E0%A4%BF%E0%A4%8F+%E0%A4%A4%E0%A5%88%E0%A4%AF%E0%A4%BE%E0%A4%B0+%E0%A4%B9%E0%A5%88%E0%A4%82%2C+%E0%A4%9C%E0%A4%BF%E0%A4%A8%E0%A4%95%E0%A5%80+%E0%A4%95%E0%A5%81%E0%A4%B2+%E0%A4%B0%E0%A4%BE%E0%A4%B6%E0%A4%BF+{total_amount}+%E2%82%B9%2A+%E0%A4%B9%E0%A5%88%E0%A5%A4+%E0%A4%95%E0%A5%83%E0%A4%AA%E0%A4%AF%E0%A4%BE+%E0%A4%85%E0%A4%AA%E0%A4%A8%E0%A5%87+Collection++Officer+%E0%A4%95%E0%A5%8B+%E0%A4%A8%E0%A4%BF%E0%A4%B0%E0%A5%8D%E0%A4%A6%E0%A5%87%E0%A4%B6+%E0%A4%A6%E0%A5%87%E0%A4%82+%E0%A4%95%E0%A4%BF+%E0%A4%B5%E0%A5%87+%E0%A4%B6%E0%A5%80%E0%A4%98%E0%A5%8D%E0%A4%B0+%E0%A4%B9%E0%A5%80+%E0%A4%B8%E0%A4%AD%E0%A5%80+%E0%A4%97%E0%A5%8D%E0%A4%B0%E0%A4%BE%E0%A4%B9%E0%A4%95%E0%A5%8B%E0%A4%82+%E0%A4%95%E0%A5%87+%E0%A4%B8%E0%A5%8D%E0%A4%A5%E0%A4%BE%E0%A4%A8+%E0%A4%AA%E0%A4%B0+%E0%A4%AA%E0%A4%B9%E0%A5%81%E0%A4%81%E0%A4%9A%E0%A5%87%E0%A4%82+%E0%A4%94%E0%A4%B0+%E0%A4%B6%E0%A5%87%E0%A4%B7+%E0%A4%B0%E0%A4%BE%E0%A4%B6%E0%A4%BF+%E0%A4%95%E0%A5%80+%E0%A4%B5%E0%A4%B8%E0%A5%82%E0%A4%B2%E0%A5%80+%E0%A4%B8%E0%A5%81%E0%A4%A8%E0%A4%BF%E0%A4%B6%E0%A5%8D%E0%A4%9A%E0%A4%BF%E0%A4%A4+%E0%A4%95%E0%A4%B0%E0%A5%87%E0%A4%82%E0%A5%A4%0A+%0A%F0%9F%91%89+%E0%A4%B5%E0%A4%BF%E0%A4%B8%E0%A5%8D%E0%A4%A4%E0%A5%83%E0%A4%A4+%E0%A4%9C%E0%A4%BE%E0%A4%A8%E0%A4%95%E0%A4%BE%E0%A4%B0%E0%A5%80+%E0%A4%B9%E0%A5%87%E0%A4%A4%E0%A5%81+%E0%A4%95%E0%A5%83%E0%A4%AA%E0%A4%AF%E0%A4%BE+%E0%A4%B8%E0%A4%82%E0%A4%B2%E0%A4%97%E0%A5%8D%E0%A4%A8+%E0%A4%AB%E0%A4%BC%E0%A4%BE%E0%A4%87%E0%A4%B2+%E0%A4%A6%E0%A5%87%E0%A4%96%E0%A5%87%E0%A4%82%E0%A5%A4%0A+%0A%E0%A4%A7%E0%A4%A8%E0%A5%8D%E0%A4%AF%E0%A4%B5%E0%A4%BE%E0%A4%A6%21&media_url={media_url}&filename={file_name}"""
    text = f"""Greeting from BSS Microfinance
        📢 नमस्ते {officer_name}) जी,
        आपकी शाखा के {total_count} ग्राहक अपनी ऋण की बकाया राशि जमा करने के लिए तैयार हैं, जिनकी कुल राशि {total_amount} ₹* है। कृपया अपने Collection  Officer को निर्देश दें कि वे शीघ्र ही सभी ग्राहकों के स्थान पर पहुँचें और शेष राशि की वसूली सुनिश्चित करें।
        👉 विस्तृत जानकारी हेतु कृपया संलग्न फ़ाइल देखें।
        धन्यवाद!
    """

    response = requests.get(url)
    if response.status_code == 200:
        print(f"Media message sent successfully to {mobile}")
        data = response.json()
        print(data)
        # # Save the message details to the database
        reply_id, message_id = data.get('response').get('id').split("-", 1)
        bankobj = db_creds.BankMstID
        chmodels.WhatsApp_Messages.objects.create(
            MessageDate=now(),
            MessageID=message_id,
            AssignedTo=None,
            Sender=bankobj.BankName if bankobj else None,
            Content=text,
            Status='sent',
            CustomerNumber=mobile,
            CustomerName=officer_name,
            Identifier=Identifier,
            GapshapReplyID=reply_id,
            BankMstID=bankobj,
            ExtraColumn1=media_url,  # Store the media URL
        )

        return response.json()
    else:
        print(f"Failed to send media message to {mobile}: {response.text}")
        return None

def send_message_to_bu(mobile, officer_name, ptp_records, total_count, total_amount, bank_mst_id=None):
    import tempfile
    import os
    import pandas as pd
    from twilio.rest import Client
    import json
    import time
    from google.cloud import storage
    from xhtml2pdf import pisa

    if not bank_mst_id:
        bank_mst_id = ptp_records[0]["BankMstID"]
    db_creds = chmodels.WhatsAppKeyMapping.objects.filter(
        BankMstID=bank_mst_id).first()
    if not db_creds:
        print(f"No WhatsAppKeyMapping found for BankMstID: {bank_mst_id}")
        return

    bankobj = db_creds.BankMstID

    df = pd.DataFrame(ptp_records)
    table_rows = ""
    for _, row in df.iterrows():
        table_rows += f"""
        <tr>
            <td>{row['DisbursementID']}</td>
            <td>{row['CustomerName']}</td>
            <td>{row['MobileNumber']}</td>
            <td>{row['PromiseAmount']}</td>
            <td>{row['BranchName']}</td>
        </tr>
        """

    # # cm."CustomerID", lm."DisbursementID", cm."CustomerName", r."Amount", lm."CollectionOfficerID", lm."CollectionOfficerName"
    # for index, row in df.iterrows():
    #     table_rows += f"""
    #     <tr>
    #         <td>{row['CustomerID']}</td>
    #         <td>{row['DisbursementID']}</td>
    #         <td>{row['CustomerName']}</td>
    #         <td>{row['Amount']}</td>
    #         <td>{row['CollectionOfficerName']}</td>
    #         <td>{row['CollectionOfficerID']}</td>
    #     </tr>
    #     """

    html_content = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8" />
            <title>Document</title>
            <style>
            body {{
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 0;
                display: flex;
                flex-direction: column;
                gap: 10px;
            }}
            .header {{
                background: #001a45;
                color: white;
                padding: 10px;
                text-align: center;
                background-image: url('header-bg.png');
                background-size: cover;
                background-position: center;
                background-repeat: no-repeat;
                width: 100%;
                position: fixed;
                top: 0;
            }}
            .box {{
                display: flex;
                gap: 10px;
                justify-content: center;
                align-items: center;
            }}
            h2 {{
                font-size: 13px;
            }}
            .content {{
                padding: 20px;
                text-align: center;
                margin-top: 120px;
            }}
            .content h3 {{
                font-size: 20px;
                margin-bottom: 20px;
                color: #0f2050;
            }}
            .allocation-table {{
                width: 100%;
                border-collapse: collapse;
                border: none;
            }}
            .allocation-table th,
            .allocation-table td {{
                padding: 10px;
                text-align: center;
                font-size: 14px;
                border: 1px solid #a7a7a7;
                word-wrap: break-word;         /* Allows long words to wrap */
                word-break: break-word;        /* Ensures breaking on word boundaries */
                white-space: normal;           /* Allows multi-line content */
                max-width: 150px;              /* Optional: prevent excessive width */
            }}
            .allocation-table th {{
                background-color: #e2ebff;
                color: #0f2050;
            }}
            .footer {{
                background-color: #001a45;
                color: white;
                text-align: left;
                padding: 10px;
                display: flex;
                align-items: center;
                position: fixed;
                bottom: 0;
                width: 100%;
            }}
            .footer img {{
                width: 50px;
                height: 50px;
                margin-right: 10px;
            }}
            .footer .title-img {{
                width: 150px;
            }}
            </style>
        </head>
        <body>
            <div class="header">
            <div class="box">
                <img src="image-79.svg" alt="logo" />
                <h2> BSS Micro Finance </h2>
            </div>
            </div>
            <div class="content">
            <h3>Today's Promises</h3>
            <div style="padding: 0.5rem; border-radius: 5px; border: 1px solid black">
                <table class="allocation-table">
                <thead>
                    <tr>
                        <th>Disbursement ID</th>
                        <th>Customer Name</th>
                        <th>Mobile Number</th>
                        <th>Promise Amount (₹)</th>
                        <th>Branch Name</th>
                    </tr>
                </thead>
                <tbody>
                    {table_rows}
                </tbody>
                </table>
            </div>
            </div>
            <div class="footer">
            <img src="logo.svg" alt="Smart Collect Icon" />
            <img src="logo-title.svg" class="title-img" alt="footer" />
            </div>
        </body>
        </html>
    """

    # Create temporary PDF file
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
    pdf_path = temp_file.name

    with open(pdf_path, "w+b") as result_file:
        pisa_status = pisa.CreatePDF(html_content, dest=result_file)

    if pisa_status.err:
        print("Failed to generate PDF using xhtml2pdf.")
        return

    os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "/home/<USER>/Product/SmartCollect2-BE/SmartCollect2/verdant-lattice-425012-f6-189acb3a46c1.json"

    try:
        # Configure GCS client
        storage_client = storage.Client()

        # Define bucket name and file name
        bucket_name = 'newrecordingsvoicebot'
        file_name = f"ptp_records_{mobile}_{int(time.time())}.pdf"

        # Get bucket and upload file
        try:
            bucket = storage_client.bucket(bucket_name)
            blob = bucket.blob(file_name)

            # Upload PDF
            blob.upload_from_filename(pdf_path)

            media_url = f"https://storage.googleapis.com/{bucket_name}/{file_name}"
            print(media_url, "*************")

            if bankobj.WhatsappService == 'Twilio':
                message = SendMediaMessageTwilio(
                    mobile, db_creds, officer_name, total_count, total_amount, file_name, media_url)
                print(f"Template message with Excel attachment sent to {mobile}, SID: {message.sid}")
            elif bankobj.WhatsappService == 'Gapshap':
                message = SendMediaMessageGapshap(
                    mobile, db_creds, officer_name, total_count, total_amount, file_name, media_url)
                print(f"Media message sent to {mobile} via Gapshap")
            else:
                print(f"Unsupported WhatsApp service for BankMstID: {bank_mst_id}")
                return


        except Exception as e:
            print(f"GCS upload failed: {str(e)}")

    except Exception as e:
        print(f"Failed to send WhatsApp template to {mobile}: {str(e)}")
    finally:
        os.remove(pdf_path)


class SendPaymentPromiseBUMessage:
    def __init__(self):
        env = Environment(loader=FileSystemLoader('.WhatsAppDocFileTemplates'))
        self.template = env.get_template('Bank32/P2P_records.html')

    def get_branches(self, user):
        try:
            print("Fetching branches from stored procedure for BO", user.BUcode, user.BankMstID_id)
            with connection.cursor() as cursor:
                cursor.execute(
                    "SELECT public.hierarchy_based_access_control_system(%s, %s);",
                    [user.BUcode, user.BankMstID_id]
                )
                result = cursor.fetchall()
                print(f"Raw SP result: {result}")
                if result and result[0]:
                   branchls = [row[0] for row in result]

                   print("Branches from hierarchy_based_access_control_system:", branchls)
                else:
                    print("SP returned no branches for BO")
                    branchls = []
        except Exception as e:
                print(f"Error fetching branches from SP: {e}")

        return branchls

    def get_promises(self, bankid, branches, status):
        query = """
            select cm."CustomerID", as2."DisbursementID", cm."CustomerName", r."Amount", as2."CollectionOfficerID", as2."CollectionOfficerName", as2.centername
            from "Response" r
            join "AccountSummary" as2 on as2."LoanMstID" = r."LoanMstID"
            join "CustomerMst" cm on as2."CustomerMstID" = cm."CustomerMstID"
            where r."BankMstID" = %d
            and r."ResponseDateTime"::date in (current_date - 1) and r."Status" = '%s' and as2."BranchMstID"  in (%s);
        """

        branches_str = ', '.join([str(branch) for branch in branches])
        query = query % (bankid, status, branches_str)

        with connection.cursor() as cursor:
            cursor.execute(query)
            columns = [col[0] for col in cursor.description]
            result = [dict(zip(columns, row)) for row in cursor.fetchall()]
            return result

    def main(self, bankmstid, status, Designation):
        # get user data
        allusers = webmodels.UserMst.objects.filter(
            BankMstID=bankmstid, Designation=Designation, is_active=True
        )
        messages_sent = 0
        for user in allusers:
            branches =  self.get_branches(user)
            if not branches:
                print(f"No branches found for user {user.username} with BU code {user.BUcode}")
                continue
            print(f"Branches for user {user.username}: {branches}")
            promises = self.get_promises(bankmstid, branches, status)
            if not promises:
                print(f"No promises found for user {user.username} with branches {branches}")
                continue
            print(f"Promises for user {user.username}: {len(promises)}")
            mobile = user.MobileNumber  # user.MobileNumber  # '**********', **********
            if not mobile:
                print(f"No mobile number found for user {user.username}")
                continue
            total_count = len(promises)
            total_amount = sum(float(promise['Amount']) for promise in promises)
            print(f"Total count: {total_count}, Total amount: {total_amount}")
            if total_count > 0:
                try:
                    threading.Thread(target=self.send_message_to_bu, args=(
                        mobile, user, promises, total_count, total_amount, bankmstid, status)).start()
                    print(f"Message sent to {mobile} for user {user.username}")
                    messages_sent += 1
                except Exception as e:
                    print(f"Failed to send message to {mobile} for user {user.username}: {str(e)}")
        print(f"Total messages sent: {messages_sent}")
        return messages_sent

    def send_media_message_gapshap(self, mobile, db_creds: chmodels.WhatsAppKeyMapping, officer_name, total_count, total_amount, file_name, media_url, Identifier=None, status='Pending'):
        if status == 'Pending':
            url = f"""https://mediaapi.smsgupshup.com/GatewayAPI/rest?userid=**********&password={db_creds.SP_Password}&send_to={mobile}&v=1.1&format=json&msg_type=DOCUMENT&method=SENDMEDIAMESSAGE&caption=%2AGreeting+from+BSS+Microfinance%2A%0A%0A%F0%9F%93%A2+%E0%A4%A8%E0%A4%AE%E0%A4%B8%E0%A5%8D%E0%A4%A4%E0%A5%87+{officer_name}%29+%E0%A4%9C%E0%A5%80%2C%0A+%0A%E0%A4%86%E0%A4%AA%E0%A4%95%E0%A5%80+%E0%A4%B6%E0%A4%BE%E0%A4%96%E0%A4%BE+%E0%A4%95%E0%A5%87+{total_count}++%E0%A4%97%E0%A5%8D%E0%A4%B0%E0%A4%BE%E0%A4%B9%E0%A4%95+%E0%A4%85%E0%A4%AA%E0%A4%A8%E0%A5%80+%E0%A4%8B%E0%A4%A3+%E0%A4%95%E0%A5%80+%E0%A4%AC%E0%A4%95%E0%A4%BE%E0%A4%AF%E0%A4%BE+%E0%A4%B0%E0%A4%BE%E0%A4%B6%E0%A4%BF+%E0%A4%9C%E0%A4%AE%E0%A4%BE+%E0%A4%95%E0%A4%B0%E0%A4%A8%E0%A5%87+%E0%A4%95%E0%A5%87+%E0%A4%B2%E0%A4%BF%E0%A4%8F+%E0%A4%A4%E0%A5%88%E0%A4%AF%E0%A4%BE%E0%A4%B0+%E0%A4%B9%E0%A5%88%E0%A4%82%2C+%E0%A4%9C%E0%A4%BF%E0%A4%A8%E0%A4%95%E0%A5%80+%E0%A4%95%E0%A5%81%E0%A4%B2+%E0%A4%B0%E0%A4%BE%E0%A4%B6%E0%A4%BF+{total_amount}+%E2%82%B9%2A+%E0%A4%B9%E0%A5%88%E0%A5%A4+%E0%A4%95%E0%A5%83%E0%A4%AA%E0%A4%AF%E0%A4%BE+%E0%A4%85%E0%A4%AA%E0%A4%A8%E0%A5%87+Collection++Officer+%E0%A4%95%E0%A5%8B+%E0%A4%A8%E0%A4%BF%E0%A4%B0%E0%A5%8D%E0%A4%A6%E0%A5%87%E0%A4%B6+%E0%A4%A6%E0%A5%87%E0%A4%82+%E0%A4%95%E0%A4%BF+%E0%A4%B5%E0%A5%87+%E0%A4%B6%E0%A5%80%E0%A4%98%E0%A5%8D%E0%A4%B0+%E0%A4%B9%E0%A5%80+%E0%A4%B8%E0%A4%AD%E0%A5%80+%E0%A4%97%E0%A5%8D%E0%A4%B0%E0%A4%BE%E0%A4%B9%E0%A4%95%E0%A5%8B%E0%A4%82+%E0%A4%95%E0%A5%87+%E0%A4%B8%E0%A5%8D%E0%A4%A5%E0%A4%BE%E0%A4%A8+%E0%A4%AA%E0%A4%B0+%E0%A4%AA%E0%A4%B9%E0%A5%81%E0%A4%81%E0%A4%9A%E0%A5%87%E0%A4%82+%E0%A4%94%E0%A4%B0+%E0%A4%B6%E0%A5%87%E0%A4%B7+%E0%A4%B0%E0%A4%BE%E0%A4%B6%E0%A4%BF+%E0%A4%95%E0%A5%80+%E0%A4%B5%E0%A4%B8%E0%A5%82%E0%A4%B2%E0%A5%80+%E0%A4%B8%E0%A5%81%E0%A4%A8%E0%A4%BF%E0%A4%B6%E0%A5%8D%E0%A4%9A%E0%A4%BF%E0%A4%A4+%E0%A4%95%E0%A4%B0%E0%A5%87%E0%A4%82%E0%A5%A4%0A+%0A%F0%9F%91%89+%E0%A4%B5%E0%A4%BF%E0%A4%B8%E0%A5%8D%E0%A4%A4%E0%A5%83%E0%A4%A4+%E0%A4%9C%E0%A4%BE%E0%A4%A8%E0%A4%95%E0%A4%BE%E0%A4%B0%E0%A5%80+%E0%A4%B9%E0%A5%87%E0%A4%A4%E0%A5%81+%E0%A4%95%E0%A5%83%E0%A4%AA%E0%A4%AF%E0%A4%BE+%E0%A4%B8%E0%A4%82%E0%A4%B2%E0%A4%97%E0%A5%8D%E0%A4%A8+%E0%A4%AB%E0%A4%BC%E0%A4%BE%E0%A4%87%E0%A4%B2+%E0%A4%A6%E0%A5%87%E0%A4%96%E0%A5%87%E0%A4%82%E0%A5%A4%0A+%0A%E0%A4%A7%E0%A4%A8%E0%A5%8D%E0%A4%AF%E0%A4%B5%E0%A4%BE%E0%A4%A6%21&media_url={media_url}&filename={file_name}"""
            text = f"""Greeting from BSS Microfinance
                📢 नमस्ते {officer_name}) जी,
                आपकी शाखा के {total_count} ग्राहक अपनी ऋण की बकाया राशि जमा करने के लिए तैयार हैं, जिनकी कुल राशि {total_amount} ₹* है। कृपया अपने Collection  Officer को निर्देश दें कि वे शीघ्र ही सभी ग्राहकों के स्थान पर पहुँचें और शेष राशि की वसूली सुनिश्चित करें।
                👉 विस्तृत जानकारी हेतु कृपया संलग्न फ़ाइल देखें।
                धन्यवाद!
            """
        elif status == 'Collection Officer Delay':
            url = f"""https://mediaapi.smsgupshup.com/GatewayAPI/rest?userid=**********&password={db_creds.SP_Password}&send_to={mobile}&v=1.1&format=json&msg_type=DOCUMENT&method=SENDMEDIAMESSAGE&caption=%2AGreeting+from+BSS+Microfinance%2A%0A%0A%F0%9F%93%A2+%E0%A4%A8%E0%A4%AE%E0%A4%B8%E0%A5%8D%E0%A4%A4%E0%A5%87+{officer_name}++%E0%A4%9C%E0%A5%80%2C%0A+%0A%E0%A4%86%E0%A4%AA%E0%A4%95%E0%A5%80+%E0%A4%B6%E0%A4%BE%E0%A4%96%E0%A4%BE+%E0%A4%95%E0%A5%87+{total_count}+%E0%A4%97%E0%A5%8D%E0%A4%B0%E0%A4%BE%E0%A4%B9%E0%A4%95%E0%A5%8B%E0%A4%82+%E0%A4%85%E0%A4%AA%E0%A4%A8%E0%A5%80+%E0%A4%8B%E0%A4%A3+%E0%A4%95%E0%A5%80+%E0%A4%95%E0%A5%81%E0%A4%B2+%E0%A4%AC%E0%A4%95%E0%A4%BE%E0%A4%AF%E0%A4%BE+%E0%A4%B0%E0%A4%BE%E0%A4%B6%E0%A4%BF+{total_amount}%E2%82%B9%2A+%E0%A4%9C%E0%A4%AE%E0%A4%BE+%E0%A4%95%E0%A4%B0%E0%A4%A8%E0%A5%87+%E0%A4%95%E0%A5%87+%E0%A4%B2%E0%A4%BF%E0%A4%8F+%E0%A4%A4%E0%A5%88%E0%A4%AF%E0%A4%BE%E0%A4%B0+%E0%A4%B9%E0%A5%88%E0%A4%82+%E0%A5%A4+%E0%A4%B9%E0%A4%BE%E0%A4%B2%E0%A4%BE%E0%A4%82%E0%A4%95%E0%A4%BF%2C+%E0%A4%B8%E0%A4%82%E0%A4%AC%E0%A4%82%E0%A4%A7%E0%A4%BF%E0%A4%A4+Collection+officer+%E0%A4%85%E0%A4%AD%E0%A5%80+%E0%A4%A4%E0%A4%95+%E0%A4%87%E0%A4%A8+%E0%A4%97%E0%A5%8D%E0%A4%B0%E0%A4%BE%E0%A4%B9%E0%A4%95%E0%A5%8B%E0%A4%82+%E0%A4%95%E0%A5%87+%E0%A4%AA%E0%A4%BE%E0%A4%B8+%E0%A4%A8%E0%A4%B9%E0%A5%80%E0%A4%82+%E0%A4%AA%E0%A4%B9%E0%A5%81%E0%A4%81%E0%A4%9A%E0%A5%87+%E0%A4%B9%E0%A5%88%E0%A4%82%2C+%E0%A4%9C%E0%A4%BF%E0%A4%B8%E0%A4%B8%E0%A5%87+%E0%A4%B5%E0%A4%B8%E0%A5%82%E0%A4%B2%E0%A5%80+%E0%A4%95%E0%A5%80+%E0%A4%AA%E0%A5%8D%E0%A4%B0%E0%A4%95%E0%A5%8D%E0%A4%B0%E0%A4%BF%E0%A4%AF%E0%A4%BE+%E0%A4%AE%E0%A5%87%E0%A4%82+%E0%A4%B5%E0%A4%BF%E0%A4%B2%E0%A4%82%E0%A4%AC+%E0%A4%B9%E0%A5%8B+%E0%A4%B0%E0%A4%B9%E0%A4%BE+%E0%A4%B9%E0%A5%88%E0%A5%A4+%E0%A4%95%E0%A5%83%E0%A4%AA%E0%A4%AF%E0%A4%BE+%E0%A4%85%E0%A4%AA%E0%A4%A8%E0%A5%87+%E0%A4%B8%E0%A4%82%E0%A4%AC%E0%A4%82%E0%A4%A7%E0%A4%BF%E0%A4%A4+Collection+officer+%E0%A4%95%E0%A5%8B+%E0%A4%A4%E0%A4%A4%E0%A5%8D%E0%A4%95%E0%A4%BE%E0%A4%B2+%E0%A4%A8%E0%A4%BF%E0%A4%B0%E0%A5%8D%E0%A4%A6%E0%A5%87%E0%A4%B6+%E0%A4%A6%E0%A5%87%E0%A4%82%2C+%E0%A4%95%E0%A4%BF+%E0%A4%B5%E0%A5%87+%E0%A4%97%E0%A5%8D%E0%A4%B0%E0%A4%BE%E0%A4%B9%E0%A4%95%E0%A5%8B%E0%A4%82+%E0%A4%95%E0%A5%87+%E0%A4%B8%E0%A5%8D%E0%A4%A5%E0%A4%BE%E0%A4%A8+%E0%A4%AA%E0%A4%B0+%E0%A4%9C%E0%A4%BE%E0%A4%95%E0%A4%B0+%E0%A4%B8%E0%A4%AE%E0%A4%AF+%E0%A4%AA%E0%A4%B0+%E0%A4%B6%E0%A5%87%E0%A4%B7+%E0%A4%B0%E0%A4%BE%E0%A4%B6%E0%A4%BF+%E0%A4%95%E0%A5%80+%E0%A4%B5%E0%A4%B8%E0%A5%82%E0%A4%B2%E0%A5%80+%E0%A4%B8%E0%A5%81%E0%A4%A8%E0%A4%BF%E0%A4%B6%E0%A5%8D%E0%A4%9A%E0%A4%BF%E0%A4%A4+%E0%A4%95%E0%A4%B0%E0%A5%87%E0%A4%82%E0%A5%A4%0A+%0A%F0%9F%91%89+%E0%A4%B5%E0%A4%BF%E0%A4%B8%E0%A5%8D%E0%A4%A4%E0%A5%83%E0%A4%A4+%E0%A4%9C%E0%A4%BE%E0%A4%A8%E0%A4%95%E0%A4%BE%E0%A4%B0%E0%A5%80+%E0%A4%B9%E0%A5%87%E0%A4%A4%E0%A5%81+%E0%A4%95%E0%A5%83%E0%A4%AA%E0%A4%AF%E0%A4%BE+%E0%A4%B8%E0%A4%82%E0%A4%B2%E0%A4%97%E0%A5%8D%E0%A4%A8+%E0%A4%AB%E0%A4%BC%E0%A4%BE%E0%A4%87%E0%A4%B2+%E0%A4%A6%E0%A5%87%E0%A4%96%E0%A5%87%E0%A4%82%E0%A5%A4%0A+%0A%E0%A4%A7%E0%A4%A8%E0%A5%8D%E0%A4%AF%E0%A4%B5%E0%A4%BE%E0%A4%A6%21&media_url={media_url}&filename={file_name}"""
            text= f"""Greeting from BSS Microfinance
            📢 नमस्ते {officer_name}  जी,
            आपकी शाखा के {total_count} ग्राहकों अपनी ऋण की कुल बकाया राशि {total_amount}₹* जमा करने के लिए तैयार हैं । हालांकि, संबंधित Collection officer अभी तक इन ग्राहकों के पास नहीं पहुँचे हैं, जिससे वसूली की प्रक्रिया में विलंब हो रहा है। कृपया अपने संबंधित Collection officer को तत्काल निर्देश दें, कि वे ग्राहकों के स्थान पर जाकर समय पर शेष राशि की वसूली सुनिश्चित करें।
            👉 विस्तृत जानकारी हेतु कृपया संलग्न फ़ाइल देखें।
            धन्यवाद!
            """
        else:
            print(f"Unsupported status: {status}")
            return None

        print('-->', url)

        response = requests.get(url)
        if response.status_code == 200:
            print(f"Media message sent successfully to {mobile}")
            data = response.json()
            print(data)
            # # Save the message details to the database
            reply_id, message_id = data.get('response').get('id').split("-", 1)
            bankobj = db_creds.BankMstID
            chmodels.WhatsApp_Messages.objects.create(
                MessageDate=now(),
                MessageID=message_id,
                AssignedTo=None,
                Sender=bankobj.BankName if bankobj else None,
                Content=text,
                Status='sent',
                CustomerNumber=mobile,
                CustomerName=officer_name,
                Identifier=Identifier,
                GapshapReplyID=reply_id,
                BankMstID=bankobj,
                ExtraColumn1=media_url,  # Store the media URL
            )

            return response.json()
        else:
            print(f"Failed to send media message to {mobile}: {response.text}")
            return None

    def save_file_to_gcp_bucket(self, file_path, bucket_name, file_name):
        from google.cloud import storage
        import os

        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "/home/<USER>/Product/SmartCollect2-BE/SmartCollect2/verdant-lattice-425012-f6-189acb3a46c1.json"

        try:
            # Configure GCS client
            storage_client = storage.Client()

            # Get bucket and upload file
            bucket = storage_client.bucket(bucket_name)
            blob = bucket.blob(file_name)

            # Upload file
            blob.upload_from_filename(file_path)
            print(f"File {file_name} uploaded to {bucket_name} successfully.")
            return f"https://storage.googleapis.com/{bucket_name}/{file_name}"
        except Exception as e:
            print(f"GCS upload failed: {str(e)}")
            return None

    def save_pdf_file(self, html_content, file_path):
        from weasyprint import HTML
        html = HTML(string=html_content)
        html.write_pdf(file_path)
        return file_path

    def generate_record_pdf_and_send_whatsapp(self, mobile, user: webmodels.UserMst, ptp_records, total_count, total_amount, bank_mst_id, status='Pending'):
        import tempfile
        import os
        import time

        db_creds = chmodels.WhatsAppKeyMapping.objects.filter(
            BankMstID=bank_mst_id).first()
        if not db_creds:
            print(f"No WhatsAppKeyMapping found for BankMstID: {bank_mst_id}")
            return

        bankobj = db_creds.BankMstID

        html_content = self.template.render(
            BranchName=user.BranchMstID.BranchName,
            BranchCode=user.BranchMstID.BranchCode,
            ptp_records=ptp_records
        )

        # Create temporary PDF file
        pdf_path = self.save_pdf_file(html_content, tempfile.NamedTemporaryFile(delete=False, suffix='.pdf').name)

        try:
            # Define bucket name and file name
            bucket_name = 'newrecordingsvoicebot'
            if status == 'Pending':
                file_name = f"ptp_records_{mobile}_{int(time.time())}.pdf"
            elif status == 'Collection Officer Delay':
                file_name = f"collection_officer_delay_{mobile}_{int(time.time())}.pdf"

            # Get bucket and upload file
            try:
                media_url = self.save_file_to_gcp_bucket(pdf_path, bucket_name, file_name)
                if bankobj.WhatsappService == 'Twilio':
                    message = SendMediaMessageTwilio(
                        mobile, db_creds, user.Fullname, total_count, total_amount, file_name, media_url)
                    print(f"Template message with Excel attachment sent to {mobile}, SID: {message.sid}")
                elif bankobj.WhatsappService == 'Gapshap':
                    identifier = 'PaymentPromiseBUMessage' if status == 'Pending' else 'CollectionOfficerDelayBUMessage'
                    message = self.send_media_message_gapshap(
                        mobile, db_creds, user.Fullname, total_count, total_amount, file_name, media_url, Identifier=identifier, status=status)
                    print(f"Media message sent to {mobile} via Gapshap")
                else:
                    print(f"Unsupported WhatsApp service for BankMstID: {bank_mst_id}")
                    return


            except Exception as e:
                print(f"GCS upload failed: {str(e)}")

        except Exception as e:
            print(f"Failed to send WhatsApp template to {mobile}: {str(e)}")
        finally:
            if pdf_path and os.path.exists(pdf_path):
                os.remove(pdf_path)

    def send_message_to_bu(self, mobile, user, ptp_records, total_count, total_amount, bank_mst_id, status='Pending'):
        self.generate_record_pdf_and_send_whatsapp(
            mobile, user, ptp_records, total_count, total_amount, bank_mst_id=bank_mst_id, status=status)


class SendPtpReminder(APIView):
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        print("*********8")
        with connection.cursor() as cursor:
            # query = """
            #     SELECT r."LoanMstID", r."PromiseDateTime", r."Status", r."Amount",
            #         r."ResponseDateTime", r."BankMstID", r."BranchMstID",
            #         a."CollectionOfficerID"
            #     FROM public."Response" r
            #     JOIN public."AccountSummary" a ON r."LoanMstID" = a."LoanMstID"
            #     WHERE DATE(r."PromiseDateTime") = CURRENT_DATE
            #     AND r."BankMstID"=9
            # """
            query = """ SELECT 
                r."LoanMstID", 
                r."PromiseDateTime", 
                r."Status", 
                r."Amount",
                r."ResponseDateTime", 
                r."BankMstID", 
                r."BranchMstID",
                a."CollectionOfficerID",
                b."BranchName",
                l."DisbursementID",
                c."CustomerName",
                c."MobileNumber",
                r."BankMstID"
            FROM public."Response" r
            JOIN public."AccountSummary" a ON r."LoanMstID" = a."LoanMstID"
            JOIN public."BranchMst" b ON r."BranchMstID" = b."BranchMstID"
            JOIN public."LoanMst" l ON r."LoanMstID" = l."LoanMstID"
            JOIN public."CustomerMst" c ON l."CustomerMstID" = c."CustomerMstID"
            WHERE DATE(r."PromiseDateTime") = CURRENT_DATE
            AND r."BankMstID" = 9 """
            cursor.execute(query)
            columns = [col[0] for col in cursor.description]
            result = [dict(zip(columns, row)) for row in cursor.fetchall()]

        if not result:
            return Response({
                "status": "no_data",
                "message": "No PTP records found on current date."
            })

        officer_data = {}
        for row in result:
            officer_id = row['CollectionOfficerID']
            bank_id = row['BankMstID']
            if officer_id not in officer_data:
                officer_data[officer_id] = {
                    "collection_officer_id": officer_id,
                    "name": "",
                    "mobile": "",
                    "ptp_records": [],
                    "bank_id": bank_id,
                    "total_number_of_promises": 0,
                    "total_promised_amount": 0.0
                }
            # officer_data[officer_id]["ptp_records"].append(row)
            officer_data[officer_id]["ptp_records"].append({
                "CustomerName": row["CustomerName"],
                "DisbursementID": row["DisbursementID"],
                "BranchName": row["BranchName"],
                "PromiseAmount": row["Amount"],
                "PromiseDate": row["PromiseDateTime"],
                "MobileNumber": row["MobileNumber"],
                "BankMstID": row["BankMstID"]
            })
            officer_data[officer_id]["total_number_of_promises"] += 1
            officer_data[officer_id]["total_promised_amount"] += float(
                row["Amount"])

            print(officer_data)
        # # Step 3: Fetch name and mobile number for each CollectionOfficerID
        for officer_id, data in officer_data.items():
            bank_id = data["bank_id"]
            print(officer_id,"officerId")
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT "BUName", "BUMobile"
                    FROM public."GeographyMst"
                    WHERE "BUCode" = %s AND "BankMstID" = %s
                    LIMIT 1
                """, [officer_id, bank_id])
                officer_info = cursor.fetchone()
                print(officer_info,"INFIIIIIIIII")
                if officer_info:
                    name, mobile = officer_info
                    data["name"] = name
                    data["mobile"] = mobile
                    send_message_to_bu(
                        mobile,
                        name,
                        data["ptp_records"],
                        data["total_number_of_promises"],
                        data["total_promised_amount"]
                    )
        return Response({
            "status": "success",
            "data": list(officer_data.values())
            # "data": list(officer_data)
        })
    




@csrf_exempt
# def create_payment_view(bankmstid, loanmstid, name,
#                     contact, Bankname, amount):
#     if bankmstid:
def create_payment_view(request):
    if request.method == 'POST':
        custmstid = request.POST.get('custmstid')
        amount = request.POST.get('amount')
        contact = request.POST.get('contact')
        bankmstid = request.POST.get('bankmstid')
        name=request.POST.get('name')
        Bankname=request.POST.get('bankname')
        loanmstid=request.POST.get('loanmstid')
        
        # custmstid=LoanMst.objects.filter(LoanMstID=loanmstid).values_list('CustomerMstID', flat=True).first()
        upi_creds = chmodels.BankMst.objects.filter(
            BankMstID=bankmstid).first()
        if not upi_creds:
            logging.error(
                f"No credentials found for BankMstID: {bankmstid}")
            return JsonResponse({'error': f'No credentials found for BankMstID: {bankmstid}'}, status=400)
        print(custmstid, amount, contact)
        if not all([custmstid, amount, contact]):
            return JsonResponse({'error': 'Missing required parameters'}, status=400)
        
        
        client = razorpay.Client(
            auth=(upi_creds.Key_ID, upi_creds.Secret_Key))
        
        
        customer = client.customer.create({
            "name": name,
            "email": "<EMAIL>",
            "contact": contact,
            "fail_existing": "0",  # If alrady exist then it will take existing customer_id
            "notes": {
                "note_key_1": f'''Customer from {Bankname}'''
            }
        })

        customer_id = customer['id']
        
        # Set QR Code Expiry
        current_time = int(time.time())
        # expiry_time = current_time + 86400  # 24 hours in seconds
        expiry_time = int((timezone.now() + timedelta(hours=12)).timestamp())
        amount_in_paise = int(float(amount) * 100)
        #MAke QR Code
        print("QR Response: beforee qr code")
        # qr_data = {
        #     "type": "upi_qr",
        #     "name": name,
        #     "usage": "single_use",
        #     "fixed_amount": True,
        #     "payment_amount": amount_in_paise,
        #     "description": "For Overdue payment purpose",
        #     "customer_id": customer_id,
        #     "close_by": expiry_time,
        #     "notes": {
        #         "purpose": f'''Payment for overdue amount of {amount} at {Bankname}''',
        #         "internal_ref": f'''qr_{custmstid}'''
        #     }
        # }
        qr_data = {
            "type": "upi_qr",
            "name": name,
            "usage": "single_use",
            "fixed_amount": True,
            "payment_amount": amount_in_paise,
            "description": "For Overdue payment purpose",
            "close_by": expiry_time,
            "notes": {
                "CustMstID": custmstid,
            }
        }
        
        try:
            # Make the QR Code request to Razorpay
            qr_response = client.qrcode.create(qr_data)
            print(qr_response,"QR CODE RESPONSE")
        except Exception as e:
            logging.error(f"Error generating QR code: {str(e)}")
            return JsonResponse({'error': f"Error generating QR code: {str(e)}"}, status=500)

        #UPI Link Code
        link_response=client.payment_link.create({
            "amount": amount_in_paise,
            "currency": "INR",
            "accept_partial": False,
            # "first_min_partial_amount": 100,
            "description": f'''Payment for overdue amount of {amount} at {Bankname}''',
            "customer": {
                "name": name,
                "email": '<EMAIL>',
                "contact": contact
            },
            "notify": {
                "sms": False,
                "email": False
            },
            "reminder_enable": False,
            "options": {
                "checkout": {
                    "method": {
                        "netbanking": "1",
                        "card": "1",
                        "upi": "1",
                        "wallet": "1"
                    }
                }
            },
            "notes": {
                "CustMstID": custmstid,
            }
        })
        
        print("Link Response:", link_response)
        
        qr_code_url = qr_response.get('image_url', '')
        qr_id = qr_response.get('id')  # QR_ID
        qr_link = qr_response.get('image_url')  # QR_Link
        Status = qr_response.get('status')
        
        payment_link = link_response.get('short_url')  # Payment_Link
        link_id = qr_response.get('id')

        # Save to PaymentLogs
        chmodels.PaymentLogs.objects.create(
            CustMstID=int(custmstid),
            QR_ID=qr_id,
            Payment_Link=payment_link,
            QR_Link=qr_link,
            DateTime=datetime.now(),
            Status=Status,
            Link_ID=link_id,
            LoanMstID=loanmstid
        )
        sendpaymentwh(payment_link, qr_link,
                      contact, name, amount, qr_id,upi_creds.Bank_Details)
        return JsonResponse({
            'customer_id': customer_id,
            'status': 'Customer created successfully',
            'qr_code_url': qr_code_url,
            'payment_link': payment_link
        })


@csrf_exempt
def razorpay_webhook(request):
    if request.method == 'POST':
        webhook_secret = 'markymarky'  # Same as in Razorpay dashboard
        received_data = request.body
        received_signature = request.headers.get('X-Razorpay-Signature')
        
        print("IN Call Bank function")
        print(received_data)
        # Verify webhook signature
        expected_signature = hmac.new(
            webhook_secret.encode(),
            received_data,
            hashlib.sha256
        ).hexdigest()

        if hmac.compare_digest(expected_signature, received_signature):
            data = json.loads(received_data)
            event = data.get("event")
            print("Eventttt", event)
            if event == 'payment.captured':
                payment_data = data["payload"]["payment"]["entity"]
                print(payment_data,"Payment Dataaa")
                # qr_id = data["payload"].get(
                #     "qr_code", {}).get("entity", {}).get("id")
                payment_id = payment_data.get("id")
                amount = payment_data.get("amount") / 100
                status = payment_data.get("status")
                method = payment_data.get("method")
                vpa = payment_data.get("vpa") or payment_data.get("upi", {}).get("vpa")
                contact = payment_data.get("contact")
                customer_id = payment_data.get("customer_id")
                cust_mst_id = payment_data.get("notes", {}).get(
                    "CustMstID")  # 🔍 Extracting CustMstID

                transaction_timestamp = payment_data.get("created_at")
                transaction_date = datetime.fromtimestamp(
                    transaction_timestamp).strftime('%Y-%m-%d %H:%M:%S')

                print("Payment Details:", payment_data)
                print(f"CustMstID      : {cust_mst_id}")

                transaction_log = chmodels.Transaction_Logs(
                    Payment_ID=payment_id,
                    Amount=amount,
                    Status=status,
                    Method=method,
                    VPA=vpa,
                    Contact=contact,
                    Payment_Cust_ID=customer_id,
                    CustMstID=cust_mst_id,  # 📝 Save it in the model
                    Transaction_date=timezone.make_aware(
                        datetime.strptime(transaction_date, '%Y-%m-%d %H:%M:%S')),
                    Body=data,
                    DateTime=timezone.now()
                )
                transaction_log.save()
                return JsonResponse({'status': 'Payment captured processed'})
                
            elif event == "payment_link.paid":
                print("Payment Link Paid")
                payment_data = data["payload"]["payment"]["entity"]
                print("In elif payment data payment link",payment_data)
                payment_link_data = data["payload"]["payment_link"]["entity"]

                payment_id = payment_data.get("id")
                amount = payment_data.get("amount", 0) / 100
                status = payment_data.get("status")
                method = payment_data.get("method")
                contact = payment_data.get("contact")
                email = payment_data.get("email")
                bank = payment_data.get("bank")  # will be None in wallet method
                order_id = payment_data.get("order_id")
                wallet = payment_data.get("wallet")
                acquirer_data = payment_data.get("acquirer_data", {})
                acquirer_transaction_id = acquirer_data.get("transaction_id")

                transaction_timestamp = payment_data.get("created_at")
                transaction_date = datetime.fromtimestamp(
                    transaction_timestamp).strftime('%Y-%m-%d %H:%M:%S')

                reference_id = payment_link_data.get("reference_id")
                cust_mst_id = payment_data.get("notes", {}).get(
                    "CustMstID")
                # policy_name = payment_link_data.get("notes", {}).get("Policy Name")

                print("Payment Link Details:")
                print(f"Payment ID     : {payment_id}")
                print(f"Amount         : {amount}")
                print(f"Status         : {status}")
                print(f"Method         : {method}")
                print(f"Wallet         : {wallet}")
                print(f"Bank           : {bank}")
                print(f"Contact        : {contact}")
                print(f"Email          : {email}")
                print(f"Order ID       : {order_id}")
                print(f"Acquirer TxnID : {acquirer_transaction_id}")
                print(f"Reference ID   : {reference_id}")
                print(f"Transaction Date : {transaction_date}")
                print(f"custmstid : {cust_mst_id}")

                # Optional: save to DB
                transaction_log = chmodels.Transaction_Logs(
                    QR_ID=None,  # not applicable here
                    Payment_ID=payment_id,
                    Amount=amount,
                    Status=status,
                    Method=method,
                    VPA=None,
                    Contact=contact,
                    Payment_Cust_ID=None,
                    Transaction_date=timezone.make_aware(
                        datetime.strptime(transaction_date, '%Y-%m-%d %H:%M:%S')),
                    Body=data,
                    DateTime=timezone.now(),
                    CustMstID=cust_mst_id
                )
                transaction_log.save()
                
                # ✅ RESPONSE HERE
                return JsonResponse({'status': 'Payment link processed'})

            return JsonResponse({'status': 'Unhandled event'})  # ✅ CATCH-ALL

        return JsonResponse({'error': 'Invalid signature'}, status=400)

    return HttpResponse(status=405)


def sendpaymentwh(pyment_link, image_link, contact, name, overdueamt, qr_id,bank_details):
    account_sid = '**********************************'
    auth_token = 'dbcb1ff3fbaeaaa00ff4d57931886b73'
    client = Client(account_sid, auth_token)
    print(bank_details,"Bank Detailssss")
    bucket_name = "newrecordingsvoicebot"
    filename = f"{qr_id}.png"
    temp_path = f"/home/<USER>/Product/SmartCollect2-BE/SmartCollect2/audio/{filename}"

    print(pyment_link, image_link, contact, name, overdueamt,"PARAAAAAAAAA")
    # Step 1: Download QR image
    response = requests.get(image_link)
    if response.status_code == 200:
        with open(temp_path, 'wb') as f:
            f.write(response.content)
        print("✅ File downloaded and saved temporarily.")
    else:
        raise Exception(
            f"❌ Failed to download file. Status code: {response.status_code}")

    # Step 2: Upload to GCS from filename
    os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "/home/<USER>/Product/SmartCollect2-BE/SmartCollect2/verdant-lattice-425012-f6-189acb3a46c1.json"
    
    storage_client = storage.Client()
    bucket = storage_client.bucket(bucket_name)
    blob = bucket.blob(filename)

    blob.upload_from_filename(
        temp_path,
        content_type="image/png"
    )

    # Set forced download metadata
    blob.content_disposition = f"inline; filename={filename}"
    blob.patch()

    print("✅ File uploaded to GCS with attachment header.")

    # Step 3: Delete temp file
    os.remove(temp_path)

    logging.info(
        f"File uploaded to GCS successfully.  https://storage.googleapis.com/newrecordingsvoicebot/{temp_path}")

    
    print(f"https://storage.googleapis.com/newrecordingsvoicebot/{filename}")
    message = client.messages.create(
        messaging_service_sid='MG455b516373622ce08e5bcfd939fc7221',
        to=f'whatsapp:+91{contact}',
        # Optional if using content API
        # content_sid='HXa5c1953436e9568dfa80eaa8112ff85f',
        content_sid='HX47d2a4253c9e9634257b78db7b235429',  #RTGS NEFT Template
        # content_variables=json.dumps({
        #     '1': f"{name}",
        #     '2': f"{overdueamt}",
        #     '3': f"{pyment_link}",
        #     '4': f"{filename}"
        # })
        
        content_variables=json.dumps({
            '1': f"{name}",
            '2': f"{overdueamt}",
            '3': f"{pyment_link}",
            '4': f"{filename}",
            '5':f"{bank_details['beneficiary_name']}",
            '6':f"{bank_details['beneficiary_bank_name']}",
            '7':f"{bank_details['beneficiary_branch_name']}",
            '8':f"{bank_details['beneficiary_ifsc_code']}",
            '9':f"{bank_details['beneficiary_account_number']}",
        })

    )

    logging.info(f"Message SID: {message.sid}")






from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
@method_decorator(csrf_exempt, name="dispatch")
class WhatsAppWebhookHandler(APIView):
    permission_classes = []
    """
    Unified Webhook Handler for WhatsApp Status and Reply callbacks.
    """

    def get(self, request):
        return JsonResponse({"status": "success", "message": "pass"}, status=200)

    def post(self, request):
        try:
            # data = request.data if request.data else json.loads(
            #     request.body.decode("utf-8"))
            # logging.info(f"Incoming unified webhook data: {data}")
            # print("Incoming unified webhook data", data)
            if request.body:
                try:
                    data = json.loads(request.body.decode("utf-8"))
                except json.JSONDecodeError:
                    logging.error("Failed to decode JSON body")
                    return HttpResponseBadRequest("Invalid JSON format")
            else:
                logging.warning("Empty request body received")
                return HttpResponseBadRequest("Empty request body")

            # logging.info(f"Incoming unified webhook data: {data}")
            print("Incoming unified webhook data", data)

            message = data.get("data", {}).get("message", {})
            sender = message.get("sender")

            if sender == "USER":
                logging.info("Message from USER - handling as reply.")
                return self.handle_reply(request, message)

            elif sender == "API":
                # logging.info("Message from API - handling status update.")
                return self.handle_status(request, data)

            else:
                logging.warning(f"Unknown sender type: {sender}. Ignoring.")
                return JsonResponse({"status": "ignored", "reason": f"Unhandled sender type: {sender}"}, status=400)

        except Exception as e:
            logging.error(f"Unified Webhook error: {e}", exc_info=True)
            return JsonResponse({"status": "error", "message": str(e)}, status=500)

    def handle_reply(self, request, request_body):
        print("Handle Reply")
        print(request_body)
        profile_name = request_body.get("userName", "")
        button_payload = request_body.get("buttonPayload", "")
        # original_replied_message_id = request_body.get(
            # "replyMessageId")
        sms_sid = request_body.get("id")
        whatsapp_status = request_body.get("status", "")
        from_full = request_body.get("phone_number", "")
        message_type = request_body.get("message_type", "text")
        message = request_body.get(
            "message_content", {}).get("text", "")
        project_id = request_body.get("projectId", "")
        num_segments = 1
        referral_num_media = 0
        api_version = "smartping_v1"
        print(request_body)
        contact_number = re.sub(r"\D", "", from_full)[-10:]
        message_sid = request_body.get("messageId", "")
        original_replied_message_id = None
        context = request_body.get("context", {})

        messagehistory = None
        next_template_id = None
        if context and context.get("id"):
            original_replied_message_id = context.get("id")
        print("****************",message_sid)
        key = chmodels.WhatsAppKeyMapping.objects.filter(
            SP_ProjectID=project_id).first()
        bank_id = None
        if key:
            bank_id = key.BankMstID
        if original_replied_message_id:
            origmessageobj = chmodels.WhatsApp_Messages.objects.filter(MessageID=original_replied_message_id).first()
        else:
            origmessageobj = None
        print(original_replied_message_id)
        print("fggggggggggggggggg",origmessageobj)

        if origmessageobj:
            custmessage = chmodels.WhatsApp_Messages()
            custmessage.MessageDate = timezone.now()
            custmessage.LoanMstID = origmessageobj.LoanMstID
            custmessage.WhatsAppQueueID = origmessageobj.WhatsAppQueueID
            custmessage.AssignedTo = None
            custmessage.MessageID = message_sid
            custmessage.Sender = profile_name
            custmessage.Content = message
            custmessage.Button = button_payload
            custmessage.Status = whatsapp_status
            custmessage.Type = origmessageobj.Type
            custmessage.WhatsAppQueueID = origmessageobj.WhatsAppQueueID
            custmessage.CustomerNumber = contact_number
            custmessage.CustomerName = origmessageobj.CustomerName
            custmessage.BankMstID = bank_id
            custmessage.CampaignMstID = origmessageobj.CampaignMstID
            custmessage.save()

            print("MEssageeeee SAveeeeeee")
            templateobj = WhatsAppFlowMapping.objects.filter(
                FlowID=origmessageobj.Type, Response=message, WhatsAppTemplateMappingID=origmessageobj.WhatsAppUserTemplateID).first()

            oldobj = webmodels.Response.objects.filter(
                # WhatsappQueueID=origmessageobj.WhatsAppQueueID
                LoanMstID=origmessageobj.LoanMstID.pk if origmessageobj.LoanMstID else None
            ).order_by('-ResponseDateTime').first()
            
            if oldobj:
                oldobj.Status = "Terminated"
                oldobj.save()
            
            next_template_id = templateobj.NextTemplate if templateobj else None
            
            messagehistory = chmodels.WhatsAppHistory.objects.filter(WhatsAppQueueID=origmessageobj.WhatsAppQueueID).first()

            # Create a new Response object
            new_response = webmodels.Response()
            new_response.ResponseDateTime = datetime.now()
            new_response.WhatsappQueueID = messagehistory.WhatsAppQueueID
            new_response.LoanMstID = messagehistory.LoanMstID.pk if messagehistory.LoanMstID else None
            
            if message in ["होय","हाँ","Yes","ஆம்","అవును","ಹೌದು","അതെ","YES","हो"]:
                print("IN Promiseeee")
                # new_response.LoanMstID=origmessageobj.LoanMstID,
                new_response.BlasterQueueID=None
                new_response.IVRQueueID=None
                new_response.VoiceBotQueueID=None
                # new_response.WhatsappQueueID=origmessageobj.WhatsAppQueueID,
                new_response.AllocationID=None
                new_response.FeedbackID=None
                new_response.ModeOfPayment=None
                new_response.PromiseDateTime=datetime.now()
                new_response.Status="Pending"
                new_response.Amount=messagehistory.OverdueAmt
                # new_response.ResponseDateTime=now(),
                new_response.BankMstID=messagehistory.BankMstID.pk if messagehistory.BankMstID else None
                new_response.BranchMstID=messagehistory.BranchMstID.pk if messagehistory.BranchMstID else None
                new_response.CampaignMstID=messagehistory.CampaignMstID
                
                bank = chmodels.BankMst.objects.filter(
                    pk=messagehistory.BankMstID.pk).first() if messagehistory.BankMstID else None
                if bank and bank.Is_UPI:
                    print("HELOOOOOOO")
                    print(messagehistory.BankMstID.pk)
                    print(messagehistory.LoanMstID.pk)
                    print(messagehistory.CustomerName)
                    print(messagehistory.ContactNumber)
                    print(messagehistory.BankName)
                    print(messagehistory.OverdueAmt)
                    
                    bankid=messagehistory.BankMstID.pk
                    loanmstid=messagehistory.LoanMstID.pk
                    customername=messagehistory.CustomerName
                    customernumber=messagehistory.ContactNumber
                    bankname=messagehistory.BankName
                    overdueamt=messagehistory.OverdueAmt
                    # create_payment_view(
                    #     bankid, loanmstid, customername, customernumber, bankname, overdueamt)

            elif message in ["नाही","नहीं","No","இல்லை","ಇಲ್ಲ","కాదు","ഇല്ല","NO"]:
                print("IN Denialssss")
                new_response.BlasterQueueID=None
                new_response.IVRQueueID=None
                new_response.VoiceBotQueueID=None
                # new_response.WhatsappQueueID=origmessageobj.WhatsAppQueueID,
                new_response.AllocationID=None
                new_response.FeedbackID=None
                new_response.ModeOfPayment=None
                # new_response.PromiseDateTime=datetime.now()
                new_response.Status="Denied"
                new_response.Amount=messagehistory.OverdueAmt
                # new_response.ResponseDateTime=now(),
                new_response.BankMstID=messagehistory.BankMstID.pk if messagehistory.BankMstID else None
                new_response.BranchMstID=messagehistory.BranchMstID.pk if messagehistory.BranchMstID else None
                new_response.CampaignMstID=messagehistory.CampaignMstID

            elif message in ["आधीच भरले आहे","भुगतान कर दिया है","Already Paid","ஏற்கனவே செலுத்தியுள்ளேன்","ಈಗಾಗಲೇ ಪಾವತಿಸಿದೆ","ఇప్పటికే చెల్లించాను","ഇതിനകം അടച്ചിട്ടുണ്ട്","रक्कम भरलेली आहे"]:
                print("IN Claimmm")
                new_response.BlasterQueueID=None
                new_response.IVRQueueID=None
                new_response.VoiceBotQueueID=None
                # new_response.WhatsappQueueID=origmessageobj.WhatsAppQueueID,
                new_response.AllocationID=None
                new_response.FeedbackID=None
                new_response.ModeOfPayment=None
                # new_response.PromiseDateTime=datetime.now()
                new_response.Status="Already Paid"
                new_response.Amount=messagehistory.OverdueAmt
                new_response.Despute="Despute"
                new_response.BankMstID=messagehistory.BankMstID.pk if messagehistory.BankMstID else None
                new_response.BranchMstID=messagehistory.BranchMstID.pk if messagehistory.BranchMstID else None
                new_response.CampaignMstID=messagehistory.CampaignMstID
            print("Saved")
            new_response.mode_of_payment = message
            new_response.save()
            self.reply_engine(messagehistory.WhatsAppQueueID, next_template_id)

        else:
            print("IN ELSEEEEEEEEEEEEEEE")
            historymsg = chmodels.WhatsAppHistory.objects.filter(
                ContactNumber=contact_number
            ).order_by('-CreatedDate').first()
            messagehistory = historymsg  # ✅ Assign here too

            custmessage = chmodels.WhatsApp_Messages()
            custmessage.MessageDate = timezone.now()
            custmessage.LoanMstID = historymsg.LoanMstID if historymsg else None
            custmessage.WhatsAppQueueID = None
            custmessage.Assigned_to = None
            custmessage.MessageID = ""
            custmessage.Sender = profile_name
            custmessage.Content = message
            custmessage.Status = "Received"
            custmessage.Type = None
            custmessage.CustomerNumber = contact_number
            custmessage.CustomerName = historymsg.CustomerName if historymsg else ""
            custmessage.BankMstID = bank_id
            custmessage.CampaignMstID = historymsg.CampaignMstID
            custmessage.save()
        # return messagehistory.WhatsAppQueueID, next_template_id
        return JsonResponse({
            "status": "success",
            "WhatsAppQueueID": messagehistory.WhatsAppQueueID,
            "next_template_id": next_template_id
        }, status=200)

    def reply_engine(self,WhatsAppQueueID, next_template_id):
        if next_template_id:
            data = chmodels.WhatsAppHistory.objects.filter(WhatsAppQueueID=WhatsAppQueueID).first()
            engineobj = WhatsAppEngine()
            if next_template_id == '658':
                # Call special Smartping logic with BranchName variable
                engineobj.get_template_variables(WhatsAppQueueID, next_template_id)
            else:
                # Proceed with regular template logic
                templateobj = engineobj.get_template(next_template_id)
                db_creds = chmodels.WhatsAppKeyMapping.objects.filter(BankMstID=data.BankMstID).first()
                sid = engineobj.smartping_message_api(data.ContactNumber, templateobj.TemplateName, db_creds, data.Language)
                engineobj.save_message(data, templateobj, sid, templateobj.TemplateBody)
            # templateobj = engineobj.get_template(next_template_id)
            # db_creds = chmodels.WhatsAppKeyMapping.objects.filter(BankMstID=data.BankMstID).first()
            # sid = engineobj.smartping_message_api(data.ContactNumber,templateobj.TemplateName,db_creds,data.Language)
            # engineobj.save_message(data ,templateobj,sid,templateobj.TemplateBody)

    


        


    def handle_status(self, request, data):
        try:
            # logging.info(f"Status update webhook data: {data}")
            timestamp = str(datetime.now())

            # Smartping format (nested data)
            message = data.get("data", {}).get("message", {})

            message_sid = message.get("messageId")
            status_value = message.get("status")
            error_message = message.get("failureResponse", {}).get(
                "error") or message.get("error_message", "")
            contact_number = message.get("phone_number", "")
            message_text = message.get(
                "message_content", {}).get("text", "")

            messageobj = chmodels.WhatsApp_Messages.objects.filter(MessageID=message_sid).first()
            if messageobj and status_value in ['DELIVERED', 'READ','FAILED']:
                
                try:
                    histobj = chmodels.WhatsAppHistory.objects.filter(WhatsAppQueueID=messageobj.WhatsAppQueueID).first()
                    if histobj:
                        if status_value == 'DELIVERED':
                            histobj.IsDelivered = True
                        elif status_value == 'READ':
                            histobj.IsRead = True
                        histobj.save()
                except Exception as e:
                    logging.error(f"Error while updating status: {e}")
                messageobj.Status = status_value
                messageobj.save()
        

            return JsonResponse({"status": "success"}, status=200)

        except Exception as e:
            logging.error(f"Error in status handler: {e}", exc_info=True)
            return JsonResponse({"status": "error", "message": str(e)}, status=500)



class WhatsAppWebhookGapShap(APIView):
    permission_classes = []

    def save_message(self, request):
        print('was post request ----------------------------------------')
        logging.info('was post request ----------------------------------------')
        print(request.body)
        logging.info(request.body)
        print(request,"RRRRRRRRRRRRRRRRRRRRRRRRRRRRRR")
        byte_str = request.body
        decoded =  byte_str.decode('utf-8')
        json_data = json.loads(decoded)
        print(json_data)
        # {'button': '{"payload":"पहले ही भुगतान कर दिया है","text":"पहले ही भुगतान कर दिया है"}', 'waNumber': '919151015811', 'mobile': '918968888687', 'context': '{"from":"919151015811","id":"af007c75-f8d5-4ac4-bb87-4dbb8802dcbc"}', 'replyId': '5446996683359375444', 'name': '.', 'messageId': '559753005011079745', 'type': 'button', 'timestamp': '1748926440000'}
 
        reply_type = json_data['type']
        reply_text = ''
        reply_id = json_data.get('replyId')
        message_id = json_data.get('messageId')

        if json_data['type'] == 'button':
            json_btn_data = json.loads(json_data['button'])
            reply_text = json_btn_data['text']
            print(reply_id)
            print(message_id)
            print(reply_type)
            print(reply_text)
        elif json_data['type'] == 'text':
            # reply_text = json_data['text']
            reply_text = json_data.get('text', '')
            print(reply_text,"REPLYYYYYYY TEXTTTTTTTTTTTTT")
        contact_number = json_data['mobile'][2:]

        print(f'''call back recived {json_data}''')
        print(reply_id,"REPLYYYYYYYY IIIIIIIIIDDDDDDDDDD")
        origmessageobj = chmodels.WhatsApp_Messages.objects.filter(GapshapReplyID=reply_id,).first() if reply_id else None
        print(origmessageobj,"FFFFFFFFFFFFFFFFFFFFFFFFF Gapshap")
        logging.info(f"{origmessageobj}, FFFFFFFFFFFFFFFFFFFFFFFFF Gapshap")
        waNumber = json_data.get('waNumber', '')
        key = chmodels.WhatsAppKeyMapping.objects.filter(
            Extra_Column1=waNumber).first() if waNumber else None
        bank_id = None
        if key:
            bank_id = key.BankMstID if key else None
        if origmessageobj:
            print("Orignalll me=essagesss")
            custmessage = chmodels.WhatsApp_Messages()
            custmessage.MessageDate = timezone.now()
            custmessage.LoanMstID = origmessageobj.LoanMstID
            custmessage.WhatsAppQueueID = origmessageobj.WhatsAppQueueID
            custmessage.AssignedTo = None
            custmessage.MessageID = reply_id
            custmessage.Sender = json_data['name']
            custmessage.Content = reply_text
            custmessage.Button = " "
            custmessage.Status = "Received"
            custmessage.Type = origmessageobj.Type
            custmessage.WhatsAppQueueID = origmessageobj.WhatsAppQueueID
            custmessage.CustomerNumber = contact_number
            custmessage.CustomerName = origmessageobj.CustomerName
            custmessage.BankMstID = bank_id
            custmessage.CampaignMstID = origmessageobj.CampaignMstID
            custmessage.save()

            messagehistory = chmodels.WhatsAppHistory.objects.filter(WhatsAppQueueID=origmessageobj.WhatsAppQueueID).first()
            templateobj = WhatsAppFlowMapping.objects.filter(
                FlowID=origmessageobj.Type, Response=reply_text, WhatsAppTemplateMappingID=origmessageobj.WhatsAppUserTemplateID).first()
            next_template_id = templateobj.NextTemplate if templateobj else None
            logging.info(f'''text template id  {next_template_id}''')

            if templateobj.FlowID == 323:
                return messagehistory.WhatsAppQueueID, next_template_id

            oldobj = webmodels.Response.objects.filter(
                # WhatsappQueueID=origmessageobj.WhatsAppQueueID
                LoanMstID=origmessageobj.LoanMstID.pk if origmessageobj.LoanMstID else None
            ).order_by('-ResponseDateTime').first()

            if oldobj:
                oldobj.Status = "Terminated"
                oldobj.save()

            # Create a new Response object
            new_response = webmodels.Response()
            new_response.ResponseDateTime = datetime.now()
            new_response.WhatsappQueueID = messagehistory.WhatsAppQueueID
            new_response.LoanMstID = messagehistory.LoanMstID.pk if messagehistory.LoanMstID else None

            if reply_text in ["भुगतान के लिए तैयार है","हाँ"]:
                print("IN Promiseeee")
                # new_response.LoanMstID=origmessageobj.LoanMstID,
                new_response.BlasterQueueID=None
                new_response.IVRQueueID=None
                new_response.VoiceBotQueueID=None
                # new_response.WhatsappQueueID=origmessageobj.WhatsAppQueueID,
                new_response.AllocationID=None
                new_response.FeedbackID=None
                new_response.ModeOfPayment=None
                new_response.PromiseDateTime=datetime.now()
                new_response.Status="Pending"
                new_response.Amount=messagehistory.OverdueAmt
                # new_response.ResponseDateTime=now(),
                new_response.BankMstID=messagehistory.BankMstID.pk if messagehistory.BankMstID else None
                new_response.BranchMstID=messagehistory.BranchMstID.pk if messagehistory.BranchMstID else None
                new_response.CampaignMstID=messagehistory.CampaignMstID

                bank = chmodels.BankMst.objects.filter(
                    pk=messagehistory.BankMstID.pk).first() if messagehistory.BankMstID else None

            elif reply_text in ["कलेक्शन ऑफिसर नहीं आया","नहीं"]:
                print("IN Collection Officer")
                new_response.BlasterQueueID=None
                new_response.IVRQueueID=None
                new_response.VoiceBotQueueID=None
                # new_response.WhatsappQueueID=origmessageobj.WhatsAppQueueID,
                new_response.AllocationID=None
                new_response.FeedbackID=None
                new_response.ModeOfPayment=None
                # new_response.PromiseDateTime=datetime.now()
                new_response.Status="Collection Officer Delay"
                new_response.Amount=messagehistory.OverdueAmt
                # new_response.ResponseDateTime=now(),
                new_response.BankMstID=messagehistory.BankMstID.pk if messagehistory.BankMstID else None
                new_response.BranchMstID=messagehistory.BranchMstID.pk if messagehistory.BranchMstID else None
                new_response.CampaignMstID=messagehistory.CampaignMstID

            elif reply_text in ["नहीं"]:
                print("IN Denialssss")
                new_response.BlasterQueueID=None
                new_response.IVRQueueID=None
                new_response.VoiceBotQueueID=None
                # new_response.WhatsappQueueID=origmessageobj.WhatsAppQueueID,
                new_response.AllocationID=None
                new_response.FeedbackID=None
                new_response.ModeOfPayment=None
                # new_response.PromiseDateTime=datetime.now()
                new_response.Status="Denied"
                new_response.Amount=messagehistory.OverdueAmt
                # new_response.ResponseDateTime=now(),
                new_response.BankMstID=messagehistory.BankMstID.pk if messagehistory.BankMstID else None
                new_response.BranchMstID=messagehistory.BranchMstID.pk if messagehistory.BranchMstID else None
                new_response.CampaignMstID=messagehistory.CampaignMstID

            elif reply_text in ["पहले ही भुगतान कर दिया है","भुगतान कर दिया है"]:
                print("IN Claimmm")
                new_response.BlasterQueueID=None
                new_response.IVRQueueID=None
                new_response.VoiceBotQueueID=None
                # new_response.WhatsappQueueID=origmessageobj.WhatsAppQueueID,
                new_response.AllocationID=None
                new_response.FeedbackID=None
                new_response.ModeOfPayment=None
                # new_response.PromiseDateTime=datetime.now()
                new_response.Status="Already Paid"
                new_response.Amount=messagehistory.OverdueAmt
                new_response.Despute="Despute"
                new_response.BankMstID=messagehistory.BankMstID.pk if messagehistory.BankMstID else None
                new_response.BranchMstID=messagehistory.BranchMstID.pk if messagehistory.BranchMstID else None
                print("Saved")
                new_response.mode_of_payment = reply_text
                new_response.CampaignMstID=messagehistory.CampaignMstID
            new_response.save()

            logging.info(f'''call back recived {messagehistory.WhatsAppQueueID}''')
            print(f'''call back recived {messagehistory.WhatsAppQueueID}''')
            print(f'''next template id  {next_template_id}''')

            return messagehistory.WhatsAppQueueID, next_template_id
        else:
            historymsg = chmodels.WhatsAppHistory.objects.filter(ContactNumber=contact_number).order_by('-CreatedDate').first()
            print(historymsg)
            print("HISSTOOYRRRRRRR")
            custmessage = chmodels.WhatsApp_Messages()
            custmessage.MessageDate = timezone.now()
            custmessage.LoanMstID = historymsg.LoanMstID
            custmessage.WhatsAppQueueID = None
            custmessage.Assigned_to = None
            # custmessage.MessageID = MessageSid
            custmessage.Sender = json_data['name']
            custmessage.Content = reply_text
            custmessage.Button = "Text"
            # custmessage.Template_ID = None
            # custmessage.Status = SmsStatus
            custmessage.Type = None
            # custmessage.Identifier = None
            custmessage.CustomerNumber = contact_number
            custmessage.CustomerName=historymsg.CustomerName
            custmessage.BankMstID = bank_id
            custmessage.CampaignMstID = historymsg.CampaignMstID
            custmessage.save()
            return None, None
        return origmessageobj.WhatsAppQueueID, None

    def reply_engine(self, request):
        WhatsAppQueueID, next_template_id = self.save_message(request)
        print(f'''Next queue ID {WhatsAppQueueID}''')
        print(f'''Next Template ID {next_template_id}''')
        logging.info(f'''Next Template ID {next_template_id}''')
        if next_template_id:
            data = chmodels.WhatsAppHistory.objects.filter(WhatsAppQueueID=WhatsAppQueueID).first()
            engineobj = WhatsAppEngine()
            templateobj = engineobj.get_template(next_template_id)
            print(templateobj,"TEMPPPPPPPPPOBBBBJJJJJ")
            db_creds = chmodels.WhatsAppKeyMapping.objects.filter(BankMstID=data.BankMstID).first()
            sid = engineobj.gapshap_api(data.ContactNumber,templateobj.MetaTemplateID,db_creds)
            engineobj.save_message(data ,templateobj,sid,templateobj.TemplateBody )
            
        else:
            message = request.data.get('Body')
            contact_number = request.data.get('WaId')
            # process_customer_response(contact_number[-10:])
            data = chmodels.WhatsAppHistory.objects.filter(ContactNumber=contact_number).last()
            
            message_history = list(
                chmodels.WhatsApp_Messages.objects.filter(
                    CustomerNumber=contact_number, LoanMstID=data.LoanMstID.pk if data.LoanMstID else None)
                .order_by('MessageDate')
                .values('Sender', 'Content')
            )

            # Format history as a structured string for LLM context
            formatted_history = "\n".join([f"{msg['Sender']}: {msg['Content']}" for msg in message_history])
            logging.info(f'''formatted_history{formatted_history}''')
            print("formatted_history",formatted_history)

    def post(self, request):
        Thread(target=self.reply_engine, args=(request,)).start()
        return HttpResponse('Success')


class WhatsAppStatusGupshap(APIView):
    authentication_classes = []
    permission_classes = []
    def post(self, request):
        try:
            events = request.data  # Already parsed JSON
            print('----------------------> Gupshup Events:', events)

            # Ensure it's always a list
            if not isinstance(events, list):
                events = [events]

            for event in events:
                external_id = event.get('externalId')
                event_type = event.get('eventType')
                id_msg = external_id.split('-')[1]

                message_qs = chmodels.WhatsApp_Messages.objects.filter(MessageID=id_msg).exclude(Status='Received')
                existing_callback = message_qs.values_list('WhatsAppQueueID', 'status_callback').first() or []
                if not existing_callback:
                    logging.warning(f"No message found with ID {id_msg} or already processed.")
                    continue

                if not isinstance(existing_callback[1], list):
                    existing_callback[1] = []

                existing_callback[1].append(event)
                message_qs.update(
                    Status=event_type,
                    status_callback=existing_callback[1]
                )

                # Prepare update for WhatsAppHistory
                history_update_fields = {}
                if event_type == 'DELIVERED':
                    history_update_fields['IsDelivered'] = True
                elif event_type == 'READ':
                    history_update_fields['IsRead'] = True
                elif event_type == 'FAILED':
                    # If you want to mark as failed in future
                    # history_update_fields['IsFailed'] = True
                    pass

                if history_update_fields and existing_callback:
                    chmodels.WhatsAppHistory.objects.filter(
                        WhatsAppQueueID=existing_callback[0]
                    ).update(**history_update_fields)

                # message_obj = chmodels.WhatsApp_Messages.objects.filter(MessageID=id_msg).first()
                # if message_obj:
                #     message_obj.Status = event_type
                #     status_callback = message_obj.status_callback
                #     if type(status_callback) is not list:
                #         status_callback = []
                #     status_callback.append(event)
                #     message_obj.status_callback = status_callback
                #     message_obj.save()

                #     hist_obj = chmodels.WhatsAppHistory.objects.filter(WhatsAppQueueID=message_obj.WhatsAppQueueID).first()
                #     if hist_obj:
                #         if event_type == 'DELIVERED':
                #             hist_obj.IsDelivered = True
                #         elif event_type == 'READ':
                #             hist_obj.IsRead = True
                #         elif event_type == 'FAILED':
                #             pass
                #             # hist_obj.IsFailed = True
                #         hist_obj.save()

        except Exception as e:
            logging.error(f"Error processing Gupshup status: {e}")
            return HttpResponse("Failure", status=500)

        return HttpResponse("Success", status=200)


class WhatsAppResponseSummary(APIView):
    permission_classes = []
    authentication_classes = []

    def get_data_summary(self, from_date, to_date, bankid):
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT wam."MessageDate"::date as MessageDate, wam."Content", count(wam."Content") FROM public."WhatsApp_Messages" AS wam
                join "WhatsAppHistory" wah on wam."WhatsAppQueueID" = wah."WhatsAppQueueID"
                where wah."BankMstID" = %s and (wam."Status" is null or wam."Status" = 'Received') and wam."Content" != '' and wam."MessageDate"::date BETWEEN %s AND %s and wah."FlowID" != 323
                group by wam."MessageDate"::date, wam."Content"
                order by count
            """, [bankid, from_date, to_date])
            rows = cursor.fetchall()
            response_data = []
            for row in rows:
                response_data.append({
                    'MessageDate': row[0].strftime('%Y-%m-%d'),
                    'Content': row[1],
                    'Count': row[2]
                })
        if not response_data:
            return []

        # pivot the data with columns, date, content as cols and count as values
        pivot_data = {}
        df = pd.DataFrame(response_data)
        if not df.empty:
            pivot_df = df.pivot_table(index='MessageDate', columns='Content', values='Count', fill_value=0)
            pivot_df.reset_index(inplace=True)
            pivot_df.columns.name = None
            pivot_data = pivot_df.to_dict(orient='records')
        else:
            pivot_data = []
        return pivot_data

    def get_data_detailed(self, from_date, to_date, bankid):
        response_data = None
        with connection.cursor() as cursor:
            cursor.execute("""
                select as2."Branch_id", cm."CustomerID", as2."DisbursementID", cm."CustomerName", as2."ExtraColumn2" as "FCSM_NO",
                    as2."CollectionOfficerName", as2."CollectionOfficerID", wam."Content", wam."MessageDate"
                from "WhatsApp_Messages" wam
                join "WhatsAppHistory" wah on wam."WhatsAppQueueID" = wah."WhatsAppQueueID"
                join "AccountSummary" as2 on as2."LoanMstID" = wam."LoanMstID"
                join "CustomerMst" cm on cm."CustomerMstID" = as2."CustomerMstID"
                where wam."BankMstID" = %s and wam."MessageDate"::date between %s and %s and wam."Status" = 'Received' and wah."FlowID" != 323
            """, [bankid, from_date, to_date])
            rows = cursor.fetchall()
            columns = [col[0] for col in cursor.description]
            df = pd.DataFrame(rows, columns=columns)
            response_data = df.to_dict(orient='records')
        # If no data found, return empty list
        if not response_data:
            return []
        # If data found, return the response data
        return response_data

    def get(self, request):
        """
        Endpoint to get WhatsApp response summary.
        """
        try:
            # date = request.GET.get('date', None)
            from_date = request.GET.get('from_date', None)
            to_date = request.GET.get('to_date', None)
            bankid = request.GET.get('bankid', None)
            if not from_date or not to_date or not bankid:
                return JsonResponse({'status': 'error', 'message': 'from_date, to_date and bankid are required'}, status=400)

            # Convert date to datetime object
            try:
                from_date_obj = datetime.strptime(from_date, '%Y-%m-%d')
                to_date_obj = datetime.strptime(to_date, '%Y-%m-%d')
            except ValueError:
                return JsonResponse({'status': 'error', 'message': 'Invalid date format. Use YYYY-MM-DD.'}, status=400)

            pivot_data = self.get_data_summary(from_date_obj, to_date_obj, bankid)
            detailed_data = self.get_data_detailed(from_date_obj, to_date_obj, bankid)
            return JsonResponse({'status': 'success', 'data': pivot_data, 'detailed_data': detailed_data}, status=200)
        except Exception as e:
            print(f"Error fetching WhatsApp response summary: {e}")
            print(traceback.format_exc())
            return JsonResponse({'status': 'error', 'message': str(e)}, status=500)



class WhatsAppWebhookPinnacle(APIView):
    permission_classes = []

    def save_message(self, request):
        print('was post request Pinnacle ----------------------------------------')
        logging.info('was post request Pinnacle ----------------------------------------')
        print(request.body)
        byte_str = request.body
        decoded =  byte_str.decode('utf-8')
        json_data = json.loads(decoded)
        print(json_data)
 
        entry = json_data['entry'][0]
        change = entry['changes'][0]
        value = change['value']
        message = value['messages'][0]

        # Extract common data
        reply_type = message.get('type', '')
        message_id = message.get('id', '')
        reply_id = message.get('context', {}).get('id', '')
        wa_number = message.get('from', '')
        contact_number = wa_number[-10:]
        name = value['contacts'][0]['profile']['name'] if value.get('contacts') else ''

        if reply_type == 'button':
            reply_text = message['button']['text']
        elif reply_type == 'text':
            reply_text = message.get('text', {}).get('body', '')

        origmessageobj = chmodels.WhatsApp_Messages.objects.filter(MessageID=reply_id,).first() if reply_id else None
        print(origmessageobj,"FFFFFFFFFFFFFFFFFFFFFFFFF Pinnacle")
        phonenumberid = value.get('metadata', {}).get('phone_number_id', '')
        key = chmodels.WhatsAppKeyMapping.objects.filter(
            PhoneNumberID=phonenumberid).first() if phonenumberid else None
        bank_id = None
        if key:
            bank_id = key.BankMstID if key else None
        if origmessageobj:
            print("Orignalll me=essagesss")
            custmessage = chmodels.WhatsApp_Messages()
            custmessage.MessageDate = timezone.now()
            custmessage.LoanMstID = origmessageobj.LoanMstID
            custmessage.WhatsAppQueueID = origmessageobj.WhatsAppQueueID
            custmessage.AssignedTo = None
            custmessage.MessageID = reply_id
            custmessage.Sender = name
            custmessage.Content = reply_text
            custmessage.Button = " "
            custmessage.Status = "Received"
            custmessage.Type = origmessageobj.Type
            custmessage.WhatsAppQueueID = origmessageobj.WhatsAppQueueID
            custmessage.CustomerNumber = contact_number
            custmessage.CustomerName = origmessageobj.CustomerName
            custmessage.BankMstID = bank_id
            custmessage.CampaignMstID = origmessageobj.CampaignMstID
            custmessage.save()

            templateobj = WhatsAppFlowMapping.objects.filter(
                FlowID=origmessageobj.Type, Response=reply_text, WhatsAppTemplateMappingID=origmessageobj.WhatsAppUserTemplateID).first()

            oldobj = webmodels.Response.objects.filter(
                # WhatsappQueueID=origmessageobj.WhatsAppQueueID
                LoanMstID=origmessageobj.LoanMstID.pk if origmessageobj.LoanMstID else None
            ).order_by('-ResponseDateTime').first()

            if oldobj:
                oldobj.Status = "Terminated"
                oldobj.save()

            next_template_id = templateobj.NextTemplate if templateobj else None
            # if classification:
            logging.info(f'''text template id  {next_template_id}''')

            messagehistory = chmodels.WhatsAppHistory.objects.filter(WhatsAppQueueID=origmessageobj.WhatsAppQueueID).first()
            # Create a new Response object
            new_response = webmodels.Response()
            new_response.ResponseDateTime = datetime.now()
            new_response.WhatsappQueueID = messagehistory.WhatsAppQueueID
            new_response.LoanMstID = messagehistory.LoanMstID.pk if messagehistory.LoanMstID else None

            if reply_text in ["भुगतान के लिए तैयार है","हाँ"]:
                print("IN Promiseeee")
                # new_response.LoanMstID=origmessageobj.LoanMstID,
                new_response.BlasterQueueID=None
                new_response.IVRQueueID=None
                new_response.VoiceBotQueueID=None
                new_response.AllocationID=None
                new_response.FeedbackID=None
                new_response.ModeOfPayment=None
                new_response.PromiseDateTime=datetime.now()
                new_response.Status="Pending"
                new_response.Amount=messagehistory.OverdueAmt
                new_response.BankMstID=messagehistory.BankMstID.pk if messagehistory.BankMstID else None
                new_response.BranchMstID=messagehistory.BranchMstID.pk if messagehistory.BranchMstID else None
                new_response.CampaignMstID=messagehistory.CampaignMstID

                bank = chmodels.BankMst.objects.filter(
                    pk=messagehistory.BankMstID.pk).first() if messagehistory.BankMstID else None

            elif reply_text in ["नहीं"]:
                print("IN Denialssss")
                new_response.BlasterQueueID=None
                new_response.IVRQueueID=None
                new_response.VoiceBotQueueID=None
                new_response.AllocationID=None
                new_response.FeedbackID=None
                new_response.ModeOfPayment=None
                new_response.Status="Denied"
                new_response.Amount=messagehistory.OverdueAmt
                new_response.BankMstID=messagehistory.BankMstID.pk if messagehistory.BankMstID else None
                new_response.BranchMstID=messagehistory.BranchMstID.pk if messagehistory.BranchMstID else None
                new_response.CampaignMstID=messagehistory.CampaignMstID
                

            elif reply_text in ["पहले ही भुगतान कर दिया है","भुगतान कर दिया है","मैंने पहले ही भर दिया है"]:
                print("IN Claimmm")
                new_response.BlasterQueueID=None
                new_response.IVRQueueID=None
                new_response.VoiceBotQueueID=None
                new_response.AllocationID=None
                new_response.FeedbackID=None
                new_response.ModeOfPayment=None
                new_response.Status="Already Paid"
                new_response.Amount=messagehistory.OverdueAmt
                new_response.Despute="Despute"
                new_response.BankMstID=messagehistory.BankMstID.pk if messagehistory.BankMstID else None
                new_response.BranchMstID=messagehistory.BranchMstID.pk if messagehistory.BranchMstID else None
                print("Saved")
                new_response.mode_of_payment = reply_text
                new_response.CampaignMstID=messagehistory.CampaignMstID
            new_response.save()

            logging.info(f'''call back recived {messagehistory.WhatsAppQueueID}''')
            print(f'''call back recived {messagehistory.WhatsAppQueueID}''')
            print(f'''next template id  {next_template_id}''')

            return messagehistory.WhatsAppQueueID, next_template_id
        else:
            sender = value.get('contacts', [{}])[0].get('profile', {}).get('name', '')
            historymsg = chmodels.WhatsAppHistory.objects.filter(ContactNumber=contact_number).order_by('-CreatedDate').first()
            print(historymsg)
            print("HISSTOOYRRRRRRR")
            custmessage = chmodels.WhatsApp_Messages()
            custmessage.MessageDate = timezone.now()
            custmessage.LoanMstID = historymsg.LoanMstID
            custmessage.WhatsAppQueueID = None
            custmessage.Assigned_to = None
            custmessage.Sender = sender
            custmessage.Content = reply_text
            custmessage.Button = "Text"
            custmessage.Type = None
            custmessage.CustomerNumber = contact_number
            custmessage.CustomerName=historymsg.CustomerName
            custmessage.BankMstID = bank_id
            custmessage.CampaignMstID = historymsg.CampaignMstID
            custmessage.save()
            return None, None
        return origmessageobj.WhatsAppQueueID, None

    def reply_engine(self,request):
        WhatsAppQueueID, next_template_id = self.save_message(request)
        logging.info(f'''Request Dataaaa {request}''')
        if next_template_id:
            data = chmodels.WhatsAppHistory.objects.filter(WhatsAppQueueID=WhatsAppQueueID).first()
            engineobj = WhatsAppEngine()
            templateobj = engineobj.get_template(next_template_id)
            db_creds = chmodels.WhatsAppKeyMapping.objects.filter(BankMstID=data.BankMstID).first()
            sid = engineobj.pinnacle_message_api(data.ContactNumber,templateobj.TemplateName,db_creds,data.Language)
            engineobj.save_message(data ,templateobj,sid,templateobj.TemplateBody)
    
    def save_status_update(self, value):
        try:
            statuses = value.get('statuses', [])
            for status in statuses:
                message_id = status.get('id')
                event_type = status.get('status')  # e.g., delivered, read, failed
                timestamp = status.get('timestamp')

                print(f"Status Update: ID={message_id}, Status={event_type}, Time={timestamp}")

                message_obj = chmodels.WhatsApp_Messages.objects.filter(MessageID=message_id).first()
                if message_obj:
                    message_obj.Status = event_type

                    if hasattr(message_obj, 'status_callback') and isinstance(message_obj.status_callback, list):
                        message_obj.status_callback.append(status)

                    message_obj.save()

                    # Update WhatsAppHistory
                    hist_obj = chmodels.WhatsAppHistory.objects.filter(WhatsAppQueueID=message_obj.WhatsAppQueueID).first()
                    if hist_obj:
                        if event_type.lower() == 'delivered':
                            hist_obj.IsDelivered = True
                        elif event_type.lower() == 'read':
                            hist_obj.IsRead = True
                        elif event_type.lower() == 'failed':
                            hist_obj.IsFailed = True
                        hist_obj.save()

        except Exception as e:
            logging.error(f"Error in save_status_update: {e}")

    def post(self, request):
        print('was post request Pinnacle ----------------------------------------')
        logging.info('was post request Pinnacle ----------------------------------------')
        try:
            byte_str = request.body
            decoded = byte_str.decode('utf-8')
            json_data = json.loads(decoded)

            value = json_data.get('entry', [])[0].get('changes', [])[0].get('value', {})

            if 'messages' in value:
                # Customer reply message
                Thread(target=self.reply_engine, args=(request,)).start()

            elif 'statuses' in value:
                # Delivery or read status update
                Thread(target=self.save_status_update, args=(value,)).start()

        except Exception as e:
            logging.error(f"Error processing Pinnacle webhook: {e}")
            return HttpResponse("Failure", status=500)

        return HttpResponse("Success", status=200)

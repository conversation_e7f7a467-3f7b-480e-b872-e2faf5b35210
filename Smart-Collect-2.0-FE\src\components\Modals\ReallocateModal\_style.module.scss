@import url("../../../index.css");

$light-gray:#D9D9D9;
$tab-height: 7px;
$light-blue: #E3F5F6;

.dialer_modal{
    .icon_img{
        width: 13px;
        height: 20px;
        img{
            width: 100%;
            object-fit: contain;
        }
    }

    .feedback_title{
        margin: 0;
        color: white;
        font-size:18px; 
        font-family: 'Kanit','Courier New', Courier, monospace;
        font-weight: 400;
    }

    .feedback_button{
        background-color: var(--dark-blue);
        color: white;
        padding: 1rem 2rem;
        border: none;
        font-family: 'Kanit','Courier New', Courier, monospace;

        &:hover{
            background-color: var(--dark-blue) !important;
            color: white !important;
            border: none;
            box-shadow:none;
        }
    }

    .custom_select{
        width: 60%;
        :global(.ant-select-selector){
            background-color: $light-blue;
            box-shadow: none !important;
            border:none;
            outline: none;
            border-bottom: 1px solid var(--dark-blue) !important;
            font-family: 'Kanit','Courier New', Courier, monospace;
        }     
        :global(.ant-select-selection-item){
            font-weight: 400;
            color: var(--dark-blue);
            font-family: 'Kanit','Courier New', Courier, monospace;
        }
    }

    .date_picker{
        border:none;
        outline: none;
        box-shadow: none;
        border-bottom: 1px solid var(--dark-blue);
        background-color: $light-blue;
        :global(.ant-picker-input>input){
            font-weight: 300;
            color:var(--dark-blue);
            font-family: 'Kanit','Courier New', Courier, monospace; 
        }
        &::placeholder{
            color: #0F20504A;
            font-weight: 600;
        }
    }

    .text{
        font-weight: 400;
        color: var(--dark-blue);
        font-family: 'Kanit','Courier New', Courier, monospace;
        font-size: 14px;
    }

    .add_btn{
        background-color: var(--dark-blue)!important;
        color:white !important;
        border: none;
        outline:none;
        width: 50%;
        margin: 2rem 0 0 0;
    }

    :global(.ant-modal-content){
        padding: 0.5rem;
    }
    :global(.ant-modal-close){
        right: 14px;
        top: 10px;
        color: white;
        &:hover{
          color: white !important;
        }
        :global(.ant-modal-close-x){
          font-size:10px;
          color: white;
        }
    }  
    :global(.ant-modal-header){
        background-color: var(--dark-blue);
        padding: 0.5rem 0px;
        text-align: center;
        border-bottom: 1px solid #FFFFFF;
        border-radius:10px;

        :global(.ant-modal-title){
            color: white;
            font-weight: 500;
        }
    }
    :global(.ant-modal-body){  
        padding: 1rem;
    }
}

// Responsive view
@media screen and (max-width:768px) {
    .dialer_modal{
        .icon_img{
            width: 10px;
            height: 24px;
        }
        .feedback_title{
            font-size: 12px;
        }
        .feedback_button{
            padding: 0rem 1rem;
            font-size: 11px;
        }
      :global(.ant-modal-content){
        padding: 4px;

        :global(.ant-modal-body){  
            padding: 0.3rem;
        }
      }
    }
}
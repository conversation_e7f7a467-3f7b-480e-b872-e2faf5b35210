import PropTypes from "prop-types";
export default function ReallocationIcon({width = 15, height = 15,  strokeColor = "#ffffff", className, ...props}) {
  return (
    <div>
    <svg 
    className={className} 
    width={width} 
    height={height} 
    viewBox="0 0 14 14" 
    fill="none"
    xmlns="http://www.w3.org/2000/svg">
        <path d="M12.5 12.5C12.5 12.7652 12.3946 13.0196 12.2071 13.2071C12.0196 13.3946 11.7652 13.5 11.5 13.5H2.5C2.23478 13.5 1.98043 13.3946 1.79289 13.2071C1.60536 13.0196 1.5 12.7652 1.5 12.5V1.5C1.5 1.23478 1.60536 0.98043 1.79289 0.792893C1.98043 0.605357 2.23478 0.5 2.5 0.5H9L12.5 4V12.5Z" stroke={strokeColor} strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M8.5 0.5V4.5H12.5" stroke={strokeColor} strokeLinecap="round" strokeLinejoin="round"/>
        <g clipPath="url(#clip0_4753_7497)">
        <path d="M3.58301 5.69238C3.58301 5.69238 3.75801 5.69238 3.99134 6.10072C3.99134 6.10072 4.63989 5.03123 5.21634 4.81738" stroke={strokeColor} strokeWidth="0.175" strokeLinecap="round" strokeLinejoin="round"/>
        </g>
        <g clipPath="url(#clip1_4753_7497)">
        <path d="M3.58301 8.69238C3.58301 8.69238 3.75801 8.69238 3.99134 9.10072C3.99134 9.10072 4.63989 8.03123 5.21634 7.81738" stroke={strokeColor} strokeWidth="0.175" strokeLinecap="round" strokeLinejoin="round"/>
        </g>
        <g clipPath="url(#clip2_4753_7497)">
        <path d="M3.58301 11.6914C3.58301 11.6914 3.75801 11.6914 3.99134 12.0997C3.99134 12.0997 4.63989 11.0303 5.21634 10.8164" stroke={strokeColor} strokeWidth="0.175" strokeLinecap="round" strokeLinejoin="round"/>
        </g>
        <path d="M6.5 5.40039H10" stroke={strokeColor} strokeWidth="0.35"/>
        <path d="M6.5 8.40039H10" stroke={strokeColor} strokeWidth="0.35"/>
        <path d="M6.5 11.4004H10" stroke={strokeColor} strokeWidth="0.35"/>
        <defs>
        <clipPath id="clip0_4753_7497">
        <rect width="2.8" height="2.8" fill="white" transform="translate(3 4)"/>
        </clipPath>
        <clipPath id="clip1_4753_7497">
        <rect width="2.8" height="2.8" fill="white" transform="translate(3 7)"/>
        </clipPath>
        <clipPath id="clip2_4753_7497">
        <rect width="2.8" height="2.8" fill="white" transform="translate(3 10)"/>
        </clipPath>
        </defs>
    </svg>
    </div>
  )
}
ReallocationIcon.propTypes ={
  width: PropTypes.number,
  height: PropTypes.number,
  strokeColor: PropTypes.string,
  className: PropTypes.string,
}
@import url("../../../index.css");

$table-radius:22px;
$attempts-bg:#BDD1FF;
$disable:#787777;
$body:#E4F8F9;

.analysis{
    padding-top: 1.5rem;

    .text{
        font-weight: 500;
        white-space: nowrap;
        color:var(--dark-blue)
    }

    .blueText{
        font-weight: 400;
        color:var(--blue);
        cursor: pointer;
        font-family: 'Kanit','Courier New', Courier, monospace;
    }

    .button{
        border: none;
        background-color: transparent;
        color: var(--blue);
        font-weight: 700;
        cursor: pointer;
        font-family: 'Kanit','Courier New', Courier, monospace;
    }
   
    //Global ant design classes

    // Pagination
    :global(.ant-pagination){
        justify-content: center;
        margin: 0 !important;
        
        :global(.ant-pagination-prev),
        :global(.ant-pagination-next){
            color: var(--dark-blue);
            border: 0 solid var(--blue);
            background: #E4F8F9;
            height: 25px;
            min-width: 15px;
            border-radius: 0px;
            margin: 0;
            button{
                border-radius: 0px;
            }
        }
        :global(.ant-pagination-item){
            margin-right: 0;
            height: 0;
            a{
                color:$disable;
                font-size: 0.9rem;
                line-height: 23px;
                font-family: 'Kanit','Courier New', Courier, monospace;
            }
            &:hover{
                background-color: transparent;
            }
        }
        :global(.ant-pagination-item-active){
            border:none;
           a{
                color:var(--dark-blue);
                font-size:1.2rem;
                padding: 0;
           }
        }
    }

    //Table container
    :global(.ant-table-container){
        padding: 0 1rem;
        margin-bottom: 0.5rem;
        background: var(--light-green);
        border-start-start-radius: $table-radius;
        border-start-end-radius: $table-radius;

        // Table header
        :global(.ant-table-header) {
            position: relative;
            margin: 0 auto;
            top: -21px;
            border-radius: $table-radius;
            :global(.ant-table-column-has-sorters) {
            background-color: var(--dark-blue);
            &:hover{
                background-color: var(--dark-blue);
            }
    
            :global(.ant-table-column-sorter-up),
            :global(.ant-table-column-sorter-down) {
                svg{
                fill: white;
                }
                &:global(.active){
                svg{
                    fill: rgb(24, 155, 249)
                }
                }
            }
            }
        }

        // Table virtual body
        :global(.ant-table-tbody-virtual){
            margin-top: -8px;
        }

        &::before{
            box-shadow: none !important;
        }
    }

    // Table rows header 
    :global(.ant-table-thead >tr){
        &:nth-child(1){
            border-bottom: none;
            th{
                border-inline-end: 0px !important;
                border-bottom:none !important;
                
              &:last-child{
                border-bottom: 1px solid white !important;
                border-left: 1px solid white !important;
              }
            }
        }
        &:nth-child(2){
            th{
              &:first-child{
                border-left: 1px solid white !important;
              }
            }   
        }

        th{
            border-start-end-radius:0 !important;
            background-color: var(--dark-blue);
            font-family: 'Kanit','Courier New', Courier, monospace;
            font-weight: 400;
            color: white;
            border-bottom:none;
            text-align: center;
            padding: 0.3rem 0;
            &:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before{
                width: 0;
            }
        }
    }

    // Table body
    :global(.ant-table-tbody){
        // Body rows
        :global(.ant-table-row){           
        // Cols
        :global(.ant-table-cell){
            font-weight: 600;
            text-align: center;
            border-bottom: 2px solid white; 
            padding: 0.8rem;
            font-family: 'Kanit','Courier New', Courier, monospace;


            &:first-child{
                border-right: none;
            }

            &:nth-last-child(2),
            &:nth-last-child(3),
            &:nth-last-child(4){
                border-right:  2px solid white;
            }

            &:global(.ant-table-cell-row-hover){
                background-color: $body;
            }

            &:global(.ant-table-cell-fix-right){
                border-bottom: none !important; 
           }
            &:global(.ant-table-cell-fix-right-first){
                border-left: 2px solid white;
              }
        }
        }
    }

    // Fixed Cols
    :global(.ant-table-cell-fix-right){
        background-color: $body;
   }
}
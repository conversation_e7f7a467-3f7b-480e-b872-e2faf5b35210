import React from 'react'
import {Tabs} from "antd";
import Style from "./_tabs.module.scss";
import PropTypes from 'prop-types';

export default function TabsComponent({
activeKey,
items, 
onChange = ()=>{}, 
onTabClick = ()=>{}}) {
  return (
    <Tabs
    defaultActiveKey={items[0]?.key}
    activeKey={activeKey}
    items={items} 
    onChange={onChange} 
    className={`${activeKey === null ? Style.tab_null : Style.tabs}`}
    onTabClick={onTabClick}
    />
  )
}
TabsComponent.propTypes={
  activeKey: PropTypes.oneOfType([PropTypes.string, PropTypes.array]),
  items: PropTypes.array, 
  onChange: PropTypes.func,
  onTabClick: PropTypes.func,
}
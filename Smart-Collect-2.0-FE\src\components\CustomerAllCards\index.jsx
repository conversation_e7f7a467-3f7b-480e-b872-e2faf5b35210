import { Col, Flex, Row, Typography } from "antd";
import React, { useEffect, useState } from "react";
import { CustomerDetailsCard } from "./CustomerDetailsCard";
import DataLoading from "../Loaders/DataLoading";
import PropTypes from "prop-types";

const { Title } = Typography;

export default function CustomerAllCards({
  pageId = null,
  customerData,
  modalButtonText,
  isModal,
}) {
  const DISPLAY_COUNT = 20;
  const [displayedData, setDisplayedData] = useState([]);
  const [isCustomerDataLoading, setIsCustomerDataLoading] = useState(false);

  // Handle Load more data
  const handleLoadMoreData = () => {
    setIsCustomerDataLoading(true);
    setTimeout(() => {
      const nextChunk = customerData.slice(
        displayedData.length,
        displayedData.length + DISPLAY_COUNT
      );
      setDisplayedData((prev) => [...prev, ...nextChunk]);
      setIsCustomerDataLoading(false);
    }, 80);
  };

  // Add data if user scroll
  useEffect(() => {
    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } =
        document.documentElement;
      const scrolled = scrollTop / (scrollHeight - clientHeight);
      if (
        scrolled >= 0.5 &&
        displayedData.length < customerData.length &&
        !isCustomerDataLoading
      ) {
        handleLoadMoreData();
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [displayedData.length, customerData.length, isCustomerDataLoading]);

  // Scroll the window if display data changed
  useEffect(() => {
    window.scrollBy(0, 1);
  }, [displayedData.length]);

  // Load initial data when customerData changes
  useEffect(() => {
    if (Array.isArray(customerData) && customerData?.length > 0) {
      setDisplayedData(customerData?.slice(0, DISPLAY_COUNT));
    }
  }, [customerData]);
  return (
    <Row gutter={[20, 20]} align={"stretch"} wrap>
      {customerData?.length ? (
        displayedData?.map((data, index) => {
          return (
            <Col xl={6} md={12} xs={24} key={`${index}-${data.branchMstId}`}>
              <CustomerDetailsCard
                loanmstid={data?.loanmstid}
                branchMstId={data?.branchmstid}
                communicationCount={data?.communication_count}
                last5CommunicationTypes={data?.last_5_communication_types}
                status={data?.status}
                name={data?.customername || "Customer Name"}
                customerId={data?.customerid}
                branchName={data?.branchname}
                disbursementId={data?.disbursementid}
                dpdAmount={data?.dpd || 0}
                feedbackDate={data?.feedbackdate}
                loanType={data?.loantype}
                overdueAmount={data?.overdue_amount || 0}
                promiseAmount={data?.promise_amount || 0}
                promiseDate={data?.promise_date}
                responseDate={data?.responsedatetime}
                denialReason={data?.denial_reason}
                firstTimeDefaulter={data?.first_time_arrer}
                modalButtonText={modalButtonText}
                isModal={isModal}
                pageId={pageId}
              />
            </Col>
          );
        })
      ) : (
        <Col span={24}>
          <Flex justify="center">
            <Title level={4}>No data found!</Title>
          </Flex>
        </Col>
      )}

      {/* Show the loader */}
      {isCustomerDataLoading && <DataLoading />}
    </Row>
  );
}

CustomerAllCards.propTypes = {
  pageId: PropTypes.string,
  customerData: PropTypes.oneOfType([PropTypes.array, PropTypes.any]),
  modalButtonText: PropTypes.string,
  isModal: PropTypes.bool,
};

import { useState, useEffect } from "react";
import { Flex } from "antd";
import AppHeader from "../AppHeader";
import AllCards from "./AllCards";
import { PayTable } from "./PayTable";
import TabsComponent from "../TabsComponent";
import { AXIOS } from "../../apis/ho-Instance";
import TOTAL_CUSTOMER_IMG from "../../assets/Images/customer.png";
import PENDING_IMG from "../../assets/Images/pending.png";
import COMPLETED_IMG from "../../assets/Images/complete.png";
import { useParams } from "react-router";
import ApplicationLoader from "../ApplicationLoader";
import { ALL_CACHE_NAMES } from "../../constant";
import {
  getCacheKey,
  getFromCache,
  storeToCache,
} from "../../utils/cacheHelper";

const CACHE_NAME = ALL_CACHE_NAMES.FIELD_DASHBOARD_CACHE;

export default function FieldReadyToPay() {
  const params = useParams();
  const [currentTab, setcurrentTab] = useState(params.type || "day");
  const [tabData, setTabData] = useState({
    day: { cards: [], table: [] },
    week: { cards: [], table: [] },
    month: { cards: [], table: [] },
  });
  const [loading, setLoading] = useState(false);

  const handleTabChange = (key) => {
    setcurrentTab(key);
  };

  // Update Cards data based on the type
  const generateCardData = (data) => [
    {
      title: "Total Customer",
      loansCount: data.TotalCustomer_Loans,
      amount: data.TotalCustomer_Amt.toFixed(2),
      image: TOTAL_CUSTOMER_IMG,
    },
    {
      title: "Pending",
      loansCount: data.Pending_Loans,
      amount: data.Pending_Amt.toFixed(2),
      image: PENDING_IMG,
    },
    {
      title: "Completed",
      loansCount: data.Completed_Loans,
      amount: data.Completed_Amt.toFixed(2),
      image: COMPLETED_IMG,
    },
  ];

  // Get the data based on the type
  const fetchData = async (type) => {
    setLoading(true);
    // Build cache key for GET request (by type)
    const cacheKey = getCacheKey({
      endpoint: `promise-FO_For_${capitalize(type)}/`,
    });
    // Try to get cached data for instant UI update
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setTabData((prev) => ({
        ...prev,
        [type]: cachedData,
      }));
      setLoading(false);
    }
    try {
      const response = await AXIOS.get(`FO_For_${capitalize(type)}/`);
      const data = response.data.ReadyToPay;
      const cards = generateCardData(data);
      const table = data.Table_data;
      const newTabData = { cards, table };
      setTabData((prev) => ({
        ...prev,
        [type]: newTabData,
      }));
      // Store the latest data in cache
      await storeToCache({
        cacheName: CACHE_NAME,
        key: cacheKey,
        data: newTabData,
      });
    } catch (error) {
      console.error(`Error fetching ${type} data:`, error);
    } finally {
      setLoading(false);
    }
  };

  // Capitalize the type
  const capitalize = (str) => str.charAt(0).toUpperCase() + str.slice(1);

  // Invoking the handler based on the current tab
  useEffect(() => {
    fetchData(currentTab);
  }, [currentTab]);

  // Iterate the items
  const items = ["day", "week", "month"].map((type) => ({
    key: type,
    label: `For ${capitalize(type)}`,
    children: (
      <Flex gap={20} vertical>
        {loading ? (
          <ApplicationLoader />
        ) : (
          <>
            <AllCards cardData={tabData[type].cards} />
            <PayTable tableData={tabData[type].table} />
          </>
        )}
      </Flex>
    ),
  }));
  return (
    <Flex vertical gap={20}>
      <AppHeader title={"Promised to pay"} />
      <TabsComponent
        items={items}
        onChange={handleTabChange}
        activeKey={currentTab}
      />
    </Flex>
  );
}

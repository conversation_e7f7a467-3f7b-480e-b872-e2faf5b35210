import { Flex, message, Typography } from "antd";
import React, { useState, useEffect } from "react";
import AppHeader from "../AppHeader";
import {
  ALL_CACHE_NAMES,
  handleDownloadTable,
  LOCAL_KEYS,
  SUMMARY_FILTERS,
} from "../../constant";
import DOWNLOAD_IMG from "../../assets/Images/download.svg";
import { DashboardEngagementAllCards } from "../DashboardEngagementAllCards";
import { DashboardEngagementTable } from "../DashboardEngagementTable";
import Style from "./_style.module.scss";
import { AXIOS } from "../../apis/ho-Instance";
import ApplicationLoader from "../ApplicationLoader";
import {
  getCacheKey,
  getFromCache,
  getPostCacheKey,
  storeToCache,
} from "../../utils/cacheHelper";
import { encryptBody, isSameEncryptedBody } from "../../utils/crypto";

const { Text } = Typography;
const CACHE_NAME = ALL_CACHE_NAMES.CAMPAIGN_SUMMARY_DASHBOARD;

export default function SummarySmsDash() {
  const [tableData, setTableData] = useState([]);
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSwitchOn, setIsSwitchOn] = useState(false);
  const storedFilter = localStorage.getItem(SUMMARY_FILTERS.summaryFilter);
  const storedCamp = localStorage.getItem(SUMMARY_FILTERS.selectedCampaignId);

  const handleFetchCampsAndBranchIds = async () => {
    try {
      const storedCampaigns = localStorage.getItem(
        SUMMARY_FILTERS.summaryCampaignIds
      );
      const storedBranches = localStorage.getItem(
        SUMMARY_FILTERS.summaryBranchIds
      );

      const campaignIds = storedCampaigns ? JSON.parse(storedCampaigns) : [];
      const branchIds = storedBranches ? JSON.parse(storedBranches) : [];

      return { campaignIds, branchIds };
    } catch (error) {
      console.error(
        "Error reading campaign/branch IDs from localStorage",
        error
      );
      return { campaignIds: [], branchIds: [] };
    }
  };

  // Apply the data
  const applyBlasterData = (data) => {
    setData(data);
    setTableData(data);
  };

  // Get blaster data
  const handleGetSmsData = async () => {
    setIsLoading(true);
    const cacheKey = getCacheKey({ endpoint: "campaign-sms-history/" });
    // Check cache
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      applyBlasterData(cachedData);
      setIsLoading(false);
    }
    const { campaignIds, branchIds } = await handleFetchCampsAndBranchIds();
    try {
      const response = await AXIOS.get("campaign-sms-history/", {
        params: {
          campaign_mstids: campaignIds,
          branch_id: branchIds,
        },
      });
      if (response.status === 200) {
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
        applyBlasterData(response.data);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Post the whatsapp data
  const handleFilterSmsData = async ({
    fromDate,
    toDate,
    BranchName,
    disbursementID,
    campaignMstIds,
  }) => {
    setIsLoading(true);
    const { campaignIds, branchIds } = await handleFetchCampsAndBranchIds();
    const body = {
      fromDate,
      toDate,
      BranchName: BranchName?.length ? BranchName : branchIds,
      dpdfrom: 0,
      dpdto: 10000,
      DisbursementID: disbursementID,
      campaign_mstids: campaignMstIds ? [campaignMstIds] : campaignIds,
    };
    const cacheKey = getPostCacheKey({
      endpoint: "campaign-sms-history/",
      body,
    });
    const encryptedOldBody = localStorage.getItem(LOCAL_KEYS.HO_CHANNEL_FILTER);
    const sameBody = isSameEncryptedBody({
      newBody: body,
      encryptedOldBody,
    });

    if (sameBody) {
      const cachedData = await getFromCache({
        cacheName: CACHE_NAME,
        key: cacheKey,
      });
      if (cachedData) {
        applyBlasterData(cachedData);
        setIsLoading(false);
      }
    }
    try {
      const response = await AXIOS.post("campaign-sms-history/", body);
      if (response.status === 200) {
        const encrypted = encryptBody(body);
        if (encrypted) {
          localStorage.setItem(LOCAL_KEYS.HO_CHANNEL_FILTER, encrypted);
        }
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
        applyBlasterData(response.data);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Apply filter and fetch data
  const applyFilters = async ({
    fromDate,
    toDate,
    BranchName,
    disbursementID,
  }) => {
    await handleFilterSmsData({
      fromDate: fromDate,
      toDate: toDate,
      BranchName,
      disbursementID,
      campaignMstIds: parseInt(storedCamp),
    });
  };

  const ClearFilters = () => {
    handleGetSmsData();
    localStorage.removeItem(SUMMARY_FILTERS.summaryFilter);
    localStorage.removeItem(LOCAL_KEYS.HO_CHANNEL_FILTER);
  };

  const handleDownload = async () => {
    if (tableData.length === 0 || !tableData) {
      return message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: "SMS",
        worksheetName: "Summary-Sms-Calls",
        tableData: tableData,
      });
    }
  };

  // On mount, check localStorage for saved filters
  useEffect(() => {
    if (storedFilter || storedCamp) {
      const { fromDate, toDate, BranchName, disbursementID } = storedFilter
        ? JSON.parse(storedFilter)
        : {
            fromDate: null,
            toDate: null,
            BranchName: [],
            disbursementID: "",
          };
      const parsedCamp = storedCamp ? parseInt(storedCamp) : null;
      handleFilterSmsData({
        fromDate,
        toDate,
        BranchName,
        disbursementID,
        campaignMstIds: parsedCamp,
      });
    } else {
      handleGetSmsData();
    }
  }, []);
  return (
    <Flex gap={10} vertical>
      <AppHeader
        title={
          <Flex gap={5} className={Style.title}>
            <Text>
              SMS: <span style={{ color: "#0F2050" }}>{data.SMS}</span>
            </Text>
            <Text>
              Distinct Customers :{" "}
              <span style={{ color: "#0F2050" }}>
                {data.distinct_customers}
              </span>
            </Text>
          </Flex>
        }
        isDashboard={false}
        isSwitchOn={isSwitchOn}
        setIsSwitchOn={setIsSwitchOn}
        ClearFilters={ClearFilters}
        applyFilters={applyFilters}
        isDashboardOtherPages={true}
      />
      <div>
        {isLoading ? (
          <ApplicationLoader />
        ) : (
          <div className={Style.details}>
            {!isSwitchOn ? (
              <DashboardEngagementAllCards data={tableData} />
            ) : (
              <Flex vertical gap={20} style={{ paddingBlockStart: "1rem" }}>
                <Flex justify="end">
                  <button
                    className={Style.download_button}
                    onClick={handleDownload}
                  >
                    <img src={DOWNLOAD_IMG} alt="download-button" />
                  </button>
                </Flex>
                <DashboardEngagementTable data={tableData} />
              </Flex>
            )}
          </div>
        )}
      </div>
    </Flex>
  );
}

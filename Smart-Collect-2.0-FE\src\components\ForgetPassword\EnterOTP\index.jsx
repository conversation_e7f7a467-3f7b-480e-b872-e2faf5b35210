// EnterOtp.jsx
import React, { useState, useEffect} from "react";
import { Input, Button, message } from "antd";
import Style from "../_forget-password.module.scss";
import { AXIOS } from "../../../apis/ho-Instance";
import CryptoJS from "crypto-js";  // Import CryptoJS
import PropTypes from "prop-types";

export default function OtpPage({ setStep, otp,setOtp, UserID}) {
    const [LocalOtp, setLocalOtp] = useState("");
    const [messageApi, contextHolder] = message.useMessage();
    const [isOtpValid, setIsOtpValid] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        setIsOtpValid(LocalOtp.length === 5);
    }, [LocalOtp]);
    
    const hashOtp = (otp) => {
        return CryptoJS.SHA256(otp).toString(CryptoJS.enc.Hex);
    };

    const handleVerify = async(e) => {
        e.preventDefault();
        setIsLoading(true);
        const hashedLocalOtp = hashOtp(LocalOtp);
        if (hashedLocalOtp === otp) {   
            messageApi.success("OTP verified successfully!");
            setTimeout(() => {
                setStep(3);  
            }, 1500) 
        } else {
            messageApi.error("Invalid OTP. Please try again.");
        }
        setIsLoading(false);
    }

    const handleResendOtp = async() => {
        setLocalOtp("");
        const requestData = {
            action: "resend_otp",
            UserID: UserID
        };
        try {
            const response = await AXIOS.post("forget_password/", requestData);
            if (response.status === 200) {
                setOtp(response.data.otp);
                messageApi.success("OTP resent successfully!");}
            else {
                messageApi.error("error in resending OTP");
                }
        }catch(error) {
          console.log("Error in verify email:", error);
        }finally {
            setIsLoading(false);
        }        
    };

    return (
        <div>
            {contextHolder}
            <form onSubmit={handleVerify} className={Style.form}>
                <div className={Style.inputContainer}>
                    <Input.OTP
                        length={5}
                        value={LocalOtp}
                        onChange={setLocalOtp} 
                        className={`${Style.input} ${Style.otpInput}`}
                    />
                </div>
                <div className={Style.resendOtpContainer}>
                    <span>Didn’t receive the OTP? </span>
                    <Button type="link" onClick={handleResendOtp} className={Style.resendOtpButton}>
                        Resend OTP
                    </Button>
                </div>
                <Button 
                 type="primary" 
                 htmlType="submit"
                 block 
                 className={`${Style.button} ${!isOtpValid && Style.disabledButton}`}
                 loading={isLoading}
                 disabled={!isOtpValid}>
                 Verify
                </Button>
            </form>
        </div>
    );
}

OtpPage.propTypes={
    setStep: PropTypes.func, 
    otp: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    setOtp: PropTypes.func, 
    UserID: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
}
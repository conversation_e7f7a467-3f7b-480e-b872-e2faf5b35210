import { But<PERSON>, Flex, message } from "antd";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router";
import TabsComponent from "../../../TabsComponent";
import AppHeader from "../../../AppHeader";
import { CategoriesComponent } from "./Categories";
import Style from "./_style.module.scss";
import { AXIOS } from "../../../../apis/ho-Instance";
import { CommunicationFlowComponent } from "./CommunicationFlow";
import TemplateComponent from "./Template";
import { CampaignsComponent } from "./FinalizeCampaign";

export default function NormalCategoryCampaign() {
  const params = useParams();
  const navigate = useNavigate();
  const CampaignMstID = localStorage.getItem("campaignId");
  const [currentTab, setCurrentTab] = useState("categories");
  const [tabs, setTabs] = useState(["categories"]);
  const [isLoading, setIsLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [commonFlowData, setCommonFlowData] = useState([]);

  // Temp Campaign state
  const [campaignData, setCampaignData] = useState({
    categories: {
      name: "",
      filters: [
        {
          parameterName: null,
          comparison: null,
          value: "",
          list: "",
          rangeFrom: 0,
          rangeTo: 1,
          comparisonType: "value",
        },
      ],
    },
    templates: [
      {
        name: "",
        approvedTemplateId: null,
        language: null,
        showScripting: true,
        communicationType: null,
        commFlowID: null,
        selectedTemplateName: null,
        scriptValue: "",
        interactionPlans: "",
        rules: "",
        description: "",
        initialTexts: "",
        endingTexts: "",
        intents: [{ name: "", response: "" }],
        response: [{ buttonName: "", text: "" }],
        buttonsMapping: [],
        variablesMapping: [],
        initialTextVariableMapping: [],
        endingTextVariableMapping: [],
        nodes: [],
        nodeButtonMapping: [],
        isIvrFlowApproved: false,
      },
    ],
    communicationFlows: {
      name: "",
      commFlow: [
        {
          applicantType: null,
          channelName: null,
          basedOn: null,
          isAfter: true,
          dayCount: null,
          daysValues: {},
          flowType: "normal",
          frequency: "daily",
          periodType: "count",
          startDate: null,
          interval: 1,
          count: null,
          until: null,
          byWeekday: null,
        },
      ],
    },
    campaign: {
      priority: 1,
      name: params.campaignName || "",
    },
  });

  // Tabs Data
  const items = [
    {
      key: "categories",
      label: "Categories",
      children: (
        <CategoriesComponent
          campaignData={campaignData}
          setCampaignData={setCampaignData}
        />
      ),
    },
    {
      key: "communicationFlow",
      label: "Communication Flow",
      children: (
        <CommunicationFlowComponent
          campaignData={campaignData}
          setCampaignData={setCampaignData}
          setCommonFlowData={setCommonFlowData}
        />
      ),
    },
    {
      key: "template",
      label: "Template",
      children: (
        <TemplateComponent
          commFlowData={commonFlowData}
          campaignData={campaignData}
          setCampaignData={setCampaignData}
        />
      ),
    },
    {
      key: "campaign",
      label: "Campaign",
      children: (
        <CampaignsComponent
          campaignData={campaignData}
          setCampaignData={setCampaignData}
          commFlowData={commonFlowData}
        />
      ),
    },
  ];

  // Transfer the common flow
  const transformDays = ({ daysString, frequency }) => {
    if (!daysString) return { dayCount: null, daysValues: {} };
    const dayList = daysString
      .split(",")
      .map((day) => Math.abs(parseInt(day))) // Convert to positive integers
      .filter((day) => !isNaN(day));

    const maxDay = Math.max(...dayList);

    // Determine dayCount as 7, 15, or 30
    let dayCount = 7;
    if (maxDay > 7 && maxDay <= 15) {
      dayCount = 15;
    } else if (maxDay > 15) {
      dayCount = 30;
    }

    // Build daysValues with all values from 1 to dayCount
    const daysValues = {};
    const isWeekFrequency = frequency === "weekly";
    const startIndex = isWeekFrequency ? 0 : 1;
    const endIndex = isWeekFrequency ? 6 : dayCount;

    for (let i = startIndex; i <= endIndex; i++) {
      daysValues[i] = dayList.includes(i);
    }
    return { dayCount, daysValues };
  };

  // Transfer the data as accurate format
  const mapApiToCampaignData = (apiData) => {
    const commFlowSet = apiData?.commflow_set || [];
    const filterSet = apiData?.filtermst_set || [];
    return {
      categories: {
        name: filterSet?.[0]?.Name || "",
        filters: filterSet.map((filter) => {
          const parameterName = filter?.FiltersID?.Column || null;
          const comparison = filter?.FiltersID?.Comparison || null;
          const comparisonType = filter?.FiltersID?.ValueType || "value";
          const value = filter?.FiltersID?.Value || "";
          const list = "";
          const rangeFrom = 0;
          const rangeTo = 1;

          const isNonEditable =
            parameterName &&
            comparisonType &&
            comparison &&
            (value !== "" ||
              list !== "" ||
              (rangeFrom !== null && rangeTo !== null));

          return {
            parameterName,
            comparison,
            value,
            list,
            rangeFrom,
            rangeTo,
            comparisonType,
            FiltersID: filter?.FiltersID?.FiltersID,
            ...(isNonEditable && { isNonEditable: true }),
          };
        }),
      },
      templates: commFlowSet.map((flow) => ({
        name: flow?.FlowID_data?.FlowName || "",
        approvedTemplateId: flow?.FlowID_data?.ApprovedTemplateId || null,
        language: flow?.FlowID_data?.Language || null,
        showScripting: flow?.FlowID === null,
        communicationType: flow?.CommunicationType || null,
        commFlowID: flow?.CommFlowID || null,
        selectedTemplateName: flow?.FlowID || null,
        scriptValue: flow?.FlowID_data?.Script || "",
        interactionPlans: flow?.FlowID_data?.InteractionPlans || "",
        rules: flow?.FlowID_data?.Rules || "",
        description: flow?.FlowID_data?.Description || "",
        initialTexts: flow?.FlowID_data?.StartMsg || "",
        endingTexts: flow?.FlowID_data?.EndMsg || "",
        intents: flow?.FlowID_data?.Intents || [{ name: "", response: "" }],
        response: flow?.FlowID_data?.Response || [{ buttonName: "", text: "" }],
        buttonsMapping: flow?.FlowID_data?.ButtonsMapping || [],
        variablesMapping: flow?.FlowID_data?.whatsappvariablemapping_set || [],
        initialTextVariableMapping:
          flow?.FlowID_data?.InitialTextVariables || [],
        endingTextVariableMapping: flow?.FlowID_data?.EndingTextVariables || [],
        nodes: flow?.FlowID_data?.Nodes || [],
        nodeButtonMapping: flow?.FlowID_data?.NodeButtonMapping || [],
        isIvrFlowApproved: flow?.FlowID_data?.IsIvrFlowApproved || false,
      })),
      communicationFlows: {
        name: commFlowSet?.[0]?.FlowName || "",
        commFlow: commFlowSet.map((flow) => {
          const daysTransformed = transformDays({
            daysString: flow?.Days || flow?.byweekday || "",
            frequency: flow?.frequency,
          });

          return {
            applicantType: flow?.ExtraColumn1 || null,
            channelName: flow?.CommunicationType || null,
            commFlowID: flow?.CommFlowID || null,
            basedOn: flow?.BasedOn || null,
            isAfter: flow?.BeforeAfter === "after",
            dayCount: daysTransformed?.dayCount,
            daysValues: daysTransformed?.daysValues,
            flowType: flow?.campaign_type || "normal",
            periodType: flow?.PeriodType || "count",
            frequency: flow?.frequency || "daily",
            startDate: flow?.startdate || null,
            interval: flow?.interval || null,
            count: flow?.count || null,
            until: flow?.until || null,
            byWeekday: transformDays({
              daysString: flow?.byweekday || "",
              frequency: flow?.frequency,
            })?.dayCount,
          };
        }),
      },
      campaign: {
        priority: apiData?.Priority || 1,
        name: apiData?.Name || "",
      },
    };
  };

  // Handle Tab clicking
  const handleTabClick = (key) => {
    const isDisabled = !tabs.includes(key);
    if (isDisabled) {
      return message.warning(
        `Please, submit the ${currentTab}, than only you can go further!`
      );
    } else {
      setCurrentTab(key);
    }
  };

  // Handle fetch campaign if have ID
  const handleFetchCampaign = async () => {
    try {
      const res = await AXIOS.get(`v1/getcampaigns/${CampaignMstID}/`);
      if (res.status === 200 && res.data) {
        const mappedData = mapApiToCampaignData(res.data);
        localStorage.setItem(
          "CommFlowMstID",
          JSON.stringify(res.data?.commflow_set?.[0]?.CommFlowMstID ?? null)
        );

        setCampaignData(mappedData);

        // Extract only required fields like in handleSendFlowsData
        const formattedCommonFlow =
          res.data.commflow_set?.map((flow) => ({
            CommFlowID: flow.CommFlowID,
            BeforeAfter: flow.BeforeAfter,
            CommunicationType: flow.CommunicationType,
          })) || [];

        setCommonFlowData(formattedCommonFlow);
      }
    } catch (error) {
      console.log("Error in Campaign detail", error?.message);
    }
  };

  // Send categories data
  const handleSendCategoriesData = async () => {
    const categoryName =
      campaignData.categories.name || "Default Campaign Name";
    const filters = campaignData.categories.filters.map((filter) => {
      let filterObj = {
        Column: filter.parameterName,
        Comparison: filter.comparison,
        Value: filter.value || "-",
        FiltersID: filter?.FiltersID || null,
        ValueType: filter?.comparisonType || "value",
      };
      if (filter.comparison === "range") {
        filterObj.range_from = filter.rangeFrom;
        filterObj.range_to = filter.rangeTo;
      } else if (filter.comparison === "list") {
        filterObj.IN = filter.list;
      }
      return filterObj;
    });

    const payload = {
      Name: categoryName,
      CampaignMstID: CampaignMstID,
      Filters: filters,
    };
    console.log("payload", payload);
    const response = await AXIOS.post("v1/filters/createfilters", payload, {
      validateStatus: () => true,
    });
    console.log("response", response);
    return response.status === 201 || response.status === 200;
  };

  // Send flows data
  const handleSendFlowsData = async () => {
    const flowName =
      campaignData.communicationFlows.name || "Default flow Name";
    const CommFlowMstID =
      JSON.parse(localStorage.getItem("CommFlowMstID")) || null;

    const payload = {
      FlowName: flowName,
      CampaignMstID: CampaignMstID,
      CommFlowMstID: CommFlowMstID,
      CommFlow: campaignData.communicationFlows.commFlow.map((flow) => {
        // Default values for normal
        let Days = Object.keys(flow.daysValues)
          .filter((key) => flow.daysValues[key])
          .join(",");
        let byweekday = null;
        if (flow.flowType === "periodic" && flow.frequency === "weekly") {
          Days = null;
          byweekday = Object.keys(flow.daysValues)
            .filter((key) => flow.daysValues[key])
            .join(",");
        }
        return {
          ExtraColumn1: flow.applicantType,
          CommunicationType: flow.channelName,
          Days: Days,
          byweekday: flow.flowType === "periodic" ? byweekday : null,
          frequency: flow.flowType === "periodic" ? flow.frequency : null,
          startdate: flow.flowType === "periodic" ? flow.startDate : null,
          interval: flow.flowType === "periodic" ? flow.interval : null,
          count: flow.flowType === "periodic" ? flow.count : null,
          until: flow.flowType === "periodic" ? flow.until : null,
          PeriodType: flow.flowType === "periodic" ? flow.periodType : null,
          BeforeAfter: flow.isAfter ? "after" : "before",
          campaign_type: flow.flowType,
          BasedOn: flow.basedOn,
          CampaignMstID: CampaignMstID,
          CommFlowID: flow.commFlowID || null,
        };
      }),
    };

    const response = await AXIOS.post("v1/campaign/createCommFlow", payload);
    if (response.status === 201 || response.status === 200) {
      // Extract only required fields from API response
      const formattedData = response.data?.flowdata.map(
        ({ CommFlowID, BeforeAfter, CommunicationType }) => ({
          CommFlowID,
          BeforeAfter,
          CommunicationType,
        })
      );

      // Reset the flow master ID
      if (CommFlowMstID) {
        localStorage.removeItem("CommFlowMstID");
      }
      localStorage.setItem(
        "CommFlowMstID",
        JSON.stringify(response.data?.mstdata?.CommFlowMstID)
      );

      // Update state
      setCommonFlowData((prev) => [...prev, ...formattedData]);

      return true;
    } else {
      return false;
    }
  };

  // Send template data
  const handleSendTemplates = async () => {
    const payload = campaignData.templates.map((temp) => ({
      CommFlowID: Number(temp.commFlowID),
      FlowID:
        temp.selectedTemplateName === "new"
          ? Number(temp.approvedTemplateId)
          : Number(temp.selectedTemplateName),
    }));
    const response = await AXIOS.post(
      "v1/campaign/updateFlowIdCommFlow",
      payload
    );
    return response.status === 201 || response.status === 200;
  };

  // Handle the api based on the tab
  const handleApiCallForCurrentTab = async (tabKey) => {
    setIsLoading(true);
    try {
      if (tabKey === "categories") {
        return await handleSendCategoriesData();
      } else if (tabKey === "communicationFlow") {
        return await handleSendFlowsData();
      } else if (tabKey === "template") {
        return await handleSendTemplates();
      }
    } catch (error) {
      console.log(`Error in create ${tabKey}`, error?.message);
      message.error(`${tabKey} is not created, try again!`);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle next functionality
  const handleNext = async () => {
    const tabKeys = items.map((item) => item.key);
    const currentIndex = tabKeys.indexOf(currentTab);

    // Call API based on the current tab before moving to the next tab
    const success = await handleApiCallForCurrentTab(currentTab);

    if (currentIndex < tabKeys.length - 1 && success) {
      setCurrentTab(tabKeys[currentIndex + 1]);

      // Add next tab to visited tabs if validation passes
      if (!tabs.includes(tabKeys[currentIndex + 1])) {
        setTabs((prevTabs) => [...prevTabs, tabKeys[currentIndex + 1]]);
      }
    }
  };

  // Handle save campaign
  const handleSave = async () => {
    setSaveLoading(true);
    try {
      const response = await AXIOS.post("v1/campaign/FinalizeCampaign", {
        Name: campaignData.campaign.name,
        Priority: Number(campaignData.campaign.priority),
        CampaignMstID,
        Status: "Saved",
      });

      if (response.status === 200 || response.status === 201) {
        localStorage.removeItem("campaignId");
        navigate("/ho/campaign-management", { replace: true });
      }
    } catch (error) {
      console.log("Error in campaign", error?.message);
    } finally {
      setSaveLoading(false);
    }
  };

  // Handle submit campaign
  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      const response = await AXIOS.post("v1/campaign/FinalizeCampaign", {
        Name: campaignData.campaign.name,
        Priority: Number(campaignData.campaign.priority),
        CampaignMstID,
      });

      if (response.status === 200 || response.status === 201) {
        localStorage.removeItem("campaignId");
        navigate("/ho/campaign-management", { replace: true });
      }
    } catch (error) {
      console.log("Error in campaign", error?.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Called the api
  useEffect(() => {
    if (params.status === "saved") {
      handleFetchCampaign();
    }
  }, [params]);

  return (
    <Flex vertical gap={10} style={{ marginBlockEnd: "2rem" }}>
      <AppHeader title={`${params.campaignName}-Normal`} />
      <TabsComponent
        activeKey={currentTab}
        items={items}
        onTabClick={handleTabClick}
      />

      <Flex align="center" justify="center">
        {currentTab === "campaign" ? (
          <Flex justify="center" gap={10}>
            <Button
              className={Style.next_button}
              loading={saveLoading}
              onClick={handleSave}
            >
              Save
            </Button>
            <Button
              className={Style.next_button}
              loading={isLoading}
              onClick={handleSubmit}
            >
              Submit
            </Button>
          </Flex>
        ) : (
          <Button
            className={Style.next_button}
            onClick={handleNext}
            // disabled={disables}
            loading={isLoading}
          >
            Next
          </Button>
        )}
      </Flex>
    </Flex>
  );
}

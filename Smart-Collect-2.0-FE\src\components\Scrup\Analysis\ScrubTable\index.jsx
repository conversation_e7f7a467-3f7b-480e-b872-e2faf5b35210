import { Flex, message, Table, Typography } from "antd";
import React, { useEffect, useState } from "react";
import Style from "./_style.module.scss";
import DOWNLOAD_IMG from "../../../../assets/Images/download.svg";
import { formatAmount, formatDigits, handleDownloadTable } from "../../../../constant";
import PropTypes from "prop-types";

const {Text} = Typography;

export function ScrubTable({ 
  isGraph, 
  tableData, 
  isAccount, 
  selectedState, 
  setSelectedState, 
  selectedAnalysisCard,
  tableLevel, 
  setTableLevel,
  handleUpdateStateTable,
  handleGetCustomerDetails }) {
  const [data, setData] = useState([]);

  // Handle State Click → Get Regions
  const handleStateClick = (stateId) => {
    setSelectedState(stateId);
    setTableLevel("region");
    handleUpdateStateTable({ stateId, cardWise:selectedAnalysisCard, tablewise:"State" });
  };
    
  // Handle Region Click → Get Branches
  const handleRegionClick = (regionId) => {
    setTableLevel("branch");
    handleUpdateStateTable({ stateId:selectedState, regionId, tablewise:"Region", cardWise: selectedAnalysisCard});
    };
    
  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      sorter: (a, b) => a.key - b.key,
      render: (_, data) => <Text>{data?.key}.</Text>,
    },
    isGraph
      ? null
      : {
          title: "State",
          dataIndex: "State",
          sorter: (a, b) => a.State?.localeCompare(b.State),
          render: (State, data) => (
            <Text
              className={Style.blueText}
              onClick={() =>handleStateClick(data?.State_id)}
            >
              {State || "--"}
            </Text>
          ),
        },
    ...(selectedState
      ? [
          {
            title: "Region",
            dataIndex: "Region",
            sorter: (a, b) => a.Region?.localeCompare(b.Region),
            render: (Region, data) => (
              <Text
                className={Style.blueText}
                onClick={() => handleRegionClick(data?.Region_id)}
              >
                {Region || "--"}
              </Text>
            ),
          },
        ]
      : []),
   ...(tableLevel === "branch" && selectedState
      ? [
          {
            title: "Branch",
            dataIndex: "Branch",
            sorter: (a, b) => a.Branch?.localeCompare(b.Branch),
            render: (Branch, data) => (
              <Text
                className={Style.blueText}
               // onClick={() => handleBranchClick(data?.Branch_id)}
              >
                {Branch || "--"}
              </Text>
            ),
          },
        ]
      : []),
    {
      title: "Risk",
      children: [
        {
          title: "Very Low",
          dataIndex: "very_low_risk",
          sorter: (a, b) => a.veryLow - b.veryLow,
          render: (value, record) => <Text 
          className={Style.lan}
          onClick={()=> handleGetCustomerDetails({riskCategory:"very_low_risk", state_id:record?.State_id})}>
            {isAccount? formatDigits(value||0): `Rs.${formatAmount(value || 0)}`}
          </Text>,
        },
        {
          title: "Low",
          dataIndex: "low_risk",
          sorter: (a, b) => a.low - b.low,
          render: (value, record) => <Text className={Style.lan} 
          onClick={()=> handleGetCustomerDetails({riskCategory:"low_risk", state_id:record?.State_id})}>
            {isAccount? formatDigits(value||0): `Rs.${formatAmount(value || 0)}`}
          </Text>,
        },
        {
          title: "Medium",
          dataIndex: "medium_risk",
          sorter: (a, b) => a.medium - b.medium,
          render: (value, record) => <Text 
          className={Style.lan}
          onClick={()=> handleGetCustomerDetails({riskCategory:"medium_risk", state_id:record?.State_id})}>
            {isAccount? formatDigits(value||0): `Rs.${formatAmount(value || 0)}`}
          </Text>,
        },
        {
          title: "High",
          dataIndex: "high_risk",
          sorter: (a, b) => a.high - b.high,
          render: (value, record) => <Text 
          className={Style.lan}
          onClick={()=> handleGetCustomerDetails({riskCategory:"high_risk", state_id:record?.State_id})}>
            {isAccount? formatDigits(value||0): `Rs.${formatAmount(value || 0)}`}
          </Text>,
        },
        {
          title: "Very High",
          dataIndex: "very_high_risk",
          sorter: (a, b) => a.high - b.high,
          render: (value, record) => <Text 
          className={Style.lan}
          onClick={()=> handleGetCustomerDetails({riskCategory:"very_high_risk", state_id:record?.State_id})}>
            {isAccount? formatDigits(value||0): `Rs.${formatAmount(value || 0)}`}
          </Text>,
        },
        // {
        //   title: "SMS",
        //   dataIndex: "sms",
        //   sorter: (a, b) => a.sms - b.sms,
        //   render: (value) => <Text className={Style.lan}>{0}</Text>,
        // },
        // {
        //   title: "Dailers",
        //   dataIndex: "dialers",
        //   sorter: (a, b) => a.dialers - b.dialers,
        //   render: (value) => <Text className={Style.lan}>{0}</Text>,
        // },
        // {
        //   title: "Blaster Call",
        //   dataIndex: "blaster",
        //   sorter: (a, b) => a.blaster - b.blaster,
        //   render: (value) => <Text className={Style.lan}>{0}</Text>,
        // },
      ],
    },
    {
      title: "Total",
      dataIndex: "total",
      sorter: (a, b) => a.total - b.total,
      render: (value, record) => <Text 
      onClick={()=> handleGetCustomerDetails({riskCategory:"total", state_id:record?.State_id})}>
        {isAccount ? formatDigits(value ||0) : `Rs.${formatAmount(value || 0)}`}
      </Text>,
    },
  ].filter(Boolean);

  const dataSource = Array.isArray(data)
  ? data.map((data, i) => ({
      key: i + 1,
      ...data,
    }))
  : [];

  useEffect(() => {
    setData(tableData);
  }, [tableData]);

  // Handle downloading
  const handleDownload = async () => {
    if (data.length === 0) {
    return  message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: "Scrub Analysis Table",
        worksheetName: "scrub-analysis-table",
        tableData: data,
      });
    }
  };

  return (
    <>
      <Flex justify="end" gap="1">
        <button className={Style.download_button}  onClick={handleDownload}>
          <img
            src={DOWNLOAD_IMG}
            alt="download-button"
          />
        </button>
      </Flex>

      <Table
        bordered
        virtual
        className={Style.custom_table}
        columns={columns}
        dataSource={dataSource}
        scroll={{
          x: 1600,
          y: 460,
        }}
        pagination={{
          showSizeChanger: false,
        }}
      />
    </>
  );
}

ScrubTable.propTypes={
  isGraph: PropTypes.bool, 
  tableData: PropTypes.array, 
  isAccount: PropTypes.bool, 
  selectedState: PropTypes.any, 
  setSelectedState: PropTypes.func, 
  selectedAnalysisCard: PropTypes.any,
  tableLevel: PropTypes.string, 
  setTableLevel: PropTypes.func,
  handleUpdateStateTable: PropTypes.func,
  handleGetCustomerDetails: PropTypes.func
}
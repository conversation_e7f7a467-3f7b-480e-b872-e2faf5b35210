@import url("../../../../index.css");

.collection {
  padding: 0rem 0 1.5rem 1rem;
  min-height: 150px;

  .text {
    color: var(--dark-blue);
    font-weight: 400;
    width: 300px;
    font-family: "<PERSON>nit", "Courier New", Courier, monospace;
  }
  .feedback_button {
    background-color: var(--dark-blue);
    color: white;
    padding: 1rem 2rem;
    border: none;
    font-family: "Kani<PERSON>", "Courier New", Courier, monospace;

    &:hover {
      background-color: var(--dark-blue) !important;
      color: white !important;
      border: none;
      box-shadow: none;
    }
  }

  :global(.ant-picker-outlined),
  :global(.ant-input-number),
  :global(.ant-input-number-group-addon) {
    background-color: #e4f8f9 !important;
    border: none !important;
    width: 200px;
  }

  :global(.ant-input-number-group-addon) {
    background-color: #e4f8f9 !important;
    border: none !important;
    width: 0;
    padding: 0 0 0 5px;
  }

  :global(.ant-input-number) {
    width: 186px;
  }

  :global(.ant-input-number-input) {
    color: var(--dark-blue);
    font-weight: 500;
    font-family: "Kanit", "Courier New", Courier, monospace;
  }

  :global(.ant-input-number-outlined) {
    &:focus-within {
      box-shadow: none !important;
    }
  }

  :global(.ant-input-number-handler-wrap) {
    display: none;
  }

  :global(.ant-segmented) {
    background-color: var(--light-green);
    border-radius: 10px;

    :global(.ant-segmented-item) {
      :global(.ant-segmented-item-label) {
        color: #0f20504a;
        font-weight: 400;
        font-family: "Kanit", "Courier New", Courier, monospace;
        background-color: transparent;
      }
      &::after {
        background-color: transparent !important;
        display: none;
      }
    }

    :global(.ant-segmented-item-selected) {
      background-color: var(--dark-blue) !important;
      color: white;
      box-shadow: none;
      border-radius: 10px;
      :global(.ant-segmented-item-label) {
        color: white;
      }
    }
    :global(.ant-segmented-thumb) {
      background-color: var(--light-green);
      border-radius: 10px;
    }
  }

  .date_picker {
    :global(.ant-picker-input > input) {
      font-weight: 400;
      color: var(--dark-blue);
      font-family: "Kanit", "Courier New", Courier, monospace;

      &::placeholder {
        font-weight: 600;
      }
    }
    &::placeholder {
      color: #0f20504a;
      font-weight: 600;
    }
  }
}

$light-gray: #d9d9d9;
$tab-height: 7px;

:global(.ant-modal-wrap):has(.pay_modal) {
  z-index: 1200 !important;
}

:global(.ant-modal-wrap):has(.success_modal) {
  z-index: 1300 !important;
}

.pay_modal,
.success_modal {
  .img_div {
    width: 120px;
    img {
      width: 100%;
      object-fit: contain;
    }
  }
  .queue_title {
    margin: 0;
    color: var(--dark-blue);
    font-size: 16px;
    font-family: "Kanit", "Courier New", Courier, monospace;
    font-weight: 400;
    text-align: center;
  }
  .yes_btn,
  .no_btn {
    border: none;
    outline: none;
    background-color: #3ba944 !important;
    color: white;
    &:hover {
      background-color: #3ba944;
      color: white !important;
    }
  }

  .no_btn {
    background-color: white !important;
    color: black;

    &:hover {
      background-color: white;
      color: black !important;
    }
  }

  .queue_button {
    background-color: var(--dark-blue);
    color: white;
    padding: 1rem 2rem;
    border: none;
    font-family: "Kanit", "Courier New", Courier, monospace;

    &:hover {
      background-color: var(--dark-blue) !important;
      color: white !important;
      border: none;
      box-shadow: none;
    }
  }

  :global(.ant-modal-close) {
    right: 14px;
    top: 10px;
    color: black;
    &:hover {
      color: black !important;
      background-color: transparent;
    }
    :global(.ant-modal-close-x) {
      font-size: 10px;
      color: black;
    }
  }

  :global(.ant-modal-header) {
    background: var(--dark-blue);
    padding: 0.5rem 0px;
    text-align: center;
    border-radius: 8px;

    :global(.ant-modal-title) {
      color: white;
      font-weight: 500;
    }
  }

  :global(.ant-modal-content) {
    padding: 6px;
    background-color: #e4f8f9;
    border: 1px solid var(--dark-blue);

    :global(.ant-modal-body) {
      padding: 1rem;

      :global(.ant-tabs) {
        width: 100%;

        :global(.ant-tabs-ink-bar) {
          background-color: var(--dark-blue);
          height: $tab-height;
          border-radius: 20px;
        }

        :global(.ant-tabs-nav) {
          :global(.ant-tabs-tab) {
            font-size: 15px;
            font-weight: 400;
            color: #00000094;
            padding-bottom: 9px;
            margin: 0;
            font-family: "Kanit", "Courier New", Courier, monospace;

            &:global(.ant-tabs-tab-active) {
              :global(.ant-tabs-tab-btn) {
                color: var(--dark-blue);
              }
            }
          }
          :global(.ant-tabs-nav-list) {
            width: 100%;
            justify-content: space-between;
          }
          &::before {
            border-bottom: $tab-height solid $light-gray;
            border-radius: 20px;
          }
        }
      }
    }
  }
}
// Responsive view
@media screen and (max-width: 768px) {
  .collection {
    padding: 0;

    .text {
      font-size: 11px;
      width: 200px;
    }
    .feedback_button {
      padding: 0rem 1rem;
      font-size: 11px;
    }

    :global(.ant-input-number-input) {
      font-size: 11px;
    }

    :global(.ant-segmented) {
      :global(.ant-segmented-item-label) {
        font-size: 11px !important;
      }
    }

    .date_picker {
      font-size: 12px;
      :global(.ant-picker-input) {
        input {
          font-size: 11px;
        }
      }
    }
  }
}

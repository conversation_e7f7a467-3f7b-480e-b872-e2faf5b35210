import json
import uuid
import logging
import asyncio
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from vertexai.generative_models import GenerationConfig, GenerativeModel, Part, Content
from datetime import datetime
from WebApp.models import ActiveConversation, ConversationHistory, ConversationMessage, UserConversations
from WebApp.views import CampaignChatbot,QueryPrompt
from django.db import connection
import pandas as pd
from google.cloud import storage
import io
import csv
import base64
import traceback
import re

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ChatConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        await self.accept()
        self.user_id = None
        self.conversation_uuid = None
        self.language = "english"
        self.BankMstID=None
        self.designation=None
        self.BranchMstID=None   
        self.model = GenerativeModel("gemini-1.5-flash-002") 
        self.chat = self.model.start_chat()
        self.history = []
        
        self.current_df = None
        self.current_file_name = None
        self.global_mandatory_fields= None
        
        logger.info("WebSocket connection established")

    async def disconnect(self, close_code):
        logger.info(f"WebSocket disconnected with code: {close_code}")
        
        if self.conversation_uuid:
            await self._archive_conversation(self.conversation_uuid)
            

    async def receive(self, text_data):
        # Parse incoming message
        try:
            data = json.loads(text_data)
            message_type = data.get('type', '')
            
            if message_type == 'initialize':
                await self._handle_initialize(data)
            elif message_type == 'message':
                await self._handle_message(data)
            elif message_type == 'reset':
                await self._handle_reset(data)
            elif message_type == 'restore':
                await self._handle_restore(data)
            elif message_type == 'edit_message':
                await self._handle_edit_message(data)
            elif message_type == 'delete_conversation':
                await self._handle_delete_conversation(data)    
            elif message_type == 'file_upload':
                await self._handle_file_upload(data)    
            else:
                await self._send_error("Unknown message type")
                
        except json.JSONDecodeError:
            await self._send_error("Invalid JSON format")
        except Exception as e:
            logger.error(f"Error in WebSocket receive: {e}")
            await self._send_error(f"An unexpected error occurred: {str(e)}")

    async def _handle_initialize(self, data):
        """Initialize conversation and send greeting"""
        try:
            self.user_id = data.get('user_id', '')
            self.conversation_uuid = data.get('conversation_uuid', None)
            self.language = data.get('language', 'english')
            self.BankMstID=data.get('BankMstID',None)
            self.designation=data.get('designation',None)
            self.BranchMstID=data.get('BranchMstID',None)
            #print("bankmstid",self.BankMstID)
            
            if not self.user_id:
                await self._send_error("User ID is required")
                return
            
            await self._delete_empty_conversations(self.user_id)
            
            last_active = await self._get_last_active_conversation()
            if last_active and last_active.conversation_status == 'active':
                old_uuid = str(last_active.uuid)
                logger.info(f"Archiving previous conversation: {old_uuid}")
                
                await self._archive_conversation(old_uuid)
            
            if not self.conversation_uuid:
                conversation = await self._create_conversation()
                self.conversation_uuid = str(conversation.uuid)
                logger.info(f"Created new conversation: {self.conversation_uuid}")
            else:
                conversation_exists = await self._check_conversation_exists(self.conversation_uuid)
                if (conversation_exists):
                    conversation = await self._get_conversation(self.conversation_uuid)
                    logger.info(f"Restored existing conversation: {self.conversation_uuid}")
                else:
                    conversation = await self._create_conversation()
                    self.conversation_uuid = str(conversation.uuid)
                    logger.info(f"Created new conversation (invalid UUID): {self.conversation_uuid}")
            
            await self._associate_conversation_with_user()
            await self._initialize_ai()
            
            greeting = "Hello! I'm SmartCollect, your AI assistant. How may I assist you today?"
            
            await self.send(text_data=json.dumps({
                'type': 'greeting',
                'message': greeting,
                'conversation_uuid': self.conversation_uuid,
                'timestamp': datetime.now().isoformat()
            }))
        except Exception as e:
            logger.error(f"Error in initialize: {e}")
            await self._send_error(f"Failed to initialize: {str(e)}")
            
            
            
    async def _handle_edit_message(self, data):
        """Handle editing a previously sent message"""
        try:
            message_id = data.get('message_id', '')
            new_message = data.get('message', '')
            
            if not message_id or not new_message.strip():
                await self._send_error("Message ID and new message content are required")
                return
            
            message_obj = await self._update_message_text(message_id, new_message)
            user_message_obj=await self._save_message(new_message, None)
            if not message_obj:
                await self._send_error("Message not found or could not be updated")
                return
                
            await self.send(text_data=json.dumps({
                'type': 'message_updated',
                'message_id': message_id,
                'timestamp': datetime.now().isoformat()
            }))
            
            await self._send_typing(True)
            
            await self._initialize_ai()
            
            ai_response = await self._get_ai_response(new_message)
            
            if "QUERY_NEEDED:" in ai_response:
                try:
                    query_desc = ai_response.split("QUERY_NEEDED:")[1].strip()
                    
                    sql_query, error = await self.generate_sql_query(query_desc, self.language)
                    
                    if error or not sql_query:
                        query_result = {
                            "error": error or "Failed to generate a valid SQL query.",
                            "query": None,
                            "data": None
                        }
                    else:
                        result_data, query_error = await self.execute_sql_query(sql_query)
                        ##print("result_data",result_data)
                        if query_error:
                            query_result = {
                                "error": query_error,
                                "query": sql_query,
                                "data": None
                            }
                        else:
                            query_result = {
                                "error": None,
                                "query": sql_query,
                                "data": result_data
                            }
                    
                    # Add information about CSV download if available
                    csv_download_info = ""
                    if result_data and result_data.get("is_large_result") and result_data.get("csv_download_url"):
                        csv_download_info = f"\n\nI've prepared a CSV file with the complete results that you can download for easier analysis."
                    
                    result_prompt = f"""
                    The following query was executed based on the user's question:
                    
                    SQL Query: {sql_query or 'Query generation failed'}
                    
                    Results:
                    {json.dumps(query_result, indent=2)}
                    {csv_download_info}
                    
                    Instructions:
                    1. Just provide a clear, concise answer to the user's question without trying to list all rows.
                    2. DO NOT suggest filtering further - the user can see all results in the data view.
                    3. DO NOT ask the user how you can help them next or list any options.
                    4. Avoid ending with questions about what the user wants to do next.
                    5. If the query failed, tell that i am not able to fetch the data currently. Please try again later.
                    6. DO NOT include any technical details about the database schema or query execution or table.
                    
                    IMPORTANT CONDITIONS STRICTLY FOLLOW THIS:
                    1. DO NOT mention BankMstID and BranchMstID in response to any query strictly follow this.
                    2. DO NOT include the sql query in any response. (Strictly follow this) 
                    3. DO NOT include the database name or table names in the response.
                    """
                   
                    ai_response = await self._get_ai_follow_up_response(result_prompt)
                
                    # Update the AI response in the database
                    await self._update_ai_response(user_message_obj, ai_response)
                    
                    await self._send_typing(False)
                    await self.send(text_data=json.dumps({
                        'type': 'ai_response_updated',
                        'message_id': message_id,
                        'response': ai_response,
                        'sql_data': result_data,  
                        'csv_download_url': result_data.get('csv_download_url') if result_data and result_data.get('is_large_result') else None,
                        'timestamp': datetime.now().isoformat()
                    }))
                    
                    return
                    
                except Exception as e:
                    logger.error(f"Error handling database query: {e}")
                    import traceback
                    logger.error(traceback.format_exc())
                    ai_response = "I'm sorry, I encountered an error while trying to query the database. " + \
                                  f"Error details: {str(e)}. Would you like to try asking your question differently?"
            
            
            await self._update_ai_response(message_obj, ai_response)
            
            await self._send_typing(False)
            await self.send(text_data=json.dumps({
                'type': 'ai_response_updated',
                'message_id': message_id,
                'response': ai_response,
                'timestamp': datetime.now().isoformat()
            }))
            
        except Exception as e:
            logger.error(f"Error handling message edit: {e}")
            # logger.error(traceback.format_exc())
            await self._send_typing(False)
            await self._send_error(f"Failed to update message: {str(e)}")  
            
            
            
            
            
    @database_sync_to_async
    def _update_message_text(self, message_id, new_text):
        """Update the text of a user message"""
        try:
            if not self.conversation_uuid:
                return None
                
            conversation = ActiveConversation.objects.get(uuid=self.conversation_uuid)
            
            messages = ConversationMessage.objects.filter(conversation=conversation)
            
            message = None
            try:
                message = messages.get(id=message_id)
            except (ValueError, ConversationMessage.DoesNotExist):
                for msg in messages:
                    if str(msg.id) == str(message_id):
                        message = msg
                        break
                
                if not message:
                    message = messages.order_by('-created_at').first()
                    logger.info(f"Using fallback approach: selected most recent message id={message.id}")
            
            if not message:
                logger.error(f"Message with ID {message_id} not found for conversation {self.conversation_uuid}")
                return None
                
            message.user_message = new_text
            message.is_edited = True
            message.save()
            logger.info(f"Successfully updated message id={message.id} with new text")
            
            return message
        except (ActiveConversation.DoesNotExist, ConversationMessage.DoesNotExist):
            logger.error(f"Message {message_id} not found for conversation {self.conversation_uuid}")
            return None
        except Exception as e:
            logger.error(f"Error updating message: {e}")
            return None

    @database_sync_to_async
    def _update_ai_response(self, message_obj, ai_response):
        """Update AI response for a message"""
        try:
            if message_obj:
                message_obj.ai_response = ai_response
                message_obj.save()
                return True
            return False
        except Exception as e:
            logger.error(f"Error updating AI response: {e}")
            return False
    
    @database_sync_to_async
    def _delete_empty_conversations(self, user_id):
        """Delete any empty conversations for this user"""
        try:
            # First check active conversations
            empty_active = ActiveConversation.objects.filter(user_id=user_id, title="Empty Conversation")
            for conv in empty_active:
                # Check if it has any messages
                message_count = ConversationMessage.objects.filter(conversation=conv).count()
                if message_count == 0:
                    logger.info(f"Deleting empty active conversation: {conv.uuid}")
                    
                    # Remove from user's conversations if exists
                    try:
                        user_convs = UserConversations.objects.get(user_id=user_id)
                        if user_convs.last_active_conversation and str(user_convs.last_active_conversation.uuid) == str(conv.uuid):
                            user_convs.last_active_conversation = None
                        
                        user_convs.active_conversations.remove(conv)
                        user_convs.save()
                    except (UserConversations.DoesNotExist, Exception) as e:
                        logger.error(f"Error removing from user conversations: {e}")
                    
                    # Delete the conversation
                    conv.delete()
            
            # Then check archived conversations
            empty_history = ConversationHistory.objects.filter(user_id=user_id, title="Empty Conversation")
            for conv in empty_history:
                # For archived conversations, check if the full_conversation is empty or contains no real interaction
                has_content = False
                try:
                    full_conv = json.loads(conv.full_conversation)
                    if full_conv and len(full_conv) > 0:
                        for msg in full_conv:
                            if msg.get("user_message") and msg.get("ai_response"):
                                has_content = True
                                break
                except:
                    pass
                
                if not has_content:
                    logger.info(f"Deleting empty archived conversation: {conv.uuid}")
                    
                    # Remove from user's conversations if exists
                    try:
                        user_convs = UserConversations.objects.get(user_id=user_id)
                        user_convs.completed_conversations.remove(conv)
                        user_convs.save()
                    except (UserConversations.DoesNotExist, Exception) as e:
                        logger.error(f"Error removing from user conversations: {e}")
                    
                    # Delete the conversation
                    conv.delete()
            
            return True
        except Exception as e:
            logger.error(f"Error deleting empty conversations: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False
    
    
        
        
    async def _handle_delete_conversation(self, data):
        """Delete a specific conversation from history"""
        try:
            conversation_uuid = data.get('conversation_uuid')
            
            if not conversation_uuid:
                await self._send_error("Conversation UUID is required for deletion")
                return
                
            success = await self._delete_conversation(conversation_uuid)
            
            if success:
                await self.send(text_data=json.dumps({
                    'type': 'delete_confirmation',
                    'conversation_uuid': conversation_uuid,
                    'message': 'Conversation deleted successfully',
                    'timestamp': datetime.now().isoformat()
                }))
            else:
                await self._send_error("Failed to delete conversation. It may have been already deleted or doesn't exist.")
                
        except Exception as e:
            logger.error(f"Error deleting conversation: {e}")
            await self._send_error(f"Failed to delete conversation: {str(e)}")  
            
            
            
    @database_sync_to_async
    def _delete_conversation(self, conversation_uuid):
        """Delete a conversation from history"""
        try:
            if ConversationHistory.objects.filter(uuid=conversation_uuid).exists():
                conversation = ConversationHistory.objects.get(uuid=conversation_uuid)
                
                user_convs = UserConversations.objects.get(user_id=conversation.user_id)
                
                user_convs.completed_conversations.remove(conversation)
                user_convs.save()
                
                conversation.delete()
                
                logger.info(f"Deleted conversation: {conversation_uuid}")
                return True
                
            elif ActiveConversation.objects.filter(uuid=conversation_uuid).exists():
                conversation = ActiveConversation.objects.get(uuid=conversation_uuid)
                
                try:
                    user_convs = UserConversations.objects.get(user_id=conversation.user_id)
                    if user_convs.last_active_conversation and str(user_convs.last_active_conversation.uuid) == conversation_uuid:
                        user_convs.last_active_conversation = None
                    
                    user_convs.active_conversations.remove(conversation)
                    user_convs.save()
                except UserConversations.DoesNotExist:
                    pass
                
                ConversationMessage.objects.filter(conversation=conversation).delete()
                
                conversation.delete()
                
                logger.info(f"Deleted active conversation: {conversation_uuid}")
                return True
                
            return False
        except Exception as e:
            logger.error(f"Error in _delete_conversation: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False          
                                  
            
    async def generate_sql_query(self, question, language="english"):
        """Generate SQL query from natural language question"""
        try:
            system_prompt = await self._get_sql_prompt()
            
            sql_prompt = f"""
            I need you to switch to SQL generation mode temporarily.
            
            {system_prompt}
            
            Based on the conversation context so far, please generate a SQL query for this question: {question}
            
            Return ONLY the SQL query without any explanation or additional text.
            """
            
            self.history.append(Content(
                role="user",
                parts=[Part.from_text(sql_prompt)]
            ))
            
            response = await asyncio.to_thread(
                self.chat.send_message,
                Part.from_text(sql_prompt),
                generation_config=GenerationConfig(
                    temperature=0.1,  
                    top_p=0.95,
                    top_k=40,
                    max_output_tokens=1024,
                )
            )
            
            self.history.append(Content(
                role="model",
                parts=[Part.from_text(response.text)]
            ))
            
            sql_query = response.text.strip()
            sql_query = sql_query.replace(";", "").strip()
            
            if sql_query.startswith("```sql"):
                sql_query = sql_query.replace("```sql", "").replace("```", "").strip()
            elif sql_query.startswith("```"):
                sql_query = sql_query.replace("```", "").strip()
            
            if any(keyword in sql_query.lower() for keyword in ["insert", "update", "delete", "drop", "alter", "create", "grant"]):
                return None, "Query contains disallowed operations."
                
            # Tell the model to go back to normal mode
            
            reset_prompt = "Thank you for generating the SQL query. Now let's go back to our normal conversation mode." # Look into this
            
            self.history.append(Content(
                role="user",
                parts=[Part.from_text(reset_prompt)]
            ))
            
            reset_response = await asyncio.to_thread(
                self.chat.send_message,
                Part.from_text(reset_prompt),
                generation_config=GenerationConfig(
                    temperature=0.2,
                    top_p=0.95,
                    top_k=40,
                    max_output_tokens=128,
                )
            )
            
            self.history.append(Content(
                role="model",
                parts=[Part.from_text(reset_response.text)]
            ))
            
            return sql_query, None
            
        except Exception as e:
            logger.error(f"Error generating SQL query: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return None, f"Failed to generate SQL query: {str(e)}"

    def _execute_sql_query_sync(self, sql_query):
        """Execute SQL query synchronously and return results"""
        try:
            logger.info(f"Executing SQL query: {sql_query}")
            
            with connection.cursor() as cursor:
                cursor.execute(sql_query)
                
                columns = [col[0] for col in cursor.description]
                
                results = cursor.fetchall()
                
                rows = []
                for row in results:
                    row_dict = {}
                    for i, col in enumerate(columns):
                        if isinstance(row[i], (int, float, str, bool, type(None))):
                            row_dict[col] = row[i]
                        else:
                            row_dict[col] = str(row[i])
                    rows.append(row_dict)
                    
                return {
                    "success": True,
                    "columns": columns,
                    "results": rows
                }
                
        except Exception as e:
            logger.error(f"Error executing SQL query: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {
                "success": False,
                "error": str(e)
            }

    async def execute_sql_query(self, sql_query):
        """Async wrapper for executing SQL query with CSV export for large result sets"""
        result = await database_sync_to_async(self._execute_sql_query_sync)(sql_query)
        
        if not result.get("success", False):
            return None, result.get("error", "Unknown database error")
        
        try:
            columns = result["columns"]
            rows = result["results"]
            row_count = len(rows)
            #print(row_count)
            
            LARGE_RESULT_THRESHOLD = 5
            is_large_result = row_count > LARGE_RESULT_THRESHOLD
            
            result_stats = {
                "row_count": row_count,
                "columns": columns,
                "results": rows[:LARGE_RESULT_THRESHOLD], 
                "is_large_result": is_large_result
            }
            
            if is_large_result:
                file_name = f"query_results_{self.user_id}_{uuid.uuid4()}.csv"
                try:
                    csv_download_url = await self._generate_and_upload_csv(columns, rows, file_name)
                    result_stats["csv_download_url"] = csv_download_url
                except Exception as e:
                    logger.error(f"Failed to generate CSV file: {e}")
                    import traceback
                    logger.error(traceback.format_exc())
            
            if len(rows) > 0:
                analysis_rows = rows[:min(row_count, 100000)]  
                df = pd.DataFrame(analysis_rows)
                result_stats["summary"] = {}
                
                for col in df.columns:
                    is_numeric = pd.api.types.is_numeric_dtype(df[col])
                    result_stats["summary"][col] = {
                        "type": "numeric" if is_numeric else "text",
                        "unique_values": min(len(df[col].unique()), 100),  
                    }
                    
                    unique_values = df[col].dropna().unique()[:5]
                    result_stats["summary"][col]["examples"] = [
                        str(val) if not isinstance(val, (int, float, bool, type(None))) else val 
                        for val in unique_values
                    ]
                    
            return result_stats, None
            
        except Exception as e:
            logger.error(f"Error processing SQL results: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return None, f"Error processing query results: {str(e)}"

    async def _generate_and_upload_csv(self, columns, rows, file_name):
        """Generate CSV and upload to GCP Cloud Storage"""
        try:
            csv_buffer = io.StringIO()
            csv_writer = csv.writer(csv_buffer)
            
            csv_writer.writerow(columns)
            
            for row in rows:
                row_values = [row[col] for col in columns]
                csv_writer.writerow(row_values)
            
            bucket_name = "newrecordingsvoicebot"  
            blob_name = f"query_exports/{file_name}"
            
            csv_content = csv_buffer.getvalue().encode('utf-8')
            
            # Upload to GCS
            signed_url = await database_sync_to_async(self._upload_to_gcs)(
                bucket_name, blob_name, csv_content
            )
            
            return signed_url
        except Exception as e:
            logger.error(f"Error generating and uploading CSV: {e}")
            import traceback
            logger.error(traceback.format_exc())
            raise
        
    

    def _upload_to_gcs(self, bucket_name, blob_name, content):
        """Upload content to GCP Cloud Storage and return a signed URL"""
        try:
            storage_client = storage.Client()
            
            try:
                bucket = storage_client.get_bucket(bucket_name)
            except Exception:
                bucket = storage_client.create_bucket(bucket_name)
            
            blob = bucket.blob(blob_name)
            blob.upload_from_string(content, content_type="text/csv")
            
            from datetime import timedelta
            expiration = timedelta(days=7)
            signed_url = blob.generate_signed_url(
                version="v4",
                expiration=expiration,
                method="GET"
            )
            
            return signed_url
        except Exception as e:
            logger.error(f"Error uploading to GCS: {e}")
            import traceback
            logger.error(traceback.format_exc())
            raise

   
    async def _handle_message(self, data):
        """Process user message and get AI response"""
        try:
            user_message = data.get('message', '')
            
            if not user_message.strip():
                await self._send_error("Message cannot be empty")
                return
                
            await self._send_typing(True)
            
            user_message_obj = await self._save_message(user_message, None)
            
            ai_response = await self._get_ai_response(user_message)
            
            # Check if this is a database query request
            print("ai_response",ai_response)
            if "QUERY_NEEDED:" in ai_response:
                try:
                    # Your existing database query handling code
                    query_desc = ai_response.split("QUERY_NEEDED:")[1].strip()
                    
                    sql_query, error = await self.generate_sql_query(query_desc, self.language)
                    
                    # Rest of your existing code for SQL query handling
                    if error or not sql_query:
                        query_result = {
                            "error": error or "Failed to generate a valid SQL query.",
                            "query": None,
                            "data": None
                        }
                    else:
                        result_data, query_error = await self.execute_sql_query(sql_query)
                        if query_error:
                            query_result = {
                                "error": query_error,
                                "query": sql_query,
                                "data": None
                            }
                        else:
                            query_result = {
                                "error": None,
                                "query": sql_query,
                                "data": result_data
                            }
                    
                    # Add information about CSV download if available
                    csv_download_info = ""
                    if result_data and result_data.get("is_large_result") and result_data.get("csv_download_url"):
                        csv_download_info = f"\n\nI've prepared a CSV file with the complete results that you can download for easier analysis."
                    
                    result_prompt = f"""
                    The following query was executed based on the user's question:
                    
                    SQL Query: {sql_query or 'Query generation failed'}
                    
                    Results:
                    {json.dumps(query_result, indent=2)}
                    {csv_download_info}
                    
                    Instructions:
                    1. Just provide a clear, concise answer to the user's question without trying to list all rows.
                    2. DO NOT suggest filtering further - the user can see all results in the data view.
                    3. DO NOT ask the user how you can help them next or list any options.
                    4. Avoid ending with questions about what the user wants to do next.
                    5. If the query failed, tell that i am not able to fetch the data currently. Please try again later.
                    6. DO NOT include any technical details about the database schema or query execution or table.
                    
                    IMPORTANT CONDITIONS STRICTLY FOLLOW THIS:
                    1. DO NOT mention BankMstID and BranchMstID in response to any query strictly follow this.
                    2. DO NOT include the sql query in any response. (Strictly follow this) 
                    3. DO NOT include the database name or table names in the response.
                    """
                    
                    ai_response_with_results = await self._get_ai_follow_up_response(result_prompt)
                    
                    ai_response = ai_response_with_results
                    
                    url_link = result_data.get('csv_download_url') if result_data and result_data.get('is_large_result') else None
                    print("url_link",url_link)
                    await self._update_message_response(user_message_obj, ai_response)
                    await self._send_typing(False)
                    await self.send(text_data=json.dumps({
                        'type': 'response',
                        'message': ai_response,
                        'sql_data': result_data,
                        'csv_download_url': url_link,
                        'conversation_uuid': self.conversation_uuid,
                        'timestamp': datetime.now().isoformat()
                    }))
                    
                    return
                    
                except Exception as e:
                    logger.error(f"Error handling database query: {e}")
                    logger.error(traceback.format_exc())
                    ai_response = "I'm sorry, I encountered an error while trying to query the database. " + \
                                  f"Error details: {str(e)}. Would you like to try asking your question differently?"

            # Check if this is a file analysis request
            elif "FILE_ANALYSIS_NEEDED:" in ai_response and hasattr(self, 'current_df') and self.current_df is not None:
                try:
                    analysis_request = ai_response.split("FILE_ANALYSIS_NEEDED:")[1].strip()
                    
                    # Process the file analysis request - now returns either response or (response, url)
                    file_analysis_result = await self._analyze_file_data(analysis_request)
                    
                    # Check if the result includes a download URL
                    if isinstance(file_analysis_result, tuple) and len(file_analysis_result) == 2:
                        file_analysis_response, csv_download_url = file_analysis_result
                        ai_response = file_analysis_response
                        
                        # Update the message response
                        await self._update_message_response(user_message_obj, ai_response)
                        
                        # Send response with CSV download URL
                        await self._send_typing(False)
                        await self.send(text_data=json.dumps({
                            'type': 'response',
                            'message': ai_response,
                            'csv_download_url': csv_download_url,
                            'conversation_uuid': self.conversation_uuid,
                            'timestamp': datetime.now().isoformat()
                        }))
                        return
                    else:
                        # Just a normal response without download URL
                        ai_response = file_analysis_result
                except Exception as e:
                    logger.error(f"Error handling file analysis: {e}")
                    logger.error(traceback.format_exc())
                    ai_response = "I'm sorry, I encountered an error while analyzing the file. " + \
                                  f"Error details: {str(e)}. Would you like to try asking your question differently?"
            
            # Handle case where file analysis is requested but no file is uploaded
            elif "FILE_ANALYSIS_NEEDED:" in ai_response and (not hasattr(self, 'current_df') or self.current_df is None):
                ai_response = "I don't see any uploaded file to analyze. Please upload a file first before asking file-related questions."
            
            await self._update_message_response(user_message_obj, ai_response)
            
        
            await self._send_typing(False)
            await self.send(text_data=json.dumps({
                'type': 'response',
                'message': ai_response,
                'conversation_uuid': self.conversation_uuid,
                'timestamp': datetime.now().isoformat()
            }))
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            await self._send_typing(False)
            await self._send_error(f"Failed to process message: {str(e)}")

    async def _handle_reset(self, data):
        """Reset conversation state with a new UUID"""
        try:
            old_uuid = self.conversation_uuid
            
            self.user_id = data.get('user_id', self.user_id)
            self.language = data.get('language', self.language)
            
            conversation = await self._create_conversation()
            self.conversation_uuid = str(conversation.uuid)
            
            if old_uuid:
                archived = await self._archive_conversation(old_uuid)
                await self._abandon_conversation(old_uuid)
                
                if archived:
                    await self._update_archived_conversation_title(old_uuid)
            
            await self._initialize_ai()
            
            await self._associate_conversation_with_user()
            
            await self.send(text_data=json.dumps({
                'type': 'reset_confirmation',
                'conversation_uuid': self.conversation_uuid,
                'message': "Conversation has been reset.",
                'timestamp': datetime.now().isoformat()
            }))
            
            greeting = "Hello! I'm SmartCollect, your AI assistant. How may I assist you today?"
            
            await self.send(text_data=json.dumps({
                'type': 'greeting',
                'message': greeting,
                'conversation_uuid': self.conversation_uuid,
                'timestamp': datetime.now().isoformat()
            }))
            
        except Exception as e:
            logger.error(f"Error resetting conversation: {e}")
            await self._send_error(f"Failed to reset conversation: {str(e)}")

    async def _handle_restore(self, data):
        """Restore a previous conversation by UUID"""
        try:
            restore_uuid = data.get('conversation_uuid', None)
            if not restore_uuid:
                await self._send_error("Conversation UUID is required for restoration")
                return
                
            conversation_exists = await self._check_conversation_exists(restore_uuid)
            if not conversation_exists:
                await self._send_error("Conversation not found or no longer active")
                return
            old_uuid = self.conversation_uuid
            
            # Generate title for previous conversation before switching
            if old_uuid and old_uuid != restore_uuid:
                logger.info(f"Archiving previous conversation {old_uuid} before switching to {restore_uuid}")
                await self._archive_conversation(old_uuid)
                    
            self.conversation_uuid = restore_uuid
            
            # await self._archive_conversation(restore_uuid)
            await self._initialize_ai()
            
            last_messages = await self._get_last_messages(50) 
            
            await self.send(text_data=json.dumps({
                'type': 'restore_confirmation',
                'conversation_uuid': self.conversation_uuid,
                'message': "Welcome Back.How can i help you today?",
                'history': last_messages,
                'timestamp': datetime.now().isoformat()
            }))
            
        except Exception as e:
            logger.error(f"Error restoring conversation: {e}")
            await self._send_error(f"Failed to restore conversation: {str(e)}")
            
            
            
    async def _generate_conversation_title(self):
        """Generate a descriptive title for the conversation"""
        try:
            conversation_history = await self._get_last_messages(500)
            
            if not conversation_history or len(conversation_history) == 0:
                return "Empty Conversation"
            
            title_prompt = "Based on this conversation, generate a short, descriptive title (max 5-7 words) that captures the main topic or purpose. The title should be concise and summarize what the conversation was about. Only respond with the title, nothing else.\n\n"
            #print(title_prompt)
            for message in conversation_history:
                title_prompt += f"User: {message['user_message']}\n"
                title_prompt += f"AI: {message['ai_response']}\n"
            
            
            
            response = await asyncio.to_thread(
                self.chat.send_message,
                Part.from_text(title_prompt),
                generation_config=GenerationConfig(
                    temperature=0.2,
                    top_p=0.95,
                    top_k=40,
                    max_output_tokens=128,  
                )
            )
            
            title = response.text.strip()
            title = title.replace('"', '').replace("'", "")
            title = " ".join(title.split())  
            
                
            return title
            
        except Exception as e:
            logger.error(f"Error generating conversation title: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return "Conversation"         

    async def _initialize_ai(self):
        """Initialize the AI model with system prompt and conversation history"""
        system_prompt = await self._get_system_prompt()
        #print("system_prompt",system_prompt)    
        self.history = [
            Content(
                role="user",
                parts=[Part.from_text(system_prompt)]
            ),
            Content(
                role="model",
                parts=[Part.from_text("I understand my role as SmartCollect, an AI assistant focused on helping users to answer there,questions related to loan, customer, account summary , response , communication queue data across the database.")]
            )
        ]
        
        if self.conversation_uuid:
            conversation_history = await self._get_conversation_history()
            
            if conversation_history:
                for message in conversation_history:
                    self.history.append(Content(
                        role="user",
                        parts=[Part.from_text(message["user_message"])]
                    ))
                    
                    self.history.append(Content(
                        role="model",
                        parts=[Part.from_text(message["ai_response"])]
                    ))
        
        
        self.chat = self.model.start_chat(history=self.history)

    async def _get_ai_response(self, user_message):
        """Get response from AI model with file context if available"""
        try:
            # Add file context if available
            if hasattr(self, 'current_df') and self.current_df is not None:
                file_name = getattr(self, 'current_file_name', 'uploaded file')
                file_context = f"\nContext: The user has previously uploaded a file named '{file_name}' with {len(self.current_df)} rows and {len(self.current_df.columns)} columns: {list(self.current_df.columns)}."
                # Don't modify the original user message, but add context in history
                context_message = user_message + file_context
                
                # Add user message with context to history
                self.history.append(Content(
                    role="user",
                    parts=[Part.from_text(context_message)]
                ))
            else:
                # Add original user message to history
                self.history.append(Content(
                    role="user",
                    parts=[Part.from_text(user_message)]
                ))
            
            response = await asyncio.to_thread(
                self.chat.send_message,
                Part.from_text(user_message),  # Use original message for the actual request
                generation_config=GenerationConfig(
                    temperature=0.2,
                    top_p=0.95,
                    top_k=40,
                    max_output_tokens=1024,
                )
            )
            
            # Add AI response to history for tracking
            self.history.append(Content(
                role="model",
                parts=[Part.from_text(response.text)]
            ))
            
            return response.text
        except Exception as e:
            logger.error(f"Error getting AI response: {e}")
            error_message = "I'm sorry, I'm having trouble processing your request. Please try again."
            
            # Add error response to history for tracking
            self.history.append(Content(
                role="model",
                parts=[Part.from_text(error_message)]
            ))
            
            return error_message

    async def _get_ai_follow_up_response(self, follow_up_prompt):
        """Get a follow-up response from AI model"""
        try:
            # Add follow-up prompt to history
            self.history.append(Content(
                role="user",
                parts=[Part.from_text(follow_up_prompt)]
            ))
            
            response = await asyncio.to_thread(
                self.chat.send_message,
                Part.from_text(follow_up_prompt),
                generation_config=GenerationConfig(
                    temperature=0.2,
                    top_p=0.95,
                    top_k=40,
                    max_output_tokens=5024,
                )
            )
            
            # Add AI response to history
            self.history.append(Content(
                role="model",
                parts=[Part.from_text(response.text)]
            ))
            
            return response.text
        except Exception as e:
            logger.error(f"Error getting AI follow-up response: {e}")
            error_message = "I'm sorry, I'm having trouble analyzing the data. Please try a different question or approach."
            
            # Add error response to history
            self.history.append(Content(
                role="model",
                parts=[Part.from_text(error_message)]
            ))
            
            return error_message

    async def _send_error(self, message):
        """Send error message to client"""
        await self.send(text_data=json.dumps({
            'type': 'error',
            'message': message,
            'timestamp': datetime.now().isoformat()
        }))
        
    async def _send_typing(self, is_typing):
        """Send typing indicator to client"""
        await self.send(text_data=json.dumps({
            'type': 'typing',
            'is_typing': is_typing
        }))

    @database_sync_to_async
    def _get_system_prompt(self):
        """Get system prompt for AI"""
        logger.info(f"bankmstid in system {self.BankMstID}")
        logger.info(f"designation in system {self.designation}")
        logger.info(f"branchmstid {self.BranchMstID}")
        

        return QueryPrompt.get_system_prompt(self.language, self.user_id,self.BankMstID,self.designation,self.BranchMstID)
    
    @database_sync_to_async
    def _get_sql_prompt(self):
        """Get SQL prompt for AI"""
        logger.info(f"bankmstid in sql {self.BankMstID}")
        logger.info(f"designation in sql {self.designation}")
        logger.info(f"branchmstid {self.BranchMstID}")
        
        
        return QueryPrompt.get_sql_prompt(self.language,self.BankMstID,self.designation,self.BranchMstID)

    @database_sync_to_async
    def _create_conversation(self):
        """Create a new conversation with a UUID"""
        return ActiveConversation.objects.create(
            user_id=self.user_id,
            conversation_status='active',
            form_data=json.dumps({})
        )

    @database_sync_to_async
    def _check_conversation_exists(self, conversation_uuid):
        """Check if conversation exists"""
        try:
            return ActiveConversation.objects.filter(uuid=conversation_uuid).exists()
        except:
            return False

    @database_sync_to_async
    def _get_conversation(self, conversation_uuid):
        """Get conversation by UUID"""
        return ActiveConversation.objects.get(uuid=conversation_uuid)

    @database_sync_to_async
    def _save_message(self, user_message, ai_response):
        """Save conversation message"""
        if not self.conversation_uuid:
            return None
            
        conversation = ActiveConversation.objects.get(uuid=self.conversation_uuid)
        
        conversation.last_updated = datetime.now()
        conversation.save()
        
        return ConversationMessage.objects.create(
            conversation=conversation,
            user_message=user_message,
            ai_response=ai_response or ""
        )
        
    @database_sync_to_async
    def _update_message_response(self, message_obj, ai_response):
        """Update message with AI response"""
        if message_obj:
            message_obj.ai_response = ai_response
            message_obj.save()

    @database_sync_to_async
    def _get_conversation_history(self):
        """Get conversation history"""
        if not self.conversation_uuid:
            return []
            
        conversation = ActiveConversation.objects.get(uuid=self.conversation_uuid)
        messages = ConversationMessage.objects.filter(conversation=conversation).order_by('created_at')
        
        return [
            {
                "user_message": message.user_message,
                "ai_response": message.ai_response
            }
            for message in messages if message.ai_response 
        ]
    
    @database_sync_to_async
    def _get_last_messages(self, count=100):
        """Get last few messages for context"""
        if not self.conversation_uuid:
            return []
            
        conversation = ActiveConversation.objects.get(uuid=self.conversation_uuid)
        messages = ConversationMessage.objects.filter(
            conversation=conversation
        ).order_by('-created_at')[:count]
        
        return [
            {
                "user_message": message.user_message,
                "ai_response": message.ai_response,
                "timestamp": message.created_at.isoformat()
            }
            for message in reversed(messages) if message.ai_response  
        ]

    @database_sync_to_async
    def _get_conversation_length(self):
        """Get the number of messages in the conversation"""
        if not self.conversation_uuid:
            return 0
            
        conversation = ActiveConversation.objects.get(uuid=self.conversation_uuid)
        return ConversationMessage.objects.filter(conversation=conversation).count()

    @database_sync_to_async
    def _complete_conversation(self):
        """Mark conversation as completed"""
        if not self.conversation_uuid:
            return
            
        conversation = ActiveConversation.objects.get(uuid=self.conversation_uuid)
        conversation.conversation_status = 'completed'
        conversation.save()
        
        if self.user_id:
            self._complete_conversation_for_user()

    @database_sync_to_async
    def _abandon_conversation(self, conversation_uuid=None):
        """Mark conversation as abandoned"""
        uuid_to_use = conversation_uuid or self.conversation_uuid
        if not uuid_to_use:
            return
            
        conversation = ActiveConversation.objects.get(uuid=uuid_to_use)
        conversation.conversation_status = 'abandoned'
        conversation.save()
        
        
        
    @database_sync_to_async
    def _do_archive_conversation(self, conversation_uuid, title):
        """Perform the actual archiving operation"""
        try:
            # Check if already archived
            if ConversationHistory.objects.filter(uuid=conversation_uuid).exists():
                return ConversationHistory.objects.get(uuid=conversation_uuid)
                
            conversation = ActiveConversation.objects.get(uuid=conversation_uuid)
            
            messages = ConversationMessage.objects.filter(
                conversation=conversation
            ).order_by('created_at')
            
            if not messages.exists():
                return None
                
            conversation_data = []
            for message in messages:
                conversation_data.append({
                    "user_message": message.user_message,
                    "ai_response": message.ai_response,
                    "timestamp": message.created_at.isoformat()
                })
            
            history = ConversationHistory.objects.create(
                uuid=conversation.uuid,
                user_id=conversation.user_id,
                created_at=conversation.created_at,
                form_data=conversation.form_data,
                full_conversation=json.dumps(conversation_data),
                title=title  # Save the AI-generated title here
            )
            
            user_convs, _ = UserConversations.objects.get_or_create(user_id=conversation.user_id)
            user_convs.active_conversations.remove(conversation)  # Remove from active
            user_convs.completed_conversations.add(history)       # Add to completed
            user_convs.save()
            
            return history
                
        except Exception as e:
            logger.error(f"Error in _do_archive_conversation: {e}")
            return None




    async def _archive_conversation(self, conversation_uuid=None):
        try:
            uuid_to_use = conversation_uuid or self.conversation_uuid
            logger.info(f"Archiving conversation: {uuid_to_use}")
            
            if not uuid_to_use:
                return
                
            
            old_uuid = self.conversation_uuid
            self.conversation_uuid = uuid_to_use
            
            try:
                title = await self._generate_conversation_title()
                logger.info(f"Generated title for conversation {uuid_to_use}: {title}")
                
                
                await self._update_active_conversation_title(uuid_to_use, title)
            except Exception as e:
                logger.error(f"Failed to generate title: {e}")
                title = "Conversation"
            finally:
                
                self.conversation_uuid = old_uuid
            
            
            history =  self._do_archive_conversation(uuid_to_use, title)
            return history
                
        except Exception as e:
            logger.error(f"Error in _archive_conversation: {e}")
            return None

    @database_sync_to_async
    def _update_active_conversation_title(self, conversation_uuid, title):
        try:
            conversation = ActiveConversation.objects.get(uuid=conversation_uuid)
            conversation.title = title
            conversation.save()
            logger.info(f"Updated title in ActiveConversation: {title}")
            return True
        except Exception as e:
            logger.error(f"Failed to update title in ActiveConversation: {e}")
            return False

    

    async def _update_archived_conversation_title(self, conversation_uuid):
        """Generate and update title for an archived conversation"""
        try:
            
            old_uuid = self.conversation_uuid
            self.conversation_uuid = conversation_uuid
            
            
            title = await self._generate_conversation_title()
            
            
            await self._update_conversation_title_in_db(conversation_uuid, title)
            
            
            self.conversation_uuid = old_uuid
            
            return title
        except Exception as e:
            logger.error(f"Error updating archived conversation title: {e}")
            return "Conversation"

    @database_sync_to_async
    def _update_conversation_title_in_db(self, conversation_uuid, title):
        """Update conversation title in database"""
        try:
            history = ActiveConversation.objects.get(uuid=conversation_uuid)
            history.title = title
            history.save()
            logger.info(f"Updated title for conversation {conversation_uuid}: {title}")
            return True
        except ActiveConversation.DoesNotExist:
            logger.error(f"Cannot update title: Conversation {conversation_uuid} not found")
            return False
        except Exception as e:
            logger.error(f"Error updating conversation title in DB: {e}")
            return False

    

    @database_sync_to_async
    def _associate_conversation_with_user(self):
        """Associate this conversation with the user"""
        if not self.user_id or not self.conversation_uuid:
            return
            
        
        user_convs, created = UserConversations.objects.get_or_create(user_id=self.user_id)
        
        
        conversation = ActiveConversation.objects.get(uuid=self.conversation_uuid)
        
        
        user_convs.active_conversations.add(conversation)
        
       
        user_convs.last_active_conversation = conversation
        user_convs.save()

    @database_sync_to_async
    def _complete_conversation_for_user(self):
        """Mark conversation as completed for this user"""
        if not self.user_id or not self.conversation_uuid:
            return
            
        try:
            user_convs = UserConversations.objects.get(user_id=self.user_id)
            
            conversation = ActiveConversation.objects.get(uuid=self.conversation_uuid)
            user_convs.active_conversations.remove(conversation)
            
            if user_convs.last_active_conversation and user_convs.last_active_conversation.uuid == self.conversation_uuid:
                user_convs.last_active_conversation = None
                
            try:
                history = ConversationHistory.objects.get(uuid=self.conversation_uuid)
                user_convs.completed_conversations.add(history)
            except ConversationHistory.DoesNotExist:
                pass
                
            user_convs.save()
        except UserConversations.DoesNotExist:
            pass

    @database_sync_to_async
    def _get_user_conversations(self):
        """Get all conversations for this user"""
        if not self.user_id:
            return [], []
            
        try:
            user_convs = UserConversations.objects.get(user_id=self.user_id)
            
            active = list(user_convs.active_conversations.all())
            
            completed = list(user_convs.completed_conversations.all())
            
            return active, completed
        except UserConversations.DoesNotExist:
            return [], []

    @database_sync_to_async
    def _get_last_active_conversation(self):
        """Get the user's last active conversation"""
        if not self.user_id:
            return None
            
        try:
            user_convs = UserConversations.objects.get(user_id=self.user_id)
            return user_convs.last_active_conversation
        except UserConversations.DoesNotExist:
            return None

    async def _handle_file_upload(self, data):
        """Process uploaded file and store it for analysis"""
        try:
            file_content = data.get('file_content', '')
            file_name = data.get('file_name', '')
            file_type = data.get('file_type', '')
            
            if not file_content:
                await self._send_error("File content is required")
                return
                
            await self._send_typing(True)
            
            # Decode base64 encoded file content
            try:
                file_bytes = base64.b64decode(file_content.split(',')[1] if ',' in file_content else file_content)
            except Exception as e:
                logger.error(f"Error decoding file content: {e}")
                logger.error(traceback.format_exc())
                await self._send_error("Invalid file content")
                return
                
            # Process the file
            df, error_message = await self._process_file(file_bytes, file_name)
            
            if df is None:
                await self._send_typing(False)
                await self._send_error(f"Failed to process file: {error_message}")
                return
                
            # Check if we have pending requests for additional files
            pending_disbursement_request = False
            if hasattr(self, 'collected_files') and self.collected_files:
                # We already have some files collected, this might be a follow-up upload
                pending_disbursement_request = True
            
            # Save the dataframe in the session
            self.current_df = df
            self.current_file_name = file_name
            
            # Generate a basic file summary
            file_summary = await self._generate_file_summary(df, file_name)
            
            # Save the message about file upload
            user_message = f"I've uploaded a file named '{file_name}' for analysis."
            user_message_obj = await self._save_message(user_message, None)
            
            # Update AI response
            await self._update_message_response(user_message_obj, file_summary)
            
            # Send response
            await self._send_typing(False)
            await self.send(text_data=json.dumps({
                'type': 'response',
                'message': file_summary,
                'file_info': {
                    'name': file_name,
                    'rows': len(df),
                    'columns': list(df.columns),
                },
                'conversation_uuid': self.conversation_uuid,
                'timestamp': datetime.now().isoformat()
            }))
            
            # If we already have other files and might be waiting for this one to complete a request,
            # we should automatically check if we can now create a disbursement file
            if hasattr(self, 'collected_files') and self.collected_files:
                await self._send_typing(True)
                
                # Store this file in collected_files
                file_key = file_name.lower().replace(" ", "_").split(".")[0]
                if not hasattr(self, 'collected_files'):
                    self.collected_files = {}
                self.collected_files[file_key] = {
                    "dataframe": df,
                    "file_name": file_name
                }
                
                # Now attempt to generate the requested file with all collected files
                await asyncio.sleep(0.5)  # Brief pause to allow UI updates
                
                try:
                    last_messages = await self._get_last_messages(3)
                    for msg in last_messages:
                        user_msg = msg.get("user_message", "").lower()
                        if "collection" in user_msg or "collect" in user_msg:
                            self.requested_file_type = "collection_file"
                            break
                        elif "disbursement" in user_msg or "disburse" in user_msg:
                            self.requested_file_type = "disbursement_file"
                            break
                    
                    print(f"Detected request type: {self.requested_file_type}")
                    extract_response, download_url = await self._extract_specific_data(
                        f"provide {self.requested_file_type}", df, file_name, is_follow_up=True
                    ) 
                     
                        
                    if download_url:
                        # We successfully generated a file
                        new_message = "use this file to create disbursement details"
                        new_message_obj = await self._save_message(new_message, None)
                        await self._update_message_response(new_message_obj, extract_response)
                        
                        await self._send_typing(False)
                        await self.send(text_data=json.dumps({
                            'type': 'response',
                            'message': extract_response,
                            'csv_download_url': download_url,
                            'conversation_uuid': self.conversation_uuid,
                            'timestamp': datetime.now().isoformat()
                        }))
                    else:
                        # Make sure to turn off typing indicator
                        await self._send_typing(False)
                except Exception as e:
                    logger.error(f"Error processing follow-up analysis: {e}")
                    logger.error(traceback.format_exc())
                    await self._send_typing(False)
                
        except Exception as e:
            logger.error(f"Error handling file upload: {e}")
            logger.error(traceback.format_exc())
            await self._send_typing(False)
            await self._send_error(f"Failed to process file: {str(e)}")

    async def _process_file(self, file_bytes, file_name):
        """Process the uploaded file into a pandas DataFrame"""
        try:
            file_extension = file_name.split('.')[-1].lower() if '.' in file_name else ''
            
            if file_extension == 'csv':
                # Convert bytes to string for text-based formats
                try:
                    text_content = file_bytes.decode('utf-8')
                except:
                    try:
                        text_content = file_bytes.decode('cp1252')  # Windows default
                    except UnicodeDecodeError:
                        text_content = file_bytes.decode('latin1')
                        
                return pd.read_csv(io.StringIO(text_content)), None
            elif file_extension in ['xls', 'xlsx', 'xlsm']:
                # Keep as bytes for binary formats
                return pd.read_excel(io.BytesIO(file_bytes)), None
            elif file_extension == 'json':
                # Convert bytes to string for text-based formats
                text_content = file_bytes.decode('utf-8')
                return pd.read_json(io.StringIO(text_content)), None
            elif file_extension == 'txt':
                # Try different delimiters
                text_content = file_bytes.decode('utf-8')
                try:
                    return pd.read_csv(io.StringIO(text_content), sep='\t'), None
                except:
                    try:
                        return pd.read_csv(io.StringIO(text_content), sep=','), None
                    except:
                        try:
                            return pd.read_csv(io.StringIO(text_content), sep=';'), None
                        except:
                            return None, "Couldn't determine file format for txt file"
            else:
                return None, f"Unsupported file extension: {file_extension}"
        except Exception as e:
            logger.error(f"Error processing file: {e}")
            logger.error(traceback.format_exc())
            return None, str(e)

    async def _generate_file_summary(self, df, file_name):
        """Acknowledge file upload without full summary - wait for user questions"""
        try:
            rows, columns = df.shape
            
             
            acknowledgment = f"""I've successfully processed your file '{file_name}'. 
            The file is now ready for analysis.
            What would you like to know about this data?
            """
            context_message = f"The user has uploaded a file named '{file_name}' containing {rows} rows and {columns} columns with the following columns: {', '.join(list(df.columns))}. This file is now available for analysis."
        
             
            self.history.append(Content(
                role="user",
                parts=[Part.from_text(context_message)]
            ))
            
             
            self.history.append(Content(
                role="model",
                parts=[Part.from_text(acknowledgment)]
            ))
             
            return acknowledgment
        except Exception as e:
            logger.error(f"Error acknowledging file upload: {e}")
            logger.error(traceback.format_exc())
            return f"I've successfully processed your file '{file_name}'. What would you like to know about this data?"

    async def _analyze_file_data(self, question):
        """Analyze the uploaded file based on the user's question"""
        try:
            print("inside _analyze_file_data")
            print("question",question)
            if not hasattr(self, 'current_df') or self.current_df is None:
                return "I don't have any file to analyze. Please upload a file first.", None

            df = self.current_df
            data_str = df.head(50).to_string(index=False)  # Restrict to first 50 rows for context
            file_name = getattr(self, 'current_file_name', 'your file')
            
             
            num_rows_before = len(df)
            num_cols = len(df.columns)
            col_list = list(df.columns)
            
            print("question",question) 
            correction_requested = any(keyword in question.lower() for keyword 
                                      in ['fix', 'correct', 'clean', 'update format', 'inconsistency', 
                                         'standardize', 'reformat', 'restructure', 'convert','format'
                                         'duplicate', 'remove','format'])
            
            extraction_requested = any(keyword in question.lower() for keyword
                                    in ['create','extract', 'filter', 'get', 'find', 'show me', 'provide'
                                        , 'details of', 'information about', 'data for','collection','disbursement'])
            
            
            data_summary = f"""
            File name: {file_name}
            Original Rows: {num_rows_before}
            Columns: {num_cols}
            Column names: {col_list}
            """
            
            reference_files = {
                "EmployeeMaster.csv": {"EmployeeID", "EmployeeName", "Gender", "ReportingTo", "Mobile No", "Designation"},
                "Geography.csv": {"BUID", "BUName", "ReportingTo", "BUType", "BUMobileNo", "BUEmail"},
                "Disbursement.csv":{"CustomerID", "DisbursementID", "LoanType", "DATEOPEN", "DateClose","CustomerName","DisburseAmount","RateOfInterest","RepaymentTenure","EMIAmount","PaymentFrequency","NumberOfDaysPastDue","Gender","MobileNo","Alternate Mobile No","PANNo","EmailID","Address","Pincode","InstStartDate","DateOfBirth","CollectionOfficerID","CollectionOfficerName","BranchName","BranchCode","groupid","groupname","ApplicantType"},
                "Collection.csv":{"CustomerID","DisbursementID","BranchName","BranchCode","PrincipleCollected","InterestCollected","InstStartDate","CollectedAmount","CollectionDate","POS","LoanType"},
                "SecondaryCustomer.csv":{"CoAppType","Name","DateOfBirth","Contact","DisbursementID","Gender","CustomerID","BranchName","LoanType","BranchCode"},
            }

            df_cols = set(df.columns)
            best_match = None
            max_overlap = 0

            for file_name_using_column, ref_cols in reference_files.items():
                overlap = len(df_cols.intersection(ref_cols))
                if overlap > max_overlap:
                    max_overlap = overlap
                    best_match = file_name_using_column
            
            file_name_using_column=best_match
            
            print("file_name_using_column",file_name_using_column)
            
            print("correction_requested",correction_requested)  
            print("extraction_requested",extraction_requested) 
             
                # Define file-specific rules
            file_rules = {
                "EmployeeMaster.csv": {
                    "duplicate_check": ["EmployeeID"],
                    "mandatory_fields": ["EmployeeID", "EmployeeName", "ReportingTo",  "Designation"],  # "Gender" and "Mobile No" is optional
                    "mandatory":[]
                },
                "Geography.csv": {
                    "duplicate_check": ["BUID"],
                    "mandatory_fields": ["BUID", "BUName", "ReportingTo", "BUType"], # "BUMobileNo", "BUEmail" are optional
                    "mandatory":[]
                },
                "Disbursement.csv": {
                    "duplicate_check": ["CustomerID", "DisbursementID", "LoanType", "BMID", "BranchName"],
                    "mandatory_fields": [],  # Assume handled differently
                    "mandatory":["CustomerID","DisbursementID","LoanType","DATEOPEN","DateClose","CustomerName",
                                 "DisburseAmount","RateOfInterest","RepaymentTenure","EMIAmount","PaymentFrequency",
                                 "NumberOfDaysPastDue","Gender","Mobile No","InstStartDate","CollectionOfficerID","CollectionOfficerName","BranchName","BranchCode","ApplicantType"]

                },
                "Collection.csv": {
                    "duplicate_check": ["CustomerID", "DisbursementID"],
                    "mandatory_fields": [],  # Assume handled differently
                    "mandatory":[]
                },
                "SecondaryCustomer.csv": {
                    "duplicate_check": ["DisbursementID", "CustomerID"],
                    "mandatory_fields": [],  # Assume handled differently
                    "mandatory":[]
                }
            }

            # Get applicable rules for the current file
            rules = file_rules.get(file_name_using_column, {})
            dup_fields = rules.get("duplicate_check", [])
            mandatory_fields = rules.get("mandatory_fields", [])
            mandatory=rules.get("mandatory",[])
            # Build the dynamic instructions
            dup_fields_str = ", ".join(dup_fields)
            mandatory_fields_str = ", ".join(mandatory_fields)
            mandatory_str=", ".join(mandatory)
            global_mandatory_fields = mandatory_str    
            if extraction_requested:
                print("global_mandatory_fields",global_mandatory_fields)
                print("inside extract request")
                return await self._extract_specific_data(question, df, file_name,mandatory_str)
            elif correction_requested:
                print("inside file correction")
                print("df inside correction",df)
                
                
                print("dup_fields_str",dup_fields_str)
                print("mandatory_fields_str",mandatory_fields_str)
                corrections_section = f"""
                ## REQUIRED CORRECTIONS - PLEASE FOLLOW EXACTLY:

                1. DUPLICATE ROWS:
                - Check for duplicate rows where ALL these fields match: {dup_fields_str}
                - If all those fields match exactly in multiple rows, keep only the first occurrence and remove others
                - Count how many duplicate rows were removed
                """

                if mandatory_fields:
                    corrections_section += f"""
                2. MISSING VALUES:
                - Drop rows where ANY of the following mandatory fields are missing (null or empty): {mandatory_fields_str}
                - Count how many rows were dropped due to missing mandatory data
                """

                corrections_section += """
                3. PHONE NUMBER STANDARDIZATION:
                - Find all columns containing phone numbers (look for 'mobile no', 'BUMobileNo','phone', 'contact', 'cell' in column names)
                - For each phone number:
                - Convert it into numberic format
                - Remove all non-numeric characters
                - If the number has +91 or 91 and the total digit is greater than 10 then Keep only the last 10 digits of the number
                - Count how many phone numbers were standardized
                """

                        
                correction_prompt = f"""
                I need you to process this data file with specific corrections:

                ## FILE INFORMATION
                - File name: "{file_name_using_column}" 
                - Records: {num_rows_before} rows × {num_cols} columns
                
                ## USER REQUEST
                The user has asked: "{question}"
                
                {corrections_section}

                ## CODING REQUIREMENTS:
                - Create a pandas function to handle these two specific tasks
                - Return the processed dataframe only
                - Count and report how many records were affected by each change
                - Ensure that the code has proper identation and is easy to read
                - Dont include import ststements in the code, as they are already present in the environment
                - Named function as helper_function always

                IMPORTANT: Give me ONLY the Python code with no explanation and proper identation . I will execute it directly.
                """
                
                 
                code_response = await self._get_ai_follow_up_response(correction_prompt)
                print("code_response",code_response)
                 
                code_lines = []
                in_code_block = False
                
                for line in code_response.splitlines():
                    if line.strip() == "```python" or line.strip() == "```":
                        in_code_block = not in_code_block
                        continue
                    if in_code_block or "```" not in code_response:
                        code_lines.append(line)
                
                correction_code = "\n".join(code_lines)
                
                 
                logger.info(f"Generated correction code for duplicates and phone numbers:\n{correction_code}")
                
                 
                try:
                     
                    import numpy as np
                    import re
                    
                    globals_dict = {
                        'pd': pd,
                        'np': np,
                        're': re,
                        'df': df,
                        '__builtins__': __builtins__
                    }
                    local_vars = {"df": df.copy()}
                    
                     
                    correction_code_with_header = f"""
# Generated code
{correction_code}

# Ensure this variable is defined
duplicates_removed = 0
phones_standardized = 0

# Execute the main function if it exists
if 'helper_function' in locals():
    # Handle the specific function name that the AI generated
    result_df = helper_function(df)
    # Since this function returns only the dataframe and not stats, calculate them
    duplicates_removed = len(df) - len(result_df)
    # We can't know the exact phone standardization count, but can estimate from code
    phones_standardized = sum(1 for col in df.columns 
                             if 'mobile' in col.lower() or 'phone' in col.lower() 
                             or 'contact' in col.lower() or 'cell' in col.lower())
elif 'clean_data' in locals():
    result_df, stats = clean_data(df)
    if isinstance(stats, dict):
        duplicates_removed = stats.get('duplicates_removed', 0)
        phones_standardized = stats.get('phones_standardized', 0)
"""
              
                     
                    exec(correction_code_with_header, globals_dict, local_vars)
                    
                     
                    if "result_df" in local_vars and isinstance(local_vars["result_df"], pd.DataFrame):
                         
                        processed_df = local_vars["result_df"]
                        duplicates_removed = local_vars.get("duplicates_removed", num_rows_before - len(processed_df))
                        phones_standardized = local_vars.get("phones_standardized", 0)
                        
                        
                        logger.info(f"Successfully processed data. Removed {duplicates_removed} duplicates and standardized {phones_standardized} phone numbers.")
                         
                        base_name = file_name_using_column.rsplit('.', 1)[0] if '.' in file_name_using_column else file_name_using_column
                        corrected_file_name = f"{base_name}_processed_{uuid.uuid4().hex[:8]}.csv"
                         
                        self.current_df = processed_df
                       
                        try:
                            columns = list(processed_df.columns)
                             
                            rows = processed_df.to_dict('records')
                            csv_download_url = await self._generate_and_upload_csv(columns, rows, corrected_file_name)
                            
                             
                            response = f"""
                            I've processed your file "{file_name_using_column}" with the following changes:

                            1. **Duplicate Removal**: I removed {duplicates_removed} duplicate records based on the combination of these fields: {dup_fields_str}. Only the first occurrence of each unique combination was kept.

                            2. **Phone Number Standardization**: I standardized {phones_standardized} phone numbers by removing country codes (like "91" or "+91") and ensuring they contain only the last 10 digits.

                            Original row count: {num_rows_before}
                            Final row count: {len(processed_df)}

                            I've prepared a processed CSV file that you can download using the link provided below.
                            """
                            
                            return response, csv_download_url
                        except Exception as e:
                            logger.error(f"Failed to generate processed CSV file: {e}")
                            logger.error(traceback.format_exc())
                            return f"I processed your file but encountered an error when creating the downloadable version. {duplicates_removed} duplicates were removed and {phones_standardized} phone numbers were standardized.", None
                    else:
                         #fallback to manual implementation
                        logger.warning("AI-generated code didn't produce a result DataFrame. Using manual implementation.")
                        
                         
                        duplicate_columns = ['CustomerID', 'DisbursementID', 'LoanType', 'BMID', 'BranchName']
                         
                        duplicate_columns = [col for col in duplicate_columns if col in df.columns]
                        
                        if duplicate_columns:
                            before_dedup = len(df)
                            df = df.drop_duplicates(subset=duplicate_columns, keep='first')
                            duplicates_removed = before_dedup - len(df)
                        else:
                            duplicates_removed = 0
                         
                        phone_columns = [col for col in df.columns if any(term in col.lower() for term in 
                                                                    ['mobile', 'phone', 'contact', 'cell'])]
                        
                        phones_standardized = 0
                        for col in phone_columns:
                            if col in df.columns:
                                 
                                original_values = df[col].copy()
                                
                                 
                                df[col] = df[col].astype(str)
                                df[col] = df[col].apply(lambda x: self._standardize_phone_number(x) if pd.notna(x) and str(x) != 'nan' else x)
                                
                                 
                                phones_standardized += (original_values != df[col]).sum()
                        
                        
                        self.current_df = df
                        
                         
                        base_name = file_name_using_column.rsplit('.', 1)[0] if '.' in file_name_using_column else file_name_using_column
                        corrected_file_name = f"{base_name}_processed_{uuid.uuid4().hex[:8]}.csv"
                        
                        try:
                            columns = list(df.columns)
                            rows = df.to_dict('records')
                            csv_download_url = await self._generate_and_upload_csv(columns, rows, corrected_file_name)
                            
                            response = f"""
                            I've processed your file "{file_name_using_column}" with the following changes:

                            1. **Duplicate Removal**: I removed {duplicates_removed} duplicate records based on the combination of these fields: {', '.join(duplicate_columns)}. Only the first occurrence of each unique combination was kept.

                            2. **Phone Number Standardization**: I standardized {phones_standardized} phone numbers across {len(phone_columns)} columns by removing country codes and ensuring they contain only 10 digits.

                            Original row count: {num_rows_before}
                            Final row count: {len(df)}

                            I've prepared a processed CSV file that you can download using the link provided below.
                            """
                            
                            return response, csv_download_url
                        except Exception as e:
                            logger.error(f"Failed to generate processed CSV file: {e}")
                            logger.error(traceback.format_exc())
                            return f"I processed your file but encountered an error when creating the downloadable version. {duplicates_removed} duplicates were removed and {phones_standardized} phone numbers were standardized.", None
                    
                except Exception as e:
                    logger.error(f"Error executing AI-generated correction code: {e}")
                    logger.error(traceback.format_exc())
                    
                     #fallback to manual implementation
                    logger.info("Falling back to manual implementation")
                     
                    duplicate_columns = ['CustomerID', 'DisbursementID', 'LoanType', 'BMID', 'BranchName']
                    duplicate_columns = [col for col in duplicate_columns if col in df.columns]
                    
                    if duplicate_columns:
                        before_dedup = len(df)
                        df = df.drop_duplicates(subset=duplicate_columns, keep='first')
                        duplicates_removed = before_dedup - len(df)
                    else:
                        duplicates_removed = 0
                    
                     
                    phone_columns = [col for col in df.columns if any(term in col.lower() for term in 
                                                                ['mobile', 'phone', 'contact', 'cell'])]
                    
                    phones_standardized = 0
                    for col in phone_columns:
                        if col in df.columns:
                            original_values = df[col].copy()
                            df[col] = df[col].astype(str)
                            df[col] = df[col].apply(lambda x: self._standardize_phone_number(x) if pd.notna(x) and str(x) != 'nan' else x)
                            phones_standardized += (original_values != df[col]).sum()
                    
                    self.current_df = df
                    base_name = file_name_using_column.rsplit('.', 1)[0] if '.' in file_name_using_column else file_name_using_column
                    corrected_file_name = f"{base_name}_processed_{uuid.uuid4().hex[:8]}.csv"
                    
                    try:
                        columns = list(df.columns)
                        rows = df.to_dict('records')
                        csv_download_url = await self._generate_and_upload_csv(columns, rows, corrected_file_name)
                        
                        response = f"""
                        I've processed your file "{file_name_using_column}" with the following changes:

                        1. **Duplicate Removal**: I removed {duplicates_removed} duplicate records based on the combination of these fields: {', '.join(duplicate_columns)}. Only the first occurrence of each unique combination was kept.

                        2. **Phone Number Standardization**: I standardized {phones_standardized} phone numbers across {len(phone_columns)} columns by removing country codes and ensuring they contain only 10 digits.

                        Original row count: {num_rows_before}
                        Final row count: {len(df)}

                        I've prepared a processed CSV file that you can download using the link provided below.
                        """
                        
                        return response, csv_download_url
                    except Exception as e:
                        logger.error(f"Failed to generate processed CSV file: {e}")
                        logger.error(traceback.format_exc())
                        return f"I processed your file but encountered an error when creating the downloadable version. {duplicates_removed} duplicates were removed and {phones_standardized} phone numbers were standardized.", None
            else:
                
                print("inside file analysis",num_rows_before,num_cols)
                analysis_prompt = f"""
                IMPORTANT CONTEXT:
                You are analyzing a file that the user previously uploaded named "{file_name}".
                This file has {num_rows_before} rows and {num_cols} columns.
                
                The user's question about this file is: "{question}"
                
                File information:
                {data_summary}
                
                Data:
                {data_str}
                
                Instructions:
                1. Answer the specific question about the file data
                2. Provide relevant statistics or calculations if needed
                3. Keep your answer focused on the file analysis
                4. Refer to the file by name in your response
                """
                 
                response = await self._get_ai_follow_up_response(analysis_prompt)
                
                return response, None
        
        except Exception as e:
            logger.error(f"Error in file analysis: {e}")
            logger.error(traceback.format_exc())
            return f"I encountered an error while analyzing the file: {str(e)}", None

    async def _extract_specific_data(self, question, df, file_name, is_follow_up=False):
        """Extract specific data from file based on user request and manage file relationships"""
        try:
            print("inside extract specific data")
            # Make sure we have a place to store collected files
            if not hasattr(self, 'collected_files'):
                self.collected_files = {}
            
            # Store the current file regardless of whether it's a follow-up
            file_key = file_name.lower().replace(" ", "_").split(".")[0]
            self.collected_files[file_key] = {
                "dataframe": df,
                "file_name": file_name
            }
            
            # Get list of all available files for context
            all_files_context = ""
            for k, v in self.collected_files.items():
                file_df = v["dataframe"]
                all_files_context += f"\nFile: {v['file_name']} ({len(file_df)} rows, {len(file_df.columns)} columns): {list(file_df.columns)}"
                
            # For each file, include sample data
            all_files_data = {}
            for k, v in self.collected_files.items():
                file_df = v["dataframe"]
                all_files_data[k] = {
                    "name": v["file_name"], 
                    "columns": list(file_df.columns), 
                    "sample_rows": file_df.head(5).to_dict('records')
                }
            
            # Parse the mandatory fields
            mandatory_fields = []
            if hasattr(self, 'global_mandatory_fields') and self.global_mandatory_fields:
                mandatory_fields = [field.strip() for field in self.global_mandatory_fields.split(',')]
            
            # Gather all available columns from all files
            all_available_columns = set()
            for file_info in self.collected_files.values():
                all_available_columns.update(list(file_info['dataframe'].columns))
            
            # Check which mandatory fields are missing
            missing_mandatory_fields = []
            for field in mandatory_fields:
                if field not in all_available_columns:
                    missing_mandatory_fields.append(field)
            
            analysis_prompt = f"""
            EXTRACTION REQUEST ANALYSIS:
            
            You have access to the following files:
            {all_files_context}
            
            The currently active file is "{file_name}" with {len(df)} rows and {len(df.columns)} columns: {list(df.columns)}
            
            Here's a preview of the first few rows of each file to understand the data structure:
            {json.dumps(all_files_data, indent=2)}
            
            The user has requested: "{question}"
            
            IMPORTANT INSTRUCTIONS:
            
            1. For a complete disbursement details report, we MUST have all of these mandatory columns:
            - {self.global_mandatory_fields}
            
            2. For a complete collection details report, we MUST have BOTH of these files:
            - Loan Details file (with DisbursementID, LoanType, InstStartDate, etc.)
            - Customer Details file (with CustomerID, CustomerName, Gender, etc.)
            
            Based on all files uploaded so far, these mandatory fields are still missing:
            - {', '.join(missing_mandatory_fields) if missing_mandatory_fields else "None - all mandatory fields are available"}
            
            I need you to determine:
            1. What type of extract is being requested (e.g., disbursement_details, collection_details)
            2. Is this request complete with all mandatory columns available across all files?
            3. What specific columns are missing that we still need to request from the user?
            4. What mapping should be used for standard column names?
            
            Respond with ONLY a JSON object in this exact format:
            {{
                "request_type": "disbursement_details|collection_details|customer_info|loan_details|other",
                "is_complete": true/false,
                "missing_mandatory_fields": [
                    "field1", "field2", "..."
                ],
                "additional_files_needed": [
                    {{
                        "file_type": "loan_data|customer_data|collection_data|applicant_data",
                        "description": "Brief explanation of what this file should contain",
                        "expected_columns": ["col1", "col2", "..."]
                    }}
                ],
                "columns_from_files": {{
                    "file_key1": [
                        {{"source": "original_column_name", "target": "standard_column_name"}}
                    ],
                    "file_key2": [
                        {{"source": "original_column_name", "target": "standard_column_name"}}
                    ]
                }},
                "expected_columns_in_result": ["CustomerID", "DisbursementID", "LoanType", etc...]
            }}
            
            No explanations or other text.
            """
            
            # Get analysis from AI
            analysis_response = await self._get_ai_follow_up_response(analysis_prompt)
            print("analysis_response inside analyze_file", analysis_response)
            # Parse the JSON response
            json_str = analysis_response
            if "```json" in json_str:
                json_str = json_str.split("```json")[1].split("```")[0].strip()
            elif "```" in json_str:
                json_str = json_str.split("```")[1].split("```")[0].strip()
                
            analysis_config = json.loads(json_str)
            
            # Get request details
            request_type = analysis_config.get("request_type", "unknown")
            is_complete = analysis_config.get("is_complete", False)
            missing_mandatory_fields = analysis_config.get("missing_mandatory_fields", [])
            additional_files_needed = analysis_config.get("additional_files_needed", [])
            columns_from_files = analysis_config.get("columns_from_files", {})
            expected_columns = analysis_config.get("expected_columns_in_result", [])
            
            # Store the request type for potential follow-up uploads
            self.requested_file_type = request_type
            
            # Process the column mappings for each file
            for file_key, mappings in columns_from_files.items():
                if file_key in self.collected_files:
                    file_info = self.collected_files[file_key]
                    file_df = file_info["dataframe"]
                    
                    standard_columns = {}
                    for mapping in mappings:
                        if isinstance(mapping, dict) and "source" in mapping and "target" in mapping:
                            source_col = mapping["source"]
                            target_col = mapping["target"]
                            if source_col in file_df.columns:
                                standard_columns[target_col] = source_col
                    
                    # Update the file info with the standard columns
                    self.collected_files[file_key]["standard_columns"] = standard_columns
                    self.collected_files[file_key]["expected_columns"] = expected_columns
            
            # If mandatory fields are missing, inform the user with detailed instructions
            if not is_complete and (missing_mandatory_fields or additional_files_needed):
                # Identify all available columns across all files
                all_mapped_columns = set()
                for file_key, file_info in self.collected_files.items():
                    if "standard_columns" in file_info:
                        all_mapped_columns.update(file_info["standard_columns"].keys())
                    else:
                        all_mapped_columns.update(list(file_info["dataframe"].columns))
                
                # Create a description of missing fields
                missing_fields_description = ""
                if missing_mandatory_fields:
                    missing_fields_description = "- Missing mandatory fields: " + ", ".join(missing_mandatory_fields) + "\n"
                
                # Create a description of missing files
                missing_files_description = ""
                for file_info in additional_files_needed:
                    file_type = file_info.get("file_type", "")
                    description = file_info.get("description", "")
                    expected_columns = file_info.get("expected_columns", [])
                    
                    missing_files_description += f"- {file_type.title()}: {description}\n"
                    if expected_columns:
                        missing_files_description += f"  Expected columns: {', '.join(expected_columns)}\n"
                
                # Generate a response based on the request type
                if request_type == "disbursement_details":
                    response = f"""
                    Thank you for providing the {file_name} file. This file contains some of the information needed for your disbursement details report.
                    
                    Based on my analysis, I need additional information to complete your request:
                    
                    {missing_fields_description}
                    {missing_files_description}
                    
                    The complete disbursement details should include these fields:
                    {', '.join(expected_columns)}
                    
                    From your current files, I have these fields:
                    {', '.join(all_mapped_columns)}
                    
                    But I'm missing these mandatory fields:
                    {', '.join(missing_mandatory_fields)}
                    
                    Please upload additional file(s) that contain these missing fields. I'll combine all data to create a complete disbursement details report.
                    """
                elif request_type == "collection_details":
                    response = f"""
                    Thank you for providing the {file_name} file. This file contains some of the information needed for your collection details report.
                    
                    Based on my analysis, I need additional information to complete your request:
                    
                    {missing_fields_description}
                    {missing_files_description}
                    
                    The complete collection details should include these fields:
                    {', '.join(expected_columns)}
                    
                    From your current files, I have these fields:
                    {', '.join(all_mapped_columns)}
                    
                    But I'm missing these mandatory fields:
                    {', '.join(missing_mandatory_fields)}
                    
                    Please upload additional file(s) that contain these missing fields. I'll combine all data to create a complete collection details report.
                    """
                else:
                    response = f"""
                    I've processed your {file_name} file, but to complete your request for {request_type}, I need additional information:
                    
                    {missing_fields_description}
                    {missing_files_description}
                    
                    These files will be linked together using common identifiers (like CustomerID or DisbursementID).
                    
                    Could you please upload additional file(s) containing the missing information so I can provide the complete report you requested?
                    """
                
                return response, None
            
            # If we have all required columns, generate the appropriate report
            if is_complete:
                print("inside is_complete")
                if request_type == "disbursement_details":
                    # Generate disbursement details with all collected files
                    result_df, result_message = await self._generate_disbursement_details()
                    if result_df is not None:
                        print("inside result of file",result_df)
                        # Convert the DataFrame to a downloadable CSV file
                        columns = list(result_df.columns)
                        rows = result_df.to_dict('records')
                        
                        # Generate a filename
                        output_filename = f"disbursement_details_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                        
                        # Use the proper parameters for the CSV generation method
                        csv_download_url = await self._generate_and_upload_csv(columns, rows, output_filename)
                        
                        return result_message, csv_download_url
                    else:
                        return result_message, None
                
                elif request_type == "collection_details":
                    # Generate collection details with all collected files
                    result_df, result_message = await self._generate_collection_details()
                    if result_df is not None:
                        # Convert the DataFrame to a downloadable CSV file
                        columns = list(result_df.columns)
                        rows = result_df.to_dict('records')
                        
                        # Generate a filename
                        output_filename = f"collection_details_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                        
                        # Use the proper parameters for the CSV generation method
                        csv_download_url = await self._generate_and_upload_csv(columns, rows, output_filename)
                        
                        return result_message, csv_download_url
                    else:
                        return result_message, None
                
            # If we get here, just acknowledge receipt of the file
            return f"""
            I've processed your {file_name} file and identified it as containing {request_type} information.
            
            This file has {len(df)} rows and {len(df.columns)} columns.
            
            What specific information would you like to extract from this data?
            """, None
            
        except Exception as e:
            logger.error(f"Error extracting specific data: {e}")
            logger.error(traceback.format_exc())
            return f"I encountered an error while trying to extract the requested data: {str(e)}", None
    
    
    
    async def _generate_disbursement_details(self):
        """Generate comprehensive disbursement details report from collected files"""
        try:
            if not hasattr(self, 'collected_files') or not self.collected_files:
                return None, "No files have been uploaded yet. Please upload relevant files first."
                
             
            target_columns = [
                "CustomerID", "DisbursementID", "LoanType", "DATEOPEN", "DateClose",
                "CustomerName", "DisburseAmount", "RateOfInterest", "RepaymentTenure",
                "EMIAmount", "PaymentFrequency", "NumberOfDaysPastDue", "Gender",
                "Mobile No", "InstStartDate", "DateOfBirth", "BranchName", 
                "BranchCode", "ApplicantType"
            ]
            
             
            all_expected_columns = set()
            for file_info in self.collected_files.values():
                if "expected_columns" in file_info:
                    all_expected_columns.update(file_info["expected_columns"])
            
             
            column_mapping_prompt = f"""
            I have the following files with these columns:
            
            {json.dumps({k: {'columns': list(v['dataframe'].columns), 'first_100_rows': v['dataframe'].head(100).to_dict(orient='records')} for k, v in self.collected_files.items()})}
            
            I need to create a comprehensive disbursement details report with these target columns:
            {target_columns}
            
            For each target column, determine which source file and column it should come from.
            Consider column name variations (e.g., "Mobile No" might be "MobileNumber" or "Phone").
            Identify relationships between files (e.g., CustomerID linking customer data to loan data).
            
            Respond with ONLY a JSON object showing the mapping in this format:
            {{
                "target_column_name": {{"file": "file_key", "column": "source_column_name"}},
                ...
            }}
            """
             
            mapping_response = await self._get_ai_follow_up_response(column_mapping_prompt)
             
            json_str = mapping_response
            if "```json" in json_str:
                json_str = json_str.split("```json")[1].split("```")[0].strip()
            elif "```" in json_str:
                json_str = json_str.split("```")[1].split("```")[0].strip()
                
            column_mappings = json.loads(json_str)
             
            base_file_key = None
            base_df = None
            
            for file_key, file_info in self.collected_files.items():
                df = file_info['dataframe']
                if any(col for col in df.columns if 'disbursement' in col.lower() or 'loan' in col.lower()):
                    base_file_key = file_key
                    base_df = df.copy()
                    break
            
            if base_df is None: 
                base_file_key = list(self.collected_files.keys())[0]
                base_df = self.collected_files[base_file_key]['dataframe'].copy()
             
            result_df = pd.DataFrame()
             
            join_keys_prompt = f"""
            I need to join these files:
            {json.dumps({k: {'columns': list(v['dataframe'].columns), 'sample_rows': v['dataframe'].head(10).to_dict(orient='records')} for k, v in self.collected_files.items()})}
            
            Provide a join strategy that links these files together.
            For each file pair, determine which columns should be used to join them.
            Common join keys could be CustomerID, DisbursementID, or similar identifiers.
            
            Respond with ONLY a JSON object like this:
            {{
                "file_pairs": [
                    {{
                        "file1": "file_key1",
                        "file2": "file_key2",
                        "join_keys": [
                            {{"file1_column": "col1", "file2_column": "col2"}}
                        ]
                    }}
                ]
            }}
            """
             
            join_response = await self._get_ai_follow_up_response(join_keys_prompt)
             
            json_str = join_response
            if "```json" in json_str:
                json_str = json_str.split("```json")[1].split("```")[0].strip()
            elif "```" in json_str:
                json_str = json_str.split("```")[1].split("```")[0].strip()
                
            join_strategy = json.loads(json_str)
             
            for target_col in target_columns:
                if target_col in column_mappings:
                    mapping = column_mappings[target_col]
                    file_key = mapping.get("file")
                    source_col = mapping.get("column")
                    
                    if file_key in self.collected_files and source_col in self.collected_files[file_key]['dataframe'].columns:
                        if file_key == base_file_key:
                             
                            result_df[target_col] = base_df[source_col]
                        else:
                             
                            related_df = self.collected_files[file_key]['dataframe']
                            
                             
                            join_pair = next((pair for pair in join_strategy.get("file_pairs", []) 
                                            if (pair["file1"] == base_file_key and pair["file2"] == file_key) 
                                            or (pair["file1"] == file_key and pair["file2"] == base_file_key)), None)
                            
                            if join_pair:
                                 
                                if join_pair["file1"] == base_file_key:
                                    base_key = join_pair["join_keys"][0]["file1_column"]
                                    related_key = join_pair["join_keys"][0]["file2_column"]
                                else:
                                    base_key = join_pair["join_keys"][0]["file2_column"]
                                    related_key = join_pair["join_keys"][0]["file1_column"]
                                
                                if base_key in base_df.columns and related_key in related_df.columns:
                                    
                                    mapping_dict = dict(zip(related_df[related_key], related_df[source_col]))
                                    
                                     
                                    result_df[target_col] = base_df[base_key].map(mapping_dict)
                                else:
                                     
                                    result_df[target_col] = None
                            else:
                                 #fallback
                                common_cols = set(base_df.columns).intersection(set(related_df.columns))
                                potential_join_keys = [col for col in common_cols if 'id' in col.lower() or 'code' in col.lower()]
                                
                                if potential_join_keys:
                                    join_key = potential_join_keys[0]
                                     
                                    mapping_dict = dict(zip(related_df[join_key], related_df[source_col]))
                                    
                                     
                                    result_df[target_col] = base_df[join_key].map(mapping_dict)
                                else:
                                     
                                    result_df[target_col] = None
                    else:
                         
                        result_df[target_col] = None
                else:
                     
                    result_df[target_col] = None
            
            applicant_file_key = next((k for k, v in self.collected_files.items() if 'applicant' in k.lower()), None)
            if applicant_file_key and 'DisbursementID' in result_df.columns:
                applicant_df = self.collected_files[applicant_file_key]['dataframe']
                applicant_id_col = next((col for col in applicant_df.columns if 'disbursement' in col.lower()), None)
                applicant_type_col = next((col for col in applicant_df.columns if 'type' in col.lower()), None)
                
                if applicant_id_col and applicant_type_col:
                    applicant_mapping = dict(zip(applicant_df[applicant_id_col], applicant_df[applicant_type_col]))
                    result_df['ApplicantType'] = 'Applicant' # edited this when details of applicant type is available
            
             
            result_df = result_df.dropna(how='all')
            
            
            output_filename = "comprehensive_disbursement_details.csv"
            
             
            columns = list(result_df.columns)
            rows = result_df.to_dict('records')
            
            
            response = f"""
            I've created a comprehensive disbursement details report by combining data from your uploaded files.
            
            The report includes:
            - {len(result_df)} disbursement records
            - {sum(1 for col in result_df.columns if not result_df[col].isna().all())} columns of information including:
              - Customer details (ID, name, gender, contact information)
              - Loan information (amount, interest rate, tenure, EMI)
              - Dates (opening, closing, installment start)
              - Branch information
              - Applicant type details
            
            You can download the complete report using the link below.
            """
            
            return result_df, response
            
        except Exception as e:
            logger.error(f"Error generating disbursement details: {e}")
            logger.error(traceback.format_exc())
            return None, f"I encountered an error while generating the disbursement details report: {str(e)}"
        
        
        
    async def _generate_collection_details(self):
        """Generate comprehensive collection details report from collected files"""
        try:
            if not hasattr(self, 'collected_files') or not self.collected_files:
                return None, "No files have been uploaded yet. Please upload relevant files first."
            
            # Define target columns for collection details report
            target_columns = [
                "CustomerID",
                "DisbursementID",
                "BranchName",
                "BranchCode",
                "PrincipleCollected",
                "InterestCollected",
                "InstStartDate", 
                "CollectedAmount",
                "CollectionDate",
                "POS",
                "LoanType"
            ]
            
            # Get list of all expected columns from collected files
            all_expected_columns = set()
            for file_info in self.collected_files.values():
                if "expected_columns" in file_info:
                    all_expected_columns.update(file_info["expected_columns"])
            
            # Get AI to determine column mappings from source files
            column_mapping_prompt = f"""
            I have the following files with these columns:
            
            {json.dumps({k: {'columns': list(v['dataframe'].columns), 'first_100_rows': v['dataframe'].head(100).to_dict(orient='records')} for k, v in self.collected_files.items()})}
            
            I need to create a comprehensive collection details report with these target columns:
            {target_columns}
            
            For each target column, determine which source file and column it should come from.
            Consider column name variations (e.g., "CollectedAmount" might be "AmountCollected" or "Payment").
            Identify relationships between files (e.g., CustomerID linking collection data to loan data).
            
            Respond with ONLY a JSON object showing the mapping in this format:
            {{
                "target_column_name": {{"file": "file_key", "column": "source_column_name"}},
                ...
            }}
            """
            
            # Get column mapping from AI
            mapping_response = await self._get_ai_follow_up_response(column_mapping_prompt)
            
            # Parse the AI response
            json_str = mapping_response
            if "```json" in json_str:
                json_str = json_str.split("```json")[1].split("```")[0].strip()
            elif "```" in json_str:
                json_str = json_str.split("```")[1].split("```")[0].strip()
                
            column_mappings = json.loads(json_str)
            
            # Find the base file to start with (preferably the one with collection information)
            base_file_key = None
            base_df = None
            
            for file_key, file_info in self.collected_files.items():
                df = file_info['dataframe']
                if any(col for col in df.columns if 'collect' in col.lower() or 'payment' in col.lower()):
                    base_file_key = file_key
                    base_df = df.copy()
                    break
            
            # If no collection-specific file found, use the first file
            if base_df is None:
                base_file_key = list(self.collected_files.keys())[0]
                base_df = self.collected_files[base_file_key]['dataframe'].copy()
            
            # Create the result dataframe
            result_df = pd.DataFrame()
            
            # Get AI to determine join strategy
            join_keys_prompt = f"""
            I need to join these files:
            {json.dumps({k: {'columns': list(v['dataframe'].columns), 'sample_rows': v['dataframe'].head(10).to_dict(orient='records')} for k, v in self.collected_files.items()})}
            
            Provide a join strategy that links these files together.
            For each file pair, determine which columns should be used to join them.
            Common join keys could be CustomerID, DisbursementID, or similar identifiers.
            
            Respond with ONLY a JSON object like this:
            {{
                "file_pairs": [
                    {{
                        "file1": "file_key1",
                        "file2": "file_key2",
                        "join_keys": [
                            {{"file1_column": "col1", "file2_column": "col2"}}
                        ]
                    }}
                ]
            }}
            """
            
            # Get join strategy from AI
            join_response = await self._get_ai_follow_up_response(join_keys_prompt)
            
            # Parse the AI response
            json_str = join_response
            if "```json" in json_str:
                json_str = json_str.split("```json")[1].split("```")[0].strip()
            elif "```" in json_str:
                json_str = json_str.split("```")[1].split("```")[0].strip()
                
            join_strategy = json.loads(json_str)
            
            # Build the result dataframe by applying mappings
            for target_col in target_columns:
                if target_col in column_mappings:
                    mapping = column_mappings[target_col]
                    file_key = mapping.get("file")
                    source_col = mapping.get("column")
                    
                    if file_key in self.collected_files and source_col in self.collected_files[file_key]['dataframe'].columns:
                        if file_key == base_file_key:
                            # If column is from base file, copy directly
                            result_df[target_col] = base_df[source_col]
                        else:
                            # For columns from other files, use join strategy
                            related_df = self.collected_files[file_key]['dataframe']
                            
                            # Find the join keys for this file pair
                            join_pair = next((pair for pair in join_strategy.get("file_pairs", []) 
                                            if (pair["file1"] == base_file_key and pair["file2"] == file_key) 
                                            or (pair["file1"] == file_key and pair["file2"] == base_file_key)), None)
                            
                            if join_pair:
                                # Determine which file is file1 and which is file2
                                if join_pair["file1"] == base_file_key:
                                    base_key = join_pair["join_keys"][0]["file1_column"]
                                    related_key = join_pair["join_keys"][0]["file2_column"]
                                else:
                                    base_key = join_pair["join_keys"][0]["file2_column"]
                                    related_key = join_pair["join_keys"][0]["file1_column"]
                                
                                if base_key in base_df.columns and related_key in related_df.columns:
                                    # Create a mapping dictionary from related file
                                    mapping_dict = dict(zip(related_df[related_key], related_df[source_col]))
                                    
                                    # Apply mapping to base dataframe
                                    result_df[target_col] = base_df[base_key].map(mapping_dict)
                                else:
                                    # If join keys not found, leave column empty
                                    result_df[target_col] = None
                            else:
                                # Fallback: use common columns as join keys
                                common_cols = set(base_df.columns).intersection(set(related_df.columns))
                                potential_join_keys = [col for col in common_cols if 'id' in col.lower() or 'code' in col.lower()]
                                
                                if potential_join_keys:
                                    join_key = potential_join_keys[0]
                                    # Create a mapping dictionary from related file
                                    mapping_dict = dict(zip(related_df[join_key], related_df[source_col]))
                                    
                                    # Apply mapping to base dataframe
                                    result_df[target_col] = base_df[join_key].map(mapping_dict)
                                else:
                                    # If no potential join keys found, leave column empty
                                    result_df[target_col] = None
                    else:
                        # If source column not found, leave target column empty
                        result_df[target_col] = None
                else:
                    # If no mapping found, leave target column empty
                    result_df[target_col] = None
            
            # Remove rows where all values are null
            result_df = result_df.dropna(how='all')
            
            # Generate response message
            response = f"""
            I've created a comprehensive collection details report by combining data from your uploaded files.
            
            The report includes:
            - {len(result_df)} collection records
            - {sum(1 for col in result_df.columns if not result_df[col].isna().all())} columns of information including:
            - Customer and disbursement identifiers
            - Branch information
            - Collection amounts and dates
            - Loan details
            
            You can download the complete report using the link below.
            """
            
            return result_df, response
            
        except Exception as e:
            logger.error(f"Error generating collection details: {e}")
            logger.error(traceback.format_exc())
            return None, f"I encountered an error while generating the collection details report: {str(e)}"
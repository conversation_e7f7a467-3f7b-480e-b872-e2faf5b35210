import React, { useEffect, useState } from "react";
import { DataCarousel } from "./DataCarousel";
import { Flex, message } from "antd";
import { CustomerDetails } from "./CustomerDetails";
import { PayCardDetails } from "./PayCardDetails";
import AppHeader from "../AppHeader";
import { Outlet, useLocation } from "react-router";
import { AXIOS } from "../../apis/ho-Instance";
import dayjs from "dayjs";
import ApplicationLoader from "../ApplicationLoader";

export default function Dashboard() {
  const location = useLocation();
  const [engagementData, setEngagementData] = useState([]);
  const [dashboardData, setDashboardData] = useState({});
  const [loading, setLoading] = useState(false);
  const [selectedDate, setSelectedDate] = useState(null);
  const storedFilter = localStorage.getItem("filterDate");

  // Get dashboard data
  async function handleGetDashboardData() {
    setLoading(true);
    try {
      const response = await AXIOS.get("dashboard/");
      const data = response.data;
      let dashboardData;
      // Check the response
      if (response.status === 200) {
        if (Array.isArray(data.dashboard_data))
          dashboardData = data.dashboard_data[0];
        else dashboardData = data.dashboard_data;
        setDashboardData(dashboardData);

        // Process engagement data
        const engagementData = Array.isArray(data?.engagement_data)
          ? data.engagement_data
          : [];
        
        //Fill the missing dates 
        const filledData = fillMissingDates(engagementData);
        localStorage.setItem("engagementData", JSON.stringify(filledData));
        setEngagementData(filledData);
      }
    } catch (error) {
      console.log("Error in HO dashboard", error);
    } finally {
      setLoading(false);
    }
  }

  // Fetch the dashboard data
  const handleDashboardFilteredData = async ({ fromDate, toDate }) => {
    setLoading(true);
    try {
      const response = await AXIOS.post("dashboard/", {
        from_date: fromDate,
        to_date: toDate,
      });

      const data = response.data;
      let dashboardData;

      // Check the response
      if (response.status === 200) {
        if (Array.isArray(data.dashboard_data))
          dashboardData = data.dashboard_data[0];
        else dashboardData = data.dashboard_data;
        setDashboardData(dashboardData);
      }
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoading(false);
    }
  };

  // Handle filter out the dashboard data based on the selected dates
  const handleDisplaySelectedData = async ({ date, value }) => {
    const formattedDates = {
      fromDate: new Date(date).toISOString(),
      toDate: new Date(date).toISOString(),
    };
    if (value === 0) {
      message.warning("Filter will not apply if value is 0!");
      handleGetDashboardData();
      setSelectedDate(null);
      return localStorage.removeItem("filterDate");
    } else {
      setSelectedDate(date);
      localStorage.setItem("filterDate", JSON.stringify(formattedDates));
      await handleDashboardFilteredData({ fromDate: date, toDate: date });
    }
  };

  // Handle missing dates
  function fillMissingDates(engagementData) {
    if (!engagementData.length) return [];
  
    // Sort data by date
    engagementData.sort((a, b) =>
      dayjs(a.CreatedDate).diff(dayjs(b.CreatedDate))
    );
  
    // Get the first date and today
    const firstDate = dayjs(engagementData[0].CreatedDate);
    const currentDate = dayjs();
  
    // Generate all dates in range
    const allDates = [];
    let tempDate = firstDate;
  
    while (
      tempDate.isBefore(currentDate) ||
      tempDate.isSame(currentDate, "day")
    ) {
      allDates.push(tempDate.format("YYYY-MM-DD"));
      tempDate = tempDate.add(1, "day");
    }
  
    // Create a lookup for existing data
    const dataMap = new Map(
      engagementData.map((item) => [item.CreatedDate, item.total_engaged])
    );
  
    // Map over all dates, filling missing ones with 0
    return allDates.map((date) => ({
      CreatedDate: date,
      total_engaged: dataMap.get(date) || 0,
    }));
  }

  // Handle apply filters
  const applyFilters = async (fromDate, toDate) => {
    // Convert to ISO format before storing in localStorage
    const formattedDates = {
      fromDate: dayjs(fromDate).format("YYYY-MM-DD"),
      toDate: dayjs(toDate).format("YYYY-MM-DD"),
    };
    localStorage.setItem("filterDate", JSON.stringify(formattedDates));
    await handleDashboardFilteredData({ fromDate, toDate });
  };

  // Handle clear the filter
  const ClearFilters = async() => {
    await handleGetDashboardData();
    setSelectedDate(null);
    localStorage.removeItem("filterDate");
  };

  // Handle the engagement data
  useEffect(() => {
    if (engagementData && engagementData.length > 0) {
      const filledData = fillMissingDates(engagementData);
      localStorage.setItem("engagementData", JSON.stringify(filledData));
    }
  }, [engagementData]);

  // Called the dashboard api as initial
  useEffect(() => {  
    if (storedFilter) {
      const { fromDate, toDate } = JSON.parse(storedFilter);
      setSelectedDate(fromDate);
      handleDashboardFilteredData({ fromDate, toDate });
    } else {
      setSelectedDate(null);
       handleGetDashboardData();
    }
  }, []);
  
  return location.pathname === "/ho/dashboard" ? (
    <Flex gap={20} vertical style={{ width: "99.7%", paddingBlockEnd: "2rem" }}>
      {/* Header */}
      <AppHeader
        title={"Dashboard"}
        isDashboard={true}
        applyFilters={applyFilters}
        ClearFilters={ClearFilters}
        selectedDate={selectedDate}
      />
      <Flex vertical gap={32}>
        {/* Data carousel */}
        {engagementData.length ? (
          <DataCarousel
            handleDisplaySelectedData={handleDisplaySelectedData}
            handleGetDashboardData={handleGetDashboardData}
            selectedDate={selectedDate}
            setSelectedDate={setSelectedDate}
          />
        ) : null}
        {
          loading
          ?<ApplicationLoader/>
          :<>{/* Customer details */}
          <CustomerDetails dashboardData={dashboardData} />
          {/* Pay cards */}
          <PayCardDetails dashboardData={dashboardData} />
          </>
        }
        </Flex>
    </Flex>
  ) : (
    <Outlet />
  );
}

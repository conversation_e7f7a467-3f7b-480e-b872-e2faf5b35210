@import url("../../index.css");


$light-blue: #E4F8F9;
$blue: #BECFFF;

.dropdown {
    width: 130px;

    :global(.ant-select-selector) {
        background-color: $light-blue !important;
        border: none !important;
        display: flex; // Ensure the arrow is aligned properly
        justify-content: space-between;
        align-items: center;
        padding: 8px; 
        box-shadow: 3px 3px 5px rgba(0, 0, 0, 0.3);
    }

    :global(.ant-select-selection-item) {
        font-weight: 400;
        color: var(--dark-blue);
        font-family: 'Kanit','Courier New', Courier, monospace;
    }

    :global(.ant-select-arrow) {
        color: #000; // Make arrow black
        font-weight: bold; // Make arrow bold
    }
}

:global(.ant-select-dropdown) {
    margin-top: 1.2rem;
    background-color: $light-blue !important;

    :global(.ant-select-item-option) {
        color: var(--dark-blue);
        font-weight: 600;
        
        &:global(.ant-select-item-option-disabled){
         color: rgba(0, 0, 0, 0.56);
      }
    }

    :global(.ant-select-item-option-active) {
        &:not(.ant-select-item-option-disabled) {
            background-color: $blue;
            color: var(--dark-blue);
            font-weight: 400;
        }
    }
}
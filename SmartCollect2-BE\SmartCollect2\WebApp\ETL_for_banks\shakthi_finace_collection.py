import pandas as pd
import numpy as np
from sqlalchemy import create_engine,update,MetaData, Table, text
from datetime import datetime, timedelta
import os
import math

DATABASE = "SmartCollect2"
USERNAME = "postgres"
PASSWORD = "Markytics12345"
HOST = "*************"
PORT = 5432

# Create database engine
engine = create_engine(f"postgresql://{USERNAME}:{PASSWORD}@{HOST}:{PORT}/{DATABASE}")

class DatabaseManager:
    """Database manager class to handle single engine instance and connections"""
    
    def __init__(self):
        self.engine = None
    
    def initialize_engine(self):
        """Initialize the database engine once"""
        if self.engine is None:
            # Use environment variables if available, otherwise fall back to constants
            db_user = os.environ.get('DATABASE_USER', USERNAME)
            db_password = os.environ.get('DATABASE_PASSWORD', PASSWORD)
            db_host = os.environ.get('DATABASE_HOST', HOST)
            db_port = os.environ.get('DATABASE_PORT', PORT)
            db_name = os.environ.get('DATABASE_NAME', DATABASE)
            
            connection_string = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
            self.engine = create_engine(connection_string)
            print("Database engine initialized successfully!")
        return self.engine
    
    def get_engine(self):
        """Get the engine instance, initialize if not already done"""
        if self.engine is None:
            self.initialize_engine()
        return self.engine


def get_rawfile_data(date1, date2, bankmstid):
    """
    Get data from rawfile table for specified dates and bank
    
    Args:
        date1: First date to fetch data (YYYY-MM-DD format)
        date2: Second date to fetch data (YYYY-MM-DD format)
        bankmstid: Bank Master ID to filter data
    
    Returns:
        DataFrame with rawfile data
    """
    db_manager = DatabaseManager()
    engine = db_manager.get_engine()
    
    try:
        with engine.connect() as connection:
            query = """
            SELECT 
                "customerid",
                "disbursementid", 
                "branchname",
                "branchcode",
                "overdueamount",
                "inserted_date",
                "bankmstid"
            FROM "rawfile" 
            WHERE "inserted_date" IN (%(date1)s, %(date2)s)
            AND "bankmstid" = %(bankmstid)s
            ORDER BY "customerid", "disbursementid", "inserted_date"
            """
            
            result_df = pd.read_sql_query(
                query, 
                connection, 
                params={
                    'date1': date1,
                    'date2': date2,
                    'bankmstid': bankmstid
                }
            )
            
        print(f"Successfully fetched {len(result_df)} records from rawfile table for bankmstid={bankmstid}")
        print(f"Date range: {date1} to {date2}")
        print(f"Column names: {list(result_df.columns)}")
        return result_df
        
    except Exception as e:
        print(f"Error fetching rawfile data: {e}")
        return pd.DataFrame()


def get_branch_codes_from_db(bank_mst_id, branch_names):
    """
    Query function to get branch codes from database based on BankMstID and BranchName
    
    Args:
        bank_mst_id: Bank Master ID
        branch_names: List or array of branch names to lookup
    
    Returns:
        Dictionary mapping branch names to branch codes
    """
    db_manager = DatabaseManager()
    engine = db_manager.get_engine()
    
    # Convert to list and check if empty
    branch_names_list = list(branch_names) if hasattr(branch_names, '__iter__') else [branch_names]
    if len(branch_names_list) == 0:
        return {}
    
    try:
        with engine.connect() as connection:
            query = """
            SELECT bm."BranchName", bm."BranchMstID"
            FROM "BranchMst" bm 
            WHERE bm."BranchName" = ANY(%(branch_names)s)
            AND bm."BankMstID" = %(bank_mst_id)s
            """
            
            result_df = pd.read_sql_query(
                query, 
                connection, 
                params={
                    'branch_names': branch_names_list,
                    'bank_mst_id': bank_mst_id
                }
            )
            
        # Create mapping dictionary: branch_name -> branch_code
        branch_mapping = dict(zip(result_df['BranchName'], result_df['BranchMstID']))
        print(f"Successfully mapped {len(branch_mapping)} branches from database")
        return branch_mapping
        
    except Exception as e:
        print(f"Error querying branch codes: {e}")
        print(f"Attempted to query {len(branch_names_list)} branch names: {branch_names_list[:5]}...")
        return {}


def calculate_overdue_difference(rawfile_data, date1, date2, min_amount=100):
    """
    Calculate overdue amount difference between two dates for each customer/disbursement
    Also handles records that exist only in date2 (new records)
    Only includes records where the overdue difference is >= min_amount
    
    Args:
        rawfile_data: DataFrame with rawfile data
        date1: Earlier date
        date2: Later date
        min_amount: Minimum overdue difference amount to include (default: 100)
    
    Returns:
        DataFrame with calculated overdue differences >= min_amount
    """
    if rawfile_data.empty:
        return pd.DataFrame()
    
    # Convert inserted_date to string for comparison if it's not already
    rawfile_data['inserted_date'] = rawfile_data['inserted_date'].astype(str)
    
    # Separate data by dates
    date1_data = rawfile_data[rawfile_data['inserted_date'] == date1].copy()
    date2_data = rawfile_data[rawfile_data['inserted_date'] == date2].copy()
    
    print(f"Records for {date1}: {len(date1_data)}")
    print(f"Records for {date2}: {len(date2_data)}")
    
    # CASE 1: Records that exist in both dates (regular difference calculation)
    merged_data = pd.merge(
        date2_data, 
        date1_data, 
        on=['customerid', 'disbursementid'],
        suffixes=('_date2', '_date1'),
        how='inner'
    )
    
    if not merged_data.empty:
        # Calculate overdue amount difference (date2 - date1)
        merged_data['OverdueAmountDifference'] = (
            merged_data['overdueamount_date2'] - merged_data['overdueamount_date1']
        )
        
        # Only keep records where there's a positive difference >= min_amount
        collection_data_matched = merged_data[
            merged_data['OverdueAmountDifference'] >= min_amount
        ].copy()
        
        print(f"Found {len(collection_data_matched)} matched records with overdue reduction >= {min_amount}")
        
        # Show how many records were filtered out
        filtered_out = len(merged_data[merged_data['OverdueAmountDifference'] > 0]) - len(collection_data_matched)
        if filtered_out > 0:
            print(f"Filtered out {filtered_out} records with overdue reduction < {min_amount}")
    else:
        collection_data_matched = pd.DataFrame()
    
    # CASE 2: Records that exist only in date2 (new records)
    # Find records in date2 that don't exist in date1
    date2_keys = set(zip(date2_data['customerid'], date2_data['disbursementid']))
    date1_keys = set(zip(date1_data['customerid'], date1_data['disbursementid']))
    new_records_keys = date2_keys - date1_keys
    
    if new_records_keys:
        # Filter date2_data for new records
        new_records_mask = date2_data.apply(
            lambda row: (row['customerid'], row['disbursementid']) in new_records_keys, 
            axis=1
        )
        new_records_data = date2_data[new_records_mask].copy()
        
        # For new records, set OverdueAmountDifference as the current overdue amount
        new_records_data['OverdueAmountDifference'] = new_records_data['overdueamount']
        
        # Filter new records to only include those with overdue amount >= min_amount
        new_records_filtered = new_records_data[
            new_records_data['OverdueAmountDifference'] >= min_amount
        ].copy()
        
        if not new_records_filtered.empty:
            # Add suffix to column names to match the format of merged_data
            new_records_filtered = new_records_filtered.rename(columns={
                'branchname': 'branchname_date2',
                'branchcode': 'branchcode_date2',
                'overdueamount': 'overdueamount_date2',
                'bankmstid': 'bankmstid_date2'
            })
            
            # Add missing columns with None values to match merged_data structure
            for col in ['branchname_date1', 'branchcode_date1', 'overdueamount_date1', 'bankmstid_date1', 'inserted_date_date1']:
                new_records_filtered[col] = None
            
            new_records_filtered['inserted_date_date2'] = new_records_filtered['inserted_date']
        
        print(f"Found {len(new_records_filtered)} new records with overdue amount >= {min_amount}")
        
        # Show how many new records were filtered out
        filtered_out_new = len(new_records_data) - len(new_records_filtered)
        if filtered_out_new > 0:
            print(f"Filtered out {filtered_out_new} new records with overdue amount < {min_amount}")
        
        new_records_data = new_records_filtered
    else:
        new_records_data = pd.DataFrame()
    
    # Combine both cases
    all_collection_data = []
    
    if not collection_data_matched.empty:
        all_collection_data.append(collection_data_matched)
    
    if not new_records_data.empty:
        # Ensure column order matches
        if not collection_data_matched.empty:
            new_records_data = new_records_data.reindex(columns=collection_data_matched.columns, fill_value=None)
        all_collection_data.append(new_records_data)
    
    if all_collection_data:
        final_collection_data = pd.concat(all_collection_data, ignore_index=True)
        print(f"Total collection records with amount >= {min_amount}: {len(final_collection_data)}")
        print(f"  - Matched records with overdue reduction >= {min_amount}: {len(collection_data_matched) if not collection_data_matched.empty else 0}")
        print(f"  - New records with overdue amount >= {min_amount}: {len(new_records_data) if not new_records_data.empty else 0}")
        
        # Show statistics about the amounts
        if not final_collection_data.empty:
            min_diff = final_collection_data['OverdueAmountDifference'].min()
            max_diff = final_collection_data['OverdueAmountDifference'].max()
            avg_diff = final_collection_data['OverdueAmountDifference'].mean()
            print(f"  - Amount range: {min_diff:.2f} to {max_diff:.2f}, Average: {avg_diff:.2f}")
        
        return final_collection_data
    else:
        print(f"No collection data found with overdue difference >= {min_amount}")
        return pd.DataFrame()


def save_to_database(df, collection_date, table_name="CollectionFile"):
    """
    Quick fix: Simplified save function that handles large datasets
    """
    if df.empty:
        print("No data to save to database")
        return False
    
    # Create the final DataFrame
    df1 = pd.DataFrame()
    df1['DisbursementID'] = df['disbursementid']
    df1['CustomerId'] = df['customerid']
    df1['BankMstID'] = df['bankmstid_date2']
    df1['BranchMstID'] = df['BranchMstID']
    df1['CreatedDate'] = datetime.now().strftime('%Y-%m-%d')
    df1['LoanType'] = None
    df1['CollectionDate'] = collection_date
    df1['CollectedAmount'] = abs(df['OverdueAmountDifference'])
    df1['BranchName'] = df['branchname_date2']
    df1['PrincipleCollected'] = None
    df1['InterestCollected'] = None
    df1['InstStartDate'] = None
    df1['BMID'] = None
    df1['POS'] = None
    df1['TransactionNumber'] = None
    df1['inserted_date'] = datetime.now().date()

    print(f"Initial records: {len(df1)}")
    
    # Remove duplicates within the current batch only
    duplicate_columns = ['CollectionDate', 'CollectedAmount', 'DisbursementID']
    df1_deduplicated = df1.drop_duplicates(subset=duplicate_columns, keep='first')
    print(f"Records after deduplication: {len(df1_deduplicated)}")
    
    try:
        db_manager = DatabaseManager()
        engine = db_manager.get_engine()
        
        # Check for existing records using a simpler approach
        with engine.connect() as connection:
            # Get existing records for this collection date only (much smaller dataset)
            existing_query = f"""
            SELECT "DisbursementID", "CollectedAmount"
            FROM "{table_name}"
            WHERE "CollectionDate" = %(collection_date)s
            """
            
            existing_records = pd.read_sql_query(
                existing_query, 
                connection, 
                params={'collection_date': collection_date}
            )
            
            if not existing_records.empty:
                print(f"Found {len(existing_records)} existing records for collection date {collection_date}")
                
                # Create set for fast lookup
                existing_combinations = set(
                    zip(existing_records['DisbursementID'], 
                        existing_records['CollectedAmount'].astype(float))
                )
                
                # Filter out existing records
                def not_exists_in_db(row):
                    combination = (row['DisbursementID'], float(row['CollectedAmount']))
                    return combination not in existing_combinations
                
                df1_final = df1_deduplicated[df1_deduplicated.apply(not_exists_in_db, axis=1)].copy()
                
                removed_count = len(df1_deduplicated) - len(df1_final)
                print(f"Removed {removed_count} records that already exist")
            else:
                df1_final = df1_deduplicated.copy()
                print("No existing records found for this collection date")
        
        if df1_final.empty:
            print("No new records to save")
            return False
        
        print(f"Saving {len(df1_final)} records to database...")
        
        # Save in batches if dataset is large
        batch_size = 5000
        if len(df1_final) > batch_size:
            total_batches = math.ceil(len(df1_final) / batch_size)
            print(f"Large dataset detected. Saving in {total_batches} batches...")
            
            for i in range(total_batches):
                start_idx = i * batch_size
                end_idx = min((i + 1) * batch_size, len(df1_final))
                batch = df1_final.iloc[start_idx:end_idx]
                
                batch.to_sql(table_name, engine, if_exists='append', index=False)
                print(f"Saved batch {i + 1}/{total_batches} ({len(batch)} records)")
        else:
            df1_final.to_sql(table_name, engine, if_exists='append', index=False)
        
        print(f"Successfully saved {len(df1_final)} records to table '{table_name}'")
        return True
        
    except Exception as e:
        print(f"Error saving to database: {e}")
        import traceback
        traceback.print_exc()
        return False


def process_collection_data(collection_data, bankmstid):
    """
    Process collection data and get branch master IDs
    
    Args:
        collection_data: DataFrame with collection data
        bankmstid: Bank Master ID
    """
    if collection_data.empty:
        return pd.DataFrame()
    
    df = collection_data.copy()
    
    # Get unique branch names for the specified bank
    unique_branches = df['branchname_date2'].unique()
    
    # Initialize BranchMstID column
    df['BranchMstID'] = None
    
    # Get branch codes for the specified bank
    branch_mapping = get_branch_codes_from_db(bankmstid, unique_branches)
    
    # Map branch codes
    for idx, row in df.iterrows():
        branch_name = row['branchname_date2']
        if branch_name in branch_mapping:
            df.at[idx, 'BranchMstID'] = branch_mapping[branch_name]
    
    # Ensure bankmstid is set to the specified value
    df['bankmstid_date2'] = bankmstid
    
    return df


def main(date1=None, date2=None, collection_date=None, bankmstid=None, min_amount=100):
    """
    Main function to orchestrate the collection data processing from rawfile table
    
    Args:
        date1: Earlier date (YYYY-MM-DD format) - optional
        date2: Later date (YYYY-MM-DD format) - optional
        collection_date: Collection date (YYYY-MM-DD format) - optional
        bankmstid: Bank Master ID - optional
        min_amount: Minimum overdue difference amount to include (default: 100)
    """
    print("="*50)
    print("COLLECTION DATA PROCESSING FROM RAWFILE STARTED")
    print("="*50)
    
    # Default configuration - use parameters if provided, otherwise use defaults
    date1 =  "2025-07-14"
    date2 =  "2025-07-11"
    collection_date = collection_date or "2025-07-13"
    bankmstid = 28
    database_table = "CollectionFile"
    
    print(f"Configuration:")
    print(f"  - Date 1: {date1}")
    print(f"  - Date 2: {date2}")
    print(f"  - Collection Date: {collection_date}")
    print(f"  - Bank Master ID: {bankmstid}")
    print(f"  - Minimum Amount Filter: {min_amount}")
    
    try:
        # Step 1: Get data from rawfile table
        print(f"\n1. Fetching data from rawfile table for dates {date1} and {date2}...")
        rawfile_data = get_rawfile_data(date1, date2, bankmstid)
        
        if rawfile_data.empty:
            print("No data found in rawfile table for specified dates. Exiting...")
            return
        
        # Step 2: Calculate overdue differences with minimum amount filter
        print(f"\n2. Calculating overdue amount differences (minimum amount: {min_amount})...")
        collection_data = calculate_overdue_difference(rawfile_data, date1, date2, min_amount)
        
        if collection_data.empty:
            print(f"No collection data found with overdue difference >= {min_amount}. Exiting...")
            return
        
        # Step 3: Process the data
        print("\n3. Processing collection data...")
        processed_data = process_collection_data(collection_data, bankmstid)
        
        if processed_data.empty:
            print("No valid records after processing. Exiting...")
            return
        
        # Step 4: Display summary
        print("\n4. Processing Summary:")
        print(f"   - Total records from rawfile: {len(rawfile_data)}")
        print(f"   - Records with overdue reduction >= {min_amount}: {len(processed_data)}")
        print(f"   - Collection date set to: {collection_date}")
        print(f"   - Bank Master ID: {bankmstid}")
        print(f"   - Minimum amount filter: {min_amount}")
        print(f"   - Columns in processed data: {list(processed_data.columns)}")
        
        # Step 5: Preview the data
        print("\n5. Preview of processed data:")
        preview_cols = ['customerid', 'disbursementid', 'branchname_date2', 'OverdueAmountDifference']
        available_cols = [col for col in preview_cols if col in processed_data.columns]
        print(processed_data[available_cols].head())
        
        # Step 6: Save to database
        print("\n6. Saving to database...")
        save_to_database(processed_data, collection_date, database_table)
        
        print("\n" + "="*50)
        print("COLLECTION DATA PROCESSING COMPLETED SUCCESSFULLY!")
        print("="*50)
        
        return processed_data
        
    except Exception as e:
        print(f"\nError in main processing: {e}")
        import traceback
        traceback.print_exc()
        print("="*50)
        print("COLLECTION DATA PROCESSING FAILED!")
        print("="*50)
        return None


def process_collection_for_dates(date1, date2, collection_date, bankmstid, save_to_db=True, table_name="CollectionFile", min_amount=100):
    """
    Process collection data for specific dates and bank
    
    Args:
        date1: Earlier date (YYYY-MM-DD format)
        date2: Later date (YYYY-MM-DD format)
        collection_date: Date to set as collection date
        bankmstid: Bank Master ID
        save_to_db: Whether to save to database
        table_name: Database table name
        min_amount: Minimum overdue difference amount to include (default: 100)
    
    Returns:
        Processed DataFrame
    """
    print(f"Processing collection data between {date1} and {date2} for bankmstid={bankmstid}")
    print(f"Collection date will be set to: {collection_date}")
    print(f"Minimum amount filter: {min_amount}")
    
    # Get and process data
    rawfile_data = get_rawfile_data(date1, date2, bankmstid)
    if rawfile_data.empty:
        return pd.DataFrame()
    
    collection_data = calculate_overdue_difference(rawfile_data, date1, date2, min_amount)
    if collection_data.empty:
        return pd.DataFrame()
    
    processed_data = process_collection_data(collection_data, bankmstid)
    
    # Save to database if requested
    if save_to_db and not processed_data.empty:
        save_to_database(processed_data, collection_date, table_name)
    
    return processed_data


if __name__ == "__main__":
    # Example usage with different parameters:
    
    # Option 1: Run with default values (minimum amount = 100)
    main()
    
    # Option 2: Run with custom minimum amount
    # main(min_amount=500)  # Only process records with overdue difference >= 500
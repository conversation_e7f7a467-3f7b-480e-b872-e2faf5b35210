import { Spin, Table, Typography } from 'antd'
import React from 'react'
import Style from "./_style.module.scss";
import PropTypes from 'prop-types';

const {Text} = Typography;

export function StateTable({ data,isLoading, onStateClick, onRangeClick }) {
    const columns = [
        {
            title: 'State',
            dataIndex: 'State',
            width: 150,
            render: (text, data) => {
                return <Text 
                style={{cursor:"pointer"}}
                onClick={()=> onStateClick({
                    stateId:data.State_id,
                    stateName: text,
                 })}>{text || "-"}</Text>
           }
        },
        {
            title: 'Regular',
            dataIndex: 0,
            render: (value, {State_id}) => <Text style={{cursor:"pointer"}} onClick={()=>{
                onRangeClick({stateId:State_id, tablewise:"State", range:"0" })
            }}>{value}</Text>
        },
        {
            title: '1 to 30',
            dataIndex: "1-30",
            render: (value,{State_id}) => <Text style={{cursor:"pointer"}} onClick={()=>{
                onRangeClick({stateId:State_id, tablewise:"State", range:"1-30" })
            }}>{value}</Text>
           
        },
        {
            title: '31 to 60',
            dataIndex: "31-60",
            render: (value, {State_id}) => <Text style={{cursor:"pointer"}} onClick={()=>{
                onRangeClick({stateId:State_id, tablewise:"State", range:"31-60" })
            }}>{value}</Text>
        },
        {
            title: '61 to 90',
            dataIndex: "61-90",
            render: (value, {State_id}) => <Text style={{cursor:"pointer"}} onClick={()=>{
                onRangeClick({stateId:State_id, tablewise:"State", range:"61-90" })
            }}>{value}</Text>
        },
        {
            title: '91 to 180',
            dataIndex: "91-180",
            render: (value, {State_id}) => <Text style={{cursor:"pointer"}} onClick={()=>{
                onRangeClick({stateId:State_id, tablewise:"State", range:"91-180" })
            }}>{value}</Text>
        },
        {
            title: '181 to 360',
            dataIndex:"181-360",
            render: (value, {State_id}) => <Text style={{cursor:"pointer"}} onClick={()=>{
                onRangeClick({stateId:State_id, tablewise:"State", range:"181-360" })
            }}>{value}</Text>
        },
        {
            title: '360+',
            dataIndex: "360+",
            render: (value,{State_id}) => <Text style={{cursor:"pointer"}} onClick={()=>{
                console.log(value);
                onRangeClick({stateId:State_id, tablewise:"State", range:"360+" })
            }}>{value}</Text>
        },
        {
            title: 'Arrear',
            dataIndex: 'arrear',
            render: (value) => <Text>{value}</Text>
        },
        {
            title: 'Arrear Percent',
            dataIndex: 'arrear_perc',
            render: (value) => <Text>{value}</Text>
        },
        {
            title: 'NPA',
            dataIndex: 'npa',
            render: (value) => <Text>{value}</Text>
        },
        {
            title: 'NPA Percent',
            dataIndex: 'npa_perc',
            render: (value) => <Text>{value}</Text>
        },
        {
            title: 'Total',
            dataIndex: 'Total',
            render: (value) => <Text>{value}</Text>
        },
    ];
    
    const dataSource = Array.isArray(data)? data.filter((d)=> d.State !==0)?.map((data, i) => ({
        key: i+1,
        ...data
    })):[];
  return (
    <Spin tip={"Loading..."} spinning={isLoading}>
       <Table
           bordered
           virtual
           className={Style.CustomTable}
           columns={columns}
           dataSource={dataSource}
           scroll={{
               x: 1500,
               y: 360,
           }}
           pagination={false}
       />
   </Spin>
 )}

StateTable.propTypes={
    data: PropTypes.array,
    isLoading: PropTypes.bool, 
    onStateClick: PropTypes.func, 
    onRangeClick :PropTypes.func
}
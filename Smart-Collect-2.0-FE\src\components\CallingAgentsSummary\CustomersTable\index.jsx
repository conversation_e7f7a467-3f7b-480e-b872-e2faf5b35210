import { Flex, message, Table, Typography } from "antd";
import React, { useEffect, useState } from "react";
import FEEDBACK_IMG from "../../../assets/Images/fluent-mdl2_feedback.svg";
import PHONE_IMG from "../../../assets/Images/call_icon.png";
import { RecentEngagement } from "../../RecentEngagement";
import {
  SMART_COLLECT_MENU_IDS,
  handleDateFormatter,
  handleDownloadTable,
  RECENT_ENGAGEMENT_APPS_DATA,
  STATUS_IDS,
  formatAmount,
} from "../../../constant";
import { FeedbackModal } from "../../Modals/FeedbackModal";
import { ConversionHistory } from "../../Modals/ConversionHistory";
import Style from "./_custom-table.module.scss";
import { DialerModal } from "../../Modals/DialerModal";
import { AXIOS } from "../../../apis/ho-Instance";
import DOWNLOAD_IMG from "../../../assets/Images/download.svg";
import { CustomerDetailsCard } from "../../CustomerAllCards/CustomerDetailsCard";
import CustomerProfileModal from "../../Modals/CustomerProfileModal";
import PropTypes from "prop-types";

const { Text } = Typography;

export default function CustomersTable({
  customerData,
  pageId = null,
  title,
  modalButtonText,
}) {
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [isLoading, setIsLoading] = useState(false);
  const [modalState, setModalState] = useState({
    history: { isOpen: false, data: null },
    feedback: { isOpen: false, data: null },
    dialer: { isOpen: false, data: null },
    customer: { isOpen: false, data: null },
  });

  const openModal = ({ type, data }) => {
    setModalState((prev) => ({
      ...prev,
      [type]: { isOpen: true, data },
    }));
  };

  const closeModal = (type) => {
    setModalState((prev) => ({
      ...prev,
      [type]: { isOpen: false, data: null },
    }));
  };

  const handleDialerSubmit = async ({
    number,
    loanmstid,
    overdueAmount,
    branchMstId,
  }) => {
    setIsLoading(true);
    message.success("Initiate call");

    // Close Dialer & Open Feedback Modal with Correct Data
    setModalState((prev) => ({
      ...prev,
      dialer: { isOpen: false, data: null }, // Close Dialer Modal
      feedback: {
        isOpen: true,
        data: {
          loanmstid,
          overdue_amount: overdueAmount,
          branchMstId,
        }, // Open Feedback Modal with Data
      },
    }));

    try {
      await AXIOS.post("v1/dialer/", {
        loanMstId: loanmstid,
        bm_phone: number,
      });
    } catch (error) {
      console.error("Error in dialer", error?.message);
    } finally {
      setIsLoading(false);
    }
  };

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      sorter: (a, b) => a.key - b.key,
      render: (_, data) => <Text>{data.key}.</Text>,
    },
    {
      title: "Customer Id",
      dataIndex: "customerid",
      sorter: (a, b) =>
        String(a.customerid || "").localeCompare(String(b.customerid || "")),
      render: (value) => (
        <Text className={Style.customerId}>{value ?? "-"}</Text>
      ),
    },
    {
      title: "Customer Name",
      dataIndex: "customername",
      sorter: (a, b) => a.customername.localeCompare(b.customername),
      render: (_, data) => (
        <Text
          className={Style.name}
          style={{ cursor: "pointer" }}
          onClick={() => openModal({ type: "customer", data })}
        >
          {data?.customername}
        </Text>
      ),
    },
    {
      title: "Recent Engagement",
      dataIndex: "recent_engagement",
      render: (_, data) => {
        if (
          !data.last_5_communication_types ||
          !Array.isArray(data.last_5_communication_types)
        ) {
          return <Text style={{ fontSize: "20px" }}>-</Text>;
        }

        // Ensure we only take the last 5 items
        const limitedTypes = data.last_5_communication_types.slice(-5);

        // Ensure we only take the last 5 items
        const recentEngagementAppsData = limitedTypes
          .map(({ source, delivery_status }) => {
            const appData = RECENT_ENGAGEMENT_APPS_DATA.find(
              (app) => app.id?.toLowerCase() === source?.toLowerCase()
            );

            if (!appData) return null;

            return {
              ...appData,
              status:
                delivery_status === "Delivered"
                  ? STATUS_IDS.SUCCESS
                  : STATUS_IDS.REJECTED,
            };
          })
          .filter(Boolean); // Remove null entries

        return recentEngagementAppsData.length === 0 ? (
          <Text style={{ fontSize: "20px" }}>-</Text>
        ) : (
          <RecentEngagement
            recentEngagementAppsData={recentEngagementAppsData}
          />
        );
      },
    },
    {
      title: "No. of Attempts",
      dataIndex: "communication_count",
      sorter: (a, b) => a.communication_count - b.communication_count,
      render: (attempts) => (
        <Flex justify="center">
          <Text className={Style.attempts}>
            {attempts !== null ? attempts : 0}
          </Text>
        </Flex>
      ),
    },
    {
      title: "DPD",
      dataIndex: "dpd",
      sorter: (a, b) => a.dpd - b.dpd,
      render: (value) => (
        <Flex justify="center">
          <Text>{value ?? "-"}</Text>
        </Flex>
      ),
    },
    {
      title: "Branch Name",
      dataIndex: "branchname",
      sorter: (a, b) => a.branchname.localeCompare(b.branchname),
      render: (value) => (
        <Flex justify="center">
          <Text>{value ?? "-"}</Text>
        </Flex>
      ),
    },
    {
      title: "Disbursement Id",
      dataIndex: "disbursementid",
      sorter: (a, b) => a.disbursementid.localeCompare(b.disbursementid),
      render: (value) => (
        <Text className={Style.disbursementId}>{value ?? "-"}</Text>
      ),
    },
    {
      title: "Loan Type",
      dataIndex: "loanType",
      width: 250,
      sorter: (a, b) => a.loantype.localeCompare(b.loantype),
      render: (value) => <Text className={Style.loanType}>{value ?? "-"}</Text>,
    },
    {
      title:
        pageId === SMART_COLLECT_MENU_IDS.ALREADY_PAY
          ? "Claim Amount"
          : "Overdue Amount",
      dataIndex: "overdueAmount",
      sorter: (a, b) => (a.overdue_amount || 0) - (b.overdue_amount || 0),
      render: (_, data) => (
        <Text>Rs.{formatAmount(data?.overdue_amount ?? 0)}</Text>
      ),
    },
    pageId !== SMART_COLLECT_MENU_IDS.READY_TO_PAY
      ? {
          title: "Response Date",
          dataIndex: "responsedatetime",
          sorter: (a, b) =>
            new Date(a.responsedatetime) - new Date(b.responsedatetime),
          render: (value) => (
            <Text>{value ? handleDateFormatter(value) : "--"}</Text>
          ),
        }
      : {
          title: "Promise Date",
          dataIndex: "promise_date",
          sorter: (a, b) => new Date(a.promise_date) - new Date(b.promise_date),
          render: (_, data) => (
            <Text>
              {data?.promise_date
                ? handleDateFormatter(data?.promise_date)
                : "--"}
            </Text>
          ),
        },
    pageId === SMART_COLLECT_MENU_IDS.READY_TO_PAY
      ? {
          title: "Promise Amount",
          dataIndex: "promiseAmount",
          sorter: (a, b) => (a.promise_amount || 0) - (b.promise_amount || 0),
          render: (_, data) => (
            <Text>Rs.{formatAmount(data?.promise_amount ?? 0)}</Text>
          ),
        }
      : null,
    pageId === SMART_COLLECT_MENU_IDS.READY_TO_PAY
      ? {
          title: "Promise Status",
          dataIndex: "status",
          sorter: (a, b) => (a.status || "").localeCompare(b.status || ""),
          render: (value) => <Text>{value}</Text>,
        }
      : null,
    {
      title: "Feedback",
      dataIndex: "feedback",
      fixed: isMobile ? null : "right",
      width: 92,
      render: (_, data) => (
        <div className={Style.icon_div}>
          <div className={Style.icon_img}>
            <input
              type="image"
              src={FEEDBACK_IMG}
              alt="feedback"
              onClick={() => openModal({ type: "feedback", data })}
            />
          </div>
        </div>
      ),
    },
    {
      title: "Call",
      dataIndex: "call",
      fixed: isMobile ? null : "right",
      width: 65,
      render: (_, data) => (
        <div className={Style.icon_div}>
          <div className={Style.icon_img}>
            <input
              type="image"
              src={PHONE_IMG}
              alt="call"
              onClick={() => openModal({ type: "dialer", data })}
            />
          </div>
        </div>
      ),
    },
  ].filter(Boolean);

  const dataSource = Array.isArray(customerData)
    ? customerData?.map((d, i) => ({
        key: i + 1,
        ...d,
      }))
    : [];

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Handle downloading
  const handleDownload = async () => {
    if (dataSource.length === 0) {
      return message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: title,
        worksheetName: `Dashboard-${title}`,
        tableData: dataSource,
      });
    }
  };

  return (
    <>
      <Flex justify="end">
        <button className={Style.download_button} onClick={handleDownload}>
          <img src={DOWNLOAD_IMG} alt="download-button" />
        </button>
      </Flex>
      <Table
        virtual
        className={Style.customTable}
        columns={columns}
        dataSource={dataSource}
        scroll={{
          x: 2000,
          y: 360,
        }}
        pagination={{
          showSizeChanger: false,
        }}
      />

      <ConversionHistory
        customerName={modalState.history.data?.customername}
        loanMstId={modalState.history.data?.loanmstid}
        modalStatus={modalState.history.isOpen}
        // handleCancel={handleHistoryCancel}
        handleCancel={() => closeModal("history")}
      />

      <FeedbackModal
        pageId={pageId}
        overdueAmount={modalState.feedback.data?.overdue_amount}
        branchMstId={modalState.feedback.data?.branchMstId}
        loanmstid={modalState.feedback.data?.loanmstid}
        modalStatus={modalState.feedback.isOpen}
        handleSubmit={() => closeModal("feedback")}
        handleCancel={() => closeModal("feedback")}
        componentProp={
          <CustomerDetailsCard
            loanmstid={modalState.feedback.data?.loanmstid}
            branchMstId={modalState.feedback.data?.branchMstId}
            communicationCount={modalState.feedback.data?.communication_count}
            last5CommunicationTypes={
              modalState.feedback.data?.last_5_communication_types
            }
            status={modalState.feedback.data?.status}
            name={modalState.feedback.data?.customername}
            customerId={modalState.feedback.data?.customerid}
            disbursementId={modalState.feedback.data?.disbursementid}
            branchName={modalState.feedback.data?.branchname}
            loanType={modalState.feedback.data?.loantype}
            overdueAmount={modalState.feedback.data?.overdue_amount}
            promiseAmount={modalState.feedback.data?.promise_amount}
            promiseDate={modalState.feedback.data?.promise_date}
            responseDate={modalState.feedback.data?.responsedatetime}
            feedbackDate={modalState.feedback.data?.feedbackdate}
            dpdAmount={modalState.feedback.data?.dpd}
            modalButtonText={"Feedback"}
            isModal={true}
            pageId={pageId}
            firstTimeDefaulter={false}
            isFeedbackClick={true}
          />
        }
      />

      <DialerModal
        branchMstId={modalState.feedback.data?.branchMstId}
        customerName={modalState.dialer.data?.customername}
        overdueAmount={modalState.dialer.data?.overdue_amount}
        loanmstid={modalState.dialer.data?.loanmstid}
        isLoading={isLoading}
        modalStatus={modalState.dialer.isOpen}
        handleSubmit={handleDialerSubmit}
        handleCancel={() => closeModal("dialer")}
      />
      {/* <ViewCustomerModal 
        data={modalState.customer.data} 
        modalStatus={modalState.customer.isOpen}
        pageId={pageId}
        modalButtonText={modalButtonText}
        handleCancel={()=> closeModal("customer")}
      /> */}

      <CustomerProfileModal
        modalStatus={modalState.customer.isOpen}
        name={modalState.customer.data?.customername}
        customerId={modalState?.customer.data?.customerid}
        disbursementId={modalState?.customer.data?.disbursementid}
        branchName={modalState?.customer.data?.branchname}
        loanType={modalState.customer.data?.loantype}
        dpdAmount={modalState.customer.data?.dpd}
        overdueAmount={modalState.customer.data?.overdue_amount}
        promiseAmount={modalState.customer.data?.promise_amount}
        loanmstid={modalState.customer.data?.loanmstid}
        branchMstId={modalState.customer.data?.branchmstid}
        pageId={pageId}
        handleClose={() => closeModal("customer")}
      />
    </>
  );
}

// Define the props type
CustomersTable.propTypes = {
  customerData: PropTypes.array,
  pageId: PropTypes.string,
  title: PropTypes.string,
  modalButtonText: PropTypes.string,
};

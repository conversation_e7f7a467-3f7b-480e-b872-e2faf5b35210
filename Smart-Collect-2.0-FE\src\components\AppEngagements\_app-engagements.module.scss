@import url("../../index.css");

.app_engagements_container {
    padding: 0.3rem 1rem;
    border: 1px solid #000000;
    border-radius: 13px;
    gap: 20px;

    .box{
      gap: 10px;
    }

    .icon_img {
      width: 20px;
      img {
        width: 100%;
        object-fit: contain;
      }
    }
    .title {
      font-size: 14px;
      font-weight: 400;
      font-family: 'Kani<PERSON>','Courier New', Courier, monospace;
    }
}

// Responsive view
@media screen and (max-width:768px) {
  .app_engagements_container{
    margin-top: 1rem;
    padding: 0.3rem;
    gap: 10px;
    width: 100%;
    justify-content: space-between;
    align-items:center;
  }
}

@media screen and (max-width:568px) {
  .app_engagements_container{
    padding: 0.3rem;
    gap: 10px;
    width: 100%;
    justify-content: space-between;
    align-items:center;
    .box{
      flex-direction: column;
      gap: 0;
    }

    .icon_img{
      width: 14px;
    }
    .title{
      font-size: 8px;
      text-align: center;
    }
  }
}
@import url("../../../index.css");

.pay_container {
  .card {
    padding: 0.5rem 0 0;
    border-radius: 5px;
    box-shadow: 4px 4px 4px 0 #00000033;
    flex: 1;
    .box{
      height: 100%;
    }
    .card_details {
      padding: 0 0 0 1rem;

      :global(.ant-typography) {
        margin: 0;
        font-weight: 500;
        font-family: 'Kani<PERSON>','Courier New', Courier, monospace;
      }
      span {
        font-size: 14px;
        font-weight: 400 !important;
        white-space: nowrap;
      }
    }
  }
  .pay_image {
    width: 80px;
    img {
      width: 100%;
      object-fit: contain;
    }
  }
  .contact_image{
    width: 100px;
    img{
      width: 100%;
      object-fit: contain;
    }
  }
}

@media screen and (max-width:768px) {
  .pay_container{
    .card{
      .box{
        flex-wrap: wrap;
        justify-content: center;
      }
      .card_details{
        padding: 0;
        :global(.ant-typography) {
          font-size: 18px;
          text-align: center;
        }
        span{
          font-size: 14px !important;
        }
      }
    }
    
    .pay_image{
      width: 50px;

    }
  }
}

@media screen and (max-width:668px) {
  .pay_container{
    :global(.ant-flex){
      flex-direction: column;
    }
    .card{
      .card_details{
        :global(.ant-typography){
          font-size: 20px;
        }
      }
    }
  }
}
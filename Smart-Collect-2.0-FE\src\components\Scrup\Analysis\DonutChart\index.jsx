import PropTypes from 'prop-types';
import React from 'react';
import ReactApexChart from 'react-apexcharts';

export function DonutChart({data, handleGetCustomerDetails, selectedAnalysisCard, selectedState}) {
  const riskKeys = [
    'very_low_risk',
    'low_risk',
    'medium_risk',
    'high_risk',
    'very_high_risk'
  ];
  const series = riskKeys.map(key => data?.[key] || 0);

  const options = {
    chart: {
      type: 'donut',
      events: {
        dataPointSelection: (event, chartContext, config) => {
          const selectedIndex = config.dataPointIndex;
          const selectedRiskKey = riskKeys[selectedIndex];
          handleGetCustomerDetails({
            state_id:selectedState,
            riskCategory: selectedRiskKey, 
            cardWise: selectedAnalysisCard
          });
        }
      }
    },
    labels: ['Very Low', 'Low', 'Medium', 'High', 'Very High'],
    colors: ['#0033A0', '#7aa4ff', '#c9d8ff', '#9be6f8', '#5480E4'],
    legend: {
      position: 'right'
    },
    tooltip: {
      y: {
        formatter: (val) => `${val}%`
      }
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
        chart: {
          width: '100%',
          height: 250
        },
        legend: {
          position: 'bottom'
        }
      }
    }
  ]};
     
  return <ReactApexChart options={options} series={series} type="donut" height={300} />;
}

DonutChart.propTypes={
  data: PropTypes.object,
  handleGetCustomerDetails: PropTypes.func,
  selectedAnalysisCard: PropTypes.any,
  selectedState: PropTypes.any
}
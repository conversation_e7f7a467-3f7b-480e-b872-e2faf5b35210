import { Flex } from "antd";
import React, { useEffect, useState } from "react";
import AppHeader from "../AppHeader";
import AppEngagements from "../AppEngagements";
import CustomersTable from "../CustomersTable";
import CustomerAllCards from "../CustomerAllCards";
import {
  ALL_CACHE_NAMES,
  LOCAL_KEYS,
  SMART_COLLECT_MENU_IDS,
} from "../../constant";
import { AXIOS } from "../../apis/ho-Instance";
import Style from "./_already-pay.module.scss";
import ApplicationLoader from "../ApplicationLoader";
import {
  getCacheKey,
  getFromCache,
  getPostCacheKey,
  storeToCache,
} from "../../utils/cacheHelper";
import { encryptBody, isSameEncryptedBody } from "../../utils/crypto";

const CACHE_NAME = ALL_CACHE_NAMES.DASHBOARD_CACHE;

export default function AlreadyPay() {
  const [isSwitchOn, setIsSwitchOn] = useState(false);
  const [customerData, setCustomerData] = useState([]);
  const [loading, setLoading] = useState(false);
  const storedDpd = localStorage.getItem("DashboardDpdCategory");
  const storedFilter = localStorage.getItem("filterDate");

  const handleGetClaimData = async () => {
    setLoading(true);
    const cacheKey = getCacheKey({ endpoint: "claim/" });
    // Check cache
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setCustomerData(cachedData);
      setLoading(false);
    }
    try {
      const response = await AXIOS.get("claim/");
      if (response.status === 200) {
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
        setCustomerData(response.data);
      }
    } catch (error) {
      console.log("Error in Ready to pay", error?.message);
    } finally {
      setLoading(false);
    }
  };

  const handleAlreadyPay = async ({
    fromDate,
    toDate,
    BranchName,
    dpdRange,
    disbursementID,
  }) => {
    setLoading(true);
    const body = {
      from_date: fromDate,
      to_date: toDate,
      branch_id: BranchName,
      dpdfrom: dpdRange?.start ?? -1,
      dpdto: dpdRange?.end ?? -1,
      disbursementids: disbursementID,
    };
    const cacheKey = getPostCacheKey({ endpoint: "claim/", body });
    const encryptedOldBody = localStorage.getItem(LOCAL_KEYS.HO_PAY_FILTER);
    const sameBody = isSameEncryptedBody({
      newBody: body,
      encryptedOldBody,
    });

    if (sameBody) {
      const cachedData = await getFromCache({
        cacheName: CACHE_NAME,
        key: cacheKey,
      });
      if (cachedData) {
        setCustomerData(cachedData);
        setLoading(false);
      }
    }
    try {
      const response = await AXIOS.post("claim/", body);
      // Check the response
      if (response.status === 200) {
        const encrypted = encryptBody(body);
        if (encrypted) {
          localStorage.setItem(LOCAL_KEYS.HO_PAY_FILTER, encrypted);
        }
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
        setCustomerData(response.data);
      }
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoading(false);
    }
  };

  // Apply filter and fetch data
  const applyFilters = async ({
    fromDate,
    toDate,
    BranchName,
    disbursementID,
  }) => {
    await handleAlreadyPay({
      fromDate: fromDate,
      toDate: toDate,
      BranchName,
      dpdRange: JSON.parse(storedDpd),
      disbursementID,
    });
  };

  // Clear filters and fetch data
  const ClearFilters = () => {
    handleGetClaimData();
    localStorage.removeItem("filterDate");
    localStorage.removeItem(LOCAL_KEYS.HO_PAY_FILTER);
  };

  useEffect(() => {
    if (storedFilter || storedDpd) {
      const { fromDate, toDate, BranchName, disbursementID } = storedFilter
        ? JSON.parse(storedFilter)
        : { fromDate: null, toDate: null, BranchName: null };
      const parsedDpd = storedDpd
        ? JSON.parse(storedDpd)
        : { start: -1, end: -1 };
      handleAlreadyPay({
        fromDate,
        toDate,
        BranchName,
        dpdRange: parsedDpd,
        disbursementID,
      });
    } else {
      handleGetClaimData();
    }
  }, []);

  return (
    <Flex gap={15} vertical className={Style.already_pay}>
      {/* Header */}
      <AppHeader
        title={`Already Paid: ${customerData.length}`}
        isDashboard={false}
        isSwitchOn={isSwitchOn}
        setIsSwitchOn={setIsSwitchOn}
        isDashboardOtherPages={true}
        applyFilters={applyFilters}
        ClearFilters={ClearFilters}
      />

      <Flex vertical gap={15}>
        {/* app engagements */}
        <AppEngagements />

        {/* Details */}
        {loading ? (
          <ApplicationLoader />
        ) : (
          <div className={Style.details}>
            {!isSwitchOn ? (
              <CustomerAllCards
                pageId={SMART_COLLECT_MENU_IDS.ALREADY_PAY}
                customerData={customerData}
                modalButtonText={"Feedback"}
                isModal={true}
              />
            ) : (
              <CustomersTable
                title={"Already Pay"}
                customerData={customerData}
                pageId={SMART_COLLECT_MENU_IDS.ALREADY_PAY}
                modalButtonText={"Feedback"}
              />
            )}
          </div>
        )}
      </Flex>
    </Flex>
  );
}

import { Table, Typography } from "antd";
import React, { useState } from "react";;
import Style from "./_analysis-table.module.scss";
import PropTypes from "prop-types";

const {Text, Title} = Typography;

export function Analysis({analysisData}) {
    const [selectedRegion, setSelectedRegion] = useState(null);
    const [selectedHubIndex, setSelectedHubIndex] = useState(null);

    const columns = [
        {
          title: 'Sr. No.',
          dataIndex: 'key',
          rowScope: 'row',
          width:90,
          render:(_, data)=> <Text>{data?.key}.</Text>
        },
        {
          title: 'Region',
          dataIndex: 'region',
          render:(_, data)=> (
            <Text className={Style.blueText} 
            onClick={() => handleRegionClick(data.region)}>
                {data?.region}
            </Text>),
        },
        ...(selectedRegion ? [{
            title: 'Hub',
            dataIndex: 'hub',
            render: (_, data,index) => (
               <button
                className={Style.button} 
                disabled = {data.region !== selectedRegion}
                onClick={() => handleHubClick(index)}>
                    {data.region === selectedRegion ? data?.hub : '-'}
                </button>
            ),
        }] : []),
        ...(selectedHubIndex !== null ? [{
                title: 'Branch',
                dataIndex: 'branch',
                render: (_, data, index) => (
                <Text className={Style.text}>
                    {index === selectedHubIndex ? data.branch : '-'}
                </Text>
                ),
        }] : []),
        {
          title: 'DPD Category',
          children:[
            {
                title: 'Arrear',
                dataIndex: 'arrear',
                render:(_, data)=> <Text className={Style.text}>{data?.DPDCategory?.arrear ? data.DPDCategory.arrear :"-"}</Text>, 
            },
            {
                title: 'NPA',
                dataIndex: 'npa',
                render:(_, data)=> <Text className={Style.text}>{data?.DPDCategory?.npa? data.DPDCategory.npa:"-"}</Text>, 
            },
            {
                title: 'Defaulter',
                dataIndex: 'defaulter',
                render:(_, data)=> <Text className={Style.text}>{data?.DPDCategory?.defaulter ? data.DPDCategory.defaulter :"-"}</Text>, 
            },
          ]
        },
    ];
    
    const handleRegionClick = (region) => {
        setSelectedRegion(region);
        setSelectedHubIndex(null)
    };

    const handleHubClick = (index) => {
        if (selectedRegion) {
          setSelectedHubIndex(index);
       }
       else{
        setSelectedHubIndex(null);
       }
      };

    const sortedData = selectedRegion
        ? [...analysisData].sort((a, b) => (a.region === selectedRegion ? -1 : 1))
        : analysisData;
    
    const dataSource = Array.isArray(sortedData)? sortedData.map((data, i) => ({
        key: i + 1,
        ...data
    })):[];
return (
    <Table
    bordered
    virtual
    className={Style.analysis}
    columns={columns}
    dataSource={dataSource}
    scroll={{
    // x: 2000,
    y: 460,
    }}
    pagination={{
      showSizeChanger:false
    }}
    />
  )
}

Analysis.propTypes={
  analysisData: PropTypes.array
}
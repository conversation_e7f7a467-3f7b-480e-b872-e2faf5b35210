import React from 'react'
import Style from "../_style.module.scss";
import { Flex, Typography } from 'antd';
import ARROW_ICON from "../../../assets/Images/arrow.svg";
import PropTypes from 'prop-types';

const {Text} = Typography;

export function SuggestionChat({title, desc, handleSuggestion}) {
  return (
    <Flex vertical className={Style.history_details} onClick={handleSuggestion}>
        {/* title */}
        <Flex justify='space-between'>
            <Text> 
                <span 
                style={{
                fontSize:"1.5rem", 
                lineHeight:0,
                marginInlineEnd:'10px'

                }}>&#x2022;</span>
                {title}
            </Text>
        </Flex>

        {/* desc */}
        <Flex gap={10}>
            <div style={{width:'10px'}}>
                <img src={ARROW_ICON} alt='arrow' style={{width:"100%", objectFit:"contain"}}/>
            </div>
            <Text className={Style.history_message}>{desc}</Text>
        </Flex>
   </Flex>
  )
}

SuggestionChat.propTypes={
    title: PropTypes.string,
    desc: PropTypes.string,
    handleSuggestion: PropTypes.func
}
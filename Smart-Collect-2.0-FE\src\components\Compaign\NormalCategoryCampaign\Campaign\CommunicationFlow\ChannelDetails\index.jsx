import { Flex, Typography, Table, Checkbox, Button } from "antd";
import Style from "./_style.module.scss";
import PropTypes from "prop-types";

const { Text } = Typography;

export default function ChannelDetails({
  basedOn,
  frequency,
  channelName,
  dayCount,
  daysValues,
  isAfter,
  onCheckboxChange,
  onSelectAllToggle,
  flowType,
}) {
  let columns;
  const isFlowTypePeriodic = flowType === "periodic";
  const isWeeklyPeriodic = isFlowTypePeriodic && dayCount === 7;
  const weekDays = [
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
    "Sunday",
  ];

  // Common "info" column (either basedOn or frequency depending on flowType)
  const infoColumn = {
    title: isFlowTypePeriodic ? "Frequency" : "Based on",
    dataIndex: "info",
    key: "info",
    render: () => (
      <Text strong>
        {isFlowTypePeriodic ? frequency : formatString(basedOn)}
      </Text>
    ),
    width: 150,
  };

  // Format the based on key
  const formatString = (str) => {
    // Custom mapping for known fields
    const fieldMappings = {
      promiseDate: "Promise Date",
      createdDate: "Created Date",
      NextEMIDate: "Next EMI Date",
      ClosingDate: "Closing Date",
    };

    // If the field exists in the mapping, return it
    if (fieldMappings[str]) return fieldMappings[str];

    // Otherwise, apply the generic formatting
    return str
      .replace(/([A-Z])/g, " $1") // Insert space before uppercase letters
      .replace(/^./, (s) => s.toUpperCase()) // Capitalize first letter
      .trim();
  };

  const generateColumns = (isReverse = false) => {
    return Array.from({ length: dayCount + 1 }, (_, index) => {
      const actualIndex = isReverse ? dayCount - index : index;

      return {
        title: actualIndex,
        dataIndex: `${actualIndex}`,
        key: `${actualIndex}`,
        render: () => (
          <Checkbox
            className={Style.custom_check}
            checked={daysValues[actualIndex] === true}
            onChange={onCheckboxChange.bind(null, actualIndex)}
          />
        ),
        width: 100,
      };
    }).filter((col) => !isFlowTypePeriodic || col.title !== 0);
  };

  const weekDayColumns = weekDays.map((day, idx) => ({
    title: `${day}(${idx})`,
    dataIndex: `${idx}`,
    key: `${idx}`,
    render: () => (
      <Checkbox
        className={Style.custom_check}
        checked={daysValues[idx] === true}
        onChange={() => onCheckboxChange(idx)}
      />
    ),
    width: 100,
  }));

  if (isWeeklyPeriodic) {
    columns = [infoColumn, ...weekDayColumns];
  } else {
    columns = isAfter
      ? [infoColumn, ...generateColumns(false)]
      : [...generateColumns(true), infoColumn];
  }

  // Build table data
  const dataSource = [
    { key: 1, info: isFlowTypePeriodic ? frequency : basedOn },
  ];
  return (
    <Flex vertical>
      <Flex justify="space-between" align="center" wrap>
        <Flex gap={10} align="baseline">
          <Text strong className={Style.text}>
            {channelName === "ivr" ? "IVR" : channelName}
          </Text>
          <Button
            type="link"
            className={Style.btn}
            onClick={() => {
              const allSelected = Object.values(daysValues).every(
                (val) => val === true
              );
              const newValue = !allSelected;
              onSelectAllToggle(newValue);
            }}
          >
            {Object.values(daysValues).every((val) => val)
              ? "Deselect all"
              : "Select all"}
          </Button>
        </Flex>
        <Text
          italic
          className={Style.text}
          style={{ color: "#087B1A", fontSize: "13px", fontWeight: 500 }}
        >{`*Please select the days you prefer to communicate with the customer.`}</Text>
      </Flex>

      <Table
        virtual
        className={Style.customTable}
        columns={columns}
        dataSource={dataSource}
        scroll={{ x: 500, y: 100 }}
        pagination={false}
      />
    </Flex>
  );
}

// Define the types
ChannelDetails.propTypes = {
  basedOn: PropTypes.string,
  frequency: PropTypes.string,
  channelName: PropTypes.string,
  dayCount: PropTypes.number,
  daysValues: PropTypes.object,
  isAfter: PropTypes.bool,
  onCheckboxChange: PropTypes.func,
  onSelectAllToggle: PropTypes.func,
  flowType: PropTypes.string,
};

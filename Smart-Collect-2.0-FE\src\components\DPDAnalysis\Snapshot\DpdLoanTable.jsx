import { Flex, message, Spin, Table, Typography } from "antd";
import React, { useCallback, useEffect, useState } from "react";
import {
  formatAmount,
  formatDigits,
  handleDownloadTable,
} from "../../../constant";
import Style from "./_style.module.scss";
import DOWNLOAD_IMG from "../../../assets/Images/download.svg";
import PropTypes from "prop-types";
const { Text } = Typography;

export function DpdLoanTable({ summaryData, isLoading }) {
  const [dpdLoanData, setDpdLoanData] = useState([]);

  const columns = [
    {
      title: "DPD Bucket",
      dataIndex: "bucket",
      sorter: (a, b) => a.bucket - b.bucket,
      render: (_, { bucket }) => <Text>{bucket || "-"}</Text>,
    },
    {
      title: "Loans",
      dataIndex: "loans",
      sorter: (a, b) => a.loans - b.loans,
      render: (_, { loans }) => <Text>{formatDigits(loans) || "0"}</Text>,
    },
    {
      title: "Pending Amount",
      dataIndex: "amount",
      sorter: (a, b) => a.amount - b.amount,
      render: (_, { amount }) => <Text>{formatAmount(amount) || "-"}</Text>,
    },
  ];

  const dataSource = Array.isArray(dpdLoanData)
    ? dpdLoanData.slice(0, -1).map((data, i) => ({
        key: i + 1,
        ...data,
      }))
    : [];

  const summaryContent = useCallback((pageData) => <TableSummaryFixedRow pageData={pageData} />,[]);

  useEffect(() => {
    setDpdLoanData(summaryData);
  }, [summaryData]);

  // Handle downloading
  const handleDownload = async () => {
    if (dpdLoanData.length === 0) {
    return  message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: "DPD loan",
        worksheetName: "DPD-loan",
        tableData: dpdLoanData,
      });
    }
  };
  return (
      <Spin tip={"Loading..."} spinning={isLoading}>
        <Flex justify="end" gap="1">
          <button className={Style.download_button} onClick={handleDownload}>
            <img
              src={DOWNLOAD_IMG}
              alt="download-button"
            />
          </button>
        </Flex>
        <Table
          bordered
          virtual
          className={Style.CustomTable}
          columns={columns}
          dataSource={dataSource}
          scroll={{
            x: 100,
            y: 160,
          }}
          pagination={false}
          summary={summaryContent}
        />
      </Spin>
  );
}
const TableSummaryFixedRow = ({ pageData }) => {
  if (pageData.length === 0) return null;

  const totalLoans = pageData.reduce((sum, row) => sum + (row.loans || 0), 0);
  const totalPendingAmount = pageData.reduce((sum, row) => sum + (row.amount || 0), 0);

  return (
    <Table.Summary fixed>
      <Table.Summary.Row>
        <Table.Summary.Cell index={0}>
          <strong>Total</strong>
        </Table.Summary.Cell>
        <Table.Summary.Cell index={1}>
          <strong>{formatDigits(totalLoans)}</strong>
        </Table.Summary.Cell>
        <Table.Summary.Cell index={2}>
          <strong>{formatAmount(totalPendingAmount)}</strong>
        </Table.Summary.Cell>
      </Table.Summary.Row>
    </Table.Summary>
  );
};

TableSummaryFixedRow.propTypes={
  pageData: PropTypes.array,
}
DpdLoanTable.propTypes = {
  summaryData:PropTypes.array, 
  isLoading:PropTypes.bool
}
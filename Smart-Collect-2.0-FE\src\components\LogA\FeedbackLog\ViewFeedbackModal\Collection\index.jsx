import React from "react";
import Style from "./_collection.module.scss";
import {
  Flex,
  InputNumber,
  Typography,
  DatePicker,
  Segmented} from "antd";
import dayjs from "dayjs";
import PropTypes from "prop-types";

const { Text } = Typography;

export function Collection({ feedbackData }) {
  return (
    <div className={Style.collection}>
      <Flex vertical gap={20} justify="center">
        {/* date */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Date:</Text>
          <DatePicker
            className={Style.date_picker}
            placeholder="dd/mm/yy"
            value={
              feedbackData?.CollectionDate
                ? dayjs(feedbackData.CollectionDate)
                : null
            }
            disabled
          />
        </Flex>

        {/* amount */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Amount:</Text>
          <InputNumber value={feedbackData.CollectionAmount || 0} disabled />
        </Flex>
        {feedbackData?.promise_amount && (
          <>
            {/* promise date */}
            <Flex justify="space-between" align="center">
              <Text className={Style.text}>Promise Date:</Text>
              <DatePicker
                className={Style.date_picker}
                placeholder="dd/mm/yy"
                value={
                  feedbackData?.promise_date
                    ? dayjs(feedbackData.promise_date)
                    : null
                }
                disabled
              />
            </Flex>

            {/* amount */}
            <Flex justify="space-between" align="center">
              <Text className={Style.text}>Promise Amount:</Text>
              <InputNumber value={feedbackData.promise_amount} disabled />
            </Flex>
          </>
        )}

        {/* mode */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Mode of Payment:</Text>

          <Segmented
            value={feedbackData.mode_of_payment}
            style={{
              marginBottom: 8,
            }}
            options={["Online", "Offline"]}
            disabled
          />
        </Flex>
      </Flex>
    </div>
  );
}

Collection.propTypes={
  feedbackData: PropTypes.object
}
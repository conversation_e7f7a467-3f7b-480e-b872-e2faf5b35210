import { Typography } from 'antd';
import PropTypes from 'prop-types';
import React from 'react'
import ReactApexChart from 'react-apexcharts';

const {Text} = Typography;

export function BarChart({data, isAccount, onBarClick}) {
    // Extract states and totals
    const stateNames = data.map(item => item.State);
    const totalAccounts = data.map(item => item.total);

    const options = {
        chart: {
          type: 'bar',
          toolbar: { show: false },
          events: {
            dataPointSelection: (event, chartContext, config) => {
              const index = config.dataPointIndex;
              const selectedState = data[index];
              if (onBarClick) onBarClick(selectedState);
            }
          },
        },
        xaxis: {
          categories: stateNames,
          title: { text: 'State' },
          labels: {
            style: { fontSize: '12px' }
          }
        },
        yaxis: {
          title: { text: isAccount ? 'Total Accounts' : 'Total Amount' },
          labels: {
            formatter: val => isAccount ? val : `${val.toLocaleString()}`
          }
        },
        plotOptions: {
          bar: {
            borderRadius: 0,
            columnWidth: '60px',
            distributed: false
          }
        },
        dataLabels: {
          enabled: true,
          style: {
            fontSize: '10px'
          },
          formatter: val => isAccount ? val : `₹${val.toLocaleString()}`,
        },
        colors: ['#0033A0'],
        responsive: [
          {
            breakpoint: 768, // screens smaller than 768px
            options: {
              plotOptions: {
                bar: {
                  columnWidth: '60%' // narrower bars on small screens
                }
              },
              chart: {
                height: 250
              },
              xaxis: {
                labels: {
                  rotate: -45,
                  style: { fontSize: '10px' }
                }
              },
              dataLabels: {
                style: { fontSize: '10px' }
              }
            }
          }
        ]
    };
    
    const series = [
        {
          name: isAccount ? 'Accounts' : 'Amount',
          data: totalAccounts
        }
    ];
    
    return <ReactApexChart options={options} series={series} type="bar" height={300} />
}

BarChart.propTypes={
  data: PropTypes.array,
  isAccount: PropTypes.bool,
  onBarClick: PropTypes.func
}
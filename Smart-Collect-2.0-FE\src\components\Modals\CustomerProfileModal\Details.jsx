import { Flex, Typography } from "antd";
import Style from "./_style.module.scss";
import { formattedTextToCapitalized } from "../../../constant";
import PropTypes from "prop-types";
const { Text } = Typography;

export function Details({ title, image, value }) {
  return (
    <Flex justify="space-between">
      <Flex gap={10} align="center">
        <div className={Style.icon_img}> {image}</div>
        <Text className={Style.title}>
          {formattedTextToCapitalized(title)}:
        </Text>
      </Flex>
      <Text className={Style.value}>{value}</Text>
    </Flex>
  );
}
Details.propTypes = {
  title: PropTypes.string,
  image: PropTypes.any,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
};

@import url("../../../../index.css");

$row-bottom:#E5E3E3;
$bottom-color:#949494;
$column-blue:#C5EDDF;
$column-yellow:#F2ECB8;

.customTable {
  border-radius: 20px;
  :global(.ant-table.ant-table-bordered) {
    :global(.ant-table-container) {
        padding: 1rem;
        overflow: auto;
        :global(.ant-table-content) {
          // Table header row styling
          :global(table > thead > tr) {
            &:nth-child(2) {
                 th {
                    text-align: center;
                    &:last-child{
                        color: #07223D;
                        border-inline-end: 0;
                    }
                }
            }
                        // First child
            &:nth-child(1){
                th{
                    &:last-child{
                        border-inline-end: 0;
                    }
                }
            }}
          }
  
          // Table header & body row border styles
          :global(table > thead > tr > th),
          :global(table > tbody > tr > td),
          :global(table > tfoot > tr > td) {
            background-color: white;
            border-inline-end: 1px solid $bottom-color;
            border-bottom: 1px solid $bottom-color !important;
            font-family: 'Kanit','Courier New', Courier, monospace;
            font-weight: 400;
          }

          // Body row column styling
          :global(table > tbody > tr) {

            // Body last rows td
            &:last-child{
              td{
                border-bottom: none !important;
              }
            }
            // First & Last Rows
            &:first-child,&:last-child {
              td {
                text-align: center;

                // First column
                &:first-child{
                  text-align: start;
                }

                //Last Column
                &:last-child {
                    border-inline-end: 0;
                  :global(.ant-typography){
                   color: #07223D;  
                  }
                }
                // Middle Columns -> Red
                &:not(:first-child):not(:last-child) {
                    :global(.ant-typography) {
                        padding: 0.5rem 1.5rem;
                        border-radius: 5px;
                        background-color: $column-blue !important;
                    }
                }
              }
            }
  
            // All other rows (except first & last)
            &:not(:first-child):not(:last-child) {
              td {
                text-align: center;

                // First column
                &:first-child{
                  text-align: start;
                }
                 //Last Column
                 &:last-child {
                    border-inline-end: 0;
                    :global(.ant-typography){
                     color: #07223D;  
                    }
                  }
                // Middle Columns -> Green
                &:not(:first-child):not(:last-child) {
                    :global(.ant-typography) {
                       padding: 0.5rem 1.5rem;
                       border-radius: 5px;
                       background-color: $column-yellow !important; 
                    }
                }
              }
            }
          }

          // Foot 
          :global(table> tfoot){
            tr{
              box-shadow: 0px 4px 4px 0px #0000001A inset;
              background-color: #E4F8F9;

              td{
                border-inline-end: 0;
                border-bottom: none !important;
                background-color: transparent;
  
                &:not(:first-child){
                  text-align: center;
                }
              }
            }
          }
    }

    // Styling all table cells typography
    :global(.ant-table-cell) {
      :global(.ant-typography) {
        color: #305496;
        font-weight: 600;
        word-break: normal;
      }
    }
    }
  }
  
// Responsive view
@media only screen and (max-width:768px) {
  .customTable{
      :global(.ant-table-thead >tr){
          th{
              padding: 0.5rem;
              font-size: 11px;
          }
      } 
      :global(.ant-table.ant-table-bordered) {
        :global(.ant-table-container) {
            // Styling all table cells typography
            :global(.ant-table-cell) {
              padding: 0.5rem;
              :global(.ant-typography) {
              font-size: 11px;
              }
            }
        }
      }
  }
}
import React, { useEffect, useState } from "react";
import Style from "./_collection.module.scss";
import {
  Button,
  Flex,
  InputNumber,
  Typography,
  DatePicker,
  Segmented,
  message,
} from "antd";
import dayjs from "dayjs";
import { AXIOS } from "../../../../apis/ho-Instance";
import { PaymentModal } from "./PaymentModal";
import SuccessModal from "./SuccessModal";
import { ReceiptModal } from "../../RecieptModal";
import PropTypes from "prop-types";

const { Text } = Typography;

export function Collection({
  overdueAmount,
  loanmstid,
  branchMstId,
  handleCross,
}) {
  const fixedAmount = overdueAmount;
  const dateFormatList = ["DD/MM/YYYY", "DD/MM/YY", "DD-MM-YYYY", "DD-MM-YY"];
  const [amount, setAmount] = useState(0);
  const [promiseAmount, setPromiseAmount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [payMode, setPayMode] = useState("Offline");
  const [showPromisePart, setShowPromisePart] = useState(false);
  const [collectionDate, setCollectionDate] = useState(
    dayjs("01/01/25", dateFormatList[0])
  );
  const [promiseDate, setPromiseDate] = useState(
    dayjs("01/01/25", dateFormatList[0])
  );
  const [showPayModal, setShowPayModal]  = useState(false);
  const [showSuccessModal, setShowSuccessModal]  = useState(false);
  const [receiptUrl, setReceiptUrl] = useState(null);
  const [showReceiptModal, setShowReceiptModal] = useState(false);

  // Handle date change
  const handleCollectionDateChange = (date) => {
    setCollectionDate(date ? dayjs(date) : null); // Ensure it's always a dayjs object or null
  };
  const handlePromiseDateChange = (date) => {
    setPromiseDate(date ? dayjs(date) : null); // Ensure it's always a dayjs object or null
  };

  const handleAmountChange = (value) => {
    const newAmount = Math.min(Number(value), fixedAmount);
    setAmount(newAmount);
    setShowPromisePart(newAmount < fixedAmount);
    setPromiseAmount(fixedAmount - newAmount);
  };

  const handlePromiseAmountChange = (value) => {
    setPromiseAmount(Math.min(Number(value), fixedAmount - amount));
  };

  const handleCollectionPromiseData = async ({sendPayInfo = false}) => {
    setShowPayModal(false);
    setLoading(true);
    const formattedCollectionDate = collectionDate
      ? dayjs(collectionDate).format("YYYY-MM-DD")
      : "";
    const formattedPromiseDate = promiseDate
      ? dayjs(promiseDate).format("YYYY-MM-DD")
      : "";
    // Conditional request data
    const requestData = showPromisePart
      ? {
          BranchMstID: branchMstId,
          LoanMstID: loanmstid,
          type: "Collection",
          collection_date: formattedCollectionDate,
          collection_amount: amount,
          promise_date: formattedPromiseDate,
          promise_amount: promiseAmount,
          mode_of_payment: payMode,
          sendPayInfo
        }
      : {
          BranchMstID: branchMstId,
          LoanMstID: loanmstid,
          type: "Collection",
          collection_date: formattedCollectionDate,
          collection_amount: amount,
          mode_of_payment: payMode,
        };

    try {
      const response = await AXIOS.post("feedback/", requestData);
     
      if (sendPayInfo) {
        // Show success modal
        setShowSuccessModal(true);
  
        // Close after 2 seconds
        setTimeout(() => {
          setShowSuccessModal(false);
          handleCross(); // your modal close handler
          // window.location.reload(); // reload if needed
        }, 2000);
      } else {
        // Just close without showing success modal
        handleCross();
      }

      // Inside your success block:
      if (response.data.receipt_url) {
        setReceiptUrl(response.data.receipt_url);
        setShowReceiptModal(true);
      }
      // Show success message
      message.success("Collection created successfully!");

      // Close the modal after 2 seconds
      // setTimeout(() => {
      //   handleCross();
      //   window.location.reload();
      // }, 500);
    } catch (error) {
      console.log("Error in Ready to Pay:", error);
      message.error("Promise not created!");
    } finally {
      setLoading(false);
    }
  };

  useEffect(()=>{ setAmount(overdueAmount) },[overdueAmount])
  return (
    <div className={Style.collection}>
      <Flex vertical gap={20} justify="center">
        {/* date */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Date:</Text>
          <DatePicker
            className={Style.date_picker}
            placeholder="dd/mm/yy"
            onChange={handleCollectionDateChange}
            // defaultValue={dayjs("01/01/25", dateFormatList[1])}
            format={dateFormatList}
            disabledDate={(current) => {
              const today = dayjs().endOf("day"); // Today’s date at the end of the day
              return current && current > today; // Disable only future dates
            }}
          />
        </Flex>

        {/* amount */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Amount:</Text>
          <InputNumber
            addonBefore={"₹"}
            formatter={(value) => {
              const num = Number(value);
              return isNaN(num) ? "" : num.toLocaleString("en-IN");
            }}
            parser={(value) => value?.replace(/[₹,]/g, "")}
            onChange={handleAmountChange}
            value={amount}
            min={0}
            max={fixedAmount}
          />
        </Flex>
        {showPromisePart && (
          <>
            {/* promise date */}
            <Flex justify="space-between" align="center">
              <Text className={Style.text}>Promise Date:</Text>
              <DatePicker
                className={Style.date_picker}
                placeholder="dd/mm/yy"
                onChange={handlePromiseDateChange}
                // defaultValue={dayjs("01/01/25", dateFormatList[1])}
                format={dateFormatList}
                disabledDate={(current) => {
                  return current && current < dayjs().add(1, "day").startOf("day"); // Disables today and past dates, allowing selection from tomorrow
                }}
              />
            </Flex>

            {/* amount */}
            <Flex justify="space-between" align="center">
              <Text className={Style.text}>Promise Amount:</Text>
              <InputNumber
                defaultValue={promiseAmount}
                addonBefore={"₹"}
                formatter={(value) => {
                  const num = Number(value);
                  return isNaN(num) ? "" : num.toLocaleString("en-IN");
                }}
                parser={(value) => value?.replace(/[₹,]/g, "")}
                onChange={handlePromiseAmountChange}
                value={promiseAmount}
                min={0}
                max={fixedAmount - amount}
                disabled
              />
            </Flex>
          </>
        )}

        {/* mode */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Mode of Payment:</Text>

          <Segmented
            value={payMode}
            style={{
              marginBottom: 8,
            }}
            onChange={setPayMode}
            options={["Online", "Offline"]}
          />
        </Flex>
        <Flex justify="center">
          <Button
            className={Style.feedback_button}
            onClick={()=>{
              showPromisePart? setShowPayModal(true):handleCollectionPromiseData({sendPayInfo:false})
            }}
            loading={loading}
          >
            {showPromisePart ? "Submit and Create Promise" :"Submit"}
          </Button>
        </Flex>
      </Flex>

      <PaymentModal modalStatus={showPayModal} handleSendPayInfo={handleCollectionPromiseData}/>
      <SuccessModal modalStatus={showSuccessModal}  setModalStatus={setShowSuccessModal}/>
      <ReceiptModal handleCancel={()=>setShowReceiptModal(false)} modalStatus={showReceiptModal} uri={receiptUrl}/>
    </div>
  );
}

Collection.propTypes={
  overdueAmount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  loanmstid: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  branchMstId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  handleCross: PropTypes.func,
}
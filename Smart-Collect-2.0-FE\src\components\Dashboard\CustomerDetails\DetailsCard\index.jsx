import { Flex, Typography } from "antd";
import React from "react";
import Style from "../_customer.module.scss";
import { Link } from "react-router";
import PropTypes from "prop-types";

const { Title, Text } = Typography;

export function DetailsCard({ imageSrc, amount, title,link }) {
  return (
    
  <Link to={link ?? "#"}>
    <Flex
      className={Style.customer_details}
      align="center"
      justify="space-between"
    >
      <div className={Style.icon_image}>
        <img src={imageSrc} alt={title}/>
      </div>
      <Title level={4} className={Style.amount}>
        {amount || 0}
      </Title>
      <Text className={Style.title}>{title}</Text>
    </Flex>
  </Link>
  );
}

DetailsCard.propTypes={
  imageSrc: PropTypes.string, 
  amount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]), 
  title: PropTypes.string,
  link: PropTypes.string
}
import { Button, Flex, message, Select, Table, Typography } from "antd";
import { useEffect, useState } from "react";
import AppHeader from "../AppHeader/index";
import Style from "./_style.module.scss";
import { Link, Outlet, useLocation } from "react-router";
import { AXIOS } from "../../apis/ho-Instance";
import DOWNLOAD_IMG from "../../assets/Images/download.svg";
import { handleDownloadTable, ALL_CACHE_NAMES } from "../../constant";
import {
  getCacheKey,
  getFromCache,
  storeToCache,
} from "../../utils/cacheHelper";
const { Text } = Typography;
const CACHE_NAME = ALL_CACHE_NAMES.DND_CACHE;

export default function DND() {
  const [tableData, setTableData] = useState([]);
  const [allData, setAllData] = useState([]);
  const [dndData, setDndData] = useState([]);
  const [disbursementOptions, setDisbursementOptions] = useState([]);
  const [loanTypeOptions, setLoanTypeOptions] = useState([]);
  const [mobileNumberOptions, setMobileNumberOptions] = useState([]);
  const [selectedDisbursement, setSelectedDisbursement] = useState([]);
  const [selectedLoanType, setSelectedLoanType] = useState([]);
  const [selectedMobileNumber, setSelectedMobileNumber] = useState([]);
  const [loadingAdd, setLoadingAdd] = useState(false);
  const [loadingRemove, setLoadingRemove] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const location = useLocation();

  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  // Fetch API data and set state
  const getAllTableData = async () => {
    // Build cache key for GET request (no params)
    const cacheKey = getCacheKey({ endpoint: "dnd/" });
    // Try to get cached data for instant UI update
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      // Use cached data for instant UI update, but do not skip API call
      setDndData(cachedData.dndData || []);
      setAllData(cachedData.allData || []);
      setTableData(cachedData.dndData || []);
      setDisbursementOptions(cachedData.disbursementOptions || []);
      setLoanTypeOptions(cachedData.loanTypeOptions || []);
      setMobileNumberOptions(cachedData.mobileNumberOptions || []);
    }
    try {
      const response = await AXIOS.get("dnd/");
      const formattedData = response.data.DND_true_data.map((item, index) => ({
        key: index + 1,
        customerId: item.CustomerID,
        disbursementId: item.DisbursementID,
        name: item.Name,
        status: item.Status?.toLowerCase(),
      }));

      setDndData(formattedData);
      setAllData(response.data.all_data);
      setTableData(formattedData);

      // Extract unique values for filters
      const uniqueDisbursement = [
        ...new Set(response.data.all_data.map((item) => item.DisbursementID)),
      ].map((id) => ({ value: id, label: id }));

      const uniqueLoanType = [
        ...new Set(response.data.all_data.map((item) => item.LoanType)),
      ].map((type) => ({ value: type, label: type }));

      const uniqueMobileNumbers = [
        ...new Set(response.data.all_data.map((item) => item.MobileNumber)),
      ].map((number) => ({ value: number, label: number }));

      setDisbursementOptions(uniqueDisbursement);
      setLoanTypeOptions(uniqueLoanType);
      setMobileNumberOptions(uniqueMobileNumbers);

      // Store the latest data in cache
      await storeToCache({
        cacheName: CACHE_NAME,
        key: cacheKey,
        data: {
          dndData: formattedData,
          allData: response.data.all_data,
          disbursementOptions: uniqueDisbursement,
          loanTypeOptions: uniqueLoanType,
          mobileNumberOptions: uniqueMobileNumbers,
        },
      });
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  useEffect(() => {
    getAllTableData();
  }, []);

  useEffect(() => {
    if (!allData.length) return;

    // Show dndData if available, otherwise fallback to allData
    if (
      selectedDisbursement.length === 0 &&
      selectedLoanType.length === 0 &&
      selectedMobileNumber.length === 0
    ) {
      setTableData(
        dndData.length > 0
          ? [...dndData]
          : allData.map((item, index) => ({
              key: index + 1,
              customerId: item.CustomerID,
              disbursementId: item.DisbursementID,
              name: item.Name,
              status: item.Status?.toLowerCase() || "-",
            }))
      );
    } else {
      const filteredData = allData
        .filter((item) => {
          return (
            (selectedDisbursement.length === 0 ||
              selectedDisbursement.includes(item.DisbursementID)) &&
            (selectedLoanType.length === 0 ||
              selectedLoanType.includes(item.LoanType)) &&
            (selectedMobileNumber.length === 0 ||
              selectedMobileNumber.includes(item.MobileNumber))
          );
        })
        .map((item, index) => ({
          key: index + 1,
          customerId: item.CustomerID,
          disbursementId: item.DisbursementID,
          name: item.Name,
          status: item.Status?.toLowerCase() || "-",
        }));

      setTableData(filteredData);
    }
  }, [
    selectedDisbursement,
    selectedLoanType,
    selectedMobileNumber,
    allData,
    dndData,
  ]);

  // Handle Disbursement   ID filter change

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      sorter: (a, b) => a.key - b.key,
      render: (_, data) => <Text>{data?.key}.</Text>,
    },
    {
      title: "Customer Id.",
      dataIndex: "customerId",
      sorter: (a, b) =>
        String(a.customerId || "").localeCompare(String(b.customerId || "")),

      render: (_, { customerId }) => (
        <Text>{customerId !== null ? customerId : "-"}</Text>
      ),
    },
    {
      title: "Disbursement Id",
      dataIndex: "disbursementId",
      sorter: (a, b) => a.disbursementId.localeCompare(b.disbursementId),
      render: (_, { disbursementId }) => (
        <Text>{disbursementId !== null ? disbursementId : "-"}</Text>
      ),
    },
    {
      title: "Name",
      dataIndex: "name",
      sorter: (a, b) => a.name.localeCompare(b.name),
      render: (_, { name }) => <Text className={Style.text}>{name}</Text>,
    },

    {
      title: "Status",
      dataIndex: "status",
      sorter: (a, b) => a.status.localeCompare(b.status),
      render: (_, { status }) => (
        <Text
          style={{
            color: `${status === "active" ? "#02951A" : "#0F2050"}`,
          }}
        >
          {status}
        </Text>
      ),
    },
  ];

  const rowSelection = { selectedRowKeys, onChange: onSelectChange };

  const handleAddDnd = async () => {
    setLoadingAdd(true);
    // Extract selected rows from tableData
    const selectedRows = tableData.filter((item) =>
      selectedRowKeys.includes(item.key)
    );
    // Extract CustomerIDs and DisbursementIDs from selected rows
    const customerIds = selectedRows.map((item) => item.customerId);
    const disbursementIds = selectedRows.map((item) => item.disbursementId);
    const requestData = {
      action: "add_dnd",
      CustomerID: customerIds,
      DisbursementID: disbursementIds,
    };
    try {
      await AXIOS.post("dnd/", requestData);
      // Show success message
      message.success("Add DND successfully!");
    } catch (error) {
      console.log("Error in Add DND:", error);
    } finally {
      setLoadingAdd(false);
    }
  };

  const handleRemoveDnd = async () => {
    setLoadingRemove(true);
    // Get unselected rows
    const selectedRows = tableData.filter((item) =>
      selectedRowKeys.includes(item.key)
    );
    // Extract CustomerIDs and DisbursementIDs
    const customerIds = selectedRows.map((item) => item.customerId);
    const disbursementIds = selectedRows.map((item) => item.disbursementId);

    const requestData = {
      action: "remove_dnd",
      CustomerID: customerIds,
      DisbursementID: disbursementIds,
    };

    try {
      await AXIOS.post("dnd/", requestData);
      message.success("Removed from DND successfully!");
    } catch (error) {
      console.error("Error in Remove DND:", error);
    } finally {
      setLoadingRemove(false);
    }
  };

  // this is use for the button
  const selectedRows = tableData.filter((item) =>
    selectedRowKeys.includes(item.key)
  );
  // Handle downloading
  const handleDownload = async () => {
    if (tableData.length === 0) {
      return message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: "DND",
        worksheetName: "dnd",
        tableData: tableData,
      });
    }
  };
  return location.pathname === "/ho/dnd" ? (
    <Flex gap={20} vertical className={Style.dnd_container}>
      <AppHeader title={"DND Customers"} />

      <Flex vertical gap={20}>
        {/* filters */}
        <Flex
          className={Style.box}
          gap={10}
          align="center"
          justify="space-between"
        >
          <Flex className={Style.fields_container}>
            <Flex align="center" gap={10}>
              <Text className={Style.text}>Disbursement ID:</Text>
              <Select
                mode="multiple"
                value={selectedDisbursement}
                onChange={setSelectedDisbursement}
                options={disbursementOptions}
                placeholder="Select"
                className={Style.custom_select}
              />
            </Flex>

            <Flex align="center" gap={10}>
              <Text className={Style.text}>Loan Type:</Text>
              <Select
                mode="multiple"
                value={selectedLoanType}
                onChange={setSelectedLoanType}
                options={loanTypeOptions}
                placeholder="Select"
                className={Style.custom_select}
              />
            </Flex>

            <Flex align="center" gap={10}>
              <Text className={Style.text}>Mobile Number:</Text>
              <Select
                mode="multiple"
                value={selectedMobileNumber}
                onChange={setSelectedMobileNumber}
                options={mobileNumberOptions}
                placeholder="Select"
                className={Style.custom_select}
              />
            </Flex>
          </Flex>

          <Flex gap={10}>
            <Link to={"/ho/dnd/upload-file"}>
              <Button className={Style.custom_button}>File Upload</Button>
            </Link>
            <div className={Style.download_button}>
              <input
                type="image"
                src={DOWNLOAD_IMG}
                alt="download-button"
                onClick={handleDownload}
              />
            </div>
          </Flex>
        </Flex>

        {/* Table */}
        <Table
          rowSelection={rowSelection}
          bordered
          virtual
          className={Style.customTable}
          columns={columns}
          dataSource={tableData}
          scroll={{
            x: 800,
            y: 300,
          }}
          pagination={{
            pageSize: 10,
            showSizeChanger: false,
          }}
        />

        {/* Add dnd */}

        <Flex justify="center" gap={20}>
          {selectedRows.length > 0 &&
            selectedRows.some((row) => row.status === "dnd") && (
              <Button
                className={Style.add_button}
                loading={loadingRemove}
                onClick={handleRemoveDnd}
              >
                Remove from DND
              </Button>
            )}

          {selectedRows.length > 0 &&
            selectedRows.some((row) => row.status === "active") && (
              <Button
                className={Style.add_button}
                loading={loadingAdd}
                onClick={handleAddDnd}
              >
                Add to DND
              </Button>
            )}
        </Flex>
      </Flex>
    </Flex>
  ) : (
    <Outlet />
  );
}

import { Table, Typography } from "antd";
import React, { useState } from "react";
import { formatAmount, formattedTextToCapitalized } from "../../constant";
import HISTORY_IMG from "../../assets/Images/history_icon.png";
import Style from "./_style.module.scss";
import { ConversionHistory } from "../Modals/ConversionHistory";
import PropTypes from "prop-types";

const { Text, Title } = Typography;

export function DashboardEngagementTable({ data }) {
  const [modalState, setModalState] = useState({
    history: { isOpen: false, data: null },
  });

  const openModal = ({ type, data }) => {
    setModalState((prev) => ({
      ...prev,
      [type]: { isOpen: true, data },
    }));
  };

  const closeModal = (type) => {
    setModalState((prev) => ({
      ...prev,
      [type]: { isOpen: false, data: null },
    }));
  };

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      sorter: (a, b) => a.key - b.key,
      render: (_, { key }) => <Text>{key}.</Text>,
    },
    {
      title: "Branch Name",
      dataIndex: "BranchName",
      sorter: (a, b) => a.BranchName.localeCompare(b.BranchName),
      render: (_, { BranchName }) => (
        <Text>{BranchName ? formattedTextToCapitalized(BranchName) : "-"}</Text>
      ),
    },
    {
      title: "Customer Id",
      dataIndex: "CustomerID",
      sorter: (a, b) => a.CustomerID - b.CustomerID, // Sorting applied here
      render: (value) => <Text className={Style.text}>{value}</Text>,
    },
    {
      title: "Customer Name",
      dataIndex: "CustomerName",
      sorter: (a, b) => a.CustomerName.localeCompare(b.CustomerName), // Sorting applied here
      render: (_, { CustomerName }) => (
        <Text>
          {CustomerName ? formattedTextToCapitalized(CustomerName) : "-"}
        </Text>
      ),
    },
    {
      title: "Disbursement Id",
      dataIndex: "DisbursementId",
      sorter: (a, b) => a.DisbursementId - b.DisbursementId,
      render: (_, { DisbursementId }) => <Text>{DisbursementId}</Text>,
    },
    {
      title: "Loan Type",
      dataIndex: "LoanType",
      sorter: (a, b) => a.LoanType.localeCompare(b.LoanType),
      render: (_, { LoanType }) => (
        <Text style={{ textTransform: "capitalize", fontSize: "13px" }}>
          {LoanType ?? "-"}
        </Text>
      ),
    },
    {
      title: "Overdue Amount",
      dataIndex: "Overdue_Amount",
      sorter: (a, b) =>
        parseFloat(a.Overdue_Amount) - parseFloat(b.Overdue_Amount), // Sorting applied here
      render: (value) => (
        <Text className={Style.blueText}>₹{formatAmount(value ?? 0)}</Text>
      ),
    },
    {
      title: "History",
      dataIndex: "history",
      fixed: "right",
      width: 78,
      render: (_, data) => (
        <button
          className={Style.icon_div}
          onClick={() => openModal({ type: "history", data })}
        >
          <div className={Style.icon_img}>
            <img src={HISTORY_IMG} alt="icon" />
          </div>
        </button>
      ),
    },
  ];

  const dataSource = Array.isArray(data)
    ? data.map((data, i) => ({
        key: i + 1,
        ...data,
      }))
    : [];
  return (
    <>
      <Table
        bordered
        virtual
        className={Style.CustomTable}
        columns={columns}
        dataSource={dataSource}
        scroll={{ x: 1000, y: 360 }}
        pagination={{
          showSizeChanger: false,
        }}
      />

      <ConversionHistory
        customerName={modalState.history.data?.CustomerName}
        loanMstId={modalState.history.data?.LoanMstID}
        modalStatus={modalState.history.isOpen}
        handleCancel={() => closeModal("history")}
      />
    </>
  );
}

DashboardEngagementTable.propTypes = {
  data: PropTypes.array,
};

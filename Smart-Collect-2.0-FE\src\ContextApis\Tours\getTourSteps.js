export const getTourSteps = ({
  menuRef,
  profileRef,
  logoutRef,
  dateWiseCustomerEngagementRef,
  customerEngagementRef,
  customerDetailsRef,
  customerDetailsGraphRef,
  payDetailsRef,
  components,
}) => [
  {
    title: "Click here to see more options",
    description: components.MenuTour,
    target: () => menuRef.current,
  },
  {
    title: "Profile",
    description: components.ProfileTour,
    target: () => profileRef.current,
  },
  {
    title: "Logout",
    description: components.LogoutTour,
    target: () => logoutRef.current,
  },
  {
    title: "Datewise Customer Engagement",
    description: components.DateWiseCustomerEngagementTour,
    target: () => dateWiseCustomerEngagementRef.current,
  },
  {
    title: "Customer Engagement",
    description: components.CustomerEngagementTour,
    target: () => customerEngagementRef.current,
  },
  {
    title: "Customer Details",
    description: components.CustomerDetailsTour,
    target: () => customerDetailsRef.current,
  },
  {
    title: "Customer Details",
    description: components.CustomerDetailsGraphTour,
    target: () => customerDetailsGraphRef.current,
  },
  {
    title: "Customer Details",
    description: components.PayDetailsTour,
    target: () => payDetailsRef.current,
  },
];


import React from 'react'
import {Flex, Typography} from "antd";
import Style from "../_style.module.scss";
import PropTypes from 'prop-types';

const {Text}  = Typography;

export function ActiveCard({imgSrc, title, value, textColor}) {
  return (
    <Flex justify='space-between' align='center' className={Style.active_card}>
        <div className={Style.img_div}>
            <img src={imgSrc} alt={title}/>
        </div>
        <Flex vertical gap={10} align='center' style={{flex:1}}>
            <Text className={Style.value}>{value}</Text>
            <Text className={Style.title} style={{color:textColor}}>{title}</Text>
        </Flex>
    </Flex>
  )
}

ActiveCard.propTypes={
  imgSrc: PropTypes.string,
  title: PropTypes.string,
  textColor: PropTypes.string,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
}
import { Flex, Modal, Tabs, Typography } from "antd";
import React, { useEffect, useState } from "react";
import FEEDBACK_IMG from "../../../assets/Images/fluent-mdl2_feedback.png";
import Style from "./_feedback.module.scss";
import { ReadyToPay } from "./ReadyToPay";
import { RefusedToPay } from "./RefusedToPay";
import { Collection } from "./Collection";
import { Other } from "./Other";
import { FeedbackButtons } from "./FeedbackButtons";
import { SMART_COLLECT_MENU_IDS } from "../../../constant";
import PropTypes from "prop-types";

const { Title } = Typography;

export function FeedbackModal({
  pageId,
  modalStatus = false,
  handleSubmit,
  handleCancel,
  loanmstid,
  branchMstId,
  overdueAmount,
  componentProp,
}) {
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);
  const [isReadyToPay] = useState(
    pageId === SMART_COLLECT_MENU_IDS.READY_TO_PAY
  );

  const [items, setItems] = useState([]);

  const handleChange = (key) => {
    console.log("change");
  };

  const handleOk = () => {
    setIsFeedbackModalOpen(false);
    handleSubmit();
  };

  const handleCross = () => {
    setIsFeedbackModalOpen(false);
    handleCancel();
  };

  const handleChangeItemDisplay = () => {
    const reorderedItems =
      pageId === SMART_COLLECT_MENU_IDS.REFUSED_TO_PAY
        ? [
            {
              key: "refusedToPay",
              label: "Refused to Pay",
              children: (
                <RefusedToPay
                  branchMstId={branchMstId}
                  loanmstid={loanmstid}
                  handleCross={handleCross}
                />
              ),
            },
            {
              key: "readyToPay",
              label: "Ready to Pay",
              children: (
                <ReadyToPay
                  overdueAmount={overdueAmount}
                  branchMstId={branchMstId}
                  loanmstid={loanmstid}
                  handleCross={handleCross}
                />
              ),
            },
            {
              key: "other",
              label: "Other",
              children: (
                <Other
                  branchMstId={branchMstId}
                  loanmstid={loanmstid}
                  handleCross={handleCross}
                />
              ),
            },
          ]
        : [
            {
              key: "readyToPay",
              label: "Ready to Pay",
              children: (
                <ReadyToPay
                  overdueAmount={overdueAmount}
                  branchMstId={branchMstId}
                  loanmstid={loanmstid}
                  handleCross={handleCross}
                />
              ),
            },
            {
              key: "refusedToPay",
              label: "Refused to Pay",
              children: (
                <RefusedToPay
                  branchMstId={branchMstId}
                  loanmstid={loanmstid}
                  handleCross={handleCross}
                />
              ),
            },
            isReadyToPay && {
              key: "collection",
              label: "Collection",
              children: (
                <Collection
                  branchMstId={branchMstId}
                  loanmstid={loanmstid}
                  overdueAmount={overdueAmount}
                  handleCross={handleCross}
                />
              ),
            },
            {
              key: "other",
              label: "Other",
              children: (
                <Other
                  branchMstId={branchMstId}
                  loanmstid={loanmstid}
                  handleCross={handleCross}
                />
              ),
            },
          ];

    setItems(reorderedItems.filter(Boolean));
  };

  useEffect(() => {
    handleChangeItemDisplay();
  }, [pageId, overdueAmount]);

  useEffect(() => {
    setIsFeedbackModalOpen(modalStatus);
  }, [modalStatus]);

  return (
    <Modal
      centered
      className={`${Style.feedback_modal} ${
        componentProp ? Style.customer_feedback_modal : ""
      }`}
      title={
        <Flex align="center" gap={5} justify="center">
          <div className={Style.icon_img}>
            <img src={FEEDBACK_IMG} alt="icon" />
          </div>
          <Title level={4} className={Style.feedback_title}>
            Feedback
          </Title>
        </Flex>
      }
      open={isFeedbackModalOpen}
      onOk={handleOk}
      onCancel={handleCross}
      footer={null}
    >
      <div className={Style.component_prop}>{componentProp}</div>
      <Flex vertical gap={10}>
        <FeedbackButtons pageId={pageId} loanmstid={loanmstid} />

        <Tabs defaultActiveKey="1" items={items} onChange={handleChange} />

        {/* <Flex justify="center">
          <Button
            className={Style.feedback_button}
            onClick={handlePostPromiseData}
          >
            {modalButtonText}
          </Button>
        </Flex> */}
      </Flex>
    </Modal>
  );
}

FeedbackModal.propTypes = {
  pageId: PropTypes.string,
  modalStatus: PropTypes.bool,
  handleSubmit: PropTypes.func,
  handleCancel: PropTypes.func,
  loanmstid: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  branchMstId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  overdueAmount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  componentProp: PropTypes.any,
};

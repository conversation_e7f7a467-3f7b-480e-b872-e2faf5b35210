import { Flex, message, Typography } from "antd";
import React, { useState, useEffect } from "react";
import AppHeader from "../AppHeader";
import {
  ALL_CACHE_NAMES,
  LOCAL_KEYS,
  SMART_COLLECT_MENU_IDS,
  SUMMARY_FILTERS,
  handleDownloadTable,
} from "../../constant";
import DOWNLOAD_IMG from "../../assets/Images/download.svg";
import { DashboardEngagementAllCards } from "../DashboardEngagementAllCards";
import { DashboardEngagementTable } from "../DashboardEngagementTable";
import Style from "./_style.module.scss";
import { AXIOS } from "../../apis/ho-Instance";
import ApplicationLoader from "../ApplicationLoader";
import {
  getCacheKey,
  getFromCache,
  getPostCacheKey,
  storeToCache,
} from "../../utils/cacheHelper";
import { encryptBody, isSameEncryptedBody } from "../../utils/crypto";

const { Text } = Typography;
const CACHE_NAME = ALL_CACHE_NAMES.CAMPAIGN_SUMMARY_DASHBOARD;

export default function SummaryAiDash() {
  const [tableData, setTableData] = useState([]);
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSwitchOn, setIsSwitchOn] = useState(false);
  const storedFilter = localStorage.getItem(SUMMARY_FILTERS.summaryFilter);
  const storedCampaign = localStorage.getItem(
    SUMMARY_FILTERS.selectedCampaignId
  );

  const handleFetchCampsAndBranchIds = async () => {
    try {
      const storedCampaigns = localStorage.getItem(
        SUMMARY_FILTERS.summaryCampaignIds
      );
      const storedBranches = localStorage.getItem(
        SUMMARY_FILTERS.summaryBranchIds
      );

      const campaignIds = storedCampaigns ? JSON.parse(storedCampaigns) : [];
      const branchIds = storedBranches ? JSON.parse(storedBranches) : [];

      return { campaignIds, branchIds };
    } catch (error) {
      console.error(
        "Error reading campaign/branch IDs from localStorage",
        error
      );
      return { campaignIds: [], branchIds: [] };
    }
  };

  // Apply the data
  const applyAiData = (data) => {
    setData(data);
    setTableData(data.ai_calls_history || []);
  };

  // Handle the get data
  const getAiCallsData = async () => {
    setIsLoading(true);
    const cacheKey = getCacheKey({ endpoint: "campaign-ai-calls-history/" });
    // Check cache
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      applyAiData(cachedData);
      setIsLoading(false);
    }
    const { campaignIds, branchIds } = await handleFetchCampsAndBranchIds();
    try {
      const response = await AXIOS.get("campaign-ai-calls-history/", {
        params: {
          campaign_mstids: campaignIds,
          branch_id: branchIds,
        },
      });
      if (response.status === 200) {
        // Store data in cache
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
        applyAiData(response.data);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle the filtered data
  const getAiCallsFilteredData = async ({
    fromDate,
    toDate,
    BranchName,
    conclusion,
    disbursementID,
    campaignMstIds,
  }) => {
    setIsLoading(true);
    const { campaignIds, branchIds } = await handleFetchCampsAndBranchIds();
    try {
      const body = {
        fromDate,
        toDate,
        BranchName: BranchName?.length ? BranchName : branchIds,
        conclusion,
        dpdfrom: 0,
        dpdto: 10000,
        DisbursementID: disbursementID,
        campaign_mstids: campaignMstIds ? [campaignMstIds] : campaignIds,
      };

      const cacheKey = getPostCacheKey({
        endpoint: "campaign-ai-calls-history/",
        body,
      });
      const encryptedOldBody = localStorage.getItem(
        LOCAL_KEYS.HO_CHANNEL_FILTER
      );
      const sameBody = isSameEncryptedBody({
        newBody: body,
        encryptedOldBody,
      });

      if (sameBody) {
        const cachedData = await getFromCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
        });
        if (cachedData) {
          applyAiData(cachedData);
          setIsLoading(false);
        }
      }
      const response = await AXIOS.post("campaign-ai-calls-history/", body);
      if (response.status === 200) {
        const encrypted = encryptBody(body);
        if (encrypted) {
          localStorage.setItem(LOCAL_KEYS.HO_CHANNEL_FILTER, encrypted);
        }
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
        applyAiData(response.data);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Apply filter and fetch data
  const applyFilters = async ({
    fromDate,
    toDate,
    BranchName,
    conclusion,
    disbursementID,
  }) => {
    await getAiCallsFilteredData({
      fromDate: fromDate,
      toDate: toDate,
      BranchName,
      conclusion,
      disbursementID,
      campaignMstIds: parseInt(storedCampaign),
    });
  };

  // Download the table data
  const handleDownload = async () => {
    if (tableData.length === 0 || !tableData) {
      return message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: "Ai Calls",
        worksheetName: "Summary-Ai-Calls",
        tableData: tableData,
      });
    }
  };

  // Clear the filter
  const ClearFilters = () => {
    getAiCallsData();
    localStorage.removeItem(SUMMARY_FILTERS.summaryFilter);
    localStorage.removeItem(LOCAL_KEYS.HO_CHANNEL_FILTER);
  };

  // On mount, check localStorage for saved filters
  useEffect(() => {
    if (storedFilter || storedCampaign) {
      const { fromDate, toDate, BranchName, conclusion, disbursementID } =
        storedFilter
          ? JSON.parse(storedFilter)
          : {
              fromDate: null,
              toDate: null,
              BranchName: [],
              conclusion: null,
              disbursementID: "",
            };
      const parsedCamp = storedCampaign ? parseInt(storedCampaign) : null;
      getAiCallsFilteredData({
        fromDate,
        toDate,
        BranchName,
        conclusion,
        disbursementID,
        campaignMstIds: parsedCamp,
      });
    } else {
      getAiCallsData();
    }
  }, []);
  return (
    <Flex gap={10} vertical>
      <AppHeader
        title={
          <Flex className={Style.title} gap={5}>
            <Text>
              AI Calls :{" "}
              <span style={{ color: "#0F2050" }}>{data.AI_Calls ?? 0}</span>
            </Text>
            <Text>
              Distinct Customers :{" "}
              <span style={{ color: "#0F2050" }}>
                {data.distinct_customers ?? 0}
              </span>
            </Text>
          </Flex>
        }
        isDashboard={false}
        isSwitchOn={isSwitchOn}
        setIsSwitchOn={setIsSwitchOn}
        ClearFilters={ClearFilters}
        applyFilters={applyFilters}
        isDashboardOtherPages={true}
        pageId={SMART_COLLECT_MENU_IDS.AI_CALLS}
      />

      <Flex gap={15} vertical>
        {isLoading ? (
          <ApplicationLoader />
        ) : (
          <div className={Style.details}>
            {!isSwitchOn ? (
              <DashboardEngagementAllCards data={tableData} />
            ) : (
              <Flex vertical gap={20} style={{ paddingBlockStart: "1rem" }}>
                <Flex justify="end">
                  <button
                    className={Style.download_button}
                    onClick={handleDownload}
                  >
                    <img src={DOWNLOAD_IMG} alt="download-button" />
                  </button>
                </Flex>
                <DashboardEngagementTable data={tableData} />{" "}
                {/* Moved inside */}
              </Flex>
            )}
          </div>
        )}
      </Flex>
    </Flex>
  );
}

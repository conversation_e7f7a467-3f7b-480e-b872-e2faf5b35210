import { useEffect, useState } from "react";
import Style from "./_style.module.scss";
import { Flex, Table, Typography } from "antd";
import {
  SMART_COLLECT_MENU_IDS,
  formatAmount,
  formattedTextToCapitalized,
  handleDateFormatter,
  ALL_CACHE_NAMES,
} from "../../constant";
import HISTORY_IMG from "../../assets/Images/history_icon.png";
import AppHeader from "../AppHeader";
import { AXIOS } from "../../apis/ho-Instance";
import ApplicationLoader from "../ApplicationLoader";
import { FeedbackModal } from "../Modals/FeedbackModal";
import FoAllocationCustomerModal from "../Modals/FoAllocationCustomerModal";
import {
  getCacheKey,
  getFromCache,
  storeToCache,
} from "../../utils/cacheHelper";

const { Text } = Typography;
const CACHE_NAME = ALL_CACHE_NAMES.FIELD_TODAY_ALLOCATION_CACHE;

export default function FieldTodayAllocation() {
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [modalState, setModalState] = useState({
    feedback: { isOpen: false, data: null },
    customer: { isOpen: false, data: null },
  });

  const openModal = ({ type, data }) => {
    setModalState((prev) => ({
      ...prev,
      [type]: { isOpen: true, data },
    }));
  };

  const closeModal = (type) => {
    setModalState((prev) => ({
      ...prev,
      [type]: { isOpen: false, data: null },
    }));
  };

  const getTodayAllocationData = async () => {
    setIsLoading(true);
    // Build cache key for GET request (no params)
    const cacheKey = getCacheKey({ endpoint: "today_allocation/" });
    // Try to get cached data for instant UI update
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setData(cachedData);
      setIsLoading(false);
    }
    try {
      const response = await AXIOS.get("today_allocation/");
      setData(response.data.TodayAllocation);
      // Store the latest data in cache
      await storeToCache({
        cacheName: CACHE_NAME,
        key: cacheKey,
        data: response.data.TodayAllocation,
      });
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    getTodayAllocationData();
  }, []);

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      render: (_, data) => <Text>{data.key}.</Text>,
    },
    {
      title: "Name",
      dataIndex: "Name",
      render: (value, data) => (
        <Text
          className={Style.name}
          onClick={() => openModal({ type: "customer", data })}
        >
          {value ? formattedTextToCapitalized(value) : "-"}
        </Text>
      ),
    },
    {
      title: "Contact",
      dataIndex: "Contact",
      render: (value) => <Text className={Style.contact}>{value || "--"}</Text>,
    },
    {
      title: "Overdue Amount",
      dataIndex: "Overdue_Amount",
      render: (value) => (
        <Text style={{ color: "#407BFF", fontWeight: 700 }}>
          Rs. {formatAmount(value || 0)}
        </Text>
      ),
    },
    {
      title: "Promise Amount",
      dataIndex: "PromiseAmount",
      render: (value) => (
        <Text style={{ color: "#407BFF", fontWeight: 700 }}>
          Rs. {formatAmount(value || 0)}
        </Text>
      ),
    },
    {
      title: "Promise Date",
      dataIndex: "PromiseDate",
      render: (value) => (
        <Text>{value ? handleDateFormatter(value) : "--"}</Text>
      ),
    },
    {
      title: "Feedback",
      dataIndex: "feedback",
      //   fixed: 'right',
      width: 100,
      render: (_, data) => (
        <div className={Style.icon_div}>
          <button
            className={Style.icon_img}
            onClick={() => openModal({ type: "feedback", data })}
          >
            <img src={HISTORY_IMG} alt="feedback" />
          </button>
        </div>
      ),
    },
    {
      title: "Status",
      dataIndex: "Status",
      render: (value) => (
        <Text style={{ color: !value == "Pending" ? "#1D9C58" : "#D01B1B" }}>
          {value}
        </Text>
      ),
    },
  ];

  const dataSource = Array.isArray(data)
    ? data?.map((d, i) => ({ key: i + 1, ...d }))
    : [];

  return (
    <Flex vertical gap={40}>
      <AppHeader title={"Today Allocation"} />
      <div>
        {isLoading ? (
          <ApplicationLoader />
        ) : (
          <Table
            virtual
            className={Style.customTable}
            columns={columns}
            dataSource={dataSource}
            scroll={{
              x: 1000,
              y: 460,
            }}
            pagination={{
              showSizeChanger: false,
            }}
          />
        )}
      </div>
      <FeedbackModal
        pageId={SMART_COLLECT_MENU_IDS.READY_TO_PAY}
        overdueAmount={modalState.feedback.data?.Overdue_Amount}
        branchMstId={modalState.feedback.data?.BranchMstID}
        loanmstid={modalState.feedback.data?.LoanMstID}
        modalStatus={modalState.feedback.isOpen}
        handleSubmit={() => closeModal("feedback")}
        handleCancel={() => closeModal("feedback")}
      />
      <FoAllocationCustomerModal
        data={modalState.customer.data}
        modalStatus={modalState.customer.isOpen}
        handleCancel={() => closeModal("customer")}
      />
    </Flex>
  );
}

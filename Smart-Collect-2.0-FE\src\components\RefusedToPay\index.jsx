import { Flex } from "antd";
import React, { useEffect, useMemo, useState } from "react";
import AppHeader from "../AppHeader";
import AppEngagements from "../AppEngagements";
import CustomerAllCards from "../CustomerAllCards";
import {
  ALL_CACHE_NAMES,
  LOCAL_KEYS,
  SMART_COLLECT_MENU_IDS,
} from "../../constant";
import { AXIOS } from "../../apis/ho-Instance";
import CustomersTable from "../CustomersTable";
import Style from "./_refused.module.scss";
import ApplicationLoader from "../ApplicationLoader";
import {
  getCacheKey,
  getFromCache,
  getPostCacheKey,
  storeToCache,
} from "../../utils/cacheHelper";
import { encryptBody, isSameEncryptedBody } from "../../utils/crypto";

const CACHE_NAME = ALL_CACHE_NAMES.DASHBOARD_CACHE;

export default function RefusedToPay() {
  const [isSwitchOn, setIsSwitchOn] = useState(false);
  const [customerData, setCustomerData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedReason, setSelectedReason] = useState(null);

  // Retrieve dates from localStorage
  const storedFilter = localStorage.getItem("filterDate");
  const storedDpd = localStorage.getItem("DashboardDpdCategory");

  const handleGetDenialsData = async () => {
    setLoading(true);
    const cacheKey = getCacheKey({ endpoint: "denials/" });
    // Check cache
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setCustomerData(cachedData);
      setLoading(false);
    }
    try {
      const response = await AXIOS.get("denials/");
      if (response.status === 200) {
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
        setCustomerData(response.data);
      }
    } catch (error) {
      console.log("Error in Ready to pay", error?.message);
    } finally {
      setLoading(false);
    }
  };

  const handleRefusedtoPay = async ({
    fromDate,
    toDate,
    BranchName,
    dpdRange,
    disbursementID,
    reasons = null,
  }) => {
    setLoading(true);
    const body = {
      from_date: fromDate,
      to_date: toDate,
      branch_id: BranchName,
      dpdfrom: dpdRange?.start ?? -1,
      dpdto: dpdRange?.end ?? -1,
      disbursementids: disbursementID,
      reason: reasons,
    };
    const cacheKey = getPostCacheKey({ endpoint: "denials/", body });
    const encryptedOldBody = localStorage.getItem(LOCAL_KEYS.HO_PAY_FILTER);
    const sameBody = isSameEncryptedBody({
      newBody: body,
      encryptedOldBody,
    });

    if (sameBody) {
      const cachedData = await getFromCache({
        cacheName: CACHE_NAME,
        key: cacheKey,
      });
      if (cachedData) {
        setCustomerData(cachedData);
        setLoading(false);
      }
    }

    try {
      const response = await AXIOS.post("denials/", body);
      // Check the response
      if (response.status === 200) {
        const encrypted = encryptBody(body);
        if (encrypted) {
          localStorage.setItem(LOCAL_KEYS.HO_PAY_FILTER, encrypted);
        }
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
        setCustomerData(response.data);
      }
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoading(false);
    }
  };

  // Apply filter and fetch data
  const applyFilters = async ({
    fromDate,
    toDate,
    BranchName,
    disbursementID,
  }) => {
    await handleRefusedtoPay({
      fromDate: fromDate,
      toDate: toDate,
      BranchName,
      dpdRange: JSON.parse(storedDpd),
      disbursementID,
      reasons: selectedReason,
    });
  };

  // Clear filters and fetch data
  const ClearFilters = () => {
    handleGetDenialsData();
    localStorage.removeItem("filterDate");
    localStorage.removeItem(LOCAL_KEYS.HO_PAY_FILTER);
    setSelectedReason(null);
  };

  const parsedFilter = useMemo(() => {
    try {
      return storedFilter ? JSON.parse(storedFilter) : null;
    } catch {
      return null;
    }
  }, [storedFilter]);

  const parsedDpd = useMemo(() => {
    try {
      return storedDpd ? JSON.parse(storedDpd) : { start: -1, end: -1 };
    } catch {
      return { start: -1, end: -1 };
    }
  }, [storedDpd]);

  useEffect(() => {
    if (storedFilter || storedDpd) {
      const { fromDate, toDate, BranchName, disbursementID } = storedFilter
        ? JSON.parse(storedFilter)
        : {
            fromDate: null,
            toDate: null,
            BranchName: null,
            disbursementID: "",
            reasons: null,
          };
      const parsedDpd = storedDpd
        ? JSON.parse(storedDpd)
        : { start: -1, end: -1 };
      handleRefusedtoPay({
        fromDate,
        toDate,
        BranchName,
        dpdRange: parsedDpd,
        disbursementID,
      });
    } else {
      handleGetDenialsData();
    }
  }, []);

  useEffect(() => {
    if (selectedReason === null) return; // Avoid initial duplicate call

    let params = {
      fromDate: null,
      toDate: null,
      BranchName: [],
      dpdRange: { start: -1, end: -1 },
      disbursementID: "",
      reasons: selectedReason ?? null,
    };
    if (storedFilter || storedDpd) {
      const { fromDate, toDate, BranchName, disbursementID } = storedFilter
        ? JSON.parse(storedFilter)
        : {
            fromDate: null,
            toDate: null,
            BranchName: null,
            disbursementID: "",
          };
      const parsedDpd = storedDpd
        ? JSON.parse(storedDpd)
        : { start: -1, end: -1 };
      params = {
        fromDate: fromDate,
        toDate: toDate,
        BranchName: BranchName,
        disbursementID: disbursementID,
        dpdRange: parsedDpd,
        reasons: selectedReason,
      };
    }
    handleRefusedtoPay(params);
  }, [parsedFilter, parsedDpd, selectedReason]);

  return (
    <Flex vertical gap={15}>
      {/* Header */}
      <AppHeader
        title={`Refused to pay: ${customerData.length}`}
        isDashboard={false}
        isSwitchOn={isSwitchOn}
        isDashboardOtherPages={true}
        pageId={SMART_COLLECT_MENU_IDS.REFUSED_TO_PAY}
        selectedReason={selectedReason}
        setIsSwitchOn={setIsSwitchOn}
        setSelectedReason={setSelectedReason}
        applyFilters={applyFilters}
        ClearFilters={ClearFilters}
      />

      <Flex vertical gap={15}>
        {/* app engagements */}
        <AppEngagements />

        {/* display the data */}
        {loading ? (
          <ApplicationLoader />
        ) : (
          <div className={Style.details}>
            {!isSwitchOn ? (
              <CustomerAllCards
                pageId={SMART_COLLECT_MENU_IDS.REFUSED_TO_PAY}
                customerData={customerData}
                modalButtonText={"Feedback"}
                isModal={true}
              />
            ) : (
              <CustomersTable
                title={"Refused to pay"}
                customerData={customerData}
                pageId={SMART_COLLECT_MENU_IDS.REFUSED_TO_PAY}
                modalButtonText={"Feedback"}
              />
            )}
          </div>
        )}
      </Flex>
    </Flex>
  );
}

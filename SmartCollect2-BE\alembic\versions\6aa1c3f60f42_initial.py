"""initial

Revision ID: 6aa1c3f60f42
Revises: 
Create Date: 2025-04-22 14:52:16.574044

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '6aa1c3f60f42'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('AccountSummaryHistory',
    sa.Column('HistoryID', sa.Integer(), nullable=False),
    sa.Column('BranchMstID', sa.Integer(), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('CustomerMstID', sa.Integer(), nullable=False),
    sa.Column('CustomerCode', sa.String(length=255), nullable=False),
    sa.Column('CustomerName', sa.String(length=255), nullable=False),
    sa.Column('MobileNumber', sa.String(length=15), nullable=False),
    sa.Column('DisbursementID', sa.String(length=255), nullable=False),
    sa.Column('DisbursementDate', sa.Date(), nullable=False),
    sa.Column('DisbursementAmt', sa.Numeric(precision=18, scale=2), nullable=False),
    sa.Column('LoanType', sa.String(length=50), nullable=False),
    sa.Column('CurrentBalance', sa.Numeric(precision=18, scale=2), nullable=False),
    sa.Column('OverDueAmt', sa.Numeric(precision=18, scale=2), nullable=False),
    sa.Column('PrincipleOutstanding', sa.Numeric(precision=18, scale=2), nullable=False),
    sa.Column('InterestOutstanding', sa.Numeric(precision=18, scale=2), nullable=False),
    sa.Column('TotalOutstanding', sa.Numeric(precision=18, scale=2), nullable=False),
    sa.Column('PrinciplePending', sa.Numeric(precision=18, scale=2), nullable=False),
    sa.Column('InterestPending', sa.Numeric(precision=18, scale=2), nullable=False),
    sa.Column('TotalPending', sa.Numeric(precision=18, scale=2), nullable=False),
    sa.Column('IsActive', sa.Boolean(), server_default=sa.text('true'), nullable=False),
    sa.Column('EMIAmount', sa.Numeric(precision=18, scale=2), nullable=False),
    sa.Column('LoanMstID', sa.Integer(), nullable=False),
    sa.Column('Gender', sa.String(length=10), nullable=True),
    sa.Column('DateOfBirth', sa.Date(), nullable=True),
    sa.Column('DPD', sa.Integer(), nullable=True),
    sa.Column('LoanClassification', sa.String(length=50), nullable=True),
    sa.Column('LastPaymentDate', sa.Date(), nullable=True),
    sa.Column('LastCollectedAmount', sa.Numeric(precision=18, scale=2), nullable=True),
    sa.Column('ClosingDate', sa.Date(), nullable=True),
    sa.Column('NextEMIDate', sa.Date(), nullable=True),
    sa.Column('CreatedDate', sa.Date(), nullable=True),
    sa.Column('Branch_id', sa.String(length=255), nullable=True),
    sa.Column('Branch', sa.String(length=255), nullable=True),
    sa.Column('Region_id', sa.String(length=255), nullable=True),
    sa.Column('Region', sa.String(length=255), nullable=True),
    sa.Column('State_id', sa.String(length=255), nullable=True),
    sa.Column('State', sa.String(length=255), nullable=True),
    sa.Column('CollectionOfficerID', sa.String(), nullable=True),
    sa.Column('CollectionOfficerName', sa.String(), nullable=True),
    sa.Column('groupid', sa.Integer(), nullable=True),
    sa.Column('groupname', sa.String(), nullable=True),
    sa.Column('CurrentDPD', sa.Integer(), nullable=True),
    sa.Column('LastMonthDPD', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('HistoryID', name='AccountSummaryHistory_pkey')
    )
    op.create_table('ActiveConversation',
    sa.Column('uuid', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.String(length=20), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('last_updated', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('conversation_status', sa.String(length=20), server_default=sa.text("'active'::character varying"), nullable=False),
    sa.Column('form_data', sa.Text(), nullable=True),
    sa.Column('title', sa.String(), nullable=True),
    sa.CheckConstraint("conversation_status::text = ANY (ARRAY['active'::character varying, 'completed'::character varying, 'abandoned'::character varying]::text[])", name='ActiveConversation_conversation_status_check'),
    sa.PrimaryKeyConstraint('uuid', name='ActiveConversation_pkey')
    )
    op.create_table('BankMst',
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('BankName', sa.String(length=255), nullable=False),
    sa.Column('OnBoardingDate', sa.Date(), server_default=sa.text('CURRENT_DATE'), nullable=True),
    sa.Column('IsActive', sa.Boolean(), server_default=sa.text('true'), nullable=True),
    sa.Column('UpdatedDate', sa.Date(), nullable=True),
    sa.Column('Remarks', sa.Text(), nullable=True),
    sa.Column('SubscriptionID', sa.Integer(), nullable=True),
    sa.Column('SubscriptionType', sa.String(length=50), nullable=True),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.Column('WhatsApp', sa.Boolean(), nullable=True),
    sa.Column('VoiceBot', sa.Boolean(), nullable=True),
    sa.Column('Blaster', sa.Boolean(), nullable=True),
    sa.Column('IVR', sa.Boolean(), nullable=True),
    sa.Column('SMS', sa.Boolean(), nullable=True),
    sa.Column('Is_UPI', sa.Boolean(), nullable=True),
    sa.Column('Key_ID', sa.String(length=255), nullable=True),
    sa.Column('Secret_Key', sa.String(length=255), nullable=True),
    sa.PrimaryKeyConstraint('BankMstID', name='TBL_BankMst_pkey')
    )
    op.create_table('CollectionFile',
    sa.Column('CollectionFileID', sa.Integer(), nullable=False),
    sa.Column('DisbursementID', sa.String(length=255), nullable=False),
    sa.Column('PrincipleCollected', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('InterestCollected', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('InstStartDate', sa.Date(), nullable=True),
    sa.Column('CollectedAmount', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('CollectionDate', sa.Date(), nullable=True),
    sa.Column('BMID', sa.Integer(), nullable=True),
    sa.Column('POS', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('LoanType', sa.String(length=255), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.Column('BranchMstID', sa.Integer(), nullable=True),
    sa.Column('CreatedDate', sa.Date(), nullable=True),
    sa.Column('BranchName', sa.String(), nullable=True),
    sa.Column('CustomerId', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('CollectionFileID', name='CollectionFile_pkey'),
    sa.UniqueConstraint('DisbursementID', 'BankMstID', name='collectionfile_unique')
    )
    op.create_table('CommunicationMapping',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('WhatsApp', sa.Boolean(), nullable=True),
    sa.Column('VoiceBot', sa.Boolean(), nullable=True),
    sa.Column('Blaster', sa.Boolean(), nullable=True),
    sa.Column('IVR', sa.Boolean(), nullable=True),
    sa.Column('SMS', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('id', name='CommunicationMapping_pkey')
    )
    op.create_table('ComponentMst',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('Designation', sa.String(length=100), nullable=False),
    sa.Column('Screens', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.PrimaryKeyConstraint('id', name='ComponentMst_pkey')
    )
    op.create_table('ConversationHistory',
    sa.Column('uuid', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.String(length=20), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('completed_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('full_conversation', sa.Text(), nullable=False),
    sa.Column('form_data', sa.Text(), nullable=True),
    sa.Column('title', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('uuid', name='ConversationHistory_pkey')
    )
    op.create_table('PaymentLogs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('CustMstID', sa.Integer(), nullable=True),
    sa.Column('QR_ID', sa.String(), nullable=True),
    sa.Column('Payment_Link', sa.String(length=200), nullable=True),
    sa.Column('QR_Link', sa.String(length=200), nullable=True),
    sa.Column('DateTime', sa.DateTime(), nullable=True),
    sa.Column('Status', sa.String(length=200), nullable=True),
    sa.Column('ResponseID', sa.Integer(), nullable=True),
    sa.Column('Link_ID', sa.String(length=200), nullable=True),
    sa.PrimaryKeyConstraint('id', name='PaymentLogs_pkey')
    )
    op.create_table('RecordingSummary',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('Recording', sa.String(), nullable=True),
    sa.Column('Summarization', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id', name='RecordingSummary_pkey')
    )
    op.create_table('Transaction_Logs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('QR_ID', sa.String(), nullable=True),
    sa.Column('Link_ID', sa.String(), nullable=True),
    sa.Column('Payment_ID', sa.String(), nullable=True),
    sa.Column('Amount', sa.Integer(), nullable=True),
    sa.Column('Status', sa.String(), nullable=True),
    sa.Column('Method', sa.String(), nullable=True),
    sa.Column('VPA', sa.String(), nullable=True),
    sa.Column('Contact', sa.String(), nullable=True),
    sa.Column('Payment_Cust_ID', sa.String(), nullable=True),
    sa.Column('Transaction_date', sa.DateTime(), nullable=True),
    sa.Column('Body', sa.JSON(), nullable=True),
    sa.Column('DateTime', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
    sa.PrimaryKeyConstraint('id', name='Transaction_Logs_pkey')
    )
    op.create_table('WrongNumberHistory',
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('CustomerMstID', sa.Integer(), nullable=True),
    sa.Column('LoanMstID', sa.Integer(), nullable=True),
    sa.Column('PreviousNumber', sa.CHAR(length=15), nullable=True),
    sa.Column('UpdatedNumber', sa.CHAR(length=15), nullable=True)
    )
    op.create_table('auth_group',
    sa.Column('id', sa.Integer(), sa.Identity(always=False, start=1, increment=1, minvalue=1, maxvalue=**********, cycle=False, cache=1), nullable=False),
    sa.Column('name', sa.String(length=150), nullable=False),
    sa.PrimaryKeyConstraint('id', name='auth_group_pkey'),
    sa.UniqueConstraint('name', name='auth_group_name_key')
    )
    op.create_index('auth_group_name_a6ea08ec_like', 'auth_group', ['name'], unique=False)
    op.create_table('campaign_blasterusertemplate',
    sa.Column('BlasterUserTemplateID', sa.Integer(), sa.Identity(always=False, start=1, increment=1, minvalue=1, maxvalue=**********, cycle=False, cache=1), nullable=False),
    sa.Column('TemplateName', sa.String(length=255), nullable=False),
    sa.Column('TemplateBody', sa.Text(), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('EmployeeMstID', sa.Integer(), nullable=False),
    sa.Column('VariableNumber', sa.Integer(), nullable=False),
    sa.Column('VariableField', sa.String(length=255), nullable=False),
    sa.PrimaryKeyConstraint('BlasterUserTemplateID', name='campaign_blasterusertemplate_pkey')
    )
    op.create_table('campaign_count',
    sa.Column('count', sa.BigInteger(), nullable=True)
    )
    op.create_table('commflow_id',
    sa.Column('CommFlowID', sa.Integer(), nullable=True)
    )
    op.create_table('communication_type',
    sa.Column('CommunicationType', sa.String(length=100), nullable=True)
    )
    op.create_table('dashboard_data',
    sa.Column('json_agg', sa.JSON(), nullable=True)
    )
    op.create_table('django_content_type',
    sa.Column('id', sa.Integer(), sa.Identity(always=False, start=1, increment=1, minvalue=1, maxvalue=**********, cycle=False, cache=1), nullable=False),
    sa.Column('app_label', sa.String(length=100), nullable=False),
    sa.Column('model', sa.String(length=100), nullable=False),
    sa.PrimaryKeyConstraint('id', name='django_content_type_pkey'),
    sa.UniqueConstraint('app_label', 'model', name='django_content_type_app_label_model_76bd3d3b_uniq')
    )
    op.create_table('django_migrations',
    sa.Column('id', sa.BigInteger(), sa.Identity(always=False, start=1, increment=1, minvalue=1, maxvalue=9223372036854775807, cycle=False, cache=1), nullable=False),
    sa.Column('app', sa.String(length=255), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('applied', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id', name='django_migrations_pkey')
    )
    op.create_table('django_session',
    sa.Column('session_key', sa.String(length=40), nullable=False),
    sa.Column('session_data', sa.Text(), nullable=False),
    sa.Column('expire_date', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('session_key', name='django_session_pkey')
    )
    op.create_index('django_session_expire_date_a5c62663', 'django_session', ['expire_date'], unique=False)
    op.create_index('django_session_session_key_c0390e0f_like', 'django_session', ['session_key'], unique=False)
    op.create_table('dynamic_sql',
    sa.Column('generated_query', sa.Text(), nullable=True)
    )
    op.create_table('engagement_data',
    sa.Column('json_agg', sa.JSON(), nullable=True)
    )
    op.create_table('filter_count',
    sa.Column('count', sa.BigInteger(), nullable=True)
    )
    op.create_table('where_conditions',
    sa.Column('string_agg', sa.Text(), nullable=True)
    )
    op.create_table('CampaignMst',
    sa.Column('CampaignMstID', sa.Integer(), nullable=False),
    sa.Column('Name', sa.String(length=255), nullable=False),
    sa.Column('IsActive', sa.Boolean(), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.Column('CreatedDate', sa.Date(), nullable=True),
    sa.Column('Priority', sa.String(), nullable=True),
    sa.Column('Status', sa.String(), nullable=True),
    sa.Column('CampaignType', sa.String(), nullable=True),
    sa.Column('Language', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='CampaignMst_FK_BankMstID'),
    sa.PrimaryKeyConstraint('CampaignMstID', name='CampaignMst_pkey'),
    sa.UniqueConstraint('Name', 'BankMstID', name='campaignmst_unique')
    )
    op.create_table('CommFlowMst',
    sa.Column('CommFlowMstID', sa.Integer(), nullable=False),
    sa.Column('BankName', sa.String(length=255), nullable=False),
    sa.Column('CreatedDate', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('IsActive', sa.Boolean(), server_default=sa.text('true'), nullable=False),
    sa.Column('Type', sa.String(length=50), nullable=False),
    sa.Column('ColumnName', sa.String(length=255), nullable=True),
    sa.Column('BasedOnTable', sa.String(), nullable=True),
    sa.Column('BasedOnColumn', sa.String(length=255), nullable=True),
    sa.Column('FlowName', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='CommFlowMst_fk_BankMstID'),
    sa.PrimaryKeyConstraint('CommFlowMstID', name='CommFlowMst_pkey')
    )
    op.create_table('ConversationMessage',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('conversation_id', sa.Uuid(), nullable=False),
    sa.Column('user_message', sa.Text(), nullable=False),
    sa.Column('ai_response', sa.Text(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('is_edited', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['conversation_id'], ['ActiveConversation.uuid'], name='ConversationMessage_conversation_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='ConversationMessage_pkey')
    )
    op.create_table('DPDCategory',
    sa.Column('DPDCategoryID', sa.Integer(), nullable=False),
    sa.Column('RangeStart', sa.Integer(), nullable=False),
    sa.Column('RangeEnd', sa.Integer(), nullable=False),
    sa.Column('CategoryLabel', sa.String(length=255), nullable=False),
    sa.Column('CreatedOn', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('UpdatedOn', sa.DateTime(), nullable=True),
    sa.Column('DeletedOn', sa.DateTime(), nullable=True),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='DPDCategory_BankMstID_fkey'),
    sa.PrimaryKeyConstraint('DPDCategoryID', name='DPDCategory_pkey')
    )
    op.create_table('DesignationMst',
    sa.Column('DesignationMstID', sa.Integer(), nullable=False),
    sa.Column('DesignationName', sa.String(length=255), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='DesignationMst_BankMstID_fkey'),
    sa.PrimaryKeyConstraint('DesignationMstID', name='DesignationMst_pkey')
    )
    op.create_table('DisbursementFile',
    sa.Column('ID', sa.Integer(), nullable=False),
    sa.Column('CustomerId', sa.Integer(), nullable=False),
    sa.Column('DisbursementID', sa.String(length=255), nullable=False),
    sa.Column('LoanType', sa.String(length=100), nullable=True),
    sa.Column('DATAOPEN', sa.Date(), nullable=True),
    sa.Column('DateClose', sa.Date(), nullable=True),
    sa.Column('CustomerName', sa.String(length=255), nullable=True),
    sa.Column('DisburseAmount', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('RateOfInterest', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('RepaymentTenure', sa.Integer(), nullable=True),
    sa.Column('EMIAmount', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('PaymentFrequency', sa.Integer(), nullable=True),
    sa.Column('NumberOfDaysPastDue', sa.Integer(), nullable=True),
    sa.Column('BUID', sa.Integer(), nullable=True),
    sa.Column('BU NAME', sa.String(length=255), nullable=True),
    sa.Column('BMID', sa.Integer(), nullable=True),
    sa.Column('Gender', sa.String(length=10), nullable=True),
    sa.Column('Mobile No', sa.String(length=15), nullable=True),
    sa.Column('Alternate Mobile No', sa.String(length=15), nullable=True),
    sa.Column('PANNo', sa.String(length=10), nullable=True),
    sa.Column('EmailID', sa.String(length=255), nullable=True),
    sa.Column('Address', sa.Text(), nullable=True),
    sa.Column('Pincode', sa.String(length=6), nullable=True),
    sa.Column('InstStartDate', sa.Date(), nullable=True),
    sa.Column('DateOfBirth', sa.Date(), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.Column('CollectionOfficerID', sa.String(), nullable=True),
    sa.Column('CollectionOfficerName', sa.String(), nullable=True),
    sa.Column('BranchName', sa.String(), nullable=True),
    sa.Column('BranchCode', sa.String(), nullable=True),
    sa.Column('CustomerCode', sa.String(), nullable=True),
    sa.Column('DND', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('groupid', sa.String(), nullable=True),
    sa.Column('groupname', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='FK_DisbursementFile_BankMstID', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('ID', name='DisbursementFile_pkey'),
    sa.UniqueConstraint('DisbursementID', 'BankMstID', name='disbursementfile_unique')
    )
    op.create_table('Filters',
    sa.Column('FiltersID', sa.Integer(), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('Column', sa.String(length=255), nullable=False),
    sa.Column('Comparison', sa.String(length=50), nullable=False),
    sa.Column('Value', sa.String(length=255), nullable=False),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='FilterMst_fk_BankID', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('FiltersID', name='Filters_pkey')
    )
    op.create_table('GeographyMst',
    sa.Column('ID', sa.Integer(), nullable=False),
    sa.Column('BUCode', sa.String(length=50), nullable=False),
    sa.Column('BUName', sa.String(length=255), nullable=False),
    sa.Column('BUType', sa.String(length=50), nullable=False),
    sa.Column('ReportingBUID', sa.String(length=50), nullable=True),
    sa.Column('ReportingBUType', sa.String(length=50), nullable=True),
    sa.Column('BUMobile', sa.String(length=15), nullable=True),
    sa.Column('BUEmail', sa.String(length=255), nullable=True),
    sa.Column('IsActive', sa.Boolean(), server_default=sa.text('true'), nullable=True),
    sa.Column('CreatedDate', sa.Date(), server_default=sa.text('CURRENT_DATE'), nullable=True),
    sa.Column('UpdatedDate', sa.Date(), nullable=True),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='GeographyMst_BankMstID_fkey'),
    sa.PrimaryKeyConstraint('ID', name='GeographyMst_pkey')
    )
    op.create_index('idx_geographymst_bankmstid', 'GeographyMst', ['BankMstID'], unique=False)
    op.create_index('idx_geographymst_bucode', 'GeographyMst', ['BUCode'], unique=False)
    op.create_index('idx_geographymst_buname', 'GeographyMst', ['BUName'], unique=False)
    op.create_index('idx_geographymst_butype', 'GeographyMst', ['BUType'], unique=False)
    op.create_index('idx_geographymst_reportingbuid', 'GeographyMst', ['ReportingBUID'], unique=False)
    op.create_table('IVRFlowMst',
    sa.Column('IVRFlowMstID', sa.Integer(), nullable=False),
    sa.Column('IsActive', sa.Boolean(), server_default=sa.text('true'), nullable=False),
    sa.Column('FlowName', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='IVRFlowMst_fk_BankMstID'),
    sa.PrimaryKeyConstraint('IVRFlowMstID', name='IVRFlowMst_pkey')
    )
    op.create_table('LanguageMst',
    sa.Column('LngMstID', sa.Integer(), nullable=False),
    sa.Column('Language', sa.String(length=255), nullable=False),
    sa.Column('IsActive', sa.Boolean(), server_default=sa.text('true'), nullable=True),
    sa.Column('CreatedDate', sa.Date(), server_default=sa.text('CURRENT_DATE'), nullable=True),
    sa.Column('UpdatedDate', sa.Date(), nullable=True),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='LanguageMst_BankMstID_fkey'),
    sa.PrimaryKeyConstraint('LngMstID', name='LanguageMst_pkey')
    )
    op.create_table('PT_ExtraColumn',
    sa.Column('PT_EC_ID', sa.Integer(), nullable=False),
    sa.Column('TableName', sa.String(length=255), nullable=False),
    sa.Column('ColumnName', sa.String(length=255), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('User_ColumnName', sa.String(length=255), nullable=False),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='PT_ExtraColumn_BankMstID_fkey'),
    sa.PrimaryKeyConstraint('PT_EC_ID', name='PT_ExtraColumn_pkey')
    )
    op.create_table('PriorityTable',
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('IsWhatsApp', sa.Boolean(), nullable=True),
    sa.Column('WhatsAppComm', sa.Integer(), nullable=True),
    sa.Column('IsVoiceBot', sa.Boolean(), nullable=True),
    sa.Column('VoiceBotComm', sa.Integer(), nullable=True),
    sa.Column('IsSMS', sa.Boolean(), nullable=True),
    sa.Column('SMSComm', sa.Integer(), nullable=True),
    sa.Column('IsIVR', sa.Boolean(), nullable=True),
    sa.Column('IVRComm', sa.Integer(), nullable=True),
    sa.Column('IsBlaster', sa.Boolean(), nullable=True),
    sa.Column('BlasterComm', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='PriorityTable_BankMstID_fkey'),
    sa.PrimaryKeyConstraint('BankMstID', name='PriorityTable_pkey')
    )
    op.create_table('SubscriptionMst',
    sa.Column('SubscriptionMstID', sa.Integer(), nullable=False),
    sa.Column('SubscriptionType', sa.String(length=255), nullable=False),
    sa.Column('IsActive', sa.Boolean(), server_default=sa.text('true'), nullable=True),
    sa.Column('StartDate', sa.Date(), nullable=True),
    sa.Column('EndDate', sa.Date(), nullable=True),
    sa.Column('LastUpdated', sa.Date(), nullable=True),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.Column('Screens', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='SubscriptionMst_BankMstID_fkey'),
    sa.PrimaryKeyConstraint('SubscriptionMstID', name='SubscriptionMst_pkey')
    )
    op.create_table('UserConversations',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.String(length=20), nullable=False),
    sa.Column('last_active_conversation_id', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['last_active_conversation_id'], ['ActiveConversation.uuid'], name='fk_last_active_conversation', ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id', name='UserConversations_pkey')
    )
    op.create_index('idx_userconversations_user_id', 'UserConversations', ['user_id'], unique=False)
    op.create_table('WhatsAppFlowMst',
    sa.Column('WhatsAppFlowMstID', sa.Integer(), nullable=False),
    sa.Column('FlowName', sa.String(length=255), nullable=False),
    sa.Column('IsActive', sa.Boolean(), server_default=sa.text('true'), nullable=False),
    sa.Column('BankID', sa.Integer(), nullable=True),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['BankID'], ['BankMst.BankMstID'], name='WhatsAppFlowMst_fk_BankID'),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='WhatsAppFlowMst_BankMstID_fkey'),
    sa.PrimaryKeyConstraint('WhatsAppFlowMstID', name='WhatsAppFlowMst_pkey')
    )
    op.create_table('WhatsAppKeyMapping',
    sa.Column('WhatsAppKeyMappingID', sa.Integer(), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('CreatedDate', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('AuthorizationKey', sa.String(length=255), nullable=False),
    sa.Column('APIVersion', sa.String(length=50), nullable=False),
    sa.Column('PhoneNumberID', sa.String(), nullable=False),
    sa.Column('Service', sa.String(length=255), nullable=False),
    sa.Column('Extra_Column1', sa.String(length=255), nullable=True),
    sa.Column('Extra_Column2', sa.String(length=255), nullable=True),
    sa.Column('Extra_Column3', sa.String(length=255), nullable=True),
    sa.Column('Extra_Column4', sa.String(length=255), nullable=True),
    sa.Column('Extra_Column5', sa.String(length=255), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='WhatsAppKeyMapping_fk_BankID'),
    sa.PrimaryKeyConstraint('WhatsAppKeyMappingID', name='WhatsAppKeyMapping_pkey')
    )
    op.create_table('auth_permission',
    sa.Column('id', sa.Integer(), sa.Identity(always=False, start=1, increment=1, minvalue=1, maxvalue=**********, cycle=False, cache=1), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('content_type_id', sa.Integer(), nullable=False),
    sa.Column('codename', sa.String(length=100), nullable=False),
    sa.ForeignKeyConstraint(['content_type_id'], ['django_content_type.id'], name='auth_permission_content_type_id_2f476e4b_fk_django_co', initially='DEFERRED', deferrable=True),
    sa.PrimaryKeyConstraint('id', name='auth_permission_pkey'),
    sa.UniqueConstraint('content_type_id', 'codename', name='auth_permission_content_type_id_codename_01ab375a_uniq')
    )
    op.create_index('auth_permission_content_type_id_2f476e4b', 'auth_permission', ['content_type_id'], unique=False)
    op.create_table('bankcontrols',
    sa.Column('ControlID', sa.Integer(), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.Column('Json', postgresql.JSONB(astext_type=sa.Text()), server_default=sa.text("'{}'::json"), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='bankcontrols_BankMstID_fkey'),
    sa.PrimaryKeyConstraint('ControlID', name='bankcontrols_pkey'),
    sa.UniqueConstraint('BankMstID', name='uk_bank_mst_id')
    )
    op.create_table('BlasterTemplateMapping',
    sa.Column('BlasterTemplateMappingID', sa.Integer(), nullable=False),
    sa.Column('Status', sa.String(length=50), nullable=False),
    sa.Column('TemplateName', sa.String(length=255), nullable=False),
    sa.Column('IsActive', sa.Boolean(), server_default=sa.text('true'), nullable=False),
    sa.Column('LngMstID', sa.Integer(), nullable=False),
    sa.Column('IsAdminCreated', sa.Boolean(), server_default=sa.text('false'), nullable=False),
    sa.Column('CreatedDate', sa.Date(), server_default=sa.text('CURRENT_DATE'), nullable=True),
    sa.Column('FileName', sa.String(length=255), nullable=True),
    sa.Column('RecordingURL', sa.String(length=255), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.Column('TemplateBody', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='BlasterTemplateMapping_BankMstID_fkey'),
    sa.ForeignKeyConstraint(['LngMstID'], ['LanguageMst.LngMstID'], name='BlasterTemplateMapping_fk_LngMstID'),
    sa.PrimaryKeyConstraint('BlasterTemplateMappingID', name='BlasterTemplateMapping_pkey')
    )
    op.create_table('BranchMst',
    sa.Column('BranchMstID', sa.Integer(), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('BranchName', sa.String(length=255), nullable=False),
    sa.Column('BranchCode', sa.String(length=50), nullable=True),
    sa.Column('IsActive', sa.Boolean(), server_default=sa.text('true'), nullable=True),
    sa.Column('CreatedDate', sa.Date(), server_default=sa.text('CURRENT_DATE'), nullable=True),
    sa.Column('UpdatedDate', sa.Date(), nullable=True),
    sa.Column('LngMstID', sa.Integer(), nullable=True),
    sa.Column('Remarks', sa.Text(), nullable=True),
    sa.Column('State', sa.String(length=100), nullable=True),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='BranchMst_BankMstID_fkey'),
    sa.ForeignKeyConstraint(['LngMstID'], ['LanguageMst.LngMstID'], name='BranchMst_LngID_fkey'),
    sa.PrimaryKeyConstraint('BranchMstID', name='BranchMst_pkey')
    )
    op.create_table('CollectionOfficerAllocation',
    sa.Column('CollectionOfficerAllocationID', sa.Integer(), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('BranchMstID', sa.Integer(), nullable=False),
    sa.Column('CustomerMstID', sa.Integer(), nullable=False),
    sa.Column('CollectionOfficerID', sa.String(), nullable=False),
    sa.Column('CollectionOfficerName', sa.String(length=255), nullable=False),
    sa.Column('LoanMstID', sa.Integer(), nullable=True),
    sa.Column('CreatedDate', sa.DateTime(), nullable=True),
    sa.Column('ContactNumber', sa.String(length=15), nullable=True),
    sa.Column('BranchName', sa.String(length=255), nullable=True),
    sa.Column('LoanType', sa.String(length=100), nullable=True),
    sa.Column('IsAction', sa.Boolean(), nullable=True),
    sa.Column('LngMstID', sa.Integer(), nullable=True),
    sa.Column('BasedOn', sa.String(), nullable=True),
    sa.Column('Status', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.ForeignKeyConstraint(['LngMstID'], ['LanguageMst.LngMstID'], name='COAllocation_LngstID_fkey'),
    sa.PrimaryKeyConstraint('CollectionOfficerAllocationID', name='CollectionOfficerAllocation_pkey')
    )
    op.create_table('CommFlow',
    sa.Column('CommFlowID', sa.Integer(), nullable=False),
    sa.Column('CommunicationType', sa.String(length=100), nullable=False),
    sa.Column('CommFlowMstID', sa.Integer(), nullable=False),
    sa.Column('Days', sa.String(), nullable=False),
    sa.Column('BeforeAfter', sa.String(length=10), nullable=False),
    sa.Column('FlowID', sa.Integer(), nullable=True),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.Column('BasedOn', sa.String(), nullable=True),
    sa.Column('BasedOnTable', sa.String(), nullable=True),
    sa.Column('TemplateID', sa.String(), nullable=True),
    sa.Column('CampaignMstID', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='CommFlow_BankMstID_fkey'),
    sa.ForeignKeyConstraint(['CampaignMstID'], ['CampaignMst.CampaignMstID'], name='CommFlow_FK_CampaignMst'),
    sa.ForeignKeyConstraint(['CommFlowMstID'], ['CommFlowMst.CommFlowMstID'], name='CommFlow_fk_CommFlowMstID'),
    sa.PrimaryKeyConstraint('CommFlowID', name='CommFlow_pkey')
    )
    op.create_table('EmployeeMst',
    sa.Column('EmployeeMstID', sa.Integer(), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('UserCode', sa.String(length=50), nullable=False),
    sa.Column('EmployeeName', sa.String(length=255), nullable=False),
    sa.Column('Contact', sa.String(length=20), nullable=False),
    sa.Column('DesignationID', sa.Integer(), nullable=False),
    sa.Column('BUID', sa.Integer(), nullable=False),
    sa.Column('IsActive', sa.Boolean(), server_default=sa.text('true'), nullable=True),
    sa.Column('UpdateDate', sa.Date(), nullable=True),
    sa.Column('ReportingID', sa.Integer(), nullable=True),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.ForeignKeyConstraint(['BUID'], ['GeographyMst.ID'], name='EmployeeMst_BUID_fkey'),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='EmployeeMst_BankMstID_fkey'),
    sa.ForeignKeyConstraint(['DesignationID'], ['DesignationMst.DesignationMstID'], name='EmployeeMst_DesignationID_fkey'),
    sa.PrimaryKeyConstraint('EmployeeMstID', name='EmployeeMst_pkey')
    )
    op.create_table('IVRTemplateMapping',
    sa.Column('IVRTemplateMappingID', sa.Integer(), nullable=False),
    sa.Column('CreatedDate', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('Status', sa.String(length=50), nullable=False),
    sa.Column('TemplateName', sa.String(length=255), nullable=False),
    sa.Column('IsActive', sa.Boolean(), server_default=sa.text('true'), nullable=False),
    sa.Column('LngMstID', sa.Integer(), nullable=False),
    sa.Column('IsAdminCreated', sa.Boolean(), nullable=False),
    sa.Column('FileName', sa.String(length=255), nullable=True),
    sa.Column('RecordingURL', sa.String(length=255), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.Column('TemplateBody', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='fk_bankmstid'),
    sa.ForeignKeyConstraint(['LngMstID'], ['LanguageMst.LngMstID'], name='fk_ivr_template'),
    sa.PrimaryKeyConstraint('IVRTemplateMappingID', name='IVRTemplateMapping_pkey')
    )
    op.create_table('PeriodicCommunication',
    sa.Column('PeriodicCommID', sa.Integer(), nullable=False),
    sa.Column('StartDate', sa.Date(), nullable=False),
    sa.Column('StartTime', sa.Time(), nullable=False),
    sa.Column('RepeatFrequency', sa.String(length=50), nullable=False),
    sa.Column('Interval', sa.Integer(), nullable=False),
    sa.Column('CommunicationType', sa.String(length=100), nullable=False),
    sa.Column('CommFlowMstID', sa.Integer(), nullable=False),
    sa.Column('EndDate', sa.Date(), nullable=True),
    sa.Column('EndTime', sa.Time(), nullable=True),
    sa.Column('Until', sa.Date(), nullable=True),
    sa.Column('ByWeekday', sa.String(length=50), nullable=True),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='PeriodicCommunication_BankMstID_fkey'),
    sa.ForeignKeyConstraint(['CommFlowMstID'], ['CommFlowMst.CommFlowMstID'], name='PeriodicCommunication_fk_CommFlowMstID'),
    sa.PrimaryKeyConstraint('PeriodicCommID', name='PeriodicCommunication_pkey')
    )
    op.create_table('SMSTemplateMapping',
    sa.Column('SMSTemplateMappingID', sa.Integer(), nullable=False),
    sa.Column('Status', sa.String(length=50), nullable=False),
    sa.Column('TemplateName', sa.String(length=255), nullable=False),
    sa.Column('IsActive', sa.Boolean(), server_default=sa.text('true'), nullable=False),
    sa.Column('LngMstID', sa.Integer(), nullable=False),
    sa.Column('FileName', sa.String(length=255), nullable=False),
    sa.Column('IsAdminCreated', sa.Boolean(), nullable=False),
    sa.Column('CreatedDate', sa.Date(), server_default=sa.text('CURRENT_DATE'), nullable=True),
    sa.Column('RecordingURL', sa.String(length=255), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.Column('TemplateBody', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='SMSTemplateMapping_BankMstID_fkey'),
    sa.ForeignKeyConstraint(['LngMstID'], ['LanguageMst.LngMstID'], name='SMSTemplateMapping_fk_LngMstID'),
    sa.PrimaryKeyConstraint('SMSTemplateMappingID', name='SMSTemplateMapping_pkey')
    )
    op.create_table('UserConversations_active_conversations',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('userconversations_id', sa.Integer(), nullable=False),
    sa.Column('activeconversation_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['activeconversation_id'], ['ActiveConversation.uuid'], name='UserConversations_active_conversatio_activeconversation_id_fkey', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['userconversations_id'], ['UserConversations.id'], name='UserConversations_active_conversation_userconversations_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='UserConversations_active_conversations_pkey'),
    sa.UniqueConstraint('userconversations_id', 'activeconversation_id', name='UserConversations_active_conv_userconversations_id_activeco_key')
    )
    op.create_table('UserConversations_completed_conversations',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('userconversations_id', sa.Integer(), nullable=False),
    sa.Column('conversationhistory_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['conversationhistory_id'], ['ConversationHistory.uuid'], name='UserConversations_completed_convers_conversationhistory_id_fkey', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['userconversations_id'], ['UserConversations.id'], name='UserConversations_completed_conversat_userconversations_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='UserConversations_completed_conversations_pkey'),
    sa.UniqueConstraint('userconversations_id', 'conversationhistory_id', name='UserConversations_completed_c_userconversations_id_conversa_key')
    )
    op.create_table('VoiceBotTemplateMapping',
    sa.Column('VoiceBotTemplateMappingID', sa.Integer(), nullable=False),
    sa.Column('CreatedDate', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('Status', sa.String(length=50), nullable=False),
    sa.Column('TemplateName', sa.String(length=255), nullable=False),
    sa.Column('IsActive', sa.Boolean(), server_default=sa.text('true'), nullable=False),
    sa.Column('LngMstID', sa.Integer(), nullable=False),
    sa.Column('FileName', sa.String(length=255), nullable=True),
    sa.Column('IsAdminCreated', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('RecordingURL', sa.String(length=255), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.Column('TemplateBody', sa.String(), nullable=True),
    sa.Column('StartMsg', sa.String(), nullable=True),
    sa.Column('EndMsg', sa.String(), nullable=True),
    sa.Column('SystemInstructions', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='VoiceBotTemplateMapping_BankMstID_fkey'),
    sa.ForeignKeyConstraint(['LngMstID'], ['LanguageMst.LngMstID'], name='VoiceBotTemplateMapping_fk_LngMstID'),
    sa.PrimaryKeyConstraint('VoiceBotTemplateMappingID', name='VoiceBotTemplateMapping_pkey')
    )
    op.create_table('WhatsAppTemplateMapping',
    sa.Column('WhatsAppTemplateMappingID', sa.Integer(), nullable=False),
    sa.Column('CreatedDate', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('Status', sa.String(length=50), nullable=False),
    sa.Column('TemplateName', sa.String(length=255), nullable=False),
    sa.Column('IsActive', sa.Boolean(), server_default=sa.text('true'), nullable=False),
    sa.Column('LngMstID', sa.Integer(), nullable=False),
    sa.Column('FileName', sa.String(length=255), nullable=False),
    sa.Column('IsAdminCreated', sa.Boolean(), nullable=False),
    sa.Column('MetaTemplateID', sa.String(length=255), nullable=True),
    sa.Column('Extra_Column1', sa.String(length=255), nullable=True),
    sa.Column('Extra_Column2', sa.String(length=255), nullable=True),
    sa.Column('Extra_Column3', sa.String(length=255), nullable=True),
    sa.Column('Extra_Column4', sa.String(length=255), nullable=True),
    sa.Column('Extra_Column5', sa.String(length=255), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.Column('TemplateBody', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='WhatsAppTemplateMapping_BankMstID_fkey'),
    sa.ForeignKeyConstraint(['LngMstID'], ['LanguageMst.LngMstID'], name='WhatsAppTemplateMapping_fk_LanguageID'),
    sa.PrimaryKeyConstraint('WhatsAppTemplateMappingID', name='WhatsAppTemplateMapping_pkey')
    )
    op.create_table('auth_group_permissions',
    sa.Column('id', sa.BigInteger(), sa.Identity(always=False, start=1, increment=1, minvalue=1, maxvalue=9223372036854775807, cycle=False, cache=1), nullable=False),
    sa.Column('group_id', sa.Integer(), nullable=False),
    sa.Column('permission_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['group_id'], ['auth_group.id'], name='auth_group_permissions_group_id_b120cbf9_fk_auth_group_id', initially='DEFERRED', deferrable=True),
    sa.ForeignKeyConstraint(['permission_id'], ['auth_permission.id'], name='auth_group_permissio_permission_id_84c5c92e_fk_auth_perm', initially='DEFERRED', deferrable=True),
    sa.PrimaryKeyConstraint('id', name='auth_group_permissions_pkey'),
    sa.UniqueConstraint('group_id', 'permission_id', name='auth_group_permissions_group_id_permission_id_0cd325b0_uniq')
    )
    op.create_index('auth_group_permissions_group_id_b120cbf9', 'auth_group_permissions', ['group_id'], unique=False)
    op.create_index('auth_group_permissions_permission_id_84c5c92e', 'auth_group_permissions', ['permission_id'], unique=False)
    op.create_table('BlasterUserTemplate',
    sa.Column('BlasterUserTemplateID', sa.Integer(), nullable=False),
    sa.Column('TemplateName', sa.String(length=255), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('VariableNumber', sa.Integer(), nullable=False),
    sa.Column('VariableField', sa.String(length=255), nullable=False),
    sa.Column('TemplateBody', sa.Text(), nullable=True),
    sa.Column('EmployeeMstID', sa.Integer(), nullable=True),
    sa.Column('BlasterTemplateMappingID', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='BlasterUserTemplate_fk_BankMstID'),
    sa.ForeignKeyConstraint(['BlasterTemplateMappingID'], ['BlasterTemplateMapping.BlasterTemplateMappingID'], name='fk_voicebot_template', ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['EmployeeMstID'], ['EmployeeMst.EmployeeMstID'], name='BlasterUserTemplate_fk_EmployeeID'),
    sa.PrimaryKeyConstraint('BlasterUserTemplateID', name='BlasterUserTemplate_pkey')
    )
    op.create_table('CustomerMst',
    sa.Column('CustomerMstID', sa.Integer(), nullable=False),
    sa.Column('BranchMstID', sa.Integer(), nullable=False),
    sa.Column('CustomerID', sa.Integer(), nullable=False),
    sa.Column('CustomerName', sa.String(length=255), nullable=False),
    sa.Column('CustomerCode', sa.String(length=50), nullable=True),
    sa.Column('MobileNumber', sa.String(length=15), nullable=True),
    sa.Column('Gender', sa.String(length=10), nullable=True),
    sa.Column('DateOfBirth', sa.Date(), nullable=True),
    sa.Column('IsDeath', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('DeathDate', sa.Date(), nullable=True),
    sa.Column('CreatedDate', sa.Date(), server_default=sa.text('CURRENT_DATE'), nullable=True),
    sa.Column('UpdatedDate', sa.Date(), nullable=True),
    sa.Column('EmailID', sa.String(length=255), nullable=True),
    sa.Column('Latitude', sa.String(length=255), nullable=True),
    sa.Column('Longitude', sa.String(length=255), nullable=True),
    sa.Column('PanNo', sa.String(length=20), nullable=True),
    sa.Column('Pincode', sa.String(length=6), nullable=True),
    sa.Column('CustomerAddress', sa.Text(), nullable=True),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='CustomerMst_BankMstID_fkey'),
    sa.ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='CustomerMst_BranchMstID_fkey'),
    sa.PrimaryKeyConstraint('CustomerMstID', name='CustomerMst_pkey')
    )
    op.create_table('EmployeeAccess',
    sa.Column('EmployeeAccessID', sa.Integer(), nullable=False),
    sa.Column('EmployeeMstID', sa.Integer(), nullable=False),
    sa.Column('BUID', sa.Integer(), nullable=False),
    sa.Column('DesignationID', sa.Integer(), nullable=False),
    sa.Column('IsActive', sa.Boolean(), server_default=sa.text('true'), nullable=True),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['BUID'], ['GeographyMst.ID'], name='EmployeeAccess_BUID_fkey'),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='EmployeeAccess_BankMstID_fkey'),
    sa.ForeignKeyConstraint(['DesignationID'], ['DesignationMst.DesignationMstID'], name='EmployeeAccess_DesignationID_fkey'),
    sa.ForeignKeyConstraint(['EmployeeMstID'], ['EmployeeMst.EmployeeMstID'], name='EmployeeAccess_EmployeeID_fkey'),
    sa.PrimaryKeyConstraint('EmployeeAccessID', name='EmployeeAccess_pkey')
    )
    op.create_table('FilterMst',
    sa.Column('ID', sa.Integer(), nullable=False),
    sa.Column('Name', sa.String(length=255), nullable=False),
    sa.Column('IsActive', sa.Boolean(), server_default=sa.text('true'), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('CommFlowID', sa.Integer(), nullable=True),
    sa.Column('FiltersID', sa.Integer(), nullable=True),
    sa.Column('CampaignMstID', sa.Integer(), nullable=True),
    sa.Column('CommFlowMstID', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='FilterMst_fk_BankID', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['CampaignMstID'], ['CampaignMst.CampaignMstID'], name='FilterMst_CampaignMstID_fkey'),
    sa.ForeignKeyConstraint(['CommFlowID'], ['CommFlow.CommFlowID'], name='FilterMst_fk_CommFlow', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['CommFlowMstID'], ['CommFlowMst.CommFlowMstID'], name='fk_commflowmst', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['FiltersID'], ['Filters.FiltersID'], name='FilterMst_FiltersID_fkey'),
    sa.PrimaryKeyConstraint('ID', name='FilterMst_pkey')
    )
    op.create_table('IVRFlowMapping',
    sa.Column('IVRFlowID', sa.Integer(), nullable=False),
    sa.Column('FlowID', sa.Integer(), nullable=False),
    sa.Column('CreatedDate', sa.DateTime(), nullable=True),
    sa.Column('FlowName', sa.String(length=255), nullable=True),
    sa.Column('Response', sa.Text(), nullable=True),
    sa.Column('IsActive', sa.Boolean(), nullable=True),
    sa.Column('LngMstID', sa.Integer(), nullable=True),
    sa.Column('IVRTemplateMappingID', sa.Integer(), nullable=True),
    sa.Column('IVRFlowMstID', sa.Integer(), nullable=True),
    sa.Column('IsStart', sa.Boolean(), nullable=True),
    sa.Column('ButtonPressed', sa.String(length=50), nullable=True),
    sa.Column('NextTemplate', sa.Integer(), nullable=True),
    sa.Column('CurrentTemplate', sa.Integer(), nullable=True),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='IVRFlowmapping_fk_BankMstID'),
    sa.ForeignKeyConstraint(['IVRFlowMstID'], ['IVRFlowMst.IVRFlowMstID'], name='IVRFlowmapping_fk_IVRFlowMstID'),
    sa.ForeignKeyConstraint(['IVRTemplateMappingID'], ['IVRTemplateMapping.IVRTemplateMappingID'], name='IVRFlowMapping_fk_IVRTemplateMappingID'),
    sa.ForeignKeyConstraint(['LngMstID'], ['LanguageMst.LngMstID'], name='fk_ivr_template'),
    sa.PrimaryKeyConstraint('IVRFlowID', name='IVRFlowMapping_pkey')
    )
    op.create_table('IVRUserTemplate',
    sa.Column('IVRUserTemplateID', sa.Integer(), nullable=False),
    sa.Column('TemplateName', sa.String(length=255), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('EmployeeMstID', sa.Integer(), nullable=True),
    sa.Column('VariableNumber', sa.Integer(), nullable=True),
    sa.Column('VariableField', sa.String(length=255), nullable=True),
    sa.Column('IVRTemplateMappingID', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['IVRTemplateMappingID'], ['IVRTemplateMapping.IVRTemplateMappingID'], name='fk_ivr_template_mapping'),
    sa.PrimaryKeyConstraint('IVRUserTemplateID', name='IVRUserTemplate_pkey')
    )
    op.create_table('IVRVariableMapping',
    sa.Column('IVRVariableMappingID', sa.Integer(), nullable=False),
    sa.Column('IVRTemplateMappingID', sa.Integer(), nullable=True),
    sa.Column('VariableNo', sa.Integer(), nullable=True),
    sa.Column('VariableField', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['IVRTemplateMappingID'], ['IVRTemplateMapping.IVRTemplateMappingID'], name='fk_ivr_template'),
    sa.PrimaryKeyConstraint('IVRVariableMappingID', name='IVRVariableMapping_pkey')
    )
    op.create_table('SMSUserTemplate',
    sa.Column('SMSUserTemplateID', sa.Integer(), nullable=False),
    sa.Column('TemplateName', sa.String(length=255), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('VariableNumber', sa.Integer(), nullable=False),
    sa.Column('VariableField', sa.String(length=255), nullable=False),
    sa.Column('EmployeeMstID', sa.Integer(), nullable=True),
    sa.Column('SMSTemplateMappingID', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='SMSUserTemplate_fk_BankMstID'),
    sa.ForeignKeyConstraint(['EmployeeMstID'], ['EmployeeMst.EmployeeMstID'], name='SMSUserTemplate_fk_EmployeeID'),
    sa.ForeignKeyConstraint(['SMSTemplateMappingID'], ['SMSTemplateMapping.SMSTemplateMappingID'], name='SMSTemplateMapping_fk_SMSTemplateMappingID'),
    sa.PrimaryKeyConstraint('SMSUserTemplateID', name='SMSUserTemplate_pkey')
    )
    op.create_table('UserMst',
    sa.Column('password', sa.String(length=128), nullable=False),
    sa.Column('is_superuser', sa.Boolean(), nullable=False),
    sa.Column('username', sa.String(length=150), nullable=False),
    sa.Column('first_name', sa.String(length=150), nullable=False),
    sa.Column('last_name', sa.String(length=150), nullable=False),
    sa.Column('email', sa.String(length=254), nullable=False),
    sa.Column('is_staff', sa.Boolean(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('date_joined', sa.DateTime(timezone=True), nullable=False),
    sa.Column('UserID', sa.Integer(), sa.Identity(always=False, start=1, increment=1, minvalue=1, maxvalue=**********, cycle=False, cache=1), nullable=False),
    sa.Column('is_admin', sa.Boolean(), nullable=False),
    sa.Column('BankMaster', sa.Boolean(), nullable=False),
    sa.Column('UpdatedDate', sa.DateTime(timezone=True), nullable=False),
    sa.Column('last_login', sa.DateTime(timezone=True), nullable=True),
    sa.Column('BranchID', sa.String(length=50), nullable=True),
    sa.Column('Designation', sa.String(length=50), nullable=True),
    sa.Column('MobileNumber', sa.String(length=50), nullable=True),
    sa.Column('ExtraColumn_1', sa.Text(), nullable=True),
    sa.Column('ExtraColumn_2', sa.Text(), nullable=True),
    sa.Column('ExtraColumn_3', sa.Text(), nullable=True),
    sa.Column('ExtraColumn_4', sa.Text(), nullable=True),
    sa.Column('ExtraColumn_5', sa.Text(), nullable=True),
    sa.Column('BankMstID_id', sa.Integer(), nullable=True),
    sa.Column('BranchMstID_id', sa.Integer(), nullable=True),
    sa.Column('FO_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID_id'], ['BankMst.BankMstID'], name='UserMst_BankMstID_id_4b4302a0_fk_BankMst_BankMstID', initially='DEFERRED', deferrable=True),
    sa.ForeignKeyConstraint(['BranchMstID_id'], ['BranchMst.BranchMstID'], name='UserMst_BranchMstID_id_224814fd_fk_BranchMst_BranchMstID', initially='DEFERRED', deferrable=True),
    sa.PrimaryKeyConstraint('UserID', name='UserMst_pkey'),
    sa.UniqueConstraint('username', name='UserMst_username_key')
    )
    op.create_index('UserMst_BankMstID_id_4b4302a0', 'UserMst', ['BankMstID_id'], unique=False)
    op.create_index('UserMst_BranchMstID_id_224814fd', 'UserMst', ['BranchMstID_id'], unique=False)
    op.create_index('UserMst_username_219fb066_like', 'UserMst', ['username'], unique=False)
    op.create_table('VoiceBotUserTemplate',
    sa.Column('VoiceBotUserTemplateID', sa.Integer(), nullable=False),
    sa.Column('TemplateName', sa.String(length=255), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('VariableNumber', sa.Integer(), nullable=False),
    sa.Column('VariableField', sa.String(length=255), nullable=False),
    sa.Column('EmployeeMstID', sa.Integer(), nullable=True),
    sa.Column('VoiceBotTemplateMappingID', sa.Integer(), nullable=True),
    sa.Column('StartEnd', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='VoiceBotUserTemplate_fk_BankMstID'),
    sa.ForeignKeyConstraint(['EmployeeMstID'], ['EmployeeMst.EmployeeMstID'], name='VoiceBotUserTemplate_fk_EmployeeID'),
    sa.ForeignKeyConstraint(['VoiceBotTemplateMappingID'], ['VoiceBotTemplateMapping.VoiceBotTemplateMappingID'], name='fk_voicebot_template', ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('VoiceBotUserTemplateID', name='VoiceBotUserTemplate_pkey')
    )
    op.create_table('WhatsAppFlowMapping',
    sa.Column('WhatsAppFlowID', sa.Integer(), nullable=False),
    sa.Column('CreatedDate', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('FlowID', sa.Integer(), nullable=False),
    sa.Column('FlowName', sa.String(length=255), nullable=False),
    sa.Column('Response', sa.Text(), nullable=False),
    sa.Column('IsActive', sa.Boolean(), server_default=sa.text('true'), nullable=False),
    sa.Column('LngMstID', sa.Integer(), nullable=False),
    sa.Column('WhatsAppTemplateMappingID', sa.Integer(), nullable=False),
    sa.Column('WhatsAppFlowMstID', sa.Integer(), nullable=False),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.Column('IsStart', sa.Boolean(), nullable=True),
    sa.Column('NextTemplate', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='WhatsAppFlowMapping_BankMstID_fkey'),
    sa.ForeignKeyConstraint(['LngMstID'], ['LanguageMst.LngMstID'], name='WhatsAppFlowMapping_fk_LanguageID'),
    sa.ForeignKeyConstraint(['WhatsAppFlowMstID'], ['WhatsAppFlowMst.WhatsAppFlowMstID'], name='WhatsAppFlowMapping_fk_WhatsAppFlowMstID'),
    sa.ForeignKeyConstraint(['WhatsAppTemplateMappingID'], ['WhatsAppTemplateMapping.WhatsAppTemplateMappingID'], name='WhatsAppFlowMapping_fk_WhatsAppTemplateMappingID'),
    sa.PrimaryKeyConstraint('WhatsAppFlowID', name='WhatsAppFlowMapping_pkey')
    )
    op.create_table('WhatsAppUserTemplate',
    sa.Column('WhatsAppUserTemplateID', sa.Integer(), nullable=False),
    sa.Column('TemplateName', sa.String(length=255), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('EmployeeMstID', sa.Integer(), nullable=True),
    sa.Column('Variable1', sa.String(length=255), nullable=True),
    sa.Column('Variable2', sa.String(length=255), nullable=True),
    sa.Column('Variable3', sa.String(length=255), nullable=True),
    sa.Column('Variable4', sa.String(length=255), nullable=True),
    sa.Column('Variable5', sa.String(length=255), nullable=True),
    sa.Column('Variable6', sa.String(length=255), nullable=True),
    sa.Column('Variable7', sa.String(length=255), nullable=True),
    sa.Column('Variable8', sa.String(length=255), nullable=True),
    sa.Column('Variable9', sa.String(length=255), nullable=True),
    sa.Column('Variable10', sa.String(length=255), nullable=True),
    sa.Column('Button1', sa.String(), nullable=True),
    sa.Column('Button2', sa.String(), nullable=True),
    sa.Column('Button3', sa.String(), nullable=True),
    sa.Column('Button4', sa.String(), nullable=True),
    sa.Column('WhatsAppTemplateMappingID', sa.Integer(), nullable=True),
    sa.Column('Response1', sa.String(), nullable=True),
    sa.Column('Response2', sa.String(), nullable=True),
    sa.Column('Response3', sa.String(), nullable=True),
    sa.Column('Response4', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='WhatsAppUserTemplate_fk_BankID'),
    sa.ForeignKeyConstraint(['EmployeeMstID'], ['EmployeeMst.EmployeeMstID'], name='WhatsAppUserTemplate_fk_EmployeeID'),
    sa.ForeignKeyConstraint(['WhatsAppTemplateMappingID'], ['WhatsAppTemplateMapping.WhatsAppTemplateMappingID'], name='fk_whatsapp_template'),
    sa.PrimaryKeyConstraint('WhatsAppUserTemplateID', name='WhatsAppUserTemplate_pkey')
    )
    op.create_table('WhatsappVariableMapping',
    sa.Column('WhatsappVariableMappingID', sa.Integer(), nullable=False),
    sa.Column('WhatsAppTemplateMappingID', sa.Integer(), nullable=True),
    sa.Column('VariableNo', sa.Integer(), nullable=True),
    sa.Column('VariableField', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['WhatsAppTemplateMappingID'], ['WhatsAppTemplateMapping.WhatsAppTemplateMappingID'], name='fk_whatsapp_template'),
    sa.PrimaryKeyConstraint('WhatsappVariableMappingID', name='WhatsappVariableMapping_pkey')
    )
    op.create_table('LoanMst',
    sa.Column('LoanMstID', sa.Integer(), nullable=False),
    sa.Column('CustomerMstID', sa.Integer(), nullable=False),
    sa.Column('BranchMstID', sa.Integer(), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('DisbursementID', sa.String(length=255), nullable=True),
    sa.Column('DisbursementDate', sa.Date(), nullable=True),
    sa.Column('DisbursementAmt', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('LoanType', sa.String(length=50), nullable=True),
    sa.Column('ClosingDate', sa.Date(), nullable=True),
    sa.Column('InstallmentStartDate', sa.Date(), nullable=True),
    sa.Column('RateOfInterest', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('RepaymentTenure', sa.Integer(), nullable=True),
    sa.Column('EMIAmount', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('PaymentFrequency', sa.Integer(), nullable=True),
    sa.Column('LastModifiedDate', sa.Date(), nullable=True),
    sa.Column('IsActive', sa.Boolean(), server_default=sa.text('true'), nullable=True),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.Column('BMID', sa.Integer(), nullable=True),
    sa.Column('CollectionOfficerID', sa.String(), nullable=True),
    sa.Column('CollectionOfficerName', sa.String(), nullable=True),
    sa.Column('DND', sa.Boolean(), nullable=True),
    sa.Column('groupid', sa.Integer(), nullable=True),
    sa.Column('groupname', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='fk_LoanMst_BankMstID', ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='LoanMst_BranchMstID_fkey'),
    sa.ForeignKeyConstraint(['CustomerMstID'], ['CustomerMst.CustomerMstID'], name='LoanMst_CustomerMstID_fkey'),
    sa.PrimaryKeyConstraint('LoanMstID', name='LoanMst_pkey'),
    sa.UniqueConstraint('DisbursementID', 'BranchMstID', 'CustomerMstID', name='unique_disbursement_combined')
    )
    op.create_table('LoginHistory',
    sa.Column('id', sa.BigInteger(), sa.Identity(always=False, start=1, increment=1, minvalue=1, maxvalue=9223372036854775807, cycle=False, cache=1), nullable=False),
    sa.Column('ip', postgresql.INET(), nullable=False),
    sa.Column('ip_info', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('city', sa.String(length=80), nullable=False),
    sa.Column('region', sa.String(length=80), nullable=False),
    sa.Column('region_code', sa.String(length=10), nullable=False),
    sa.Column('country_code', sa.String(length=2), nullable=False),
    sa.Column('country_name', sa.String(length=80), nullable=False),
    sa.Column('currency', sa.String(length=5), nullable=False),
    sa.Column('user_agent', sa.Text(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['UserMst.UserID'], name='LoginHistory_user_id_b2cfd3c6_fk_UserMst_UserID', initially='DEFERRED', deferrable=True),
    sa.PrimaryKeyConstraint('id', name='LoginHistory_pkey')
    )
    op.create_index('LoginHistory_ip_19de82e3', 'LoginHistory', ['ip'], unique=False)
    op.create_index('LoginHistory_user_id_b2cfd3c6', 'LoginHistory', ['user_id'], unique=False)
    op.create_table('UserMst_groups',
    sa.Column('id', sa.BigInteger(), sa.Identity(always=False, start=1, increment=1, minvalue=1, maxvalue=9223372036854775807, cycle=False, cache=1), nullable=False),
    sa.Column('usermst_id', sa.Integer(), nullable=False),
    sa.Column('group_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['group_id'], ['auth_group.id'], name='UserMst_groups_group_id_bfa15f2a_fk_auth_group_id', initially='DEFERRED', deferrable=True),
    sa.ForeignKeyConstraint(['usermst_id'], ['UserMst.UserID'], name='UserMst_groups_usermst_id_d5c7157a_fk_UserMst_UserID', initially='DEFERRED', deferrable=True),
    sa.PrimaryKeyConstraint('id', name='UserMst_groups_pkey'),
    sa.UniqueConstraint('usermst_id', 'group_id', name='UserMst_groups_usermst_id_group_id_082a8409_uniq')
    )
    op.create_index('UserMst_groups_group_id_bfa15f2a', 'UserMst_groups', ['group_id'], unique=False)
    op.create_index('UserMst_groups_usermst_id_d5c7157a', 'UserMst_groups', ['usermst_id'], unique=False)
    op.create_table('UserMst_user_permissions',
    sa.Column('id', sa.BigInteger(), sa.Identity(always=False, start=1, increment=1, minvalue=1, maxvalue=9223372036854775807, cycle=False, cache=1), nullable=False),
    sa.Column('usermst_id', sa.Integer(), nullable=False),
    sa.Column('permission_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['permission_id'], ['auth_permission.id'], name='UserMst_user_permiss_permission_id_06930a11_fk_auth_perm', initially='DEFERRED', deferrable=True),
    sa.ForeignKeyConstraint(['usermst_id'], ['UserMst.UserID'], name='UserMst_user_permissions_usermst_id_ca7abfab_fk_UserMst_UserID', initially='DEFERRED', deferrable=True),
    sa.PrimaryKeyConstraint('id', name='UserMst_user_permissions_pkey'),
    sa.UniqueConstraint('usermst_id', 'permission_id', name='UserMst_user_permissions_usermst_id_permission_id_384750fe_uniq')
    )
    op.create_index('UserMst_user_permissions_permission_id_06930a11', 'UserMst_user_permissions', ['permission_id'], unique=False)
    op.create_index('UserMst_user_permissions_usermst_id_ca7abfab', 'UserMst_user_permissions', ['usermst_id'], unique=False)
    op.create_table('django_admin_log',
    sa.Column('id', sa.Integer(), sa.Identity(always=False, start=1, increment=1, minvalue=1, maxvalue=**********, cycle=False, cache=1), nullable=False),
    sa.Column('action_time', sa.DateTime(timezone=True), nullable=False),
    sa.Column('object_repr', sa.String(length=200), nullable=False),
    sa.Column('action_flag', sa.SmallInteger(), nullable=False),
    sa.Column('change_message', sa.Text(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('object_id', sa.Text(), nullable=True),
    sa.Column('content_type_id', sa.Integer(), nullable=True),
    sa.CheckConstraint('action_flag >= 0', name='django_admin_log_action_flag_check'),
    sa.ForeignKeyConstraint(['content_type_id'], ['django_content_type.id'], name='django_admin_log_content_type_id_c4bce8eb_fk_django_co', initially='DEFERRED', deferrable=True),
    sa.ForeignKeyConstraint(['user_id'], ['UserMst.UserID'], name='django_admin_log_user_id_c564eba6_fk_UserMst_UserID', initially='DEFERRED', deferrable=True),
    sa.PrimaryKeyConstraint('id', name='django_admin_log_pkey')
    )
    op.create_index('django_admin_log_content_type_id_c4bce8eb', 'django_admin_log', ['content_type_id'], unique=False)
    op.create_index('django_admin_log_user_id_c564eba6', 'django_admin_log', ['user_id'], unique=False)
    op.create_table('AccountSummary',
    sa.Column('BranchMstID', sa.Integer(), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('CustomerMstID', sa.Integer(), nullable=False),
    sa.Column('CustomerCode', sa.String(length=255), nullable=True),
    sa.Column('CustomerName', sa.String(length=255), nullable=False),
    sa.Column('MobileNumber', sa.String(length=15), nullable=False),
    sa.Column('Gender', sa.String(length=10), nullable=True),
    sa.Column('DateOfBirth', sa.Date(), nullable=True),
    sa.Column('DisbursementID', sa.String(length=255), nullable=False),
    sa.Column('DisbursementDate', sa.Date(), nullable=True),
    sa.Column('DisbursementAmt', sa.Numeric(precision=18, scale=2), nullable=False),
    sa.Column('LoanType', sa.String(length=50), nullable=False),
    sa.Column('DPD', sa.Integer(), nullable=True),
    sa.Column('LoanClassification', sa.String(length=50), nullable=True),
    sa.Column('LastPaymentDate', sa.Date(), nullable=True),
    sa.Column('LastCollectedAmount', sa.Numeric(precision=18, scale=2), nullable=True),
    sa.Column('CurrentBalance', sa.Numeric(precision=18, scale=2), nullable=True),
    sa.Column('OverDueAmt', sa.Numeric(precision=18, scale=2), nullable=False),
    sa.Column('PrincipleOutstanding', sa.Numeric(precision=18, scale=2), nullable=True),
    sa.Column('InterestOutstanding', sa.Numeric(precision=18, scale=2), nullable=True),
    sa.Column('TotalOutstanding', sa.Numeric(precision=18, scale=2), nullable=True),
    sa.Column('PrinciplePending', sa.Numeric(precision=18, scale=2), nullable=False),
    sa.Column('InterestPending', sa.Numeric(precision=18, scale=2), nullable=False),
    sa.Column('TotalPending', sa.Numeric(precision=18, scale=2), nullable=False),
    sa.Column('ClosingDate', sa.Date(), nullable=True),
    sa.Column('IsActive', sa.Boolean(), server_default=sa.text('true'), nullable=False),
    sa.Column('EMIAmount', sa.Numeric(precision=18, scale=2), nullable=False),
    sa.Column('NextEMIDate', sa.Date(), nullable=True),
    sa.Column('LoanMstID', sa.Integer(), nullable=False),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.Column('CreatedDate', sa.Date(), nullable=True),
    sa.Column('Branch_id', sa.String(length=255), nullable=True),
    sa.Column('Branch', sa.String(length=255), nullable=True),
    sa.Column('Region_id', sa.String(length=255), nullable=True),
    sa.Column('Region', sa.String(length=255), nullable=True),
    sa.Column('State_id', sa.String(length=255), nullable=True),
    sa.Column('State', sa.String(length=255), nullable=True),
    sa.Column('CollectionOfficerID', sa.String(), nullable=True),
    sa.Column('CollectionOfficerName', sa.String(), nullable=True),
    sa.Column('groupid', sa.String(), nullable=True),
    sa.Column('groupname', sa.String(), nullable=True),
    sa.Column('CurrentDPD', sa.String(), nullable=True),
    sa.Column('LastMonthDPD', sa.String(), nullable=True),
    sa.Column('Division', sa.String(length=100), nullable=True),
    sa.Column('District_id', sa.String(length=50), nullable=True),
    sa.Column('CO', sa.String(length=100), nullable=True),
    sa.Column('Circle_id', sa.String(length=50), nullable=True),
    sa.Column('Zone', sa.String(length=100), nullable=True),
    sa.Column('Zone_id', sa.String(length=50), nullable=True),
    sa.Column('Circle', sa.String(length=100), nullable=True),
    sa.Column('Division_id', sa.String(length=50), nullable=True),
    sa.Column('CO_id', sa.String(length=50), nullable=True),
    sa.Column('HeadOffice_id', sa.String(length=50), nullable=True),
    sa.Column('HeadOffice', sa.String(length=100), nullable=True),
    sa.Column('District', sa.String(length=100), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='AccountSummary_BankID_fkey'),
    sa.ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='AccountSummary_BranchID_fkey'),
    sa.ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='AccountSummary_LoanID_fkey')
    )
    op.create_table('BMAllocation',
    sa.Column('BMAllocationID', sa.Integer(), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('BranchMstID', sa.Integer(), nullable=False),
    sa.Column('CustomerMstID', sa.Integer(), nullable=False),
    sa.Column('CreatedDate', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('ContactNumber', sa.String(length=15), nullable=False),
    sa.Column('BranchName', sa.String(length=255), nullable=False),
    sa.Column('LoanType', sa.String(length=100), nullable=False),
    sa.Column('IsAction', sa.Boolean(), server_default=sa.text('false'), nullable=False),
    sa.Column('LngMstID', sa.Integer(), nullable=False),
    sa.Column('LoanMstID', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='BMAllocation_fk_BankMstID', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='BMAllocation_fk_BranchMstID', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['CustomerMstID'], ['CustomerMst.CustomerMstID'], name='BMAllocation_fk_CustomerMstID', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['LngMstID'], ['LanguageMst.LngMstID'], name='BMAllocation_fk_LngID', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='fk_BMAllocation_LoanMstID', ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('BMAllocationID', name='BMAllocation_pkey')
    )
    op.create_table('BlasterHistory',
    sa.Column('BlasterHistoryID', sa.Integer(), nullable=False),
    sa.Column('BlasterQueueID', sa.Integer(), nullable=False),
    sa.Column('CreatedDate', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('BranchMstID', sa.Integer(), nullable=False),
    sa.Column('LoanMstID', sa.Integer(), nullable=False),
    sa.Column('CustomerID', sa.Integer(), nullable=False),
    sa.Column('DisbursementID', sa.String(length=255), nullable=False),
    sa.Column('ContactNumber', sa.String(length=15), nullable=False),
    sa.Column('LoanType', sa.String(length=100), nullable=False),
    sa.Column('CustomerName', sa.String(length=255), nullable=False),
    sa.Column('IsSent', sa.Boolean(), server_default=sa.text('false'), nullable=False),
    sa.Column('Type', sa.String(length=50), nullable=False),
    sa.Column('BankName', sa.String(length=255), nullable=False),
    sa.Column('Language', sa.String(), nullable=False),
    sa.Column('Next_EMI_Amount', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('Next_EMI_Date', sa.Date(), nullable=True),
    sa.Column('Total_Collection', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('Latest_CollectedDate', sa.Date(), nullable=True),
    sa.Column('Latest_CollectedAmt', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('Identifier', sa.String(length=255), nullable=True),
    sa.Column('CallID', sa.String(length=255), nullable=True),
    sa.Column('Recording', sa.String(length=255), nullable=True),
    sa.Column('Conversation_json', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('Conclusion', sa.String(length=255), nullable=True),
    sa.Column('CallTried', sa.Integer(), nullable=True),
    sa.Column('CallDuration', sa.Double(precision=53), nullable=True),
    sa.Column('CallConnected', sa.Boolean(), nullable=True),
    sa.Column('Slot', sa.String(), nullable=True),
    sa.Column('BasedOn', sa.String(), nullable=True),
    sa.Column('BranchCode', sa.String(), nullable=True),
    sa.Column('CustomerCode', sa.String(), nullable=True),
    sa.Column('FlowID', sa.Integer(), nullable=True),
    sa.Column('BlasterTemplateMappingID', sa.Integer(), nullable=True),
    sa.Column('IsDelivered', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('IsRead', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('IsResponded', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('OverDueAmt', sa.Numeric(precision=18, scale=2), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='BlasterHistory_fk_BankMstID'),
    sa.ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='BlasterHistory_fk_BranchMstID'),
    sa.ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='BlasterHistory_fk_LoanMstID'),
    sa.PrimaryKeyConstraint('BlasterHistoryID', name='BlasterHistory_pkey'),
    sa.UniqueConstraint('BlasterQueueID', name='unique_blasterqueueid_history')
    )
    op.create_table('BlasterQueue',
    sa.Column('BlasterQueueID', sa.Integer(), nullable=False),
    sa.Column('CreatedDate', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('BranchMstID', sa.Integer(), nullable=False),
    sa.Column('LoanMstID', sa.Integer(), nullable=False),
    sa.Column('BranchCode', sa.String(length=50), nullable=False),
    sa.Column('LoanType', sa.String(length=100), nullable=False),
    sa.Column('CustomerName', sa.String(length=255), nullable=False),
    sa.Column('BankName', sa.String(length=255), nullable=False),
    sa.Column('CustomerCode', sa.String(length=50), nullable=True),
    sa.Column('Language', sa.String(length=100), nullable=True),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.Column('Slot', sa.String(length=255), nullable=True),
    sa.Column('CallTried', sa.Integer(), server_default=sa.text('0'), nullable=True),
    sa.Column('CallConnected', sa.Boolean(), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.Column('BasedOn', sa.String(), nullable=True),
    sa.Column('CommFlowID', sa.Integer(), nullable=True),
    sa.Column('ContactNumber', sa.String(), nullable=True),
    sa.Column('CallDuration', sa.Double(precision=53), nullable=True),
    sa.Column('CustomerID', sa.Integer(), nullable=True),
    sa.Column('DisbursementID', sa.String(), nullable=True),
    sa.Column('FlowID', sa.Integer(), nullable=True),
    sa.Column('BlasterTemplateMappingID', sa.Integer(), nullable=True),
    sa.Column('Total_Collection', sa.Integer(), nullable=True),
    sa.Column('Latest_CollectedDate', sa.Date(), nullable=True),
    sa.Column('Latest_CollectedAmt', sa.Integer(), nullable=True),
    sa.Column('Next_EMI_Amount', sa.Integer(), nullable=True),
    sa.Column('Next_EMI_Date', sa.Date(), nullable=True),
    sa.Column('Identifier', sa.String(), nullable=True),
    sa.Column('Type', sa.String(), nullable=True),
    sa.Column('OverDueAmt', sa.Numeric(precision=18, scale=2), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='BlasterQueue_BankMstID_fkey'),
    sa.ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='BlasterQueue_BranchMstID_fkey'),
    sa.ForeignKeyConstraint(['CommFlowID'], ['CommFlow.CommFlowID'], name='fk_commflow_id', ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='BlasterQueue_LoanMstID_fkey'),
    sa.PrimaryKeyConstraint('BlasterQueueID', name='BlasterQueue_pkey')
    )
    op.create_table('CommunicationQueue',
    sa.Column('CommunicationQueueID', sa.Integer(), nullable=False),
    sa.Column('BranchMstID', sa.Integer(), nullable=False),
    sa.Column('IsWhatsApp', sa.Boolean(), server_default=sa.text('false'), nullable=False),
    sa.Column('IsBlaster', sa.Boolean(), server_default=sa.text('false'), nullable=False),
    sa.Column('IsVoiceBot', sa.Boolean(), server_default=sa.text('false'), nullable=False),
    sa.Column('IsSMS', sa.Boolean(), server_default=sa.text('false'), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('LoanMstID', sa.Integer(), nullable=False),
    sa.Column('Type', sa.String(length=50), nullable=False),
    sa.Column('IsIVR', sa.Boolean(), server_default=sa.text('false'), nullable=False),
    sa.Column('Slot', sa.String(length=50), nullable=True),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.Column('CreatedDate', sa.Date(), nullable=True),
    sa.Column('BasedOnDate', sa.String(), nullable=True),
    sa.Column('BMAllocate', sa.String(), nullable=True),
    sa.Column('CollectionOfficerAllocate', sa.String(), nullable=True),
    sa.Column('CampaignMstID', sa.Integer(), nullable=True),
    sa.Column('FlowID', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='CommunicationQueue_fk_BankID'),
    sa.ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='CommunicationQueue_fk_BranchID'),
    sa.ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='CommunicationQueue_fk_LoanMstID'),
    sa.PrimaryKeyConstraint('CommunicationQueueID', name='CommunicationQueue_pkey')
    )
    op.create_table('DNDHistory',
    sa.Column('DNDHistoryID', sa.Integer(), nullable=False),
    sa.Column('LoanMstID', sa.Integer(), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('InsertionDate', sa.DateTime(), nullable=True),
    sa.Column('DeletionDate', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='fk_bankmst'),
    sa.ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='fk_loanmst'),
    sa.PrimaryKeyConstraint('DNDHistoryID', name='DNDHistory_pkey')
    )
    op.create_table('Dialer',
    sa.Column('DialerID', sa.Integer(), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('BranchMstID', sa.Integer(), nullable=False),
    sa.Column('LoanMstID', sa.Integer(), nullable=False),
    sa.Column('Call_ID', sa.String(length=50), nullable=False),
    sa.Column('DateTime', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('RecordingURL', sa.Text(), nullable=False),
    sa.Column('CustomerMstID', sa.Integer(), nullable=True),
    sa.Column('InitiatingNumber', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='Dialer_fk_BankMstID', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='Dialer_fk_BranchMstID', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='Dialer_fk_LoanMstID', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('DialerID', name='Dialer_pkey')
    )
    op.create_table('IVRCallHistory',
    sa.Column('IVRCallHistoryID', sa.Integer(), nullable=False),
    sa.Column('IVRCallQueueID', sa.Integer(), nullable=False),
    sa.Column('CreatedDate', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('BranchMstID', sa.Integer(), nullable=False),
    sa.Column('LoanMstID', sa.Integer(), nullable=False),
    sa.Column('DisbursementID', sa.String(length=255), nullable=False),
    sa.Column('CustomerName', sa.String(length=255), nullable=False),
    sa.Column('IsSent', sa.Boolean(), server_default=sa.text('false'), nullable=False),
    sa.Column('BankName', sa.String(length=255), nullable=False),
    sa.Column('BranchCode', sa.String(), nullable=True),
    sa.Column('CustomerID', sa.Integer(), nullable=True),
    sa.Column('ContactNumber', sa.String(length=15), nullable=True),
    sa.Column('LoanType', sa.String(length=100), nullable=True),
    sa.Column('Next_EMI_Amount', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('Next_EMI_Date', sa.Date(), nullable=True),
    sa.Column('Total_Collection', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('Latest_CollectedDate', sa.Date(), nullable=True),
    sa.Column('Latest_CollectedAmt', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('Type', sa.String(length=50), nullable=True),
    sa.Column('Identifier', sa.String(length=255), nullable=True),
    sa.Column('CallID', sa.String(length=255), nullable=True),
    sa.Column('Recording', sa.String(length=255), nullable=True),
    sa.Column('Conversation_json', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('Conclusion', sa.String(length=255), nullable=True),
    sa.Column('CallTried', sa.Integer(), nullable=True),
    sa.Column('CallDuration', sa.Double(precision=53), nullable=True),
    sa.Column('CustomerCode', sa.String(length=50), nullable=True),
    sa.Column('OverDueAmt', sa.Numeric(precision=18, scale=2), nullable=True),
    sa.Column('Language', sa.String(length=100), nullable=True),
    sa.Column('Slot', sa.String(length=255), nullable=True),
    sa.Column('CallConnected', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('BasedOn', sa.String(), nullable=True),
    sa.Column('CommFlowID', sa.Integer(), nullable=True),
    sa.Column('FlowID', sa.Integer(), nullable=True),
    sa.Column('IVRTemplateMappingID', sa.Integer(), nullable=True),
    sa.Column('IsDelivered', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('IsRead', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('IsResponded', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='IVRCallHistory_fk_BankMstID'),
    sa.ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='IVRCallHistory_fk_BranchMstID'),
    sa.ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='IVRCallHistory_fk_LoanMstID'),
    sa.PrimaryKeyConstraint('IVRCallHistoryID', name='IVRCallHistory_pkey'),
    sa.UniqueConstraint('IVRCallQueueID', name='IVRCallHistory_Unique_IVRQueueID'),
    sa.UniqueConstraint('IVRCallQueueID', name='unique_ivrcallqueueid_history')
    )
    op.create_table('IVRQueue',
    sa.Column('IVRQueueID', sa.Integer(), nullable=False),
    sa.Column('CreatedDate', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('BranchMstID', sa.Integer(), nullable=False),
    sa.Column('LoanMstID', sa.Integer(), nullable=False),
    sa.Column('BranchCode', sa.String(length=50), nullable=False),
    sa.Column('LoanType', sa.String(length=100), nullable=False),
    sa.Column('CustomerName', sa.String(length=255), nullable=False),
    sa.Column('Language', sa.String(length=100), nullable=False),
    sa.Column('BankName', sa.String(length=255), nullable=False),
    sa.Column('CustomerCode', sa.String(length=50), nullable=True),
    sa.Column('OverDueAmt', sa.Numeric(precision=18, scale=2), nullable=True),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.Column('Slot', sa.String(length=255), nullable=True),
    sa.Column('CallTried', sa.Integer(), server_default=sa.text('0'), nullable=True),
    sa.Column('CallConnected', sa.Boolean(), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.Column('BasedOn', sa.String(), nullable=True),
    sa.Column('CommFlowID', sa.Integer(), nullable=True),
    sa.Column('DisbursementID', sa.String(), nullable=True),
    sa.Column('CustomerID', sa.Integer(), nullable=True),
    sa.Column('ContactNumber', sa.String(), nullable=True),
    sa.Column('Next_EMI_Amount', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('Next_EMI_Date', sa.Date(), nullable=True),
    sa.Column('Total_Collection', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('Latest_CollectedDate', sa.Date(), nullable=True),
    sa.Column('Latest_CollectedAmt', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('Type', sa.String(length=50), nullable=True),
    sa.Column('FlowID', sa.Integer(), nullable=True),
    sa.Column('IVRTemplateMappingID', sa.Integer(), nullable=True),
    sa.Column('Identifier', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='IVRQueue_BankMstID_fkey'),
    sa.ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='IVRQueue_BranchMstID_fkey'),
    sa.ForeignKeyConstraint(['CommFlowID'], ['CommFlow.CommFlowID'], name='fk_commflow_id', ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='IVRQueue_LoanMstID_fkey'),
    sa.PrimaryKeyConstraint('IVRQueueID', name='IVRQueue_pkey')
    )
    op.create_table('LoanInstallmentMst',
    sa.Column('InstallmentMstID', sa.Integer(), nullable=False),
    sa.Column('LoanMstID', sa.Integer(), nullable=False),
    sa.Column('Demand', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('ScheduleID', sa.Integer(), nullable=True),
    sa.Column('ScheduleDate', sa.Date(), nullable=True),
    sa.Column('PrincipleAmt', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('InterestAmt', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.Column('DisbursementID', sa.String(length=255), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.Column('CustomerMstID', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='LoanInstallmentMst_BankMstID_fkey'),
    sa.ForeignKeyConstraint(['CustomerMstID'], ['CustomerMst.CustomerMstID'], name='LoanInstallmentMst_CustomerMstID_fkey'),
    sa.ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='LoanInstallmentMst_LoanMstID_fkey'),
    sa.PrimaryKeyConstraint('InstallmentMstID', name='LoanInstallmentMst_pkey')
    )
    op.create_table('Response',
    sa.Column('ResponseID', sa.Integer(), nullable=False),
    sa.Column('LoanMstID', sa.Integer(), nullable=False),
    sa.Column('BlasterQueueID', sa.Integer(), nullable=True),
    sa.Column('IVRQueueID', sa.Integer(), nullable=True),
    sa.Column('VoiceBotQueueID', sa.Integer(), nullable=True),
    sa.Column('WhatsappQueueID', sa.Integer(), nullable=True),
    sa.Column('AllocationID', sa.Integer(), nullable=True),
    sa.Column('FeedbackID', sa.Integer(), nullable=True),
    sa.Column('ModeOfPayment', sa.String(length=50), nullable=True),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.Column('PromiseDateTime', sa.DateTime(), nullable=True),
    sa.Column('Status', sa.String(length=50), nullable=True),
    sa.Column('Amount', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('ResponseDateTime', sa.DateTime(), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.Column('BranchMstID', sa.Integer(), nullable=True),
    sa.Column('wrong_number', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='Response_BankMstID_fkey'),
    sa.ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='Response_BranchMstID_fkey'),
    sa.ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='Response_fkey_LOANID'),
    sa.PrimaryKeyConstraint('ResponseID', name='Response_pkey')
    )
    op.create_table('SMSHistory',
    sa.Column('SMSHistoryID', sa.Integer(), nullable=False),
    sa.Column('SMSQueueID', sa.Integer(), nullable=False),
    sa.Column('CreatedDate', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('BranchMstID', sa.Integer(), nullable=False),
    sa.Column('LoanMstID', sa.Integer(), nullable=False),
    sa.Column('CustomerID', sa.Integer(), nullable=False),
    sa.Column('DisbursementID', sa.String(length=255), nullable=False),
    sa.Column('ContactNumber', sa.String(length=15), nullable=False),
    sa.Column('LoanType', sa.String(length=100), nullable=False),
    sa.Column('CustomerName', sa.String(length=255), nullable=False),
    sa.Column('IsSent', sa.Boolean(), server_default=sa.text('false'), nullable=False),
    sa.Column('IsDelivered', sa.Boolean(), server_default=sa.text('false'), nullable=False),
    sa.Column('IsRead', sa.Boolean(), server_default=sa.text('false'), nullable=False),
    sa.Column('IsResponded', sa.Boolean(), server_default=sa.text('false'), nullable=False),
    sa.Column('Type', sa.String(length=50), nullable=False),
    sa.Column('BankName', sa.String(length=255), nullable=False),
    sa.Column('BranchName', sa.String(length=255), nullable=True),
    sa.Column('Next_EMI_Amount', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('Next_EMI_Date', sa.Date(), nullable=True),
    sa.Column('Total_Collection', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('Latest_CollectedDate', sa.Date(), nullable=True),
    sa.Column('Latest_CollectedAmt', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('Response', sa.Text(), nullable=True),
    sa.Column('Identifier', sa.String(length=255), nullable=True),
    sa.Column('Conversation_json', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('Conclusion', sa.String(length=255), nullable=True),
    sa.Column('Attempts', sa.Integer(), nullable=True),
    sa.Column('BranchCode', sa.String(length=50), nullable=True),
    sa.Column('CustomerCode', sa.String(length=50), nullable=True),
    sa.Column('OverDueAmt', sa.Numeric(precision=18, scale=2), nullable=True),
    sa.Column('Language', sa.String(length=100), nullable=True),
    sa.Column('Slot', sa.String(length=255), nullable=True),
    sa.Column('BasedOn', sa.String(), nullable=True),
    sa.Column('CommFlowID', sa.Integer(), nullable=True),
    sa.Column('FlowID', sa.Integer(), nullable=True),
    sa.Column('SMSTemplateMappingID', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='SMSHistory_fk_BankMstID'),
    sa.ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='SMSHistory_fk_BranchMstID'),
    sa.ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='SMSHistory_fk_LoanMstID'),
    sa.PrimaryKeyConstraint('SMSHistoryID', name='SMSHistory_pkey'),
    sa.UniqueConstraint('SMSQueueID', name='unique_smsqueueid_history')
    )
    op.create_table('SMSQueue',
    sa.Column('SMSQueueID', sa.Integer(), nullable=False),
    sa.Column('CreatedDate', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('BranchMstID', sa.Integer(), nullable=False),
    sa.Column('LoanMstID', sa.Integer(), nullable=False),
    sa.Column('BranchCode', sa.String(length=50), nullable=False),
    sa.Column('LoanType', sa.String(length=100), nullable=False),
    sa.Column('CustomerName', sa.String(length=255), nullable=False),
    sa.Column('BankName', sa.String(length=255), nullable=False),
    sa.Column('CustomerCode', sa.String(length=50), nullable=True),
    sa.Column('Language', sa.String(length=100), nullable=True),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.Column('Slot', sa.String(length=255), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.Column('BasedOn', sa.String(), nullable=True),
    sa.Column('CommFlowID', sa.Integer(), nullable=True),
    sa.Column('CustomerID', sa.Integer(), nullable=True),
    sa.Column('DisbursementID', sa.String(length=255), nullable=True),
    sa.Column('ContactNumber', sa.String(length=15), nullable=True),
    sa.Column('BranchName', sa.String(length=255), nullable=True),
    sa.Column('Next_EMI_Amount', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('Next_EMI_Date', sa.Date(), nullable=True),
    sa.Column('Total_Collection', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('Latest_CollectedDate', sa.Date(), nullable=True),
    sa.Column('Latest_CollectedAmt', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('Type', sa.String(length=50), nullable=True),
    sa.Column('LanguageID', sa.Integer(), nullable=True),
    sa.Column('Identifier', sa.String(length=255), nullable=True),
    sa.Column('FlowID', sa.Integer(), nullable=True),
    sa.Column('SMSTemplateMappingID', sa.Integer(), nullable=True),
    sa.Column('OverDueAmt', sa.Numeric(precision=18, scale=2), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='SMSQueue_BankMstID_fkey'),
    sa.ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='SMSQueue_BranchMstID_fkey'),
    sa.ForeignKeyConstraint(['CommFlowID'], ['CommFlow.CommFlowID'], name='fk_commflow_id', ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='SMSQueue_LoanMstID_fkey'),
    sa.PrimaryKeyConstraint('SMSQueueID', name='SMSQueue_pkey')
    )
    op.create_table('SecondaryCustMst',
    sa.Column('SecondaryCustMstID', sa.Integer(), nullable=False),
    sa.Column('ApplicantName', sa.String(length=255), nullable=False),
    sa.Column('BranchMstID', sa.Integer(), nullable=True),
    sa.Column('DateOfBirth', sa.Date(), nullable=True),
    sa.Column('CoApplicantContact', sa.String(length=20), nullable=True),
    sa.Column('IsActive', sa.Boolean(), server_default=sa.text('true'), nullable=True),
    sa.Column('LastModifiedDate', sa.Date(), nullable=True),
    sa.Column('DisbursementID', sa.String(length=255), nullable=True),
    sa.Column('LoanMstID', sa.Integer(), nullable=True),
    sa.Column('Gender', sa.String(length=10), nullable=True),
    sa.Column('MobileNumber', sa.String(length=20), nullable=True),
    sa.Column('AlternativeMobileNumber', sa.String(length=20), nullable=True),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='SecondaryCustMst_BankMstID_fkey'),
    sa.ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='CoApplicantMst_BranchID_fkey'),
    sa.ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='CoApplicantMst_LoanID_fkey'),
    sa.PrimaryKeyConstraint('SecondaryCustMstID', name='CoApplicantMst_pkey')
    )
    op.create_table('Transactions',
    sa.Column('TransactionID', sa.Integer(), nullable=False),
    sa.Column('BranchMstID', sa.Integer(), nullable=False),
    sa.Column('LoanMstID', sa.Integer(), nullable=False),
    sa.Column('CreatedDate', sa.Date(), server_default=sa.text('CURRENT_DATE'), nullable=True),
    sa.Column('DisbursementID', sa.String(length=255), nullable=True),
    sa.Column('CollectedAmt', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('CollectedDate', sa.Date(), nullable=True),
    sa.Column('Processed', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('PrincipleCollected', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('InterestCollected', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.Column('BranchName', sa.String(), nullable=True),
    sa.Column('LoanType', sa.String(), nullable=True),
    sa.Column('CustomerId', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='Transactions_BankMstID_fkey'),
    sa.ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='Transactions_BranchID_fkey'),
    sa.ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='Transactions_LoanID_fkey'),
    sa.PrimaryKeyConstraint('TransactionID', name='Transactions_pkey')
    )
    op.create_table('UserFeedback',
    sa.Column('UserFeedbackID', sa.Integer(), nullable=False),
    sa.Column('LoanMstID', sa.Integer(), nullable=False),
    sa.Column('FeedbackDate', sa.Date(), nullable=False),
    sa.Column('AgreedToPay', sa.Boolean(), nullable=False),
    sa.Column('RefusedToPay', sa.Boolean(), nullable=False),
    sa.Column('WrongNumber', sa.Boolean(), nullable=False),
    sa.Column('CustomerBusy', sa.Boolean(), nullable=False),
    sa.Column('BranchMstID', sa.Integer(), nullable=True),
    sa.Column('promise_date', sa.Date(), nullable=True),
    sa.Column('promise_amount', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('ReasonForDenial', sa.Text(), nullable=True),
    sa.Column('mode_of_payment', sa.String(length=255), nullable=True),
    sa.Column('CollectionDate', sa.String(length=255), nullable=True),
    sa.Column('CollectionAmount', sa.String(length=255), nullable=True),
    sa.Column('CustomerNotReply', sa.String(length=255), nullable=True),
    sa.Column('OtherComments', sa.String(length=255), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='UserFeedback_BankMstID_fkey'),
    sa.ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='UserFeedback_fk_BranchID', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='UserFeedback_fk_LoanID', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('UserFeedbackID', name='UserFeedback_pkey')
    )
    op.create_table('VoiceBotHistory',
    sa.Column('VoiceBotHistoryID', sa.Integer(), nullable=False),
    sa.Column('VoiceBotQueueID', sa.Integer(), nullable=False),
    sa.Column('CreatedDate', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('BranchMstID', sa.Integer(), nullable=False),
    sa.Column('LoanMstID', sa.Integer(), nullable=False),
    sa.Column('BranchCode', sa.String(), nullable=False),
    sa.Column('LoanType', sa.String(length=100), nullable=False),
    sa.Column('Type', sa.String(length=50), nullable=False),
    sa.Column('BankName', sa.String(length=255), nullable=False),
    sa.Column('CustomerID', sa.Integer(), nullable=True),
    sa.Column('DisbursementID', sa.String(length=255), nullable=True),
    sa.Column('ContactNumber', sa.String(length=15), nullable=True),
    sa.Column('BranchName', sa.String(length=255), nullable=True),
    sa.Column('CustomerName', sa.String(length=255), nullable=True),
    sa.Column('Overdue_Amount', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('Next_EMI_Amount', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('Next_EMI_Date', sa.Date(), nullable=True),
    sa.Column('Total_Collection', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('Latest_CollectedDate', sa.Date(), nullable=True),
    sa.Column('Latest_CollectedAmt', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('IsSent', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('IsDelivered', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('IsRead', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('IsResponded', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('Response', sa.Text(), nullable=True),
    sa.Column('Identifier', sa.String(length=255), nullable=True),
    sa.Column('CallID', sa.String(length=255), nullable=True),
    sa.Column('Recording', sa.String(length=255), nullable=True),
    sa.Column('Conversation_json', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('Conclusion', sa.String(length=255), nullable=True),
    sa.Column('CallTried', sa.Integer(), nullable=True),
    sa.Column('CallDuration', sa.Double(precision=53), nullable=True),
    sa.Column('BasedOn', sa.String(), nullable=True),
    sa.Column('CommFlowID', sa.Integer(), nullable=True),
    sa.Column('CustomerCode', sa.String(), nullable=True),
    sa.Column('Language', sa.String(), nullable=True),
    sa.Column('OverdueAmt', sa.Numeric(precision=18, scale=2), nullable=True),
    sa.Column('Slot', sa.String(length=255), nullable=True),
    sa.Column('FlowID', sa.Integer(), nullable=True),
    sa.Column('VoiceBotTemplateMappingID', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='VoiceBotHistory_fk_BankMstID'),
    sa.ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='VoiceBotHistory_fk_BranchMstID'),
    sa.ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='VoiceBotHistory_fk_LoanMstID'),
    sa.PrimaryKeyConstraint('VoiceBotHistoryID', name='VoiceBotHistory_pkey'),
    sa.UniqueConstraint('VoiceBotQueueID', name='unique_voicebotqueueid_history')
    )
    op.create_table('VoiceBotQueue',
    sa.Column('VoiceBotQueueID', sa.Integer(), nullable=False),
    sa.Column('CreatedDate', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('BranchMstID', sa.Integer(), nullable=False),
    sa.Column('LoanMstID', sa.Integer(), nullable=False),
    sa.Column('BranchCode', sa.String(length=50), nullable=False),
    sa.Column('LoanType', sa.String(length=100), nullable=False),
    sa.Column('CustomerName', sa.String(length=255), nullable=False),
    sa.Column('BankName', sa.String(length=255), nullable=False),
    sa.Column('CustomerCode', sa.String(length=50), nullable=True),
    sa.Column('OverdueAmt', sa.Numeric(precision=18, scale=2), nullable=True),
    sa.Column('Language', sa.String(length=100), nullable=True),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.Column('Slot', sa.String(length=255), nullable=True),
    sa.Column('CallTried', sa.Integer(), server_default=sa.text('0'), nullable=True),
    sa.Column('CallConnected', sa.Boolean(), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.Column('BasedOn', sa.String(), nullable=True),
    sa.Column('CommFlowID', sa.Integer(), nullable=True),
    sa.Column('ContactNumber', sa.String(), nullable=True),
    sa.Column('CustomerID', sa.Integer(), nullable=True),
    sa.Column('DisbursementID', sa.String(length=255), nullable=True),
    sa.Column('BranchID', sa.Integer(), nullable=True),
    sa.Column('BranchName', sa.String(length=255), nullable=True),
    sa.Column('Next_EMI_Amount', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('Next_EMI_Date', sa.Date(), nullable=True),
    sa.Column('Total_Collection', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('Latest_CollectedDate', sa.Date(), nullable=True),
    sa.Column('Latest_CollectedAmt', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('Type', sa.String(length=50), nullable=True),
    sa.Column('Identifier', sa.String(length=255), nullable=True),
    sa.Column('FlowID', sa.Integer(), nullable=True),
    sa.Column('VoiceBotTemplateMappingID', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='VoiceBotQueue_BankMstID_fkey'),
    sa.ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='VoiceBotQueue_BranchMstID_fkey'),
    sa.ForeignKeyConstraint(['CommFlowID'], ['CommFlow.CommFlowID'], name='fk_commflow_id', ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='VoiceBotQueue_LoanMstID_fkey'),
    sa.PrimaryKeyConstraint('VoiceBotQueueID', name='VoiceBotQueue_pkey')
    )
    op.create_table('WhatsAppHistory',
    sa.Column('WhatsAppHistoryID', sa.Integer(), nullable=False),
    sa.Column('WhatsAppQueueID', sa.Integer(), nullable=False),
    sa.Column('CreatedDate', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('BankMstID', sa.Integer(), nullable=False),
    sa.Column('BranchMstID', sa.Integer(), nullable=False),
    sa.Column('LoanMstID', sa.Integer(), nullable=False),
    sa.Column('BranchCode', sa.String(), nullable=False),
    sa.Column('DisbursementID', sa.String(length=255), nullable=False),
    sa.Column('CustomerName', sa.String(length=255), nullable=False),
    sa.Column('BankName', sa.String(length=255), nullable=False),
    sa.Column('CustomerID', sa.Integer(), nullable=True),
    sa.Column('ContactNumber', sa.String(length=15), nullable=True),
    sa.Column('BranchName', sa.String(length=255), nullable=True),
    sa.Column('LoanType', sa.String(length=100), nullable=True),
    sa.Column('Overdue_Amount', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('Next_EMI_Amount', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('Next_EMI_Date', sa.Date(), nullable=True),
    sa.Column('Total_Collection', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('Latest_CollectedDate', sa.Date(), nullable=True),
    sa.Column('Latest_CollectedAmt', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('IsSent', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('IsDelivered', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('IsRead', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('IsResponded', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('Response', sa.Text(), nullable=True),
    sa.Column('Type', sa.String(length=50), nullable=True),
    sa.Column('Identifier', sa.String(length=50), nullable=True),
    sa.Column('LngMstID', sa.Integer(), nullable=True),
    sa.Column('BasedOn', sa.String(), nullable=True),
    sa.Column('CommFlowID', sa.Integer(), nullable=True),
    sa.Column('CustomerCode', sa.String(), nullable=True),
    sa.Column('Language', sa.String(), nullable=True),
    sa.Column('FlowID', sa.Integer(), nullable=True),
    sa.Column('WhatsAppTemplateMappingID', sa.Integer(), nullable=True),
    sa.Column('OverdueAmt', sa.Numeric(precision=18, scale=2), nullable=True),
    sa.Column('Slot', sa.String(length=255), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='WhatsAppHistory_fk_BankMstID'),
    sa.ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='WhatsAppHistory_fk_BranchMstID'),
    sa.ForeignKeyConstraint(['LngMstID'], ['LanguageMst.LngMstID'], name='WhatsAppHistory_fk_LanguageID'),
    sa.ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='WhatsAppHistory_fk_LoanMstID'),
    sa.PrimaryKeyConstraint('WhatsAppHistoryID', name='WhatsAppHistory_pkey'),
    sa.UniqueConstraint('WhatsAppQueueID', name='unique_whatsappqueueid_history')
    )
    op.create_table('WhatsAppQueue',
    sa.Column('WhatsAppQueueID', sa.Integer(), nullable=False),
    sa.Column('CreatedDate', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('BranchMstID', sa.Integer(), nullable=False),
    sa.Column('LoanMstID', sa.Integer(), nullable=False),
    sa.Column('BranchCode', sa.String(length=50), nullable=False),
    sa.Column('LoanType', sa.String(length=100), nullable=False),
    sa.Column('CustomerCode', sa.String(length=50), nullable=False),
    sa.Column('CustomerName', sa.String(length=255), nullable=False),
    sa.Column('BankName', sa.String(length=255), nullable=False),
    sa.Column('OverdueAmt', sa.Numeric(precision=18, scale=2), nullable=True),
    sa.Column('Language', sa.String(length=100), nullable=True),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.Column('Slot', sa.String(length=255), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.Column('BasedOn', sa.String(), nullable=True),
    sa.Column('CommFlowID', sa.Integer(), nullable=True),
    sa.Column('ContactNumber', sa.String(), nullable=True),
    sa.Column('FlowID', sa.Integer(), nullable=True),
    sa.Column('WhatsAppTemplateMappingID', sa.Integer(), nullable=True),
    sa.Column('CustomerID', sa.Integer(), nullable=True),
    sa.Column('DisbursementID', sa.String(length=255), nullable=True),
    sa.Column('Next_EMI_Amount', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('Next_EMI_Date', sa.Date(), nullable=True),
    sa.Column('Total_Collection', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('Latest_CollectedDate', sa.Date(), nullable=True),
    sa.Column('Latest_CollectedAmt', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('Response', sa.Text(), nullable=True),
    sa.Column('Type', sa.String(length=50), nullable=True),
    sa.Column('Identifier', sa.String(length=50), nullable=True),
    sa.Column('LngMstID', sa.Integer(), nullable=True),
    sa.Column('BranchName', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='WhatsAppQueue_BankMstID_fkey'),
    sa.ForeignKeyConstraint(['BranchMstID'], ['BranchMst.BranchMstID'], name='WhatsAppQueue_BranchMstID_fkey'),
    sa.ForeignKeyConstraint(['CommFlowID'], ['CommFlow.CommFlowID'], name='fk_commflow_id', ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='WhatsAppQueue_LoanMstID_fkey'),
    sa.PrimaryKeyConstraint('WhatsAppQueueID', name='WhatsAppQueue_pkey')
    )
    op.create_table('WhatsApp_Messages',
    sa.Column('WhatsAppMessageID', sa.Integer(), nullable=False),
    sa.Column('MessageDate', sa.Date(), nullable=False),
    sa.Column('MessageID', sa.String(length=255), nullable=False),
    sa.Column('Sender', sa.String(length=255), nullable=False),
    sa.Column('Content', sa.Text(), nullable=False),
    sa.Column('Status', sa.String(length=50), nullable=False),
    sa.Column('WhatsAppQueueID', sa.Integer(), nullable=True),
    sa.Column('AssignedTo', sa.String(length=255), nullable=True),
    sa.Column('CustomerName', sa.String(length=255), nullable=True),
    sa.Column('CustomerNumber', sa.String(length=20), nullable=True),
    sa.Column('LoanMstID', sa.Integer(), nullable=True),
    sa.Column('Button', sa.String(), nullable=True),
    sa.Column('Type', sa.String(), nullable=True),
    sa.Column('Identifier', sa.String(), nullable=True),
    sa.Column('WhatsAppUserTemplateID', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='WhatsAppMessages_LoanMstID_fkey'),
    sa.PrimaryKeyConstraint('WhatsAppMessageID', name='WhatsApp_Messages_pkey')
    )
    op.create_table('CoAppMst',
    sa.Column('CoAppMstID', sa.Integer(), nullable=False),
    sa.Column('LoanMstID', sa.Integer(), nullable=True),
    sa.Column('SecondaryCustMstID', sa.Integer(), nullable=True),
    sa.Column('CoAppType', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn1', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn2', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn3', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn4', sa.String(length=255), nullable=True),
    sa.Column('ExtraColumn5', sa.String(length=255), nullable=True),
    sa.Column('BankMstID', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['BankMstID'], ['BankMst.BankMstID'], name='CoAppMst_BankMstID_fkey'),
    sa.ForeignKeyConstraint(['LoanMstID'], ['LoanMst.LoanMstID'], name='CoAppMst_LoanMstID_fkey'),
    sa.ForeignKeyConstraint(['SecondaryCustMstID'], ['SecondaryCustMst.SecondaryCustMstID'], name='CoAppMst_SecondaryCustMstID_fkey'),
    sa.PrimaryKeyConstraint('CoAppMstID', name='CoAppMst_pkey')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('CoAppMst')
    op.drop_table('WhatsApp_Messages')
    op.drop_table('WhatsAppQueue')
    op.drop_table('WhatsAppHistory')
    op.drop_table('VoiceBotQueue')
    op.drop_table('VoiceBotHistory')
    op.drop_table('UserFeedback')
    op.drop_table('Transactions')
    op.drop_table('SecondaryCustMst')
    op.drop_table('SMSQueue')
    op.drop_table('SMSHistory')
    op.drop_table('Response')
    op.drop_table('LoanInstallmentMst')
    op.drop_table('IVRQueue')
    op.drop_table('IVRCallHistory')
    op.drop_table('Dialer')
    op.drop_table('DNDHistory')
    op.drop_table('CommunicationQueue')
    op.drop_table('BlasterQueue')
    op.drop_table('BlasterHistory')
    op.drop_table('BMAllocation')
    op.drop_table('AccountSummary')
    op.drop_index('django_admin_log_user_id_c564eba6', table_name='django_admin_log')
    op.drop_index('django_admin_log_content_type_id_c4bce8eb', table_name='django_admin_log')
    op.drop_table('django_admin_log')
    op.drop_index('UserMst_user_permissions_usermst_id_ca7abfab', table_name='UserMst_user_permissions')
    op.drop_index('UserMst_user_permissions_permission_id_06930a11', table_name='UserMst_user_permissions')
    op.drop_table('UserMst_user_permissions')
    op.drop_index('UserMst_groups_usermst_id_d5c7157a', table_name='UserMst_groups')
    op.drop_index('UserMst_groups_group_id_bfa15f2a', table_name='UserMst_groups')
    op.drop_table('UserMst_groups')
    op.drop_index('LoginHistory_user_id_b2cfd3c6', table_name='LoginHistory')
    op.drop_index('LoginHistory_ip_19de82e3', table_name='LoginHistory')
    op.drop_table('LoginHistory')
    op.drop_table('LoanMst')
    op.drop_table('WhatsappVariableMapping')
    op.drop_table('WhatsAppUserTemplate')
    op.drop_table('WhatsAppFlowMapping')
    op.drop_table('VoiceBotUserTemplate')
    op.drop_index('UserMst_username_219fb066_like', table_name='UserMst')
    op.drop_index('UserMst_BranchMstID_id_224814fd', table_name='UserMst')
    op.drop_index('UserMst_BankMstID_id_4b4302a0', table_name='UserMst')
    op.drop_table('UserMst')
    op.drop_table('SMSUserTemplate')
    op.drop_table('IVRVariableMapping')
    op.drop_table('IVRUserTemplate')
    op.drop_table('IVRFlowMapping')
    op.drop_table('FilterMst')
    op.drop_table('EmployeeAccess')
    op.drop_table('CustomerMst')
    op.drop_table('BlasterUserTemplate')
    op.drop_index('auth_group_permissions_permission_id_84c5c92e', table_name='auth_group_permissions')
    op.drop_index('auth_group_permissions_group_id_b120cbf9', table_name='auth_group_permissions')
    op.drop_table('auth_group_permissions')
    op.drop_table('WhatsAppTemplateMapping')
    op.drop_table('VoiceBotTemplateMapping')
    op.drop_table('UserConversations_completed_conversations')
    op.drop_table('UserConversations_active_conversations')
    op.drop_table('SMSTemplateMapping')
    op.drop_table('PeriodicCommunication')
    op.drop_table('IVRTemplateMapping')
    op.drop_table('EmployeeMst')
    op.drop_table('CommFlow')
    op.drop_table('CollectionOfficerAllocation')
    op.drop_table('BranchMst')
    op.drop_table('BlasterTemplateMapping')
    op.drop_table('bankcontrols')
    op.drop_index('auth_permission_content_type_id_2f476e4b', table_name='auth_permission')
    op.drop_table('auth_permission')
    op.drop_table('WhatsAppKeyMapping')
    op.drop_table('WhatsAppFlowMst')
    op.drop_index('idx_userconversations_user_id', table_name='UserConversations')
    op.drop_table('UserConversations')
    op.drop_table('SubscriptionMst')
    op.drop_table('PriorityTable')
    op.drop_table('PT_ExtraColumn')
    op.drop_table('LanguageMst')
    op.drop_table('IVRFlowMst')
    op.drop_index('idx_geographymst_reportingbuid', table_name='GeographyMst')
    op.drop_index('idx_geographymst_butype', table_name='GeographyMst')
    op.drop_index('idx_geographymst_buname', table_name='GeographyMst')
    op.drop_index('idx_geographymst_bucode', table_name='GeographyMst')
    op.drop_index('idx_geographymst_bankmstid', table_name='GeographyMst')
    op.drop_table('GeographyMst')
    op.drop_table('Filters')
    op.drop_table('DisbursementFile')
    op.drop_table('DesignationMst')
    op.drop_table('DPDCategory')
    op.drop_table('ConversationMessage')
    op.drop_table('CommFlowMst')
    op.drop_table('CampaignMst')
    op.drop_table('where_conditions')
    op.drop_table('filter_count')
    op.drop_table('engagement_data')
    op.drop_table('dynamic_sql')
    op.drop_index('django_session_session_key_c0390e0f_like', table_name='django_session')
    op.drop_index('django_session_expire_date_a5c62663', table_name='django_session')
    op.drop_table('django_session')
    op.drop_table('django_migrations')
    op.drop_table('django_content_type')
    op.drop_table('dashboard_data')
    op.drop_table('communication_type')
    op.drop_table('commflow_id')
    op.drop_table('campaign_count')
    op.drop_table('campaign_blasterusertemplate')
    op.drop_index('auth_group_name_a6ea08ec_like', table_name='auth_group')
    op.drop_table('auth_group')
    op.drop_table('WrongNumberHistory')
    op.drop_table('Transaction_Logs')
    op.drop_table('RecordingSummary')
    op.drop_table('PaymentLogs')
    op.drop_table('ConversationHistory')
    op.drop_table('ComponentMst')
    op.drop_table('CommunicationMapping')
    op.drop_table('CollectionFile')
    op.drop_table('BankMst')
    op.drop_table('ActiveConversation')
    op.drop_table('AccountSummaryHistory')
    # ### end Alembic commands ###

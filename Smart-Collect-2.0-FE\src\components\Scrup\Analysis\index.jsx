import { Col, Flex, message, Row, Segmented, Typography } from "antd";
import React, { useEffect, useState } from "react";
import { ScrubTable } from "./ScrubTable";
import AllCards from "./AllCards";
import { Bar<PERSON>hart } from "./BarChart";
import { Donut<PERSON>hart } from "./DonutChart";
import Style from "./_style.module.scss";
import { AXIOS } from "../../../apis/ho-Instance";
import { handleDownloadTable } from "../../../constant";
import DOWNLOAD_IMG from "../../../assets/Images/download.svg";
import { CustomerDetailsTable } from "./CustomerDetailsTable";
import ApplicationLoader from "../../ApplicationLoader";

const { Text, Title } = Typography;

export function Analysis() {
  const USER_DETAILS = JSON.parse(localStorage.getItem("user"));
  const [stateView, setStateView] = useState("Graph");
  const [tableType, setTableType] = useState("Account");
  const [selectedAnalysisCard, setSelectedAnalysisCard] = useState(
    "both_within_3_months_card"
  );
  const [analysisData, setAnalysisData] = useState({});
  const [stateTableData, setStateTableData] = useState([]);
  const [rdGraphData, setRdGraphData] = useState({});
  const [isLoading] = useState(false);
  const [isCustomerLoading, setIsCustomerLoading] = useState(false);
  const [selectedState, setSelectedState] = useState("all");
  const [dataSelectedState, setDataSelectedState] = useState(null);
  const [tableLevel, setTableLevel] = useState("region");

  const [customersData, setCustomersData] = useState({
    current: 1,
    total: 1,
    data: [],
  });
  const [customerPayload, setCustomerPayload] = useState({
    state_id: "all",
    region_id: "all",
    branch_id: "all",
    riskCategory: "",
    cardWise: "",
  });
  const [banksData, setBanksData] = useState({
    account: [],
    amount: [],
  });
  const analysisCardTitleMapping = {
    both_within_3_months_card: {
      title: "Good in XYZ Bank Paying to others",
      dataKeyPrefix: "both_within_3_months_df",
      cardWisePrefix: "both_within_3_months",
    },
    hmpl_within_3_months_card: {
      title: "Good in XYZ not Paying",
      dataKeyPrefix: "hmpl_within_3_months_df",
      cardWisePrefix: "hmpl_within_3_months",
    },
    neither_within_3_months_card: {
      title: "Default in XYZ Bank Paying to Others",
      dataKeyPrefix: "neither_within_3_months_df",
      cardWisePrefix: "neither_within_3_months",
    },
    other_mfi_within_3_months_card: {
      title: "Default in XYZ Bank not Paying to Others",
      dataKeyPrefix: "other_mfi_within_3_months_df",
      cardWisePrefix: "other_mfi_within_3_months",
    },
  };

  // Handle format the dpd category
  const handleFormatCategory = (value) => {
    return value
      .split("_") // Split at _
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize each word
      .join(" "); // Join back with spaces
  };

  // Handle customer downloading
  const handleDownloadCustomer = async () => {
    const selectedData = customersData.data;
    if (selectedData.length === 0) {
      return message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: "Analysis Customer Distribution",
        worksheetName: "analysis-customer-distribution",
        tableData: selectedData,
      });
      message.success("data downloaded successfully!");
    }
  };

  const handleUpdateAnalysisData = async ({ type }) => {
    setSelectedAnalysisCard(type);
    setDataSelectedState(null);
    setSelectedState("all");
    setCustomersData([]);
    setCustomerPayload({
      state_id: "all",
      region_id: "all",
      branch_id: "all",
      riskCategory: "",
      cardWise: "",
    });
  };

  // Update the table data
  const handleUpdateStateTable = async ({
    stateId = "all",
    regionId = "all",
    branchId = "all",
    tablewise = "State",
    cardWise,
  }) => {
    const payload = {
      State_id: stateId === "all" ? "all" : [stateId],
      Region_id: regionId === "all" ? "all" : [regionId],
      Branch_id: branchId === "all" ? "all" : [branchId],
      table_wise: tablewise,
      card_wise: analysisCardTitleMapping[cardWise]?.cardWisePrefix,
      BankMstID: [USER_DETAILS?.BankMstID],
      CollectionOfficerID: "all",
      LoanType: "all",
    };
    try {
      const response = await AXIOS.post("scrub_analysis_hierarchy/", payload);
      if (response.status === 200) {
        const finalData = response.data;
        setBanksData({
          account: finalData?.account || [],
          amount: finalData?.amount || [],
        });
      } else {
        setBanksData({
          account: [],
          amount: [],
        });
        setDataSelectedState(null);
        setTableLevel(null);
      }
    } catch (error) {
      console.log("Error in State table", error?.message);
      message.warning(`Something is wrong, try again!`);
    }
  };

  // Handle get analysis data
  const handleGetAnalysisData = async () => {
    const payload = {
      State_id: "all",
      Region_id: "all",
      Branch_id: "all",
      CollectionOfficerID: "all",
      LoanType: "all",
      BankMstID: [USER_DETAILS?.BankMstID],
    };
    try {
      const res = await AXIOS.post("scrub_analysis_top/", payload);
      if (res.status === 200) {
        setAnalysisData(res.data);
      }
    } catch (error) {
      console.log("Error in get analysis data", error?.message);
    }
  };

  // Handle update the RD data based on the selected state
  const handleBarClick = (selectedState) => {
    const { very_low_risk, low_risk, medium_risk, high_risk, very_high_risk } =
      selectedState;
    setSelectedState(selectedState?.State_id);
    setRdGraphData({
      very_low_risk,
      low_risk,
      medium_risk,
      high_risk,
      very_high_risk,
    });
  };

  // Handle Get customer details
  const handleGetCustomerDetails = async ({
    state_id = "all",
    region_id = "all",
    branch_id = "all",
    riskCategory,
    page = 1,
    cardWise,
  }) => {
    setIsCustomerLoading(true);
    // Store customer payload
    setCustomerPayload({
      state_id,
      region_id,
      branch_id,
      riskCategory,
      cardWise,
    });

    const payload = {
      State_id: state_id === "all" ? "all" : [state_id],
      Region_id: region_id === "all" ? "all" : [region_id],
      Branch_id: branch_id === "all" ? "all" : [branch_id],
      CollectionOfficerID: "all",
      LoanType: "all",
      BankMstID: [USER_DETAILS?.BankMstID],
      risk_category: handleFormatCategory(riskCategory),
      card_wise: analysisCardTitleMapping[cardWise]?.cardWisePrefix,
      page_limit: 10,
      offset: page - 1,
    };
    try {
      const response = await AXIOS.post("scrub_analysis_customer/", payload);
      if (response.status === 200 && response.data.data.length) {
        setCustomersData({
          current: response.data?.current_page || 1,
          total: response.data?.total_records || 1,
          data: response.data?.data,
        });
        // Wait for state update to reflect in DOM
        setTimeout(() => {
          window.scrollTo({
            top: document.body.scrollHeight,
            behavior: "smooth",
          });
        }, 100);
      } else {
        message.warning("There are no customer data found!");
        setCustomersData({
          current: 1,
          total: 1,
          data: [],
        });
      }
    } catch (error) {
      console.log("Error in get customer details", error?.message);
      message.warning(`Something is wrong, try again!`);
    } finally {
      setIsCustomerLoading(false);
    }
  };

  // Handle customer pagination
  const handleCustomerPagination = async (page = 1) => {
    await handleGetCustomerDetails({
      state_id: customerPayload.state_id,
      region_id: customerPayload.region_id,
      card_wise: customerPayload.cardWise,
      riskCategory: customerPayload.riskCategory,
      branch_id: customerPayload.branch_id,
      page: page,
    });
  };

  // Update the state & graph table
  // useEffect(()=>{
  //   const key = `${analysisCardTitleMapping[selectedAnalysisCard]?.dataKeyPrefix}_${tableType.toLowerCase()}`;
  //   const selectedData = analysisData?.final_response?.[key];

  //   // Update the state
  //   if(selectedData){
  //     setStateTableData(selectedData?.states);
  //     setRdGraphData(selectedData?.total_risk_distribution);
  //   }
  // },[selectedAnalysisCard, tableType, analysisData]);

  useEffect(() => {
    const updatedStateTableData =
      tableType === "Account" ? banksData?.account : banksData?.amount;
    if (dataSelectedState && updatedStateTableData?.length) {
      const selectedStateRD = updatedStateTableData.find(
        (d) => d?.State_id === dataSelectedState
      );
      if (selectedStateRD) {
        const {
          very_low_risk,
          low_risk,
          medium_risk,
          high_risk,
          very_high_risk,
        } = selectedStateRD;
        setRdGraphData({
          very_low_risk,
          low_risk,
          medium_risk,
          high_risk,
          very_high_risk,
        });
      }
      setStateTableData(updatedStateTableData);
    } else {
      // No state selected — use root-level data from analysisData
      const key = `${
        analysisCardTitleMapping[selectedAnalysisCard]?.dataKeyPrefix
      }_${tableType?.toLowerCase()}`;
      const selectedData = analysisData?.final_response?.[key];

      if (selectedData) {
        setStateTableData(selectedData?.states);
        setRdGraphData(selectedData?.total_risk_distribution);
      }
    }
  }, [
    selectedState,
    tableType,
    selectedAnalysisCard,
    analysisData,
    dataSelectedState,
    banksData,
  ]);

  // Invoking get calls
  useEffect(() => {
    handleGetAnalysisData();
  }, []);
  return isLoading ? (
    <ApplicationLoader />
  ) : (
    <Flex vertical gap={20}>
      <AllCards
        data={analysisData}
        selectedAnalysisCard={selectedAnalysisCard}
        handleUpdateAnalysisData={handleUpdateAnalysisData}
      />

      {/* State 2 */}
      <Flex vertical gap={10} className={Style.table_container}>
        <Flex justify="space-between" align="center" wrap>
          <Title level={4} className={Style.title}>
            {analysisCardTitleMapping[selectedAnalysisCard]?.title}
          </Title>
          <Flex gap={10} align="center" wrap>
            <Segmented
              value={stateView}
              onChange={(value) => setStateView(value)}
              options={["Graph", "Table"]}
            />
            <Segmented
              value={tableType}
              onChange={(value) => setTableType(value)}
              options={["Account", "Amount"]}
            />
          </Flex>
        </Flex>
        {stateView === "Graph" && (
          <Row gutter={[10, 10]} justify="space-between">
            <Col md={12} xs={24} className={Style.chart_container}>
              {stateTableData?.length ? (
                <>
                  <Text className={Style.title}>
                    {" "}
                    {`Total ${tableType} by State`}{" "}
                  </Text>
                  <BarChart
                    data={stateTableData}
                    isAccount={tableType === "Account"}
                    onBarClick={handleBarClick}
                  />
                </>
              ) : (
                <div style={{ height: "200px" }}>
                  <Text
                    className={Style.title}
                    style={{ textAlign: "center", display: "block" }}
                  >
                    No Data Found
                  </Text>
                </div>
              )}
            </Col>
            <Col md={11} xs={24} className={Style.chart_container}>
              {stateTableData?.length ? (
                <>
                  <Text className={Style.title}>
                    {" "}
                    {`Risk Distribution for (${tableType})`}
                  </Text>
                  <DonutChart
                    data={rdGraphData}
                    isData={stateTableData?.length}
                    selectedAnalysisCard={selectedAnalysisCard}
                    selectedState={selectedState}
                    handleGetCustomerDetails={handleGetCustomerDetails}
                  />
                </>
              ) : (
                <Text
                  className={Style.title}
                  style={{ textAlign: "center", display: "block" }}
                >
                  {" "}
                  No Data Found
                </Text>
              )}
            </Col>
          </Row>
        )}
        <ScrubTable
          isGraph={stateView === "Graph"}
          tableData={stateTableData}
          isAccount={tableType === "Account"}
          selectedAnalysisCard={selectedAnalysisCard}
          selectedState={dataSelectedState}
          setSelectedState={setDataSelectedState}
          tableLevel={tableLevel}
          setTableLevel={setTableLevel}
          handleGetCustomerDetails={handleGetCustomerDetails}
          handleUpdateStateTable={handleUpdateStateTable}
        />
      </Flex>

      {/* Customer Details  */}
      <Flex
        vertical
        gap={20}
        className={Style.table_container}
        style={{ background: "white" }}
      >
        {isCustomerLoading && <ApplicationLoader />}
        {!isCustomerLoading && customersData.data?.length ? (
          <>
            <Flex justify="space-between" align="center" wrap>
              <Title level={4} className={Style.title}>
                {`Customer Distribution for "${handleFormatCategory(
                  customerPayload.riskCategory
                )}"`}
              </Title>
              <Flex gap={10} align="center" wrap>
                <button
                  className={Style.download_button}
                  onClick={handleDownloadCustomer}
                >
                  <img src={DOWNLOAD_IMG} alt="download-button" />
                </button>
              </Flex>
            </Flex>
            <CustomerDetailsTable
              customerDetails={customersData}
              onPaginationChange={handleCustomerPagination}
            />
          </>
        ) : null}
      </Flex>
    </Flex>
  );
}

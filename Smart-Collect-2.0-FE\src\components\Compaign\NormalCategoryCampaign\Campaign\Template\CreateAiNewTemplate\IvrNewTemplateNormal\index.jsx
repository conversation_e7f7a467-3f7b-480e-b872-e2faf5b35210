import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  But<PERSON>,
  Input,
  Select,
  DatePicker,
  Row,
  Col,
  message,
} from "antd";
import React, { useEffect, useRef, useState } from "react";
import Style from "../_style.module.scss";
import { Graph } from "@antv/x6";
import PropTypes from "prop-types";

const { Text } = Typography;

export function IvrNewTemplateNormal({
  template,
  templateIndex,
  setCampaignData,
}) {
  const [showFlow, setShowFlow] = useState(false);
  const [graph, setGraph] = useState(null);
  const containerRef = useRef(null);

  // State for node connections
  const [selectedNode, setSelectedNode] = useState(null);
  const [parentNode, setParentNode] = useState(null);
  const [variableMappingOptions, setVariableMappingOptions] = useState([]);
  const [variableMappingTypes, setVariableMappingTypes] = useState({});

  // Generate node options from template nodes
  const nodeOptions = template.nodes.map((node, index) => ({
    label: node.name || `Node ${index + 1}`,
    value: node.id,
  }));

  const prettifyLabel = (key) => {
    return key
      .replace(/_/g, " ") // Replace underscores with space
      .replace(/([a-z])([A-Z])/g, "$1 $2") // Add space before capital letters
      .replace(/\b\w/g, (char) => char.toUpperCase()); // Capitalize each word
  };

  // Handle fetch variables mapping
  const handleFetchVariables = async () => {
    try {
      const res = await AXIOS.get("v1/campaign/dropdownoptions", {
        params: {
          requesttype: "variablemapping",
        },
      });
      if (res.status === 200) {
        const rawParams = res.data;
        const options = Object.keys(rawParams).map((key) => ({
          label: prettifyLabel(key),
          value: key,
          type: rawParams[key],
        }));
        setVariableMappingOptions(options);
        // For quick lookup by value
        setVariableMappingTypes(rawParams);
      }
    } catch (error) {
      console.log("Error in variable,", error?.message);
    }
  };

  // Handle add ivr variables
  const handleAddIvrVariables = (nodeIndex) => {
    setCampaignData((prevData) => {
      const updatedTemplates = [...prevData.templates];

      const template = updatedTemplates[templateIndex];
      const updatedNodes = [...template.nodes];

      const node = updatedNodes[nodeIndex];
      const newVariable = { columnName: null, sampleValue: "" };
      const updatedVariableMapping = [...node.variableMapping, newVariable];

      const updatedNode = {
        ...node,
        variableMapping: updatedVariableMapping,
        response: node.response + `{${updatedVariableMapping.length}}`,
      };

      updatedNodes[nodeIndex] = updatedNode;
      updatedTemplates[templateIndex] = { ...template, nodes: updatedNodes };

      return { ...prevData, templates: updatedTemplates };
    });
  };

  // Handle add new buttons
  const handleAddIvrButtons = (nodeIndex) => {
    setCampaignData((prevData) => {
      const updatedTemplates = [...prevData.templates];
      const template = updatedTemplates[templateIndex];

      const updatedNodes = [...template.nodes];
      const node = updatedNodes[nodeIndex];

      const existingButtons = node.nodeButtonMapping || [];
      const newButtonId =
        existingButtons.length > 0
          ? Math.max(...existingButtons.map((b) => b.buttonId)) + 1
          : 1;

      const updatedNode = {
        ...node,
        nodeButtonMapping: [
          ...existingButtons,
          { buttonId: newButtonId, buttonReportedNode: null },
        ],
      };

      updatedNodes[nodeIndex] = updatedNode;
      updatedTemplates[templateIndex] = { ...template, nodes: updatedNodes };

      return { ...prevData, templates: updatedTemplates };
    });
  };

  // Handle delete node's variable
  const handleDeleteIvrVariable = ({ nodeIndex, varIndex }) => {
    setCampaignData((prevData) => {
      const updatedTemplates = [...prevData.templates];
      const template = updatedTemplates[templateIndex];

      const updatedNodes = [...template.nodes];
      const node = updatedNodes[nodeIndex];

      const updatedVariableMapping = node.variableMapping.filter(
        (_, idx) => idx !== varIndex
      );

      updatedNodes[nodeIndex] = {
        ...node,
        variableMapping: updatedVariableMapping,
      };

      updatedTemplates[templateIndex] = {
        ...template,
        nodes: updatedNodes,
      };

      return { ...prevData, templates: updatedTemplates };
    });
  };

  // Handle edit node's variables
  const handleEditIvrVariable = ({ nodeIndex, varIndex, field, value }) => {
    setCampaignData((prevData) => {
      const updatedTemplates = [...prevData.templates];
      const template = updatedTemplates[templateIndex];
      const updatedNodes = [...template.nodes];
      const node = updatedNodes[nodeIndex];
      const updatedVariableMapping = [...node.variableMapping];

      updatedVariableMapping[varIndex] = {
        ...updatedVariableMapping[varIndex],
        [field]: value,
      };

      const updatedNode = {
        ...node,
        variableMapping: updatedVariableMapping,
      };

      updatedNodes[nodeIndex] = updatedNode;
      updatedTemplates[templateIndex] = {
        ...template,
        nodes: updatedNodes,
      };

      return {
        ...prevData,
        templates: updatedTemplates,
      };
    });
  };

  // Handle delete node's variable
  const handleDeleteIvrButton = ({ nodeIndex, varIndex }) => {
    setCampaignData((prevData) => {
      const updatedTemplates = [...prevData.templates];
      const template = updatedTemplates[templateIndex];
      const updatedNodes = [...template.nodes];
      const node = updatedNodes[nodeIndex];

      const updatedNode = {
        ...node,
        nodeButtonMapping: node.nodeButtonMapping.filter(
          (_, idx) => idx !== varIndex
        ),
      };

      updatedNodes[nodeIndex] = updatedNode;
      updatedTemplates[templateIndex] = {
        ...template,
        nodes: updatedNodes,
      };

      return {
        ...prevData,
        templates: updatedTemplates,
      };
    });
  };

  // Handle edit node's variables
  const handleEditIvrButton = ({ nodeIndex, varIndex, field, value }) => {
    setCampaignData((prevData) => {
      const updatedTemplates = [...prevData.templates];
      const template = updatedTemplates[templateIndex];
      const updatedNodes = [...template.nodes];
      const node = updatedNodes[nodeIndex];
      const updatedButtonMapping = [...node.nodeButtonMapping];

      updatedButtonMapping[varIndex] = {
        ...updatedButtonMapping[varIndex],
        [field]: value,
      };

      const updatedNode = {
        ...node,
        nodeButtonMapping: updatedButtonMapping,
      };

      updatedNodes[nodeIndex] = updatedNode;
      updatedTemplates[templateIndex] = {
        ...template,
        nodes: updatedNodes,
      };

      return {
        ...prevData,
        templates: updatedTemplates,
      };
    });
  };

  // Handle variables changing
  const handleNodesChange = ({ nodeIndex, field, value }) => {
    setCampaignData((prevData) => {
      const updatedTemplates = [...prevData.templates];
      const template = updatedTemplates[templateIndex];
      const updatedNodes = [...template.nodes];

      updatedNodes[nodeIndex] = {
        ...updatedNodes[nodeIndex],
        [field]: value,
      };

      updatedTemplates[templateIndex] = {
        ...template,
        nodes: updatedNodes,
      };

      return {
        ...prevData,
        templates: updatedTemplates,
      };
    });
  };

  // Handle node connection
  const handleConnectNodes = () => {
    if (!graph || parentNode === undefined || selectedNode === undefined) {
      message.warning("Please select both a parent and a node to connect.");
      return;
    }

    if (parentNode === selectedNode) {
      message.warning("A node cannot be connected to itself.");
      return;
    }

    const parentCell = graph.getCellById(String(parentNode));
    if (!parentCell) {
      message.warning("Parent node does not exist in the graph.");
      return;
    }

    const parentNodeData = template?.nodes.find((n) => n.id === parentNode);
    const existingChildren = parentNodeData?.childIds?.length || 0;
    const parentBBox = parentCell?.getBBox();

    if (existingChildren >= 9) {
      message.warning(
        "Parent node already has the maximum of 9 connected nodes."
      );
      return;
    }

    // **Calculate child node position dynamically**
    let baseX = parentBBox ? parentBBox.x : 350;
    let baseY = parentBBox ? parentBBox.y + 120 : 300;
    let newNodeX = baseX + existingChildren * 180 - (existingChildren - 1) * 90;
    let newNodeY = baseY;

    const ensureNodeExists = (nodeId) => {
      const node = graph.getCellById(String(nodeId));
      if (!node) {
        const nodeData = template?.nodes.find((n) => n.id === nodeId);
        if (nodeData) {
          graph.addNode({
            id: String(nodeId),
            x: newNodeX,
            y: newNodeY,
            width: 120,
            height: 50,
            label: nodeData.name,
            shape: "rect",
            attrs: {
              body: { fill: "#5AD8A6", stroke: "#333", rx: 10, ry: 10 },
              label: { fill: "#fff", fontSize: 14, fontWeight: "bold" },
            },
          });
        }
      }
    };

    ensureNodeExists(parentNode);
    ensureNodeExists(selectedNode);

    graph.addEdge({
      source: { cell: String(parentNode), anchor: "bottom" },
      target: { cell: String(selectedNode), anchor: "top" },
      attrs: {
        line: {
          stroke: "#333",
          strokeWidth: 2,
          targetMarker: { tagName: "circle", r: 4 },
        },
      },
    });

    setCampaignData((prevData) =>
      updateNodeConnection({
        prevData,
        templateIndex,
        parentNode,
        selectedNode,
      })
    );

    setSelectedNode(null);
    setParentNode(null);

    message.success("Node connected to parent successfully!");
  };

  // Update the node connection
  const updateNodeConnection = ({
    prevData,
    templateIndex,
    parentNode,
    selectedNode,
  }) => {
    const updatedTemplates = prevData.templates.map((template, index) => {
      if (index !== templateIndex) return template;

      const updatedNodes = template.nodes.reduce((acc, node) => {
        if (node.id === selectedNode) {
          acc.push({ ...node, parentId: parentNode });
        } else if (node.id === parentNode) {
          const updatedChildIds = [
            ...new Set([...(node.childIds || []), selectedNode]),
          ];
          acc.push({ ...node, childIds: updatedChildIds });
        } else {
          acc.push(node);
        }
        return acc;
      }, []);

      return { ...template, nodes: updatedNodes };
    });

    return { ...prevData, templates: updatedTemplates };
  };

  // Handle Submit Flow
  const handleSubmitFlow = () => {
    if (!graph) {
      message.error("Graph is not initialized.");
      return;
    }
    const edges = graph.getEdges();

    // Ensure at least one connection exists
    if (edges.length === 0) {
      message.warning(
        "Please connect at least one node before submitting the flow."
      );
      return;
    }

    setCampaignData((prevData) => ({
      ...prevData,
      templates: prevData.templates.map((template) => ({
        ...template,
        isIvrFlowApproved: true, // flow as approved
      })),
    }));

    message.success("Flow has been successfully created!");
  };

  // Handle Show Flow
  const handleShowFlow = () => {
    const isValidFlow =
      template.nodes.length > 0 &&
      template.nodes.every((node) => {
        // Ensure node has a name and response filled
        if (!node.name.trim() || !node.response.trim()) return false;

        // Ensure variableMapping has valid entries
        const isVariablesValid =
          node.variableMapping.length === 0 ||
          node.variableMapping.every(
            (variable) => variable.columnName && variable.sampleValue
          );

        // Ensure nodeButtonMapping is properly set
        const isButtonsValid =
          node.nodeButtonMapping.length === 0 ||
          node.nodeButtonMapping.every(
            (button) => button.buttonReportedNode !== null
          );

        return isVariablesValid && isButtonsValid;
      });
    if (!isValidFlow) {
      message.error("Filled all the fields before setting flow!");
    } else {
      setShowFlow((prev) => !prev);
    }
  };

  const getRootText = (scriptValue) => {
    if (!scriptValue) return "Root Node";
    return scriptValue.length > 10
      ? `${scriptValue.slice(0, 10)}...`
      : scriptValue;
  };

  const renderInputByType = ({type, sampleValue, index, nodeVarIndex}) => {
    const commonProps = {
      value: sampleValue,
      onChange: (e) =>
        handleEditIvrVariable({
          nodeIndex: index,
          varIndex: nodeVarIndex,
          field: "sampleValue",
          value: type === "number" ? e.target.value : e.target?.value,
        }),
    };

    if (type === "date") {
      return (
        <DatePicker
          format="DD-MM-YYYY"
          className={Style.date_picker}
          onChange={(date) => {
            handleEditIvrVariable({
              nodeIndex: index,
              varIndex: nodeVarIndex,
              field: "sampleValue",
              value: dayjs(date).format("YYYY-MM-DD"),
            });
          }}
        />
      );
    }

    if (type === "number") {
      return (
        <InputNumber
          placeholder="Enter"
          className={Style.custom_input_number}
          {...commonProps}
        />
      );
    }

    return (
      <Input
        placeholder="Enter"
        className={Style.custom_input}
        {...commonProps}
      />
    );
  };

  // Initialize Graph
  useEffect(() => {
    if (!containerRef.current) return;
    const rootText = getRootText(template.scriptValue);
    const newGraph = new Graph({
      container: containerRef.current,
      grid: true,
      width: 750,
      height: 300,
      panning: {
        enabled: true,
        eventTypes: ["leftMouseDown", "mouseWheel"],
      },
      mousewheel: {
        enabled: true,
        modifiers: ["ctrl", "meta"],
        minScale: 0.5,
        maxScale: 2,
      },
    });

    // Add Root Node
    newGraph.addNode({
      id: "root",
      x: 350,
      y: 250,
      width: 120,
      height: 50,
      label: rootText,
      shape: "rect",
      attrs: {
        body: {
          fill: "#5B8FF9",
          stroke: "#333",
          rx: 10,
          ry: 10,
        },
        label: {
          fill: "#fff",
          fontSize: 14,
          fontWeight: "bold",
        },
      },
    });

    setGraph(newGraph);
  }, [showFlow]);

  useEffect(() => {
    handleFetchVariables();
  }, []);

  return (
    <>
      <Flex justify="space-between" align="center">
        <Text className={Style.text}>Nodes Mapping:</Text>
        <Button
          type="link"
          style={{ fontWeight: 500 }}
          onClick={handleShowFlow}
        >
          {showFlow ? "Edit Nodes" : "Set Flow"}
        </Button>
      </Flex>

      {showFlow ? (
        <Row gutter={[16, 16]}>
          <Col span={15}>
            <div
              ref={containerRef}
              style={{
                maxWidth: "800px", // Limit width to prevent overflow
                height: "300px", // Adjust based on your needs
                backgroundColor: "white",
                border: "1px solid #ddd",
                overflow: "hidden", // Prevent spilling over
                borderRadius: "8px",
                position: "relative", // Keeps it within boundaries
              }}
            />
          </Col>
          <Col span={9}>
            <Flex vertical gap={10}>
              <Text style={{ fontWeight: 500, textAlign: "center" }}>
                Set Node Connection
              </Text>

              <Flex gap={20} vertical>
                {/* Select node */}
                <Flex gap={10} align="center" justify="space-between">
                  <Text className={Style.text}>Select Node:</Text>
                  <Select
                    value={selectedNode}
                    options={nodeOptions}
                    style={{ width: "70%" }}
                    onChange={setSelectedNode}
                    placeholder="Select a node"
                    className={Style.custom_select}
                  />
                </Flex>

                {/* Select parent node */}
                <Flex gap={10} align="center" justify="space-between">
                  <Text className={Style.text}>Select Parent Node:</Text>
                  <Select
                    value={parentNode}
                    options={[{ label: "Root", value: "root" }, ...nodeOptions]}
                    style={{ width: "70%" }}
                    onChange={setParentNode}
                    placeholder="Select a parent node"
                    className={Style.custom_select}
                  />
                </Flex>

                {/* Select child node */}
                {/* <Flex gap={10} align="center" justify='space-between'>
                        <Text className={Style.text}>Select Child Node:</Text>
                        <Select
                            allowClear
                            value={childNode}
                            options={nodeOptions}
                            style={{ width: "70%" }}
                            onChange={setChildNode}
                            placeholder="Select Child Node"
                            className={Style.custom_select}
                        />
                    </Flex> */}

                {/* Buttons */}
                <Flex justify="center" gap={10}>
                  {/* Connect nodes */}
                  <Button
                    className={Style.submit_variable}
                    type="primary"
                    onClick={handleConnectNodes}
                    disabled={selectedNode === null || parentNode === null}
                  >
                    Connect Nodes
                  </Button>

                  {/* Submit approval */}
                  <Button
                    className={Style.submit_variable}
                    type="primary"
                    onClick={handleSubmitFlow}
                  >
                    Set Flow
                  </Button>
                </Flex>
              </Flex>
            </Flex>
          </Col>
        </Row>
      ) : (
        <Flex vertical className={Style.intent_container} gap={10}>
          <Flex vertical gap={15} className={Style.intent_variables_container}>
            {template?.nodes?.map((node, index) => (
              <Flex
                vertical
                gap={10}
                key={`${index}-${node.response}`}
                style={{
                  border: "1px solid lightgray",
                  padding: "15px",
                  borderRadius: "8px",
                }}
              >
                {/* Name & Response */}
                <Flex align="center" gap={15}>
                  <span className={Style.text}>{`{${index + 1}}`}</span>
                  <Flex
                    gap={20}
                    style={{ width: "100%" }}
                    justify="space-between"
                  >
                    {/* Name */}
                    <Flex align="center" gap={20} style={{ width: "100%" }}>
                      <Text className={Style.text}>Name: </Text>
                      <Input
                        placeholder="Enter"
                        value={node.name}
                        className={Style.custom_input}
                        style={{ width: "100%" }}
                        onChange={(e) =>
                          handleNodesChange({
                            nodeIndex: index,
                            field: "name",
                            value: e.target.value,
                          })
                        }
                      />
                    </Flex>

                    {/*Response  */}
                    <Flex align="center" gap={20} style={{ width: "100%" }}>
                      <Text className={Style.text}>Response:</Text>
                      <Input
                        placeholder="Enter"
                        value={node.response}
                        className={Style.custom_input}
                        style={{ width: "100%" }}
                        onChange={(e) =>
                          handleNodesChange({
                            nodeIndex: index,
                            field: "response",
                            value: e.target.value,
                          })
                        }
                      />
                    </Flex>

                    {/* variable mapping */}
                    <Flex justify="end">
                      <Button
                        className={Style.submit_variable}
                        onClick={handleAddIvrVariables.bind(null, index)}
                      >
                        Add Variable
                      </Button>
                    </Flex>

                    {/* Button mapping */}
                    <Flex justify="end">
                      <Button
                        className={Style.submit_variable}
                        onClick={handleAddIvrButtons.bind(null, index)}
                      >
                        Add Button
                      </Button>
                    </Flex>
                  </Flex>
                </Flex>

                {/* Nodes variables */}
                <Flex vertical gap={1} className={Style.variables_container}>
                  {node?.variableMapping?.length ? (
                    <Text className={Style.text}>Variables Mapping:</Text>
                  ) : null}
                  {node?.variableMapping.map((variable, nodeVarIndex) => (
                    <Flex
                      key={`${nodeVarIndex}-${variable.columnName}`}
                      gap={10}
                      align="center"
                      justify="space-between"
                      className={Style.container}
                    >
                      <Flex align="center" gap={15}>
                        <span className={Style.text}>{`{${
                          nodeVarIndex + 1
                        }}`}</span>

                        <Flex align="center" gap={20}>
                          <Text className={Style.text}>Column Name: </Text>
                          <Select
                            showSearch
                            className={Style.custom_select}
                            placeholder="Select"
                            options={[...variableMappingOptions].sort((a, b) =>
                              a.label.localeCompare(b.label)
                            )}
                            value={variable?.columnName}
                            onChange={(value) =>
                              handleEditIvrVariable({
                                nodeIndex: index,
                                varIndex: nodeVarIndex,
                                field: "columnName",
                                value,
                              })
                            }
                          />
                        </Flex>

                        <Flex align="center" gap={20}>
                          <Text className={Style.text}>Sample Value:</Text>
                           {renderInputByType({type: variableMappingTypes[variable?.columnName], sampleValue:variable?.sampleValue, index, nodeVarIndex})}
                        </Flex>

                        <Flex justify="end">
                          <Button
                            className={Style.submit_variable}
                            style={{ background: "#DB3939" }}
                            onClick={handleDeleteIvrVariable.bind(null, {
                              nodeIndex: index,
                              varIndex: nodeVarIndex,
                            })}
                          >
                            Remove
                          </Button>
                        </Flex>
                      </Flex>
                    </Flex>
                  ))}
                </Flex>

                {/* Button Mappings */}
                {node?.nodeButtonMapping?.length ? (
                  <Flex vertical gap={1} className={Style.variables_container}>
                    <Text className={Style.text}>Button Mapping:</Text>
                    {node.nodeButtonMapping.map((variable, nodeVarIndex) => (
                      <Flex
                        key={`${nodeVarIndex}-${variable.buttonReportedNode}`}
                        gap={10}
                        align="center"
                        justify="space-between"
                        className={Style.container}
                      >
                        <Flex align="center" gap={15}>
                          <span className={Style.text}>{`{${
                            nodeVarIndex + 1
                          }}`}</span>

                          <Flex align="center" gap={20}>
                            <Text className={Style.text}>Report Node: </Text>
                            <Select
                              showSearch
                              className={Style.custom_select}
                              placeholder="Select"
                              options={nodeOptions}
                              value={variable?.buttonReportedNode}
                              onChange={(value) =>
                                handleEditIvrButton({
                                  nodeIndex: index,
                                  varIndex: nodeVarIndex,
                                  field: "buttonReportedNode",
                                  value,
                                })
                              }
                            />
                          </Flex>

                          <Flex justify="end">
                            <Button
                              className={Style.submit_variable}
                              style={{ background: "#DB3939" }}
                              onClick={handleDeleteIvrButton.bind(null, {
                                nodeIndex: index,
                                varIndex: nodeVarIndex,
                              })}
                            >
                              Remove
                            </Button>
                          </Flex>
                        </Flex>
                      </Flex>
                    ))}
                  </Flex>
                ) : null}
              </Flex>
            ))}
          </Flex>
        </Flex>
      )}
    </>
  );
}

// Define prop types
IvrNewTemplateNormal.propTypes = {
  template: PropTypes.object,
  templateIndex: PropTypes.number,
  setCampaignData: PropTypes.func,
};

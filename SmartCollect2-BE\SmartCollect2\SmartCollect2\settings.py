"""
Django settings for SmartCollect2 project.

Generated by 'django-admin startproject' using Django 4.2.3.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""
from datetime import timed<PERSON>ta
from pathlib import Path
from dotenv import load_dotenv, dotenv_values
import os
load_dotenv()

config = dotenv_values(".env")

# set config values as environment variables
for key, value in config.items():
    os.environ[key] = value

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.getenv("SECRET_KEY")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.getenv("DEBUG", "False").lower() == "true"

ALLOWED_HOSTS = [
    'http://localhost:5173',  # React frontend origin
    'http://localhost:5174',  # React frontend origin
    'https://smartcollect2.markytics.com',
    'https://smartcollect.markytics.ai',
    'smartcollect.markytics.ai',
    'sarthi.sonataindia.com',
    '127.0.0.1',      # for local development
    'localhost',  
    "*"
]


# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "rest_framework",
    'corsheaders',
    'rest_framework_simplejwt',
    'django_extensions',
    # 'csp',
    "WebApp",
    "MobileApp",
    "campaign",
    "Channels",
    "drf_spectacular"
]

MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    # "csp.middleware.CSPMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "SmartCollect2.encrypterdecrypter.EncryptionMiddleware",
    "SmartCollect2.timestamp_middleware.TimestampMiddleware",
]

ROOT_URLCONF = "SmartCollect2.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

# WSGI_APPLICATION = "SmartCollect2.wsgi.application"
ASGI_APPLICATION = "SmartCollect2.asgi.application"

# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": os.getenv("DATABASE_NAME"),
        "USER": os.getenv("DATABASE_USER"),
        "PASSWORD": os.getenv("DATABASE_PASSWORD"),
        "HOST": os.getenv("DATABASE_HOST"),
        "PORT": os.getenv("DATABASE_PORT"),
        "URL": os.getenv("DATABASE_URL"),
        "TEST": {
            "MIRROR": "default",
        }
    }
}

ASTERISK_DETAILS = {
    'HOST': os.getenv("ASTERISK_HOST"),
    'PORT': os.getenv("ASTERISK_PORT"),
    'USERNAME': os.getenv("ASTERISK_USERNAME"),
    'PASSWORD': os.getenv("ASTERISK_PASSWORD"),
    'ASTERISK_PREFIX': os.getenv("ASTERISK_PREFIX"),
    'ASTERISK_CONTEXT': os.getenv("ASTERISK_CONTEXT"),
    'ASTERISK_EXTENSION': os.getenv("ASTERISK_EXTENSION"),
    'BASE_DIR': os.getenv("ASTERISK_BASE_DIR"),
    'REC_DIR': os.getenv("ASTERISK_REC_DIR"),
    'GCP_BUCKET_NAME': os.getenv("GCP_BUCKET_NAME"),
    'GCP_BUCKET_URL': os.getenv("GCP_BUCKET_URL"),
    'FILE_NAME': os.getenv("ASTERISK_UPLOAD_FILE_NAME"),
}

CORS_ALLOWED_ORIGINS = [
    'http://localhost:5173',  # React frontend origin 
    'https://smartcollect2.markytics.com',
    # "*"
]

CORS_ALLOW_ALL_ORIGINS = False

CSRF_TRUSTED_ORIGINS = ['https://smartcollect2.markytics.com']


# Add these additional CORS settings to be more restrictive
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOWED_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]

CORS_ALLOWED_METHODS = [
    'DELETE',
    'GET',
    'PATCH',
    'POST',
    'PUT',
]

CORS_EXPOSE_HEADERS = [
    'Content-Type',
    'X-CSRFToken',
]


AUTH_USER_MODEL = "WebApp.UserMst"

# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]



REST_FRAMEWORK = {
    # "DEFAULT_PAGINATION_CLASS": 'standardPagination.StandardPagination',
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rest_framework_simplejwt.authentication.JWTAuthentication",
        "rest_framework.authentication.BasicAuthentication",
        "rest_framework.authentication.SessionAuthentication",
    ],
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.IsAuthenticated",
    ],
    # "DEFAULT_RENDERER_CLASSES": [
    #     "rest_framework.renderers.JSONRenderer",
    #     # Remove or conditionally include BrowsableAPIRenderer for production
    #     "rest_framework.renderers.BrowsableAPIRenderer",
    # ],
    "DEFAULT_FILTER_BACKENDS": [
        "django_filters.rest_framework.DjangoFilterBackend",
        # search filter
        "rest_framework.filters.SearchFilter",
        # ordering filter
        "rest_framework.filters.OrderingFilter",
    ],
    'DEFAULT_SCHEMA_CLASS': 'drf_spectacular.openapi.AutoSchema',
    
    'DATETIME_FORMAT': None,  # This will exclude datetime fields from serialization
    'DATE_FORMAT': None,
    'TIME_FORMAT': None,
}

SPECTACULAR_SETTINGS = {
    'SCHEMA_PATH_PREFIX': '/backend/api/v1',
    'SCHEMA_PATH_PREFIX_INSERT': '/backend/api',
    'TITLE': 'SmartCollect API',
    'DESCRIPTION': 'API documentation for SmartCollect',
    'VERSION': '1.0.0',
    'SERVE_INCLUDE_SCHEMA': False,
    # OTHER SETTINGS
}

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(days=1),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=1),
    'ROTATE_REFRESH_TOKENS': False,
    'BLACKLIST_AFTER_ROTATION': True,
    'UPDATE_LAST_LOGIN': False,

    'USER_ID_FIELD': 'UserID',  # Use the primary key of your custom user model
    'USER_ID_CLAIM': 'user_id',  # The key used in the token payload
}

# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = "static/"

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = os.getenv("EMAIL_HOST_USER")
EMAIL_HOST_PASSWORD = os.getenv("EMAIL_HOST_PASSWORD")


os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "/path/to/your-service-account-key.json"


# Content Security Policy (CSP) Configuration
CSP_DEFAULT_SRC = ("'self'",)
CSP_SCRIPT_SRC = (
    "'self'",
    # "'unsafe-inline'",  # Allow inline scripts (for Django admin and DRF)
    # "'unsafe-eval'",    # Allow eval() if needed
    "https://cdn.jsdelivr.net",
    "https://cdnjs.cloudflare.com",
)
CSP_STYLE_SRC = (
    "'self'",
    "'unsafe-inline'",  # Allow inline styles (for Django admin styling)
    "https://fonts.googleapis.com",
    "https://cdn.jsdelivr.net",
    "https://cdnjs.cloudflare.com",
)
CSP_FONT_SRC = (
    "'self'",
    "https://fonts.gstatic.com",
    "data:",
)
CSP_IMG_SRC = (
    "'self'",
    "data:",
    "https:",
    "blob:",
)
CSP_CONNECT_SRC = (
    "'self'",
    "https://smartcollect2.markytics.com",
    "http://localhost:5173",  # For development
    "wss:",
    "ws:",
)
CSP_FRAME_SRC = ("'self'",)
CSP_FRAME_ANCESTORS = ("'none'",)
CSP_OBJECT_SRC = ("'none'",)
CSP_BASE_URI = ("'self'",)
CSP_FORM_ACTION = ("'self'",)

# Additional security headers
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
SECURE_REFERRER_POLICY = "strict-origin-when-cross-origin"
X_FRAME_OPTIONS = "DENY"

SECURE_FRAME_DENY = True

# Remove server information disclosure
DISALLOWED_USER_AGENTS = []
SECURE_REDIRECT_EXEMPT = []

# Environment-specific HTTPS settings
if not DEBUG:
    # Production settings - Full HTTPS enforcement
    # SECURE_SSL_REDIRECT = True  # Redirect HTTP to HTTPS
    SESSION_COOKIE_SECURE = True  # Use secure cookies
    CSRF_COOKIE_SECURE = True  # Use secure CSRF cookies
    SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')  # For reverse proxy
    # HTTP Strict Transport Security (HSTS) Configuration
    SECURE_HSTS_SECONDS = 31536000  # 1 year (31,536,000 seconds)
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True  # Apply to all subdomains
    SECURE_HSTS_PRELOAD = True  # Allow inclusion in browser preload lists
else:
    # Development settings - HSTS disabled for local development
    SECURE_HSTS_SECONDS = 0
    SECURE_SSL_REDIRECT = False
    SESSION_COOKIE_SECURE = False
    CSRF_COOKIE_SECURE = False

# Additional cookie security (applies to both environments)
SESSION_COOKIE_HTTPONLY = True
CSRF_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Lax'
CSRF_COOKIE_SAMESITE = 'Lax'
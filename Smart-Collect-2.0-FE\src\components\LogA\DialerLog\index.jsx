import { Flex, message, Table, Typography } from "antd";
import React, { useEffect, useState } from "react";
import { AXIOS } from "../../../apis/ho-Instance";
import Style from "./_style.module.scss";
import ApplicationLoader from "../../ApplicationLoader";
import {
  ALL_CACHE_NAMES,
  formatTime,
  handleDownloadTable,
} from "../../../constant";
import PLAY_ICON from "../../../assets/Images/lets-icons_video-light.svg";
import PAUSE_ICON from "../../../assets/Images/pause.svg";
import DOWNLOAD_IMG from "../../../assets/Images/download.svg";
import {
  getCacheKey,
  getFromCache,
  storeToCache,
} from "../../../utils/cacheHelper";

const { Text } = Typography;
const CACHE_NAME = ALL_CACHE_NAMES.ACTIVITY_CACHE;

export function DialerLogTable() {
  const [data, setData] = useState([]);
  const [durations, setDurations] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [playingIndex, setPlayingIndex] = useState(null);

  const handleFetchDialerLogs = async () => {
    setIsLoading(true);
    const cacheKey = getCacheKey({ endpoint: "dialer-logs/" });
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setData(cachedData);
      setIsLoading(false);
    }
    try {
      const res = await AXIOS.get("dialerlogs/");
      if (res.status === 200) {
        setData(res.data.data);
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: res.data.data?.length ? res.data.data : [],
        });
      }
    } catch (error) {
      console.log("Error in dialer logs", error?.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFetchAudioDuration = async (url) => {
    return new Promise((resolve) => {
      const audio = new Audio(url);
      audio.addEventListener("loadedmetadata", () => {
        const duration = Math.floor(audio.duration); // Duration in seconds
        const minutes = Math.floor(duration / 60);
        const seconds = duration % 60;
        resolve(`${minutes}:${seconds.toString().padStart(2, "0")}`);
      });
      audio.addEventListener("error", () => resolve("--")); // Handle errors
    });
  };

  const handlePlayRecording = (index) => {
    const audio = document.getElementById(`audio-${index}`);

    if (!audio) {
      message.error("There is no recording for this customer");
      return;
    }
    if (playingIndex === index) {
      audio.pause();
      setPlayingIndex(null);
    } else {
      if (playingIndex !== null) {
        document.getElementById(`audio-${playingIndex}`).pause();
      }
      audio.play();
      setPlayingIndex(index);
    }
  };

  useEffect(() => {
    handleFetchDialerLogs();
  }, []);

  useEffect(() => {
    const fetchDurations = async () => {
      const durationData = {};
      for (const item of data) {
        if (item.RecordingURL) {
          durationData[item.Call_ID] = await handleFetchAudioDuration(
            item.RecordingURL
          );
        } else {
          durationData[item.Call_ID] = "--";
        }
      }
      setDurations(durationData);
    };

    fetchDurations();
  }, [data]);

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      sorter: (a, b) => a.key - b.key,
      render: (_, data) => <Text>{data?.key}.</Text>,
    },
    {
      title: "Call Id",
      dataIndex: "Call_ID",
      sorter: (a, b) => a.Call_ID - b.Call_ID,
      render: (value) => <Text className={Style.text}> {value} </Text>,
    },
    {
      title: "Time",
      dataIndex: "DateTime",
      sorter: (a, b) => new Date(a.DateTime) - new Date(b.DateTime),
      render: (value) => (
        <Text className={Style.blueText}> {formatTime(value) || "--"} </Text>
      ),
    },
    {
      title: "Customer Name",
      dataIndex: "CustomerName",
      sorter: (a, b) => a.CustomerName - b.CustomerName,
    },
    // {
    //     title:'Responsive Category',
    //     dataIndex:'category',
    //     render: (value) => (
    //       <Text className={Style.blueText}> {value || "--"} </Text>
    //   )
    // },
    {
      title: "Duration",
      dataIndex: "RecordingURL",
      sorter: (a, b) => {
        const getSeconds = (duration) => {
          if (!duration) return 0;
          const [hh, mm, ss] = duration.split(":").map(Number);
          return (hh || 0) * 3600 + (mm || 0) * 60 + (ss || 0);
        };

        const aDuration = getSeconds(durations[a.Call_ID]);
        const bDuration = getSeconds(durations[b.Call_ID]);
        return aDuration - bDuration;
      },
      render: (_, record) => (
        <Text className={Style.blueText}>
          {durations[record.Call_ID] || "Loading..."}
        </Text>
      ),
    },
    {
      title: "Recording",
      dataIndex: "RecordingURL",
      render: (url, _, index) => (
        <Flex justify="center">
          <div className="icon_img">
            <input
              type="image"
              alt="play"
              src={playingIndex === index ? PAUSE_ICON : PLAY_ICON}
              onClick={() => handlePlayRecording(index)}
              style={{ cursor: "pointer" }}
            />
            <audio
              id={`audio-${index}`}
              src={url}
              onEnded={() => setPlayingIndex(null)}
            >
              <track kind="captions" label="No captions" />
            </audio>
          </div>
        </Flex>
      ),
    },
    {
      title: "Agent Number",
      dataIndex: "InitiatingNumber",
      sorter: (a, b) => a.InitiatingNumber - b.InitiatingNumber,
      render: (value) => <Text>{value ?? "--"} </Text>,
    },
  ];

  const dataSource = Array.isArray(data)
    ? data.map((d, i) => ({
        key: i + 1,
        ...d,
      }))
    : [];

  // Handle downloading
  const handleDownload = async () => {
    if (data.length === 0) {
      return message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: "Activity Dailerlog",
        worksheetName: "activity-Dailerlog",
        tableData: data,
      });
    }
  };
  return (
    <Flex vertical gap={20}>
      {isLoading ? (
        <ApplicationLoader />
      ) : (
        <>
          <Flex justify="end">
            <button className={Style.download_button} onClick={handleDownload}>
              <img src={DOWNLOAD_IMG} alt="download-button" />
            </button>
          </Flex>
          <Table
            bordered
            virtual
            className={Style.CustomTable}
            columns={columns}
            dataSource={dataSource}
            scroll={{
              x: 700,
              y: 460,
            }}
            pagination={{
              showSizeChanger: false,
            }}
          />
        </>
      )}
    </Flex>
  );
}

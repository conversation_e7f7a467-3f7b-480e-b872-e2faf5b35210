{"name": "smart-collect", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@antv/x6": "^2.18.1", "@emailjs/browser": "^4.4.1", "@reduxjs/toolkit": "^2.5.1", "antd": "^5.23.1", "apexcharts": "^4.5.0", "axios": "^1.7.9", "classnames": "^2.5.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "html2pdf.js": "^0.9.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "prop-types": "^15.8.1", "react": "^18.3.1", "react-apexcharts": "^1.7.0", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router": "7.1.3", "react-slick": "^0.30.3", "sass": "^1.83.4", "slick-carousel": "^1.8.1", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "vite": "^6.0.5", "vite-plugin-svgr": "^4.3.0"}}
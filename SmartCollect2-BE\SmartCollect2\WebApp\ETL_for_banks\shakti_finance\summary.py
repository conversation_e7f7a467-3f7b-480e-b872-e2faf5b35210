import pandas as pd
import numpy as np
from ..utils import clean_mobile_number
from datetime import datetime
from ..queries import insert_data_in_raw_table
import threading
from ..sp import execute_post_processing_procedures

def run_post_processing_in_background(bank_id):
    """
    Run post-processing procedures in a background thread
    """
    try:
        print(f"Starting background post-processing for bank_id: {bank_id}")
        execute_post_processing_procedures(bank_id)
        print(f"Background post-processing initiated for bank_id: {bank_id}")
    except Exception as e:
        print(f"Error starting background post-processing for bank_id {bank_id}: {str(e)}")


def format_date_field(date_value):
    """
    Convert various date formats to YYYY-MM-DD string format
    """
    if pd.isna(date_value) or date_value is None:
        return None
    
    try:
        # If it's already a string, try to parse it
        if isinstance(date_value, str):
            date_value = date_value.strip()
            if not date_value:
                return None
            # Try to parse the string date
            parsed_date = pd.to_datetime(date_value)
        # If it's already a datetime object
        elif isinstance(date_value, (pd.Timestamp, datetime)):
            parsed_date = pd.to_datetime(date_value)
        else:
            # Try to convert whatever it is to datetime
            parsed_date = pd.to_datetime(date_value)
        
        # Return in YYYY-MM-DD format
        return parsed_date.strftime('%Y-%m-%d')
        
    except (ValueError, TypeError) as e:
        print(f"Warning: Could not parse date value '{date_value}': {e}")
        return None


def process_shakti_finance_raw_data(df1):
    db_operation_success = False
    db_error_message = None
    
    # Create a list to store all processed records
    rawfile_records = []
    
    # Counters for guarantor statistics
    records_with_guarantors = 0
    records_without_guarantors = 0
    
    # Process each row in the input dataframe
    for index, row in df1.iterrows():
        # Create base record with standard mappings
        base_record = {
            'customerid': row['CustomerID'],
            'disbursementid': str(row['DisbursementID']).strip(),
            'branchname': str(row['BranchName']).strip(),
            'loantype': str(row['LoanType']).strip(),
            'customername': str(row['CustomerName']).strip(),
            'mobileno': clean_mobile_number(row['MobileNo.']),
            'overdueamount': row['OverdueAmount'],
            'emiamount': row['EMIAmount'],
            'nextemidate': format_date_field(row['NextEMIDate yyyy-mm-dd']),  # Format date properly
            'extracolumn1': str(row['Live / Expired']).strip(),
            'numberofdayspastdue': pd.to_numeric(row['NumberOfDaysPastDue'], errors='coerce') if pd.notna(row['NumberOfDaysPastDue']) else 0,
            'applicanttype': row['ApplicantType'],
            'bankmstid': 28,
            'inserted_date': datetime.now().strftime('%Y-%m-%d'),  # Format as date string
            'language': row['Language'],
            'originaldisbursementid': str(row['DisbursementID']).strip(),
            'guarantor': '',  # Initialize empty
            'guarantor_mobile': '',  # Initialize empty
            'secondary_user_type': None  # Initialize as None
        }
        
        # Process branch code with dynamic zero padding
        branch_code = str(row['BranchCode']).strip()
        # Find the maximum number of digits in all branch codes first
        all_branch_codes = df1['BranchCode'].astype(str).str.strip()
        max_digits = all_branch_codes.str.len().max()
        
        # Apply zero padding based on max digits
        if max_digits <= 2:
            base_record['branchcode'] = branch_code.zfill(3)
        elif max_digits == 3:
            base_record['branchcode'] = branch_code.zfill(3)
        else:
            base_record['branchcode'] = branch_code.zfill(max_digits)
        
        # Process guarantors
        guarantors = []
        
        # Check Guarantor 1
        if (pd.notna(row['Guarantor 1 Name']) and str(row['Guarantor 1 Name']).strip() and 
            pd.notna(row['Guarantor 1 Mobile Number']) and 
            row['Guarantor 1 Mobile Number'] is not None and 
            len(str(row['Guarantor 1 Mobile Number'])) >= 10):
            guarantors.append({
                'name': str(row['Guarantor 1 Name']).strip(),
                'mobile': clean_mobile_number(row['Guarantor 1 Mobile Number'])
            })
        
        # Check Guarantor 2
        if (pd.notna(row['Guarantor 2 Name']) and str(row['Guarantor 2 Name']).strip() and 
            pd.notna(row['Guarantor 2 Mobile Number']) and 
            row['Guarantor 2 Mobile Number'] is not None and 
            len(str(row['Guarantor 2 Mobile Number'])) >= 10):
            guarantors.append({
                'name': str(row['Guarantor 2 Name']).strip(),
                'mobile': clean_mobile_number(row['Guarantor 2 Mobile Number'])
            })
        
        # Create records based on guarantors
        if not guarantors:
            # If no valid guarantors, create one record without guarantor info
            rawfile_records.append(base_record.copy())
            records_without_guarantors += 1
        else:
            # Create separate records for each guarantor
            for guarantor in guarantors:
                record = base_record.copy()
                record['guarantor'] = guarantor['name']
                record['guarantor_mobile'] = guarantor['mobile']
                record['secondary_user_type'] = 'guarantor'  # Set to 'guarantor' for records with guarantors
                rawfile_records.append(record)
            records_with_guarantors += len(guarantors)
    
    # Convert the list of records to DataFrame
    df = pd.DataFrame(rawfile_records)
    
    # Ensure numberofdayspastdue is int64
    df['numberofdayspastdue'] = df['numberofdayspastdue'].astype('int64')
    
    # Format all date columns in the final DataFrame to ensure consistency
    date_columns = ['nextemidate', 'inserted_date']
    for col in date_columns:
        if col in df.columns:
            df[col] = df[col].apply(format_date_field)
    
    # Optional: Save to CSV for debugging
    # df.to_csv("shaktifinance.csv", index=False)
    
    # Database operation
    try:
        result = insert_data_in_raw_table(df, bank_id=28)
        db_operation_success = result['success']
        if not result['success']:
            db_error_message = result['message']
    except Exception as e:
        db_operation_success = False
        db_error_message = str(e)
        print(f"Database update failed for shakti finance data: {e}")
        result = {'inserted_count': 0, 'duplicate_count': 0, 'message': str(e)}
    
    # # ========== START BACKGROUND POST-PROCESSING ==========
    # if db_operation_success:
    #     # Start post-processing in background thread
    #     bank_id=28
    #     background_thread = threading.Thread(
    #         target=run_post_processing_in_background,
    #         args=(bank_id,),
    #         daemon=True
    #     )
    #     background_thread.start()
    #     print(f"Post-processing started in background thread for bank_id: {bank_id}")
    # else:
    #     print(f"Skipping post-processing due to database operation failure: {db_error_message}")
    return {
        "Total records processed": len(df),
        "New records inserted": result.get('inserted_count', 0),
        "Duplicate records found": result.get('duplicate_count', 0),
        "Records with guarantors": records_with_guarantors,
        "Records without guarantors": records_without_guarantors,
        "db_operation_success": db_operation_success,
        "db_error_message": db_error_message,
        "message": result.get('message', ''),
    }
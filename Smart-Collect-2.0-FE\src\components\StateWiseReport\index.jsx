import {
  But<PERSON>,
  Col,
  Flex,
  Input,
  message,
  Row,
  Select,
  Typography,
} from "antd";
import React, { useEffect, useState } from "react";
import AppHeader from "../AppHeader";
import Style from "./_style.module.scss";
import { AXIOS } from "../../apis/ho-Instance";
import NumberTable from "./NumberTable";
import SimilarTable from "./Similartable";
import ApplicationLoader from "../ApplicationLoader";

const { Text } = Typography;
const { Search } = Input;

export default function StateWiseReport() {
  const [isLoading, setIsLoading] = useState(false);
  const [similarLoading, setSimilarLoading] = useState(false);
  const [number, setNumber] = useState("");
  const [relatedPhoneData, setRelatedPhoneData] = useState([]);
  const [similarPhoneData, setSimilarPhoneData] = useState([]);

  const isValidIndianPhoneNumber = (num) => {
    const indianPhoneRegex = /^[6-9]\d{9}$/;
    return indianPhoneRegex.test(num);
  };

  // Handle promise deletion
  const handleSearch = async (value) => {
    if (!isValidIndianPhoneNumber(value)) {
      message.error("Please enter a valid Indian phone number.");
      return;
    }
    setIsLoading(true);
    setNumber(value);
    try {
      const res = await AXIOS.post(
        "findnumber/",
        { phonenumber: String(value) },
        { validateStatus: () => true }
      );
      if (res.status === 200) {
        const normalizedData = Array.isArray(res.data) ? res.data : [res.data];
        setRelatedPhoneData(normalizedData);
      } else {
        message.warning(
          res.data?.message || "No data found for the given number"
        );
        setRelatedPhoneData([]);
      }
    } catch (error) {
      console.error("Error in Promise response", error);
      message.info(`No, related data found for ${number}`);
      setRelatedPhoneData([]);
    } finally {
      setIsLoading(false);
      setNumber("");
      setSimilarPhoneData([]);
    }
  };

  // Handle search by similar phone number
  const handleSearchBySimilar = async () => {
    setSimilarLoading(true);
    try {
      const res = await AXIOS.post(
        "findnumber/",
        {
          address: relatedPhoneData[0].address || "",
          name: relatedPhoneData[0].name || "",
          state: relatedPhoneData[0]?.Folder_Name || "",
        },
        { validateStatus: () => true }
      );
      if (res.status === 200) {
        const normalizedData = Array.isArray(res.data) ? res.data : [res.data];
        setSimilarPhoneData(normalizedData);
      } else {
        message.warning(res.data?.message || "No similar phone data found");
        setSimilarPhoneData([]);
      }
    } catch (error) {
      console.log("Error in fetching similar phone data", error);
      message.error("Failed to fetch similar phone data");
    } finally {
      setSimilarLoading(false);
    }
  };

  return (
    <Flex vertical gap={10}>
      <AppHeader title={"State Wise Report"} />
      <Row
        gutter={[20, 30]}
        className={Style.add_bank}
        justify={"space-between"}
      >
        {/* search */}
        <Col span={24}>
          <Flex vertical className={Style.label_container}>
            <Text className={Style.label_text}>Phone Number</Text>
            <Search
              className={Style.custom_number}
              placeholder="Search by phone number"
              onSearch={(value) => handleSearch(value)}
              loading={isLoading}
            />
          </Flex>
        </Col>

        {/* Number Table */}
        {Array.isArray(relatedPhoneData) && relatedPhoneData.length > 0 && (
          <Col span={24}>
            <NumberTable
              data={relatedPhoneData}
              handleSearchBySimilar={handleSearchBySimilar}
            />
          </Col>
        )}

        {/* Similar Phone Numbers Table */}
        {similarLoading ? (
          <ApplicationLoader />
        ) : (
          relatedPhoneData.length > 0 &&
          Array.isArray(similarPhoneData) &&
          similarPhoneData.length > 0 && (
            <Col span={24}>
              <SimilarTable data={similarPhoneData} />
            </Col>
          )
        )}
      </Row>
    </Flex>
  );
}

import pandas as pd
import numpy as np
from ..utils import clean_mobile_number
from datetime import datetime
from ..queries import insert_data_in_raw_table,get_data_from_account_summary
import threading
from ..sp import execute_post_processing_procedures

def run_post_processing_in_background(bank_id):
    """
    Run post-processing procedures in a background thread
    """
    try:
        print(f"Starting background post-processing for bank_id: {bank_id}")
        execute_post_processing_procedures(bank_id)
        print(f"Background post-processing initiated for bank_id: {bank_id}")
    except Exception as e:
        print(f"Error starting background post-processing for bank_id {bank_id}: {str(e)}")

def process_daily_data(df1):
    df = pd.DataFrame()
    df1.columns = df1.columns.str.strip().str.replace(r'\s+', ' ', regex=True)
    df['Branch_Id'] = df1['Branch_Id']
    df['Branch_Name'] = df1['Branch_Name']
    df['Loan_Acc_No'] = df1['Loan_Acc_No']
    df['Loan_Type'] = df1['Loan_Type']
    df['Collected_Amt'] = df1['Collected_Amt']
    df['Collection _Date'] = df1['Collection _Date'].apply(lambda x: str(datetime.strptime(str(x).zfill(8), "%d%m%Y").date()))
    df['Current_Balance'] = df1['Current_Balance']
    df['disbursementid'] = df1['Loan_Acc_No'] + '-' +df1['Loan_GL_No'] + '-' + df1['Branch_Id']
    df['customerid'] = df1['Customer_Id'] + '-' + df1['Branch_Id']

    return df

def prcoess_base_data(df1):
    df = pd.DataFrame()
    df1.columns = df1.columns.str.strip().str.replace(r'\s+', ' ', regex=True)
    df['disbursementid'] = df1['Acc_no'] + '-' + df1['GL_Code'] + '-' + df1['branch_id']
    df['customerid'] = df1['Client  id'] + '-' + df1['branch_id']
    df['Customer Name'] = df1['name'].str.upper()
    df['mobile'] = df1['mobile'].apply(clean_mobile_number)
    df['loantype'] = df1['LoanType'].str.upper()
    df['overdueamount'] = df1['OverdueAmountWIthPenal-Charges-interrecival-overdueamt']
    df['NextEMIAmount'] = df1['NextEMIAmount'].replace(0, '')
    df['branchcode'] = df1['branch_id']
    df['Branch Name'] = df1['Branch Name'].str.upper()
    # df['NextEMIDate'] = df1['NextEMIDate'].fillna('').apply(lambda x: str(datetime.strptime(str(x).zfill(8), "%d%m%Y").date()))
    df['NextEMIDate'] = df1['NextEMIDate']

    return df

def process_swami_samarth_base_raw_data(base_df):
    """
    Process base data for Swami Samarth bank and prepare for raw table insertion
    """
    try:
        if base_df.empty:
            return {
                "Total records processed": 0,
                "New records inserted": 0,
                "Duplicate records found": 0,
                "db_operation_success": False,
                "db_error_message": "No base data available for processing",
                "message": "No base data available for processing"
            }
        
        df = pd.DataFrame()
        
        # Check if data is already processed (has 'customerid' column) or raw
        if 'customerid' in base_df.columns:
            # Data is already processed
            df['customerid'] = base_df['customerid']
            df['disbursementid'] = base_df['disbursementid']
            df['customername'] = base_df['Customer Name']
            df['emiamount'] = base_df['NextEMIAmount']
            df['mobileno'] = base_df['mobile']
            df['nextemidate'] = base_df['NextEMIDate']
            df['overdueamount'] = base_df['overdueamount']
            df['loantype'] = base_df['loantype']  # Fixed: Use 'loantype' instead of 'LoanType'
            df['branchcode'] = base_df['branchcode']
            df['branchname'] = base_df['Branch Name']

        else:
            # Data is raw, need to process it first
            print("Processing raw base data for Swami Samarth")
            
            # Clean column names
            base_df.columns = base_df.columns.str.strip().str.replace(r'\s+', ' ', regex=True)
            
            # Debug: Print available columns after cleaning
            print(f"Available columns after cleaning: {list(base_df.columns)}")
            
            # Create a mapping for case-insensitive column access
            column_mapping = {col.lower(): col for col in base_df.columns}
            
            # Helper function to get column safely
            def get_column_name(search_name):
                # Try exact match first
                if search_name in base_df.columns:
                    return search_name
                # Try case-insensitive match
                if search_name.lower() in column_mapping:
                    return column_mapping[search_name.lower()]
                # Return None if not found
                return None
            
            # Get the correct column names
            client_id_col = get_column_name('Client  id') or get_column_name('client  id') or get_column_name('Client id')
            gl_code_col = get_column_name('GL_Code') or get_column_name('gl_code')
            acc_no_col = get_column_name('Acc_no') or get_column_name('acc_no')
            branch_id_col = get_column_name('branch_id')
            name_col = get_column_name('name')
            mobile_col = get_column_name('mobile')
            overdue_col = get_column_name('OverdueAmountWIthPenal-Charges-interrecival-overdueamt') or get_column_name('overdueamountwithpenal-charges-interrecival-overdueamt')
            next_emi_amount_col = get_column_name('NextEMIAmount') or get_column_name('nextemiamount')
            next_emi_date_col = get_column_name('NextEMIDate') or get_column_name('nextemidate')
            loan_type_col = get_column_name('LoanType') or get_column_name('loantype')  # Added loan type column mapping
            branch_code_col = get_column_name('branchcode') or get_column_name('branch_code')
            branch_name_col = get_column_name('Branch Name') or get_column_name('branch_name') or get_column_name('branchname')

            
            print(f"Column mapping results:")
            print(f"  Client ID column: {client_id_col}")
            print(f"  GL Code column: {gl_code_col}")
            print(f"  Account No column: {acc_no_col}")
            print(f"  Branch ID column: {branch_id_col}")
            print(f"  Name column: {name_col}")
            print(f"  Mobile column: {mobile_col}")
            print(f"  Overdue column: {overdue_col}")
            print(f"  Next EMI Amount column: {next_emi_amount_col}")
            print(f"  Next EMI Date column: {next_emi_date_col}")
            print(f"  Loan Type column: {loan_type_col}")  # Added loan type to debug print
            print(f"  Branch Code column: {branch_code_col}")
            print(f"  Branch Name column: {branch_name_col}")
            
            # Check if all required columns are found
            required_columns = {
                'Client ID': client_id_col,
                'GL Code': gl_code_col, 
                'Account No': acc_no_col,
                'Branch ID': branch_id_col,
                'Name': name_col,
                'Mobile': mobile_col,
                'Overdue Amount': overdue_col
            }
            
            missing_columns = [k for k, v in required_columns.items() if v is None]
            
            if missing_columns:
                error_msg = f"Missing required columns: {missing_columns}. Available columns: {list(base_df.columns)}"
                print(error_msg)
                return {
                    "Total records processed": 0,
                    "New records inserted": 0,
                    "Duplicate records found": 0,
                    "db_operation_success": False,
                    "db_error_message": error_msg,
                    "message": error_msg
                }
            
            # Create processed columns using the correct column names
            df['disbursementid'] = base_df[acc_no_col].astype(str) + '-' + base_df[gl_code_col].astype(str) + '-' + base_df[branch_id_col].astype(str)
            df['customerid'] = base_df[client_id_col].astype(str) + '-' + base_df[branch_id_col].astype(str)
            df['customername'] = base_df[name_col].str.upper()
            df['mobileno'] = base_df[mobile_col].apply(clean_mobile_number)
            df['overdueamount'] = base_df[overdue_col]
            
            # Handle loan type column
            if loan_type_col:
                df['loantype'] = base_df[loan_type_col].str.upper()
                print(f"Added loantype from column: {loan_type_col}")
            else:
                df['loantype'] = None
                print("Warning: Loan type column not found, setting loantype to None")
            
            # Handle optional columns
            if next_emi_amount_col:
                df['emiamount'] = base_df[next_emi_amount_col].replace(0, '')
            else:
                df['emiamount'] = None
                
            if next_emi_date_col:
                # Handle NextEMIDate with proper formatting
                next_emi_values = base_df[next_emi_date_col]
                print(f"NextEMIDate sample values: {next_emi_values.head()}")
                print(f"NextEMIDate data types: {next_emi_values.dtypes}")
                
                # Convert to proper date format
                try:
                    # If it's already datetime, convert to date string
                    if pd.api.types.is_datetime64_any_dtype(next_emi_values):
                        df['nextemidate'] = pd.to_datetime(next_emi_values).dt.strftime('%Y-%m-%d')
                        print(f"Converted datetime NextEMIDate to: {df['nextemidate'].head()}")
                    else:
                        # If it's NaN or other format, handle appropriately
                        df['nextemidate'] = next_emi_values.apply(lambda x: 
                            pd.to_datetime(x).strftime('%Y-%m-%d') if pd.notna(x) and x != '' else None
                        )
                        print(f"Converted other NextEMIDate to: {df['nextemidate'].head()}")
                except Exception as e:
                    print(f"Error converting NextEMIDate: {e}, setting to None")
                    df['nextemidate'] = None
            else:
                df['nextemidate'] = None

            if branch_code_col:
                df['branchcode'] = base_df[branch_code_col]
            else:
                df['branchcode'] = base_df[branch_id_col]  # Use branch_id as fallback

            if branch_name_col:
                df['branchname'] = base_df[branch_name_col].str.upper()
            else:
                df['branchname'] = None
                print("Warning: Branch name column not found, setting branchname to None")
        
        # Common fields - FIX THE DATE FORMAT ISSUE HERE
        df['bankmstid'] = 369
        
        # Fix the date format issue - ensure it's a proper date string
        current_date = datetime.now().date()
        df['inserted_date'] = current_date.strftime('%Y-%m-%d')  # Explicitly format as YYYY-MM-DD string
        
        print(f"DEBUG: Current date object: {current_date}")
        print(f"DEBUG: Formatted date string: {current_date.strftime('%Y-%m-%d')}")
        print(f"DEBUG: inserted_date column sample: {df['inserted_date'].head()}")
        print(f"DEBUG: inserted_date data type: {df['inserted_date'].dtype}")
        
        # Debug: Print all columns and their data types before database insertion
        print("DEBUG: Final DataFrame column info before database insertion:")
        for col in df.columns:
            print(f"  {col}: dtype={df[col].dtype}, sample_value={df[col].iloc[0] if len(df) > 0 else 'N/A'}")
        
        # Check for any datetime columns that might be causing issues
        datetime_columns = []
        date_columns = []
        for col in df.columns:
            col_dtype = str(df[col].dtype)
            if 'datetime' in col_dtype or df[col].dtype == 'datetime64[ns]':
                datetime_columns.append(col)
                print(f"WARNING: Found datetime column '{col}' with dtype {df[col].dtype}")
                print(f"  Sample values: {df[col].head()}")
            elif col in ['nextemidate', 'inserted_date'] or 'date' in col.lower():
                date_columns.append(col)
                print(f"INFO: Found date column '{col}' with dtype {df[col].dtype}")
                print(f"  Sample values: {df[col].head()}")
        
        # Convert datetime columns to proper date format
        if datetime_columns:
            print(f"Converting datetime columns to proper format: {datetime_columns}")
            for col in datetime_columns:
                try:
                    df[col] = pd.to_datetime(df[col]).dt.strftime('%Y-%m-%d')
                    print(f"  Successfully converted {col} to: {df[col].head()}")
                except Exception as e:
                    print(f"  Error converting {col}: {e}")
                    df[col] = None
        
        # Ensure all date columns are in proper format
        for col in date_columns:
            try:
                # Check if any values look like datetime strings
                sample_values = df[col].dropna().head()
                if len(sample_values) > 0:
                    first_value = str(sample_values.iloc[0])
                    if ' 00:00:00' in first_value or len(first_value.split()) > 1:
                        print(f"WARNING: Date column '{col}' contains datetime format: {first_value}")
                        # Convert datetime strings to date strings
                        df[col] = df[col].apply(lambda x: 
                            pd.to_datetime(x).strftime('%Y-%m-%d') if pd.notna(x) and str(x) != 'nan' and str(x) != '' else None
                        )
                        print(f"  Converted {col} to proper date format: {df[col].head()}")
            except Exception as e:
                print(f"  Error processing date column {col}: {e}")
                # If there's an error, set problematic values to None
                df[col] = None
        
        print(f"DEBUG: Final DataFrame shape: {df.shape}")
        print(f"DEBUG: Final DataFrame columns: {list(df.columns)}")
        
        # Database operation
        db_operation_success = True
        db_error_message = ""
        
        try:
            result = insert_data_in_raw_table(df, bank_id=369)
            db_operation_success = result['success']
            if not result['success']:
                db_error_message = result['message']
        except Exception as e:
            db_operation_success = False
            db_error_message = str(e)
            print(f"Database update failed for Swami Samarth base data: {e}")
        
        return {
            "Total records processed": len(df),
            "New records inserted": result.get('inserted_count', 0) if db_operation_success else 0,
            "Duplicate records found": result.get('duplicate_count', 0) if db_operation_success else 0,
            "db_operation_success": db_operation_success,
            "db_error_message": db_error_message,
            "message": result.get('message', '') if db_operation_success else db_error_message,
        }
        
    except Exception as e:
        return {
            "Total records processed": 0,
            "New records inserted": 0,
            "Duplicate records found": 0,
            "db_operation_success": False,
            "db_error_message": str(e),
            "message": f"Error processing Swami Samarth base data: {str(e)}"
        }

def process_swami_samarth_daily_raw_data(daily_df):
    """
    Process daily data for Swami Samarth bank and prepare for raw table insertion
    """
    try:
        if daily_df.empty:
            return {
                "Total records processed": 0,
                "New records inserted": 0,
                "Duplicate records found": 0,
                "Records filtered out (no customer data)": 0,
                "db_operation_success": False,
                "db_error_message": "No daily data available for processing",
                "message": "No daily data available for processing"
            }
        
        # Get account summary data for matching
        account_summary_result = get_data_from_account_summary(369)
        if not account_summary_result['success']:
            print(f"Warning: Could not retrieve account summary data: {account_summary_result['message']}")
            account_data = {}
        else:
            account_data = account_summary_result['data']
        
        # Store original count for tracking
        original_count = len(daily_df)
        
        df = pd.DataFrame()
        df['customerid'] = daily_df['Customer_Id'] +'-'+ daily_df['Branch_Id']
        df['disbursementid'] = daily_df['Loan_Acc_No'] + '-' + daily_df['Loan_GL_No'] +'-'+ daily_df['Branch_Id']
        df['loantype'] = daily_df['Loan_Type'].str.upper()
        df['branchname'] = daily_df['Branch_Name'].str.upper()
        df['branchcode'] = daily_df['Branch_Id']
        df['overdueamount'] = daily_df['Current_Balance']
        df['collectedamount'] = daily_df['Collected_Amt']
        df['collectiondate'] = daily_df['Collection _Date'].apply(lambda x: str(datetime.strptime(str(x).zfill(8), "%d%m%Y").date()))
        df['originaldisbursementid'] = daily_df['Loan_Acc_No']
        df['bankmstid'] = 369
        df['inserted_date'] = datetime.now().date()
        
        # Add new fields from account summary by matching disbursementid
        df['customername'] = df['disbursementid'].apply(
            lambda x: account_data.get(x, {}).get('CustomerName', None)
        )
        df['mobileno'] = df['disbursementid'].apply(
            lambda x: account_data.get(x, {}).get('MobileNumber', None)
        )
        df['emiamount'] = df['disbursementid'].apply(
            lambda x: account_data.get(x, {}).get('EMIAmount', None)
        )
        df['nextemidate'] = df['disbursementid'].apply(
            lambda x: account_data.get(x, {}).get('NextEMIDate', None)
        )
        
        # Filter out records where no customer data is found
        # Keep only records where at least customername is found
        records_before_filter = len(df)
        df_filtered = df[df['customername'].notna()]
        records_after_filter = len(df_filtered)
        filtered_out_count = records_before_filter - records_after_filter
        
        print(f"Original records: {original_count}")
        print(f"Records before filter: {records_before_filter}")
        print(f"Records after filter: {records_after_filter}")
        print(f"Records filtered out (no customer data): {filtered_out_count}")
        
        # If no records remain after filtering, return early
        if df_filtered.empty:
            return {
                "Total records processed": original_count,
                "New records inserted": 0,
                "Duplicate records found": 0,
                "Records filtered out (no customer data)": filtered_out_count,
                "db_operation_success": False,
                "db_error_message": "No records found with matching customer data",
                "message": "No records found with matching customer data after filtering"
            }
        
        # Database operation with filtered data
        db_operation_success = True
        db_error_message = ""
        # df_filtered.to_csv("swami_samarath.csv", index=False)
        
        try:
            result = insert_data_in_raw_table(df_filtered, bank_id=369)
            db_operation_success = result['success']
            if not result['success']:
                db_error_message = result['message']
        except Exception as e:
            db_operation_success = False
            db_error_message = str(e)
            print(f"Database update failed for Swami Samarth daily data: {e}")
        
        # # ========== START BACKGROUND POST-PROCESSING ==========
        if db_operation_success:
            # Start post-processing in background thread
            bank_id=369
            background_thread = threading.Thread(
                target=run_post_processing_in_background,
                args=(bank_id,),
                daemon=True
            )
            background_thread.start()
            print(f"Post-processing started in background thread for bank_id: {bank_id}")
        else:
            print(f"Skipping post-processing due to database operation failure: {db_error_message}")
            
        return {
            "Total records processed": original_count,
            "New records inserted": result.get('inserted_count', 0) if db_operation_success else 0,
            "Duplicate records found": result.get('duplicate_count', 0) if db_operation_success else 0,
            "Records filtered out (no customer data)": filtered_out_count,
            "Records uploaded to raw table": records_after_filter,
            "db_operation_success": db_operation_success,
            "db_error_message": db_error_message,
            "message": result.get('message', '') if db_operation_success else db_error_message,
        }
        
    except Exception as e:
        return {
            "Total records processed": 0,
            "New records inserted": 0,
            "Duplicate records found": 0,
            "Records filtered out (no customer data)": 0,
            "db_operation_success": False,
            "db_error_message": str(e),
            "message": f"Error processing Swami Samarth daily data: {str(e)}"
        }
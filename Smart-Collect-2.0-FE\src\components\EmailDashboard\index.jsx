import {Flex, message } from 'antd';
import React, { useState } from 'react';
import AppHeader from '../AppHeader';
import { handleDownloadTable } from '../../constant';
import DOWNLOAD_IMG from "../../assets/Images/download.svg";
import {DashboardEngagementAllCards} from '../DashboardEngagementAllCards';
import { DashboardEngagementTable } from '../DashboardEngagementTable';
import Style from "./_style.module.scss";
import ApplicationLoader from '../ApplicationLoader';
import PropTypes from 'prop-types';

export default function EmailDashboard({
    totalEmail=240,
    distinctCustomer=90}) {
    const [data] = useState([]);
    const [isLoading] = useState(false);
    const [isSwitchOn, setIsSwitchOn] = useState(false); 
    const handleDownload = async () => {
          if(data.length === 0 || !data){
            return  message.error("There is no data!");
          }
          else{
              await handleDownloadTable({
                  excelName:"Email", 
                  worksheetName:"Dashboard-Email",
                  tableData:data
              });
          }
    };
  return (
    <Flex gap={10} vertical>
        <AppHeader 
          title={
          <span  className={Style.title}>
             Email : <span style={{color:"#0F2050"}}>{totalEmail}</span><br/>
             Distinct Customers : <span style={{color:"#0F2050"}}>{distinctCustomer}</span>
          </span>
          }
          isDashboard={false}
          isSwitchOn={isSwitchOn}
          setIsSwitchOn={setIsSwitchOn}
          />
            {isLoading
            ?<ApplicationLoader/>
            :<div className={Style.details}>
                {!isSwitchOn 
                    ? <DashboardEngagementAllCards data={data}/>
                    : <Flex vertical gap={20}>
                        <Flex justify='end'>
                            <button className={Style.download_button} onClick={handleDownload}>
                                <img 
                                    src={DOWNLOAD_IMG} 
                                    alt='download-button' 
                                />
                            </button>
                        </Flex>
                        <DashboardEngagementTable data={data}/>
                    </Flex>
                }
            </div>}
    </Flex>
)}

EmailDashboard.propTypes={
    totalEmail: PropTypes.number,
    distinctCustomer: PropTypes.number
}
@import url("../../../../index.css");

$light-blue: #E4F8F9;
$blue: #BECFFF;

.refused_to_pay{
    padding: 0rem 0 1.5rem 1rem;
    min-height: 150px;

    .text{
        color: var(--dark-blue);
        font-weight:400;
        word-wrap: break-word;
        width: 300px;
        font-family: 'Kanit','Courier New', Courier, monospace;
    }
    .refused_select{
        width: 300px;
        :global(.ant-select-selector){
        background-color: $light-blue;
        border:none !important;
      }     
      :global(.ant-select-selection-item){
        font-weight: 400;
        color: var(--dark-blue);
        font-family: 'Kanit','Courier New', Courier, monospace;
      }
    }
    .feedback_button{
      background-color: var(--dark-blue);
      color: white;
      padding: 1rem 2rem;
      border: none;
      font-family: 'Kanit','Courier New', Courier, monospace;

      &:hover{
          background-color: var(--dark-blue) !important;
          color: white !important;
          border: none;
          box-shadow:none;
      }
  }

    .custom_input{
        width: 300px;
        caret-color: #407BFF;
        &:global(.ant-input-outlined){
            border:none;
            box-shadow: none;
            background-color: $light-blue;
            color: var(--dark-blue);
            font-weight: 400;
            font-family: 'Kanit','Courier New', Courier, monospace;
            &:focus-within{
                box-shadow:none !important;
             }
        }
    }
}
:global(.ant-select-dropdown){
    margin-top: 1.2rem;
    background-color: $light-blue;

    :global(.ant-select-item-option) {
      color: var(--dark-blue);
      font-weight: 600;
      
      &:global(.ant-select-item-option-disabled){
       color: rgba(0, 0, 0, 0.56);
    }
    }
    
    :global(.ant-select-item-option-active){
        &:not(.ant-select-item-option-disabled){
        background-color: $blue;
        font-weight: 600;
      }
    }
}

// Responsive view
@media screen and (max-width:768px) {
 .refused_to_pay{
    padding: 0;
    .text{
        font-size: 11px;
        width: 200px;
    }

    .refused_select{   
      :global(.ant-select-selection-item){
       font-size: 10px;
      }
    }

    .custom_input{
        font-size: 10px;
    }
    .feedback_button{
      padding: 0rem 1rem;
      font-size: 11px;
  }
 }

 :global(.ant-select-dropdown){
    :global(.ant-select-item-option){
      font-size: 10px;
    }
  }
}

@media screen and (max-width:568px) {
    .refused_to_pay{
       .text{
           width: 150px;
       }
       .refused_select,
       .custom_input{
        width: 150px;
       }
    }
}
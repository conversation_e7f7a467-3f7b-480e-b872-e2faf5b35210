import PropTypes from "prop-types";
import React from "react";
import ApplicationLoader from "../ApplicationLoader";
import Style from "./_style.module.scss";
import { Flex, Table, Typography } from "antd";
import DOWNLOAD_IMG from "../../assets/Images/download.svg";
import { handleDownloadTable } from "../../constant";

const { Text } = Typography;
export default function AiCalls({ data, isLoading, dates }) {
  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      render: (_, data) => <Text>{data.key}.</Text>,
    },
    {
      title: "Bank ID",
      dataIndex: "Bank ID",
      width: 200,
      render: (value) => <Text>{value ?? "--"}</Text>,
    },
    {
      title: "Bank Name",
      dataIndex: "Bank Name",
      render: (value) => <Text>{value ?? "--"}</Text>,
    },
    {
      title: "Total AI Calls",
      dataIndex: "Total AI Calls",
      render: (value) => <Text>{value ?? 0}</Text>,
    },
    {
      title: "AI Calls Made",
      dataIndex: "AI Calls Made",
      render: (value) => <Text>{value ?? 0}</Text>,
    },
    {
      title: "AI Calls Connected",
      dataIndex: "AI Calls Connected",
      render: (value) => <Text>{value ?? 0}</Text>,
    },
    {
      title: "AI Calls Not Connected",
      dataIndex: "AI Calls Not Connected",
      render: (value) => <Text>{value ?? 0}</Text>,
    },
    {
      title: "Conclusion",
      dataIndex: "Conclusion",
      children: [
        {
          title: "Positive",
          dataIndex: "positive",
          render: (value) => <Text>{value ?? 0}</Text>,
        },
        {
          title: "Negative",
          dataIndex: "negative",
          render: (value) => <Text>{value ?? 0}</Text>,
        },
        {
          title: "Neutral",
          dataIndex: "neutral",
          render: (value) => <Text>{value ?? 0}</Text>,
        },
        {
          title: "Already Paid",
          dataIndex: "Already Paid",
          render: (value) => <Text>{value ?? 0}</Text>,
        },
        {
          title: "Busy",
          dataIndex: "busy",
          render: (value) => <Text>{value ?? 0}</Text>,
        },
        {
          title: "Wrong Number",
          dataIndex: "Wrong Number",
          render: (value) => <Text>{value ?? 0}</Text>,
        },
        {
          title: "AI Nothing",
          dataIndex: "AI Nothing",
          render: (value) => <Text>{value ?? 0}</Text>,
        },
      ],
    },
  ];

  const dataSource = Array.isArray(data)
    ? data.map((data, i) => ({
        key: i + 1,
        ...data,
      }))
    : [];

  // Handle downloading
  const handleDownload = async () => {
    if (data.length === 0) {
      return message.error("There is no data!");
    } else {
      // Get list of visible column keys
      const visibleColumns = columns.map((col) => col.dataIndex);
      // Filter only those keys from each row
      const filteredData = dataSource.map((row) => {
        const filteredRow = {};
        visibleColumns.forEach((key) => {
          filteredRow[key] = row[key];
        });
        return filteredRow;
      });
      await handleDownloadTable({
        excelName: "Ai-allocation",
        worksheetName: "Ai-Allocation",
        tableData: filteredData,
        fromDate: dates?.fromDate,
        toDate: dates?.toDate,
      });
    }
  };

  function getSummaryRow({ dataSource }) {
    const getTotal = (key) =>
      dataSource.reduce((sum, row) => sum + (Number(row[key]) || 0), 0);
    return (
      <Table.Summary fixed="bottom">
        <Table.Summary.Row>
          <Table.Summary.Cell index={0}>Total</Table.Summary.Cell>
          <Table.Summary.Cell index={1}>--</Table.Summary.Cell>
          <Table.Summary.Cell index={2}>--</Table.Summary.Cell>
          <Table.Summary.Cell index={3}>
            <Text>{getTotal("Total AI Calls")}</Text>
          </Table.Summary.Cell>
          <Table.Summary.Cell index={4}>
            <Text>{getTotal("AI Calls Made")}</Text>
          </Table.Summary.Cell>
          <Table.Summary.Cell index={5}>
            <Text>{getTotal("AI Calls Connected")}</Text>
          </Table.Summary.Cell>
          <Table.Summary.Cell index={6}>
            <Text>{getTotal("AI Calls Not Connected")}</Text>
          </Table.Summary.Cell>
          <Table.Summary.Cell index={7}>
            <Text>{getTotal("positive")}</Text>
          </Table.Summary.Cell>
          <Table.Summary.Cell index={8}>
            <Text>{getTotal("negative")}</Text>
          </Table.Summary.Cell>
          <Table.Summary.Cell index={9}>
            <Text>{getTotal("neutral")}</Text>
          </Table.Summary.Cell>
          <Table.Summary.Cell index={10}>
            <Text>{getTotal("Already Paid")}</Text>
          </Table.Summary.Cell>
          <Table.Summary.Cell index={11}>
            <Text>{getTotal("busy")}</Text>
          </Table.Summary.Cell>
          <Table.Summary.Cell index={12}>
            <Text>{getTotal("Wrong Number")}</Text>
          </Table.Summary.Cell>
          <Table.Summary.Cell index={13}>
            <Text>{getTotal("AI Nothing")}</Text>
          </Table.Summary.Cell>
        </Table.Summary.Row>
      </Table.Summary>
    );
  }

  return isLoading ? (
    <ApplicationLoader />
  ) : (
    <Flex vertical gap={10}>
      <Flex justify="end">
        <button className={Style.download_button} onClick={handleDownload}>
          <img src={DOWNLOAD_IMG} alt="download-button" />
        </button>
      </Flex>
      <Table
        bordered
        virtual
        className={[Style.customTable, Style.childrenTable]}
        columns={columns}
        dataSource={dataSource}
        scroll={{
          x: 2300,
          y: 350,
        }}
        pagination={{
          showSizeChanger: false,
        }}
        summary={getSummaryRow.bind(null, { dataSource })}
      />
    </Flex>
  );
}
AiCalls.propTypes = {
  data: PropTypes.array,
  isLoading: PropTypes.bool,
  dates: PropTypes.object,
};

import { Flex, Typography } from "antd";
import PropTypes from "prop-types";
import React from "react";
import { formatDigits } from "../../../constant";
import Style from "./_style.module.scss";

const { Text } = Typography;

export default function AnalysisCommunicationCard({ title, image, count }) {
  return (
    <Flex gap={10} vertical className={Style.comm_card}>
      <Text className={Style.title}>{title}</Text>
      <Flex justify="space-between" align="end">
        {image && (
          <div className={Style.img_div}>
            <img alt="channel" src={image} />
          </div>
        )}
        <Flex align="center" gap={4}>
          <Text className={Style.value}>{formatDigits(count ?? 0)}</Text>
        </Flex>
      </Flex>
    </Flex>
  );
}

AnalysisCommunicationCard.propTypes = {
  title: PropTypes.string,
  image: PropTypes.string,
  count: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
};

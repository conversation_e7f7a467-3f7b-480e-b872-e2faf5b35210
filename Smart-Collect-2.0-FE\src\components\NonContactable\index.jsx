import React, { useEffect, useState } from "react";
import {
  ALL_CACHE_NAMES,
  LOCAL_KEYS,
  SMART_COLLECT_MENU_IDS,
} from "../../constant";
import { Flex } from "antd";
import AppHeader from "../AppHeader";
import AppEngagements from "../AppEngagements";
import CustomerAllCards from "../CustomerAllCards";
import CustomersTable from "../CustomersTable";
import { AXIOS } from "../../apis/ho-Instance";
import Style from "./_non-contactable.module.scss";
import ApplicationLoader from "../ApplicationLoader";
import {
  getCacheKey,
  getFromCache,
  getPostCacheKey,
  storeToCache,
} from "../../utils/cacheHelper";
import { encryptBody, isSameEncryptedBody } from "../../utils/crypto";

const CACHE_NAME = ALL_CACHE_NAMES.DASHBOARD_CACHE;

export default function NonContactable() {
  const [isSwitchOn, setIsSwitchOn] = useState(false);
  const [customerData, setCustomerData] = useState([]);
  const [loading, setLoading] = useState(false);
  // Retrieve dates from localStorage
  const storedFilter = localStorage.getItem("filterDate");
  const storedDpd = localStorage.getItem("DashboardDpdCategory");

  const handleGetNonData = async () => {
    setLoading(true);
    const cacheKey = getCacheKey({ endpoint: "notcontactable/" });
    // Check cache
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setCustomerData(cachedData);
      setLoading(false);
    }
    try {
      const response = await AXIOS.get("notcontactable/");
      if (response.status === 200) {
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
        setCustomerData(response.data);
      }
    } catch (error) {
      console.log("Error in non-contactable", error?.message);
    } finally {
      setLoading(false);
    }
  };

  const handleNonContactable = async ({
    BranchName,
    dpdRange,
    disbursementID,
  }) => {
    setLoading(true);
    const body = {
      branch_id: BranchName,
      dpdfrom: dpdRange?.start ?? -1,
      dpdto: dpdRange?.end ?? -1,
      disbursementids: disbursementID,
    };
    const cacheKey = getPostCacheKey({ endpoint: "notcontactable/", body });
    const encryptedOldBody = localStorage.getItem(LOCAL_KEYS.HO_PAY_FILTER);
    const sameBody = isSameEncryptedBody({
      newBody: body,
      encryptedOldBody,
    });

    if (sameBody) {
      const cachedData = await getFromCache({
        cacheName: CACHE_NAME,
        key: cacheKey,
      });
      if (cachedData) {
        setCustomerData(cachedData);
        setLoading(false);
      }
    }
    try {
      const response = await AXIOS.post("notcontactable/", body);
      // Check the response
      if (response.status === 200) {
        const encrypted = encryptBody(body);
        if (encrypted) {
          localStorage.setItem(LOCAL_KEYS.HO_PAY_FILTER, encrypted);
        }
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
        setCustomerData(response.data);
      }
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoading(false);
    }
  };

  // Apply filter and fetch data
  const applyFilters = async ({
    fromDate,
    toDate,
    BranchName,
    disbursementID,
  }) => {
    await handleNonContactable({
      BranchName,
      dpdRange: JSON.parse(storedDpd),
      disbursementID,
    });
  };

  // Clear filters and fetch data
  const ClearFilters = () => {
    handleGetNonData();
    localStorage.removeItem("filterDate");
    localStorage.removeItem(LOCAL_KEYS.HO_PAY_FILTER);
  };

  useEffect(() => {
    if (storedFilter || storedDpd) {
      const { BranchName, disbursementID } = storedFilter
        ? JSON.parse(storedFilter)
        : { fromDate: null, toDate: null, BranchName: null };
      const parsedDpd = storedDpd
        ? JSON.parse(storedDpd)
        : { start: -1, end: -1 };
      handleNonContactable({ BranchName, dpdRange: parsedDpd, disbursementID });
    } else {
      handleGetNonData();
    }
  }, []);
  return (
    <Flex gap={15} vertical className={Style.non_contactable}>
      {/* Header */}
      <AppHeader
        title={`Non-Contactable: ${customerData.length}`}
        isDashboard={false}
        isSwitchOn={isSwitchOn}
        setIsSwitchOn={setIsSwitchOn}
        isDashboardOtherPages={true}
        applyFilters={applyFilters}
        ClearFilters={ClearFilters}
      />
      <Flex gap={15} vertical>
        {/* app engagements */}
        <AppEngagements />

        {/* Details */}
        {loading ? (
          <ApplicationLoader />
        ) : (
          <div className={Style.details}>
            {!isSwitchOn ? (
              <CustomerAllCards
                pageId={SMART_COLLECT_MENU_IDS.NON_CONTACTABLE}
                customerData={customerData}
                modalButtonText={""}
                isModal={false}
              />
            ) : (
              <CustomersTable
                title={"Non-Contactable"}
                customerData={customerData}
                pageId={SMART_COLLECT_MENU_IDS.NON_CONTACTABLE}
                modalButtonText={""}
              />
            )}
          </div>
        )}
      </Flex>
    </Flex>
  );
}

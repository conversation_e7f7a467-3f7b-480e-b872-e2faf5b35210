import json
from django.http import JsonResponse
from cryptography.fernet import <PERSON><PERSON><PERSON>
from SmartCollect2.urls import unencrypted_urls, PREFIX_CONSTANT
from Channels.urls import unencrypted_urls as channel_unencrypted_urls
from campaign.urls import exclude_urls as campaign_exclude_urls
from urllib.parse import urlencode, parse_qs
import os
from Crypto.Cipher import AES
from base64 import b64decode, b64encode
import binascii
from Crypto.Util.Padding import unpad, pad
from django.http import QueryDict
from io import BytesIO
import re
from WebApp.urls import SKIP_URLS

# Replace this key with your actual encryption key get key from 'ENCRYPT_KEY' environment variable
ENCRYPTION_KEY = os.getenv('ENCRYPT_KEY').encode()


all_unencrypted_urls = unencrypted_urls + channel_unencrypted_urls + SKIP_URLS

# print the patterns for unencrypted urls

unencrypted_urls_patters = [re.compile(url.pattern.regex.pattern) for url in all_unencrypted_urls]


def decrypt_data_new(encrypted_data):
    try:
        # Convert SECRET_KEY to bytes and create a cipher object
        cipher = AES.new(ENCRYPTION_KEY, AES.MODE_ECB)

        # Decode Base64 and decrypt the data
        decrypted_data = cipher.decrypt(b64decode(encrypted_data))
        # print(f"Decrypted data: {decrypted_data}")
        unpadded_data = unpad(decrypted_data, AES.block_size)
        # Parse and return JSON
        return unpadded_data
    except (ValueError) as e:
        # Handle Base64 errors, padding errors, or invalid decryption data
        print(f"Decryption error: {e}")
        return None


def encrypt_data(data):
    try:
        # Convert SECRET_KEY to bytes and create a cipher object
        cipher = AES.new(ENCRYPTION_KEY, AES.MODE_ECB)

        # Convert the data to JSON and pad it
        json_data = data
        padded_data = pad(json_data, AES.block_size)

        # Encrypt the data
        encrypted_data = cipher.encrypt(padded_data)

        # Encode the encrypted data as Base64
        return b64encode(encrypted_data).decode('utf-8')
    except Exception as e:
        print(f"Encryption error: {e}")
        return None


class EncryptionMiddleware:
    def __init__(self, get_response):
        try:
            self.get_response = get_response
        except Exception as e:
            print(f"Error initializing EncryptionMiddleware: {e}")

    def decrypt_incoming(self, request):
        try:
            # Decrypt incoming GET query parameters
            if request.method == 'GET' and request.GET:
                encrypted_query = request.GET.get('query')
                if encrypted_query:
                    decrypted_query = decrypt_data_new(encrypted_query)
                    if decrypted_query:
                        # Parse the decrypted query string
                        query_dict = parse_qs(decrypted_query.decode())
                        # Update the request's GET parameters
                        request.GET = QueryDict(urlencode(query_dict, doseq=True))

            # Decrypt incoming POST request body (JSON payload)

            if request.method == 'POST' and request.body:
                bytesdata = decrypt_data_new(request.body.decode())
                request._stream = BytesIO(bytesdata)
                request._read_started = False
                request.META['CONTENT_TYPE'] = 'application/json'
        except Exception as e:
            print(f"Decryption error: {e}")
            return JsonResponse({'error': 'Invalid or unencrypted request'}, status=400)

    def encrypt_response(self, response):
        try:
            # skip encryption if the content type is not json
            # Skip FileResponse and StreamingHttpResponse types
            from django.http import StreamingHttpResponse, FileResponse
            if isinstance(response, (StreamingHttpResponse, FileResponse)):
                # print("Skipping encryption for streaming response (e.g., FileResponse)")
                return response

            if not response.get('Content-Type', '').lower().startswith('application/json'):
                # print("Skipping encryption for non-JSON response", response.get('Content-Type'))
                return response

            # Encrypt the response content
            if getattr(response, 'content', None) is not None:
                encrypted_response = encrypt_data(response.content)
                response.content = encrypted_response
                response['Content-Type'] = 'application/json'
        except Exception as e:
            print(f"Encryption error: {e}")
            return JsonResponse({'error': 'Failed to encrypt response'}, status=500)

    def __call__(self, request):
        try:
            # Skip encryption for unencrypted URLs
            request_path = request.path[1:]
            for pattern in unencrypted_urls_patters:
                # remove the prefix from the URL path
                request_path = re.sub(f"^{PREFIX_CONSTANT}", '', request_path)
                # print(f"Checking pattern: {pattern}", request_path)
                if re.match(pattern, request_path):
                    print(f"Skipping encryption for pattern: {pattern}")
                    return self.get_response(request)

            # Skip encryption and decryption for key 'X-Skip-Decryption' is in headers and settings.debug is True
            # NOTE: THIS IS FOR DEBUGGING PURPOSES ONLY, DO NOT USE IN PRODUCTION, REMOVE THIS LINE BEFORE DEPLOYMENT
            # Check if the request has the header or Get Param 'HTTP_X_SKIP_DECRYPTION' and if DEBUG is True
            if (request.headers.get('X-Skip-Decryption') == 'true' or request.GET.get('HTTP_X_SKIP_DECRYPTION') == 'true') and os.getenv('DEBUG', 'False').lower() == 'true':
                return self.get_response(request)

            # options method skip decryption and encryption
            if request.method == 'OPTIONS':
                return self.get_response(request)

            # Decrypt incoming request
            self.decrypt_incoming(request)

            # Process the request
            response = self.get_response(request)

            # Encrypt outgoing response
            self.encrypt_response(response)
            return response
        except Exception as e:
            print(f"Middleware error: {e}")
            return JsonResponse({'error': 'An error occurred in the middleware'}, status=500)

import React from "react";

export default function Pending() {
  return (
    <svg
      width={15}
      height={15}
      viewBox="0 0 18 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.385 18.9983C12.2703 18.9983 11.325 18.6103 10.549 17.8343C9.773 17.0569 9.385 16.1116 9.385 14.9983C9.385 13.8849 9.773 12.9399 10.549 12.1633C11.325 11.3866 12.27 10.9983 13.384 10.9983C14.4987 10.9983 15.444 11.3866 16.22 12.1633C16.9967 12.9393 17.385 13.8843 17.385 14.9983C17.385 16.1123 16.9967 17.0576 16.22 17.8343C15.4433 18.6109 14.4983 18.9989 13.385 18.9983ZM15.04 17.2003L15.586 16.6543L13.769 14.8363V12.1143H13V15.1603L15.04 17.2003ZM1.615 17.9983C1.16833 17.9983 0.787333 17.8409 0.472 17.5263C0.157333 17.2116 0 16.8306 0 16.3833V3.61328C0 3.16661 0.157333 2.78561 0.472 2.47028C0.786667 2.15561 1.168 1.99828 1.616 1.99828H6.252C6.34533 1.60761 6.553 1.27928 6.875 1.01328C7.197 0.746615 7.572 0.613281 8 0.613281C8.436 0.613281 8.814 0.746615 9.134 1.01328C9.454 1.27861 9.66067 1.60695 9.754 1.99828H14.384C14.832 1.99828 15.2133 2.15561 15.528 2.47028C15.8427 2.78495 16 3.16628 16 3.61428V9.63328C15.828 9.54461 15.6653 9.46695 15.512 9.40028C15.3587 9.33362 15.188 9.27395 15 9.22128V3.61328C15 3.45995 14.936 3.31895 14.808 3.19028C14.68 3.06161 14.5387 2.99761 14.384 2.99828H12V5.22828H4V2.99828H1.616C1.462 2.99828 1.32067 3.06228 1.192 3.19028C1.06333 3.31828 0.999333 3.45961 1 3.61428V16.3833C1 16.5626 1.05767 16.7099 1.173 16.8253C1.28833 16.9406 1.436 16.9983 1.616 16.9983H7.742C7.79533 17.1849 7.85833 17.3586 7.931 17.5193C8.00433 17.6793 8.09767 17.8389 8.211 17.9983H1.615ZM8 3.22828C8.232 3.22828 8.42467 3.15195 8.578 2.99928C8.73133 2.84595 8.808 2.65328 8.808 2.42128C8.808 2.18928 8.73133 1.99661 8.578 1.84328C8.42467 1.68995 8.232 1.61328 8 1.61328C7.768 1.61328 7.57533 1.68995 7.422 1.84328C7.26867 1.99661 7.192 2.18928 7.192 2.42128C7.192 2.65328 7.26867 2.84595 7.422 2.99928C7.57533 3.15261 7.768 3.22828 8 3.22828Z"
        fill="#0F2050"
      />
    </svg>
  );
}

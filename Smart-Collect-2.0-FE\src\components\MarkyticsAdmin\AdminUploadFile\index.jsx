import { Button, <PERSON>, <PERSON>lex, <PERSON>, <PERSON>, Typography } from 'antd'
import React from 'react'
import AppHeader from '../../AppHeader'
import Style from "./_style.module.scss";
import { UploadComponent } from './UploadComponent';

const {Text} = Typography;

export default function AdminUploadFile() {
  const handleFileChange = () =>{}
  const handleBankChange = () =>{}
  return (
    <Flex vertical gap={50}>
      <AppHeader title={"Upload File"}/>

      <Flex vertical gap={20} className={Style.upload_container}>
        <Row gutter={[20,40]}>
          <Col md={16} xs={24}>
            <Select
              className={Style.custom_select}
              placeholder="Select File Type"
              onChange={handleFileChange}
              options={[]}
              allowClear
            />
          </Col>

          <Col md={8} xs={24}> 
            <Select
              className={Style.custom_select}
              placeholder="Select Bank"
              onChange={handleBankChange}
              options={[]}
              allowClear
            />
          </Col>

          <Col span={24}>
           <UploadComponent/>
          </Col>
        </Row>

        <Flex vertical gap={5}>
          <Text className={Style.error}>Please select file type to download the sample.</Text>
          <Text className={Style.text}>Add data to an excel file with same format as sample file</Text>
        </Flex>
        
        <Flex justify='center'>
          <Button className={Style.upload_button}>Upload File</Button>
        </Flex>
      </Flex>
    </Flex>
  )
}

import React, { useMemo } from "react";
import Style from "./_refused.module.scss";
import { Flex, Input, Select, Typography } from "antd";
import { REFUSED_TO_PAY_OPTIONS_DATA } from "../../../../../constant";
import PropTypes from "prop-types";

const { Text } = Typography;
export function RefusedToPay({ feedbackData }) {
  const selectedReason = useMemo(()=>{
    if(["holiday","death"].includes(feedbackData.ReasonForDenial)) return feedbackData.ReasonForDenial;
    else return "custom";
  },[feedbackData]);

  return (
    <div className={Style.refused_to_pay}>
      <Flex vertical gap={20} justify="center">
        {/* reason */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Reason for Customer Denial:</Text>
          <Select
            className={Style.refused_select}
             value={selectedReason}
             options={REFUSED_TO_PAY_OPTIONS_DATA}
            disabled
          />
        </Flex>

        {/* custom */}
        { feedbackData?.ReasonForDenial &&  
          feedbackData?.ReasonForDenial !== "holiday" &&
          feedbackData?.ReasonForDenial !== "death" && (
            <Flex justify="space-between" align="center">
              <Text className={Style.text}>Description for Denial:</Text>
              <Input
                placeholder="Enter"
                className={Style.custom_input}
                value={feedbackData?.ReasonForDenial}
                disabled
              />
            </Flex>
          )}
      </Flex>
    </div>
  );
}

RefusedToPay.propTypes={
  feedbackData: PropTypes.object
}
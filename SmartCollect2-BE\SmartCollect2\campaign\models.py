from django.db import models
from WebApp.models import BankMst, LanguageMst


# Create your models here.
class CommFlowMst(models.Model):
    CommFlowMstID = models.AutoField(primary_key=True, db_column='CommFlowMstID')
    ColumnName = models.CharField(max_length=255, blank=True, null=True)
    BasedOnTable = models.CharField(max_length=255, blank=True, null=True)
    BankName = models.CharField(max_length=255)
    CreatedDate = models.DateTimeField(auto_now_add=True)
    BankMstID = models.ForeignKey(BankMst, on_delete=models.PROTECT, db_column='BankMstID')
    IsActive = models.BooleanField(default=True)
    Type = models.CharField(max_length=50)
    BasedOnColumn = models.CharField(max_length=255, blank=True, null=True)
    FlowName = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = "CommFlowMst"


class CommFlow(models.Model):
    CommFlowID = models.AutoField(primary_key=True, db_column='CommFlowID')
    CommunicationType = models.CharField(max_length=100)
    CommFlowMstID = models.ForeignKey(CommFlowMst, on_delete=models.PROTECT, db_column='CommFlowMstID')
    Days = models.CharField(max_length=255, null=True, blank=True)
    BeforeAfter = models.CharField(max_length=10)
    FlowID = models.IntegerField(blank=True, null=True)
    ExtraColumn1 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn2 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn3 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn4 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn5 = models.CharField(max_length=255, blank=True, null=True)
    BankMstID = models.ForeignKey(BankMst, on_delete=models.PROTECT, db_column='BankMstID', blank=True, null=True)
    BasedOn = models.CharField(max_length=255, blank=True, null=True)
    BasedOnTable = models.CharField(max_length=255, blank=True, null=True)
    TemplateID = models.CharField(max_length=255, blank=True, null=True)
    CampaignMstID = models.ForeignKey('CampaignMst', on_delete=models.PROTECT, db_column='CampaignMstID', blank=True, null=True)
    campaign_type = models.TextField(blank=True, null=True)
    PeriodType = models.CharField(max_length=20, blank=True, null=True)
    frequency = models.TextField(max_length=20, blank=True, null=True)
    startdate = models.DateField(blank=True, null=True)
    interval = models.IntegerField(blank=True, null=True)
    count = models.IntegerField(blank=True, null=True)
    until = models.DateField(blank=True, null=True)
    bysetpos = models.TextField(blank=True, null=True)
    bymonth = models.TextField(blank=True, null=True)
    byyearday = models.TextField(blank=True, null=True)
    byweekno = models.TextField(blank=True, null=True)
    byweekday = models.TextField(blank=True, null=True)
    weekstart = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "CommFlow"


class dates_table(models.Model):
    date = models.DateField(primary_key=True, db_column='date')
    commflowid = models.ForeignKey(CommFlow, on_delete=models.CASCADE, db_column='commflowid', blank=True, null=True)

    class Meta:
        managed = False
        db_table = "dates_table"
        constraints = [
            models.UniqueConstraint(fields=['date', 'commflowid'], name='unique_date_commflow')
        ]


class CampaignMst(models.Model):
    CampaignMstID = models.AutoField(primary_key=True, db_column='CampaignMstID')
    Name = models.CharField(max_length=255)
    IsActive = models.BooleanField(default=True)
    BankMstID = models.ForeignKey(BankMst, on_delete=models.PROTECT, db_column='BankMstID')
    CreatedDate = models.DateField(blank=True, null=True)
    Priority = models.CharField(max_length=255, blank=True, null=True)
    Status = models.CharField(max_length=255, blank=True, null=True, default='Saved')
    CampaignType = models.CharField(max_length=255, blank=True, null=True)
    Language = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = "CampaignMst"


class Filters(models.Model):
    FiltersID = models.AutoField(primary_key=True)
    BankMstID = models.ForeignKey(BankMst, on_delete=models.PROTECT, db_column='BankMstID')
    Column = models.CharField(max_length=255)
    Comparison = models.CharField(max_length=50, choices=[
        ("=", "="),
        ("!=", "!="),
        (">", ">"),
        (">=", ">="),
        ("<", "<"),
        ("<=", "<="),
        ("in", "IN"),
        ("not in", "NOT IN"),
        # ("LIKE", "LIKE"),
        # ("NOT LIKE", "NOT LIKE"),
        # ("BETWEEN", "BETWEEN"),
        # ("NOT BETWEEN", "NOT BETWEEN"),
        # ("range", "range")
    ])
    Value = models.CharField(max_length=1000)
    ValueType = models.CharField(max_length=50, blank=True, null=True, choices=[("value", "Value"), ("column", "Column")])

    class Meta:
        managed = False
        db_table = "Filters"


class FilterMst(models.Model):
    ID = models.AutoField(primary_key=True)
    Name = models.CharField(max_length=255)
    CommFlowID = models.ForeignKey(CommFlow, on_delete=models.PROTECT, db_column='CommFlowID', null=True)
    IsActive = models.BooleanField(default=True)
    BankMstID = models.ForeignKey(BankMst, on_delete=models.PROTECT, db_column='BankMstID')
    FiltersID = models.ForeignKey(Filters, on_delete=models.PROTECT, db_column='FiltersID', null=True)
    CampaignMstID = models.ForeignKey(CampaignMst, on_delete=models.PROTECT, db_column='CampaignMstID', null=True)
    CommFlowMstID = models.ForeignKey(CommFlowMst, on_delete=models.PROTECT, db_column='CommFlowMstID', null=True)

    class Meta:
        managed = False
        db_table = "FilterMst"


class WhatsAppUserTemplate(models.Model):
    WhatsAppUserTemplateID = models.AutoField(primary_key=True)
    TemplateName = models.CharField(max_length=255)
    # TemplateBody = models.TextField()
    BankMstID = models.ForeignKey(BankMst, on_delete=models.PROTECT, db_column='BankMstID')
    # EmployeeMstID = models.ForeignKey('WebApp.EmployeeMst', on_delete=models.PROTECT, db_column='EmployeeMstID')
    Variable1 = models.CharField(max_length=255, blank=True, null=True)
    Variable2 = models.CharField(max_length=255, blank=True, null=True)
    Variable3 = models.CharField(max_length=255, blank=True, null=True)
    Variable4 = models.CharField(max_length=255, blank=True, null=True)
    Variable5 = models.CharField(max_length=255, blank=True, null=True)
    Variable6 = models.CharField(max_length=255, blank=True, null=True)
    Variable7 = models.CharField(max_length=255, blank=True, null=True)
    Variable8 = models.CharField(max_length=255, blank=True, null=True)
    Variable9 = models.CharField(max_length=255, blank=True, null=True)
    Variable10 = models.CharField(max_length=255, blank=True, null=True)
    Button1 = models.CharField(max_length=255, blank=True, null=True)
    Button2 = models.CharField(max_length=255, blank=True, null=True)
    Button3 = models.CharField(max_length=255, blank=True, null=True)
    Button4 = models.CharField(max_length=255, blank=True, null=True)
    Response1 = models.TextField(null=True, blank=True)
    Response2 = models.TextField(null=True, blank=True)
    Response3 = models.TextField(null=True, blank=True)
    Response4 = models.TextField(null=True, blank=True)
    WhatsAppTemplateMappingID = models.ForeignKey('WhatsAppTemplateMapping', on_delete=models.PROTECT, db_column='WhatsAppTemplateMappingID', null=True)

    class Meta:
        managed = False
        db_table = "WhatsAppUserTemplate"


class WhatsAppTemplateMapping(models.Model):
    WhatsAppTemplateMappingID = models.AutoField(primary_key=True)
    MetaTemplateID = models.CharField(max_length=255, blank=True, null=True)
    CreatedDate = models.DateTimeField(auto_now_add=True)
    Status = models.CharField(max_length=50)
    TemplateName = models.CharField(max_length=255)
    IsActive = models.BooleanField(default=True)
    LngMstID = models.ForeignKey(LanguageMst, on_delete=models.PROTECT, db_column='LngMstID')
    FileName = models.CharField(max_length=255)
    IsAdminCreated = models.BooleanField(default=False)
    # UserTemplateID = models.ForeignKey(WhatsAppUserTemplate, on_delete=models.PROTECT, db_column='UserTemplateID')
    Extra_Column1 = models.CharField(max_length=255, blank=True, null=True)
    Extra_Column2 = models.CharField(max_length=255, blank=True, null=True)
    Extra_Column3 = models.CharField(max_length=255, blank=True, null=True)
    Extra_Column4 = models.CharField(max_length=255, blank=True, null=True)
    Extra_Column5 = models.CharField(max_length=255, blank=True, null=True)
    BankMstID = models.ForeignKey(BankMst, on_delete=models.PROTECT, db_column='BankMstID', null=True)
    TemplateBody = models.TextField(null=True, blank=True)

    class Meta:
        managed = False
        db_table = "WhatsAppTemplateMapping"


class WhatsAppFlowMapping(models.Model):
    WhatsAppFlowID = models.AutoField(primary_key=True)
    CreatedDate = models.DateTimeField(auto_now_add=True)
    FlowID = models.IntegerField()
    FlowName = models.CharField(max_length=255)
    Response = models.TextField()
    IsActive = models.BooleanField(default=True)
    LngMstID = models.ForeignKey(LanguageMst, on_delete=models.PROTECT, db_column='LngMstID')
    WhatsAppTemplateMappingID = models.ForeignKey(WhatsAppTemplateMapping, on_delete=models.PROTECT, db_column='WhatsAppTemplateMappingID')
    WhatsAppFlowMstID = models.ForeignKey('WhatsAppFlowMst', on_delete=models.PROTECT, db_column='WhatsAppFlowMstID')
    ExtraColumn1 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn2 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn3 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn4 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn5 = models.CharField(max_length=255, blank=True, null=True)
    BankMstID = models.ForeignKey(BankMst, on_delete=models.PROTECT, db_column='BankMstID', blank=True, null=True)
    IsStart = models.BooleanField(default=False)
    NextTemplate = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = "WhatsAppFlowMapping"


class WhatsAppFlowMst(models.Model):
    WhatsAppFlowMstID = models.AutoField(primary_key=True, db_column='WhatsAppFlowMstID')
    FlowName = models.CharField(max_length=255)
    IsActive = models.BooleanField(default=True)
    # BankID = models.ForeignKey(BankMst, on_delete=models.PROTECT, db_column='BankID')
    ExtraColumn1 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn2 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn3 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn4 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn5 = models.CharField(max_length=255, blank=True, null=True)
    BankMstID = models.ForeignKey(BankMst, on_delete=models.PROTECT, db_column='BankMstID', blank=True, null=True)

    class Meta:
        managed = False
        db_table = "WhatsAppFlowMst"


class WhatsappVariableMapping(models.Model):
    WhatsappVariableMappingID = models.AutoField(primary_key=True)
    WhatsAppTemplateMappingID = models.ForeignKey(WhatsAppTemplateMapping, on_delete=models.PROTECT, db_column='WhatsAppTemplateMappingID', blank=True, null=True)
    VariableNo = models.IntegerField(blank=True, null=True)
    VariableField = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = "WhatsappVariableMapping"


class BlasterTemplateMapping(models.Model):
    BlasterTemplateMappingID = models.AutoField(primary_key=True)
    CreatedDate = models.DateField(auto_now_add=True)
    Status = models.CharField(max_length=50)
    TemplateName = models.CharField(max_length=255)
    IsActive = models.BooleanField(default=True)
    LngMstID = models.ForeignKey(LanguageMst, on_delete=models.PROTECT, db_column='LngMstID')
    FileName = models.CharField(max_length=255, null=True, blank=True)
    IsAdminCreated = models.BooleanField(default=False)
    # BlasterUserTemplateID = models.ForeignKey(BlasterUserTemplate, on_delete=models.PROTECT, db_column='BlasterUserTemplateID')
    RecordingURL = models.CharField(max_length=255, null=True, blank=True)
    BankMstID = models.ForeignKey(BankMst, on_delete=models.PROTECT, db_column='BankMstID', null=True)
    TemplateBody = models.TextField(null=True, blank=True)

    class Meta:
        managed = False
        db_table = "BlasterTemplateMapping"


class BlasterUserTemplate(models.Model):
    BlasterUserTemplateID = models.AutoField(primary_key=True)
    TemplateName = models.CharField(max_length=255)
    TemplateBody = models.TextField(null=True, blank=True)
    BankMstID = models.ForeignKey(BankMst, on_delete=models.PROTECT, db_column='BankMstID')
    # EmployeeMstID = models.ForeignKey('WebApp.EmployeeMst', on_delete=models.PROTECT, db_column='EmployeeMstID')
    VariableNumber = models.IntegerField()
    VariableField = models.CharField(max_length=255)
    BlasterTemplateMappingID = models.ForeignKey(BlasterTemplateMapping, on_delete=models.PROTECT, db_column='BlasterTemplateMappingID', null=True)

    class Meta:
        managed = False
        db_table = "BlasterUserTemplate"


class VoiceBotTemplateMapping(models.Model):
    VoiceBotTemplateMappingID = models.AutoField(primary_key=True)
    CreatedDate = models.DateTimeField(auto_now_add=True)
    Status = models.CharField(max_length=50)
    TemplateName = models.CharField(max_length=255)
    IsActive = models.BooleanField(default=True)
    LngMstID = models.ForeignKey(LanguageMst, on_delete=models.PROTECT, db_column='LngMstID')
    FileName = models.CharField(max_length=255, null=True, blank=True)
    IsAdminCreated = models.BooleanField(default=False)
    RecordingURL = models.CharField(max_length=255, null=True, blank=True)
    BankMstID = models.ForeignKey(BankMst, on_delete=models.PROTECT, db_column='BankMstID', null=True)
    TemplateBody = models.TextField(null=True, blank=True)
    StartMsg = models.TextField(null=True, blank=True)
    EndMsg = models.TextField(null=True, blank=True)

    class Meta:
        managed = False
        db_table = "VoiceBotTemplateMapping"


class VoiceBotUserTemplate(models.Model):
    VoiceBotUserTemplateID = models.AutoField(primary_key=True)
    TemplateName = models.CharField(max_length=255)
    BankMstID = models.ForeignKey(BankMst, on_delete=models.PROTECT, db_column='BankMstID')
    # EmployeeMstID = models.CharField(max_length=255)
    VariableNumber = models.IntegerField()
    VariableField = models.CharField(max_length=255)
    VoiceBotTemplateMappingID = models.ForeignKey(VoiceBotTemplateMapping, on_delete=models.PROTECT, db_column='VoiceBotTemplateMappingID', null=True)
    StartEnd = models.CharField(max_length=50, null=True, blank=True)

    class Meta:
        managed = False
        db_table = "VoiceBotUserTemplate"


class IVRFlowMst(models.Model):
    IVRFlowMstID = models.AutoField(primary_key=True, db_column='IVRFlowMstID')
    FlowName = models.CharField(max_length=255, blank=True, null=True)
    IsActive = models.BooleanField(default=True)
    ExtraColumn1 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn2 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn3 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn4 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn5 = models.CharField(max_length=255, blank=True, null=True)
    BankMstID = models.ForeignKey(BankMst, on_delete=models.PROTECT, db_column='BankMstID', blank=True, null=True)

    class Meta:
        managed = False
        db_table = "IVRFlowMst"


class IVRFlowMapping(models.Model):
    IVRFlowID = models.AutoField(primary_key=True, db_column='IVRFlowID')
    CreatedDate = models.DateTimeField(auto_now_add=True)
    FlowID = models.IntegerField()
    FlowName = models.CharField(max_length=255, blank=True, null=True)
    Response = models.TextField(blank=True, null=True)
    IsActive = models.BooleanField(default=True)
    LngMstID = models.ForeignKey(LanguageMst, on_delete=models.PROTECT, db_column='LngMstID', blank=True, null=True)
    IVRTemplateMappingID = models.ForeignKey('IVRTemplateMapping', on_delete=models.PROTECT, db_column='IVRTemplateMappingID', blank=True, null=True)
    IVRFlowMstID = models.ForeignKey(IVRFlowMst, on_delete=models.PROTECT, db_column='IVRFlowMstID', blank=True, null=True)
    IsStart = models.BooleanField(default=False)
    ButtonPressed = models.CharField(max_length=50, blank=True, null=True)
    NextTemplate = models.IntegerField(blank=True, null=True)
    CurrentTemplate = models.IntegerField(blank=True, null=True)
    ExtraColumn1 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn2 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn3 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn4 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn5 = models.CharField(max_length=255, blank=True, null=True)
    BankMstID = models.ForeignKey(BankMst, on_delete=models.PROTECT, db_column='BankMstID', blank=True, null=True)

    class Meta:
        managed = False
        db_table = "IVRFlowMapping"


class IVRTemplateMapping(models.Model):
    IVRTemplateMappingID = models.AutoField(primary_key=True, db_column='IVRTemplateMappingID')
    CreatedDate = models.DateTimeField(auto_now_add=True)
    Status = models.CharField(max_length=50)
    TemplateName = models.CharField(max_length=255)
    IsActive = models.BooleanField(default=True)
    LngMstID = models.ForeignKey(LanguageMst, on_delete=models.PROTECT, db_column='LngMstID')
    FileName = models.CharField(max_length=255)
    IsAdminCreated = models.BooleanField(default=False)
    RecordingURL = models.CharField(max_length=255)
    BankMstID = models.ForeignKey(BankMst, on_delete=models.PROTECT, db_column='BankMstID', blank=True, null=True)
    TemplateBody = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "IVRTemplateMapping"


class IVRUserTemplate(models.Model):
    IVRUserTemplateID = models.AutoField(primary_key=True, db_column='IVRUserTemplateID')
    TemplateName = models.CharField(max_length=255)
    BankMstID = models.ForeignKey(BankMst, on_delete=models.PROTECT, db_column='BankMstID')
    # EmployeeMstID = models.ForeignKey('WebApp.EmployeeMst', on_delete=models.PROTECT, db_column='EmployeeMstID', blank=True, null=True)
    VariableNumber = models.IntegerField(null=True)
    VariableField = models.CharField(max_length=255, null=True)
    IVRTemplateMappingID = models.ForeignKey(IVRTemplateMapping, on_delete=models.PROTECT, db_column='IVRTemplateMappingID', blank=True, null=True)

    class Meta:
        managed = False
        db_table = "IVRUserTemplate"


class IVRVariableMapping(models.Model):
    IVRVariableMappingID = models.AutoField(primary_key=True, db_column='IVRVariableMappingID')
    IVRTemplateMappingID = models.ForeignKey(IVRTemplateMapping, on_delete=models.PROTECT, db_column='IVRTemplateMappingID', blank=True, null=True)
    VariableNo = models.IntegerField(blank=True, null=True)
    VariableField = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = "IVRVariableMapping"


'''
CREATE TABLE public."SMSTemplateMapping" (
	"SMSTemplateMappingID" serial4 NOT NULL,
	"CreatedDate" date DEFAULT CURRENT_DATE NULL,
	"Status" varchar(50) NOT NULL,
	"TemplateName" varchar(255) NOT NULL,
	"IsActive" bool DEFAULT true NOT NULL,
	"LngMstID" int4 NOT NULL,
	"FileName" varchar(255) NOT NULL,
	"IsAdminCreated" bool NOT NULL,
	"SMSUserTemplateID" int4 NOT NULL,
	"RecordingURL" varchar(255) NULL,
	"BankMstID" int4 NULL,
	"TemplateBody" varchar NULL,
	CONSTRAINT "SMSTemplateMapping_pkey" PRIMARY KEY ("SMSTemplateMappingID"),
	CONSTRAINT "SMSTemplateMapping_BankMstID_fkey" FOREIGN KEY ("BankMstID") REFERENCES public."BankMst"("BankMstID"),
	CONSTRAINT "SMSTemplateMapping_fk_LngMstID" FOREIGN KEY ("LngMstID") REFERENCES public."LanguageMst"("LngMstID"),
	CONSTRAINT "SMSTemplateMapping_fk_SMSUserTemplateID" FOREIGN KEY ("SMSUserTemplateID") REFERENCES public."SMSUserTemplate"("SMSUserTemplateID")
);'''
class SMSTemplateMapping(models.Model):
    SMSTemplateMappingID = models.AutoField(primary_key=True)
    CreatedDate = models.DateField(auto_now_add=True)
    Status = models.CharField(max_length=50)
    TemplateName = models.CharField(max_length=255)
    IsActive = models.BooleanField(default=True)
    LngMstID = models.ForeignKey(LanguageMst, on_delete=models.PROTECT, db_column='LngMstID')
    FileName = models.CharField(max_length=255)
    IsAdminCreated = models.BooleanField(default=False)
    RecordingURL = models.CharField(max_length=255, blank=True, null=True)
    BankMstID = models.ForeignKey(BankMst, on_delete=models.PROTECT, db_column='BankMstID', blank=True, null=True)
    TemplateBody = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "SMSTemplateMapping"


class SMSUserTemplate(models.Model):
    SMSUserTemplateID = models.AutoField(primary_key=True)
    TemplateName = models.CharField(max_length=255)
    BankMstID = models.ForeignKey(BankMst, on_delete=models.PROTECT, db_column='BankMstID')
    # EmployeeMstID = models.ForeignKey('WebApp.EmployeeMst', on_delete=models.PROTECT, db_column='EmployeeMstID')
    VariableNumber = models.IntegerField()
    VariableField = models.CharField(max_length=255)
    SMSTemplateMappingID = models.ForeignKey(SMSTemplateMapping, on_delete=models.PROTECT, db_column='SMSTemplateMappingID', blank=True, null=True)

    class Meta:
        managed = False
        db_table = "SMSUserTemplate"



# ...existing code...

class CallingTemplateMapping(models.Model):
    CallingTemplateMappingID = models.AutoField(primary_key=True)
    CreatedDate = models.DateField(auto_now_add=True)
    Status = models.CharField(max_length=50)
    TemplateName = models.CharField(max_length=255)
    IsActive = models.BooleanField(default=True)
    IsAdminCreated = models.BooleanField(default=False)
    RecordingURL = models.CharField(max_length=255, null=True, blank=True)
    BankMstID = models.ForeignKey(BankMst, on_delete=models.PROTECT, db_column='BankMstID', null=True)
    TemplateBody = models.TextField(null=True, blank=True)

    class Meta:
        managed = False
        db_table = "CallingTemplateMapping"


class CallingUserTemplate(models.Model):
    CallingUserTemplateID = models.AutoField(primary_key=True)
    TemplateName = models.CharField(max_length=255)
    TemplateBody = models.TextField(null=True, blank=True)
    BankMstID = models.ForeignKey(BankMst, on_delete=models.PROTECT, db_column='BankMstID')
    #EmployeeMstID = models.ForeignKey('WebApp.EmployeeMst', on_delete=models.PROTECT, db_column='EmployeeMstID', null=True)
    CallingTemplateMappingID = models.ForeignKey(CallingTemplateMapping, on_delete=models.PROTECT, db_column='CallingTemplateMappingID', null=True)

    class Meta:
        managed = False
        db_table = "CallingUserTemplate"
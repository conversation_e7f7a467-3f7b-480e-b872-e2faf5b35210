import requests
import pandas as pd
from datetime import datetime,<PERSON><PERSON><PERSON>

def get_geolocation_data(ip: str):
    with requests.get(f'http://www.geoplugin.net/json.gp?ip={ip}') as handler:
        data = handler.json()
        if data.get('geoplugin_status') == 404:
            return {}
        newdata = {}
        for key, value in data.items():
            key = key.replace('geoplugin_', '')
            if key == 'regionCode':
                newdata['region_code'] = value
            elif key == 'regionName':
                newdata['region_name'] = value
            elif key == 'countryCode':
                newdata['country_code'] = value
            elif key == 'countryName':
                newdata['country_name'] = value
            elif key == 'currencyCode':
                newdata['currency'] = value
            else:
                newdata[key] = value
        
        return newdata
import { Table, Typography } from "antd";
import React, { useEffect, useState } from "react";
import {
  formatAmount,
  formatDigits,
  handleDateFormatter,
} from "../../../constant";
import Style from "./_style.module.scss";
import PropTypes from "prop-types";

const { Text } = Typography;

export default function CommunicationResponseTable({ responseData, loading }) {
  const [data, setData] = useState([]);
  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      render: (_, data) => <Text>{data.key}.</Text>,
    },
    {
      title: "Record Date",
      dataIndex: "record_date",
      render: (value) => (
        <Text>{value ? handleDateFormatter(value) : "--"}</Text>
      ),
    },
    {
      title: "Event Type",
      dataIndex: "event_type",
      render: (value) => <Text>{value ?? "-"}</Text>,
    },
    {
      title: "Status",
      dataIndex: "status",
      render: (value) => <Text>{value}</Text>,
    },
    {
      title: "Communication Channels",
      dataIndex: "communication_channel",
      children: [
        {
          title: "Whatsapp",
          dataIndex: "whatsapp_count",
          render: (value) => <Text>{formatDigits(value ?? 0)}</Text>,
        },
        {
          title: "Blaster",
          dataIndex: "blaster_count",
          render: (value) => <Text>{formatDigits(value ?? 0)}</Text>,
        },
        {
          title: "AI calls",
          dataIndex: "voicebot_count",
          render: (value) => <Text>{formatDigits(value ?? 0)}</Text>,
        },
        {
          title: "Dialer calls",
          dataIndex: "feedback_count",
          render: (value) => <Text>{formatDigits(value ?? 0)}</Text>,
        },
      ],
    },
    {
      title: "Event Source",
      dataIndex: "event_source",
      render: (value) => <Text>{value ?? "-"}</Text>,
    },
    {
      title: "Event Date",
      dataIndex: "event_date",
      render: (value) => (
        <Text>{value ? handleDateFormatter(value) : "-"}</Text>
      ),
    },
    {
      title: "Event Amount",
      dataIndex: "event_amount",
      render: (value) => (
        <Text>{value ? `Rs. ${formatAmount(value)}` : 0}</Text>
      ),
    },
    {
      title: "Overdue Amount",
      dataIndex: "overdue_amount",
      render: (value) =>
        value == null ? <Text>-</Text> : <Text>Rs. {formatAmount(value)}</Text>,
    },
    {
      title: "DPD",
      dataIndex: "dpd",
      render: (value) =>
        value == null ? <Text>-</Text> : <Text>{formatAmount(value)}</Text>,
    },
  ];

  useEffect(() => {
    setData(responseData);
  }, [responseData]);

  const dataSource = Array.isArray(data)
    ? data.map((d, i) => ({ key: i + 1, ...d }))
    : [];
  return (
    <Table
      virtual
      bordered
      loading={loading}
      className={Style.customTable}
      columns={columns}
      dataSource={dataSource}
      scroll={{ x: 1500, y: "auto" }}
      pagination={false}
    />
  );
}

CommunicationResponseTable.propTypes={
  responseData: PropTypes.array,
  loading: PropTypes.bool
}
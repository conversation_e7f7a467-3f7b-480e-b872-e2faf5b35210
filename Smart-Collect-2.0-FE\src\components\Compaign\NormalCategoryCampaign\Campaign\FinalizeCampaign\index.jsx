import {
  Flex,
  Input,
  Typography,
  Segmented,
  Row,
  Col,
  InputNumber,
} from "antd";
import React from "react";
import Style from "./_style.module.scss";
import {
  CAMPAIGN_DATA_PROP_TYPES,
  handleDateFormatter,
} from "../../../../../constant";
import PropTypes from "prop-types";

const { Text } = Typography;
export function CampaignsComponent({
  campaignData,
  setCampaignData,
  commFlowData,
  handleChangeTab,
}) {
  const handleChangeCampaignName = (value) => {
    setCampaignData((prevData) => ({
      ...prevData,
      campaign: { ...prevData.campaign, name: value },
    }));
  };
  const handleChangeCampaignPriority = (value) => {
    setCampaignData((prevData) => ({
      ...prevData,
      campaign: { ...prevData.campaign, priority: value },
    }));
  };
  const comparisonLabelMap = {
    range: "Range:",
    list: "List:",
  };
  const renderInputByComparison = (category) => {
    if (category.comparison === "range") {
      return (
        <>
          <Input
            className={Style.custom_input_small}
            value={category.rangeFrom}
            readOnly
            disabled
          />
          <Input
            className={Style.custom_input_small}
            value={category.rangeTo}
            readOnly
            disabled
          />
        </>
      );
    }

    if (category.comparison === "list") {
      return (
        <Input
          className={Style.custom_input_small}
          value={category.list || ""}
          readOnly
          disabled
        />
      );
    }

    return (
      <Input
        className={Style.custom_input_small}
        value={
          category.parameterName === "DateOfBirth"
            ? handleDateFormatter(category.value)
            : category.value || ""
        }
        readOnly
        disabled
      />
    );
  };

  return (
    <Flex className={Style.campaign_container} vertical>
      {/* Campaign Name input box */}
      <Flex gap={10} direction="vertical" className={Style.input_container}>
        <Text className={Style.text}>Campaign Name:</Text>
        <Input
          className={Style.custom_input}
          placeholder="Enter"
          value={campaignData?.campaign?.name || ""}
          onChange={(e) => handleChangeCampaignName(e.target.value)}
        />
      </Flex>

      {/* Campaign Priority input box */}
      <Flex direction="vertical" className={Style.input_container}>
        <Text className={Style.text}>Campaign Priority:</Text>
        <InputNumber
          className={`${Style.custom_input} ${Style.priority_input}`}
          value={campaignData?.campaign.priority || 0}
          onChange={(value) => {
            handleChangeCampaignPriority(value);
          }}
          controls={false}
        />
      </Flex>

      {/* All filters */}
      <Flex className={Style.container} vertical>
        {/* Category Section */}
        <Flex direction="horizontal" align="middle" className={Style.section}>
          <Text className={Style.text}>Category</Text>
        </Flex>
        {campaignData?.categories?.filters?.map((category, index) => (
          <Flex
            direction="horizontal"
            gap={10}
            className={Style.row}
            key={`${index}-${category.parameterName}`}
          >
            <Flex direction="vertical" className={Style.input_container_small}>
              <Text className={Style.text}>Parameter Name:</Text>
              <Input
                className={Style.custom_input_small}
                value={
                  category?.parameterName?.replace(
                    /([a-z])([A-Z])/g,
                    "$1 $2"
                  ) || ""
                }
                readOnly
                disabled={true}
              />
            </Flex>
            <Flex direction="vertical" className={Style.input_container_small}>
              <Text className={Style.text}>Comparison:</Text>
              <Input
                className={Style.custom_input_small}
                value={category.comparison || ""}
                readOnly
                disabled={true}
              />
            </Flex>

            {!["in", "not in"].includes(category.comparison) && (
              <Flex
                direction="vertical"
                className={Style.input_container_small}
              >
                <Text className={Style.text}>Comparison Type:</Text>
                <Input
                  className={Style.custom_input_small}
                  value={category.comparisonType || ""}
                  readOnly
                  disabled={true}
                />
              </Flex>
            )}
            <Flex
              gap={10}
              direction="vertical"
              className={Style.input_container_small}
            >
              <Text className={Style.text}>
                {comparisonLabelMap[category.comparison] || "Value:"}
              </Text>
              {renderInputByComparison(category)}
            </Flex>
          </Flex>
        ))}

        {/* Divider */}
        <hr className={Style.divider} />

        {/* Communication Flow Section */}
        <Flex direction="horizontal" align="middle" className={Style.section}>
          <Text className={Style.text}>Communication Flow</Text>
        </Flex>
        {campaignData?.communicationFlows?.commFlow.map((flow, index) => (
          <div
            key={`${index}-${flow.applicantType}`}
            className={Style.row}
            style={{ display: "flex", flexDirection: "column", gap: "10px" }}
          >
            {/* Changed to div, flex column */}
            <Row gutter={[16, 16]} align="middle" style={{ display: "flex" }}>
              <Col xs={24} sm={10} style={{ flex: 1 }}>
                <Flex className={Style.input_container_small}>
                  {/* Keep Flex */}
                  <Text className={Style.text}>Applicant Type:</Text>
                  <Input
                    className={Style.custom_input_small}
                    value={flow.applicantType || ""}
                    disabled
                  />
                </Flex>
              </Col>
              <Col xs={24} sm={10} style={{ flex: 1 }}>
                <Flex className={Style.input_container_small}>
                  {" "}
                  {/* Keep Flex */}
                  <Text className={Style.text}>Communication Channel:</Text>
                  <Input
                    className={Style.custom_input_small}
                    value={flow.channelName || ""}
                    disabled
                  />
                </Flex>
              </Col>
              <Col xs={24} sm={10} style={{ flex: 1 }}>
                <Flex className={Style.input_container_small}>
                  {/* Keep Flex */}
                  <Text className={Style.text}>Based On:</Text>
                  <Input
                    className={Style.custom_input_small}
                    value={flow.basedOn || ""}
                    disabled
                  />
                </Flex>
              </Col>
              <Col xs={24} sm={4} style={{ flex: 1 }}>
                <Flex className={Style.input_container_small}>
                  {/* Keep Flex */}
                  <Text className={Style.text}>Days:</Text>
                  <Input
                    className={Style.custom_input_small}
                    value={flow.dayCount || 0}
                    disabled
                  />
                </Flex>
              </Col>
              <Col xs={24} sm={4} style={{ flex: 1 }}>
                <Flex className={Style.input_container_small}>
                  {/* Keep Flex */}
                  <Segmented
                    value={flow?.isAfter ? "After" : "Before"}
                    options={["After", "Before"]}
                    disabled
                  />
                </Flex>
              </Col>
            </Row>
          </div>
        ))}

        {/* Divider */}
        <hr className={Style.divider} />

        {/* Template Section */}
        <Flex direction="horizontal" align="middle" className={Style.section}>
          <Text className={Style.text}>Template</Text>
        </Flex>
        {campaignData?.templates?.map((template, index) => {
          // Find the matching communication flow
          const matchedFlow = commFlowData.find(
            (flow) => flow.CommFlowID === template.communicationType
          );

          // Construct the value based on match
          const displayValue = matchedFlow
            ? `${matchedFlow.CommFlowID}-${matchedFlow.CommunicationType}-${
                matchedFlow.BeforeAfter || "--"
              }`
            : template.communicationType || "";

          return (
            <Flex
              key={`${index}-${template.name}`}
              direction="horizontal"
              gap={10}
              className={Style.row}
            >
              <Flex
                direction="vertical"
                className={Style.input_container_small}
              >
                <Text className={Style.text}>Communication Type:</Text>
                <Input
                  className={Style.custom_input_small}
                  value={displayValue}
                  disabled
                />
              </Flex>
              <Flex
                direction="vertical"
                className={Style.input_container_small}
              >
                <Text className={Style.text}>Selected Template:</Text>
                <Input
                  className={Style.custom_input_small}
                  value={`${template.name}` || "NA"}
                  disabled
                />
              </Flex>
            </Flex>
          );
        })}
      </Flex>
    </Flex>
  );
}

CampaignsComponent.propTypes = {
  campaignData: CAMPAIGN_DATA_PROP_TYPES,
  setCampaignData: PropTypes.func,
  commFlowData: PropTypes.array,
  handleChangeTab: PropTypes.func,
};

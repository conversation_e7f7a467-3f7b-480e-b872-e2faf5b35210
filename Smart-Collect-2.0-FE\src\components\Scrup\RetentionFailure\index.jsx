import { Col, Flex, message, Row, Segmented, Typography } from 'antd'
import React, { useEffect, useState } from 'react'
import { ScrubTable } from './ScrubTable';
import AllCards from './AllCards';
import { Bar<PERSON><PERSON> } from './BarChart';
import { Account<PERSON>hart } from './AccountChart';
import Style from "./_style.module.scss";
import { AXIOS } from '../../../apis/ho-Instance';
import ApplicationLoader from '../../ApplicationLoader';

const {Text, Title} = Typography;

export function RetentionFailure() {
  const USER_DETAILS = JSON.parse(localStorage.getItem('user'));
  const [stateView, setStateView]= useState("Graph");
  const [selectedRetentionCard, setSelectedRetentionCard] = useState("active_state_data");
  const [retentionData, setRetentionData] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [selectedState, setSelectedState] = useState(null);
  const [stateTableData, setStateTableData] = useState([]);
  const [tableLevel , setTableLevel ] = useState("region");
  
  const retentionCardTitleMapping ={
    active_state_data: {
      title: "Active in XYZ Bank and Loan in other Banks",
      dataKeyPrefix: "active_state_data",
      prefix:"active",
    },
    inactive_state_data: {
      title: "Inactive in XYZ Bank and New Loan in Other Banks",
      dataKeyPrefix: "inactive_state_data",
      prefix:"inactive"
    }
  }

  // Fetch retention data
  const handleGetRetentionData = async()=>{
    const payload ={
      State_id: "all",  
      Region_id: "all",    
      Branch_id: "all",          
      CollectionOfficerID: "all",  
      LoanType: "all",
      BankMstID : [USER_DETAILS?.BankMstID]
    }
    setIsLoading(true);
    try {
      const res = await AXIOS.post('retention_failure/',payload);
      if(res.status === 200){
        setRetentionData(res.data.final_response);
      }
    } catch (error) {
      console.log("Error in retention failure", error?.message);
    }finally{
      setIsLoading(false);
    }
  }

  // Update the retention card
  const handleUpdatedRetention = (type)=>{
    setSelectedRetentionCard(type);
    setSelectedState(null);
  }

  // Handle update table based on the selected state 
  const handleBarClick = ({selectedState, selectedRetentionCard}) => {
    setSelectedState(selectedState?.State_id);
    handleUpdateStateTable({
      stateId:selectedState?.State_id,
      regionId: "all",
      branchId: "all",
      tablewise:"State",
      activeInactive: selectedRetentionCard,
    })
  };

  // Update the table data 
  const handleUpdateStateTable = async({
    stateId ="all",
    regionId = "all",
    branchId = "all",
    tablewise ="State",
    activeInactive,
  })=>{
    const payload ={
      State_id: stateId === "all" ? "all" : [stateId],  
      Region_id: regionId ==="all"? "all" : [regionId],    
      Branch_id: branchId === "all"? "all" : [branchId],          
      tablewise : tablewise,
      active_inactive: retentionCardTitleMapping[activeInactive]?.prefix,
      BankMstID : [USER_DETAILS?.BankMstID],
      CollectionOfficerID: "all",  
      LoanType: "all",
    };
    try {
      const response  = await AXIOS.post('retention_failure_hierarchy/',payload);     
      if(response.status === 200 && response.data.final_response?.length){
        setStateTableData(response.data.final_response);
      }else{
        setStateTableData([]);
        setSelectedState(null);
        setTableLevel(null);
      }
    } catch (error) {
      console.log("Error in State table", error?.message);
      message.warning(`Something is wrong, try again!`)
    }
  }

  useEffect(()=>{
    handleGetRetentionData();
  },[]);

  useEffect(()=>{
    const key = `${retentionCardTitleMapping[selectedRetentionCard]?.dataKeyPrefix}`;
    const selectedData = retentionData?.[key];
    if (selectedData) {
      setStateTableData(selectedData);
    }
  },[selectedRetentionCard, retentionData]);
 return (
    isLoading
    ?<ApplicationLoader/>
    :<Flex  vertical gap={20}>
        {/* Account chart */}
        <Flex vertical gap={10} className={Style.chart_container}>
          {retentionData?.loan_cycle_id_graph?.length
            ?<>
             <Text className={Style.title}>Total Loans Cycle</Text>  
             <AccountChart data={retentionData?.loan_cycle_id_graph}/>
             </>
            :<div style={{height:"250px"}}>
              <Text className={Style.title} style={{textAlign:"center", display:"block"}}> No Data Found </Text>
            </div>
          }
        </Flex>
       
        {/*Cards  */}
        <AllCards 
          data={retentionData} 
          selectedRetentionCard={selectedRetentionCard} 
          handleUpdatedRetention={handleUpdatedRetention}/>
        
        {/* state, branch, region tables */}
        <Flex vertical gap={10} className={Style.table_container}>
          <Flex justify='space-between' align='center' wrap>
            <Title level={4} className={Style.title}>{retentionCardTitleMapping[selectedRetentionCard]?.title}</Title>  
            <Flex gap={10} align='center' wrap>
              <Segmented
                value={stateView}
                onChange={(value) => setStateView(value)}
                options={["Graph", "Table"]}
              />
            </Flex>
          </Flex>
          { stateView === "Graph" && 
            <Row gutter={[10,10]} justify='space-between'>
              <Col span={24} className={Style.chart_container}>
              {retentionData[selectedRetentionCard]?.length
              ?<>
                <Text className={Style.title}>Total Accounts by State</Text>  
                <BarChart 
                 data={retentionData[selectedRetentionCard]} 
                 selectedRetentionCard={selectedRetentionCard}
                 onBarClick={handleBarClick}/>
              </>
              :<div style={{height:"250px"}}>
                <Text className={Style.title} style={{textAlign:"center", display:"block"}}> No Data Found </Text>
              </div>
              }
              </Col>
            </Row>
          }
          <ScrubTable 
           isGraph={stateView === "Graph"} 
           tableData={stateTableData}
           selectedRetentionCard={selectedRetentionCard}
           selectedState={selectedState}
           tableLevel={tableLevel}
           setTableLevel={setTableLevel}
           setSelectedState={setSelectedState}
           handleUpdateStateTable={handleUpdateStateTable}/>
        </Flex>
      </Flex>
  )
}

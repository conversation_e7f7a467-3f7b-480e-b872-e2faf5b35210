import { Flex, Typography } from "antd";
import PropTypes from "prop-types";
import Style from "./_style.module.scss";
import React from "react";

const { Text } = Typography;

export function CommunicationCard({ icon, title, count, size = 28 }) {
  return (
    <Flex
      className={Style.card_container}
      align="center"
      justify="space-between"
    >
      {icon && (
        <div
          className={Style.img_container}
          style={{ width: `${size}px`, height: `${size}px` }}
        >
          <img src={icon} alt={title} />
        </div>
      )}
      <Flex vertical align="center">
        <Text className={Style.count}>{count}</Text>
        <Text className={Style.title}>{title}</Text>
      </Flex>
    </Flex>
  );
}

CommunicationCard.propTypes = {
  icon: PropTypes.any,
  title: PropTypes.string,
  count: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  size: PropTypes.number,
};

@import url("../../../index.css");

$light-blue: #E3F5F6;

.container{
    position: relative;

    .button_container{
        background-color: white;
        color:white;
        cursor: pointer;
        padding:0.5rem;
        width: 30px;
        display: flex;
        align-items: center;
        justify-content: center;

        input[type='image']{
            width: 20px;
            object-fit: contain;
        }
    }

    .modal{
        position: absolute;
        right:0;
        top:0;
        background-color:$light-blue;
        box-shadow: 4px 4px 4px 0px #00000040;
        width: 300px;
        padding: 0.4rem 1rem;
        z-index: 2;
        border-radius:10px;

        .text{
            text-align: center;
            color: var(--dark-blue);
            font-family: 'Kanit','Courier New', Courier, monospace;
            font-size: 16px;
            font-weight: bold;
        }

        .fields{
            color: var(--dark-blue);
            font-family: 'Kanit','Courier New', Courier, monospace;
        }

        .apply{
            background-color: var(--dark-blue) !important;
            color: white !important;
            border:none;
            outline: none;
            box-shadow: none;
            font-family: 'Kanit','Courier New', Courier, monospace;
            padding: 1rem 2rem;
            margin: 1rem 0;
        }

        .clear{
            background-color: $light-blue !important;
            color: var(--dark-blue) !important;
            outline: none;
            box-shadow: none;
            font-family: 'Kanit','Courier New', Courier, monospace;
            border: 1px solid #0F2050 !important;
            padding: 1rem 2rem;
            margin: 1rem 0 ;
        }

        
        .custom_select{
            :global(.ant-select-selector){
            background-color: $light-blue;
            box-shadow: none !important;
            border:none;
            outline: none;
            border-bottom: 1px solid var(--dark-blue) !important;
            font-family: 'Kanit','Courier New', Courier, monospace;
        }     
        :global(.ant-select-selection-item){
            font-weight: 400;
            color: var(--dark-blue);
            font-family: 'Kanit','Courier New', Courier, monospace;
        }
        }

        .date_picker{
            border:none;
            outline: none;
            box-shadow: none;
            border-bottom: 1px solid var(--dark-blue);
            background-color: transparent;
            :global(.ant-picker-input>input){
                font-weight: 300;
                color:var(--dark-blue);
                font-family: 'Kanit','Courier New', Courier, monospace;

                &::placeholder{
                font-weight: 600;  
                }
                
            }
            &::placeholder{
                color: #0F20504A;
                font-weight: 600;
            }
        }

        .custom_input {
            background-color: $light-blue;
            caret-color: #407BFF;
            padding: 0.3rem 0.5rem;
            width: 100%; 
            text-transform: capitalize;
            border-bottom: 1px solid var(--dark-blue) !important;

            &:global(.ant-input-outlined) {
                border: none;
                box-shadow: none;
                color: var(--dark-blue);
                font-weight: 400;
                border-radius: 6px;
                font-family: 'Kanit','Courier New', Courier, monospace;
                
            }
        }
    }
}
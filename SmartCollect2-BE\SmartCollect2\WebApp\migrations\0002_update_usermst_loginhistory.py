# Generated by Django 5.1.5 on 2025-02-06 05:20

import django.contrib.auth.models
import django.contrib.auth.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("WebApp", "0001_initial"),
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserMst",
            fields=[
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "username",
                    models.CharField(
                        error_messages={
                            "unique": "A user with that username already exists."
                        },
                        help_text="Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.",
                        max_length=150,
                        unique=True,
                        validators=[
                            django.contrib.auth.validators.UnicodeUsernameValidator()
                        ],
                        verbose_name="username",
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="first name"
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="last name"
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        blank=True, max_length=254, verbose_name="email address"
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log into this admin site.",
                        verbose_name="staff status",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Designates whether this user should be treated as active. Unselect this instead of deleting accounts.",
                        verbose_name="active",
                    ),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="date joined"
                    ),
                ),
                ("UserID", models.AutoField(primary_key=True, serialize=False)),
                ("BranchID", models.CharField(blank=True, max_length=50, null=True)),
                ("Designation", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "MobileNumber",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                ("is_admin", models.BooleanField(default=False)),
                ("BankMaster", models.BooleanField(default=False)),
                ("UpdatedDate", models.DateTimeField(auto_now=True)),
                ("ExtraColumn_1", models.TextField(blank=True, null=True)),
                ("ExtraColumn_2", models.TextField(blank=True, null=True)),
                ("ExtraColumn_3", models.TextField(blank=True, null=True)),
                ("ExtraColumn_4", models.TextField(blank=True, null=True)),
                ("ExtraColumn_5", models.TextField(blank=True, null=True)),
                (
                    "BankMstID",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to="WebApp.bankmst",
                    ),
                ),
                (
                    "BranchMstID",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to="WebApp.branchmst",
                    ),
                ),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True, related_name="usermst_groups", to="auth.group"
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        related_name="usermst_permissions",
                        to="auth.permission",
                    ),
                ),
            ],
            options={
                "db_table": "UserMst",
                "ordering": ["BankMstID", "BranchMstID", "UserID"],
                "managed": True,
            },
            managers=[
                ("objects", django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name="LoginHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("ip", models.GenericIPAddressField(db_index=True)),
                ("ip_info", models.JSONField(default=dict)),
                ("city", models.CharField(blank=True, max_length=80)),
                ("region", models.CharField(blank=True, max_length=80)),
                ("region_code", models.CharField(blank=True, max_length=10)),
                ("country_code", models.CharField(blank=True, max_length=2)),
                ("country_name", models.CharField(blank=True, max_length=80)),
                ("currency", models.CharField(blank=True, max_length=5)),
                ("user_agent", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "LoginHistory",
                "ordering": ["-created_at"],
                "managed": True,
            },
        ),
    ]

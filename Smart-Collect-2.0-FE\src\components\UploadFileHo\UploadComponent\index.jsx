import React, { useState, useCallback, useEffect } from 'react';
import UPLOAD_IMG from '../../../assets/Images/upload.png';
import { Button } from 'antd';
import Style from './_style.module.scss';
import PropTypes from 'prop-types';

export function UploadComponent({onFileUpload, isUpload=false, acceptFileTypes=".csv, .xlsx, .xls, .json, .txt"}) {
  const [isDragging, setIsDragging] = useState(false);
  const [files, setFiles] = useState([]);
    
  const handleDragEnter = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);
    
  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    
  }, []);
    
  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);
    
  const handleDrop = useCallback(
    (e) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(false);
    
      const droppedFiles = [...e.dataTransfer.files];
      setFiles(droppedFiles);
      onFileUpload(droppedFiles); // Pass files to parent component
  },[onFileUpload]);
    
  const handleFileSelect = useCallback(
    (e) => {
      const selectedFiles = [...e.target.files];
      setFiles(selectedFiles);
      onFileUpload(selectedFiles); // Pass files to parent component
  },[onFileUpload]);

  useEffect(() => { 
    // Reset files when isUpload changes
    if (isUpload) {
      setFiles([]);
    }
  }, [isUpload]);
  return (
    <div className={Style.container}>
      <button
        className={`${Style.uploadZone} ${isDragging ? Style.dragging : ''}`}
        onDragEnter={handleDragEnter}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div className={Style.content}>
            <div className={Style.upload_img}>
                <img src={UPLOAD_IMG} alt='upload'/>
            </div>
          <label className={Style.text}>
            <span>Drag & Drop File or </span>
            <Button type="link" className={Style.browseButton}>Browse</Button>
            <input
                type="file"
                accept={acceptFileTypes}
                className={Style.fileInput}
                onChange={handleFileSelect}
                multiple
              />
          </label>

          {files.length > 0 && (
              <div className={Style.fileList}>
                <ul>
                {files.map((file, index) => (
                  <li key={`${index}-${file.name}`} className={Style.text}>{file.name}</li>
                  ))}
                </ul>
              </div>
            )}
        </div>
      </button>
    </div>
  )
}

UploadComponent.propTypes={
  onFileUpload: PropTypes.func, 
  isUpload: PropTypes.bool, 
  acceptFileTypes: PropTypes.string
}
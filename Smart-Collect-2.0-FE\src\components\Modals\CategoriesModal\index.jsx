import { Button, Flex, Input, message, Modal, Select } from "antd";
import React, { useEffect, useState } from "react";
import Style from "./_style.module.scss";
import { SMART_COLLECT_MENU_IDS } from "../../../constant";
import { useNavigate } from "react-router";
import { AXIOS } from "../../../apis/ho-Instance";
import PropTypes from "prop-types";

export function CategoriesModal({ open, handleOk, handleCancel }) {
  const navigate = useNavigate();
  const [category, setCategory] = useState(null);
  const [selectedLanguage, setSelectedLanguage] = useState(null);
  const [languageOptions, setLanguageOptions] = useState([]);
  const [campaignTypeOptions, setCampaignTypeOptions] = useState([]);
  const [campaignName, setCampaignName] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const [flowType, setFlowType] = useState(null);
  const [afterOption, setAfterOption] = useState(null);
  const [daysOption, setDaysOption] = useState(null);

  // Fetch all languages
  const handleFetchLanguage = async () => {
    try {
      const response = await AXIOS.get("v1/languages/");
      if (response.status === 200) {
        const updatesLanguages = response.data
          ?.filter(({ IsActive }) => IsActive)
          .map(({ Language, LngMstID }) => ({
            label: Language,
            value: LngMstID,
          }));
        setLanguageOptions(updatesLanguages);
      }
    } catch (error) {
      console.log("Error in Languages", error);
    }
  };

  // Fetch all campaign types
  const handleFetchCampaignTypes = async () => {
    try {
      const response = await AXIOS.get("v1/getCampaignCategoryOptions/");
      if (response.status === 200) {
        const updatedCampaignTypes = response.data
          ?.filter(({ IsActive }) => IsActive)
          .map(({ TypeName, DisplayName }) => ({
            label: DisplayName,
            value: TypeName,
          }));
        setCampaignTypeOptions(updatedCampaignTypes);
      }
    } catch (error) {
      console.log("Error in Campaign Types", error);
    }
  };

  const handleCategoriesChange = (key) => {
    setCategory(key);
    if (key === "calling") {
      setSelectedLanguage(null);
    }
  };

  const handleNext = async () => {
    setIsLoading(true);
    try {
      // Prepare payload based on campaign type
      const payload = {
        Name: campaignName,
        CampaignType: category,
      };

      // Only add language for normal campaigns
      if (category === "normal") {
        payload.Language = selectedLanguage;
      }

      const response = await AXIOS.post("v1/campaign/createcampaign", payload);

      if (response.status === 200 || response.status === 201) {
        const { message, campaign_id } = response.data;
        // Check if campaignId exists in localStorage
        const existingCampaignId = localStorage.getItem("campaignId");
        const existingCampaignFlowID = localStorage.getItem("CommFlowMstID");

        if (existingCampaignId) {
          localStorage.removeItem("campaignId");
          localStorage.removeItem("campaignLanguage");
        }
        localStorage.setItem("campaignId", campaign_id?.CampaignMstID);

        if (existingCampaignFlowID) {
          localStorage.removeItem("CommFlowMstID");
        }
        if (campaign_id?.Language)
          localStorage.setItem("campaignLanguage", campaign_id?.Language);
        messageApi.open(message);
        if (category === "normal") {
          navigate(`${SMART_COLLECT_MENU_IDS.NORMAL}/${campaignName}`);
        } else if (category === "calling") {
          navigate(`${SMART_COLLECT_MENU_IDS.CALLING}/${campaignName}`);
        }
        handleOk();
      }
    } catch (error) {
      console.log("Error in create campaign", error?.message);
      message.error("Campaign is not created. Please try again!");
    } finally {
      setIsLoading(false);
    }
  };

  const handleModalClose = () => {
    setCategory(null);
    setCampaignName("");
    setIsLoading(false);
    handleCancel();
  };

  useEffect(() => {
    handleFetchLanguage();
    handleFetchCampaignTypes();
  }, []);

  return (
    <>
      {contextHolder}
      <Modal
        title="Create Campaign"
        centered
        open={open}
        onOk={handleOk}
        onCancel={handleModalClose}
        footer={null}
        className={Style.categories_modal}
      >
        <Flex vertical justify="space-between" gap={20}>
          <Flex gap={10} vertical>
            <Input
              className={Style.custom_input}
              value={campaignName}
              placeholder="Enter campaign name..."
              onChange={(e) => setCampaignName(e.target.value)}
            />

            <Select
              style={{ width: "100%", marginBlockEnd: "0" }}
              placeholder={"Select campaign type"}
              onChange={handleCategoriesChange}
              options={campaignTypeOptions}
              allowClear
              className={Style.custom_select}
              value={category}
            />

            {/* Language select for normal campaign type */}
            {category === "normal" && (
              <Select
                allowClear
                style={{ width: "100%" }}
                placeholder={"Select language"}
                onChange={setSelectedLanguage}
                options={languageOptions}
                className={Style.custom_select}
                value={selectedLanguage}
              />
            )}
          </Flex>

          <Flex justify="center" align="center" style={{ width: "100%" }}>
            <Button
              className={Style.next_button}
              onClick={handleNext}
              disabled={
                !category ||
                campaignName === "" ||
                (category === "normal" && !selectedLanguage)
              }
              loading={isLoading}
            >
              Next
            </Button>
          </Flex>
        </Flex>
      </Modal>
    </>
  );
}

// Define the props
CategoriesModal.propTypes = {
  open: PropTypes.bool,
  handleOk: PropTypes.func,
  handleCancel: PropTypes.func,
};

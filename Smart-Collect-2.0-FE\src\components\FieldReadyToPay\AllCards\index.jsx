import { Col, Row } from 'antd'
import React from 'react'
import { Card } from './Card'
import PropTypes from 'prop-types'
import {formatAmount, formatDigits} from "../../../constant";
export default function AllCards({cardData}) {
  return (
    <Row gutter={[50,30]} align={'stretch'}>
        {
            cardData?.map((card, index)=>(
               <Col md={8} xs={24} style={{alignSelf:"stretch"}}key={`${index}-${card.title}`}>
                <Card 
                title={card?.title} 
                loanCount={formatDigits(card?.loansCount ?? 0)} 
                amount={formatAmount(card?.amount ?? 0)}
                image={card?.image}/>
               </Col>
            ))
        }
    </Row>
  )
}
AllCards.propTypes={
  cardData: PropTypes.array
}
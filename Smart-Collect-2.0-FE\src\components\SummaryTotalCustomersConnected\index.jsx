import { Flex, message, Table, Typography } from "antd";
import React, { useState, useEffect } from "react";
import AppHeader from "../AppHeader";
import {
  ALL_CACHE_NAMES,
  formatAmount,
  handleDateFormatter,
  handleDownloadTable,
  SUMMARY_FILTERS,
} from "../../constant";
import DOWNLOAD_IMG from "../../assets/Images/download.svg";
import Style from "./_style.module.scss";
import { AXIOS } from "../../apis/ho-Instance";
import ApplicationLoader from "../ApplicationLoader";
import HISTORY_IMG from "../../assets/Images/history_icon.png";
import { ConversionHistory } from "../Modals/ConversionHistory";
import {
  getCacheKey,
  getFromCache,
  storeToCache,
} from "../../utils/cacheHelper";

const { Text } = Typography;
const CACHE_NAME = ALL_CACHE_NAMES.CAMPAIGN_SUMMARY_DASHBOARD;

export default function SummaryTotalCustomersConnected() {
  const [connectedData, setConnectedData] = useState({
    results: [],
    pagination: [],
  });
  const [isLoading, setIsLoading] = useState(false);
  const storedFilter = localStorage.getItem(SUMMARY_FILTERS.summaryFilter);
  const storedCamp = localStorage.getItem(SUMMARY_FILTERS.selectedCampaignId);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [modalState, setModalState] = useState({
    history: { isOpen: false, data: null },
  });

  const openModal = ({ type, data }) => {
    setModalState((prev) => ({
      ...prev,
      [type]: { isOpen: true, data },
    }));
  };

  const closeModal = (type) => {
    setModalState((prev) => ({
      ...prev,
      [type]: { isOpen: false, data: null },
    }));
  };

  const handleFetchCampsAndBranchIds = async () => {
    try {
      const storedCampaigns = localStorage.getItem(
        SUMMARY_FILTERS.summaryCampaignIds
      );
      const storedBranches = localStorage.getItem(
        SUMMARY_FILTERS.summaryBranchIds
      );

      const campaignIds = storedCampaigns ? JSON.parse(storedCampaigns) : [];
      const branchIds = storedBranches ? JSON.parse(storedBranches) : [];

      return { campaignIds, branchIds };
    } catch (error) {
      console.error(
        "Error reading campaign/branch IDs from localStorage",
        error
      );
      return { campaignIds: [], branchIds: [] };
    }
  };

  // Fetch API data and set state
  const getTotalCustomersConnectedData = async ({
    page = 1,
    page_size = 10,
    fromDate,
    toDate,
    BranchName,
    disbursementID,
    loanTypes,
    customerNames,
    campaignMstIds,
  } = {}) => {
    setIsLoading(true);
    const { campaignIds, branchIds } = await handleFetchCampsAndBranchIds();

    const params = {
      page,
      page_size,
      fromDate,
      toDate,
      BranchName: BranchName?.length ? BranchName : branchIds,
      dpdfrom: 0,
      dpdto: 10000,
      DisbursementID: disbursementID,
      LoanType: loanTypes,
      CustomerName: customerNames,
      campaign_mstids: campaignMstIds ? [campaignMstIds] : campaignIds,
    };
    const cacheKey = getCacheKey({
      endpoint: "campaign-total-customers-connected/",
      params: params,
    });
    // Check cache
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setConnectedData({
        results: cachedData.results,
        pagination: cachedData.pagination,
      });
      setIsLoading(false);
    }
    try {
      const response = await AXIOS.get("campaign-total-customers-connected/", {
        params: params,
      });
      if (response.status === 200 && response.data.results) {
        const { results, pagination } = response.data;
        // Store data in cache
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
        setConnectedData({ results, pagination });
      } else {
        setConnectedData({ results: [], pagination: {} });
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Apply filter and fetch data
  const applyFilters = async ({
    fromDate,
    toDate,
    BranchName,
    disbursementID,
    customerNames,
    loanTypes,
  }) => {
    await getTotalCustomersConnectedData({
      fromDate: fromDate,
      toDate: toDate,
      BranchName,
      disbursementID,
      loanTypes,
      customerNames,
      campaignMstIds: parseInt(storedCamp),
    });
  };

  // Handle clear filters
  const ClearFilters = () => {
    getTotalCustomersConnectedData({ page: 1 });
    localStorage.removeItem(SUMMARY_FILTERS.summaryFilter);
  };

  // Handle downloading
  const handleDownload = async () => {
    if (connectedData.results.length === 0 || !connectedData.results) {
      return message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: "Summary-Total-Customer-Connected",
        worksheetName: "Summary-Total-Customer-Connected",
        tableData: connectedData.results,
      });
    }
  };

  // On mount, check localStorage for saved filters
  useEffect(() => {
    const {
      fromDate,
      toDate,
      BranchName,
      disbursementID,
      loanTypes,
      customerNames,
    } = storedFilter
      ? JSON.parse(storedFilter)
      : {
          fromDate: null,
          toDate: null,
          BranchName: [],
          disbursementID: "",
          loanTypes: "",
          customerNames: "",
        };

    const parsedCamp = storedCamp ? parseInt(storedCamp) : null;
    getTotalCustomersConnectedData({
      page: 1,
      fromDate,
      toDate,
      BranchName: BranchName,
      disbursementID,
      loanTypes,
      customerNames,
      campaignMstIds: parsedCamp, // Use storedCamp or fallback to all
    });
  }, []);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      sorter: (a, b) => (a?.key ?? 0) - (b?.key ?? 0),
      render: (_, { key }) => <Text>{key ?? "-"}.</Text>,
    },
    {
      title: "Customer Id",
      dataIndex: "CustomerID",
      sorter: (a, b) => Number(a?.CustomerID ?? 0) - Number(b?.CustomerID ?? 0),
      render: (CustomerId) => <Text>{CustomerId ?? "-"}</Text>,
    },
    {
      title: "Customer Name",
      dataIndex: "CustomerName",
      sorter: (a, b) =>
        String(a?.CustomerName ?? "").localeCompare(
          String(b?.CustomerName ?? "")
        ),
      render: (_, { CustomerName }) => (
        <Text className={Style.text}>{CustomerName ?? "-"}</Text>
      ),
    },
    {
      title: "Branch Name​",
      dataIndex: "BranchName",
      sorter: (a, b) =>
        String(a?.BranchName ?? "").localeCompare(String(b?.BranchName ?? "")),
      render: (value) => <Text>{value ?? "-"}</Text>,
    },
    {
      title: "Disbursement ID​",
      dataIndex: "DisbursementID",
      sorter: (a, b) =>
        String(a?.DisbursementID ?? "").localeCompare(
          String(b?.DisbursementID ?? "")
        ),
      render: (value) => <Text>{value ?? "-"}</Text>,
    },
    {
      title: "Loan Type​",
      dataIndex: "LoanType",
      sorter: (a, b) =>
        String(a?.LoanType ?? "").localeCompare(String(b?.LoanType ?? "")),
      render: (value) => <Text>{value ?? "-"}</Text>,
    },
    {
      title: "Disbursement Date​",
      dataIndex: "DisbursementDate",
      sorter: (a, b) => {
        const dateA = a?.DisbursementDate
          ? new Date(a.DisbursementDate)
          : new Date(0);
        const dateB = b?.DisbursementDate
          ? new Date(b.DisbursementDate)
          : new Date(0);
        return dateA - dateB;
      },
      render: (value) => (
        <Text>{value ? handleDateFormatter(value) : "--"}</Text>
      ),
    },
    {
      title: "Disbursement Amount​",
      dataIndex: "DisbursementAmt",
      sorter: (a, b) =>
        Number(a?.DisbursementAmt ?? 0) - Number(b?.DisbursementAmt ?? 0),
      render: (value) => (
        <Text className={Style.blueText} style={{ whiteSpace: "nowrap" }}>
          {formatAmount(value ?? 0)}
        </Text>
      ),
    },
    {
      title: "EMI  Amount",
      dataIndex: "EMIAmount",
      sorter: (a, b) => Number(a?.EMIAmount ?? 0) - Number(b?.EMIAmount ?? 0),
      render: (value) => <Text>{formatAmount(value ?? 0)}</Text>,
    },
    {
      title: "Overdue   Amount",
      dataIndex: "OverDueAmt",
      sorter: (a, b) => Number(a?.OverDueAmt ?? 0) - Number(b?.OverDueAmt ?? 0),
      render: (value) => <Text>{formatAmount(value ?? 0)}</Text>,
    },
    {
      title: "DPD",
      dataIndex: "DPD",
      sorter: (a, b) => Number(a?.DPD ?? 0) - Number(b?.DPD ?? 0),
      render: (value) => <Text>{value ?? 0}</Text>,
    },
    {
      title: "History",
      key: "history",
      dataIndex: "history",
      fixed: isMobile ? null : "right",
      width: 92,
      render: (_, data) => (
        <button
          className={Style.icon_div}
          onClick={() => openModal({ type: "history", data })}
        >
          <div className={Style.icon_img}>
            <img src={HISTORY_IMG} alt="history" />
          </div>
        </button>
      ),
    },
  ];

  const dataSource = Array.isArray(connectedData.results)
    ? connectedData.results.map((data, i) => ({
        key:
          (connectedData.pagination.page - 1) *
            connectedData.pagination.page_size +
          i +
          1,
        ...data,
      }))
    : [];
  return (
    <Flex gap={20} vertical>
      <AppHeader
        title={"Total Customers Connected"}
        ClearFilters={ClearFilters}
        applyFilters={applyFilters}
        isDashboardOtherPages={true}
        pageId={"TotalLoansConnected"}
      />

      <Flex gap={20} vertical>
        <Flex justify="end">
          <button className={Style.download_button} onClick={handleDownload}>
            <img src={DOWNLOAD_IMG} alt="download-button" />
          </button>
        </Flex>

        {isLoading ? (
          <ApplicationLoader />
        ) : (
          <Table
            bordered
            virtual
            className={Style.CustomTable}
            columns={columns}
            dataSource={dataSource}
            scroll={{
              x: 2000,
              y: 360,
            }}
            pagination={{
              current: connectedData.pagination.page,
              pageSize: connectedData.pagination.page_size,
              total: connectedData.pagination.total_entries,
              showSizeChanger: false,
              onChange: (page) => {
                getTotalCustomersConnectedData({ page });
              },
            }}
          />
        )}
      </Flex>

      <ConversionHistory
        customerName={modalState.history.data?.CustomerName}
        loanMstId={modalState.history.data?.LoanMstID}
        modalStatus={modalState.history.isOpen}
        handleCancel={() => closeModal("history")}
      />
    </Flex>
  );
}

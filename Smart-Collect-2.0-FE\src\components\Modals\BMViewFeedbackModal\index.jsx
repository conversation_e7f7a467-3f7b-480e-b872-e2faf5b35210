import { Mo<PERSON>, Ta<PERSON>, Typography, Flex, Spin } from "antd";
import React, { useEffect, useState } from "react";
import FEEDBACK_IMG from "../../../assets/Images/fluent-mdl2_feedback.png";
import Style from "./_feedback.module.scss";
import { ReadyToPay } from "./ReadyToPay";
import { RefusedToPay } from "./RefusedToPay";
import { Collection } from "./Collection";
import { Other } from "./Other";
import PropTypes from "prop-types";

const { Title } = Typography;

export function BMViewFeedbackModal({
  modalStatus = false,
  handleCancel,
  feedbackData
}) {
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);
  const [items, setItems] = useState([]);

  const handleOk = () => {
    setIsFeedbackModalOpen(false);
    handleCancel();
  };

  const handleCross = () => {
    setIsFeedbackModalOpen(false);
    handleCancel();
  };

  useEffect(()=>{
    setIsFeedbackModalOpen(modalStatus);
  },[modalStatus]);

  // Update tabs dynamically when latestFeedBack updates
  useEffect(() => {
    if (!feedbackData) return;

    const updatedItems = [
      feedbackData?.AgreedToPay 
      ?{
        key: "readyToPay",
        label: "Ready to Pay",
        children: (
          <ReadyToPay
            handleCross={handleCross}
            latestFeedBack={feedbackData}
          />
        ),
      }:null,
      feedbackData?.RefusedToPay ?{
        key: "refusedToPay",
        label: "Refused to Pay",
        children: (
          <RefusedToPay
            handleCross={handleCross}
            latestFeedBack={feedbackData}
          />
        ),
      }:null,
      feedbackData?.CollectionDate &&
      feedbackData?.CollectionAmount ?{
        key: "collection",
        label: "Collection",
        children: (
          <Collection
            handleCross={handleCross}
            latestFeedBack={feedbackData}
          />
        ),
      }:null,
      {
        key: "other",
        label: "Other",
        children: <Other latestFeedBack={feedbackData} />,
      },
    ].filter(Boolean);

    setItems(updatedItems);
  }, [feedbackData]);

  return (
    <Modal
      centered
      className={Style.feedback_modal}
      title={
        <Flex align="center" gap={5} justify="center">
          <div className={Style.icon_img}>
            <img src={FEEDBACK_IMG} alt="icon" />
          </div>
          <Title level={4} className={Style.feedback_title}>
            Feedback
          </Title>
        </Flex>
      }
      open={isFeedbackModalOpen}
      onOk={handleOk}
      onCancel={handleCross}
      footer={null}
    >
      <Flex vertical gap={10}>
        {items.length > 0 ? (
          <Tabs items={items} />
        ) : (
          <Spin tip={"Loading..."}></Spin>
        )}
      </Flex>
    </Modal>
  );
}

BMViewFeedbackModal.propTypes={
  modalStatus: PropTypes.bool,
  handleCancel: PropTypes.func,
  feedbackData: PropTypes.object
}
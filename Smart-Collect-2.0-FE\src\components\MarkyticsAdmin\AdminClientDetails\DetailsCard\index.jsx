import React from 'react'
import Style from "./_style.module.scss";
import { Flex, Typography } from 'antd';
import PropTypes from 'prop-types';

const {Text, Title} = Typography;

export function DetailsCard({title, amount, image}) {
  return (
    <Flex className={Style.card_container} vertical gap={20}>
        <Title level={3} className={Style.title}>{title}</Title>

        <Flex gap={3} justify='space-between' align='center'>
            <Title level={3} className={Style.desc}>{amount}</Title>
            <div className={Style.image_container}>
                <img src={image} alt={title}/>
            </div>       
        </Flex>
    </Flex>
  )
}

DetailsCard.propTypes={
  title: PropTypes.string, 
  amount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]), 
  image: PropTypes.string
}
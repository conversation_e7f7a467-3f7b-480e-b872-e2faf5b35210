# from SmartCollect2 import asgi
# from WebApp.views import GetBrokenPromise,DeliveryStatus
# # from clientonboarding.views import ClientSummary, status, collection_status_update, DeliveryStatus
# from apscheduler.schedulers.background import BackgroundScheduler
# from apscheduler.triggers.cron import CronTrigger
# from redis import Redis
# from fastapi import FastAPI
# import logging
# import sys
# from datetime import datetime, timedelta
# from Channels.views import WhatsAppEngine

# # formatters
# formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# # error log stream handler
# error_handler = logging.StreamHandler(sys.stderr)
# error_handler.setLevel(logging.NOTSET)
# error_handler.setFormatter(formatter)

# # logger
# logging.basicConfig(level=logging.DEBUG, handlers=[error_handler])

# app = FastAPI()

# rcache = Redis(host='localhost', port=6379, db=0, decode_responses=True)

# def getbrokenpromisestatus():
#     logging.info("Starting WhatsApp Engine at {}".format(datetime.now()))
#     GetBrokenPromise._generate_and_send_broken_promise_email()

# def sendDeliveryStatusMail():
#     logging.info("Sending Delivery Status Email at {}".format(datetime.now()))
#     DeliveryStatus().send_DeliveryStatus_email()
    

# def whenginestart():
#     logging.info("Starting WhatsApp Engine at {}".format(datetime.now()))
#     print(">>> WhatsApp Engine triggered at", datetime.now())
#     WhatsAppEngine().wh_engine()

# def scheduler():
#     background_scheduler = BackgroundScheduler()
#     # trigger every hour from 10Am to 7PM
#     print(">>> WhatsApp Engine triggered at", datetime.now())
#     logging.info("Starting WhatsApp Engine at {}".format(datetime.now()))
#     trigger1019 = CronTrigger(hour='10-22', minute='18', second='38', timezone='Asia/Kolkata')
#     # trigger at 7PM
#     # trigger1900 = CronTrigger(hour='19', minute='18', second='21', timezone='Asia/Kolkata')
#     background_scheduler.add_job(whenginestart, trigger1019, max_instances=1, misfire_grace_time=1)

    
#     # add jobs
#     # Run the WhatsApp Engine from 10AM to 7PM every hour
#     # background_scheduler.add_job(getbrokenpromisestatus, trigger1019, max_instances=1, misfire_grace_time=1)
#     # Send Delivery Status Report Everyday at 7PM
#     # background_scheduler.add_job(sendDeliveryStatusMail, trigger1900, max_instances=1, misfire_grace_time=1)
    
#     background_scheduler.start()

# scheduler()





from SmartCollect2 import asgi
from WebApp.views import GetBrokenPromise, DeliveryStatus
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR, EVENT_JOB_MISSED
from redis import Redis
from fastapi import FastAPI
import logging
import sys
from datetime import datetime, timedelta
from Channels.views import WhatsAppEngine, SendPaymentPromiseBUMessage
import atexit
import asyncio
 
# formatters
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
 
# error log stream handler
error_handler = logging.StreamHandler(sys.stderr)
error_handler.setLevel(logging.NOTSET)
error_handler.setFormatter(formatter)
 
# logger
logging.basicConfig(level=logging.DEBUG, handlers=[error_handler])
 
app = FastAPI()
rcache = Redis(host='localhost', port=6379, db=0, decode_responses=True)
 
# Global scheduler instance
scheduler_instance = None

def job_listener(event):
    """Listen to job events for debugging"""
    if event.exception:
        logging.error(f"Job {event.job_id} crashed: {event.exception}")
    else:
        logging.info(f"Job {event.job_id} executed successfully at {datetime.now()}")

def getbrokenpromisestatus():
    try:
        logging.info("Starting Broken Promise Status at {}".format(datetime.now()))
        print(f">>> Broken Promise job triggered at {datetime.now()}")
        GetBrokenPromise._generate_and_send_broken_promise_email()
        logging.info("Broken Promise Status completed successfully")
    except Exception as e:
        logging.error(f"Error in getbrokenpromisestatus: {str(e)}")
        raise

def sendDeliveryStatusMail():
    try:
        logging.info("Sending Delivery Status Email at {}".format(datetime.now()))
        print(f">>> Delivery Status job triggered at {datetime.now()}")
        DeliveryStatus().send_DeliveryStatus_email()
        logging.info("Delivery Status Email sent successfully")
    except Exception as e:
        logging.error(f"Error in sendDeliveryStatusMail: {str(e)}")
        raise

def whenginestart():
    try:
        logging.info("Starting WhatsApp Engine at {}".format(datetime.now()))
        print(f">>> WhatsApp Engine triggered at {datetime.now()}")
        WhatsAppEngine().wh_engine()
        logging.info("WhatsApp Engine completed successfully")
    except Exception as e:
        logging.error(f"Error in whengin  estart: {str(e)}")
        raise

def test_job():
    """Simple test job to verify scheduler is working"""
    print(f">>> TEST JOB EXECUTED at {datetime.now()}")
    logging.info(f"Test job executed at {datetime.now()}")

def execute_communication_queue_procedures(bank_id, connection=None):
    """
    Execute post-processing procedures starting from communication queue
    
    Args:
        bank_id: Bank ID parameter
        connection: Optional database connection
    
    Returns:
        dict: Result with success status and details
    """
    from DB.db_manager import db_manager
    
    # Import the function from your existing SP code
    # We'll modify this to only run procedures starting from communication queue
    
    # Define procedures starting from communication queue
    procedures = [
        {
            'name': 'public.process_communication_queuev5',
            'description': 'Process communication queue'
        },
        {
            'name': 'public.insert_in_voicebot_queue2',
            'description': 'Insert in voicebot queue'
        }
    ]
    
    logger = logging.getLogger(__name__)
    logger.info(f"Starting communication queue procedures for bank_id: {bank_id}")
    
    execution_start_time = datetime.now()
    successful_procedures = 0
    executed_procedures = []
    
    # Use provided connection or get one from db_manager
    should_close_connection = False
    if connection is None:
        logger.info("Creating new database connection")
        try:
            engine = db_manager.get_engine()
            if hasattr(engine, 'raw_connection'):
                connection = engine.raw_connection()
            elif hasattr(engine, 'connect'):
                connection = engine.connect()
            else:
                connection = engine
            should_close_connection = True
        except Exception as e:
            logger.error(f"Failed to get database connection: {str(e)}")
            raise
    
    try:
        logger.info(f"Total procedures to execute: {len(procedures)}")
        
        for i, procedure in enumerate(procedures):
            logger.info(f"Step {i + 1}/{len(procedures)}: Starting {procedure['description']}")
            
            try:
                # Execute stored procedure
                start_time = datetime.now()
                
                if hasattr(connection, 'execute') and not hasattr(connection, 'fetchone'):
                    # SQLAlchemy style execution
                    result = connection.execute(f"SELECT {procedure['name']}(%s);", (bank_id,))
                    result_value = result.fetchone()
                else:
                    # Raw cursor execution
                    cursor = connection.cursor()
                    cursor.execute(f"SELECT {procedure['name']}(%s);", (bank_id,))
                    result_value = cursor.fetchone()
                
                # Commit the transaction
                if hasattr(connection, 'commit'):
                    connection.commit()
                elif hasattr(connection, 'connection') and hasattr(connection.connection, 'commit'):
                    connection.connection.commit()
                
                end_time = datetime.now()
                execution_time = end_time - start_time
                
                result_dict = {
                    'success': True,
                    'procedure': procedure['name'],
                    'description': procedure['description'],
                    'result': result_value[0] if result_value else None,
                    'execution_time': execution_time.total_seconds(),
                    'bank_id': bank_id
                }
                
                executed_procedures.append(result_dict)
                successful_procedures += 1
                
                logger.info(f"✓ Step {i + 1}/{len(procedures)} COMPLETED: {procedure['description']} in {execution_time.total_seconds():.2f}s")
                
            except Exception as e:
                # Rollback in case of error
                try:
                    if hasattr(connection, 'rollback'):
                        connection.rollback()
                    elif hasattr(connection, 'connection') and hasattr(connection.connection, 'rollback'):
                        connection.connection.rollback()
                except Exception as rollback_error:
                    logger.error(f"Failed to rollback transaction: {str(rollback_error)}")
                
                error_msg = f"Failed to execute {procedure['description']} for bank_id {bank_id}: {str(e)}"
                logger.error(error_msg)
                raise Exception(error_msg)
        
        execution_end_time = datetime.now()
        execution_time = execution_end_time - execution_start_time
        
        logger.info(f"All communication queue procedures completed successfully for bank_id: {bank_id}")
        logger.info(f"Total execution time: {execution_time}")
        
        return {
            'success': True,
            'bank_id': bank_id,
            'procedures_executed': successful_procedures,
            'total_procedures': len(procedures),
            'execution_time': execution_time.total_seconds(),
            'executed_procedures': executed_procedures
        }
        
    except Exception as e:
        execution_end_time = datetime.now()
        execution_time = execution_end_time - execution_start_time
        
        logger.error(f"Communication queue procedures failed for bank_id: {bank_id}")
        logger.error(f"Error: {str(e)}")
        logger.error(f"Successfully completed: {successful_procedures}/{len(procedures)} procedures")
        logger.error(f"Total execution time before failure: {execution_time}")
        
        return {
            'success': False,
            'bank_id': bank_id,
            'procedures_executed': successful_procedures,
            'total_procedures': len(procedures),
            'execution_time': execution_time.total_seconds(),
            'error': str(e),
            'executed_procedures': executed_procedures
        }
        
    finally:
        if should_close_connection and connection:
            logger.info("Closing database connection")
            try:
                if hasattr(connection, 'close'):
                    connection.close()
            except Exception as e:
                logger.error(f"Failed to close database connection: {str(e)}")

def send_batch_processing_email(results, bank_ids):
    """
    Send email notification for batch processing results
    
    Args:
        results: List of execution results for each bank
        bank_ids: List of bank IDs processed
    """
    try:
        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart
        from django.conf import settings
        
        # Count successful and failed banks
        successful_banks = [r for r in results if r.get('success', False)]
        failed_banks = [r for r in results if not r.get('success', False)]
        
        # Create email subject
        if len(failed_banks) == 0:
            subject = f"Batch Communication Queue Processing - ALL SUCCESSFUL ({len(successful_banks)} banks)"
        else:
            subject = f"Batch Communication Queue Processing - {len(failed_banks)} FAILED, {len(successful_banks)} SUCCESS"
        
        # Create email body
        body = f"""
<html>
<body>
<h2>Batch Communication Queue Processing Results</h2>
<p><strong>Execution Time:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
<p><strong>Total Banks Processed:</strong> {len(bank_ids)}</p>
<p><strong>Successful:</strong> {len(successful_banks)}</p>
<p><strong>Failed:</strong> {len(failed_banks)}</p>

<h3>Processing Results:</h3>
<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
    <thead style="background-color: #f2f2f2;">
        <tr>
            <th>Bank ID</th>
            <th>Status</th>
            <th>Procedures Executed</th>
            <th>Execution Time (s)</th>
            <th>Details</th>
        </tr>
    </thead>
    <tbody>
"""
        
        for result in results:
            bank_id = result.get('bank_id', 'N/A')
            status = "SUCCESS" if result.get('success', False) else "FAILED"
            procedures = f"{result.get('procedures_executed', 0)}/{result.get('total_procedures', 0)}"
            exec_time = f"{result.get('execution_time', 0):.2f}"
            details = result.get('error', 'All procedures completed successfully')
            
            status_color = "#28a745" if status == "SUCCESS" else "#dc3545"
            
            body += f"""
        <tr>
            <td>{bank_id}</td>
            <td style="color: {status_color}; font-weight: bold;">{status}</td>
            <td>{procedures}</td>
            <td>{exec_time}</td>
            <td>{details}</td>
        </tr>
            """
        
        body += """
    </tbody>
</table>
</body>
</html>
        """
        
        # Send email
        smtp_server = settings.EMAIL_HOST
        smtp_port = settings.EMAIL_PORT
        smtp_user = settings.EMAIL_HOST_USER
        smtp_password = settings.EMAIL_HOST_PASSWORD
        use_tls = settings.EMAIL_USE_TLS
        
        recipients = ["<EMAIL>", "<EMAIL>", "<EMAIL>", 
                     "<EMAIL>", "<EMAIL>", "<EMAIL>", 
                     "<EMAIL>", "<EMAIL>"]
        
        msg = MIMEMultipart()
        msg['From'] = smtp_user
        msg['To'] = ", ".join(recipients)
        msg['Subject'] = subject
        msg.attach(MIMEText(body, 'html'))
        
        server = smtplib.SMTP(smtp_server, smtp_port)
        if use_tls:
            server.starttls()
        server.login(smtp_user, smtp_password)
        server.sendmail(smtp_user, recipients, msg.as_string())
        server.quit()
        
        logging.info(f"Batch processing email sent successfully: {subject}")
        
    except Exception as e:
        logging.error(f"Failed to send batch processing email: {str(e)}")

def process_multiple_banks_communication_queue():
    """
    Process communication queue for multiple banks - runs at 8 AM daily
    """
    try:
        logging.info("Starting batch communication queue processing at {}".format(datetime.now()))
        print(f">>> Batch Communication Queue Processing triggered at {datetime.now()}")
        
        # Define the bank IDs you want to process
        # You can modify this list as needed
        bank_ids = [31,39]  # Add your bank IDs here
        
        results = []
        
        for bank_id in bank_ids:
            try:
                logging.info(f"Processing communication queue for bank_id: {bank_id}")
                result = execute_communication_queue_procedures(bank_id)
                results.append(result)
                
                if result['success']:
                    logging.info(f"Successfully processed bank_id: {bank_id}")
                else:
                    logging.error(f"Failed to process bank_id: {bank_id} - {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                logging.error(f"Exception while processing bank_id {bank_id}: {str(e)}")
                results.append({
                    'success': False,
                    'bank_id': bank_id,
                    'procedures_executed': 0,
                    'total_procedures': 4,
                    'execution_time': 0,
                    'error': str(e),
                    'executed_procedures': []
                })
        
        # Send email with results
        send_batch_processing_email(results, bank_ids)
        
        # Log summary
        successful_count = sum(1 for r in results if r.get('success', False))
        failed_count = len(results) - successful_count
        
        logging.info(f"Batch communication queue processing completed")
        logging.info(f"Total banks: {len(bank_ids)}, Successful: {successful_count}, Failed: {failed_count}")
        
    except Exception as e:
        logging.error(f"Error in process_multiple_banks_communication_queue: {str(e)}")
        raise

@app.on_event("startup")
async def startup_event():
    global scheduler_instance
    logging.info("Starting application and scheduler...")
    scheduler_instance = BackgroundScheduler(timezone='Asia/Kolkata')
    
    # Add event listeners for debugging
    scheduler_instance.add_listener(job_listener, EVENT_JOB_EXECUTED | EVENT_JOB_ERROR | EVENT_JOB_MISSED)
    
    # Add a test job that runs every minute for debugging
    test_trigger = CronTrigger(second='0', timezone='Asia/Kolkata')  # Every minute
    # scheduler_instance.add_job(test_job, test_trigger, id='test_job', max_instances=1)
    
    # Your original jobs
    trigger1019 = CronTrigger(second='0', timezone='Asia/Kolkata')
    # every day at 10AM
    trigger10 = CronTrigger(hour='10', timezone='Asia/Kolkata')
    scheduler_instance.add_job(whenginestart, trigger1019, id='whatsapp_engine', max_instances=1, misfire_grace_time=60)
    scheduler_instance.add_job(send_whatsapp_to_bu, trigger10, id='send_whatsapp_to_bu', max_instances=1, misfire_grace_time=60, next_run_time=datetime.now() + timedelta(seconds=60))
    
    # NEW: Add the 8 AM batch processing job
    trigger_8am = CronTrigger(hour='7', minute='30', second='0', timezone='Asia/Kolkata')
    scheduler_instance.add_job(
        process_multiple_banks_communication_queue, 
        trigger_8am, 
        id='batch_communication_queue_8am', 
        max_instances=1, 
        misfire_grace_time=300  # 5 minutes grace time
    )
    
    # Uncomment these when you want to enable them
    # scheduler_instance.add_job(getbrokenpromisestatus, trigger1019, id='broken_promise', max_instances=1, misfire_grace_time=60)
    # trigger1900 = CronTrigger(hour='19', minute='18', second='21', timezone='Asia/Kolkata')
    # scheduler_instance.add_job(sendDeliveryStatusMail, trigger1900, id='delivery_status', max_instances=1, misfire_grace_time=60)
    
    scheduler_instance.start()
    logging.info("Scheduler started successfully")
    
    # Print all scheduled jobs for debugging
    jobs = scheduler_instance.get_jobs()
    logging.info(f"Scheduled jobs: {[job.id for job in jobs]}")
    for job in jobs:
        logging.info(f"Job: {job.id}, Next run: {job.next_run_time}")

@app.on_event("shutdown")
async def shutdown_event():
    global scheduler_instance
    if scheduler_instance:
        logging.info("Shutting down scheduler...")
        scheduler_instance.shutdown()

# Add debugging endpoints
@app.get("/scheduler/status")
async def scheduler_status():
    """Check scheduler status"""
    if scheduler_instance:
        jobs = scheduler_instance.get_jobs()
        return {
            "running": scheduler_instance.running,
            "jobs": [
                {
                    "id": job.id,
                    "next_run_time": str(job.next_run_time),
                    "trigger": str(job.trigger)
                }
                for job in jobs
            ]
        }
    return {"error": "Scheduler not initialized"}

@app.get("/scheduler/logs")
async def get_recent_logs():
    """Get recent log entries (you might want to implement this with a log handler)"""
    return {"message": "Check your application logs for scheduler activity"}

@app.post("/test/whatsapp")
async def test_whatsapp_manually():
    """Manually trigger WhatsApp engine for testing"""
    try:
        whenginestart()
        return {"status": "success", "message": "WhatsApp engine triggered manually"}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@app.post("/sendwhatsapptobu")
def send_whatsapp_to_bu():
    """Manually trigger WhatsApp engine for BU testing"""
    try:
        obj = SendPaymentPromiseBUMessage()
        # send for status = 'Pending'
        result = obj.main(bankmstid=32, status='Pending', Designation='Branch Manager')
        logging.info("WhatsApp engine triggered for BU Messages Sent %s", result)

        # send for status = 'Collection Officer Delay'
        result = obj.main(bankmstid=32, status='Collection Officer Delay', Designation='Branch Manager')
        logging.info("WhatsApp engine triggered for BU Messages Sent %s", result)
        return {"status": "success", "message": "WhatsApp engine triggered for BU"}
    except Exception as e:
        return {"status": "error", "message": str(e)}

# NEW: Add manual trigger endpoint for batch processing
@app.post("/test/batch-communication-queue")
async def test_batch_communication_queue():
    """Manually trigger batch communication queue processing for testing"""
    try:
        process_multiple_banks_communication_queue()
        return {"status": "success", "message": "Batch communication queue processing triggered manually"}
    except Exception as e:
        return {"status": "error", "message": str(e)}

# NEW: Add endpoint to update bank IDs for batch processing
@app.post("/config/update-bank-ids")
async def update_bank_ids(bank_ids: list):
    """Update bank IDs for batch processing"""
    try:
        # You can store this in Redis or database
        # For now, we'll just log it
        logging.info(f"Bank IDs updated for batch processing: {bank_ids}")
        rcache.set("batch_processing_bank_ids", ",".join(map(str, bank_ids)))
        return {"status": "success", "message": f"Bank IDs updated: {bank_ids}"}
    except Exception as e:
        return {"status": "error", "message": str(e)}

# Clean shutdown
atexit.register(lambda: scheduler_instance.shutdown() if scheduler_instance else None)
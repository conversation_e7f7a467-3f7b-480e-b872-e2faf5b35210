# Generated by Django 5.1.5 on 2025-02-18 06:54

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Dialer",
            fields=[
                ("DialerID", models.AutoField(primary_key=True, serialize=False)),
                ("Call_ID", models.CharField(blank=True, max_length=250, null=True)),
                ("DateTime", models.DateTimeField(blank=True, null=True)),
                ("RecordingURL", models.TextField(blank=True, null=True)),
            ],
            options={
                "verbose_name": "Dialer History",
                "verbose_name_plural": "Dialer Histories",
                "db_table": "Dialer",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="WhatsApp_History",
            fields=[
                (
                    "WhatsAppHistoryID",
                    models.BigAutoField(primary_key=True, serialize=False),
                ),
                ("BranchCode", models.CharField(blank=True, max_length=50, null=True)),
                ("LoanType", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "CustomerCode",
                    models.Char<PERSON>ield(blank=True, max_length=50, null=True),
                ),
                (
                    "CustomerName",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "OverdueAmount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=15, null=True
                    ),
                ),
                ("Language", models.CharField(blank=True, max_length=100, null=True)),
                ("BankName", models.CharField(blank=True, max_length=100, null=True)),
                ("CreatedDate", models.DateField(blank=True, null=True)),
                (
                    "Identifier",
                    models.CharField(
                        blank=True, max_length=255, null=True, unique=True
                    ),
                ),
            ],
            options={
                "db_table": "WhatsApp_History",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="WhatsApp_Messages",
            fields=[
                (
                    "WhatsAppMessagesID",
                    models.BigAutoField(primary_key=True, serialize=False),
                ),
                ("MessageDate", models.DateField(blank=True, null=True)),
                ("WhatsAppQueueID", models.BigIntegerField(blank=True, null=True)),
                ("Message_ID", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "Assigned_to",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("Sender", models.CharField(blank=True, max_length=100, null=True)),
                ("Content", models.TextField(blank=True, null=True)),
                ("Status", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "ContactNumber",
                    models.CharField(blank=True, max_length=15, null=True),
                ),
                (
                    "CustomerNumber",
                    models.CharField(blank=True, max_length=15, null=True),
                ),
                ("Button", models.CharField(blank=True, max_length=255, null=True)),
                ("Template_ID", models.BigIntegerField(blank=True, null=True)),
                ("Identifier", models.CharField(blank=True, max_length=255, null=True)),
                ("Type", models.CharField(blank=True, max_length=50, null=True)),
            ],
            options={
                "db_table": "WhatsApp_Messages",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="WhatsApp_Queue",
            fields=[
                (
                    "WhatsAppQueueID",
                    models.BigAutoField(primary_key=True, serialize=False),
                ),
                ("CreatedDate", models.DateField(blank=True, null=True)),
                ("BranchCode", models.CharField(blank=True, max_length=50, null=True)),
                ("LoanType", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "CustomerCode",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "CustomerName",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "OverdueAmount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=15, null=True
                    ),
                ),
                ("Language", models.CharField(blank=True, max_length=100, null=True)),
                ("LngMstID", models.IntegerField(blank=True, null=True)),
                ("BankName", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "Extra_Column1",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "Extra_Column2",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "Extra_Column3",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "Extra_Column4",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "Extra_Column5",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
            ],
            options={
                "db_table": "WhatsApp_Queue",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="WhatsAppIntentMaster",
            fields=[
                (
                    "WhatsAppIntentID",
                    models.BigAutoField(primary_key=True, serialize=False),
                ),
                ("intent", models.TextField(blank=True, null=True)),
                (
                    "Extra_Column1",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "Extra_Column2",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "Extra_Column3",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "Extra_Column4",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "Extra_Column5",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
            ],
            options={
                "db_table": "WhatsAppIntentMaster",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="WhatsAppKeyMapping",
            fields=[
                (
                    "WhatsAppKeyMappingID",
                    models.BigAutoField(primary_key=True, serialize=False),
                ),
                ("CreatedDate", models.DateTimeField(blank=True, null=True)),
                ("AuthorizationKey", models.TextField(blank=True, null=True)),
                ("APIVersion", models.TextField(blank=True, null=True)),
                ("PhoneNumberID", models.TextField(blank=True, null=True)),
                ("Service", models.TextField(blank=True, null=True)),
            ],
            options={
                "db_table": "WhatsAppKeyMapping",
                "managed": False,
            },
        ),
    ]

import { Flex, message, Tooltip, Typography } from "antd";
import React, { useEffect, useId, useRef, useState } from "react";
import Style from "./_style.module.scss";
import RECENT_ICON from "../../assets/Images/suggest.svg";
import SUGGESTION_ICON from "../../assets/Images/recent.svg";
import AI_BACK_ICON from "../../assets/Images/ai-arrow.svg";
import SENDER_ICON from "../../assets/Images/sender-icon.svg";
import NEW_CHAT_ICON from "../../assets/Images/new-chat.svg";
import ATTACHED_ICON from "../../assets/Images/attach.svg";
import { HistoryChat } from "./HistoryChat";
import { AIChatSection } from "./AIChatSection";
import { useLocation, useNavigate } from "react-router";
import { SuggestionChat } from "./SuggestionChat";
import { AXIOS } from "../../apis/ho-Instance";
import { v4 as uuidv4 } from "uuid";

const { Text } = Typography;

export default function SmartCollectAI() {
  const userDetails = JSON.parse(localStorage.getItem("user"));
  const USER_ID = userDetails?.username;

  const id = useId();
  const navigate = useNavigate();
  const location = useLocation();
  const [messageData, setMessageData] = useState([]);
  const [historyData, setHistoryData] = useState({
    active: [],
    completed: [],
    lastActive: {},
  });
  const [text, setText] = useState("");
  const [conversationUuid, setConversationUuid] = useState(
    localStorage.getItem("conversationUuid") || null
  );
  const [language] = useState("english");
  const [webSocket, setWebSocket] = useState(null);
  const [isTyping, setIsTyping] = useState(false);
  const [isEditedMessage, setIsEditedMessage] = useState(false);
  const [lastMessageId, setLastMessageId] = useState(false);
  const [attachedFile, setAttachedFile] = useState(null);
  const fileInputRef = useRef(null);

  const handleGoBack = () => {
    if (location.key !== "default") {
      navigate(-1);
    } else {
      navigate("/");
    }
  };

  const handleInputChange = (e) => {
    setText(e.target.value);
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      if (e.shiftKey) {
        // Shift+Enter: Insert a new line
        e.preventDefault();
        setText((prevText) => prevText + "\n");
      } else {
        // Enter: Send message
        e.preventDefault();
        handleSendMessage();
      }
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file && (file.name.endsWith(".xlsx") || file.name.endsWith(".xls"))) {
      setAttachedFile(file);
    } else {
      message.error("Only Excel files (.xlsx, .xls) are allowed.");
      setAttachedFile(null);
    }
  };

  //Handle fetch history data
  const handleFetchHistoryData = async () => {
    try {
      const response = await AXIOS.get(`user-conversations/${USER_ID}/`);
      if (response.status === 200 && response.data.conversations) {
        setHistoryData({
          active: response.data.conversations.active,
          completed: response.data.conversations.completed,
          lastActive: response.data.conversations.last_active,
        });
      }
    } catch (error) {
      console.log("Error in history api", error);
    }
  };

  //Handle Restore the  conversation by UUID
  const handleRestoreConversation = (uuid) => {
    console.log(`Restoring conversation: ${uuid}`);

    if (!uuid) {
      // Handle case where null is passed (start new conversation)
      // handleNewConversation();
      return;
    }

    // If WebSocket is not connected, store the UUID to restore later
    if (!webSocket || webSocket.readyState !== WebSocket.OPEN) {
      console.log("WebSocket not connected, saving UUID for later restoration");
      setConversationUuid(uuid);
      localStorage.setItem("conversationUuid", uuid);
      return;
    }

    // Clear existing messages to prepare for restored conversation
    setMessageData([
      {
        text: "Restoring conversation...",
        sender: "system",
        timestamp: new Date().toISOString(),
      },
    ]);

    // Send restore message to WebSocket
    webSocket.send(
      JSON.stringify({
        type: "restore",
        conversation_uuid: uuid,
        user_id: USER_ID,
        language: language,
        designation: userDetails?.designation,
        BankMstID: userDetails?.BankMstID,
        ...(userDetails?.designation === "BM" && {
          BranchID: userDetails?.BranchID,
        }),
      })
    );

    // Update state and localStorage
    setConversationUuid(uuid);
    localStorage.setItem("conversationUuid", uuid);
  };

  // Version-2
  const handleSendMessage = () => {
    if (text.trim() === "") {
      message.warning("Please enter a message before sending.");
      return;
    }

    const messageId = Date.now().toString() + uuidv4();
    const timestamp = new Date().toISOString();

    const sendPayload = (fileData = null) => {
      const payload = {
        type: "message",
        message: text,
        user_id: USER_ID,
        designation: userDetails.designation,
        BankMstID: userDetails.BankMstID,
        conversation_uuid: conversationUuid,
        language,
        ...(userDetails?.designation === "BM" && {
          BranchID: userDetails?.BranchID,
        }),
      };

      if (fileData) {
        payload.file = fileData;
      }

      if (webSocket && webSocket.readyState === WebSocket.OPEN) {
        webSocket.send(JSON.stringify(payload));
      } else {
        setMessageData((prev) => [
          ...prev,
          {
            text: "Not Connected to server. Please try again.",
            sender: "system",
            isError: true,
            timestamp,
          },
        ]);
        handleConnectWebSocket();
      }

      setText("");
      setAttachedFile(null);
    };

    if (attachedFile) {
      const fileName = attachedFile.name?.toLowerCase();
      if (!fileName.endsWith(".xls") && !fileName.endsWith(".xlsx")) {
        message.warning("Only Excel files (.xls, .xlsx) are allowed.");
        return;
      }

      const reader = new FileReader();
      reader.onload = () => {
        // 👇 Add file name at top of text
        const combinedText = `📎 ${attachedFile.name}\n${text}`;
        setMessageData((prev) => [
          ...prev,
          {
            id: messageId,
            text: combinedText,
            sender: "user",
            timestamp,
            isEdit: true,
          },
        ]);

        if (webSocket && webSocket.readyState === WebSocket.OPEN) {
          webSocket.send(
            JSON.stringify({
              type: "file_upload",
              file_name: attachedFile.name,
              file_type: attachedFile.type,
              file_content: reader.result, // Contains data:application/... format
              conversation_uuid: conversationUuid,
            })
          );
        } else {
          setMessageData((prev) => [
            ...prev,
            {
              text: "Not connected to server. Please try again.",
              sender: "system",
              isError: true,
              timestamp,
            },
          ]);
          handleConnectWebSocket();
        }
        setText("");
        setAttachedFile(null);
      };
      reader.readAsDataURL(attachedFile);
    } else {
      // No file: just send text
      setMessageData((prev) => [
        ...prev,
        {
          id: messageId,
          text,
          sender: "user",
          timestamp,
          isEdit: true,
        },
      ]);
      sendPayload();
    }
  };

  // Handle editing a message
  const handleEditMessage = () => {
    if (!webSocket || webSocket.readyState !== WebSocket.OPEN) {
      console.log("WebSocket not connected, can't edit message");
      return;
    }

    // Update the message text locally first
    setMessageData((prev) => [
      ...prev,
      {
        id: lastMessageId,
        text,
        sender: "user",
        timestamp: new Date().toISOString(),
        isEdit: true,
      },
    ]);

    // Send edit message to WebSocket
    webSocket.send(
      JSON.stringify({
        type: "edit_message",
        message_id: lastMessageId,
        message: text,
        user_id: USER_ID,
        designation: userDetails.designation,
        BankMstID: userDetails.BankMstID,
        conversation_uuid: conversationUuid,
        ...(userDetails?.designation === "BM" && {
          BranchID: userDetails?.BranchID,
        }),
      })
    );

    // Empty the field
    setText("");
  };

  // Handle Edit Last Message
  const handleEditLastMessage = ({ oldMessage, index }) => {
    setText(oldMessage?.text);
    setLastMessageId(oldMessage?.id);
    setIsEditedMessage(true);

    // Remove all messages below the selected one, including the edited one
    setMessageData((prevMessages) => {
      // Remove the selected message & all below it
      const updatedMessages = prevMessages.slice(0, index);
      return updatedMessages.map((msg, idx) => ({
        ...msg,
        isEdit: false,
      }));
    });
  };

  // Handle Delete the conversion
  const handleDeleteConversion = (uuid) => {
    // Send delete request to WebSocket
    if (webSocket && webSocket.readyState === WebSocket.OPEN) {
      webSocket.send(
        JSON.stringify({
          type: "delete_conversation",
          conversation_uuid: uuid,
        })
      );

      // Optimistically remove from local state immediately
      setHistoryData((prev) => {
        const newActive = prev.active.filter((conv) => conv.uuid !== uuid);
        const newCompleted = prev.completed.filter(
          (conv) => conv.uuid !== uuid
        );
        const newLastActive =
          prev.lastActive.uuid === uuid ? {} : prev.lastActive;

        return {
          active: newActive,
          completed: newCompleted,
          lastActive: newLastActive,
        };
      });

      message.success("Conversion Deleted Successfully!");
    } else {
      console.log("Not connected to server. Please try again.");
    }
  };

  // Handle to add new chat
  const handleNewConversation = () => {
    // Reset conversation UUID
    setConversationUuid(null);
    localStorage.removeItem("conversationUuid");

    // Clear messages
    setMessageData([]);

    // Create new chat object
    const newChat = {
      uuid: `New Chat ${historyData.active.length + 1}`, // You can customize the title
      created_at: new Date().toISOString(),
      campaign_name: "New campaign",
    };

    // Update historyData's active chats
    setHistoryData((prev) => ({
      ...prev,
      active: [newChat, ...prev.active], // Add  chat in beginning
      lastActive: newChat,
    }));

    // If connected, send reset message
    if (webSocket && webSocket.readyState === WebSocket.OPEN) {
      webSocket.send(
        JSON.stringify({
          type: "reset",
          user_id: USER_ID,
          language: language,
          BankMstID: userDetails?.BankMstID,
          designation: userDetails?.designation,
          ...(userDetails?.designation === "BM" && {
            BranchID: userDetails?.BranchID,
          }),
        })
      );
    } else {
      // Try to reconnect with a fresh state
      handleConnectWebSocket();
    }
  };

  // Handle suggestion
  const handleSuggestion = (type) => {
    console.log("suggestion");
    // create new connection
    // send the type as initial message.
    // display the message
  };

  // Handle WebSocket messages
  const handleWebSocketMessage = (data) => {
    if (!data?.type) {
      console.warn("Invalid WebSocket message received:", data);
      return;
    }
    const timestamp = data.timestamp || new Date().toISOString();
    const newMessages = [];

    switch (data.type) {
      case "greeting":
        newMessages.push({
          text: data.message,
          sender: "ai",
          timestamp,
        });

        // Store conversation UUID if provided
        if (data.conversation_uuid) {
          setConversationUuid(data.conversation_uuid);
          localStorage.setItem("conversationUuid", data.conversation_uuid);
        }
        break;

      case "response":
        newMessages.push({
          text: data.message,
          csv_download_url: data.csv_download_url,
          sender: "ai",
          timestamp,
        });

        // Handle form completion if needed
        if (data.form_complete) {
          console.log("Form completed:", data.form_data);
        }
        break;

      case "error":
        newMessages.push({
          text: data.message,
          sender: "ai",
          isError: true,
          timestamp,
        });
        break;

      case "typing":
        setIsTyping(data.is_typing);
        break;

      case "restore_confirmation":
        console.log("Conversation restored:", data.conversation_uuid);
        setConversationUuid(data.conversation_uuid);
        localStorage.setItem("conversationUuid", data.conversation_uuid);

        // Clear messages and add system message
        setMessageData([]);
        newMessages.push({
          text: data.message || "Previous conversation restored.",
          sender: "system",
          timestamp,
        });
        // Add conversation history if provided
        if (data.history && Array.isArray(data.history)) {
          console.log("History Messages", data.history);
          data.history.forEach((msg) => {
            newMessages.push(
              {
                text: msg.user_message,
                sender: "user",
                timestamp: msg.timestamp,
              },
              { text: msg.ai_response, sender: "ai", timestamp: msg.timestamp }
            );
          });
        }
        break;

      case "reset_confirmation":
        console.log("Conversation reset:", data.conversation_uuid);
        setConversationUuid(data.conversation_uuid);
        localStorage.setItem("conversationUuid", data.conversation_uuid);

        // Clear all messages and notify the user
        setMessageData([]);
        newMessages.push({
          text: data.message || "Conversation has been reset.",
          sender: "system",
          timestamp,
        });
        break;

      case "message_updated":
        console.log("Message updated:", data);
        break;

      case "ai_response_updated":
        console.log("AI response updated for edited message:", data);
        newMessages.push({
          id: data.message_id,
          text: data.response,
          csv_download_url: data.csv_download_url,
          sender: "ai",
          timestamp,
          isEdit: false,
        });
        break;

      case "delete_confirmation":
        console.log("Conversation deleted:", data.conversation_uuid);

        // If the current conversation was deleted, start a new one
        if (data.conversation_uuid === conversationUuid) {
          handleNewConversation();
        }

        // Add system message about deletion if needed
        newMessages.push({
          text: data.message || "Conversation deleted successfully.",
          sender: "system",
          timestamp: data.timestamp || new Date().toISOString(),
        });
        break;

      default:
        console.log("Unknown message type:", data.type, data);
        break;
    }
    // Batch add messages for performance optimization
    if (newMessages.length > 0) {
      setMessageData((prevMessages) => [...prevMessages, ...newMessages]);
    }
  };

  //Handle connect WebSocket function
  const handleConnectWebSocket = () => {
    try {
      // Step1: Close existing connection if any
      if (webSocket && webSocket.readyState !== WebSocket.CLOSED) {
        console.log("Closing existing WebSocket connection");
        webSocket.close();
      }

      // Step2: Create socket URL & Connect new socket
      // const wsScheme = window.location.protocol === 'https:' ? 'wss://' : 'ws://';
      const wsHost = "smartcollect.markytics.ai";
      const wsUrl = `wss://${wsHost}/ws/chat/`;

      const ws = new WebSocket(wsUrl);

      // Handle get initial message
      ws.onopen = () => {
        console.log("WebSocket connection established!");

        // Get the current value from localStorage, which should be null after refresh
        const currentUuid = localStorage.getItem("conversationUuid");

        // Initialize or restore conversation
        const initMessage = {
          type: "initialize",
          user_id: USER_ID,
          language: language,
          BankMstID: userDetails?.BankMstID,
          designation: userDetails?.designation,
          ...(userDetails?.designation === "BM" && {
            BranchID: userDetails?.BranchID,
          }),
        };

        // Add conversation_uuid if we have one stored
        if (currentUuid) {
          console.log(
            `Initializing with stored conversation UUID: ${currentUuid}`
          );
          initMessage.conversation_uuid = currentUuid;
        } else {
          console.log("Starting new conversation - no UUID provided");
        }

        ws.send(JSON.stringify(initMessage));
      };

      // Handle closing socket
      ws.onclose = (event) => {
        console.log(
          `WebSocket closed: code=${event.code}, reason=${
            event.reason || "No reason"
          }`
        );
      };

      // Handle socket error
      ws.onerror = (error) => {
        console.error("WebSocket error:", error);
      };

      // Handle display response message
      ws.onmessage = (event) => {
        console.log("Message received:");
        try {
          const data = JSON.parse(event.data);
          console.log(data);
          handleWebSocketMessage(data);
        } catch (error) {
          console.error("Error parsing WebSocket message:", error);
        }
      };

      setWebSocket(ws);
      // Return cleanup function
      return () => {
        if (ws && ws.readyState !== WebSocket.CLOSED) {
          ws.close();
        }
      };
    } catch (error) {
      console.error("Error setting up WebSocket:", error);
      return null;
    }
  };

  //Initialize WebSocket connection
  // useEffect(() => {
  //   handleFetchHistoryData();
  //   const cleanup = handleConnectWebSocket();
  //   return () => {
  //       if (cleanup) cleanup();
  //   };
  // }, []);

  useEffect(() => {
    console.log("Initial call on refresh");

    setConversationUuid(null);
    localStorage.removeItem("conversationUuid");
    console.log("Removed conversation UUID from state and localStorage");

    handleFetchHistoryData();

    const cleanup = handleConnectWebSocket();

    return () => {
      if (cleanup) cleanup();
    };
  }, []);

  return (
    <Flex className={Style.container}>
      <Flex vertical gap={20} className={Style.history_container}>
        <Flex vertical gap={10} className={Style.chat_history_container}>
          {/* page title*/}
          <Flex align="center" justify="space-between" gap={5}>
            <Flex gap={5} align="center">
              <button className={Style.title_img_div} onClick={handleGoBack}>
                <img src={AI_BACK_ICON} alt="arrow" />
              </button>
              <Text className={Style.title}>Smart Collect AI</Text>
            </Flex>

            <button
              className={Style.new_chat_img}
              onClick={handleNewConversation}
            >
              <Tooltip placement="bottom" title={"New Chat"}>
                <img src={NEW_CHAT_ICON} alt="chat" />
              </Tooltip>
            </button>
          </Flex>

          {/*Recent history  */}
          {historyData.active?.length ||
          historyData.completed?.length ||
          historyData.lastActive ? (
            <Flex vertical gap={10}>
              <Flex
                align="center"
                gap={5}
                style={{
                  borderBlockEnd: "1px solid black",
                  paddingInlineStart: "1rem",
                }}
              >
                <div className={Style.img_div}>
                  <img src={RECENT_ICON} alt="recent" />
                </div>
                <Text className={Style.chat_title}>Recent</Text>
              </Flex>

              <Flex vertical gap={10} className={Style.recent_container}>
                {/*Active  */}
                {historyData.active.length
                  ? historyData.active?.map((active, index) => {
                      return (
                        <HistoryChat
                          key={`${index}-${id}`}
                          data={active}
                          handleRestoreConversation={handleRestoreConversation}
                          handleDeleteConversion={handleDeleteConversion}
                        />
                      );
                    })
                  : null}
                {/* Last Active */}
                {historyData.lastActive && (
                  <HistoryChat
                    data={historyData.lastActive}
                    handleRestoreConversation={handleRestoreConversation}
                    handleDeleteConversion={handleDeleteConversion}
                  />
                )}

                {/* Completed */}
                {historyData.completed.length
                  ? historyData.completed?.map((active, index) => {
                      return (
                        <HistoryChat
                          key={`${id}-${index}`}
                          data={active}
                          handleRestoreConversation={handleRestoreConversation}
                          handleDeleteConversion={handleDeleteConversion}
                        />
                      );
                    })
                  : null}
              </Flex>
            </Flex>
          ) : null}
        </Flex>

        {/* Suggestion history */}
        <Flex vertical gap={10} className={Style.chat_history_container}>
          <Flex
            align="center"
            gap={5}
            style={{
              borderBlockEnd: "1px solid black",
              paddingInlineStart: "1rem",
            }}
          >
            <div className={Style.img_div}>
              <img src={SUGGESTION_ICON} alt="suggestions" />
            </div>
            <Text className={Style.chat_title}>Suggestions</Text>
          </Flex>

          <Flex vertical gap={10} className={Style.suggestion_container}>
            <SuggestionChat
              typeSuggestion={"overdueAmount"}
              title={"How many customer are there?"}
              desc={"Whose overdue amount is greater than."}
              handleSuggestion={() => handleSuggestion("overdueAmount")}
            />
          </Flex>
        </Flex>
      </Flex>

      <Flex
        vertical
        justify="space-between"
        className={Style.history_chat_container}
      >
        {/* Chats */}
        <AIChatSection
          data={messageData}
          isTyping={isTyping}
          handleEditLastMessage={handleEditLastMessage}
        />

        {/* Input field & sender button  */}
        <Flex className={Style.input_box} justify="space-between">
          <Flex vertical gap={10} style={{ flex: "1" }}>
            {attachedFile && (
              <div className={Style.file_preview}>
                <Text>{attachedFile.name}</Text>
              </div>
            )}
            <textarea
              value={text}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              className={Style.custom_input}
              placeholder="Ask Smart Collect AI"
              rows={2} // Reduce default rows
            />
          </Flex>

          <input
            type="file"
            accept=".xlsx,.xls"
            ref={fileInputRef}
            onChange={handleFileChange}
            style={{ display: "none" }}
          />
          <Flex gap={10} align="center">
            <button
              onClick={() => fileInputRef.current?.click()}
              style={{
                cursor: "pointer",
                background: "transparent",
                border: "none",
              }}
            >
              <img src={ATTACHED_ICON} alt="sender" style={{ width: "25px" }} />
            </button>

            {isEditedMessage ? (
              <button className={Style.sender_btn} onClick={handleEditMessage}>
                <img src={SENDER_ICON} alt="sender" />
              </button>
            ) : (
              <button className={Style.sender_btn} onClick={handleSendMessage}>
                <img src={SENDER_ICON} alt="sender" />
              </button>
            )}
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  );
}

import { Modal } from 'antd'
import React from 'react'
import { CustomerDetailsCard } from './CustomerDetailsCard'
import Style from "./_style.module.scss";
import PropTypes from 'prop-types';

export default function FoAllocationCustomerModal({
    modalStatus, 
    data, 
    handleCancel}) {
  return (
    <Modal
        centered
        className={Style.view_modal} 
        title={null}
        open={modalStatus} 
        onOk={handleCancel} 
        onCancel={handleCancel}
        footer={null}
        closable={false}
    >
        <CustomerDetailsCard 
            loanmstid={data?.LoanMstID}
            branchMstId={data?.BranchMstID}
            status={data?.Status}
            name={data?.Name || "Customer Name"}
            contact={data?.Contact || ""}
            branchName={data?.Branch}
            disbursementId={data?.DisbursementID}
            dpdAmount={data?.DPD || 0}
            feedbackDate={data?.FeedbackDate}
            loanType={data?.LoanType}
            overdueAmount={data?.Overdue_Amount || 0}
            promiseAmount={data?.PromiseAmount || 0}
            promiseDate={data?.PromiseDate}
            pendingScene={data?.PendingScene}
            modalButtonText={"Feedback"}
            isModal={true}
            />
    </Modal>
    )
}
FoAllocationCustomerModal.propTypes={
    modalStatus: PropTypes.bool, 
    data: PropTypes.object, 
    handleCancel: PropTypes.func
}
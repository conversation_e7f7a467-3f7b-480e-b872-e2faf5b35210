@import url("../../../index.css");

$light-blue: #E3F5F6;
$blue: #BECFFF;
$error: #EC3939;

.upload_container{
    .custom_select{
        width: 100%;
        box-shadow: 0px 2px 2px 0px #00000040;
        background-color: $light-blue;
        border-radius: 10px;

        :global(.ant-select-selector){
            background-color: $light-blue;
            color: var(--dark-blue);
            border:none !important;
            padding: 1.1rem;
          
        }     
        :global(.ant-select-selection-item){
            font-weight: 500;
            color: var(--dark-blue);
            font-family: 'Kanit','Courier New', Courier, monospace;
        }
        :global(.ant-select-selection-placeholder){
            color: #0F205052;
            font-weight: 500;
        }
    }

    .error{
        color: $error;
        font-style: italic;
        font-family: 'Kanit','Courier New', Courier, monospace;
        font-weight: normal;
    }

    .text{
        color: #407BFF;
        font-weight: 400;
        font-family: 'Kanit','Courier New', Courier, monospace;
    }

    .upload_button{
        padding: 1.2rem 6rem;
        background-color: var(--dark-blue);
        color: white;
        font-family: 'Kanit','Courier New', Courier, monospace;
        font-weight: 400;
        &:hover{
            background-color: var(--dark-blue) !important;
            color: white !important;
            box-shadow: none;
            outline: none;
        }
    }
}

// Responsive view
@media screen and (max-width:568px) {
    .upload_container{
        .text,.error{
            font-size: 11px;
        }
    }
}
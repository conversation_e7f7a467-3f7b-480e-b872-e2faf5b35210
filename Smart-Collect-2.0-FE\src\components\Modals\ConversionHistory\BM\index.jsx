import { Flex, Typography } from "antd";
import BM_IMG from "../../../../assets/Images/bm.svg";
import React, { useState } from "react";
import { handleDateFormatter } from "../../../../constant";
import { BMViewFeedbackModal } from "../../BMViewFeedbackModal";
import Style from "./_style.module.scss";
import PropTypes from "prop-types";

const { Text } = Typography;

export const BM = ({ bmData }) => {
  const [isViewFeedbackModalOpen, setIsViewFeedbackModalOpen] = useState(false);
  const [selectedFeedbackData, setSelectedFeedbackData] = useState({});

  // Grouping AI calls by conversation_date
  const groupedCalls = bmData.reduce((acc, call) => {
    const { conversation_date, person } = call;
    if (!acc[conversation_date]) {
      acc[conversation_date] = {
        bms: [],
        person,
      };
    }
    acc[conversation_date].bms.push(call);
    return acc;
  }, {});

  const handleViewFeedbackCancel = () => {
    setIsViewFeedbackModalOpen(false);
    setSelectedFeedbackData({});
  };

  const handleViewModal = ({ data }) => {
    setSelectedFeedbackData(data);
    setIsViewFeedbackModalOpen(true);
  };
  return (
    <Flex className={Style.bm_container} vertical gap={20}>
      {Object.entries(groupedCalls).map(([date, data]) => {
        const modifyPersonSpell =
          data?.person?.toLowerCase() === "Gaurantator"
            ? "Guarantor"
            : data?.person;
        return (
          <Flex key={date} vertical gap={10} className={Style.group_container}>
            {/* Show Date Once */}
            <Flex justify="center">
              <Text className={Style.date}>{handleDateFormatter(date)}</Text>
            </Flex>

            {/* Show BM title */}
            <Flex className={Style.bm_chat_box} vertical gap={10}>
              <Flex vertical>
                <Flex justify="center" gap={5} align="baseline">
                  <img src={BM_IMG} className={Style.img} alt="AI Icon" />
                  <Text className={Style.bm_text}>BM</Text>
                </Flex>
                <Flex justify="center">
                  <Text className={Style.belongs_text}>
                    ({modifyPersonSpell || "Unknown"})
                  </Text>
                </Flex>
              </Flex>

              {/* Show the feedback view */}
              <Flex className={Style.response_container} vertical gap={5}>
                {data.bms.map((bm, index) => (
                  <Flex
                    key={`${index}-${bm.conversation_json}`}
                    justify="center"
                  >
                    <Text
                      className={Style.response_text}
                      onClick={handleViewModal.bind(null, {
                        data: bm?.conversation_json,
                      })}
                    >
                      View the feedback
                    </Text>
                  </Flex>
                ))}
              </Flex>
            </Flex>
          </Flex>
        );
      })}

      <BMViewFeedbackModal
        modalStatus={isViewFeedbackModalOpen}
        handleCancel={handleViewFeedbackCancel}
        feedbackData={selectedFeedbackData}
      />
    </Flex>
  );
};

BM.propTypes = {
  bmData: PropTypes.array,
};

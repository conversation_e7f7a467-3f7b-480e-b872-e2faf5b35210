import {
  But<PERSON>,
  Col,
  Flex,
  message,
  Row,
  Select,
  Table,
  Typography,
} from "antd";
import { useEffect, useState } from "react";
import AppHeader from "../../AppHeader";
import Style from "../_global.module.scss";
import { AXIOS } from "../../../apis/ho-Instance";

const { Text } = Typography;

export default function AdminComponentConfiguration() {
  const [isLoading, setIsLoading] = useState(false);
  const [isSetScreen] = useState(false);
  const [data] = useState([]);
  const [allBanks, setAllBanks] = useState([]);
  const [componentConfigData, setComponentConfigData] = useState([]);
  const [isLoadingConfig, setIsLoadingConfig] = useState(false);
  const [desAndScreens, setDesAndScreens] = useState({
    designation: [],
    screens: [],
    designationScreens: {},
  });
  const [configData, setConfigData] = useState({
    bank: null,
    designation: null,
    screens: null,
  });

  // Table columns for component configuration
  const configColumns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 80,
      render: (_, data) => <Text>{data?.key}.</Text>,
    },
    {
      title: "Bank ID",
      width: 140,
      dataIndex: "bankId",
      render: (value) => (
        <Text className={Style.blueText}>{value || "--"}</Text>
      ),
    },
    {
      title: "Bank Name",
      dataIndex: "bankName",
      render: (value) => <Text>{value || "--"}</Text>,
    },
    {
      title: "Designation",
      width: 130,
      dataIndex: "designation",
      render: (value) => <Text>{value || "--"}</Text>,
    },
    {
      title: "Screens",
      dataIndex: "screens",
      render: (value) => <Text>{value || "--"}</Text>,
    },
  ];

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      render: (_, data) => <Text>{data?.key}.</Text>,
    },
    {
      title: "Screen Name",
      dataIndex: "ScreenName",
      render: (value) => (
        <Text className={Style.blueText}>{value || "--"}</Text>
      ),
    },
    {
      title: "Designation",
      children: [
        {
          title: "HO",
          dataIndex: "HO",
          render: (value) => <Text>{value}</Text>,
        },
        {
          title: "CEO",
          dataIndex: "CEO",
          render: (value) => <Text>{value}</Text>,
        },
        {
          title: "Manager",
          dataIndex: "Manager",
          render: (value) => <Text>{value}</Text>,
        },
        {
          title: "Co-Founder",
          dataIndex: "Co-Founder",
          render: (value) => <Text>{value}</Text>,
        },
      ],
    },
  ];

  // Fetch component configurations
  const handleFetchComponentConfigs = async () => {
    setIsLoadingConfig(true);
    try {
      // Get all component configs for all banks by default
      const allConfigs = [];

      for (const bank of allBanks) {
        try {
          const res = await AXIOS.post("getcomponents/", {
            bankmstid: bank.BankMstID,
          });

          if (res.status === 200 && res.data?.data) {
            // Transform the data to match our table structure
            const transformedData = res.data.data.map((config, index) => {
              const screensList = config.Screens
                ? Object.entries(config.Screens)
                    .filter(
                      ([, enabled]) => enabled === true || enabled === "True"
                    )
                    .map(([screenName]) => screenName)
                    .join(", ")
                : "";

              return {
                key: allConfigs.length + index + 1,
                bankId: config.BankMstID,
                bankName: bank.BankName,
                designation: config.Designation,
                screens: screensList || "--",
              };
            });
            allConfigs.push(...transformedData);
          }
        } catch (error) {
          console.log(
            `Error fetching configs for bank ${bank.BankMstID}:`,
            error?.message
          );
        }
      }

      setComponentConfigData(allConfigs);
    } catch (error) {
      console.log("Error in fetching component configs", error?.message);
      setComponentConfigData([]);
    } finally {
      setIsLoadingConfig(false);
    }
  };

  // Fetch component configurations for a specific bank
  const handleFetchComponentConfigsForBank = async (bankId) => {
    setIsLoadingConfig(true);
    try {
      const res = await AXIOS.post("getcomponents/", {
        bankmstid: bankId,
      });

      if (res.status === 200 && res.data?.data) {
        // Transform the data to match our table structure
        const selectedBank = allBanks.find((b) => b.BankMstID === bankId);
        const transformedData = res.data.data.map((config, index) => {
          const screensList = config.Screens
            ? Object.entries(config.Screens)
                .filter(([, enabled]) => enabled === true || enabled === "True")
                .map(([screenName]) => screenName)
                .join(", ")
            : "";

          return {
            key: index + 1,
            bankId: config.BankMstID,
            bankName: selectedBank ? selectedBank.BankName : "--",
            designation: config.Designation,
            screens: screensList || "--",
          };
        });
        setComponentConfigData(transformedData);
      } else {
        setComponentConfigData([]);
      }
    } catch (error) {
      console.log("Error in fetching component configs", error?.message);
      setComponentConfigData([]);
    } finally {
      setIsLoadingConfig(false);
    }
  };

  // Fetch banks
  const handleFetchBanks = async () => {
    try {
      const res = await AXIOS.get("getbanks/");
      if (res.status === 200 && res.data.Banks.length > 0) {
        setAllBanks(res.data.Banks);
      } else {
        setAllBanks([]);
      }
    } catch (error) {
      console.log("Error in fetching all banks", error?.message);
      setAllBanks([]);
    }
  };

  // Fetch Designation & Screens
  const handleFetchDesAndScreen = async () => {
    try {
      const selectedBank = allBanks.find(
        (bank) => bank.BankMstID === configData.bank
      );
      const res = await AXIOS.post("setscreens/", {
        bankmstid: selectedBank.BankMstID,
        subscriptionid: selectedBank.SubscriptionID,
      });
      if (res.status === 200) {
        const {
          BU_Types = [],
          Subscription_Details = [],
          Designation_Screens = {},
        } = res.data;

        const transformedScreens = Object.keys(
          Subscription_Details?.[0] || {}
        ).map((key) => ({
          label: key,
          value: key,
        }));

        const transformedDesignations = BU_Types?.map(({ BUType }) => ({
          label: BUType,
          value: BUType,
        }));

        setDesAndScreens({
          designation: transformedDesignations || [],
          screens: transformedScreens || [],
          designationScreens: Designation_Screens || {},
        });
      }
    } catch (error) {
      console.log("Error in designation", error?.message);
    }
  };

  // /Handle set bank
  const handleSet = async () => {
    if (
      !configData.bank ||
      !configData.designation ||
      !configData.screens ||
      configData.screens.length === 0
    ) {
      message.warning("Please select all fields, before setting component");
      return;
    }
    setIsLoading(true);
    try {
      // Convert screen selection to an object like: { AI: "True", DND: "False", ... }
      const screensObj = desAndScreens.screens.reduce((acc, screen) => {
        acc[screen.value] = configData.screens?.includes(screen.value);
        return acc;
      }, {});

      const res = await AXIOS.post("addcomponent/", {
        bankmstid: configData.bank,
        designation: configData.designation,
        screens: screensObj,
      });
      if (res.status === 201) {
        message.success("Component created successfully!");
        // Refresh the component config data after successful creation
        handleFetchComponentConfigs();
      }
    } catch (error) {
      console.log("Error in creating component", error?.message);
      message.error("Component not created, Please try again!");
    } finally {
      setIsLoading(false);
      setConfigData(() => ({
        bank: null,
        designation: null,
        screens: null,
      }));
    }
  };

  // Added data source
  const dataSource = Array.isArray(data)
    ? data?.map((data, i) => ({
        key: i + 1,
        ...data,
      }))
    : [];

  // Invock designation, screens api
  useEffect(() => {
    if (!configData.bank) return;
    handleFetchDesAndScreen();
    // Fetch component configs for the selected bank
    handleFetchComponentConfigsForBank(configData.bank);
  }, [configData.bank]);

  // Handle the selected screen options based on the designation
  useEffect(() => {
    if (!configData.designation) return;
    const activeScreens = Object.entries(
      desAndScreens.designationScreens[configData.designation] || {}
    )
      .filter(([, val]) => val === "True" || val === true)
      .map(([key]) => key);

    // Set the selected screens
    setConfigData((prev) => ({
      ...prev,
      screens: activeScreens || null,
    }));
  }, [configData.designation]);

  // Invock the fetch banks api
  useEffect(() => {
    handleFetchBanks();
  }, []);

  // Fetch all component configs when banks are loaded (default behavior)
  useEffect(() => {
    if (allBanks.length > 0) {
      handleFetchComponentConfigs();
    }
  }, [allBanks]);

  return (
    <Flex vertical gap={10}>
      <AppHeader title={"Component Configuration"} />
      <Row
        gutter={[20, 40]}
        className={Style.add_bank}
        justify={"space-between"}
      >
        {/* bank */}
        <Col span={24}>
          <Flex gap={20} align="center">
            <Flex gap={5} align="center">
              <Text className={Style.text}>Select Bank:</Text>
            </Flex>
            <Select
              showSearch
              className={Style.custom_select}
              placeholder="Select Bank"
              options={allBanks?.map((bank) => ({
                label: bank.BankName,
                value: bank.BankMstID,
              }))}
              value={configData.bank}
              onChange={(value) =>
                setConfigData(() => ({
                  bank: value,
                  designation: null,
                  screens: null,
                }))
              }
              filterOption={(input, option) =>
                (option?.label ?? "")
                  ?.toLowerCase()
                  .includes(input?.toLowerCase())
              }
            />
          </Flex>
        </Col>

        {/* designation */}
        <Col md={12} xs={24}>
          <Flex gap={20} align="center">
            <Flex gap={5} align="center">
              <Text className={Style.text} style={{ width: "130px" }}>
                Select Designation:
              </Text>
            </Flex>
            <Select
              className={Style.custom_select}
              placeholder="Select Designation"
              options={desAndScreens.designation}
              value={configData.designation}
              onChange={(value) =>
                setConfigData((prev) => ({ ...prev, designation: value }))
              }
            />
          </Flex>
        </Col>

        {/*  screen */}
        <Col md={12} xs={24}>
          <Flex gap={20} align="center">
            <Flex gap={5} align="center">
              <Text className={Style.text}>Select Screen:</Text>
            </Flex>
            <Select
              showSearch={false}
              mode="multiple"
              className={Style.custom_select}
              placeholder="Select Screen"
              options={desAndScreens.screens}
              value={configData.screens}
              onChange={(value) =>
                setConfigData((prev) => ({ ...prev, screens: value }))
              }
            />
          </Flex>
        </Col>

        {/* set button */}
        <Col span={24}>
          <Flex align="center" justify="center">
            <Button
              className={Style.add_button}
              loading={isLoading}
              onClick={handleSet}
            >
              Set
            </Button>
          </Flex>
        </Col>

        {/* Component Configuration Table */}
        <Col span={24}>
          <Table
            bordered
            virtual
            className={Style.customTable}
            columns={configColumns}
            dataSource={componentConfigData}
            loading={isLoadingConfig}
            scroll={{
              x: 1200,
              y: 460,
            }}
            pagination={{
              showSizeChanger: false,
            }}
          />
        </Col>

        {/* Table view */}
        {isSetScreen && (
          <Col span={24}>
            <Table
              bordered
              virtual
              className={Style.custom_table}
              columns={columns}
              dataSource={dataSource}
              scroll={{
                x: 1600,
                y: 460,
              }}
              pagination={{
                showSizeChanger: false,
              }}
            />
          </Col>
        )}
      </Row>
    </Flex>
  );
}

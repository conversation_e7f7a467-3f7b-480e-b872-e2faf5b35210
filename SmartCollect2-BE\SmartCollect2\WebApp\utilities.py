import os
from sqlalchemy import create_engine
from dotenv import load_dotenv
import os
import pandas as pd
import numpy as np

def get_connect_engine():
    DB_USER = os.getenv("DATABASE_USER")
    DB_PASSWORD = os.getenv("DATABASE_PASSWORD")
    DB_HOST = os.getenv("DATABASE_HOST")
    DB_PORT = os.getenv("DATABASE_PORT")
    DB_NAME = os.getenv("DATABASE_NAME")
    print(f"DB_USER={DB_USER}, DB_PASSWORD={DB_PASSWORD}, DB_HOST={DB_HOST}, DB_PORT={DB_PORT}, DB_NAME={DB_NAME}")
    if None in [DB_USER, DB_PASSWORD, DB_HOST, DB_PORT, DB_NAME]:
        raise ValueError("One or more environment variables are missing!")
    return create_engine(f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}")


def close_engine_connection(engine):
    if engine:
        engine.dispose()


def generate_where_clause(request):
    """
    Generate a dynamic WHERE clause based on filter_data.
    @Args:
        filter_data (dict): A dictionary containing filter fields and their values.
                            e.g., {
                                "State_id": [5, 10, 15],
                                "Region_id": [20, 25],
                                "Branch_id": "all",
                                "CollectionOfficerID": [342, 450],
                                "LoanType": ["Personal Loan", "Business Loan"]
                            }
                            
    @Returns:
        tuple: (where_clause, params) where where_clause is a string (e.g., ' WHERE ...')
               and params is a dict of parameters.
    <AUTHOR>
    """
    filter_data = request.data
    conditions = []
    params = {}
    for key, value in filter_data.items():
        if value == "all":
            continue
        elif isinstance(value, list) and value:
            value_tuple = tuple(str(v) if isinstance(v, int) else v for v in value)
            conditions.append(f'"{key}" IN %({key})s')
            params[key] = value_tuple
        else:
            conditions.append(f'"{key}" = %({key})s')
            params[key] = str(value)
    where_clause = " AND ".join(conditions) if conditions else ""
    return where_clause, params


def generate_where_clause_sqlalchemy(request):
    """
    Generate a dynamic WHERE clause based on filter_data.
    Args:
        filter_data (dict): A dictionary containing filter fields and their values.
                            e.g., {
                                "State_id": [5, 10, 15],
                                "Region_id": [20, 25],
                                "Branch_id": "all",
                                "CollectionOfficerID": [342, 450],
                                "LoanType": ["Personal Loan", "Business Loan"]
                            }
                            
    Returns:
        tuple: (where_clause, params) where where_clause is a string (e.g., ' WHERE ...')
               and params is a dict of parameters.
    """
    filter_data = request.data
    conditions = []
    params = {}
    for key, value in filter_data.items():
        if value == "all":
            continue
        elif isinstance(value, list) and value:
            value_tuple = tuple(str(v) if isinstance(v, int) else v for v in value)
            conditions.append(f'"{key}" IN :{key}')
            params[key] = value_tuple
        else:
            conditions.append(f'"{key}" = :{key}')
            params[key] = str(value)
    where_clause = " AND ".join(conditions) if conditions else ""
    return where_clause, params


def master_dpd_names_portfolio():
    dpd_categories = [
                    {'dpd_range_start': 0, 'dpd_range_end': 0, 'dpd_category_name': '0'},
                    {'dpd_range_start': 1, 'dpd_range_end': 30, 'dpd_category_name': '1-30'},
                    {'dpd_range_start': 31, 'dpd_range_end': 60, 'dpd_category_name': '31-60'},
                    {'dpd_range_start': 61, 'dpd_range_end': 90, 'dpd_category_name': '61-90'},
                    {'dpd_range_start': 91, 'dpd_range_end': 180, 'dpd_category_name': '91-180'},
                    {'dpd_range_start': 181, 'dpd_range_end': 360, 'dpd_category_name': '181-360'},
                    {'dpd_range_start': 361, 'dpd_range_end': 9999, 'dpd_category_name': '360+'}
                    ]
    return dpd_categories


def generate_where_clause_scrub_sp(request):
    filter_data = request.data
    conditions = []
   
    # Parameters to exclude from WHERE clause (these are handled separately)
    exclude_params = ['page_limit', 'offset', 'tablewise', 'risk_category']
 
    for key, value in filter_data.items():
        # Skip pagination and other non-database parameters
        if key in exclude_params:
            continue
           
        if value == "all" or value == []:
            continue
 
        if not isinstance(value, (list, tuple)):
            value = [value]
 
        # Escape each value
        formatted_values = ", ".join(f"''{v}''" for v in value)
        conditions.append(f'ds."{key}" IN ({formatted_values})')
 
    where_clause = " AND ".join(conditions) if conditions else "1=1"
    return where_clause
 


def create_dpd_buckets(dpd_categories):
    dpd_buckets = []
    for category in dpd_categories:
        bucket = {
            'name': category['dpd_category_name'],
            'range': range(category['dpd_range_start'], category['dpd_range_end'] + 1)
        }
        dpd_buckets.append(bucket)
    return dpd_buckets


def groupby_conditions(df, designation):
    if designation == 1:
        # grouped = df.groupby(['state', 'dpd_category'])['account_number'].nunique().unstack(fill_value=0)
        var = ['State', 'State_id']
        grouped = df.pivot_table(index=['State', 'State_id'], columns='dpd_category', values='accounts', aggfunc='sum', fill_value=0)
        grouped_amount = df.pivot_table(index=['State', 'State_id'], columns='dpd_category', values='amount', aggfunc='sum', fill_value=0)
        grouped_amount_pos = df.pivot_table(index=['State', 'State_id'], columns='dpd_category', values='current_pos', aggfunc='sum', fill_value=0)
    elif designation ==2:
        print('hereeeeeeeeeeeeeee')
        var = ['Region', 'Region_id']
        grouped = df.pivot_table(index=['Region', 'Region_id'], columns='dpd_category', values='accounts', aggfunc='sum', fill_value=0)
        grouped_amount = df.pivot_table(index=['Region', 'Region_id'], columns='dpd_category', values='amount', aggfunc='sum', fill_value=0)
        grouped_amount_pos = df.pivot_table(index=['Region', 'Region_id'], columns='dpd_category', values='current_pos', aggfunc='sum', fill_value=0)
    elif designation ==4 or designation == 5:
        var = ['Branch', 'Branch_id']
        grouped = df.pivot_table(index=['Branch', 'Branch_id'], columns='dpd_category', values='accounts', aggfunc='sum', fill_value=0)
        grouped_amount = df.pivot_table(index=['Branch', 'Branch_id'], columns='dpd_category', values='amount', aggfunc='sum', fill_value=0)
        grouped_amount_pos = df.pivot_table(index=['Branch', 'Branch_id'], columns='dpd_category', values='current_pos', aggfunc='sum', fill_value=0)   
    else:
        raise ValueError("Invalid hierarchy level")
    # Check if 'grouped' is empty using .empty
    if grouped.empty:
        print("Grouped DataFrame is empty")
    else:
        print(f"Length of grouped: {grouped.shape[0]}")  #
    # print(len(grouped))
    grouped_amount.rename(columns = {'amount': 'current_overdue'})
    return grouped,grouped_amount,var, grouped_amount_pos


def seperated_df_portfolio_dpd_cards(df) :
    df = df.loc[:, ~df.columns.duplicated()]
    categories = ["good", "arrear", "npa", "npaplus", "npa2"]
    separated_dfs = {}
    for cat in categories:
        acc_col = f"{cat}_customers"
        amt_col = f"{cat}_principleoutstanding_outstanding"
        if acc_col in df.columns and amt_col in df.columns:
            temp_df = df[["LoanType", acc_col, amt_col, "active_account_count", "active_principleoutstanding_sum"]].copy()
            temp_df[f"{cat}_account_percentage"] = ((temp_df[acc_col] / df[acc_col].sum()) * 100).round(2)
            temp_df[f"{cat}_amount_percentage"] = ((temp_df[amt_col] / df[amt_col].sum()) * 100).round(2)
            temp_df.fillna(0.00, inplace = True)
            separated_dfs[cat] = temp_df
    df.loc['Total'] = df.sum(numeric_only=True)
    df.loc['Total', 'LoanType'] = 'Total'
    return separated_dfs


def calculate_percentage_portfolio(df, column) :
    df = df.loc[:, ~df.columns.duplicated()]
    categories = ["good", "arrear", "npa", "npaplus", "npa2"]
    separated_dfs = {}
    for cat in categories:
        acc_col = f"{cat}_customers"
        amt_col = f"{cat}_principleoutstanding_outstanding"
        if acc_col in df.columns and amt_col in df.columns:
            df[f"{cat}_account_percentage"] = ((df[acc_col] / df[acc_col].sum()) * 100).round(2)
            df[f"{cat}_amount_percentage"] = ((df[amt_col] / df[amt_col].sum()) * 100).round(2)
            temp_df = df[[column, acc_col, amt_col, "active_account_count", "active_principleoutstanding_sum",
                        f"{cat}_account_percentage", f"{cat}_amount_percentage"]].copy()
            separated_dfs[cat] = temp_df
    df.loc['Total'] = df.sum(numeric_only=True)
    df.loc['Total', column] = 'Total'
    df.fillna(0.00, inplace=True)
    return df


import json
def generate_where_clause_scrub(request):
    filter_data = request.data
    conditions = []
    params = {}
    for key, value in filter_data.items():
        if value == "all":
            continue
        if not isinstance(value, (list, tuple)):
            value = (f"''{value}'',")
        else:
            value = tuple(value)
        conditions.append(f'ds."{key}" IN :{key}')
        params[key] = value
    where_clause = " AND ".join(conditions) if conditions else ""
    return where_clause, params




def dpd_query_where_clause(dpd_category_name) :
    dpd_categories = master_dpd_names_portfolio()
    for category in dpd_categories:
        if category['dpd_category_name'] == dpd_category_name:
            start = category['dpd_range_start']
            end = category['dpd_range_end']
            return f""" ds."DPD" BETWEEN {start} AND {end}"""
    return None




def convert_to_graph_data(data):
    df = data.reset_index()
    df = df[df['state_id'].notna() & df['state_id'] != '']
    df = df.dropna(subset=['state', 'state_id'], how='all')
    if df.empty:
        return {'states': [], 'total_risk_distribution': [0, 0, 0, 0, 0]}

    df = df.rename(columns={
        'HIGH': 'HIGH',
        'LOW': 'LOW',
        'LOW-MEDIUM': 'LOW-MEDIUM',
        'MEDIUM': 'MEDIUM',
    })
    print(df)
    risk_columns = ['HIGH', 'LOW', 'LOW-MEDIUM', 'MEDIUM']
    df = df.reindex(columns=df.columns.tolist() + [col for col in risk_columns if col not in df.columns], fill_value=0)
    df['total'] = df[risk_columns].sum(axis=1)
    
    total_risk_distribution = {
        'HIGH': int(df['HIGH'].sum()),
        'LOW' : int(df['LOW'].sum()),
        'LOW-MEDIUM' : int(df['LOW-MEDIUM'].sum()),
        'MEDIUM' : int(df['MEDIUM'].sum())
    }
    response = df[['state', 'state_id', 'HIGH', 'LOW', 'LOW-MEDIUM', 'MEDIUM', 'total']].to_dict(orient='records')
    for item in response:
        item['state_id'] = item['state_id']
    return {
        'states': response,
        'total_risk_distribution': total_risk_distribution
    }
 
 
 
def Scrub_Analysis_top_account_amount(df):
    df['state_id'] = df['state_id'].astype(str)
    df['last_collected_amt'] = pd.to_numeric(df['last_collected_amt'], errors='coerce')
    dpd_matrix = pd.pivot_table(
        df,
        index=['state', 'state_id'],
        columns='risk_category',
        values='disbursementid',
        aggfunc='nunique',
        fill_value=0
    )
    
    dpd_matrix_overdue = pd.pivot_table(
        df,
        index=['state', 'state_id'],
        columns='risk_category',
        values='last_collected_amt',
        aggfunc='sum',
        fill_value=0
    )
    
    account_data = convert_to_graph_data(dpd_matrix)
    amount_data = convert_to_graph_data(dpd_matrix_overdue)
    return {
        'states': account_data['states'],
        'total_risk_distribution': account_data['total_risk_distribution']
    }, {
        'states': amount_data['states'],
        'total_risk_distribution': amount_data['total_risk_distribution']
    }



def generate_where_clause_scrub_analysis(request):
    filter_data = dict(request.data)
    conditions = []
    params = {}

    for key, value in filter_data.items():
        if value == "all":
            continue
        if not isinstance(value, (list, tuple)):
            value = (value,)
        else:
            value = tuple(value)
        if not all(isinstance(item, (int, str, float)) for item in value):
            raise ValueError(f"Invalid items for {key}: {value}")
        conditions.append(f'"{key}" IN :{key}')
        params[key] = value
    where_clause = " AND ".join(conditions) if conditions else ""
    if where_clause:
        where_clause = "WHERE " + where_clause
    return where_clause, params


def convert_scrub_analysis_data(data, var):
    df = data.reset_index()

    # Filter out invalid state_id values
    df = df[df['state_id'].notna() & df['state_id'] != '']
    df = df.dropna(subset=var, how='all')
    if df.empty:
        return []

    # Rename risk columns to uppercase
    df = df.rename(columns={
        'HIGH': 'HIGH',
        'LOW': 'LOW',
        'LOW - MEDIUM': 'LOW-MEDIUM',
        'MEDIUM': 'MEDIUM'
    })

    # Define expected risk columns
    risk_columns = ['HIGH', 'LOW', 'LOW-MEDIUM', 'MEDIUM']

    # Add missing risk columns with default 0
    for col in risk_columns:
        if col not in df.columns:
            df[col] = 0

    # Calculate total
    df['TOTAL'] = df[risk_columns].sum(axis=1)

    # Select required columns for the response
    selected_columns = var + risk_columns + ['TOTAL']
    response = df[selected_columns].to_dict(orient='records')
    return response
# def convert_scrub_analysis_data(data, var):
#     df = data.reset_index()
#     df = df[df['state_id'].notna() & df['state_id'] != '']
#     df = df.dropna(subset=['state', 'state_id'], how='all')
#     if df.empty:
#         return []
#     df = df.rename(columns={
#         'HIGH': 'HIGH',
#         'LOW': 'LOW',
#         'MEDIUM': 'MEDIUM',
#         'LOW-MEDIUM': 'LOW-MEDIUM'
#     })
#     risk_columns = ['HIGH', 'LOW', 'MEDIUM', 'LOW-MEDIUM']
#     df = df.reindex(columns=df.columns.tolist() + [col for col in risk_columns if col not in df.columns], fill_value=0)
#     df['total'] = df[risk_columns].sum(axis=1)
#     selected_columns = var + ['HIGH', 'LOW', 'MEDIUM', 'LOW-MEDIUM','total']
#     response = df[selected_columns].to_dict(orient='records')
#     return response
 


def generate_where_clause_scrub_customers(filter_data):
    conditions = []
    params = {}
    for key, value in filter_data.items():
        if value == "all":
            continue
        elif isinstance(value, list) and value:
            conditions.append(f'"{key}" IN :{key}')
            params[key] = tuple(value)   
        else:
            conditions.append(f'"{key}" = :{key}')
            params[key] = (value,)   
    return conditions, params


def generate_where_clause_scrub_retention_failure(request):
    filter_data = request.data
    conditions = []
    params = {}
    for key, value in filter_data.items():
        if value == "all":
            continue
        if not isinstance(value, (list, tuple)):
            value = (value,)
        else:
            value = tuple(value)
        conditions.append(f'acs."{key}" IN :{key}')
        params[key] = value
    where_clause = " AND ".join(conditions) if conditions else ""
    return where_clause, params

def pagination_data_convertion(df):
    page_size = 10
    total_records = len(df)
    total_pages = (total_records // page_size) + (1 if total_records % page_size != 0 else 0)
    paginated_data = []
    for page in range(total_pages):
        start_idx = page * page_size
        end_idx = start_idx + page_size
        paginated_data.append({
            "page": page + 1,
            "data": df[start_idx:end_idx],
            "total_pages": total_pages
        })
   
    return paginated_data
 
# def pagination_data_convertion(df, page=1, page_size=10):
#     total_records = len(df)
#     total_pages = (total_records // page_size) + (1 if total_records % page_size != 0 else 0)
   
#     # Calculate start and end indices for the requested page
#     start_idx = (page - 1) * page_size
#     end_idx = start_idx + page_size
   
#     # Get the data for the requested page
#     page_data = df[start_idx:end_idx]
   
#     return {
#         "page": page,
#         "data": page_data,
#         "total_pages": total_pages,
#         "total_records": total_records
#     }
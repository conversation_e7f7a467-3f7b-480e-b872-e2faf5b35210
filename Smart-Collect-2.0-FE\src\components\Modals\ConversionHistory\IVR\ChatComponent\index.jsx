import React from "react";
import Style from "./_style.module.scss";
import { Card, Flex } from "antd";
import PropTypes from "prop-types";

export function ChatComponent({messages}) {
  return (
    <Flex vertical gap={10} className={Style.chat_container}>
      {messages?.map((msg, index) => {
        const sender = Object?.keys(msg)[0]; // "Bot" or "Customer"
        const messageText = msg[sender];

        return <Flex
            key={`${sender}-${index}`}
            className={`${Style.chat_message} ${
              sender === "Bot" ? Style.bot : Style.user
            }`}
            justify={sender === "Bot" ? "start" : "end"}
          >
            <Card className={Style.chat_bubble}>
              <p dangerouslySetInnerHTML={{ __html: messageText }}></p>
            </Card>
          </Flex>
      })}
    </Flex>
  );
}

ChatComponent.propTypes={
  messages: PropTypes.array
}
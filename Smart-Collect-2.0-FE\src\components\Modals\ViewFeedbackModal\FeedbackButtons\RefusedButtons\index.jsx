import { Flex } from 'antd'
import React from 'react'
import Style from "./_refused-button.module.scss";

export  function RefusedButtons() {
    const handleTakeLegalAction = ()=>{}
    const handleAddPriorityQueue = ()=>{}
  return (
    <Flex justify='space-between' align='center' gap={20} className={Style.button_container}>
        <button onClick={handleTakeLegalAction}>Take legal action</button>
        <button onClick={handleAddPriorityQueue}>Add to priority Queue</button>
    </Flex>
  )
}

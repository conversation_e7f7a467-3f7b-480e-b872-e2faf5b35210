import django_filters
from campaign.models import WhatsAppFlowMst, BlasterTemplateMapping, IVRFlowMst, VoiceBotTemplateMapping


class WhatsAppFlowMstFilter(django_filters.FilterSet):
    Language = django_filters.CharFilter(
        field_name='whatsappflowmapping__WhatsAppTemplateMappingID__LngMstID__Language',
        lookup_expr='iexact'
    )

    class Meta:
        model = WhatsAppFlowMst
        fields = []
        order_by = ['WhatsAppFlowMstID']
        order_by_desc = ['-WhatsAppFlowMstID']
        distinct = True


class BlasterTemplateMappingFilter(django_filters.FilterSet):
    Language = django_filters.CharFilter(
        field_name='LngMstID__Language',
        lookup_expr='iexact'
    )

    class Meta:
        model = BlasterTemplateMapping
        fields = []
        order_by = ['BlasterTemplateMappingID']
        order_by_desc = ['-BlasterTemplateMappingID']
        distinct = True


class IVRFlowMstFilter(django_filters.FilterSet):
    Language = django_filters.CharFilter(
        field_name='ivrflowmapping__IVRTemplateMappingID__LngMstID__Language',
        lookup_expr='iexact'
    )

    class Meta:
        model = IVRFlowMst
        fields = []
        order_by = ['IVRFlowMstID']
        order_by_desc = ['-IVRFlowMstID']
        distinct = True


class VoiceBotTemplateMappingFilter(django_filters.FilterSet):
    Language = django_filters.CharFilter(
        field_name='LngMstID__Language',
        lookup_expr='iexact'
    )

    class Meta:
        model = VoiceBotTemplateMapping
        fields = []
        order_by = ['VoiceBotTemplateMappingID']
        order_by_desc = ['-VoiceBotTemplateMappingID']
        distinct = True

import React from 'react';
import { Col, Flex, Row, Typography } from 'antd';
import TOTAL_CUSTOMER_IMG from "../../../../assets/Images/customer.png";
import PENDING_IMG from "../../../../assets/Images/pending.png";
import Style from "./_card.module.scss";
import PropTypes from 'prop-types';

const {Title}  = Typography;
export default function AllCards({data, selectedRetentionCard, handleUpdatedRetention}) {
  return (
    <Row gutter={[40,10]} align={'stretch'} justify={"space-between"}>
        <Col md={12} xs={24} onClick={()=> handleUpdatedRetention("active_state_data")}>
            <Flex className={`${Style.card_container} ${selectedRetentionCard === "active_state_data" ? Style.active_card : ""}`} 
            vertical gap={20}>
                <Flex justify='space-between' align='center' style={{marginBlockStart:"1rem"}}>
                    <Title level={3} className={Style.title}>Active in XYZ Bank and Loan in other Banks</Title>
                </Flex>

                <Flex gap={3} justify='space-between' align='center'>
                    <Title level={3} className={Style.title}>{data?.cards?.active}</Title>
                    <div className={Style.image_container}>
                        <img src={TOTAL_CUSTOMER_IMG} alt='customer'/>
                    </div>       
                </Flex>
            </Flex>
        </Col>
        <Col md={12} xs={24} onClick={()=> handleUpdatedRetention("inactive_state_data")}>
            <Flex className={`${Style.card_container} ${selectedRetentionCard === "inactive_state_data" ? Style.active_card : ""}`} 
            vertical gap={20}>
                <Flex justify='space-between' align='center' style={{marginBlockStart:"1rem"}}>
                    <Title level={3} className={Style.title}>Inactive in XYZ Bank and New Loan in Other Banks </Title>
                </Flex>

                <Flex gap={3} justify='space-between' align='center'>
                    <Title level={3} className={Style.title}>{data?.cards?.inactive}</Title>
                    <div className={Style.image_container}>
                        <img src={PENDING_IMG} alt='customer'/>
                    </div>       
                </Flex>
            </Flex>
        </Col>
   </Row>
  )
}

AllCards.propTypes={
    data: PropTypes.object, 
    selectedRetentionCard: PropTypes.string, 
    handleUpdatedRetention: PropTypes.func
}
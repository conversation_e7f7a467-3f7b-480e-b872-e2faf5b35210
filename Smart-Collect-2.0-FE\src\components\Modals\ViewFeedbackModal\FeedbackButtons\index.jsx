import React from "react";
import { Flex } from "antd";
import { SMART_COLLECT_MENU_IDS } from "../../../../constant";
import { RefusedButtons } from "./RefusedButtons";
import AlreadyPayButtons from "./AlreadyPayButtons";
import PropTypes from "prop-types";

export function FeedbackButtons({ pageId }) {
  return (
    <Flex align="center" justify="space-between" gap={10}>
      {pageId === SMART_COLLECT_MENU_IDS.REFUSED_TO_PAY && <RefusedButtons />}
      {pageId === SMART_COLLECT_MENU_IDS.ALREADY_PAY ? (
        <AlreadyPayButtons />
      ) : null}
    </Flex>
  );
}

FeedbackButtons.propTypes = {
  pageId: PropTypes.string,
};

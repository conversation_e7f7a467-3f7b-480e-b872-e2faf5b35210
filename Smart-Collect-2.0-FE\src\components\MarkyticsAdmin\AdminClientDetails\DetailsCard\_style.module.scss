@import url("../../../../index.css");

$light-blue: #E3F5F6;

.card_container{
    background-color: $light-blue;
    border-top: 6.32px solid var(--dark-blue);
    border-radius: 10px;
    padding: 1rem;
    margin-top: 1rem;
    .image_container{
        width: 120px;
        height: 100px;
        img{
            object-fit: contain;
            width: 100%;
        }
    }
    .title{
        margin: 0;
        color: var(--dark-blue);
        font-weight: normal;
        font-size: 1.2rem;
        line-height: 1px;
        font-family: 'Kanit','Courier New', Courier, monospace;
    }
    .desc{
        color: var(--dark-blue);
        font-weight: 600;
        margin: 0;
    }

    .icon_img{
        width: 15px;
        height: 15px;
        cursor: pointer;
        img{
            object-fit: contain;
            width: 100%;
        }
    }
}

// Responsive view
@media screen and (max-width:768px) {
    .card_container{
        margin-top: 0.5rem;
        padding: 1rem 0.5rem 0.5rem;

        .title{
            font-size: 14px
        }

        .desc{
            font-size: 16px;
        }

        .image_container{
            width: 80px;
            height: 80px;
        }
    }
}
import { Flex,Typo<PERSON>, Button } from 'antd'
import React from 'react'
import HO_DASHBOARD_IMG from "../../assets/Images/dashboard-ho.png";
import ANALYSIS_1_IMG from "../../assets/Images/analysis_1.png";
import HO_FIELD_IMG from "../../assets/Images/field-officer.png";
import HO_DPD_IMG from "../../assets/Images/dpd-ho.png";
import HO_REPORT_IMG from "../../assets/Images/report-ho.png";
import UPLOAD_IMG from "../../assets/Images/admin-upload.svg";
import Style from "../_style.module.scss";
import PropTypes from 'prop-types';

const {Text}  = Typography;

export function MenuTour({handleNext, handleSkip}) {
  return (
    <Flex vertical gap={30}>
        <Flex gap={10} style={{
            borderRadius:"5px",
            border:"1px solid #DAE6FF", 
            padding:"1rem .5rem"}}>
            <div className={Style.container}>
                <div className={Style.icon}>
                    <img src={HO_DASHBOARD_IMG} alt="dashboard" />
                </div>
                <Text className={Style.title}>Dashboard</Text>
            </div>

            <div className={Style.container}>
                <div className={Style.icon}>
                    <img src={ANALYSIS_1_IMG} alt="dashboard" />
                </div>
                <Text className={Style.title}>Analysis</Text>
            </div>

            <div className={Style.container}>
                <div className={Style.icon}>
                    <img src={HO_REPORT_IMG} alt="dashboard" />
                </div>
                <Text className={Style.title}>Reports</Text>
            </div>

            <div className={Style.container}>
                <div className={Style.icon}>
                    <img src={UPLOAD_IMG} alt="dashboard" />
                </div>
                <Text className={Style.title}>Upload file</Text>
            </div>

            <div className={Style.container}>
                <div className={Style.icon}>
                    <img src={HO_FIELD_IMG} alt="dashboard" />
                </div>
                <Text className={Style.title}>Field Officer</Text>
            </div>

            <div className={Style.container}>
                <div className={Style.icon}>
                    <img src={HO_DPD_IMG} alt="dashboard" />
                </div>
                <Text className={Style.title}>Insight</Text>
            </div>
        </Flex>
        <Flex justify='end' gap={10}>
            <Button className={Style.skip_btn} onClick={handleSkip}>Skip</Button>
            <Button className={Style.next_btn} onClick={handleNext}>Next</Button>
        </Flex>
    </Flex>
  )
}

// Define the props
MenuTour.propTypes = {
  handleNext: PropTypes.func.isRequired,
  handleSkip: PropTypes.func.isRequired,
};
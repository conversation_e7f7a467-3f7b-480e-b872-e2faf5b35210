import { Flex, message, Typography } from "antd";
import React, { useState, useEffect } from "react";
import AppHeader from "../AppHeader";
import {
  ALL_CACHE_NAMES,
  formatAmount,
  formattedTextToCapitalized,
  handleDownloadTable,
  LOCAL_KEYS,
} from "../../constant";
import DOWNLOAD_IMG from "../../assets/Images/download.svg";
import { DashboardEngagementAllCards } from "../DashboardEngagementAllCards";
import { DashboardEngagementTable } from "../DashboardEngagementTable";
import Style from "./_style.module.scss";
import { AXIOS } from "../../apis/ho-Instance";
import ApplicationLoader from "../ApplicationLoader";
import {
  getCacheKey,
  getFromCache,
  getPostCacheKey,
  storeToCache,
} from "../../utils/cacheHelper";
import { encryptBody, isSameEncryptedBody } from "../../utils/crypto";

const { Text } = Typography;
const CACHE_NAME = ALL_CACHE_NAMES.DASHBOARD_CACHE;

export default function BlasterDashboard() {
  const [tableData, setTableData] = useState([]);
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSwitchOn, setIsSwitchOn] = useState(false);
  const storedFilter = localStorage.getItem("filterDate");
  const storedDpd = localStorage.getItem("DashboardDpdCategory");

  // Apply the data
  const applyBlasterData = (data) => {
    setData(data);
    setTableData(data?.blaster_history || []);
  };

  // Get blaster data
  const getBlasterData = async () => {
    setIsLoading(true);
    const cacheKey = getCacheKey({ endpoint: "blaster_history/" });
    // Check cache
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      applyBlasterData(cachedData);
      setIsLoading(false);
    }
    try {
      const response = await AXIOS.get("blaster_history/");
      if (response.status === 200) {
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
        applyBlasterData(response.data);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Post the whatsapp data
  const getBlasterFilteredData = async ({
    fromDate,
    toDate,
    BranchName,
    dpdRange,
    disbursementID,
  }) => {
    setIsLoading(true);
    const body = {
      fromDate,
      toDate,
      BranchName,
      dpdfrom: dpdRange?.start ?? -1,
      dpdto: dpdRange?.end ?? -1,
      DisbursementID: disbursementID,
    };
    const cacheKey = getPostCacheKey({ endpoint: "blaster_history/", body });
    const encryptedOldBody = localStorage.getItem(LOCAL_KEYS.HO_CHANNEL_FILTER);
    const sameBody = isSameEncryptedBody({
      newBody: body,
      encryptedOldBody,
    });

    if (sameBody) {
      const cachedData = await getFromCache({
        cacheName: CACHE_NAME,
        key: cacheKey,
      });
      if (cachedData) {
        applyBlasterData(cachedData);
        setIsLoading(false);
      }
    }
    try {
      const response = await AXIOS.post("blaster_history/", body);
      if (response.status === 200) {
        const encrypted = encryptBody(body);
        if (encrypted) {
          localStorage.setItem(LOCAL_KEYS.HO_CHANNEL_FILTER, encrypted);
        }
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data,
        });
        applyBlasterData(response.data);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Apply filter and fetch data
  const applyFilters = async ({
    fromDate,
    toDate,
    BranchName,
    disbursementID,
  }) => {
    await getBlasterFilteredData({
      fromDate: fromDate,
      toDate: toDate,
      BranchName,
      dpdRange: JSON.parse(storedDpd),
      disbursementID,
    });
  };

  const ClearFilters = () => {
    getBlasterData();
    localStorage.removeItem("filterDate");
    localStorage.removeItem(LOCAL_KEYS.HO_CHANNEL_FILTER);
  };

  const handleDownload = async () => {
    if (tableData.length === 0 || !tableData) {
      return message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: "BlasterCalls",
        worksheetName: "Dashboard-Blaster-Calls",
        tableData: tableData,
      });
    }
  };

  // On mount, check localStorage for saved filters
  useEffect(() => {
    if (storedFilter || storedDpd) {
      const { fromDate, toDate, BranchName, disbursementID } = storedFilter
        ? JSON.parse(storedFilter)
        : {
            fromDate: null,
            toDate: null,
            BranchName: null,
            disbursementID: "",
          };
      const parsedDpd = storedDpd
        ? JSON.parse(storedDpd)
        : { start: -1, end: -1 };
      getBlasterFilteredData({
        fromDate,
        toDate,
        BranchName,
        dpdRange: parsedDpd,
        disbursementID,
      });
    } else {
      getBlasterData();
    }
  }, []);
  return (
    <Flex gap={10} vertical>
      <AppHeader
        title={
          <Flex className={Style.title} gap={5}>
            <Text>
              Blaster Calls :{" "}
              <span style={{ color: "#0F2050" }}>{data.Blaster_Calls}</span>
            </Text>
            <Text>
              Distinct Customers :{" "}
              <span style={{ color: "#0F2050" }}>
                {data.distinct_customers}
              </span>
            </Text>
          </Flex>
        }
        isDashboard={false}
        isSwitchOn={isSwitchOn}
        setIsSwitchOn={setIsSwitchOn}
        ClearFilters={ClearFilters}
        applyFilters={applyFilters}
        isDashboardOtherPages={true}
      />
      <div>
        {isLoading ? (
          <ApplicationLoader />
        ) : (
          <div className={Style.details}>
            {!isSwitchOn ? (
              <DashboardEngagementAllCards data={tableData} />
            ) : (
              <Flex vertical gap={20} style={{ paddingBlockStart: "1rem" }}>
                <Flex justify="end">
                  <button
                    className={Style.download_button}
                    onClick={handleDownload}
                  >
                    <img src={DOWNLOAD_IMG} alt="download-button" />
                  </button>
                </Flex>
                <DashboardEngagementTable data={tableData} />
              </Flex>
            )}
          </div>
        )}
      </div>
    </Flex>
  );
}

import React from "react";
import Style from "./_style.module.scss";
import { formatAmount, formatDigits } from "../../../../constant";
import { Table, Typography } from "antd";
import PropTypes from "prop-types";

const { Text } = Typography;

export function CustomerDetailsTable({ customerDetails, onPaginationChange }) {
  const columns = [
    {
      title: "Bank Name",
      children: [
        {
          title: "Sr. No.",
          dataIndex: "key",
          rowScope: "row",
          width: 90,
          render: (_, data) => <Text>{data?.key}.</Text>,
        },
        {
          title: "Account No.",
          dataIndex: "DisbursementID",
          render: (value) => <Text className={Style.lan}>{value}</Text>,
        },
        {
          title: "Account Name",
          dataIndex: "CustomerName",
          render: (value) => <Text className={Style.lan}>{value ?? "-"}</Text>,
        },
        {
          title: "Demand",
          dataIndex: "demand",
          render: (value) => (
            <Text className={Style.lan}>Rs.{formatAmount(value)}</Text>
          ),
        },
        {
          title: "Outstanding",
          dataIndex: "TotalOutstanding",
          render: (value) => (
            <Text className={Style.lan}>Rs.{formatAmount(value)}</Text>
          ),
        },
      ],
    },
    {
      title: "Other Bank",
      children: [
        {
          title: "Active",
          dataIndex: "active_accounts",
          render: (value) => (
            <Text className={Style.lan}>{formatDigits(value)}</Text>
          ),
        },
        {
          title: "Closed",
          dataIndex: "closed_accounts",
          render: (value) => (
            <Text className={Style.lan}>{formatDigits(value)}</Text>
          ),
        },
        {
          title: "Delinquent",
          dataIndex: "delinquent_accounts",
          render: (value) => (
            <Text className={Style.lan}>{formatDigits(value)}</Text>
          ),
        },
        {
          title: "Writeoff",
          dataIndex: "writeoff_accounts",
          render: (value) => (
            <Text className={Style.lan}>{formatDigits(value)}</Text>
          ),
        },
        {
          title: "Other",
          dataIndex: "others",
          render: (value) => (
            <Text className={Style.lan}>{formatDigits(value)}</Text>
          ),
        },
        {
          title: "Total",
          dataIndex: "total_accounts",
          render: (value) => <Text>{formatDigits(value)}</Text>,
        },
      ],
    },
  ];

  const dataSource = Array.isArray(customerDetails.data)
    ? customerDetails.data.map((data, i) => ({
        key: i + 1,
        ...data,
      }))
    : [];

  const summaryContent = (pageData) => {
    return <DemandSummary pageData={pageData} />;
  };
  return (
    <Table
      bordered
      // virtual
      className={Style.custom_table}
      columns={columns}
      dataSource={dataSource}
      scroll={{
        x: 2000,
        y: 460,
      }}
      pagination={{
        current: customerDetails.current,
        total: customerDetails.total,
        onChange: onPaginationChange,
        pageSize: 10,
        showSizeChanger: false,
      }}
      summary={summaryContent}
    />
  );
}

function DemandSummary({ pageData }) {
  const totalDemand = pageData.reduce((sum, row) => sum + (row.demand || 0), 0);
  const totalOutstanding = pageData.reduce(
    (sum, row) => sum + (row.TotalOutstanding || 0),
    0
  );
  const active = pageData.reduce(
    (sum, row) => sum + (row.active_accounts || 0),
    0
  );
  const closed = pageData.reduce(
    (sum, row) => sum + (row.closed_accounts || 0),
    0
  );
  const delinquent = pageData.reduce(
    (sum, row) => sum + (row.delinquent_accounts || 0),
    0
  );
  const writeoff = pageData.reduce(
    (sum, row) => sum + (row.writeoff_accounts || 0),
    0
  );
  const others = pageData.reduce((sum, row) => sum + (row.others || 0), 0);
  const totalAccounts = pageData.reduce(
    (sum, row) => sum + (row.total_accounts || 0),
    0
  );

  return (
    <Table.Summary fixed>
      <Table.Summary.Row>
        <Table.Summary.Cell index={0} />
        <Table.Summary.Cell index={1} colSpan={2}>
          <Text strong>Total</Text>
        </Table.Summary.Cell>
        <Table.Summary.Cell>
          <Text>Rs.{formatAmount(totalDemand)}</Text>
        </Table.Summary.Cell>
        <Table.Summary.Cell>
          <Text>Rs.{formatAmount(totalOutstanding)}</Text>
        </Table.Summary.Cell>
        <Table.Summary.Cell>
          <Text>{formatDigits(active)}</Text>
        </Table.Summary.Cell>
        <Table.Summary.Cell>
          <Text>{formatDigits(closed)}</Text>
        </Table.Summary.Cell>
        <Table.Summary.Cell>
          <Text>{formatDigits(delinquent)}</Text>
        </Table.Summary.Cell>
        <Table.Summary.Cell>
          <Text>{formatDigits(writeoff)}</Text>
        </Table.Summary.Cell>
        <Table.Summary.Cell>
          <Text>{formatDigits(others)}</Text>
        </Table.Summary.Cell>
        <Table.Summary.Cell>
          <Text>{formatDigits(totalAccounts)}</Text>
        </Table.Summary.Cell>
      </Table.Summary.Row>
    </Table.Summary>
  );
}

DemandSummary.propTypes = {
  pageData: PropTypes.array,
};
CustomerDetailsTable.propTypes = {
  customerDetails: PropTypes.object,
  onPaginationChange: PropTypes.func,
};

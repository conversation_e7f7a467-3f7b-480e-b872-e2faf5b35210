import {
  Avatar,
  Button,
  Flex,
  Input,
  message,
  Select,
  Typography,
  InputNumber,
  Row,
  Col,
  DatePicker,
} from "antd";
import React, { useEffect, useState } from "react";
import TextArea from "antd/es/input/TextArea";
import AVATAR_IMG from "../../../../../../assets/Images/mage_robot.svg";
import DELETE_IMG from "../../../../../../assets/Images/delete.svg";
import { AXIOS } from "../../../../../../apis/ho-Instance";
import {
  CAMPAIGN_DATA_PROP_TYPES,
  CAMPAIGN_DROPDOWN_DATA,
} from "../../../../../../constant";
import { IvrNewTemplateNormal } from "./IvrNewTemplateNormal";
import Style from "./_style.module.scss";
import dayjs from "dayjs";
import PropTypes from "prop-types";

const { Text } = Typography;

export function CreateAiNewTemplate({
  template,
  index,
  campaignData,
  setCampaignData,
  commFlowData,
}) {
  const DATE_FIELDS = CAMPAIGN_DROPDOWN_DATA.filter((item) =>
    item.value?.toLowerCase().includes("date")
  ).map((item) => item.value);
  const [messageApi, contextHolder] = message.useMessage();
  const [isLoading, setIsLoading] = useState(false);

  // Find the matching commFlowData entry
  const matchingCommFlow = commFlowData?.find(
    (flow) => flow.CommunicationType === template.communicationType
  );

  // Check if the CommunicationType
  const isAi = matchingCommFlow?.CommunicationType?.toLowerCase() === "ai";
  const isIVR = matchingCommFlow?.CommunicationType?.toLowerCase() === "ivr";
  const isWhatsapp =
    matchingCommFlow?.CommunicationType?.toLowerCase() === "whatsapp";
  const isBlaster =
    matchingCommFlow?.CommunicationType?.toLowerCase() === "blaster";
  const [languageOptions, setLanguageOptions] = useState([]);
  const [variableMappingOptions, setVariableMappingOptions] = useState([]);
  const [variableMappingTypes, setVariableMappingTypes] = useState({});
  const prettifyLabel = (key) => {
    return key
      .replace(/_/g, " ") // Replace underscores with space
      .replace(/([a-z])([A-Z])/g, "$1 $2") // Add space before capital letters
      .replace(/\b\w/g, (char) => char.toUpperCase()); // Capitalize each word
  };

  const nodeOptions = template.nodes.map((node) => ({
    label: `${node.name}`, // Show Name & ID
    value: node.id, // Store ID as value
  }));

  // Handle fetch variables mapping
  const handleFetchVariables = async () => {
    try {
      const res = await AXIOS.get("v1/campaign/dropdownoptions", {
        params: {
          requesttype: "variablemapping",
        },
      });
      if (res.status === 200) {
        const rawParams = res.data;
        const options = Object.keys(rawParams).map((key) => ({
          label: prettifyLabel(key),
          value: key,
          type: rawParams[key],
        }));
        setVariableMappingOptions(options);
        // For quick lookup by value
        setVariableMappingTypes(rawParams);
      }
    } catch (error) {
      console.log("Error in variable,", error?.message);
    }
  };

  // Handle text fields
  const handleTextOnChange = ({ value, fieldName }) => {
    setCampaignData((prevData) => {
      const updatedTemplates = prevData.templates.map((t, i) =>
        i === index
          ? {
              ...t,
              [fieldName]: value,
            }
          : t
      );
      return { ...prevData, templates: updatedTemplates };
    });
  };

  // Handle add new variables
  const handleAddVariable = () => {
    setCampaignData((prevData) => {
      const updatedTemplates = prevData.templates.map((t, i) =>
        i === index
          ? {
              ...t,
              variablesMapping: [
                ...t.variablesMapping,
                { columnName: null, sampleValue: "" },
              ],
              scriptValue: t.scriptValue + `{${t.variablesMapping.length + 1}}`,
            }
          : t
      );
      return { ...prevData, templates: updatedTemplates };
    });
  };

  // Handle add node button variables
  const handleAddIVRNodeButton = () => {
    setCampaignData((prevData) => {
      const templates = [...prevData.templates];
      const template = templates[index];

      const existingButtons = template.nodeButtonMapping || [];
      const newButtonId = existingButtons.length
        ? Math.max(...existingButtons.map((b) => b.buttonId)) + 1
        : 1;

      const updatedTemplate = {
        ...template,
        nodeButtonMapping: [
          ...existingButtons,
          { buttonId: newButtonId, buttonReportedNode: null },
        ],
      };

      templates[index] = updatedTemplate;

      return { ...prevData, templates };
    });
  };

  // Handle delete node buttons
  const handleDeleteIvrNodeButton = ({ nodeIndex }) => {
    setCampaignData((prevData) => {
      const templates = [...prevData.templates];
      const template = templates[index];

      const updatedTemplate = {
        ...template,
        nodeButtonMapping: template.nodeButtonMapping.filter(
          (_, bIdx) => bIdx !== nodeIndex
        ),
      };

      templates[index] = updatedTemplate;

      return { ...prevData, templates };
    });
  };

  // Handle edit node buttons
  const handleEditIvrNodeButton = ({ varIndex, field, value }) => {
    setCampaignData((prevData) => {
      const templates = [...prevData.templates];
      const template = templates[index];

      const updatedButtonMapping = template.nodeButtonMapping.map(
        (button, btnIdx) =>
          btnIdx === varIndex ? { ...button, [field]: value } : button
      );

      const updatedTemplate = {
        ...template,
        nodeButtonMapping: updatedButtonMapping,
      };

      templates[index] = updatedTemplate;

      return { ...prevData, templates };
    });
  };

  // Handle add initial variables
  const handleAddInitialTextVariable = () => {
    setCampaignData((prevData) => {
      const updatedTemplates = prevData.templates.map((t, i) =>
        i === index
          ? {
              ...t,
              initialTextVariableMapping: [
                ...t.initialTextVariableMapping,
                { columnName: null, sampleValue: "" },
              ],
              initialTexts:
                t.initialTexts + `{${t.initialTextVariableMapping.length + 1}}`,
            }
          : t
      );
      return { ...prevData, templates: updatedTemplates };
    });
  };

  // Handle add end variables
  const handleAddEndTextVariable = () => {
    setCampaignData((prevData) => {
      const updatedTemplates = prevData.templates.map((t, i) =>
        i === index
          ? {
              ...t,
              endingTextVariableMapping: [
                ...t.endingTextVariableMapping,
                { columnName: null, sampleValue: "" },
              ],
              endingTexts:
                t.endingTexts + `{${t.endingTextVariableMapping.length + 1}}`,
            }
          : t
      );
      return { ...prevData, templates: updatedTemplates };
    });
  };

  // Handle add new buttons
  const handleAddButtons = () => {
    setCampaignData((prevData) => {
      const updatedTemplates = prevData.templates.map((t, i) => {
        if (i === index) {
          // Check if the number of buttons is less than 3
          if (t.buttonsMapping.length >= 3) {
            messageApi.info("You can only add up to 3 buttons.");
            return t;
          }

          return {
            ...t,
            buttonsMapping: [
              ...t.buttonsMapping,
              { buttonName: "", buttonResponse: "" },
            ],
          };
        }
        return t;
      });

      return { ...prevData, templates: updatedTemplates };
    });
  };

  // Handle add new buttons
  const handleAddIvrNodes = () => {
    setCampaignData((prevData) => {
      const updatedTemplates = prevData.templates.map((t, i) => {
        if (i === index) {
          // Check if the number of buttons is less than 3
          // if (t.buttonsMapping.length >= 3) {
          //     messageApi.info("You can only add up to 3 buttons.")
          //     return t;
          // }
          const nextId = t.nodes.length + 1;
          return {
            ...t,
            nodes: [
              ...t.nodes,
              {
                id: nextId,
                name: "",
                response: "",
                variableMapping: [],
                nodeButtonMapping: [],
                parentId: null,
                childIds: [],
              },
            ],
          };
        }
        return t;
      });

      return { ...prevData, templates: updatedTemplates };
    });
  };

  // Handle delete buttons
  const handleDeleteButton = (buttonIndex) => {
    setCampaignData((prevData) => {
      const templates = [...prevData.templates];
      const template = templates[index];

      const updatedTemplate = {
        ...template,
        buttonsMapping: template.buttonsMapping.filter(
          (_, bIndex) => bIndex !== buttonIndex
        ),
      };

      templates[index] = updatedTemplate;

      return { ...prevData, templates };
    });
  };

  // Handle button changing
  const handleButtonsChange = ({ btnIndex, field, value }) => {
    setCampaignData((prevData) => {
      const templates = [...prevData.templates];
      const template = templates[index];

      const updatedButtons = template.buttonsMapping.map((button, bIndex) =>
        bIndex === btnIndex ? { ...button, [field]: value } : button
      );

      const updatedTemplate = {
        ...template,
        buttonsMapping: updatedButtons,
      };

      templates[index] = updatedTemplate;

      return { ...prevData, templates };
    });
  };

  // Handle variables changing
  const handleVariablesChange = ({ varIndex, field, value }) => {
    setCampaignData((prevData) => {
      const templates = [...prevData.templates];
      const template = templates[index];

      const updatedVariables = template.variablesMapping.map((variable, vIdx) =>
        vIdx === varIndex ? { ...variable, [field]: value } : variable
      );

      const updatedTemplate = {
        ...template,
        variablesMapping: updatedVariables,
      };

      templates[index] = updatedTemplate;

      return { ...prevData, templates };
    });
  };

  // Handle delete variables
  const handleVariableDelete = (varIndex) => {
    setCampaignData((prevData) => {
      const templates = [...prevData.templates];
      const template = templates[index];

      const updatedTemplate = {
        ...template,
        variablesMapping: template.variablesMapping.filter(
          (_, vIdx) => vIdx !== varIndex
        ),
      };

      templates[index] = updatedTemplate;

      return { ...prevData, templates };
    });
  };

  // Handle variables changing
  const handleInitialTextVariableMapping = ({ varIndex, field, value }) => {
    setCampaignData((prevData) => {
      const templates = [...prevData.templates];
      const template = templates[index];

      const updatedInitialMapping = template.initialTextVariableMapping.map(
        (variable, vIdx) =>
          vIdx === varIndex ? { ...variable, [field]: value } : variable
      );

      const updatedTemplate = {
        ...template,
        initialTextVariableMapping: updatedInitialMapping,
      };

      templates[index] = updatedTemplate;

      return { ...prevData, templates };
    });
  };

  // Handle variables changing
  const handleEndingTextVariableMapping = ({ varIndex, field, value }) => {
    setCampaignData((prevData) => {
      const templates = [...prevData.templates];
      const template = templates[index];

      const updatedEndingMapping = template.endingTextVariableMapping.map(
        (variable, vIdx) =>
          vIdx === varIndex ? { ...variable, [field]: value } : variable
      );

      const updatedTemplate = {
        ...template,
        endingTextVariableMapping: updatedEndingMapping,
      };

      templates[index] = updatedTemplate;

      return { ...prevData, templates };
    });
  };

  // Handle template name change
  const handleTemplateName = (value) => {
    setCampaignData((prevData) => {
      const updatedTemplates = prevData.templates.map((t, i) =>
        i === index ? { ...t, name: value } : t
      );
      return { ...prevData, templates: updatedTemplates };
    });
  };

  // Handle language change
  const handleLanguageName = (value) => {
    setCampaignData((prevData) => {
      const updatedTemplates = prevData.templates.map((t, i) =>
        i === index ? { ...t, language: value } : t
      );
      return { ...prevData, templates: updatedTemplates };
    });
  };

  const transformTemplatesNodes = () => {
    const rootChildIds = template.nodes
      .filter((node) => node.parentId === "root")
      ?.map((item) => item.id);

    const rootNode = {
      id: 0,
      initialText: template.scriptValue,
      variableMapping: template.variablesMapping,
      nodeButtonMapping: template.nodeButtonMapping,
      childIds: template.nodes.length > 0 ? rootChildIds : [],
    };

    const transformedNodes = template.nodes.map((node) => ({
      ...node,
    }));

    return {
      0: rootNode,
      ...transformedNodes.reduce((acc, node) => {
        acc[node.id] = node;
        return acc;
      }, {}),
    };
  };

  // Handle whats app approval
  const handleWhatsappApproval = async () => {
    setIsLoading(true);
    try {
      const body = {
        Name: template.name,
        Language: template.language,
        TemplateText: template.scriptValue,
        VariableMapping: template.variablesMapping.reduce(
          (acc, item, index) => {
            acc[index + 1] = item.columnName;
            return acc;
          },
          {}
        ),
        SampleMapping: template.variablesMapping.reduce((acc, item, index) => {
          acc[index + 1] = item.sampleValue;
          return acc;
        }, {}),
        ButtonMapping: template.buttonsMapping.reduce((acc, item, index) => {
          acc[index + 1] = {
            Name: item.buttonName,
            Response: item.buttonResponse,
          };
          return acc;
        }, {}),
      };
      const res = await AXIOS.post("v1/template/registerWhatsapp", body);
      if (res.status === 201) {
        return res.data?.whmst;
      }
      message.success("Template approved!");
    } catch (error) {
      console.log("Error in whats app template", error?.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle ai approval
  const handleAiApproval = async () => {
    setIsLoading(true);
    try {
      const body = {
        Name: template.name,
        Language: template.language,
        TemplateText: `${template.description}\n${template.rules}\n${template.interactionPlans}`,
        start: {
          message: template.initialTexts,
          VariableMapping: template.initialTextVariableMapping.reduce(
            (acc, item, index) => {
              acc[index + 1] = item.columnName;
              return acc;
            },
            {}
          ),
          SampleMapping: template.initialTextVariableMapping.reduce(
            (acc, item, index) => {
              acc[index + 1] = item.sampleValue;
              return acc;
            },
            {}
          ),
        },
        end: {
          message: template.endingTexts,
          VariableMapping: template.endingTextVariableMapping.reduce(
            (acc, item, index) => {
              acc[index + 1] = item.columnName;
              return acc;
            },
            {}
          ),
          SampleMapping: template.endingTextVariableMapping.reduce(
            (acc, item, index) => {
              acc[index + 1] = item.sampleValue;
              return acc;
            },
            {}
          ),
        },
      };
      const res = await AXIOS.post("v1/template/registerVoiceBot", body);
      if (res.status === 201) {
        return res.data?.voice;
      }
      message.success("Template approved!");
    } catch (error) {
      console.log("Error in whats app template", error?.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle blaster approval
  const handleBlasterApproval = async () => {
    setIsLoading(true);
    try {
      const body = {
        Name: template.name,
        Language: template.language,
        TemplateText: template.scriptValue,
        VariableMapping: template.variablesMapping.reduce(
          (acc, item, index) => {
            acc[index + 1] = item.columnName;
            return acc;
          },
          {}
        ),
        SampleMapping: template.variablesMapping.reduce((acc, item, index) => {
          acc[index + 1] = item.sampleValue;
          return acc;
        }, {}),
      };
      const res = await AXIOS.post("v1/template/registerBlaster", body);
      if (res.status === 201) {
        return res.data?.blaster;
      }
      message.success("Template approved!");
    } catch (error) {
      console.log("Error in whats app template", error?.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle calling approval
  const handleCallingApproval = async () => {
    setIsLoading(true);
    try {
      const body = {
        Name: template.name,
        Language: template.language,
        TemplateText: template.scriptValue,
      };
      const res = await AXIOS.post("v1/template/registerCalling", body);
      if (res.status === 201) {
        return res.data?.calling;
      }
      message.success("Template approved!");
    } catch (error) {
      console.log("Error in calling template", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle blaster approval
  const handleSmsApproval = async () => {
    setIsLoading(true);
    try {
      const body = {
        Name: template.name,
        Language: template.language,
        TemplateText: template.scriptValue,
        VariableMapping: template.variablesMapping.reduce(
          (acc, item, index) => {
            acc[index + 1] = item.columnName;
            return acc;
          },
          {}
        ),
        SampleMapping: template.variablesMapping.reduce((acc, item, index) => {
          acc[index + 1] = item.sampleValue;
          return acc;
        }, {}),
      };
      const res = await AXIOS.post("v1/template/registerSms", body);
      if (res.status === 201) {
        return res.data?.sms;
      }
      message.success("Template approved!");
    } catch (error) {
      console.log("Error in whats app template", error?.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle ivr approval
  const handleIVRApproval = async () => {
    setIsLoading(true);
    try {
      const body = {
        Name: template.name,
        Language: template.language,
        TemplateText: JSON.stringify(transformTemplatesNodes()),
      };
      const res = await AXIOS.post("v1/template/registerIVR", body);
      if (res.status === 201 || res.status === 200) {
        return res.data?.ivr;
      }
      message.success("Template approved!");
    } catch (error) {
      console.log("Error in whats app template", error?.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle Approval template
  const handleApprovalTemplate = async () => {
    if (isWhatsapp) {
      return await handleWhatsappApproval();
    } else if (isAi) {
      return await handleAiApproval();
    } else if (isBlaster) {
      return await handleBlasterApproval();
    } else if (isIVR) {
      return await handleIVRApproval();
    } else if (isCalling) {
      return await handleCallingApproval();
    } else {
      return await handleSmsApproval();
    }
  };

  // Handle submit approval
  const handleSubmitApproval = async () => {
    let createdNewTemplateId = null;

    // Common Validations (Applied to ALL Templates)
    const isCommonValid =
      template.name?.trim() !== "" &&
      template.language &&
      template.communicationType &&
      template.selectedTemplateName &&
      (!isWhatsapp ||
        template.buttonsMapping?.every(
          (button) => button.buttonName && button.buttonResponse
        ));
    // &&(!isIVR || template.response?.every(resp => resp.buttonName && resp.text?.trim() !== ""));

    // AI Template Validation (Skip scriptValue & variableMapping)
    const isAiValid = isAi
      ? template.interactionPlans?.trim() !== "" &&
        template.rules?.trim() !== "" &&
        template.description?.trim() !== "" &&
        template.initialTexts?.trim() !== "" &&
        template.endingTexts?.trim() !== "" &&
        template.initialTextVariableMapping?.every(
          (text) => text.columnName && text.sampleValue
        ) &&
        template.endingTextVariableMapping?.every(
          (text) => text.columnName && text.sampleValue
        )
      : true; // If not AI, skip this check

    // **IVR Template Validation**
    const isIvrValid = isIVR
      ? template.nodes.length > 0 &&
        (template.nodeButtonMapping?.length === 0 ||
          template.nodeButtonMapping?.every(
            (button) => button.buttonReportedNode
          )) && // Correct property
        template.nodes.every(
          (node) =>
            node.name.trim() !== "" &&
            node.response.trim() !== "" &&
            (node.variableMapping?.length === 0 ||
              node.variableMapping?.every(
                (variable) =>
                  variable.columnName &&
                  variable.sampleValue !== "" &&
                  variable.sampleValue != null
              )) &&
            (node.nodeButtonMapping?.length === 0 ||
              node.nodeButtonMapping?.every(
                (button) => button.buttonReportedNode
              )) // Correct property
        )
      : true; // If not IVR, skip this check

    // Non-AI Template Validation (Check scriptValue & variableMapping only for non-AI)
    const isNonAiValid = !isAi
      ? template.scriptValue?.trim() !== "" &&
        template.variablesMapping?.every(
          (variable) =>
            variable.columnName &&
            variable.sampleValue !== "" &&
            variable.sampleValue != null
        )
      : true; // If AI, skip this check

    // Final validation check
    const isTemplateValid =
      isCommonValid && isAiValid && isIvrValid && isNonAiValid;
    if (!isTemplateValid) {
      message.error("Please fill all the required fields before submitting.");
      return;
    }

    // Call API for approval
    createdNewTemplateId = await handleApprovalTemplate();

    if (!createdNewTemplateId) {
      messageApi.error("Template creation failed. Please try again.");
      return;
    }

    // Mark template as approved
    setCampaignData((prevData) => {
      const updatedTemplates = prevData.templates.map((t, i) =>
        i === index
          ? {
              ...t,
              showScripting: false,
              approvedTemplateId: createdNewTemplateId,
            }
          : t
      );
      return { ...prevData, templates: updatedTemplates };
    });

    message.success("Template approved successfully!");
  };

  // Fetch all languages
  const handleFetchLanguage = async () => {
    try {
      const response = await AXIOS.get("v1/languages/");
      if (response.status === 200) {
        const updatesLanguages = response.data
          ?.filter(({ IsActive }) => IsActive)
          .map(({ Language }) => ({
            label: Language,
            value: Language,
          }));
        setLanguageOptions(updatesLanguages);
      }
    } catch (error) {
      console.log("Error in Languages", error);
    }
  };

  // Handle Clear all
  const handleClearAll = () => {
    setCampaignData((prevData) => {
      return {
        ...prevData,
        templates: prevData.templates.map((template) => ({
          ...template,
          approvedTemplateId: null,
          scriptValue: "",
          interactionPlans: "",
          rules: "",
          description: "",
          initialTexts: "",
          endingTexts: "",
          initialTextVariableMapping: [],
          endingTextVariableMapping: [],
          showScripting: true,
          variablesMapping: [],
          buttonsMapping: [],
          intents: [{ name: "", response: "" }],
          response: [{ buttonName: "", text: "" }],
          nodes: [],
          nodeButtonMapping: [],
          isIvrFlowApproved: false,
        })),
      };
    });
  };

  // Called the get apis
  useEffect(() => {
    handleFetchLanguage();
    handleFetchVariables();
  }, []);

  return (
    <>
      {contextHolder}
      <Flex vertical gap={10}>
        <Row
          align="center"
          justify="space-between"
          gutter={[20, 10]}
          className={Style.filters_container}
        >
          <Col span={12} className={Style.custom_flex}>
            <Text className={Style.input_text}>Enter Template Name:</Text>
            <Input
              placeholder="Enter"
              value={template?.name}
              className={Style.custom_input}
              onChange={(e) => handleTemplateName(e.target.value)}
            />
          </Col>

          <Col span={12} className={Style.custom_flex}>
            <Text className={Style.input_text}>Select Language:</Text>
            <Select
              className={Style.custom_select}
              placeholder="Select"
              options={languageOptions}
              value={template?.language}
              onChange={(value) => handleLanguageName(value)}
            />
          </Col>
        </Row>

        <Flex justify={`${isIVR ? "space-between" : "end"}`} align={"center"}>
          {isIVR ? <Text className={Style.text}>Initial Text:</Text> : null}
          <Button className={Style.clear} onClick={handleClearAll}>
            Clear all
          </Button>
        </Flex>

        {!isAi ? (
          <Flex
            className={Style.text_container}
            justify="space-between"
            vertical
            gap={10}
          >
            {/* Text area */}
            <Flex gap={5}>
              <Avatar size={25} src={AVATAR_IMG} className={Style.avatar} />
              <TextArea
                value={template?.scriptValue}
                placeholder="Type something here...."
                className={Style.custom_textarea}
                autoSize={{ minRows: 1 }}
                style={{ height: "120px", resize: "none" }}
                onChange={(e) =>
                  handleTextOnChange({
                    value: e.target.value,
                    fieldName: "scriptValue",
                  })
                }
              />
            </Flex>

            {/* Add variable */}
            <Flex justify="end" gap={10}>
              {isWhatsapp && (
                <Button
                  className={Style.add_variable}
                  onClick={handleAddButtons}
                >
                  Add Button
                </Button>
              )}
              {isIVR && (
                <>
                  {template?.nodes?.length > 0 && (
                    <Button
                      className={Style.add_variable}
                      onClick={handleAddIVRNodeButton}
                    >
                      Add Button
                    </Button>
                  )}
                  <Button
                    className={Style.add_variable}
                    onClick={handleAddIvrNodes}
                  >
                    Add Node
                  </Button>
                </>
              )}
              <Button
                className={Style.add_variable}
                onClick={handleAddVariable}
              >
                Add variable
              </Button>
            </Flex>
          </Flex>
        ) : (
          <Flex vertical gap={10}>
            {/* description */}
            <Flex vertical gap={10}>
              <Text className={Style.text}>Description:</Text>
              <Flex gap={5} className={Style.text_container}>
                <Avatar size={25} src={AVATAR_IMG} className={Style.avatar} />
                <TextArea
                  value={template?.description}
                  placeholder="Type something here...."
                  className={Style.custom_textarea}
                  autoSize={{ minRows: 1 }}
                  style={{ height: "120px", resize: "none" }}
                  onChange={(e) =>
                    handleTextOnChange({
                      value: e.target.value,
                      fieldName: "description",
                    })
                  }
                />
              </Flex>
            </Flex>

            {/* rules */}
            <Flex vertical gap={10}>
              <Text className={Style.text}>Rules:</Text>
              <Flex gap={5} className={Style.text_container}>
                <Avatar size={25} src={AVATAR_IMG} className={Style.avatar} />
                <TextArea
                  value={template?.rules}
                  placeholder="Type something here...."
                  className={Style.custom_textarea}
                  autoSize={{ minRows: 1 }}
                  style={{ height: "120px", resize: "none" }}
                  onChange={(e) =>
                    handleTextOnChange({
                      value: e.target.value,
                      fieldName: "rules",
                    })
                  }
                />
              </Flex>
            </Flex>

            {/* Interaction plans */}
            <Flex vertical gap={10}>
              <Text className={Style.text}>Interaction plans:</Text>
              <Flex gap={5} className={Style.text_container}>
                <Avatar size={25} src={AVATAR_IMG} className={Style.avatar} />
                <TextArea
                  value={template?.interactionPlans}
                  placeholder="Type something here...."
                  className={Style.custom_textarea}
                  autoSize={{ minRows: 1 }}
                  style={{ height: "120px", resize: "none" }}
                  onChange={(e) =>
                    handleTextOnChange({
                      value: e.target.value,
                      fieldName: "interactionPlans",
                    })
                  }
                />
              </Flex>
            </Flex>

            {/*Initial text  */}
            <>
              <Flex vertical gap={10}>
                <Text className={Style.text}>Initial text:</Text>
                <Flex
                  className={Style.text_container}
                  justify="space-between"
                  vertical
                  gap={10}
                >
                  {/* Text area */}
                  <Flex gap={5}>
                    <Avatar
                      size={25}
                      src={AVATAR_IMG}
                      className={Style.avatar}
                    />
                    <TextArea
                      value={template?.initialTexts}
                      placeholder="Type something here...."
                      className={Style.custom_textarea}
                      autoSize={{ minRows: 1 }}
                      style={{ height: "120px", resize: "none" }}
                      onChange={(e) =>
                        handleTextOnChange({
                          value: e.target.value,
                          fieldName: "initialTexts",
                        })
                      }
                    />
                  </Flex>

                  {/* Add variable */}
                  <Flex justify="end" gap={10}>
                    <Button
                      className={Style.add_variable}
                      onClick={handleAddInitialTextVariable}
                    >
                      Add variable
                    </Button>
                  </Flex>
                </Flex>
              </Flex>
              {/* initialTextVariableMapping mapping */}
              {template?.initialTextVariableMapping.length ? (
                <Flex vertical gap={15} className={Style.variables_container}>
                  <Text className={Style.text}>Variable Mapping:</Text>
                  {template?.initialTextVariableMapping?.map(
                    (variable, index) => (
                      <Flex
                        key={`${index}-${variable.columnName}`}
                        gap={10}
                        align="center"
                        justify="space-between"
                        className={Style.container}
                      >
                        <Flex align="center" gap={15}>
                          <span className={Style.text}>{`{${index + 1}}`}</span>

                          <Flex align="center" gap={20}>
                            <Text className={Style.text}>
                              Select Column Name:
                            </Text>
                            <Select
                              showSearch
                              className={Style.custom_select}
                              placeholder="Select"
                              options={[...variableMappingOptions].sort(
                                (a, b) => a.label.localeCompare(b.label)
                              )}
                              value={variable?.columnName}
                              onChange={(value) =>
                                handleInitialTextVariableMapping({
                                  varIndex: index,
                                  field: "columnName",
                                  value,
                                })
                              }
                            />
                          </Flex>

                          <Flex align="center" gap={20}>
                            <Text className={Style.text}>
                              Enter Sample Value:
                            </Text>
                            {variableMappingTypes[variable?.columnName] ===
                              "date" && (
                              <DatePicker
                                format="DD-MM-YYYY"
                                className={Style.date_picker}
                                onChange={(date) => {
                                  handleInitialTextVariableMapping({
                                    varIndex: index,
                                    field: "sampleValue",
                                    value: dayjs(date).format("YYYY-MM-DD"),
                                  });
                                }}
                              />
                            )}

                            {variableMappingTypes[variable?.columnName] ===
                              "number" && (
                              <InputNumber
                                placeholder="Enter"
                                value={variable?.sampleValue}
                                className={Style.custom_input_number}
                                onChange={(value) =>
                                  handleInitialTextVariableMapping({
                                    varIndex: index,
                                    field: "sampleValue",
                                    value,
                                  })
                                }
                              />
                            )}

                            {variableMappingTypes[variable?.columnName] !==
                              "date" &&
                              variableMappingTypes[variable?.columnName] !==
                                "number" && (
                                <Input
                                  placeholder="Enter"
                                  value={variable?.sampleValue}
                                  className={Style.custom_input}
                                  onChange={(e) =>
                                    handleInitialTextVariableMapping({
                                      varIndex: index,
                                      field: "sampleValue",
                                      value: e.target.value,
                                    })
                                  }
                                />
                              )}
                          </Flex>
                        </Flex>
                      </Flex>
                    )
                  )}
                </Flex>
              ) : null}
            </>

            {/*Ending text  */}
            <>
              <Flex vertical gap={10}>
                <Text className={Style.text}>Ending text:</Text>
                <Flex
                  className={Style.text_container}
                  justify="space-between"
                  vertical
                  gap={10}
                >
                  {/* Text area */}
                  <Flex gap={5}>
                    <Avatar
                      size={25}
                      src={AVATAR_IMG}
                      className={Style.avatar}
                    />
                    <TextArea
                      value={template?.endingTexts}
                      placeholder="Type something here...."
                      className={Style.custom_textarea}
                      autoSize={{ minRows: 1 }}
                      style={{ height: "120px", resize: "none" }}
                      onChange={(e) =>
                        handleTextOnChange({
                          value: e.target.value,
                          fieldName: "endingTexts",
                        })
                      }
                    />
                  </Flex>

                  {/* Add variable */}
                  <Flex justify="end" gap={10}>
                    <Button
                      className={Style.add_variable}
                      onClick={handleAddEndTextVariable}
                    >
                      Add variable
                    </Button>
                  </Flex>
                </Flex>
              </Flex>
              {/* Ending text variableMapping mapping */}
              {template?.endingTextVariableMapping.length ? (
                <Flex vertical gap={15} className={Style.variables_container}>
                  <Text className={Style.text}>Variable Mapping:</Text>
                  {template?.endingTextVariableMapping?.map(
                    (variable, index) => (
                      <Flex
                        key={`${index}-${variable.columnName}`}
                        gap={10}
                        align="center"
                        justify="space-between"
                        className={Style.container}
                      >
                        <Flex align="center" gap={15}>
                          <span className={Style.text}>{`{${index + 1}}`}</span>

                          <Flex align="center" gap={20}>
                            <Text className={Style.text}>
                              Select Column Name:
                            </Text>
                            <Select
                              showSearch
                              className={Style.custom_select}
                              placeholder="Select"
                              options={[...CAMPAIGN_DROPDOWN_DATA].sort(
                                (a, b) => a.label.localeCompare(b.label)
                              )}
                              value={variable?.columnName}
                              onChange={(value) =>
                                handleEndingTextVariableMapping({
                                  varIndex: index,
                                  field: "columnName",
                                  value,
                                })
                              }
                            />
                          </Flex>

                          <Flex align="center" gap={20}>
                            <Text className={Style.text}>
                              Enter Sample Value:
                            </Text>
                            {DATE_FIELDS.includes(variable?.columnName) ? (
                              <DatePicker
                                format="DD-MM-YYYY"
                                className={Style.date_picker}
                                onChange={(date) => {
                                  handleEndingTextVariableMapping({
                                    varIndex: index,
                                    field: "sampleValue",
                                    value: dayjs(date).format("YYYY-MM-DD"),
                                  });
                                }}
                              />
                            ) : (
                              <Input
                                placeholder="Enter"
                                value={variable?.sampleValue}
                                className={Style.custom_input}
                                onChange={(e) =>
                                  handleEndingTextVariableMapping({
                                    varIndex: index,
                                    field: "sampleValue",
                                    value: e.target.value,
                                  })
                                }
                              />
                            )}
                          </Flex>
                        </Flex>
                      </Flex>
                    )
                  )}
                </Flex>
              ) : null}
            </>
          </Flex>
        )}

        {/* Nodes Button Mappings */}
        {template?.nodeButtonMapping?.length && isIVR ? (
          <Flex vertical gap={1} className={Style.variables_container}>
            <Text className={Style.text}>Button Mapping:</Text>
            {template.nodeButtonMapping.map((variable, nodeVarIndex) => (
              <Flex
                key={`${nodeVarIndex}-${variable.buttonReportedNode}`}
                gap={10}
                align="center"
                justify="space-between"
                className={Style.container}
              >
                <Flex align="center" gap={15}>
                  <span className={Style.text}>{`{${nodeVarIndex + 1}}`}</span>

                  <Flex align="center" gap={20}>
                    <Text className={Style.text}>Report Node: </Text>
                    <Select
                      showSearch
                      className={Style.custom_select}
                      placeholder="Select"
                      options={nodeOptions}
                      value={variable?.buttonReportedNode || null} // Ensure value is not undefined
                      onChange={(value) =>
                        handleEditIvrNodeButton({
                          varIndex: nodeVarIndex,
                          field: "buttonReportedNode",
                          value,
                        })
                      }
                    />
                  </Flex>

                  <Flex justify="end">
                    <Button
                      className={Style.submit_variable}
                      style={{ background: "#DB3939" }}
                      onClick={handleDeleteIvrNodeButton.bind(null, {
                        nodeIndex: nodeVarIndex,
                      })}
                    >
                      Remove
                    </Button>
                  </Flex>
                </Flex>
              </Flex>
            ))}
          </Flex>
        ) : null}

        {/* variable mapping */}
        {template?.variablesMapping.length ? (
          <Text className={Style.text}>Variable Mapping:</Text>
        ) : null}
        <Flex vertical gap={15} className={Style.variables_container}>
          {template?.variablesMapping.map((variable, index) => (
            <Flex
              key={`${index}-${variable.columnName}`}
              gap={10}
              align="center"
              justify="space-between"
              className={Style.container}
            >
              <Flex align="center" gap={15}>
                <span className={Style.text}>{`{${index + 1}}`}</span>

                <Flex align="center" gap={20}>
                  <Text className={Style.text}>Column Name: </Text>
                  <Select
                    showSearch
                    className={Style.custom_select}
                    placeholder="Select"
                    options={[...variableMappingOptions].sort((a, b) =>
                      a.label.localeCompare(b.label)
                    )}
                    value={variable?.columnName}
                    onChange={(value) =>
                      handleVariablesChange({
                        varIndex: index,
                        field: "columnName",
                        value,
                      })
                    }
                  />
                </Flex>

                <Flex align="center" gap={20}>
                  <Text className={Style.text}>Sample Value:</Text>
                  {variableMappingTypes[variable.columnName] === "date" && (
                    <DatePicker
                      format="DD-MM-YYYY"
                      className={Style.date_picker}
                      onChange={(date) => {
                        handleVariablesChange({
                          varIndex: index,
                          field: "sampleValue",
                          value: dayjs(date).format("YYYY-MM-DD"),
                        });
                      }}
                    />
                  )}
                  {variableMappingTypes[variable.columnName] === "number" && (
                    <InputNumber
                      placeholder="Enter"
                      value={variable?.sampleValue}
                      className={Style.custom_input_number}
                      onChange={(value) =>
                        handleVariablesChange({
                          varIndex: index,
                          field: "sampleValue",
                          value,
                        })
                      }
                    />
                  )}
                  {variableMappingTypes[variable?.columnName] !== "date" &&
                    variableMappingTypes[variable?.columnName] !== "number" && (
                      <Input
                        placeholder="Enter"
                        value={variable?.sampleValue}
                        className={Style.custom_input}
                        onChange={(e) =>
                          handleVariablesChange({
                            varIndex: index,
                            field: "sampleValue",
                            value: e.target.value,
                          })
                        }
                      />
                    )}
                </Flex>

                <Flex>
                  <Button
                    style={{
                      background: "#DB3939",
                      color: "white",
                      fontSize: "12px",
                    }}
                    onClick={() => handleVariableDelete(index)}
                  >
                    Delete
                  </Button>
                </Flex>
              </Flex>
            </Flex>
          ))}
        </Flex>

        {/* buttons mapping */}
        {template?.buttonsMapping.length && isWhatsapp ? (
          <>
            <Text className={Style.text}>Button Mapping:</Text>
            <Flex vertical gap={15} className={Style.variables_container}>
              {template?.buttonsMapping.map((variable, index) => (
                <Flex
                  key={`${index}-${variable.buttonName}`}
                  gap={10}
                  align="center"
                  justify="space-between"
                  className={Style.container}
                >
                  <Flex align="center" gap={15}>
                    <span className={Style.text}>{`{${index + 1}}`}</span>

                    <Flex align="center" gap={20}>
                      <Text className={Style.text}>Button Name: </Text>
                      <Input
                        placeholder="Enter"
                        value={variable?.buttonName}
                        className={Style.custom_input}
                        onChange={(e) =>
                          handleButtonsChange({
                            btnIndex: index,
                            field: "buttonName",
                            value: e.target.value,
                          })
                        }
                      />
                    </Flex>

                    <Flex align="center" gap={20}>
                      <Text className={Style.text}>Button Response:</Text>
                      <Input
                        placeholder="Enter"
                        value={variable?.buttonResponse}
                        className={Style.custom_input}
                        onChange={(e) =>
                          handleButtonsChange({
                            btnIndex: index,
                            field: "buttonResponse",
                            value: e.target.value,
                          })
                        }
                      />
                    </Flex>

                    <Flex justify="end">
                      <input
                        type="image"
                        src={DELETE_IMG}
                        alt="delete"
                        style={{ cursor: "pointer", width: "9px" }}
                        onClick={() => handleDeleteButton(index)}
                      />
                    </Flex>
                  </Flex>
                </Flex>
              ))}
            </Flex>
          </>
        ) : null}

        {/*Add response  */}
        {template?.nodes?.length && isIVR ? (
          <IvrNewTemplateNormal
            template={template}
            templateIndex={index}
            setCampaignData={setCampaignData}
          />
        ) : null}

        {/* submit button */}
        <Flex justify="center">
          <Button
            className={Style.submit_variable}
            loading={isLoading}
            onClick={handleSubmitApproval}
          >
            Submit for approval
          </Button>
        </Flex>
      </Flex>
    </>
  );
}

CreateAiNewTemplate.propTypes = {
  template: PropTypes.object,
  index: PropTypes.number,
  campaignData: CAMPAIGN_DATA_PROP_TYPES,
  setCampaignData: PropTypes.func,
  commFlowData: PropTypes.array,
};

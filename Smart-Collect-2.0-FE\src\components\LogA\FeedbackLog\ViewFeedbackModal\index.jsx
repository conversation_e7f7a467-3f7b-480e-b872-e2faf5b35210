import { Mo<PERSON>, Ta<PERSON>, Typography, Flex } from "antd";
import React, { useEffect, useState } from "react";
import FEEDBACK_IMG from "../../../../assets/Images/fluent-mdl2_feedback.png";
import Style from "./_feedback.module.scss";
import { ReadyToPay } from "./ReadyToPay";
import { RefusedToPay } from "./RefusedToPay";
import { Collection } from "./Collection";
import { Other } from "./Other";
import PropTypes from "prop-types";

const { Title } = Typography;

export function ViewFeedbackModal({modalStatus = false,  feedbackData}) {
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);

  const handleOk = () => {
    setIsFeedbackModalOpen(false);
  };

  const handleCross = () => {
    setIsFeedbackModalOpen(false);
  };

  useEffect(() => {
    setIsFeedbackModalOpen(modalStatus);
  }, [feedbackData, modalStatus]);

  const items = [
    feedbackData?.AgreedToPay && {
      key: `readyToPay-${feedbackData?.key}`,
      label: "Ready to Pay",
      children: <ReadyToPay feedbackData={feedbackData} />,
    },

    feedbackData?.RefusedToPay && {
      key: `refusedToPay-${feedbackData?.key}`,
      label: "Refused to Pay",
      children: <RefusedToPay feedbackData={feedbackData} />,
    },

    feedbackData?.CollectionDate && {
      key: `collection-${feedbackData?.key}`,
      label: "Collection",
      children: <Collection feedbackData={feedbackData} />,
    },
    feedbackData?.WrongNumber && feedbackData?.CustomerNotReply && {
      key: `other-${feedbackData?.key}`,
      label: "Other",
      children: <Other feedbackData={feedbackData} />,
    }
  ].filter(Boolean);
  return (
    <Modal
      centered
      className={Style.feedback_modal}
      title={
        <Flex align="center" gap={5} justify="center">
          <div className={Style.icon_img}>
            <img src={FEEDBACK_IMG} alt="icon" />
          </div>
          <Title level={4} className={Style.feedback_title}>
            Feedback
          </Title>
        </Flex>
      }
      open={isFeedbackModalOpen}
      onOk={handleOk}
      onCancel={handleCross}
      footer={null}
    >
      <Flex vertical gap={10}>
        <Tabs items={items}/>
      </Flex>
    </Modal>
  );
}

ViewFeedbackModal.propTypes={
  modalStatus: PropTypes.bool,
  feedbackData: PropTypes.object
}
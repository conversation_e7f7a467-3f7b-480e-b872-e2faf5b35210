@import url("../../index.css");

$light-blue: #e4f8f9;

.container {
  .title{
    font-family: 'Kanit','Courier New', Courier, monospace;
    font-weight: 500;
    font-size: 18px;
    color: var(--blue);
    margin: 0.5rem 0;
  }

  .date_text{
    font-family: 'Kanit','Courier New', Courier, monospace;
    font-size: 17px;
    font-weight: 500;
    color: var(--dark-blue);
  }

  .date_picker {
    border: none;
    outline: none;
    box-shadow: none;
    border-bottom: 1px solid var(--dark-blue);
    background-color: $light-blue;
    :global(.ant-picker-input > input) {
      font-weight: 300;
      color: var(--dark-blue);
      font-family: 'Kanit','Courier New', Courier, monospace;

      &::placeholder {
        font-weight: 600;
      }
    }
    &::placeholder {
      color: #0f20504a;
      font-weight: 600;
    }
  }

  .details_row{
    margin-top: 0.5rem;
  }
}

// Responsive View
@media only screen and (max-width: 768px) {
  .container{
    margin-top: 1rem;
    .title{
      font-size: 14px;
    }
    .date_text{
      font-size: 14px;
    }
    .date_picker{
      :global(.ant-picker-input > input) {
        font-size: 12px;
      }
    }
  }
}
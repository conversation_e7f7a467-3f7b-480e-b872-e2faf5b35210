# db_manager.py - Production configuration for banking applications
import os
import logging
import time
import threading
import atexit
from sqlalchemy import create_engine, event, text
from sqlalchemy.pool import QueuePool
from datetime import datetime
from functools import wraps

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def singleton_with_lock(cls):
    """Thread-safe singleton decorator with lock"""
    instances = {}
    lock = threading.Lock()
    
    @wraps(cls)
    def get_instance(*args, **kwargs):
        if cls not in instances:
            with lock:
                # Double-check pattern
                if cls not in instances:
                    instances[cls] = cls(*args, **kwargs)
        return instances[cls]
    return get_instance

@singleton_with_lock
class ProductionDatabaseManager:
    """Production-grade database manager for banking applications"""
    
    def __init__(self):
        self._engine = None
        self._initialization_lock = threading.Lock()
        self._initialized = False
        self._process_id = os.getpid()
        self._initialization_timestamp = None
        
        # Register cleanup on exit
        atexit.register(self.cleanup)
        
        # Log process information
        logger.info(f"DatabaseManager instance created in process {self._process_id}")
    
    def initialize_engine(self, force_reinit=False):
        """Initialize with production-grade settings for banking workloads"""
        
        # Check if we're in a different process (forked)
        current_pid = os.getpid()
        if self._process_id != current_pid:
            logger.warning(f"Process fork detected: {self._process_id} -> {current_pid}")
            self._reset_for_new_process(current_pid)
        
        # Thread-safe initialization
        with self._initialization_lock:
            if self._initialized and not force_reinit:
                logger.debug(f"Engine already initialized in process {current_pid}")
                return self._engine
            
            if self._engine is not None and not force_reinit:
                logger.debug("Engine exists, skipping initialization")
                return self._engine
            
            try:
                self._perform_initialization()
                self._initialized = True
                self._initialization_timestamp = datetime.now()
                
                logger.info(f"✅ Database engine initialized successfully in process {current_pid}")
                logger.info(f"   - Pool: 20 base + 30 overflow (max: 50)")
                logger.info(f"   - Timeout: 20s")
                logger.info(f"   - Initialization time: {self._initialization_timestamp}")
                
            except Exception as e:
                self._initialized = False
                self._engine = None
                logger.error(f"❌ CRITICAL: Failed to initialize database engine: {e}")
                raise
                
        return self._engine
    
    def _reset_for_new_process(self, new_pid):
        """Reset state for new process (after fork)"""
        logger.info(f"Resetting database manager for new process {new_pid}")
        self._engine = None
        self._initialized = False
        self._process_id = new_pid
        self._initialization_timestamp = None
    
    def _perform_initialization(self):
        """Perform actual engine initialization"""
        connection_string = (
            f"postgresql://{os.environ.get('DATABASE_USER')}:"
            f"{os.environ.get('DATABASE_PASSWORD')}@"
            f"{os.environ.get('DATABASE_HOST')}:"
            f"{os.environ.get('DATABASE_PORT')}/"
            f"{os.environ.get('DATABASE_NAME')}"
        )
        
        # Production settings for high-volume banking applications
        self._engine = create_engine(
            connection_string,
            
            # Connection Pool Settings - Optimized for banking workloads
            poolclass=QueuePool,       # Best for high concurrency
            pool_size=10,              # Base connections (increased from 5)
            max_overflow=20,           # Extra connections when busy (total max: 50)
            
            # Connection Health & Lifecycle
            pool_pre_ping=True,        # Essential for production reliability
            pool_recycle=3600,         # Recycle every hour
            
            # Timeout Settings - Aggressive for banking responsiveness
            pool_timeout=20,           # Wait 20s for connection from pool
            connect_args={
                "connect_timeout": 15,              # 15s to establish connection
                "application_name": f"SmartCollect_Banking_PID_{self._process_id}",
                
                # PostgreSQL performance optimizations
                "options": "-c statement_timeout=300000 "      # 5 minutes
                          "-c idle_in_transaction_session_timeout=600000 "  # 10 minutes
                          "-c tcp_keepalives_idle=300 "       # Keep connections alive
                          "-c tcp_keepalives_interval=30 "
                          "-c tcp_keepalives_count=3"
            },
            
            # Performance & Debugging
            echo=False,                # Set to True only for debugging
            echo_pool=False,          # Pool debugging
            future=True               # Use SQLAlchemy 2.0 style
        )
        
        # Add connection event listeners for monitoring
        self._setup_connection_events()
        
        # Test the connection
        self._test_connection()
    
    def _test_connection(self):
        """Test database connection"""
        with self._engine.connect() as conn:
            result = conn.execute(text("SELECT version(), current_database(), current_user, pg_backend_pid()"))
            db_info = result.fetchone()
            logger.info(f"Database connection test successful:")
            logger.info(f"  - Database: {db_info[1]}")
            logger.info(f"  - User: {db_info[2]}")
            logger.info(f"  - Backend PID: {db_info[3]}")
    
    def _setup_connection_events(self):
        """Setup connection monitoring events"""
        
        @event.listens_for(self._engine, "connect")
        def set_connection_settings(dbapi_connection, connection_record):
            """Optimize individual connections"""
            with dbapi_connection.cursor() as cursor:
                # Set connection-level optimizations
                cursor.execute("SET work_mem = '32MB'")
                cursor.execute("SET temp_buffers = '32MB'")
                cursor.execute("SET random_page_cost = 1.1")
                cursor.execute("SET effective_cache_size = '256MB'")
        
        @event.listens_for(self._engine, "checkout")
        def receive_checkout(dbapi_connection, connection_record, connection_proxy):
            """Log when connections are checked out"""
            connection_record.checkout_time = datetime.now()
        
        @event.listens_for(self._engine, "checkin")
        def receive_checkin(dbapi_connection, connection_record):
            """Log connection usage time"""
            if hasattr(connection_record, 'checkout_time'):
                usage_time = datetime.now() - connection_record.checkout_time
                if usage_time.total_seconds() > 30:
                    logger.warning(f"Long connection usage: {usage_time.total_seconds():.2f}s")
    
    def get_engine(self):
        """Get the engine instance (lazy initialization)"""
        if not self._initialized or self._engine is None:
            self.initialize_engine()
        return self._engine
    
    def get_detailed_pool_status(self):
        """Get detailed pool status for production monitoring"""
        if not self._engine:
            return {"status": "not_initialized", "process_id": self._process_id}
        
        pool = self._engine.pool
        status = {
            "process_id": self._process_id,
            "initialized_at": self._initialization_timestamp.isoformat() if self._initialization_timestamp else None,
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "total_connections": pool.checkedin() + pool.checkedout(),
            "utilization_percent": round((pool.checkedout() / 50) * 100, 2),
            "timestamp": datetime.now().isoformat()
        }
        
        # Add health indicators
        if status["utilization_percent"] > 90:
            status["health"] = "CRITICAL"
            status["recommendation"] = "Increase pool size immediately"
        elif status["utilization_percent"] > 75:
            status["health"] = "WARNING"
            status["recommendation"] = "Monitor closely, consider increasing pool"
        elif status["utilization_percent"] > 50:
            status["health"] = "CAUTION"
            status["recommendation"] = "Normal high usage"
        else:
            status["health"] = "HEALTHY"
            status["recommendation"] = "Pool size adequate"
        
        return status
    
    def execute_query_with_retry(self, query, params=None, max_retries=3):
        """Execute query with automatic retry for production reliability"""
        for attempt in range(max_retries):
            try:
                engine = self.get_engine()
                with engine.connect() as connection:
                    if params:
                        result = connection.execute(text(query), params)
                    else:
                        result = connection.execute(text(query))
                    return result.fetchall()
                    
            except Exception as e:
                logger.warning(f"Query attempt {attempt + 1} failed: {e}")
                if attempt == max_retries - 1:
                    logger.error(f"All {max_retries} query attempts failed for query: {query}")
                    raise
                time.sleep(0.5 * (attempt + 1))
    
    def execute_query_db(self, query, params=None, max_retries=3):
        """Execute query and return rows with columns"""
        for attempt in range(max_retries):
            try:
                engine = self.get_engine()
                with engine.connect() as connection:
                    result = connection.execute(text(query), params or {})
                    rows = result.fetchall()
                    columns = result.keys()
                    return rows, columns
            except Exception as e:
                logger.warning(f"Query attempt {attempt + 1} failed: {e}")
                if attempt == max_retries - 1:
                    logger.error(f"All {max_retries} query attempts failed for query: {query}")
                    raise
                time.sleep(0.5 * (attempt + 1))
                
    def execute_query_with_positional_params(self, query, params=None, max_retries=3):
        """Execute query with positional parameters for psycopg2-style queries"""
        for attempt in range(max_retries):
            try:
                engine = self.get_engine()
                with engine.connect() as connection:
                    # Use raw connection to handle %s placeholders correctly
                    raw_connection = connection.connection
                    with raw_connection.cursor() as cursor:
                        if params:
                            cursor.execute(query, params)
                        else:
                            cursor.execute(query)
                        
                        # Fetch results
                        rows = cursor.fetchall()
                        # Get column names
                        columns = [desc[0] for desc in cursor.description] if cursor.description else []
                        
                        return rows, columns
                        
            except Exception as e:
                logger.warning(f"Query attempt {attempt + 1} failed: {e}")
                if attempt == max_retries - 1:
                    logger.error(f"All {max_retries} query attempts failed for query: {query}")
                    raise
                time.sleep(0.5 * (attempt + 1))
    
    def execute_query_with_named_params(self, query, params=None, max_retries=3):
        """Execute query with named parameters for SQLAlchemy text() queries"""
        for attempt in range(max_retries):
            try:
                engine = self.get_engine()
                with engine.connect() as connection:
                    if params:
                        result = connection.execute(text(query), params)
                    else:
                        result = connection.execute(text(query))
                    rows = result.fetchall()
                    columns = list(result.keys())
                    return rows, columns
            except Exception as e:
                logger.warning(f"Query attempt {attempt + 1} failed: {e}")
                if attempt == max_retries - 1:
                    logger.error(f"All {max_retries} query attempts failed for query: {query}")
                    raise
                time.sleep(0.5 * (attempt + 1))
    
    def health_check(self):
        """Comprehensive health check"""
        try:
            if not self._initialized:
                return {"status": "not_initialized", "healthy": False}
            
            # Test connection
            with self._engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()
            
            pool_status = self.get_detailed_pool_status()
            
            return {
                "status": "healthy",
                "healthy": True,
                "process_id": self._process_id,
                "pool_health": pool_status["health"],
                "utilization": pool_status["utilization_percent"],
                "connections_active": pool_status["checked_out"],
                "initialized_at": pool_status["initialized_at"]
            }
            
        except Exception as e:
            return {"status": "error", "healthy": False, "error": str(e)}
    
    def cleanup(self):
        """Cleanup resources"""
        if self._engine:
            logger.info(f"Cleaning up database engine for process {self._process_id}")
            try:
                self._engine.dispose()
                self._engine = None
                self._initialized = False
                logger.info("Database engine cleaned up successfully")
            except Exception as e:
                logger.error(f"Error during cleanup: {e}")
    
    def close_engine(self):
        """Gracefully close the engine"""
        self.cleanup()

# Global singleton instance
db_manager = ProductionDatabaseManager()

# Health check function for monitoring
def quick_db_health_check():
    """Quick health check for monitoring systems"""
    return db_manager.health_check()

# Django integration functions
def is_django_reloader_process():
    """Check if this is Django's auto-reloader process"""
    return os.environ.get('RUN_MAIN') != 'true'

def is_django_development():
    """Check if running in Django development mode"""
    return os.environ.get('DJANGO_SETTINGS_MODULE') and 'runserver' in os.environ.get('_', '')

# Optional: Startup function for explicit initialization
def initialize_database_engine():
    """Explicit initialization function"""
    if is_django_reloader_process():
        logger.info("Skipping database initialization in Django reloader process")
        return None
    
    logger.info("Initializing database engine...")
    return db_manager.initialize_engine()
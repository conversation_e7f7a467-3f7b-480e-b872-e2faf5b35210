import pandas as pd
from .process_data import *
from ..queries import *
from ..utils import *
import numpy as np
import threading
from ..sp import execute_post_processing_procedures

def clean_mobile_number_arth(mobile):
    # Check if value is missing
    if pd.isna(mobile) or str(mobile).strip() in ['', 'nan', 'None']:
        return None
    
    # Convert to string first
    mobile_str = str(mobile).strip()
    
    # If it's a float (contains decimal point), convert to int to remove decimal
    try:
        if '.' in mobile_str:
            # Convert to float first, then to int to remove decimal part
            mobile_int = int(float(mobile_str))
            return str(mobile_int)
    except (ValueError, OverflowError):
        pass
    
    return mobile_str

def process_recovery_data(recovery_df):
    """Process RECOVERY data and return standardized DataFrame"""
    if recovery_df.empty:
        return pd.DataFrame()
    
    print("Processing RECOVERY data...")
    
    # Create processed recovery DataFrame
    rec_df = recovery_df[['Member No', 'Loan No', 'Member', 'Received Date', 'Branch Name',
                         'Collection Amount', 'Principle Collection Amount', 'Interest Collection Amount',
                         'POS', 'Loan Type', 'Loan Amount', 'EMI Amount', 'Repayment Frequency',
                         'Loan Date']].copy()
    
    # Clean data
    rec_df['Member No'] = clean_for_merge(rec_df['Member No'])
    rec_df['Loan No'] = clean_for_merge(rec_df['Loan No'])
    
    # Convert numeric columns to proper numeric types
    numeric_columns = ['Collection Amount', 'Principle Collection Amount', 'Interest Collection Amount',
                      'POS', 'Loan Amount', 'EMI Amount']
    
    for col in numeric_columns:
        rec_df[col] = pd.to_numeric(rec_df[col], errors='coerce')
    
    # Group by Loan No to get latest recovery info
    rec_df = rec_df.groupby(['Loan No'], as_index=False).agg({
        'Received Date': 'max',
        'Branch Name': 'first',
        'Member No': 'first',
        'Member': 'first',
        'POS': 'min',
        'Collection Amount': 'sum',
        'Principle Collection Amount': 'sum',
        'Interest Collection Amount': 'sum',
        'Loan Type': 'first',
        'Loan Amount': 'first',
        'EMI Amount': 'first',
        'Repayment Frequency': 'first',
        'Loan Date': 'first'
    })
    
    print(f"RECOVERY data processed - Records: {len(rec_df)}")
    return rec_df

################################### Avanti Code #####################################################################
# def process_overdue_data(overdue_df):
#     """Process OVERDUE data and return standardized DataFrame"""
    
#     # Path to the additional Excel file
#     excel_file_path = r"C:\Users\<USER>\OneDrive\Documents\Project\SmartCollect2-BE\static\artha-siddhi\Aarthsiddhi Avanti OD Report.xlsx"
    
#     # Read and append Excel file data if it exists
#     if os.path.exists(excel_file_path):
#         try:
#             print(f"Reading Excel file: {excel_file_path}")
#             excel_df = pd.read_excel(excel_file_path,skiprows=2)
#             print(f"Excel file data - Records: {len(excel_df)}")
            
#             # Check if required columns exist in Excel file
#             required_columns = ['MemberNo', 'LoanNo', 'MemberName', 'Mobile No.', 'OdDays',
#                               'LastCollDt', 'LastCollAmt', 'ODAmount', 'Oustanding', 'Branch',
#                               'LoanAmt', 'Scheme', 'LoanDt']
            
#             # Get available columns from Excel file
#             available_columns = [col for col in required_columns if col in excel_df.columns]
#             missing_columns = [col for col in required_columns if col not in excel_df.columns]
            
#             if missing_columns:
#                 print(f"Warning: Missing columns in Excel file: {missing_columns}")
#                 # Add missing columns with NaN values
#                 for col in missing_columns:
#                     excel_df[col] = pd.NA
            
#             # Select only required columns from Excel file
#             excel_df = excel_df[required_columns]
            
#             # Append Excel data to existing overdue_df
#             if not overdue_df.empty:
#                 print(f"Original OVERDUE data - Records: {len(overdue_df)}")
#                 overdue_df = pd.concat([overdue_df, excel_df], ignore_index=True)
#                 print(f"Combined data after appending Excel - Records: {len(overdue_df)}")
#             else:
#                 print("Original OVERDUE data is empty, using Excel data only")
#                 overdue_df = excel_df
                
#         except Exception as e:
#             print(f"Error reading Excel file: {str(e)}")
#             print("Continuing with original overdue_df data only")
#     else:
#         print(f"Excel file not found: {excel_file_path}")
#         print("Continuing with original overdue_df data only")
    
#     # Check if final DataFrame is empty
#     if overdue_df.empty:
#         print("No data to process")
#         return pd.DataFrame()
    
#     print("Processing combined OVERDUE data...")
    
#     # Create processed overdue DataFrame
#     od_df = overdue_df[['MemberNo', 'LoanNo', 'MemberName', 'Mobile No.', 'OdDays',
#                        'LastCollDt', 'LastCollAmt', 'ODAmount', 'Oustanding', 'Branch',
#                        'LoanAmt', 'Scheme', 'LoanDt']].copy()
    
#     # Clean MemberName - remove Aadhaar related text
#     def clean_member_name(name):
#         if pd.isna(name) or not isinstance(name, str):
#             return name
        
#         # Convert to string and strip whitespace
#         name = str(name).strip()
        
#         # Split by common separators and filter out Aadhaar-related parts
#         separators = [' - ', '-', '–', '—']
#         parts = [name]
        
#         # Split by all separators
#         for sep in separators:
#             new_parts = []
#             for part in parts:
#                 new_parts.extend(part.split(sep))
#             parts = new_parts
        
#         # Filter out parts containing 'aadhaar', 'adhar', or similar patterns
#         cleaned_parts = []
#         for part in parts:
#             part = part.strip()
#             # Skip empty parts or parts containing Aadhaar-related keywords (case insensitive)
#             if (part and 
#                 not any(keyword in part.lower() for keyword in ['aadhaar', 'adhar', 'aadhar', 'adhaar'])):
#                 cleaned_parts.append(part)
        
#         # Join the remaining parts and clean up extra spaces
#         if cleaned_parts:
#             cleaned_name = ' '.join(cleaned_parts)
#             # Remove extra spaces and return
#             return ' '.join(cleaned_name.split())
#         else:
#             # If all parts were filtered out, return the original name
#             return name
    
#     # Apply cleaning to MemberName column
#     od_df['MemberName'] = od_df['MemberName'].apply(clean_member_name)
    
#     # Clean data (assuming clean_for_merge function exists)
#     od_df['MemberNo'] = clean_for_merge(od_df['MemberNo'])
#     od_df['LoanNo'] = clean_for_merge(od_df['LoanNo'])
    
#     print(f"Final processed OVERDUE data - Records: {len(od_df)}")
#     return od_df

def process_overdue_data(overdue_df):
    """Process OVERDUE data and return standardized DataFrame"""
    if overdue_df.empty:
        return pd.DataFrame()
    
    print("Processing OVERDUE data...")
    
    # Create processed overdue DataFrame
    od_df = overdue_df[['MemberNo', 'LoanNo', 'MemberName', 'Mobile No.', 'OdDays',
                       'LastCollDt', 'LastCollAmt', 'ODAmount', 'Oustanding', 'Branch',
                       'LoanAmt', 'Scheme', 'LoanDt']].copy()
    
    # Clean data
    od_df['MemberNo'] = clean_for_merge(od_df['MemberNo'])
    od_df['LoanNo'] = clean_for_merge(od_df['LoanNo'])
    
    print(f"OVERDUE data processed - Records: {len(od_df)}")
    return od_df

def process_demand_data(demand_df):
    """Process DEMAND data and return standardized DataFrame"""
    if demand_df.empty:
        return pd.DataFrame()
    
    print("Processing DEMAND data...")
    
    # Create processed demand DataFrame
    dem_df = demand_df[['Member No', 'Loan No', 'Member', 'Mobile Number', 'Loan Amount',
                       'Loan Scheme', 'Loan Date', 'EMI Amount', 'Savings Demand Amount',
                       'Total All Demand', 'Branch', 'Demand Date']].copy()
    
    # Clean data
    dem_df['Member No'] = clean_for_merge(dem_df['Member No'])
    dem_df['Loan No'] = clean_for_merge(dem_df['Loan No'])
    
    # Calculate proper EMI amount
    dem_df['EMI Amount'] = pd.to_numeric(dem_df['EMI Amount'], errors='coerce').fillna(0)
    dem_df['Savings Demand Amount'] = pd.to_numeric(dem_df['Savings Demand Amount'], errors='coerce').fillna(0)
    dem_df['Total All Demand'] = pd.to_numeric(dem_df['Total All Demand'], errors='coerce').fillna(0)
    
    # Apply the condition to update Total All Demand
    demand_sum = dem_df['EMI Amount'] + dem_df['Savings Demand Amount']
    dem_df['Total All Demand'] = np.where(demand_sum > dem_df['Total All Demand'],
                                          demand_sum,
                                          dem_df['Total All Demand'])
    
    print(f"DEMAND data processed - Records: {len(dem_df)}")
    return dem_df

def process_od_sheet_data(csv_df):
    csv_final_df = pd.DataFrame()
    csv_final_df['customerid'] = csv_df['Member No'].apply(clean_column)
    csv_final_df['disbursementid'] = csv_df['Loan No'].apply(clean_disbursement_id)
    csv_final_df['customername'] = csv_df['Member'].apply(clean_name)
    csv_final_df['mobileno'] = csv_df['Contact No'].apply(clean_mobile_number)  # Updated to use clean_mobile_number
    csv_final_df['lastpaymentdate'] = csv_df['Last Receive Date'].apply(convert_excel_serial_date)
    csv_final_df['lastcollectedamount'] = pd.to_numeric(csv_df['Last Receive Amt'], errors='coerce')
    csv_final_df['emiamount'] = pd.to_numeric(csv_df['EMI Amount'], errors='coerce')
    csv_final_df['overdueamount'] = pd.to_numeric(csv_df['Total All Demand'], errors='coerce')
    csv_final_df['totaloutstanding'] = pd.to_numeric(csv_df['POS'], errors='coerce')
    csv_final_df['loantype'] = csv_df['Loan Scheme'].apply(clean_column)
    csv_final_df['disbursementdate'] = csv_df['Loan Date'].apply(convert_excel_serial_date)
    csv_final_df['disbursementamount'] = pd.to_numeric(csv_df['Loan Amount'], errors='coerce')
    csv_final_df['branchname'] = csv_df['Branch'].apply(clean_column)
    csv_final_df['nextemidate'] = csv_df['Demand Date'].apply(convert_excel_serial_date)
    csv_final_df['rateofinterest'] = pd.to_numeric(csv_df['Interest Rate'], errors='coerce')
    csv_final_df['inststartdate'] = csv_final_df['disbursementdate']
    branch_mapping = get_branch_codes_from_db()
    if branch_mapping:
        print(f"Branch mapping retrieved from database: {len(branch_mapping)} branches")
        
        # Map branch codes for CSV data
        csv_final_df['branchcode'] = (csv_final_df['branchname']
                                     .str.upper()
                                     .str.strip()
                                     .map(branch_mapping))
        
        mapped_count = csv_final_df['branchcode'].notna().sum()
        unmapped_count = csv_final_df['branchcode'].isna().sum()
        
        print(f"Mapped branch codes for {mapped_count} out of {len(csv_final_df)} records")
        if unmapped_count > 0:
            print(f"Warning: {unmapped_count} records could not be mapped to branch codes")
            unmapped_branches = csv_final_df[csv_final_df['branchcode'].isna()]['branchname'].unique()
            print(f"Unmapped branch names: {list(unmapped_branches)}")
    else:
        print("Warning: Could not retrieve branch mapping from database")
        csv_final_df['branchcode'] = None
    return csv_final_df

def merge_all_data(od_df, dem_df=None, rec_df=None):
    """Merge all data with FULL OUTER JOIN and track record sources"""
    print("\n🔀 Merging all datasets with source tracking...")
    
    # Initialize result DataFrame starting with overdue data
    if od_df is not None and not od_df.empty:
        result_df = od_df.copy()
        result_df['source'] = 'overdue'
        print(f"📊 Starting with overdue data: {len(result_df)} records")
    else:
        result_df = pd.DataFrame()
        print("⚠️  No overdue data provided")
    
    # Process demand data
    if dem_df is not None and not dem_df.empty:
        print(f"📊 Processing demand data: {len(dem_df)} records")
        
        # Prepare demand data for merging
        dem_processed = dem_df.copy()
        dem_processed['source'] = 'demand'
        
        # Handle the merge based on loan number columns
        if not result_df.empty:
            # Merge demand data with existing result
            # Use outer join to preserve all records
            merged = pd.merge(
                result_df, 
                dem_processed, 
                left_on='LoanNo', 
                right_on='Loan No', 
                how='outer', 
                suffixes=('', '_demand')
            )
            
            # Handle source column after merge
            merged['source'] = merged['source'].fillna('demand')
            merged.loc[(merged['source'] == 'overdue') & (merged['source_demand'] == 'demand'), 'source'] = 'overdue+demand'
            
            # Clean up duplicate source column
            if 'source_demand' in merged.columns:
                merged.drop('source_demand', axis=1, inplace=True)
            
            result_df = merged
        else:
            # If no existing data, start with demand data
            result_df = dem_processed
        
        print(f"📊 After demand merge: {len(result_df)} records")
    
    # Process recollection data
    if rec_df is not None and not rec_df.empty:
        print(f"📊 Processing recollection data: {len(rec_df)} records")
        
        # Prepare recollection data for merging
        rec_processed = rec_df.copy()
        rec_processed['source'] = 'recollection'
        
        # Handle the merge based on loan number columns
        if not result_df.empty:
            # Determine which loan number column to use for merging
            if 'LoanNo' in result_df.columns:
                loan_col_left = 'LoanNo'
            elif 'Loan No' in result_df.columns:
                loan_col_left = 'Loan No'
            else:
                print("⚠️  No loan number column found in existing data")
                # Append recollection data as new records
                result_df = pd.concat([result_df, rec_processed], ignore_index=True, sort=False)
                print(f"📊 After recollection append: {len(result_df)} records")
                return result_df
            
            # Merge recollection data with existing result
            merged = pd.merge(
                result_df, 
                rec_processed, 
                left_on=loan_col_left, 
                right_on='Loan No', 
                how='outer', 
                suffixes=('', '_recollection')
            )
            
            # Handle source column after merge
            merged['source'] = merged['source'].fillna('recollection')
            
            # Update source for records that exist in multiple datasets
            mask_existing = merged['source'].notna() & (merged['source'] != 'recollection')
            mask_recollection = merged['source_recollection'] == 'recollection'
            
            merged.loc[mask_existing & mask_recollection, 'source'] = merged.loc[mask_existing & mask_recollection, 'source'] + '+recollection'
            
            # Clean up duplicate source column
            if 'source_recollection' in merged.columns:
                merged.drop('source_recollection', axis=1, inplace=True)
            
            result_df = merged
        else:
            # If no existing data, start with recollection data
            result_df = rec_processed
        
        print(f"📊 After recollection merge: {len(result_df)} records")
    
    # Handle column mapping and cleanup
    if not result_df.empty:
        print("🔗 Starting column mapping and consolidation...")
        
        # NEW: Create branch mapping before processing columns
        branch_mapping = create_branch_mapping(result_df)
        
        # NEW: Process branch_demand column if it exists
        if 'Branch_demand' in result_df.columns:
            print("🏢 Processing branch_demand column...")
            
            # Count records with branch_demand data
            branch_demand_records = result_df['Branch_demand'].notna().sum()
            print(f"   Found {branch_demand_records} records with branch_demand data")
            
            # Apply branch mapping
            mapped_branches = []
            unmapped_branches = []
            
            for idx, branch_value in result_df['Branch_demand'].items():
                if pd.notna(branch_value):
                    mapped_value = map_branch_demand(branch_value, branch_mapping)
                    mapped_branches.append(mapped_value)
                    
                    # Check if mapping was successful
                    if str(mapped_value).strip().upper() == str(branch_value).strip().upper():
                        unmapped_branches.append(branch_value)
                else:
                    mapped_branches.append(branch_value)
            
            # Update the column with mapped values
            result_df['Branch_demand'] = mapped_branches
            
            # Handle unmapped branches with database lookup
            unique_unmapped = list(set([str(b) for b in unmapped_branches if pd.notna(b)]))
            if unique_unmapped:
                print(f"   ⚠️  {len(unique_unmapped)} branch names could not be mapped initially:")
                for branch in unique_unmapped[:10]:  # Show first 10
                    print(f"      - {branch}")
                if len(unique_unmapped) > 10:
                    print(f"      ... and {len(unique_unmapped) - 10} more")
                
                # Try to get branch codes from database for unmapped branches
                print("🔍 Querying database for unmapped branches...")
                db_branch_mapping = get_branch_codes_from_db(13, unique_unmapped)  # Assuming bank_mst_id is 13
                
                if db_branch_mapping:
                    print(f"   ✅ Found {len(db_branch_mapping)} matches in database")
                    
                    # Update the Branch_demand column with database matches
                    for idx, branch_value in result_df['Branch_demand'].items():
                        if pd.notna(branch_value) and str(branch_value) in db_branch_mapping:
                            result_df.loc[idx, 'Branch_demand'] = db_branch_mapping[str(branch_value)]
                    
                    # Report which branches were successfully mapped from database
                    mapped_from_db = [branch for branch in unique_unmapped if branch in db_branch_mapping]
                    still_unmapped = [branch for branch in unique_unmapped if branch not in db_branch_mapping]
                    
                    if mapped_from_db:
                        print(f"   📊 Successfully mapped {len(mapped_from_db)} branches from database:")
                        for branch in mapped_from_db[:5]:  # Show first 5
                            print(f"      - {branch} → {db_branch_mapping[branch]}")
                        if len(mapped_from_db) > 5:
                            print(f"      ... and {len(mapped_from_db) - 5} more")
                    
                    if still_unmapped:
                        print(f"   ⚠️  {len(still_unmapped)} branches still remain unmapped:")
                        for branch in still_unmapped[:5]:  # Show first 5
                            print(f"      - {branch}")
                        if len(still_unmapped) > 5:
                            print(f"      ... and {len(still_unmapped) - 5} more")
                    else:
                        print("   ✅ All branches successfully mapped!")
                else:
                    print("   ❌ No additional matches found in database")
            else:
                print("   ✅ All branch_demand values were successfully mapped")
        
        # Define column mapping dictionary (overdue_column: [alternative_names])
        column_mappings = {
            'LoanNo': ['Loan No', 'Loan No_recollection'],
            'MemberNo': ['Member No', 'Member No_recollection'],
            'MemberName': ['Member', 'Member_recollection'],
            'Mobile No.': ['Mobile Number', 'MemberMobile Number'],
            'LoanAmt': ['Loan Amount', 'Loan Amount_recollection'],
            'Scheme': ['Loan Scheme','Loan Type'],
            'LoanDt': ['Loan Date', 'Loan Date_recollection'],
            'Branch': ['Branch Name', 'Branch_demand'],  # Updated to include Branch_demand
            'EMI Amount': ['EMI Amount_recollection']
        }
        
        # Process each mapping
        for primary_col, alternative_cols in column_mappings.items():
            available_alternatives = [col for col in alternative_cols if col in result_df.columns]
            
            if available_alternatives:
                if primary_col in result_df.columns:
                    # Primary column exists, merge with alternatives
                    for alt_col in available_alternatives:
                        print(f"   Merging {alt_col} into {primary_col}")
                        # Fill missing values in primary column with alternative column values
                        result_df[primary_col] = result_df[primary_col].fillna(result_df[alt_col])
                        # Drop the alternative column
                        result_df.drop(alt_col, axis=1, inplace=True)
                else:
                    # Primary column doesn't exist, use the first available alternative
                    first_alt = available_alternatives[0]
                    print(f"   Renaming {first_alt} to {primary_col}")
                    result_df.rename(columns={first_alt: primary_col}, inplace=True)
                    
                    # Merge other alternatives if they exist
                    for alt_col in available_alternatives[1:]:
                        if alt_col in result_df.columns:
                            print(f"   Merging {alt_col} into {primary_col}")
                            result_df[primary_col] = result_df[primary_col].fillna(result_df[alt_col])
                            result_df.drop(alt_col, axis=1, inplace=True)
        
        print("✅ Column mapping completed")
        
        # Remove duplicate rows based on LoanNo (keeping first occurrence)
        if 'LoanNo' in result_df.columns:
            before_dedup = len(result_df)
            result_df = result_df.drop_duplicates(subset=['LoanNo'], keep='first')
            after_dedup = len(result_df)
            if before_dedup != after_dedup:
                print(f"📋 Removed {before_dedup - after_dedup} duplicate records based on loan number")
    
    else:
        print("⚠️  No data to merge - all inputs are empty")
    return result_df

def run_post_processing_in_background(bank_id):
    """
    Run post-processing procedures in a background thread
    """
    try:
        print(f"Starting background post-processing for bank_id: {bank_id}")
        execute_post_processing_procedures(bank_id)
        print(f"Background post-processing initiated for bank_id: {bank_id}")
    except Exception as e:
        print(f"Error starting background post-processing for bank_id {bank_id}: {str(e)}")

def create_final_rawfile_data(merged_df):
    """Create final rawfile data with proper column mapping"""
    print("Creating final rawfile data...")
    db_operation_success = False
    db_error_message = None
    # Create rawfile DataFrame with all expected columns
    expected_columns = [
        'customerid', 'disbursementid', 'loantype', 'customername',
        'disbursementamount', 'rateofinterest', 'repaymenttenure', 'emiamount',
        'paymentfrequency', 'numberofdayspastdue', 'mobileno', 'inststartdate',
        'collectionofficerid', 'collectionofficername', 'branchname', 'branchcode',
        'applicanttype', 'overdueamount', 'totaloutstanding', 'principlecollected',
        'interestcollected', 'collectedamount', 'collectiondate', 'pos', 'bankmstid',
        'inserted_date', 'gender', 'dateofbirth', 'disbursementdate', 'loanclassification',
        'lastpaymentdate', 'lastcollectedamount', 'currentbalance', 'interestoutstanding',
        'totalpending', 'principlepending', 'interestpending', 'closingdate',
        'previousemidate', 'guarantor', 'guarantor_mobile', 'extracolumn1',
        'extracolumn2', 'extracolumn3', 'extracolumn4', 'extracolumn5', 'nextemidate'
    ]
    final_df = pd.DataFrame(columns=expected_columns)
    merged_df.columns = merged_df.columns.str.strip().str.replace(r'\s+', ' ', regex=True)
    # Map columns from OVERDUE data (base)
    if 'MemberNo' in merged_df.columns is not None:
        final_df['customerid'] = merged_df['MemberNo'].apply(clean_column)
    if 'LoanNo' in merged_df.columns:
        final_df['disbursementid'] = merged_df['LoanNo'].apply(clean_disbursement_id)
    if 'MemberName' in merged_df.columns:
        final_df['customername'] = merged_df['MemberName'].apply(lambda x: clean_name(x).upper())
    if 'Mobile No.' in merged_df.columns:
        final_df['mobileno'] = merged_df['Mobile No.'].apply(clean_mobile_number_arth)
    if 'LoanAmt' in merged_df.columns:
        final_df['disbursementamount'] = merged_df['LoanAmt']
    if 'Scheme' in merged_df.columns:
        final_df['loantype'] = merged_df['Scheme'].apply(clean_column)
    if 'LoanDt' in merged_df.columns:
        final_df['inststartdate'] = merged_df['LoanDt'].apply(convert_excel_serial_date)
        final_df['disbursementdate'] = merged_df['LoanDt'].apply(convert_excel_serial_date)
    if 'OdDays' in merged_df.columns:
        final_df['numberofdayspastdue'] = merged_df['OdDays']
    if 'ODAmount' in merged_df.columns:
        final_df['overdueamount'] = merged_df['ODAmount']
    if 'LastCollDt' in merged_df.columns:
        final_df['lastpaymentdate'] = merged_df['LastCollDt'].apply(convert_excel_serial_date)
    if 'LastCollAmt' in merged_df.columns:
        final_df['lastcollectedamount'] = merged_df['LastCollAmt']
    if 'Oustanding' in merged_df.columns:
        final_df['totaloutstanding'] = merged_df['Oustanding']
    
    # Process branch information from OVERDUE
    if 'Branch' in merged_df.columns:
        branch_info = merged_df['Branch'].apply(process_branch)
        final_df['branchname'] = [x[1] for x in branch_info]
        unique_branch_names = list(set([name for name in final_df['branchname'] if name]))
        branch_code_mapping = get_branch_codes_from_db(
                bank_mst_id=13, 
                branch_names=unique_branch_names
            )
        final_df['branchcode'] = [
                str(branch_code_mapping.get(branch_name, '0'))  # Default to '0' if not found
                for branch_name in final_df['branchname']
            ]
    # Map DEMAND data if available
    if 'Total All Demand' in merged_df.columns:
        final_df['emiamount'] = merged_df['Total All Demand'].fillna(0).astype(int)
    if 'Demand Date' in merged_df.columns:
        print("checking demand date",merged_df['Demand Date'])
        final_df['nextemidate'] = merged_df['Demand Date'].apply(convert_excel_serial_date)
        print(final_df['nextemidate'],"sfjdfhjdfh")
    
    # Map RECOVERY data if available
    if 'Collection Amount' in merged_df.columns:
        final_df['collectedamount'] = merged_df['Collection Amount'].fillna(0)
    if 'Principle Collection Amount' in merged_df.columns:
        final_df['principlecollected'] = merged_df['Principle Collection Amount'].fillna(0)
    if 'Interest Collection Amount' in merged_df.columns:
        final_df['interestcollected'] = merged_df['Interest Collection Amount'].fillna(0)
    if 'Received Date' in merged_df.columns:
        final_df['collectiondate'] = pd.to_datetime(merged_df['Received Date'], errors='coerce').dt.date
    if 'POS' in merged_df.columns:
        final_df['pos'] = merged_df['POS'].fillna(merged_df.get('Oustanding', 0))
    frequency_mapping = {
        'BI-WEEKLY':15,
        'WEEKLY':7,
        'MONTHLY': 30,      # Monthly
        'QUATERLY': 90,      # Quarterly
        'HALF-YEARLY': 180,     # Half-yearly
        'YEARLY': 365,     # Yearly
        'UNKNOWN': 0,       # Unknown
        '': 0         # Empty
        }
    if 'Repayment Frequency' in merged_df.columns:
        print("Inside Repayment freqyencty")
        final_df["paymentfrequency"] = merged_df["Repayment Frequency"].str.upper().map(frequency_mapping).fillna(0).astype(int)
        print("Printing final_df",final_df["paymentfrequency"])
    
    # Set common values
    final_df['bankmstid'] = 13
    final_df['inserted_date'] = datetime.now().date()
    if 'LoanNo' in merged_df.columns:
        final_df['originaldisbursementid'] = merged_df['LoanNo']
    # Fill remaining columns with None
    for col in expected_columns:
        if col not in final_df.columns or final_df[col].isna().all():
            final_df[col] = None
    # final_df.to_csv("Artha-siddhi.csv",index=False)
    # print(f"Final rawfile data created - Records: {len(final_df)}")
    try:
            result = insert_data_in_raw_table(final_df, bank_id=13)
            db_operation_success = result['success']
            if not result['success']:
                db_error_message = result['message']
    except Exception as e:
            db_operation_success = False
            db_error_message = str(e)
            print(f"Database update failed for pavana data: {e}")
    
    # # ========== START BACKGROUND POST-PROCESSING ==========
    if db_operation_success:
        # Start post-processing in background thread
        bank_id = 13
        background_thread = threading.Thread(
            target=run_post_processing_in_background,
            args=(bank_id,),
            daemon=True
        )
        background_thread.start()
        print(f"Post-processing started in background thread for bank_id: {bank_id}")
    else:
        print(f"Skipping post-processing due to database operation failure: {db_error_message}")
    response = {
            'total records':len(final_df),
            'db_operation_success': result['success'],
            'db_error_message': db_error_message,
            "New Records": result['inserted_count'],
            "Duplicates in DB": result['duplicate_count'],
            'message':result['message']
    }
    return response

###############################################################################

def process_arthasidhi_recovery_data(df, bank_id):
    """Process Arthasidhi recovery data and generate summary"""
    try:
        print(bank_id,"cndfdkfjkf")
        coll_df = process_arhta_sidhi_collection_data(df)
        existing_collections = check_existing_collections(coll_df, bank_id=13)
        
        summary_data = []
        new_records = []  # This will store actual new records (inserts only)
        counters = {'update': 0, 'insert': 0, 'no_change': 0, 'error': 0}
        db_operation_success = False
        db_error_message = None
        
        for index, row in coll_df.iterrows():
            try:
                disbursement_id = row['DisbursementID']
                customer_id = row['CustomerID']
                collection_date = row['CollectionDate']
                collection_date_str = collection_date.strftime('%Y-%m-%d') if not pd.isna(collection_date) else None
                new_amount = row['CollectedAmount']
                
                # Skip records with missing essential data
                if pd.isna(disbursement_id) or pd.isna(collection_date):
                    counters['error'] += 1
                    continue
                
                # Check if this collection already exists
                collection_key = (disbursement_id, collection_date_str)
                
                if collection_key in existing_collections:
                    current_amount = existing_collections[collection_key]
                    if current_amount != new_amount:
                        action = 'Update'
                        counters['update'] += 1
                    else:
                        action = 'No Change'
                        counters['no_change'] += 1
                    
                    summary_data.append({
                        'CustomerID': customer_id,
                        'DisbursementID': disbursement_id,
                        'CollectionDate': collection_date,
                        'Action': action,
                        'CurrentCollectedAmount': current_amount,
                        'NewCollectedAmount': new_amount
                    })
                else:
                    # This is a new record - add to new_records for insertion
                    action = 'Insert'
                    counters['insert'] += 1
                    new_records.append(row)  # Add the actual row data for new records
                    
                    summary_data.append({
                        'CustomerID': customer_id,
                        'DisbursementID': disbursement_id,
                        'CollectionDate': collection_date,
                        'Action': action,
                        'CurrentCollectedAmount': None,
                        'NewCollectedAmount': new_amount
                    })
            except Exception as e:
                print(f"Error processing row {index}: {e}")
                counters['error'] += 1
        
        summary_df = pd.DataFrame(summary_data)
        total_records = len(coll_df)
        
        # Pass the new_records list directly to insert_collection_data
        if new_records:
            try:
                insert_collection_data(new_records, bank_id=13)
                db_operation_success = True
            except Exception as e:
                db_operation_success = False
                db_error_message = str(e)
                print(f"Database insertion failed for Loan Recovery data: {e}")
        else:
            print("No new records to insert")
        
        return {
            'summary_df': summary_df,
            'new_records': new_records,
            'counters': counters,
            'total_records': total_records,
            'db_operation_success': db_operation_success,
            'db_error_message': db_error_message
        }
        
    except Exception as e:
        raise Exception(f"Error processing recovery data: {str(e)}")

def process_arthasidhi_od_data(df, bank_id):
    """Process Arthasidhi overdue data and generate summary"""
    try:
        od_df = process_artha_sidhi_over_due_data(df)
        existing_records = check_existing_account_summary_for_od(od_df, bank_id=13)
        all_bank_records = set(existing_records.keys())
        update_data = []
        insert_data = []
        zero_data = []
        db_operation_success = False
        db_error_message = None

        # Track which records we're handling from the CSV
        handled_records = set()

        for _, row in od_df.iterrows():
            disbursement_id = row['DisbursementID']
            dpd_days = row['DPD']
            handled_records.add(disbursement_id)
            handled_records.add(dpd_days)
            
            if disbursement_id in existing_records:
                current_od = existing_records[disbursement_id]
                new_od = row['OverDueAmt']
                
                if current_od != new_od:
                    update_data.append({
                        'DisbursementID': disbursement_id,
                        'CurrentOverDueAmt': current_od,
                        'NewOverDueAmt': new_od
                    })
            else:
                insert_data.append({
                    'DisbursementID': disbursement_id,
                    'NewOverDueAmt': row['OverDueAmt']
                })

        # Step 3: Identify records to zero out (all bank records not in CSV)
        records_to_zero = all_bank_records - handled_records
        for disbursement_id in records_to_zero:
            zero_data.append({
                'DisbursementID': disbursement_id,
                'CurrentOverDueAmt': existing_records[disbursement_id],
                'NewOverDueAmt': 0
            })

        # Create DataFrames
        update_df = pd.DataFrame(update_data)
        zero_df = pd.DataFrame(zero_data)
        try:
            if not update_df.empty or not zero_df.empty:
                update_od_data(update_df, zero_df)
                db_operation_success = True
        except Exception as e:
            db_operation_success = False
            db_error_message = str(e)
            print(f"Database update failed for over due data: {e}")
        return {
            'Records updated with new overdue amounts': len(update_df),
            'Records set to zero overdue amount': len(zero_df),
            'New records to be inserted': len(insert_data),
            'od_df': od_df,
            'db_operation_success': db_operation_success,
            'db_error_message': db_error_message
        }
        
    except Exception as e:
        raise Exception(f"Error processing overdue data: {str(e)}")
    
def process_arthasidhi_demand_data(df, bank_id):
    """Process Arthasidhi demand data and generate summary"""
    # try:
    #     demand_df = process_artha_sidhi_demand_data(df)
    #     existing_records = check_existing_account_summary(demand_df, bank_id=13)
        
    #     summary_data = []
    #     counters = {'update': 0, 'insert': 0, 'no_change': 0}
    #     db_operation_success = False
    #     db_error_message = None
        
    #     for index, row in demand_df.iterrows():
    #         disbursement_id = row['DisbursementID']
    #         customer_id = row['CustomerID']
    #         new_emi_date = row['NextEMIDate']
    #         new_emi_amount = row['EMIAmount']            
    #         if disbursement_id in existing_records:
    #             current_emi_date, current_emi_amount = existing_records[disbursement_id]
    #             # Check if either date or amount needs updating
    #             if current_emi_date != new_emi_date or current_emi_amount != new_emi_amount:
    #                 action = 'Update'
    #                 counters['update'] += 1
    #             else:
    #                 action = 'No Change'
    #                 counters['no_change'] += 1
                
    #             summary_data.append({
    #                 'CustomerID': customer_id,
    #                 'DisbursementID': disbursement_id,
    #                 'Action': action,
    #                 'CurrentEMIDate': current_emi_date,
    #                 'NewEMIDate': new_emi_date,
    #                 'CurrentEMIAmount': current_emi_amount,
    #                 'NewEMIAmount': new_emi_amount
    #             })
    #         else:
    #             action = 'Insert'
    #             counters['insert'] += 1
    #             summary_data.append({
    #                 'CustomerID': customer_id,
    #                 'DisbursementID': disbursement_id,
    #                 'Action': action,
    #                 'CurrentEMIDate': None,
    #                 'NewEMIDate': new_emi_date,
    #                 'CurrentEMIAmount': None,
    #                 'NewEMIAmount': new_emi_amount
    #             })
        
    #     summary_df = pd.DataFrame(summary_data)
    #     # Save summary_df to CSV
    #     try:
    #         timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    #         csv_filename = f'arthasidhi_demand_summary_{timestamp}.csv'
    #         summary_df.to_csv(csv_filename, index=False)
    #         print(f"Summary data saved to {csv_filename}")
    #     except Exception as e:
    #         print(f"Error saving summary to CSV: {str(e)}")
        
    #     #update the records
    #     # try:
    #     #     if not summary_df.empty and (counters['update'] > 0 or counters['insert'] > 0):
    #     #         update_demand_data(summary_df)
    #     #         db_operation_success = True
    #     # except Exception as e:
    #     #     db_operation_success = False
    #     #     db_error_message = str(e)
    #     #     print(f"Database update failed for demand data: {e}")
    #     print(summary_df,'flmdfedff')
        
    #     total_records = len(demand_df)
    #     update_records = len(summary_df[summary_df['Action'] == 'Update'])
    #     insert_records = len(summary_df[summary_df['Action'] == 'Insert'])
    #     no_change = len(summary_df[summary_df['Action'] == 'No Change'])

        
        # return {
        #     'total_records': total_records,
        #     'db_operation_success': db_operation_success,
        #     'db_error_message': db_error_message,
        #     "insert_records": insert_records,
        #     "update_records": update_records,
        #     'no_change':no_change,
        #     "message":f"Database Updated successfully for the total records {total_records}"
        # }
    # except Exception as e:
    #     raise Exception(f"Error processing demand data: {str(e)}")
    return {
            "message":f" Yet to implement the logic to handle the 2pm data, We are working on it"
        }
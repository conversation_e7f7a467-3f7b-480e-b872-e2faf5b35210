from django.db import models

class RawFile(models.Model):
    # Customer information
    customerid = models.Char<PERSON>ield(max_length=255, null=True, blank=True)
    disbursementid = models.CharField(max_length=255, null=True, blank=True)
    loantype = models.CharField(max_length=100, null=True, blank=True)
    customername = models.CharField(max_length=255, null=True, blank=True)
    
    # Financial amounts
    disbursementamount = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    rateofinterest = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    repaymenttenure = models.IntegerField(null=True, blank=True)
    emiamount = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    
    # Payment information
    paymentfrequency = models.CharField(max_length=50, null=True, blank=True)
    numberofdayspastdue = models.IntegerField(null=True, blank=True)
    mobileno = models.Char<PERSON>ield(max_length=20, null=True, blank=True)
    
    # Dates
    inststartdate = models.DateField(null=True, blank=True)
    
    # Collection information
    collectionofficerid = models.CharField(max_length=255, null=True, blank=True)
    collectionofficername = models.CharField(max_length=255, null=True, blank=True)
    branchname = models.CharField(max_length=255, null=True, blank=True)
    branchcode = models.CharField(max_length=50, null=True, blank=True)
    applicanttype = models.CharField(max_length=50, null=True, blank=True)
    overdueamount = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    # Outstanding amounts
    totaloutstanding = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    principlecollected = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    interestcollected = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    collectedamount = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    
    # More dates
    collectiondate = models.DateField(null=True, blank=True)
    
    # Position and banking
    pos = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    bankmstid = models.IntegerField(null=True, blank=True)
    inserted_date = models.DateField(null=True, blank=True)
    
    # Personal information
    gender = models.CharField(max_length=10, null=True, blank=True)
    dateofbirth = models.DateField(null=True, blank=True)
    disbursementdate = models.DateField(null=True, blank=True)
    loanclassification = models.CharField(max_length=100, null=True, blank=True)
    lastpaymentdate = models.DateField(null=True, blank=True)
    
    # Balance information
    lastcollectedamount = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    currentbalance = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    interestoutstanding = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    totalpending = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    principlepending = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    interestpending = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    
    # Additional dates
    closingdate = models.DateField(null=True, blank=True)
    previousemidate = models.DateField(null=True, blank=True)
    
    # Guarantor information
    guarantor = models.CharField(max_length=255, null=True, blank=True)
    guarantor_mobile = models.CharField(max_length=255, null=True, blank=True)
    
    # Extra columns (flexible fields)
    extracolumn1 = models.CharField(max_length=255, null=True, blank=True)
    extracolumn2 = models.CharField(max_length=255, null=True, blank=True)
    extracolumn3 = models.CharField(max_length=255, null=True, blank=True)
    extracolumn4 = models.CharField(max_length=255, null=True, blank=True)
    extracolumn5 = models.CharField(max_length=255, null=True, blank=True)
    
    # More dates and flags
    nextemidate = models.DateField(null=True, blank=True)
    first_time_arrear_clients = models.BooleanField(default=False, null=True, blank=True)
    
    # Language and other information
    language = models.CharField(max_length=255, null=True, blank=True)
    scheduledate = models.DateField(null=True, blank=True)
    originaldisbursementid = models.CharField(max_length=255, null=True, blank=True)
    guarantorid = models.CharField(max_length=255, null=True, blank=True)
    pending_emis = models.CharField(max_length=255, null=True, blank=True)
    centername = models.CharField(max_length=255, null=True, blank=True)
    nextemiamount = models.CharField(max_length=255, null=True, blank=True)
    secondary_user_type = models.CharField(max_length=255, null=True, blank=True)

    class Meta:
        db_table = 'rawfile'  # Specify the actual table name if different
        verbose_name = 'Raw File'
        verbose_name_plural = 'Raw Files'

    def __str__(self):
        return f"RawFile {self.customerid} - {self.customername}"
import { Flex } from "antd";
import AppHeader from "../AppHeader";
import React, { useState } from "react";
import TabsComponent from "../TabsComponent";
import Accounts from "./Accounts";
import CommunicationHistory from "./CommunicationHistory";
import AccountAnalysis from "./Accounts/AccountAnalysis";
import { ALL_CACHE_NAMES } from "../../constant";
import ResponseSummary from "./ResponseSummary";

export default function ReportsHO() {
  const [currentTab, setcurrentTab] = useState("accounts");
  const [isAnalysisSwitch, setIsAnalysisSwitch] = useState(false);

  const items = [
    {
      key: "accounts",
      label: "Accounts Summary",
      children: (
        <Flex gap={20} vertical align="end">
          {isAnalysisSwitch ? (
            <AccountAnalysis />
          ) : (
            <Accounts currentTab={currentTab} isAnalysis={isAnalysisSwitch} />
          )}
        </Flex>
      ),
    },
    {
      key: "communicationSummary",
      label: "Communication Summary",
      children: <CommunicationHistory />,
    },
    {
      key: "responseSummary",
      label: "Response Summary",
      children: <ResponseSummary />,
    },
  ];
  return (
    <Flex gap={30} vertical>
      <AppHeader
        title={"Reports"}
        // isAccountReportsPage={currentTab === "accounts"}
        isAnalysisSwitch={isAnalysisSwitch}
        setIsAnalysisSwitch={setIsAnalysisSwitch}
      />
      <TabsComponent
        items={items}
        activeKey={currentTab}
        onChange={setcurrentTab}
        className="custom-tabs"
      />
    </Flex>
  );
}

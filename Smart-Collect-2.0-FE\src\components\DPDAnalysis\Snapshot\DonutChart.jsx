import React, { useEffect, useState } from "react";
import Chart from "react-apexcharts";
import Style from "./_style.module.scss";
import { Spin } from "antd";
import PropTypes from "prop-types";

const DonutChart = ({summaryData, isLoading}) => {
  const [chartSeries, setChartSeries] = useState([]);
  const [chartLabels, setChartLabels] = useState([]);

  // Fetch API data
  useEffect(() => {
    let labels = [];
    let series = [];
  
    if (!summaryData || summaryData.length === 0) {
      // Fallback default data when no data is available
      labels = ["No Data"];
      series = [0];
    } else {
      // Process API data normally
      labels = summaryData.map((item) => item.bucket);
      series = summaryData.map((item) =>
        parseFloat(item.dist_pct_amount.replace("%", ""))
      );
    }
  
    setChartLabels(labels);
    setChartSeries(series);
  }, [summaryData]);  

  const chartOptions = {
    chart: { type: "donut" },
    colors: ["#C8B133", "#0F2050", "#9260FF", "#68CF8B", "#058293", "#07223D", "#637617"],
    labels: chartLabels,
    dataLabels: {
      enabled: true,
      formatter: (val) => `${val.toFixed(1)}%`,
      style: { fontSize: "10px", fontWeight: "bold", colors: ["#fff"] },
      dropShadow: { enabled: false },
    },
    plotOptions: {
      pie: { donut: { labels: { show: false } } },
    },
    legend: {
      position: "right",
      markers: { width: 120, height: 120, radius: 4 },
    },
    tooltip: {
      y: { formatter: (val) => `${val.toFixed(2)}%` },
    },
    responsive: [{ breakpoint: 768, options: { legend: { position: "bottom" } } }],
  };

  return (
    <div className={Style.donut_chart_container}>
      <Spin tip={"Loading..."} spinning={isLoading}>
        <Chart 
        options={chartOptions} 
        series={chartSeries} 
        type="donut" 
        height={300} 
        width={350} />
      </Spin>
    </div>
  );
};

DonutChart.propTypes={
  summaryData: PropTypes.array,
  isLoading: PropTypes.bool
}
export default DonutChart;

import React, { useState } from "react";
import { Input, But<PERSON>, message } from "antd";
import { useNavigate } from "react-router";
import Style from "../_forget-password.module.scss";
import { AXIOS } from "../../../apis/ho-Instance";
import PropTypes from "prop-types";

export default function UpdatePassword({UserID}) {
    const [newPassword, setNewPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");
    const [messageApi, contextHolder] = message.useMessage();
    const navigate = useNavigate(); 
    const[isLoading, setIsLoading]= useState(false);

    const handleSubmit = async(e)=>{
        e.preventDefault();
        // Check if both fields are empty
        if (!newPassword && !confirmPassword) {
            messageApi.error("Please fill out both fields");
            return;
        }
        // Check if any one of the fields is empty
        if (!newPassword) {
            messageApi.error("Please enter a new password");
            return;
        }
        if (!confirmPassword) {
            messageApi.error("Please confirm your password");
            return;
        }
        const requestData = {
             action: "reset_password",
             UserID: UserID,
             new_password: newPassword
         };
         try {
             const response = await AXIOS.post("forget_password/", requestData);
             if (response.status === 200) {
                 messageApi.success("Password successfully reset. Redirecting to Login Page");       
                 setTimeout(() => {
                    navigate("/"); 
                }, 2000);}
             else {
                 messageApi.error("Failed to reset password. Please try again.");
                 }
         }catch(error) {
           console.log("Error resetting password", error);
         }finally {
             setIsLoading(false);
         }        
    };

    return (
        <div>
            {contextHolder}
            <form onSubmit={handleSubmit} className={Style.form}>
                <div className={Style.inputContainer}>
                    <label className={Style.label} htmlFor="newPass">New Password</label>
                    <Input.Password
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        className={Style.input}
                        visibilityToggle={false}
                        name="newPass"
                        id="newPass"
                    />
                </div>
                <div className={Style.inputContainer}>
                    <label className={Style.label} htmlFor="confirmPass">Confirm Password</label>
                    <Input.Password
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        className={Style.input}
                        visibilityToggle={false}
                        name="confirmPass"
                        id="confirmPass"
                    />
                </div>
                <Button type="primary" htmlType="submit" block loading={isLoading} className={Style.button}>Save Password</Button>
            </form>
        </div>
    );
}

UpdatePassword.propTypes={
    UserID: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
}
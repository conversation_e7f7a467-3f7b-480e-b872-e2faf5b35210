$light-gray:#D9D9D9;
$tab-height: 7px;

.modal_container{
    .img_div{
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        border: 2px solid #EC3939;
        span{
            width: 65px;
            height: 65px;
            color: #EC3939;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-family: "kanit",'Courier New', Courier, monospace;
        }
    }

    .queue_title, .queue_desc{
        margin: 0;
        color: black;
        font-size:18px; 
        font-family: 'Kanit','Courier New', Courier, monospace;
        font-weight: 400;
        text-align: center;
    }
    .queue_desc{
        font-size: 14px;
        color: gray;
    }

    .yes_btn, 
    .no_btn{
        border: none;
        outline: none;
        background-color: #EC3939 !important;
        color: white;
        margin-bottom: 1rem;
        &:hover{
            background-color: #EC3939;
            color: white !important;
        }
    }

    .no_btn{
        background-color: gray !important;
        color: white;

        &:hover{
            background-color: gray;
            color: white !important;
        }
    }

    :global(.ant-modal-close){
        right: 14px;
        top: 10px;
        color: black;
        &:hover{
          color:black !important;
          background-color: transparent;
        }
        :global(.ant-modal-close-x){
          font-size:10px;
          color: black;
        }
    }  

    :global(.ant-modal-header){
        background: var(--dark-blue);
        padding: 0.5rem 0px;
        text-align: center;
        border-radius: 8px;
        
        :global(.ant-modal-title){
            color: white;
            font-weight: 500;
        }
    }

    :global(.ant-modal-content){
       padding: 1rem;
       background-color: white;
    }
}
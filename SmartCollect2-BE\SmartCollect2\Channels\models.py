from django.db import models
from WebApp.models import <PERSON><PERSON>t, BranchMst, Customer<PERSON>t, Loan<PERSON>t
from campaign.models import WhatsAppTemplateMapping  # Routing to WhatsAppTemplateMapping model Do Not Remove it.

# # , <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, CommunicationFlow,LanguageMst,


class WhatsAppIntentMaster(models.Model):
    WhatsAppIntentID = models.BigAutoField(primary_key=True)
    intent = models.TextField(blank=True, null=True)
    Extra_Column1 = models.CharField(max_length=255, blank=True, null=True)
    Extra_Column2 = models.CharField(max_length=255, blank=True, null=True)
    Extra_Column3 = models.CharField(max_length=255, blank=True, null=True)
    Extra_Column4 = models.CharField(max_length=255, blank=True, null=True)
    Extra_Column5 = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = "WhatsAppIntentMaster"


class WhatsAppKeyMapping(models.Model):
    WhatsAppKeyMappingID = models.BigAutoField(primary_key=True)
    BankMstID = models.ForeignKey(
        BankMst, models.DO_NOTHING, db_column="BankMstID", blank=True, null=True
    )
    CreatedDate = models.DateTimeField(blank=True, null=True)
    AuthorizationKey = models.TextField(blank=True, null=True)
    APIVersion = models.TextField(blank=True, null=True)
    PhoneNumberID = models.TextField(blank=True, null=True)
    Service = models.TextField(blank=True, null=True)
    SP_ProjectID = models.CharField(max_length=255)
    SP_Password = models.CharField(max_length=255)
    Extra_Column1 = models.CharField(max_length=255, blank=True, null=True, verbose_name="WhatsApp Number")
    class Meta:
        managed = False
        db_table = "WhatsAppKeyMapping"


class WhatsAppQueue(models.Model):
    WhatsAppQueueID = models.BigAutoField(primary_key=True)
    CreatedDate = models.DateField(blank=True, null=True)
    BankMstID = models.ForeignKey(
        BankMst, models.DO_NOTHING, db_column="BankMstID", blank=True, null=True
    )
    BranchMstID = models.ForeignKey(
        BranchMst, models.DO_NOTHING, db_column="BranchMstID", blank=True, null=True
    )
    LoanMstID = models.ForeignKey(
        LoanMst, models.DO_NOTHING, db_column="LoanMstID", blank=True, null=True
    )
    BranchCode = models.CharField(max_length=50, blank=True, null=True)
    LoanType = models.CharField(max_length=50, blank=True, null=True)
    CustomerCode = models.CharField(max_length=50, blank=True, null=True)
    CustomerName = models.CharField(max_length=100, blank=True, null=True)
    OverdueAmt = models.DecimalField(
        max_digits=15, decimal_places=2, blank=True, null=True
    )
    Language = models.CharField(max_length=100, blank=True, null=True)
    # LngMstID = models.IntegerField(blank=True, null=True)
    BankName = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn1 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn2 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn3 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn4 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn5 = models.CharField(max_length=255, blank=True, null=True)
    Slot = models.CharField(max_length=255, blank=True, null=True)
    BasedOn = models.CharField(max_length=255, blank=True, null=True)
    CommFlowID = models.IntegerField()
    WhatsAppTemplateMappingID = models.IntegerField()
    ContactNumber = models.CharField(max_length=255, blank=True, null=True)
    DisbursementID = models.CharField(max_length=100)
    Next_EMI_Amount = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True
    )
    Next_EMI_Date = models.DateField(blank=True, null=True)
    Total_Collection = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True
    )
    Latest_CollectedDate = models.DateField(blank=True, null=True)
    Latest_CollectedAmt = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True
    )
    FlowID = models.IntegerField()
    SecondaryCustMstID = models.IntegerField()
    BranchName=models.CharField(max_length=100)
    CampaignMstID = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "WhatsAppQueue"


class WhatsAppHistory(models.Model):
    WhatsAppHistoryID = models.BigAutoField(primary_key=True)
    WhatsAppQueueID = models.BigIntegerField(blank=True, null=True)
    BankMstID = models.ForeignKey(
        BankMst, models.DO_NOTHING, db_column="BankMstID", blank=True, null=True
    )
    BranchMstID = models.ForeignKey(
        BranchMst, models.DO_NOTHING, db_column="BranchMstID", blank=True, null=True
    )
    # CustomerMstID = models.ForeignKey(CustomerMst, models.DO_NOTHING, db_column='CustomerMstID', blank=True, null=True)
    LoanMstID = models.ForeignKey(
        LoanMst, models.DO_NOTHING, db_column="LoanMstID", blank=True, null=True
    )
    BranchCode = models.CharField(max_length=50, blank=True, null=True)
    LoanType = models.CharField(max_length=50, blank=True, null=True)
    CustomerCode = models.CharField(max_length=50, blank=True, null=True)
    CustomerName = models.CharField(max_length=100, blank=True, null=True)
    OverdueAmt = models.DecimalField(
        max_digits=15, decimal_places=2, blank=True, null=True
    )
    Language = models.CharField(max_length=100, blank=True, null=True)
    BankName = models.CharField(max_length=100, blank=True, null=True)
    CreatedDate = models.DateField(blank=True, null=True)
    Identifier = models.CharField(max_length=255, blank=True, null=True, unique=True)
    WhatsAppTemplateMappingID = models.IntegerField()
    IsSent = models.BooleanField()
    IsDelivered = models.BooleanField()
    IsRead = models.BooleanField()
    DisbursementID = models.CharField(max_length=100)
    ContactNumber = models.CharField(max_length=255, blank=True, null=True)
    DisbursementID = models.CharField(max_length=100)
    Next_EMI_Amount = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True
    )
    Next_EMI_Date = models.DateField(blank=True, null=True)
    Total_Collection = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True
    )
    Latest_CollectedDate = models.DateField(blank=True, null=True)
    Latest_CollectedAmt = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True
    )
    FlowID = models.IntegerField()
    CampaignMstID = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "WhatsAppHistory"


class WhatsApp_Messages(models.Model):
    WhatsAppMessageID = models.BigAutoField(primary_key=True)
    MessageDate = models.DateTimeField(blank=True, null=True)
    WhatsAppQueueID = models.BigIntegerField(blank=True, null=True)
    MessageID = models.CharField(max_length=255, blank=True, null=True)
    status_callback = models.JSONField(default=list)
    AssignedTo = models.CharField(max_length=100, blank=True, null=True)
    Sender = models.CharField(max_length=100, blank=True, null=True)
    Content = models.TextField(blank=True, null=True)
    Status = models.CharField(max_length=100, blank=True, null=True)
    CustomerNumber = models.CharField(max_length=15, blank=True, null=True)
    CustomerName = models.CharField(max_length=255, blank=True, null=True)
    LoanMstID = models.ForeignKey(
        LoanMst, models.DO_NOTHING, db_column="LoanMstID", blank=True, null=True
    )
    Button = models.CharField(max_length=255, blank=True, null=True)
    # Template_ID = models.BigIntegerField(blank=True, null=True)
    Type = models.CharField(max_length=50, blank=True, null=True)
    Identifier = models.CharField(max_length=255, blank=True, null=True)
    WhatsAppUserTemplateID = models.IntegerField()
    # Message_DateTime = models.DateTimeField(blank=True, null=True)
    # Message_Content = models.TextField(blank=True, null=True)
    # Template_ID = models.BigIntegerField(blank=True, null=True)
    # Message_Status = models.CharField(max_length=50, blank=True, null=True)
    ExtraColumn1 = models.TextField(blank=True, null=True)
    GapshapReplyID = models.CharField(max_length=500, blank=True, null=True)
    BankMstID = models.ForeignKey(
        BankMst, models.DO_NOTHING, db_column="BankMstID", blank=True, null=True
    )
    CampaignMstID = models.IntegerField(blank=True, null=True)
    # ErrorCode
    class Meta:
        managed = False
        db_table = "WhatsApp_Messages"


class Dialer(models.Model):
    DialerID = models.AutoField(primary_key=True)
    BankMstID = models.ForeignKey(
        BankMst, on_delete=models.CASCADE, db_column="BankMstID", blank=True, null=True
    )
    BranchMstID = models.ForeignKey(
        BranchMst,
        on_delete=models.CASCADE,
        db_column="BranchMstID",
        blank=True,
        null=True,
    )
    LoanMstID = models.ForeignKey(
        LoanMst, on_delete=models.CASCADE, db_column="LoanMstID", blank=True, null=True
    )
    CustomerMstID = models.ForeignKey(
        CustomerMst,
        on_delete=models.CASCADE,
        db_column="CustomerMstID",
        blank=True,
        null=True,
    )
    Call_ID = models.CharField(max_length=250, blank=True, null=True)
    DateTime = models.DateTimeField(blank=True, null=True)
    RecordingURL = models.TextField(null=True, blank=True)
    InitiatingNumber = models.CharField(max_length=255)

    class Meta:
        managed = False
        db_table = "Dialer"
        verbose_name = "Dialer History"
        verbose_name_plural = "Dialer Histories"
        # # ordering = ['-datetime']


class CommFlow(models.Model):
    CommFlowID = models.IntegerField()
    CommunicationType = models.CharField(max_length=255)
    CommFlowMstID = models.IntegerField()
    Days = models.CharField(max_length=255)
    BeforeAfter = models.CharField(max_length=255)
    FlowID = models.IntegerField()
    ExtraColumn1 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn2 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn3 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn4 = models.CharField(max_length=255, blank=True, null=True)
    ExtraColumn5 = models.CharField(max_length=255, blank=True, null=True)
    BankMstID = models.IntegerField()
    BasedOn = models.CharField(max_length=255)
    BasedOnTable = models.CharField(max_length=255)
    TemplateID = models.CharField(max_length=255)

    class Meta:
        managed = False
        db_table = "CommFlow"


class WhatsappVariableMapping(models.Model):
    WhatsappVariableMappingID = models.IntegerField()
    WhatsAppTemplateMappingID = models.IntegerField()
    VariableNo = models.IntegerField()
    VariableField = models.CharField(max_length=255)

    class Meta:
        managed = False
        db_table = "WhatsappVariableMapping"


class PaymentLogs(models.Model):
    CustMstID = models.IntegerField(db_column="CustMstID")
    QR_ID = models.CharField(max_length=255, db_column="QR_ID")
    Payment_Link = models.CharField(max_length=255, db_column="Payment_Link")
    QR_Link = models.CharField(max_length=255, db_column="QR_Link")
    DateTime = models.DateTimeField(db_column="DateTime")
    Status = models.CharField(max_length=255, db_column="Status")
    ResponseID = models.IntegerField(db_column="ResponseID")
    Link_ID = models.CharField(max_length=255, db_column="Link_ID")
    LoanMstID = models.IntegerField(null=True, blank=True)

    class Meta:
        managed = False
        db_table = "PaymentLogs"


class Transaction_Logs(models.Model):
    QR_ID = models.CharField(max_length=255, null=True, blank=True)
    Link_ID = models.CharField(max_length=255, null=True, blank=True)
    Payment_ID = models.CharField(max_length=255, null=True, blank=True)
    Amount = models.IntegerField(null=True, blank=True)
    Status = models.CharField(max_length=255, null=True, blank=True)
    Method = models.CharField(max_length=255, null=True, blank=True)
    VPA = models.CharField(max_length=255, null=True, blank=True)
    Contact = models.CharField(max_length=255, null=True, blank=True)
    Payment_Cust_ID = models.CharField(max_length=255, null=True, blank=True)
    Transaction_date = models.DateTimeField(null=True, blank=True)
    Body = models.JSONField(null=True, blank=True)
    DateTime = models.DateTimeField(null=True, blank=True)
    CustMstID = models.IntegerField(null=True, blank=True)
    LoanMstID = models.IntegerField(null=True, blank=True)

    class Meta:
        managed = False
        db_table = "Transaction_Logs"


# class WhatsAppTemplateMapping(models.Model):
#     WhatsAppTemplateMappingID = models.AutoField(primary_key=True)
#     MetaTemplateID = models.CharField(max_length=255, blank=True, null=True)
#     CreatedDate = models.DateTimeField(auto_now_add=True)
#     Status = models.CharField(max_length=50)
#     TemplateName = models.CharField(max_length=255)
#     IsActive = models.BooleanField(default=True)
#     # LngMstID = models.ForeignKey(LanguageMst, on_delete=models.PROTECT, db_column='LngMstID')
#     FileName = models.CharField(max_length=255)
#     IsAdminCreated = models.BooleanField(default=False)
#     # UserTemplateID = models.ForeignKey(WhatsAppUserTemplate, on_delete=models.PROTECT, db_column='UserTemplateID')
#     Extra_Column1 = models.CharField(max_length=255, blank=True, null=True)
#     Extra_Column2 = models.CharField(max_length=255, blank=True, null=True)
#     Extra_Column3 = models.CharField(max_length=255, blank=True, null=True)
#     Extra_Column4 = models.CharField(max_length=255, blank=True, null=True)
#     Extra_Column5 = models.CharField(max_length=255, blank=True, null=True)
#     BankMstID = models.ForeignKey(BankMst, on_delete=models.PROTECT, db_column='BankMstID', null=True)
#     TemplateBody = models.TextField(null=True, blank=True)

#     class Meta:
#         managed = False
#         db_table = "WhatsAppTemplateMapping"
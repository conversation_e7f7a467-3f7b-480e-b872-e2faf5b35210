import { Flex, message, Tooltip, Typography } from "antd";
import React, { useMemo, useState } from "react";
import PROFILE_IMG from "../../../assets/Images/gg_profile.png";
import HISTORY_IMG from "../../../assets/Images/history-white.png";
import PHONE_IMG from "../../../assets/Images/mi_call.png";
import REFUSED_TO_PAY_IMG from "../../../assets/Images/refusedToPay.png";
import {
  SMART_COLLECT_MENU_IDS,
  getResponsiveFontSize,
  handleDateFormatter,
  RECENT_ENGAGEMENT_APPS_DATA,
  STATUS_IDS,
  formatAmount,
} from "../../../constant";
import { Link } from "react-router";
import { RecentEngagement } from "../../RecentEngagement";
import { FeedbackModal } from "../../Modals/FeedbackModal";
import { Details } from "./Details";
import UpdateNumberModal from "../../Modals/UpdateNumberModal";
import Style from "./_customer-details-card.module.scss";
import { DialerModal } from "../../Modals/DialerModal";
import { AXIOS } from "../../../apis/ho-Instance";
import { ConversionHistory } from "../../Modals/ConversionHistory";
import { ViewFeedbackModal } from "../../Modals/ViewFeedbackModal";
import FIRST_TIME_DEFAULTER_IMG from "../../../assets/Images/firsttime.svg";
import CustomerProfileModal from "../../Modals/CustomerProfileModal";
import PropTypes from "prop-types";
import DateSVG from "../../../assets/SVGs/Date";
import LoanTypeSVG from "../../../assets/SVGs/LoanTypeSVG";
import AppId from "../../../assets/SVGs/AppId";
import Status from "../../../assets/SVGs/Status";
import BranchSVG from "../../../assets/SVGs/Branch";

const { Title, Text } = Typography;

export function CustomerDetailsCard({
  loanmstid,
  branchMstId,
  communicationCount = 0,
  last5CommunicationTypes,
  status,
  name,
  customerId,
  disbursementId,
  branchName,
  loanType,
  overdueAmount,
  promiseAmount,
  promiseDate,
  responseDate,
  feedbackDate,
  dpdAmount,
  denialReason,
  modalButtonText,
  isModal = false,
  pageId = null,
  firstTimeDefaulter = false,
  isFeedbackClick = false,
}) {
  const [messageApi, contextHolder] = message.useMessage();
  const [isLoading, setIsLoading] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [modals, setModals] = useState({
    isModalOpen: false,
    isViewFeedbackModalOpen: false,
    isDialerModalOpen: false,
    isHistoryModalOpen: false,
    showProfileModalOpen: false,
  });

  const updateModal = (key, value) => {
    setModals((prev) => ({ ...prev, [key]: value }));
  };

  const getAmountDetail = () => {
    if (pageId === SMART_COLLECT_MENU_IDS.READY_TO_PAY) {
      return {
        value: promiseAmount,
        label: "Promise Amount",
        className: "success",
      };
    } else if (pageId === SMART_COLLECT_MENU_IDS.ALREADY_PAY) {
      return {
        value: overdueAmount,
        label: "Claim Amount",
        className: "error",
      };
    }
    return null;
  };

  const detailProps = (() => {
    if (
      [
        SMART_COLLECT_MENU_IDS.NON_CONTACTABLE,
        SMART_COLLECT_MENU_IDS.BMI_ALLOCATION,
      ].includes(pageId)
    ) {
      return null; // Hide the entire section
    }
    if (pageId !== SMART_COLLECT_MENU_IDS.READY_TO_PAY) {
      return {
        title: "Response date",
        value: responseDate ? handleDateFormatter(responseDate) : "-",
      };
    } else {
      return {
        title: "Promise date",
        value: promiseDate ? handleDateFormatter(promiseDate) : "-",
      };
    }
  })();

  const amountDetail = getAmountDetail();

  // Convert API data into UI data for RecentEngagement
  const recentEngagementAppsData = useMemo(() => {
    if (typeof last5CommunicationTypes === "string") {
      last5CommunicationTypes = JSON.parse(last5CommunicationTypes);
    }
    if (!last5CommunicationTypes || !Array.isArray(last5CommunicationTypes))
      return [];

    return last5CommunicationTypes
      .slice(-5)
      .map(({ source, delivery_status }) => {
        const appData = RECENT_ENGAGEMENT_APPS_DATA.find(
          (app) => app.id?.toLowerCase() === source?.toLowerCase()
        );

        if (!appData) return null;

        return {
          ...appData,
          status:
            delivery_status === "Delivered"
              ? STATUS_IDS.SUCCESS
              : STATUS_IDS.REJECTED, // Green for Delivered, Red for Not Delivered
        };
      })
      .filter(Boolean); // Remove null entries
  }, [last5CommunicationTypes, communicationCount]);

  const handleSubmit = () => updateModal("isModalOpen", false);
  const handleCancel = () => updateModal("isModalOpen", false);
  const handleViewFeedbackCancel = () =>
    updateModal("isViewFeedbackModalOpen", false);

  const handleDialerSubmit = async ({ number }) => {
    setIsLoading(true);
    messageApi.success("Initiate call");
    // Close the dialer & open the feedback modal
    updateModal("isModalOpen", true);
    updateModal("isDialerModalOpen", false);
    try {
      await AXIOS.post("v1/dialer/", {
        loanMstId: loanmstid,
        bm_phone: number,
      });
    } catch (error) {
      console.error("Error in dialer", error?.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDialerCancel = () => {
    updateModal("isDialerModalOpen", false);
  };

  const handleHistoryCancel = () => {
    updateModal("isHistoryModalOpen", false);
  };

  return (
    <>
      {contextHolder}
      <Flex
        vertical
        className={`${Style.card_container} ${
          isFeedbackClick ? Style.feedback_card_container : null
        }`}
      >
        {/* customer details */}
        <Flex
          justify="space-between"
          align="center"
          className={`${Style.customer_details}`}
        >
          <Flex
            gap={10}
            align="center"
            className={Style.profile_container}
            style={{ cursor: "pointer" }}
            onClick={() => {
              setSelectedCustomer({
                name,
                customerId,
                disbursementId,
                branchName,
                loanType,
                dpdAmount,
                overdueAmount,
                promiseAmount,
                loanmstid,
                branchMstId,
                pageId,
              });
              updateModal("showProfileModalOpen", true);
            }}
          >
            {/* icon */}
            <div className={Style.profile_img}>
              {" "}
              <img src={PROFILE_IMG} alt={name} />{" "}
            </div>

            {/* details */}
            <Flex vertical>
              <Title level={4} className={Style.name}>
                {name}
              </Title>
              <Text className={Style.customer_id}>
                Disbursement Id: {disbursementId}
              </Text>
            </Flex>
          </Flex>

          {/* icons */}
          <Flex gap={10} align="center">
            <div className={Style.profile_img}>
              <input
                type="image"
                src={PHONE_IMG}
                alt="phone"
                onClick={() => updateModal("isDialerModalOpen", true)}
              />
            </div>
            <div className={Style.profile_img}>
              <input
                type="image"
                src={HISTORY_IMG}
                alt="history"
                onClick={() => updateModal("isHistoryModalOpen", true)}
              />
            </div>
          </Flex>
        </Flex>

        {/* payments details */}
        <Flex vertical gap={2} className={Style.payment_details}>
          <div
            className={Style.attempts}
            style={{ marginBlockEnd: `${isFeedbackClick ? "1rem" : "0"}` }}
          >
            <Flex justify="space-between" align="center">
              <Text className={Style.attempts_value}>
                {" "}
                No. of Attempts:{" "}
                <span style={{ color: "black" }}>{communicationCount}</span>
              </Text>
              <Flex className={Style.first_time}>
                {recentEngagementAppsData.length ? (
                  <div style={{ padding: "0.3rem 1rem" }}>
                    <RecentEngagement
                      recentEngagementAppsData={recentEngagementAppsData}
                    />
                  </div>
                ) : null}

                {firstTimeDefaulter && (
                  <Tooltip
                    color={"#CEDDFF"}
                    title={
                      <div style={{ color: "black", fontWeight: "600" }}>
                        First Time Defaulter
                      </div>
                    }
                    placement="topRight"
                  >
                    <Flex
                      align="center"
                      style={{
                        background: "#CEDDFF",
                        border: "1px solid #A9A8A8",
                        borderRadius: "8px",
                        padding: "0 0.3rem",
                        cursor: "pointer",
                      }}
                    >
                      <img
                        src={FIRST_TIME_DEFAULTER_IMG}
                        alt="first time defaulter"
                      />
                    </Flex>
                  </Tooltip>
                )}
              </Flex>
            </Flex>
          </div>

          {isFeedbackClick && (
            <>
              <Details
                image={<AppId />}
                title={"Customer Id"}
                value={customerId ?? "-"}
                width="18px"
              />
              <Details
                image={<BranchSVG />}
                title={"Branch Name"}
                value={branchName ?? "-"}
              />
              <Details
                image={<LoanTypeSVG />}
                title={"Loan Type"}
                value={loanType ?? "-"}
              />
            </>
          )}
        </Flex>

        {/* amount details */}
        <Flex
          justify="space-evenly"
          align="center"
          gap={20}
          className={Style.amount_details}
        >
          <Flex vertical align="center" gap={5}>
            <Title level={4} className={`${Style.amount}`}>
              {overdueAmount > 0 && dpdAmount === 0 ? "-" : dpdAmount}{" "}
              {/*if dpdAmount is 0 than show '-' */}
            </Title>
            <Text className={Style.text}>DPD</Text>
          </Flex>

          {pageId !== SMART_COLLECT_MENU_IDS.ALREADY_PAY && (
            <Flex vertical align="center" gap={5}>
              <Title
                level={4}
                className={`${Style.amount} ${Style.error}`}
                style={{ fontSize: getResponsiveFontSize(overdueAmount ?? 0) }}
              >
                Rs.{formatAmount(overdueAmount ?? 0)}
              </Title>
              <Link
                to="#"
                className={Style.text}
                style={{ textDecoration: "underline" }}
              >
                Overdue Amount
              </Link>
            </Flex>
          )}

          {amountDetail && (
            <Flex vertical align="center" gap={5}>
              <Title
                level={4}
                className={`${Style.amount} ${Style[amountDetail.className]}`}
                style={{
                  fontSize: getResponsiveFontSize(amountDetail.value ?? 0),
                }}
              >
                Rs.{formatAmount(amountDetail.value ?? 0)}
              </Title>
              <Text className={Style.text}>{amountDetail.label}</Text>
            </Flex>
          )}
        </Flex>

        {/* dates */}
        <Flex vertical gap={2} className={Style.date_details}>
          {pageId === SMART_COLLECT_MENU_IDS.REFUSED_TO_PAY && (
            <Details
              image={REFUSED_TO_PAY_IMG}
              title={"Denial Reason"}
              value={denialReason ?? "--"}
              width="16px"
            />
          )}
          {detailProps && (
            <Details
              image={<DateSVG />}
              title={detailProps.title}
              value={detailProps.value}
            />
          )}

          {/* promise status */}
          {pageId === SMART_COLLECT_MENU_IDS.READY_TO_PAY && (
            <Details
              image={<Status />}
              title={"Promise Status"}
              value={status}
            />
          )}

          {feedbackDate && (
            <>
              <Details
                image={<DateSVG />}
                title={"Latest BM Feedback Date"}
                value={handleDateFormatter(feedbackDate)}
              />

              <Link to="#" className={Style.view_link}>
                <Text
                  onClick={() => {
                    updateModal("isViewFeedbackModalOpen", true);
                  }}
                >
                  View
                </Text>
              </Link>
            </>
          )}
        </Flex>

        {/* modalButton */}
        {isModal ? (
          !isFeedbackClick && (
            <Flex align="center" className={Style.modal_button_container}>
              <button
                className={Style.modal_button}
                onClick={() => {
                  updateModal("isModalOpen", true);
                }}
              >
                {modalButtonText}
              </button>
            </Flex>
          )
        ) : (
          <Flex
            justify="space-between"
            align="center"
            gap={2}
            className={Style.number_status}
          >
            {/* <Text style={{ fontWeight: 500 }}>Number Status:</Text>
            <Text className={Style.status}>
              { status ?? "Not reachable"}
            </Text> */}
          </Flex>
        )}

        {pageId === SMART_COLLECT_MENU_IDS.WRONG_NUMBER ? (
          <UpdateNumberModal
            loanMstId={loanmstid}
            modalStatus={modals.isModalOpen}
            handleSubmit={handleSubmit}
            handleCancel={handleCancel}
          />
        ) : (
          <FeedbackModal
            pageId={pageId}
            branchMstId={branchMstId}
            loanmstid={loanmstid}
            overdueAmount={overdueAmount}
            modalStatus={modals.isModalOpen}
            handleSubmit={handleSubmit}
            handleCancel={handleCancel}
            componentProp={
              <CustomerDetailsCard
                loanmstid={loanmstid}
                branchMstId={branchMstId}
                communicationCount={communicationCount}
                last5CommunicationTypes={last5CommunicationTypes}
                status={status}
                name={name}
                customerId={customerId}
                disbursementId={disbursementId}
                branchName={branchName}
                loanType={loanType}
                overdueAmount={overdueAmount}
                promiseAmount={promiseAmount}
                promiseDate={promiseDate}
                responseDate={responseDate}
                feedbackDate={feedbackDate}
                dpdAmount={dpdAmount}
                denialReason={denialReason}
                modalButtonText={modalButtonText}
                isModal={isModal}
                pageId={pageId}
                firstTimeDefaulter={firstTimeDefaulter}
                isFeedbackClick={true}
              />
            }
          />
        )}

        <DialerModal
          customerName={name}
          overdueAmount={overdueAmount}
          loanmstid={loanmstid}
          isLoading={isLoading}
          modalStatus={modals.isDialerModalOpen}
          handleSubmit={handleDialerSubmit}
          handleCancel={handleDialerCancel}
        />

        <ConversionHistory
          customerName={name}
          loanMstId={loanmstid}
          modalStatus={modals.isHistoryModalOpen}
          handleCancel={handleHistoryCancel}
        />

        <ViewFeedbackModal
          loanMstId={loanmstid}
          modalStatus={modals.isViewFeedbackModalOpen}
          handleCancel={handleViewFeedbackCancel}
        />

        {selectedCustomer && (
          <CustomerProfileModal
            modalStatus={modals.showProfileModalOpen}
            handleClose={() => {
              setSelectedCustomer(null);
              updateModal("showProfileModalOpen", false);
            }}
            {...selectedCustomer}
          />
        )}
      </Flex>
    </>
  );
}

// Define the prop types
CustomerDetailsCard.propTypes = {
  loanmstid: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  branchMstId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  communicationCount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  last5CommunicationTypes: PropTypes.oneOfType([
    PropTypes.array,
    PropTypes.string,
  ]),
  status: PropTypes.string,
  name: PropTypes.string,
  denialReason: PropTypes.string,
  customerId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  disbursementId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  branchName: PropTypes.string,
  loanType: PropTypes.string,
  overdueAmount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  promiseAmount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  promiseDate: PropTypes.string,
  responseDate: PropTypes.string,
  feedbackDate: PropTypes.string,
  dpdAmount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  modalButtonText: PropTypes.string,
  isModal: PropTypes.bool,
  pageId: PropTypes.string,
  firstTimeDefaulter: PropTypes.bool,
  isFeedbackClick: PropTypes.bool,
};

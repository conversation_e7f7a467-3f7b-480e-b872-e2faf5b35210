@import url("../../../../../index.css");

$light-blue: #e3f5f6;
$bg-color: #1d3261f7;

.campaign_container {
  background-color: white;
  padding: 1rem;
  border-radius: 8px;
  flex-direction: column;

  .input_container {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    .custom_input {
      background-color: $light-blue;
      caret-color: #407bff;
      padding: 0.3rem 0.5rem;
      width: 100%;
      text-transform: capitalize;

      &:global(.ant-input-outlined) {
        border: none;
        color: var(--dark-blue);
        font-weight: 400;
        border-radius: 6px;
        box-shadow: 0px 2px 2px 0px #00000040 inset;
        font-family: "Kanit", "Courier New", Courier, monospace;
      }
    }
    .priority_input {
      border: none;
      padding: 0rem 0.5rem;
      &::placeholder {
        color: var(--dark-blue);
        font-weight: 400;
        font-family: "Kanit", "Courier New", Courier, monospace;
      }
      :global(.ant-input-number-input) {
        color: var(--dark-blue);
        font-weight: 400;
        font-family: "Kanit", "Courier New", Courier, monospace;
      }
    }
    .text {
      font-size: 14px;
      color: var(--dark-blue);
      font-family: "Kanit", "Courier New", Courier, monospace;
      white-space: nowrap;
      margin-right: 1rem; // Add margin between text and input box
    }
  }

  .container {
    background-color: $light-blue;
    padding: 1rem;
    border-radius: 5px;

    .section {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 1rem;
      margin-top: 1rem;
      .text {
        font-size: 16px;
        color: #407bff;
        font-family: "Kanit", "Courier New", Courier, monospace;
        white-space: nowrap;
        margin-right: 0.9rem;
      }

      .button {
        background: none;
        border: none;
        cursor: pointer;
        background-color: var(--dark-blue);
        color: white;
        &:hover,
        &:focus {
          background-color: var(--dark-blue);
          color: white;
          box-shadow: none;
        }

        &:active {
          background-color: var(--dark-blue);
        }
      }
      .edit_icon {
        width: 14px;
        height: auto;
      }
    }
    .row {
      display: flex;
      gap: 10px;
      margin-bottom: 1rem;

      .input_container_small {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-bottom: 0.5rem;
        max-width: 600px;
        .text {
          font-size: 14px;
          color: var(--dark-blue);
          font-family: "Kanit", "Courier New", Courier, monospace;
          margin-right: 0.9rem;
        }
        .custom_input_small {
          flex: 1;
          padding: 0.3rem 0.5rem;
          width: 100%;
          background-color: white;
          text-transform: capitalize;

          &:global(.ant-input-outlined) {
            border: none;
            box-shadow: none;
            color: var(--dark-blue);
            font-weight: 500;
            border-radius: 6px;
            font-family: "Kanit", "Courier New", Courier, monospace;
            font-size: 14px;
          }
        }
        :global(.ant-segmented) {
          background-color: white;
          border-radius: 10px;

          :global(.ant-segmented-item) {
            :global(.ant-segmented-item-label) {
              color: #0f20504a;
              font-weight: 400;
              font-family: "Kanit", "Courier New", Courier, monospace;
              background-color: transparent;
            }
            &::after {
              background-color: transparent !important;
              display: none;
            }
          }

          :global(.ant-segmented-item-selected) {
            background-color: var(--dark-blue) !important;
            color: white;
            box-shadow: none;
            border-radius: 10px;
            :global(.ant-segmented-item-label) {
              color: white;
            }
          }
          :global(.ant-segmented-thumb) {
            border-radius: 10px;
          }
        }
      }
    }
    .divider {
      border: none;
      border-top: 2px solid #ddd;
      margin: 1rem 0;
      background-color: grey;
    }
  }
}

@media screen and (max-width: 768px) {
  .campaign_container {
    .input_container {
      .text {
        font-size: 11px !important;
      }
    }
    .container {
      .section {
        .text {
          font-size: 13px !important;
        }
      }
      .row {
        .input_container_small {
          .text {
            font-size: 11px !important;
          }
          .custom_input_small {
            &:global(.ant-input-outlined) {
              font-size: 11px !important;
            }
          }
        }
      }
    }
  }
}

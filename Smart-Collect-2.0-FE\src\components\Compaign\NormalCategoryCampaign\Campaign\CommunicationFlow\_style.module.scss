@import url("../../../../../index.css");

$light-blue: #e3f5f6;
$bg-color: #1d3261f7;

.flow_container {
  background-color: $bg-color;
  padding: 1rem 2rem;
  border-radius: 8px;

  .filters_container {
    background-color: $light-blue;
    border-radius: 8px;
    padding: 1rem 2rem;

    .input_text {
      font-family: "Kanit", "Courier New", Courier, monospace;
      font-size: 14px;
      color: var(--dark-blue);
      white-space: nowrap;
    }
    .custom_input {
      caret-color: #407bff;
      padding: 0.3rem 0.5rem;

      &:global(.ant-input-outlined) {
        border: none;
        color: var(--dark-blue);
        font-weight: 400;
        border-radius: 6px;
        box-shadow: 0px 2px 2px 0px #00000040 inset;
        font-family: "Kanit", "Courier New", Courier, monospace;
      }
    }
  }

  .row {
    background-color: $light-blue;
    border-radius: 8px;
    padding: 1rem 2rem;
    gap: 30px;

    .text {
      font-size: 14px;
      color: var(--dark-blue);
      font-family: "Kanit", "Courier New", Courier, monospace;
      white-space: nowrap;
    }

    .custom_select {
      width: 100%;
      border-radius: 10px;

      :global(.ant-select-selector) {
        color: var(--dark-blue);
        border: none !important;
        padding: 0 1.1rem;
        box-shadow: none !important;
        font-family: "Kanit", "Courier New", Courier, monospace;
      }
      :global(.ant-select-selection-item) {
        font-weight: 400;
        color: var(--dark-blue);
      }
      :global(.ant-select-selection-placeholder) {
        color: #0f205052;
        font-weight: 400;
      }
    }

    .communication_container {
      display: flex;
      justify-content: space-between;
    }

    .dimension {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 20px;
    }

    :global(.ant-picker-outlined),
    :global(.ant-input-number) {
      background-color: white;
      border: none !important;
      width: 200px;
      &:global(.ant-input-number-disabled) {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }

    :global(.ant-input-number-input) {
      color: var(--dark-blue);
      font-weight: 500;
      font-family: "Kanit", "Courier New", Courier, monospace;
    }

    :global(.ant-input-number-handler-wrap) {
      display: none;
    }

    :global(.ant-segmented) {
      background-color: white;
      border-radius: 10px;

      :global(.ant-segmented-item) {
        :global(.ant-segmented-item-label) {
          color: #0f20504a;
          font-weight: 400;
          font-family: "Kanit", "Courier New", Courier, monospace;
          background-color: transparent;
        }
        &::after {
          background-color: transparent !important;
          display: none;
        }
      }

      :global(.ant-segmented-item-selected) {
        background-color: var(--dark-blue) !important;
        color: white;
        box-shadow: none;
        border-radius: 10px;
        :global(.ant-segmented-item-label) {
          color: white;
        }
      }
      :global(.ant-segmented-thumb) {
        border-radius: 10px;
      }
    }

    .date_picker {
      border: none;
      outline: none;
      box-shadow: none;
      border-bottom: 1px solid var(--dark-blue);
      background-color: white;
      :global(.ant-picker-input > input) {
        font-weight: 300;
        color: var(--dark-blue);
        font-family: "Kanit", "Courier New", Courier, monospace;

        &::placeholder {
          font-weight: 600;
        }
      }
      &::placeholder {
        color: #0f20504a;
        font-weight: 600;
      }
      &:global(.ant-picker-outlined.ant-picker-disabled) {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }
  }

  .add_button {
    background-color: var(--dark-blue);
    color: white;
    border-radius: 50px;
    padding: 0.1rem;
    cursor: pointer;
    height: 30px;
    width: 30px;
    border: none;
    .icon_img {
      img {
        width: 100%;
        object-fit: contain;
      }
    }
  }
}

// Responsive View
@media screen and (max-width: 768px) {
  .flow_container {
    padding: 0.5rem;
    .filters_container {
      padding: 0.5rem;
      flex-direction: column;

      .input_text {
        width: 100%;
        font-size: 11px;
      }
      .custom_input {
        font-size: 10px;
      }
    }
    .row {
      padding: 0.5rem;

      .text {
        font-size: 10px;
      }
      .custom_select {
        font-size: 10px;
        :global(.ant-select-selector) {
          padding: 0.5rem;
        }
      }
    }
    .text {
      font-size: 11px;
    }
    .custom_select {
      :global(.ant-select-selection-item) {
        font-size: 10px;
      }
      :global(.ant-select-selection-placeholder) {
        font-size: 10px;
      }
    }

    :global(.ant-input-number-input) {
      font-size: 11px;
    }

    :global(.ant-segmented) {
      :global(.ant-segmented-item-label) {
        font-size: 11px !important;
      }
    }

    :global(.ant-select-dropdown) {
      :global(.ant-select-item-option) {
        font-size: 10px;
      }
    }
  }
}

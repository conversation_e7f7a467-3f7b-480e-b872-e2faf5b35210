class FileProcessingError(Exception):
    """Base exception for file processing errors"""
    def __init__(self, message, status_code=500, error_code=None):
        super().__init__(message)
        self.status_code = status_code
        self.error_code = error_code

class FileValidationError(FileProcessingError):
    """Exception for file validation errors"""
    def __init__(self, message, status_code=400, error_code="FILE_VALIDATION_ERROR"):
        super().__init__(message, status_code, error_code)

class ColumnMissingError(FileProcessingError):
    """Exception for missing required columns"""
    def __init__(self, missing_columns, found_columns=None, status_code=422, error_code="MISSING_COLUMNS"):
        message = f"Missing required columns: {', '.join(missing_columns)}"
        if found_columns:
            message += f". Found columns: {', '.join(found_columns[:10])}"
            if len(found_columns) > 10:
                message += f" and {len(found_columns) - 10} more"
        super().__init__(message, status_code, error_code)
        self.missing_columns = missing_columns
        self.found_columns = found_columns

class DataProcessingError(FileProcessingError):
    """Exception for data processing errors"""
    def __init__(self, message, status_code=422, error_code="DATA_PROCESSING_ERROR"):
        super().__init__(message, status_code, error_code)

class UnsupportedFileTypeError(FileProcessingError):
    """Exception for unsupported file types"""
    def __init__(self, file_type, bank_id, available_types=None, status_code=400, error_code="UNSUPPORTED_FILE_TYPE"):
        message = f"File type '{file_type}' is not supported for bank ID {bank_id}"
        if available_types:
            message += f". Supported types: {', '.join(available_types)}"
        super().__init__(message, status_code, error_code)

class EmptyFileError(FileProcessingError):
    """Exception for empty files"""
    def __init__(self, filename, status_code=422, error_code="EMPTY_FILE"):
        message = f"File '{filename}' is empty or contains no valid data"
        super().__init__(message, status_code, error_code)

class FileFormatError(FileProcessingError):
    """Exception for invalid file formats"""
    def __init__(self, filename, expected_formats, status_code=400, error_code="INVALID_FILE_FORMAT"):
        message = f"File '{filename}' has invalid format. Expected: {', '.join(expected_formats)}"
        super().__init__(message, status_code, error_code)

class ColumnMissingError(FileProcessingError):
    """Exception for missing required columns"""
    def __init__(self, missing_columns, found_columns, status_code=400, error_code="COLUMN_ERROR"):
        # Create a clear message showing exactly which columns are missing
        missing_str = ", ".join(missing_columns)
        found_str = ", ".join(found_columns)
        
        message = f"Missing required columns: {missing_str}. Found columns: {found_str}"
        super().__init__(message, status_code, error_code)
        
        # Store the missing columns for programmatic access
        self.missing_columns = missing_columns
        self.found_columns = found_columns
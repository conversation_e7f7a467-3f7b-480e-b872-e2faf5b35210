import React from 'react'
import {Table, Typography } from "antd";
import Style from "./_style.module.scss";
import { formatAmount, handleDateFormatter } from '../../../../constant';
import PropTypes from 'prop-types';

const {Text}  = Typography;

export function ClientDetailsTable({data}) {
    const columns = [
        {
          title: 'Sr. No.',
          dataIndex: 'key',
          rowScope: 'row',
          width:90,
          render:(_, {key})=> <Text>{key}.</Text>
        },
        {
          title: 'Date',
          dataIndex: 'Date',
          render:(date)=> <Text>{date? handleDateFormatter(date): "--"}</Text>,
        },
        {
          title: 'Unique Login',
          dataIndex: 'Unique_Login',
          render:(unique)=> <Text>{unique}</Text>,
        },
        {
          title: 'Total Login',
          dataIndex: 'Total_Login',
          render:(totalLogin)=> <Text>{totalLogin}</Text>,
        },
        {
          title: 'Total Engagement',
          dataIndex: 'Total_Engagement',
          render:(totalEngagement)=> <Text>{totalEngagement}</Text>,
        },
        {
          title: 'Unique Customers Connected',
          dataIndex: 'Unique_Customer_Connected',
          render:(uniqueCustomer)=> <Text>{uniqueCustomer}</Text>,
        },
        {
          title: 'Total Promise Made',
          dataIndex: 'Total_Promise_Made',
          render:(totalPromise)=> <Text>{totalPromise}</Text>,
        },
        {
          title: 'Total Promise Amount',
          dataIndex: 'Total_Promise_Amount',
          render:(totalPromiseAmount)=> <Text className={Style.name}>Rs.{formatAmount(totalPromiseAmount || 0)}</Text>,
        },
        {
          title: 'Feedback',
          dataIndex: 'Feedback',
          render:(feedback)=> <Text>{feedback}</Text>,
        },
        {
          title: 'Total Amount from Feedback',
          dataIndex: 'Feedback_Amount',
          render:(totalAmountFeedback)=> <Text className={Style.name}>Rs.{formatAmount(totalAmountFeedback || 0)}</Text>,
        },
    ];
          
    const dataSource = Array.isArray(data)?data.map((data, i) => ({
    key:i+1, 
    ...data
    })):[];
    
    return (
      <Table
        virtual
        className={Style.customTable}
        columns={columns}
        dataSource={dataSource}
          scroll={{
            x: 1500,
            y: 460,
          }}
        pagination={{
          showSizeChanger:false
        }}
      />
    )
}
ClientDetailsTable.propTypes={
  data: PropTypes.array
}
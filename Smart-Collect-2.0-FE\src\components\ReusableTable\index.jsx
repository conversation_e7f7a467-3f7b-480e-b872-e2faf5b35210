import { Table, Typography } from "antd";
import React, { useState } from "react";
import Style from './_reusable-table.module.scss';
import PropTypes from "prop-types";

const { Text } = Typography;

export default function ReusableTable({ data, tabType, tableName }) {
    const [selectedRegion, setSelectedRegion] = useState(null);
    const [selectedHubIndex, setSelectedHubIndex] = useState(null);
    const [selectedBranch, setSelectedBranch] = useState(null);

    // Determine final title/key/dataIndex based on tabType and tableName
    let amountColumnTitle = 'EMI Amount';
    let amountColumnKey = 'emiAmount';

    if (tabType === 'promise') {
        if (tableName === 'allocation') {
            amountColumnTitle = 'Promise Amount';
            amountColumnKey = 'promiseAmount';
        } else {
            amountColumnTitle = 'Collected Amount';
            amountColumnKey = 'collectedAmount';
        }
    }

    const handleRegionClick = (region) => {
        if (selectedRegion === region) {
            setSelectedRegion(null);
            setSelectedHubIndex(null);
            setSelectedBranch(null);
        } else {
            setSelectedRegion(region);
            setSelectedHubIndex(null);
            setSelectedBranch(null);
        }
    };

    const handleHubClick = (index) => {
        if (selectedHubIndex === index) {
            setSelectedHubIndex(null);
            setSelectedBranch(null);
        } else {
            setSelectedHubIndex(index);
            setSelectedBranch(null);
        }
    };

    const handleBranchClick = (index) => {
        if (selectedBranch === index) {
            setSelectedBranch(null);
        } else {
            setSelectedBranch(index);
        }
    };

    const handleLanPromiseClick = (lanPromise) => {
        console.log('LAN Promise clicked:', lanPromise);
    };

    const columns = [
        {
            title: 'Sr. No.',
            dataIndex: 'key',
            rowScope: 'row',
            width: 90,
            render: (_, data) => <Text>{data?.key}.</Text>
        },
        {
            title: 'Region',
            dataIndex: 'region',
            render: (_, data) => (
                <Text className={Style.blueText} onClick={() => handleRegionClick(data.region)}>
                    {data?.region}
                </Text>
            ),
        },
        ...(selectedRegion ? [{
            title: 'Hub',
            dataIndex: 'hub',
            render: (_, data, index) => (
                <button
                    className={Style.button}
                    disabled={data.region !== selectedRegion}
                    onClick={() => handleHubClick(index)}
                >
                    {data.region === selectedRegion ? data?.hub : '-'}
                </button>
            ),
        }] : []),
        ...(selectedHubIndex !== null ? [{
            title: 'Branch',
            dataIndex: 'branch',
            render: (_, data, index) => (
                <button
                    className={Style.button}
                    disabled={index !== selectedHubIndex}
                    onClick={() => handleBranchClick(index)}
                >
                    {index === selectedHubIndex ? data?.branch : '-'}
                </button>
            ),
        }] : []),
        ...(selectedBranch !== null ? [{
            title: 'Collection Officer',
            dataIndex: 'collectionOfficer',
            render: (_, data, index) => (
                <Text className={Style.text}>
                    {index === selectedBranch ? data.collectionOfficer : '-'}
                </Text>
            ),
        }] : []),
        {
            title: '#LAN',
            dataIndex: 'lanPromise',
            render: (_, data) => (
                <Text  onClick={() => handleLanPromiseClick(data.lanPromise)}  style={{ cursor: 'pointer', color: 'black' }}>
                    {data?.lanPromise}
                </Text>
            ),
        },
        {
            title: amountColumnTitle,
            dataIndex: amountColumnKey,
            key: amountColumnKey,
        },
    ];

    const sortedData = selectedRegion
        ? [...data].sort((a, b) => (a.region === selectedRegion ? -1 : 1))
        : data;

    const dataSource = Array.isArray(sortedData)? sortedData.map((data, i) => ({
        key: i + 1,
        collectedAmount: data.promiseAmount,
        ...data
    })):[];

    return (
            <Table
                bordered
                virtual
                className={Style.CustomTable}
                columns={columns}
                dataSource={dataSource}
                scroll={{
                    x:800,
                    y: 460,
                }}
                pagination={{
                    showSizeChanger:false
                }}
            />
    );
}

ReusableTable.propTypes ={
    data: PropTypes.array,
    tabType: PropTypes.string,
    tableName: PropTypes.string
}
import { Col, Flex, message, Row, Typography } from "antd";
import React, { useEffect, useState } from "react";
import AnalysisCommunicationCard from "./AnalysisCommunicationCard";
import TOTAL_COMM_SVG from "../../../assets/Images/Group 1261158451.svg";
import BLASTER_ANALYSIS_SVG from "../../../assets/Images/blaster-analysis.svg";
import AI_ANALYSIS_SVG from "../../../assets/Images/ai-analysis.svg";
import WHAT_ANALYSIS_SVG from "../../../assets/Images/whatsapp-analysis.svg";
import IVR_ANALYSIS_SVG from "../../../assets/Images/ivr-analysis.svg";
import SMS_ANALYSIS_SVG from "../../../assets/Images/sms-analysis.svg";
import Style from "./_style.module.scss";
import AnalysisCategoryCard from "./AnalysisCategoryCard";
import TotalPromise from "../../../assets/SVGs/TotalPromise";
import PartiallyFulfilled from "../../../assets/SVGs/PartiallyFulfilled";
import TotalClaim from "../../../assets/SVGs/TotalClaim";
import Pending from "../../../assets/SVGs/Pending";
import Broken from "../../../assets/SVGs/Broken";
import Fulfilled from "../../../assets/SVGs/Fulfilled";
import { AXIOS } from "../../../apis/ho-Instance";
import ApplicationLoader from "../../ApplicationLoader";
import { formatAmount, formatDigits } from "../../../constant";

const { Text } = Typography;

export default function AccountAnalysis() {
  const [isLoading, setIsLoading] = useState(false);
  const [details, setDetails] = useState({});

  const responseData = [
    {
      label: "Total Promises",
      icon: <TotalPromise />,
      count: details?.responses?.total_resp?.count,
      amount: details?.responses?.total_resp?.amount,
    },
    {
      label: "Pending",
      icon: <Pending />,
      count: details?.responses?.pending?.count,
      amount: details?.responses?.pending?.amount,
    },
    {
      label: "Broken",
      icon: <Broken />,
      count: details?.responses?.broken?.count,
      amount: details?.responses?.broken?.amount,
    },
    {
      label: "Fulfilled",
      icon: <Fulfilled />,
      count: details?.responses?.fulfilled?.count,
      amount: details?.responses?.fulfilled?.amount,
    },
    {
      label: "Partially Fulfilled",
      icon: <PartiallyFulfilled />,
      count: details?.responses?.partially_fulfilled?.count,
      amount: details?.responses?.partially_fulfilled?.amount,
    },
    {
      label: "Total Claim",
      icon: <TotalClaim />,
      count: details?.responses?.total_claim?.count,
      amount: details?.responses?.total_claim?.amount,
    },
  ];

  const analysisCardsData = [
    {
      title: "Total Communication",
      count: details?.comm_count?.total_comm,
      image: TOTAL_COMM_SVG,
    },
    {
      title: "Whatsapp",
      count: details?.comm_count?.whatsapp,
      image: WHAT_ANALYSIS_SVG,
    },
    {
      title: "AI BOT",
      count: details?.comm_count?.voice_bot,
      image: AI_ANALYSIS_SVG,
    },
    {
      title: "Blaster",
      count: details?.comm_count?.blaster,
      image: BLASTER_ANALYSIS_SVG,
    },
    {
      title: "IVR",
      count: details?.comm_count?.ivr,
      image: IVR_ANALYSIS_SVG,
    },
    {
      title: "SMS",
      count: details?.comm_count?.sms,
      image: SMS_ANALYSIS_SVG,
    },
  ];

  const handleFetchData = async () => {
    setIsLoading(true);
    try {
      const res = await AXIOS.get("accountsummaryanallysis/");
      if (res.status === 200) {
        setDetails(res.data?.data);
      }
    } catch (error) {
      console.log("Error in fetch analysis details", error?.message);
      message.error("Something went wrong, Try again!");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    handleFetchData();
  }, []);

  return isLoading ? (
    <ApplicationLoader />
  ) : (
    <Flex className={Style.analysis_container} vertical gap={20}>
      {/* Channels */}
      <Flex gap={20} justify="space-between" className={Style.container}>
        {analysisCardsData.map((card, index) => {
          return card.count !== 0 ? (
            <AnalysisCommunicationCard
              key={`${index}-${card.title}`}
              title={card.title}
              count={card.count}
              image={card.image}
            />
          ) : null;
        })}
      </Flex>

      <Row gutter={[40]} justify={"space-between"} style={{ margin: "0" }}>
        {/* Response */}
        <Col span={24} md={12} className={Style.responses_summary}>
          <Row className={Style.header} justify="end">
            <Col span={24}>
              <Text className={Style.heading}> Responses: </Text>
            </Col>
            <Col span={6} style={{ textAlign: "center" }}>
              <Text className={Style.title}>Count</Text>
            </Col>
            <Col span={6} style={{ textAlign: "center" }}>
              <Text className={Style.title}>Amount</Text>
            </Col>
          </Row>

          {responseData.map((item, idx) => (
            <Row
              className={Style.summary_row}
              key={`${idx}-${item.label}`}
              align="middle"
            >
              <Col span={12}>
                <Flex gap={10} align="center">
                  {item.icon}
                  <Text className={Style.label}>{item.label}:</Text>
                </Flex>
              </Col>
              <Col span={6} style={{ textAlign: "center" }}>
                <div className={Style.value}>
                  {formatDigits(item.count ?? 0)}
                </div>
              </Col>
              <Col span={6} style={{ textAlign: "center" }}>
                <div className={Style.amount}>
                  ₹ {formatAmount(item.amount ?? 0)}
                </div>
              </Col>
            </Row>
          ))}
        </Col>

        {/* collection & matrix */}
        <Col span={24} md={12} style={{ paddingRight: 0 }} className={Style.collection_matrix}>
          <Row gutter={[0, 20]}>
            <Col span={24} className={Style.collection_summary}>
              <Row className={Style.header} justify="end">
                <Col span={12} style={{ textAlign: "center" }}>
                  <Text className={Style.count_input}>Count</Text>
                </Col>
                <Col span={12} style={{ textAlign: "center" }}>
                  <Text className={Style.amount_input}>Amount</Text>
                </Col>
              </Row>

              <Row className={Style.summary_row}>
                <Col span={12}>
                  <Text className={Style.label}>Total Collection:</Text>
                </Col>
                <Col span={12}>
                  <Row className={Style.collection_count}>
                    <Col span={12}>
                      <Text className={Style.count_input}>
                        {formatDigits(details?.total_collection?.count ?? 0)}
                      </Text>
                    </Col>
                    <Col span={12}>
                      <Text className={Style.amount_input}>
                        ₹ {formatAmount(details?.total_collection?.amount ?? 0)}
                      </Text>
                    </Col>
                  </Row>
                </Col>
              </Row>

              <Row className={Style.summary_row}>
                <Col span={12}>
                  <Text className={Style.label}>
                    Collection Against Promise:
                  </Text>
                </Col>
                <Col span={12}>
                  <Row className={Style.collection_count}>
                    <Col span={12}>
                      <Text className={Style.count_input}>
                        {formatDigits(details?.col_against_promise?.count)}
                      </Text>
                    </Col>
                    <Col span={12}>
                      <Text className={Style.amount_input}>
                        ₹ {formatAmount(details?.col_against_promise?.amount)}
                      </Text>
                    </Col>
                  </Row>
                </Col>
              </Row>
            </Col>

            <Col span={24} className={Style.matrix_summary}>
              <Flex vertical gap={20}>
                <Text className={Style.heading}> Comparison Matrix: </Text>

                <Flex vertical className={Style.count_container}>
                  <Row
                    gutter={[20, 0]}
                    style={{ borderBottom: "1px solid #0F2050" }}
                  >
                    <Col span={24} md={12} className={Style.summary} style={{ borderRight: "1px solid #0F2050" }}>
                      <Text className={Style.label}>Initial Overdue -</Text>
                      <Text className={Style.value}>
                        {formatAmount(
                          details?.comparison_metrics?.initial_overdue ?? 0
                        )}
                      </Text>
                    </Col>
                    <Col span={24} md={12} className={Style.summary}>
                      <Text className={Style.label}>Current Overdue -</Text>
                      <Text className={Style.value}>
                        {formatAmount(
                          details?.comparison_metrics?.current_overdue ?? 0
                        )}
                      </Text>
                    </Col>
                  </Row>

                  <Row gutter={[20, 0]}>
                    <Col span={24} md={12} className={Style.summary} style={{ borderRight: "1px solid #0F2050" }}>
                      <Text className={Style.label}>Initial Avg. DPD -</Text>
                      <Text className={Style.value}>
                        {formatDigits(
                          details?.comparison_metrics?.initial_dpd ?? 0
                        )}
                      </Text>
                    </Col>
                    <Col span={24} md={12} className={Style.summary}>
                      <Text className={Style.label}>Current Avg. DPD -</Text>
                      <Text className={Style.value}>
                        {formatDigits(
                          details?.comparison_metrics?.current_dpd ?? 0
                        )}
                      </Text>
                    </Col>
                  </Row>
                </Flex>
              </Flex>
            </Col>
          </Row>
        </Col>
      </Row>

      {/* Dpd categories */}
      <Flex vertical gap={10} className={Style.category_container}>
        <Text className={Style.heading}>DPD Category wise Customers:</Text>
        <Flex gap={20} justify="space-between" className={Style.container}>
          <AnalysisCategoryCard
            title={"Good"}
            initial={details?.dpd_count?.good?.before}
            current={details?.dpd_count?.good?.current}
            textColor={"#02951A"}
          />
          <AnalysisCategoryCard
            title={"Arrear"}
            initial={details?.dpd_count?.arrear?.before}
            current={details?.dpd_count?.arrear?.current}
            textColor={"#CB4848"}
          />
          <AnalysisCategoryCard
            title={"NPA"}
            initial={details?.dpd_count?.npa?.before}
            current={details?.dpd_count?.npa?.current}
            textColor={"#05989F"}
          />
          <AnalysisCategoryCard
            title={"NPA+"}
            initial={details?.dpd_count?.npa_plus?.before}
            current={details?.dpd_count?.npa_plus?.current}
            textColor={"#23379C"}
          />
          <AnalysisCategoryCard
            title={"NPA 2"}
            initial={details?.dpd_count?.npa2?.before}
            current={details?.dpd_count?.npa2?.current}
            textColor={"#B8D90C"}
          />
        </Flex>
      </Flex>
    </Flex>
  );
}

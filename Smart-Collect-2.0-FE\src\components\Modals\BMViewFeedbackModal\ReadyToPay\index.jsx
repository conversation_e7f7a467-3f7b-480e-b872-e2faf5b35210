import {
  DatePicker,
  Flex,
  InputNumber,
  Segmented,
  Typography,
} from "antd";
import React from "react";
import Style from "./_ready.module.scss";
import dayjs from "dayjs";
import PropTypes from "prop-types";

const { Text } = Typography;

export function ReadyToPay({ latestFeedBack }) {
  const dateFormatList = ["DD/MM/YYYY", "DD/MM/YY", "DD-MM-YYYY", "DD-MM-YY"];

  return (
    <div className={Style.ready_to_pay}>
      <Flex vertical gap={20} justify="center">
        {/* date */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Date:</Text>
          <DatePicker
            className={Style.date_picker}
            placeholder="DD/MM/YYYY"
            format={dateFormatList}
            value={
              latestFeedBack?.promise_date
                ? dayjs(latestFeedBack.promise_date)
                : null
            }
            disabled
          />
        </Flex>

        {/* amount */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Amount:</Text>
          <InputNumber
            defaultValue={latestFeedBack?.promise_amount}
            addonBefore={"₹"}
            formatter={(value) => {
              const num = Number(value);
              return isNaN(num) ? "" : num.toLocaleString("en-IN");
            }}
            parser={(value) => value?.replace(/[₹,]/g, "")}
            disabled
          />
        </Flex>

        {/* mode */}
        <Flex justify="space-between" align="center">
          <Text className={Style.text}>Mode of Payment:</Text>

          <Segmented
            value={latestFeedBack.mode_of_payment}
            style={{
              marginBottom: 8,
            }}
            options={["Online", "Offline"]}
            disabled
          />
        </Flex>
      </Flex>
    </div>
  );
}

ReadyToPay.propTypes={
  latestFeedBack: PropTypes.object
}
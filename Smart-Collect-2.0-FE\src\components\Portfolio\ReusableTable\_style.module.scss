@import url("../../../index.css");

$light-blue: #E4F8F9;
$table-radius:22px;
$disable:#787777;
$body:#E4F8F9;
$light-blue: #E4F8F9;
$blue: #BECFFF;
$row-bottom:#E5E3E3;
$bottom-color:#949494;
$column-blue:#C5EDDF;
$column-yellow:#F2ECB8;

.reuseable_table {
    padding-top: 1rem;

    .text {
        font-weight: 500;
        white-space: nowrap;
        color: var(--dark-blue);
    }

    .blueText {
        font-weight: 400;
        color: var(--blue);
        cursor: pointer;
    }

    .button {
        border: none;
        background-color: transparent;
        color: var(--blue);
        font-weight: 700;
        cursor: pointer;
    }

    .customer_count{
       border-radius: 2px;
       background-color: white;
       border: 1px solid #305496;
       padding: 0rem 0.5rem;
    }

    :global(.ant-table){
        background-color: $light-blue;
        border-start-start-radius: $table-radius;
        border-start-end-radius: $table-radius;
    }

    // Global ant design classes

    // Pagination
    :global(.ant-pagination) {
        justify-content: center;
        margin: 0 !important;

        :global(.ant-pagination-prev),
        :global(.ant-pagination-next) {
            color: var(--dark-blue);
            border: 0 solid var(--blue);
            background: #E4F8F9;
            height: 25px;
            min-width: 15px;
            border-radius: 0px;
            margin: 0;

            button {
                border-radius: 0px;
            }
        }

        :global(.ant-pagination-item) {
            margin-right: 0;
            height: 0;
            a {
                color: $disable;
                font-size: 0.9rem;
                line-height: 23px;
            }

            &:hover {
                background-color: transparent;
            }
        }

        :global(.ant-pagination-item-active) {
            border: none;

            a {
                color: var(--dark-blue);
                font-size: 1.2rem;
                padding: 0;
            }
        }
    }

    // Table container
    :global(.ant-table-container) {
        padding: 0 1rem 0.5rem 1rem;
        margin-bottom: 0.5rem;
        background: transparent;
        border-start-start-radius: $table-radius;
        border-start-end-radius: $table-radius;
        position: relative;

        // Table header
        :global(.ant-table-header) {
            position: relative;
            margin: 0 auto;
            top: -21px;
            border-radius: $table-radius;
        }

        // Table virtual body
        :global(.ant-table-tbody-virtual) {
            margin-top: -8px;
            position: static !important;

            &::-webkit-scrollbar{
                display: none;
            }
        }
        &::before {
            box-shadow: none !important;
        }
    }

    // Table rows header 
    :global(.ant-table-thead > tr) {
        th {
            border-start-end-radius: 0 !important;
            background-color: var(--dark-blue);
            border-inline-end: none !important;
            color: white;
            border-bottom: none;
            white-space: nowrap;
            text-align: center;
            font-family: 'Kanit','Courier New', Courier, monospace;
            font-weight: normal;

            &:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
                width: 0;
            }
            &:global(.ant-table-cell-scrollbar){
                &:not([rowspan]){
                    box-shadow: none;
                }
            }
        }
    }

    // Table body
    :global(.ant-table-tbody) {
        // Body rows
        :global(.ant-table-row) {
            // Cols
            :global(.ant-table-cell) {
                font-weight: 500;
                text-align: center;
                padding: 10px; 
                font-family: 'Kanit','Courier New', Courier, monospace;
                border: none;

                :global(.ant-typography){
                    font-family: 'Kanit','Courier New', Courier, monospace;
                    color: #284A88;
                }
                
                &:first-child {
                    border-right: none;
                }

                &:nth-child(2) {
                    border-right: none;
                }

                &:global(.ant-table-cell-row-hover) {
                    background-color: transparent;
                }
            }
        }
    }

    // Fixed Cols
    :global(.ant-table-cell-fix-right) {
        background-color: $body;
    }

    // Footer
    :global(.ant-table-summary){
        text-align: center;
        box-shadow: 0px 4px 4px 0px #0000001A inset;
        
        :global(.ant-table-cell){
            font-family: 'Kanit','Courier New', Courier, monospace;
            font-weight: 500;
            padding: 0.6rem;
        }
    }
}
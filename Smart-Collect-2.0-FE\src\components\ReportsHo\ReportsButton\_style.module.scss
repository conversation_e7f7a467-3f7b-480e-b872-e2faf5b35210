@import url("../../../index.css");

$light-blue: #e3f5f6;

.container {
  position: relative;

  .button_container {
    background-color: var(--dark-blue);
    color: white;
    width: 80px;
    box-shadow: 0px 4px 4px 0px #00000040;
    border-radius: 10px;
    .img_container {
      display: flex;
      justify-content: center;
      cursor: pointer;
      input[type="image"] {
        width: 15px;
        padding: 0.5rem 0;
        cursor: pointer;
      }
    }
  }

  .modal {
    position: absolute;
    right: 0;
    top: 0;
    background-color: $light-blue;
    box-shadow: 4px 4px 4px 0px #00000040;
    width: 350px;
    padding: 0.4rem 1rem;
    z-index: 4;
    border-radius: 10px;

    .text {
      text-align: center;
      color: var(--dark-blue);
      font-family: "Kanit", "Courier New", Courier, monospace;
      font-size: 16px;
    }

    .fields {
      color: var(--dark-blue);
      font-family: "Kanit", "Courier New", Courier, monospace;
    }

    .apply {
      background-color: var(--dark-blue) !important;
      color: white !important;
      border: none;
      outline: none;
      box-shadow: none;
      font-family: "Kanit", "Courier New", Courier, monospace;
      padding: 1rem 2rem;
      margin: 1rem 0;
    }

    .clear {
      background-color: $light-blue !important;
      color: var(--dark-blue) !important;
      outline: none;
      box-shadow: none;
      font-family: "Kanit", "Courier New", Courier, monospace;
      border: 1px solid #0f2050 !important;
      padding: 1rem 2rem;
      margin: 1rem 0;
    }

    .custom_select {
      :global(.ant-select-selector) {
        background-color: $light-blue;
        box-shadow: none !important;
        border: none;
        outline: none;
        border-bottom: 1px solid var(--dark-blue) !important;
        font-family: "Kanit", "Courier New", Courier, monospace;
      }
      :global(.ant-select-selection-item) {
        font-weight: 400;
        color: var(--dark-blue);
        font-family: "Kanit", "Courier New", Courier, monospace;
      }
    }

    .custom_input {
      width: 300px;
      caret-color: #407bff;
      &:global(.ant-input-outlined) {
        border: none;
        border-bottom: 1px solid var(--dark-blue);
        box-shadow: none;
        background-color: $light-blue;
        color: var(--dark-blue);
        font-weight: 400;
        font-family: "Kanit", "Courier New", Courier, monospace;
        &:focus-within {
          box-shadow: none !important;
        }
      }
    }

    .date_picker {
      border: none;
      outline: none;
      box-shadow: none;
      border-bottom: 1px solid var(--dark-blue);
      background-color: transparent;
      :global(.ant-picker-input > input) {
        font-weight: 300;
        color: var(--dark-blue);
        font-family: "Kanit", "Courier New", Courier, monospace;

        &::placeholder {
          font-weight: 600;
        }
      }
      &::placeholder {
        color: #0f20504a;
        font-weight: 600;
      }
    }
  }

  .date_picker {
    border: none;
    outline: none;
    box-shadow: none;
    border-bottom: 1px solid var(--dark-blue);
    background-color: transparent;
    :global(.ant-picker-input > input) {
      font-weight: 300;
      color: var(--dark-blue);
      font-family: "Kanit", "Courier New", Courier, monospace;

      &::placeholder {
        font-weight: 600;
      }
    }
    &::placeholder {
      color: #0f20504a;
      font-weight: 600;
    }
  }
}

// Responsive View
@media only screen and (max-width: 768px) {
  .container {
    .modal {
      width: 250px;
      padding: 0.4rem 0.5rem;

      .container {
        gap: 0px !important;
      }

      .text {
        font-size: 14px;
      }

      .fields {
        font-size: 12px;
      }

      .apply,
      .clear {
        font-size: 12px;
        padding: 0.3rem 1rem;
        height: auto;
        margin: 0.4rem 0;
      }

      .custom_select {
        :global(.ant-select-selector) {
          background-color: $light-blue;
          box-shadow: none !important;
          border: none;
          outline: none;
          border-bottom: 1px solid var(--dark-blue) !important;
          font-family: "Kanit", "Courier New", Courier, monospace;
          font-size: 12px;
        }
        :global(.ant-select-selection-item) {
          font-weight: 400;
          color: var(--dark-blue);
          font-family: "Kanit", "Courier New", Courier, monospace;
          font-size: 12px;
        }
      }

      .custom_input {
        width: 100%;
        font-size: 13px;
      }

      .date_picker {
        :global(.ant-picker-input > input) {
          font-size: 12px;
        }
      }
    }
  }
}

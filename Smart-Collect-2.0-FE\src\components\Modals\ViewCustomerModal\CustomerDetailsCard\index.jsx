import { Flex, message, Typography } from "antd";
import React, { useMemo, useState } from "react";
import PROFILE_IMG from "../../../../assets/Images/gg_profile.png";
import HISTORY_IMG from "../../../../assets/Images/history-white.png";
import PHONE_IMG from "../../../../assets/Images/mi_call.png";
import DISBURSEMENT_IMG from "../../../../assets/Images/disbursement-id.svg";
import EDIT_IMG from "../../../../assets/Images/edit.svg";
import LOAN_IMG from "../../../../assets/Images/carbon_global-loan-and-trial.svg";
import DATE_IMG from "../../../../assets/Images/clarity_date-line.png";
import {
  SMART_COLLECT_MENU_IDS,
  handleDateFormatter,
  RECENT_ENGAGEMENT_APPS_DATA,
  STATUS_IDS,
  formatAmount,
} from "../../../../constant";
import { Link } from "react-router";
import { RecentEngagement } from "../../../RecentEngagement";
import { FeedbackModal } from "../../../Modals/FeedbackModal";
import { Details } from "./Details";
import UpdateNumberModal from "../../../Modals/UpdateNumberModal";
import Style from "./_customer-details-card.module.scss";
import { DialerModal } from "../../../Modals/DialerModal";
import { AXIOS } from "../../../../apis/ho-Instance";
import { ConversionHistory } from "../../../Modals/ConversionHistory";
import { ViewFeedbackModal } from "../../../Modals/ViewFeedbackModal";
import PropTypes from "prop-types";

const { Title, Text } = Typography;

export function CustomerDetailsCard({
  loanmstid,
  branchMstId,
  communicationCount = 0,
  last5CommunicationTypes,
  status,
  name,
  customerId,
  disbursementId,
  branchName,
  loanType,
  overdueAmount,
  promiseAmount,
  promiseDate,
  feedbackDate,
  dpdAmount,
  modalButtonText,
  isModal = false,
  pageId = null,
}) {
  const [messageApi, contextHolder] = message.useMessage();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isViewFeedbackModalOpen, setIsViewFeedbackModalOpen] = useState(false);
  const [isDialerModalOpen, setIsDialerModalOpen] = useState(false);
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Convert API data into UI data for RecentEngagement
  const recentEngagementAppsData = useMemo(() => {
    if (!last5CommunicationTypes || !Array.isArray(last5CommunicationTypes))
      return [];

    return last5CommunicationTypes
      .slice(-5)
      .map(({ source, delivery_status }) => {
        const appData = RECENT_ENGAGEMENT_APPS_DATA.find(
          (app) => app.id?.toLowerCase() === source?.toLowerCase()
        );

        if (!appData) return null;

        return {
          ...appData,
          status:
            delivery_status === "Delivered"
              ? STATUS_IDS.SUCCESS
              : STATUS_IDS.REJECTED, // Green for Delivered, Red for Not Delivered
        };
      })
      .filter(Boolean); // Remove null entries
  }, [last5CommunicationTypes, communicationCount]);

  const handleSubmit = () => setIsModalOpen(false);
  const handleCancel = () => setIsModalOpen(false);
  const handleViewFeedbackCancel = () => setIsViewFeedbackModalOpen(false);

  const handleDialerSubmit = async ({
    number,
    loanmstid,
    overdueAmount,
    branchMstId,
  }) => {
    setIsLoading(true);
    messageApi.success("Initiate call");
    // Close the dialer & open the feedback modal
    setIsModalOpen(true);
    setIsDialerModalOpen(false);

    try {
      await AXIOS.post("v1/dialer/", {
        loanMstId: loanmstid,
        bm_phone: number,
      });
    } catch (error) {
      console.log("Error in dialer", error?.message);
      console.error("Error in dialer", error?.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDialerCancel = () => {
    setIsDialerModalOpen(false);
  };

  const handleHistoryCancel = () => {
    setIsHistoryModalOpen(false);
  };

  return (
    <>
      {contextHolder}
      <Flex vertical className={Style.card_container}>
        {/* customer details */}
        <Flex
          justify="space-between"
          align="center"
          className={Style.customer_details}
        >
          <Flex gap={10} align="start" className={Style.profile_container}>
            {/* icon */}
            <div className={Style.profile_img}>
              <img src={PROFILE_IMG} alt={name} />
            </div>

            {/* details */}
            <Flex vertical gap={5}>
              <Title level={4} className={Style.name}>
                {name}
              </Title>
              <Text className={Style.customer_id}>
                Customer Id: {customerId}
              </Text>
            </Flex>
          </Flex>

          {/* icons */}
          <Flex gap={20}>
            <div className={Style.profile_img}>
              <input
                type="image"
                src={PHONE_IMG}
                alt="phone"
                onClick={() => setIsDialerModalOpen(true)}
              />
            </div>
            <div className={Style.profile_img}>
              <input
                type="image"
                src={HISTORY_IMG}
                alt="history"
                onClick={() => setIsHistoryModalOpen(true)}
              />
            </div>
          </Flex>
        </Flex>

        {/* payments details */}
        <Flex vertical gap={2} className={Style.payment_details}>
          <div className={Style.attempts}>
            <Flex justify="space-between" align="center">
              <Text className={Style.attempts_value}>
                No. of Attempts:{" "}
                <span style={{ color: "black" }}>{communicationCount}</span>
              </Text>

              {recentEngagementAppsData.length ? (
                <RecentEngagement
                  recentEngagementAppsData={recentEngagementAppsData}
                />
              ) : null}
            </Flex>
          </div>

          <Details
            image={DISBURSEMENT_IMG}
            title={"Disbursement Id"}
            value={disbursementId}
          />
          <Details image={EDIT_IMG} title={"Branch Name"} value={branchName} />
          <Details image={LOAN_IMG} title={"Loan Type"} value={loanType} />
        </Flex>

        {/* amount details */}
        <Flex
          justify="space-evenly"
          align="center"
          gap={20}
          className={Style.amount_details}
        >
          <Flex vertical align="center" gap={5}>
            <Title level={4} className={`${Style.amount}`}>
              {dpdAmount}
            </Title>
            <Text className={Style.text}>DPD</Text>
          </Flex>
          {pageId !== SMART_COLLECT_MENU_IDS.ALREADY_PAY && (
            <Flex vertical align="center" gap={5}>
              <Title level={4} className={`${Style.amount} ${Style.error}`}>
                Rs.{formatAmount(overdueAmount)}
              </Title>
              <Link
                to="#"
                className={Style.text}
                style={{ textDecoration: "underline" }}
              >
                Overdue Amount
              </Link>
            </Flex>
          )}
          {pageId === SMART_COLLECT_MENU_IDS.READY_TO_PAY && (
            <Flex vertical align="center" gap={5}>
              <Title level={4} className={`${Style.amount} ${Style.success}`}>
                Rs.{formatAmount(promiseAmount)}
              </Title>
              <Text className={Style.text}> Promise Amount</Text>
            </Flex>
          )}
          {pageId === SMART_COLLECT_MENU_IDS.ALREADY_PAY ? (
            <Flex vertical align="center" gap={5}>
              <Title level={4} className={`${Style.amount} ${Style.error}`}>
                Rs.{formatAmount(overdueAmount)}
              </Title>
              <Text className={Style.text}> Claim Amount</Text>
            </Flex>
          ) : null}
        </Flex>

        {/* dates */}
        <Flex vertical gap={2} className={Style.date_details}>
          <Details
            image={DATE_IMG}
            title={
              pageId === SMART_COLLECT_MENU_IDS.REFUSED_TO_PAY
                ? "Response Date"
                : "Promise date"
            }
            value={handleDateFormatter(promiseDate)}
          />

          {/* Need Change the icon & value */}
          {pageId === SMART_COLLECT_MENU_IDS.READY_TO_PAY && (
            <Details image={DATE_IMG} title={"Promise Status"} value={status} />
          )}

          {feedbackDate && (
            <>
              <Details
                image={DATE_IMG}
                title={"Latest BM Feedback Date"}
                value={handleDateFormatter(feedbackDate)}
              />

              <Link to="#" className={Style.view_link}>
                <Text
                  onClick={() => {
                    setIsViewFeedbackModalOpen(true);
                  }}
                >
                  View
                </Text>
              </Link>
            </>
          )}
        </Flex>

        {/* modalButton */}
        {isModal ? (
          <Flex align="center" className={Style.modal_button_container}>
            <button
              className={Style.modal_button}
              onClick={() => {
                setIsModalOpen(true);
              }}
            >
              {modalButtonText}
            </button>
          </Flex>
        ) : (
          <Flex
            justify="space-between"
            align="center"
            gap={2}
            className={Style.number_status}
          >
            <Text style={{ fontWeight: 500 }}>Number Status:</Text>
            <Text className={Style.status}>{status ?? "Not reachable"}</Text>
          </Flex>
        )}

        {pageId === SMART_COLLECT_MENU_IDS.WRONG_NUMBER ? (
          <UpdateNumberModal
            loanMstId={loanmstid}
            modalStatus={isModalOpen}
            handleSubmit={handleSubmit}
            handleCancel={handleCancel}
          />
        ) : (
          <FeedbackModal
            pageId={pageId}
            branchMstId={branchMstId}
            loanmstid={loanmstid}
            overdueAmount={overdueAmount}
            modalStatus={isModalOpen}
            handleSubmit={handleSubmit}
            handleCancel={handleCancel}
          />
        )}

        <DialerModal
          customerName={name}
          overdueAmount={overdueAmount}
          loanmstid={loanmstid}
          isLoading={isLoading}
          modalStatus={isDialerModalOpen}
          handleSubmit={handleDialerSubmit}
          handleCancel={handleDialerCancel}
        />

        <ConversionHistory
          customerName={name}
          loanMstId={loanmstid}
          modalStatus={isHistoryModalOpen}
          handleCancel={handleHistoryCancel}
        />

        <ViewFeedbackModal
          loanMstId={loanmstid}
          modalStatus={isViewFeedbackModalOpen}
          handleCancel={handleViewFeedbackCancel}
        />
      </Flex>
    </>
  );
}

CustomerDetailsCard.propTypes = {
  loanmstid: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  branchMstId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  communicationCount: PropTypes.number,
  last5CommunicationTypes: PropTypes.array,
  status: PropTypes.string,
  name: PropTypes.string,
  customerId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  disbursementId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  branchName: PropTypes.string,
  loanType: PropTypes.string,
  overdueAmount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  promiseAmount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  promiseDate: PropTypes.string,
  feedbackDate: PropTypes.string,
  dpdAmount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  modalButtonText: PropTypes.string,
  isModal: PropTypes.bool,
  pageId: PropTypes.string,
};

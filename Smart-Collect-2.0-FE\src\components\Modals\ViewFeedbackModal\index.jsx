import { Mo<PERSON>, Ta<PERSON>, <PERSON>po<PERSON>, <PERSON>lex, Spin } from "antd";
import React, { useEffect, useState } from "react";
import FEEDBACK_IMG from "../../../assets/Images/fluent-mdl2_feedback.png";
import Style from "./_feedback.module.scss";
import { ReadyToPay } from "./ReadyToPay";
import { RefusedToPay } from "./RefusedToPay";
import { Collection } from "./Collection";
import { Other } from "./Other";
import { AXIOS } from "../../../apis/ho-Instance";
import PropTypes from "prop-types";

const { Title } = Typography;

export function ViewFeedbackModal({
  modalStatus = false,
  handleSubmit,
  handleCancel,
  loanMstId,
}) {
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);
  const [latestFeedBack, setLatestFeedBack] = useState(null);
  const [activeTabKey, setActiveTabKey] = useState("readyToPay"); // Default active tab
  const [items, setItems] = useState([]);

  const handleOk = () => {
    setIsFeedbackModalOpen(false);
    handleSubmit();
  };

  const handleCross = () => {
    setIsFeedbackModalOpen(false);
    handleCancel();
  };

  // Fetch latest feedback
  const getLatestFeedback = async (loanMstId) => {
    if (!loanMstId) return;
    try {
      const response = await AXIOS.get(
        `latest_feedback/?LoanMstID=${loanMstId}`
      );
      setLatestFeedBack(response.data.latest_feedback);
    } catch (error) {
      console.error("Error fetching feedback:", error);
    }
  };

  useEffect(() => {
    setIsFeedbackModalOpen(modalStatus);
    if (modalStatus) {
      getLatestFeedback(loanMstId);
    }
  }, [modalStatus]);

  // Update tabs dynamically when latestFeedBack updates
  useEffect(() => {
    if (!latestFeedBack) return;

    const updatedItems = [
      {
        key: "readyToPay",
        label: "Ready to Pay",
        children: (
          <ReadyToPay
            handleCross={handleCross}
            latestFeedBack={latestFeedBack}
          />
        ),
      },
      {
        key: "refusedToPay",
        label: "Refused to Pay",
        children: (
          <RefusedToPay
            handleCross={handleCross}
            latestFeedBack={latestFeedBack}
          />
        ),
      },
      {
        key: "collection",
        label: "Collection",
        children: (
          <Collection
            handleCross={handleCross}
            latestFeedBack={latestFeedBack}
          />
        ),
      },
      {
        key: "other",
        label: "Other",
        children: <Other latestFeedBack={latestFeedBack} />,
      },
    ];

    setItems(updatedItems);

    // Dynamically set active tab based on latest feedback
    if (latestFeedBack.AgreedToPay) {
      setActiveTabKey("readyToPay");
    } else if (latestFeedBack.RefusedToPay) {
      setActiveTabKey("refusedToPay");
    } else if (latestFeedBack.WrongNumber || latestFeedBack.CustomerBusy) {
      setActiveTabKey("other");
    } else if (
      latestFeedBack.CollectionAmount ||
      latestFeedBack.CollectionDate
    ) {
      setActiveTabKey("collection");
    }
  }, [latestFeedBack]);

  return (
    <Modal
      centered
      className={Style.feedback_modal}
      title={
        <Flex align="center" gap={5} justify="center">
          <div className={Style.icon_img}>
            <img src={FEEDBACK_IMG} alt="icon" />
          </div>
          <Title level={4} className={Style.feedback_title}>
            Feedback
          </Title>
        </Flex>
      }
      open={isFeedbackModalOpen}
      onOk={handleOk}
      onCancel={handleCross}
      footer={null}
    >
      <Flex vertical gap={10}>
        {items.length > 0 ? (
          <Tabs
            activeKey={activeTabKey}
            items={items.map((item) => ({
              ...item,
              disabled: item.key !== activeTabKey, // Disable tabs that are not active
            }))}
          />
        ) : (
          <Spin tip={"Loading..."}></Spin>
        )}
      </Flex>
    </Modal>
  );
}

ViewFeedbackModal.propTypes={
  modalStatus: PropTypes.bool,
  handleSubmit: PropTypes.func,
  handleCancel: PropTypes.func,
  loanMstId: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
}
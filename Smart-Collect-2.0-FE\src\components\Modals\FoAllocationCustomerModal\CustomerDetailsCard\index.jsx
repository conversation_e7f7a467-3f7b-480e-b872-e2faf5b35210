import { Flex, message, Typography } from "antd";
import React, { useState } from "react";
import PROFILE_IMG from "../../../../assets/Images/gg_profile.png";
import HISTORY_IMG from "../../../../assets/Images/history-white.png";
import PHONE_IMG from "../../../../assets/Images/mi_call.png";
import DISBURSEMENT_IMG from "../../../../assets/Images/disbursement-id.svg";
import EDIT_IMG from "../../../../assets/Images/edit.svg";
import LOAN_IMG from "../../../../assets/Images/carbon_global-loan-and-trial.svg";
import DATE_IMG from "../../../../assets/Images/clarity_date-line.png";
import {
  SMART_COLLECT_MENU_IDS,
  handleDateFormatter,
  formatAmount,
} from "../../../../constant";
import { Link } from "react-router";
import { FeedbackModal } from "../../../Modals/FeedbackModal";
import { Details } from "./Details";
import Style from "./_customer-details-card.module.scss";
import { DialerModal } from "../../../Modals/DialerModal";
import { AXIOS } from "../../../../apis/ho-Instance";
import { ConversionHistory } from "../../../Modals/ConversionHistory";
import { ViewFeedbackModal } from "../../../Modals/ViewFeedbackModal";
import PropTypes from "prop-types";

const { Title, Text } = Typography;

export function CustomerDetailsCard({
  loanmstid,
  branchMstId,
  status,
  name,
  contact,
  branchName,
  disbursementId,
  dpdAmount,
  feedbackDate,
  loanType,
  overdueAmount,
  promiseAmount,
  promiseDate,
  pendingScene,
  modalButtonText,
  isModal = false,
}) {
  const [messageApi, contextHolder] = message.useMessage();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isViewFeedbackModalOpen, setIsViewFeedbackModalOpen] = useState(false);
  const [isDialerModalOpen, setIsDialerModalOpen] = useState(false);
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = () => setIsModalOpen(false);
  const handleCancel = () => setIsModalOpen(false);
  const handleViewFeedbackCancel = () => setIsViewFeedbackModalOpen(false);

  const handleDialerSubmit = async ({
    number,
    loanmstid,
    overdueAmount,
    branchMstId,
  }) => {
    setIsLoading(true);
    messageApi.success("Initiate call");
    // Close the dialer & open the feedback modal
    setIsModalOpen(true);
    setIsDialerModalOpen(false);

    try {
      await AXIOS.post("v1/dialer/", {
        loanMstId: loanmstid,
        bm_phone: number,
      });
    } catch (error) {
      console.log("Error in allocation customer", error?.message);
      console.error("Error in dialer", error?.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDialerCancel = () => {
    setIsDialerModalOpen(false);
  };

  const handleHistoryCancel = () => {
    setIsHistoryModalOpen(false);
  };

  return (
    <>
      {contextHolder}
      <Flex vertical className={Style.card_container}>
        {/* customer details */}
        <Flex
          justify="space-between"
          align="center"
          className={Style.customer_details}
        >
          <Flex gap={10} align="start" className={Style.profile_container}>
            {/* icon */}
            <div className={Style.profile_img}>
              <img src={PROFILE_IMG} alt={name} />
            </div>

            {/* details */}
            <Flex vertical gap={5}>
              <Title level={4} className={Style.name}>
                {name}
              </Title>
              <Text className={Style.customer_id}>Branch: {branchName}</Text>
            </Flex>
          </Flex>

          {/* icons */}
          <Flex gap={20}>
            <div className={Style.profile_img}>
              <input
                type="image"
                src={PHONE_IMG}
                alt="phone"
                onClick={() => setIsDialerModalOpen(true)}
              />
            </div>
            <div className={Style.profile_img}>
              <input
                type="image"
                src={HISTORY_IMG}
                alt="history"
                onClick={() => setIsHistoryModalOpen(true)}
              />
            </div>
          </Flex>
        </Flex>

        {/* payments details */}
        <Flex vertical gap={2} className={Style.payment_details}>
          <Details
            image={DISBURSEMENT_IMG}
            title={"Disbursement Id"}
            value={disbursementId}
          />
          <Details image={EDIT_IMG} title={"Contact"} value={contact} />
          <Details image={LOAN_IMG} title={"Loan Type"} value={loanType} />
        </Flex>

        {/* amount details */}
        <Flex
          justify="space-evenly"
          align="center"
          gap={20}
          className={Style.amount_details}
        >
          <Flex vertical align="center" gap={5}>
            <Title level={4} className={`${Style.amount}`}>
              {dpdAmount}
            </Title>
            <Text className={Style.text}>DPD</Text>
          </Flex>
          <Flex vertical align="center" gap={5}>
            <Title level={4} className={`${Style.amount} ${Style.error}`}>
              Rs.{formatAmount(overdueAmount)}
            </Title>
            <Link
              to="#"
              className={Style.text}
              style={{ textDecoration: "underline" }}
            >
              Overdue Amount
            </Link>
          </Flex>
          <Flex vertical align="center" gap={5}>
            <Title level={4} className={`${Style.amount} ${Style.success}`}>
              Rs.{formatAmount(promiseAmount)}
            </Title>
            <Text className={Style.text}> Promise Amount</Text>
          </Flex>
        </Flex>

        {/* dates */}
        <Flex vertical gap={2} className={Style.date_details}>
          <Details
            image={DATE_IMG}
            title={"Promise date"}
            value={handleDateFormatter(promiseDate)}
          />

          {/*  Change the icon & value */}
          <Details image={DATE_IMG} title={"Promise Status"} value={status} />

          {pendingScene && (
            <Details
              image={DATE_IMG}
              title={"Pending Scene"}
              value={pendingScene}
            />
          )}
          {feedbackDate && (
            <>
              <Details
                image={DATE_IMG}
                title={"Latest BM Feedback Date"}
                value={handleDateFormatter(feedbackDate)}
              />

              <Link to="#" className={Style.view_link}>
                <Text
                  onClick={() => {
                    setIsViewFeedbackModalOpen(true);
                  }}
                >
                  View
                </Text>
              </Link>
            </>
          )}
        </Flex>

        {/* modalButton */}
        {isModal ? (
          <Flex align="center" className={Style.modal_button_container}>
            <button
              className={Style.modal_button}
              onClick={() => {
                setIsModalOpen(true);
              }}
            >
              {modalButtonText}
            </button>
          </Flex>
        ) : (
          <Flex
            justify="space-between"
            align="center"
            gap={2}
            className={Style.number_status}
          >
            <Text style={{ fontWeight: 500 }}>Number Status:</Text>
            <Text className={Style.status}>{status ?? "Not reachable"}</Text>
          </Flex>
        )}

        <FeedbackModal
          pageId={SMART_COLLECT_MENU_IDS.READY_TO_PAY}
          branchMstId={branchMstId}
          loanmstid={loanmstid}
          overdueAmount={overdueAmount}
          modalStatus={isModalOpen}
          handleSubmit={handleSubmit}
          handleCancel={handleCancel}
        />

        <DialerModal
          customerName={name}
          overdueAmount={overdueAmount}
          loanmstid={loanmstid}
          isLoading={isLoading}
          modalStatus={isDialerModalOpen}
          handleSubmit={handleDialerSubmit}
          handleCancel={handleDialerCancel}
        />

        <ConversionHistory
          customerName={name}
          loanMstId={loanmstid}
          modalStatus={isHistoryModalOpen}
          handleCancel={handleHistoryCancel}
        />

        <ViewFeedbackModal
          loanMstId={loanmstid}
          modalStatus={isViewFeedbackModalOpen}
          handleCancel={handleViewFeedbackCancel}
        />
      </Flex>
    </>
  );
}

CustomerDetailsCard.propTypes = {
  loanmstid: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  branchMstId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  status: PropTypes.string,
  name: PropTypes.string,
  contact: PropTypes.string,
  branchName: PropTypes.string,
  disbursementId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  dpdAmount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  feedbackDate: PropTypes.string,
  loanType: PropTypes.string,
  overdueAmount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  promiseAmount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  promiseDate: PropTypes.string,
  pendingScene: PropTypes.string,
  modalButtonText: PropTypes.string,
  isModal: PropTypes.bool,
};

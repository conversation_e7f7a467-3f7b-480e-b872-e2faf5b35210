@import url("../../../../index.css");

$light-gray: #d9d9d9;
$tab-height: 7px;
$tab-width: 525px; // Increased the tab width
$table-radius: 22px;
$attempts-bg: #bdd1ff;
$disable: #787777;
$body: #e4f8f9;
$light-blue: #e4f8f9;
$blue: #becfff;

.download_button {
  background-color: var(--dark-blue);
  padding: 0.2rem 1rem;
  border-radius: 10px;
  cursor: pointer;
  margin-right: 1.3rem;
  border-color: transparent;
  img {
    width: 20px;
  }
}
.custom_table {
  padding-top: 1.5rem;
  overflow: auto;
  .text {
    font-weight: 500;
    white-space: nowrap;
    color: var(--dark-blue);
  }
  .blueText {
    font-weight: 700;
    color: var(--blue);
    cursor: pointer;
  }

  //Global ant design classes

  :global(.ant-table){
    background-color: transparent;
  }
  
  // Pagination
  :global(.ant-pagination) {
    justify-content: center;
    margin: 0 !important;

    :global(.ant-pagination-prev),
    :global(.ant-pagination-next) {
      color: var(--dark-blue);
      border: 0 solid var(--blue);
      background: #e4f8f9;
      height: 25px;
      min-width: 15px;
      border-radius: 0px;
      margin: 0;
      button {
        border-radius: 0px;
      }
    }
    :global(.ant-pagination-item) {
      margin-right: 0;
      height: 0;
      a {
        color: $disable;
        font-size: 0.9rem;
        line-height: 23px;
        font-weight: normal;
        font-family: 'Kanit','Courier New', Courier, monospace;
      }
      &:hover {
        background-color: transparent;
      }
    }
    :global(.ant-pagination-item-active) {
      border: none;
      a {
        color: var(--dark-blue);
        font-size: 1.2rem;
        padding: 0;
      }
    }
  }

  //Table container
  :global(.ant-table-container) {
    padding: 0 1rem;
    margin-bottom: 0.5rem;
    background: white;
    border-start-start-radius: $table-radius;
    border-start-end-radius: $table-radius;

    // Table header
    :global(.ant-table-header) {
      position: relative;
      margin: 0 auto;
      top: -21px;
      border-radius: $table-radius;
      :global(.ant-table-column-has-sorters) {
        background-color: var(--dark-blue);
        &:hover{
          background-color: var(--dark-blue);
        }
    
        :global(.ant-table-column-sorter-up),
        :global(.ant-table-column-sorter-down) {
          svg{
            fill: white;
          }
          &:global(.active){
            svg{
                fill: rgb(24, 155, 249)
                }
                }
            }
            }
    }

    // Table virtual body
    :global(.ant-table-tbody-virtual) {
      margin-top: -8px;
    }

    &::before {
      box-shadow: none !important;
    }
  }

  // Table rows header
  :global(.ant-table-thead > tr) {
    // First row
    &:nth-child(1) {
      // last 1 columns
      th {
        &:nth-last-child(1),
        &:nth-last-child(2) {
          border-inline-start: 1px solid white;
          border-bottom: 1px solid white !important;
        }
      }
    }

    // Second row first col
    &:nth-child(2) {
      th {
        border-inline-end: none !important;
      }
    }
    th {
      border-start-end-radius: 0 !important;
      background-color: var(--dark-blue);
      color: white;
      border-bottom: none;
      text-align: center;
      font-family: 'Kanit','Courier New', Courier, monospace;
      font-weight: normal;
      padding: 10px;
      &:not(:last-child):not(.ant-table-selection-column):not(
          .ant-table-row-expand-icon-cell
        ):not([colspan])::before {
        width: 0;
      }
    }
  }

  // Table body
  :global(.ant-table-tbody) {
    // Body rows
    :global(.ant-table-row) {
      &:nth-child(even){
        background-color: var(--light-green);
      }  
      // Cols
      :global(.ant-table-cell) {
        text-align: center;
        border-bottom: 2px solid white;
        border-inline-end: 0px;
        font-family: 'Kanit','Courier New', Courier, monospace;
        font-weight: normal;
        padding: 10px;

        :global(.ant-typography) {
          font-weight: 400;
          font-family: 'Kanit','Courier New', Courier, monospace;
          cursor: pointer;
        }
        
        &:global(.ant-table-cell-row-hover) {
          background-color: transparent;
        }

        // Fixed Cols
        &:global(.ant-table-cell-fix-right) {
          background-color: $body;
        }
      }
    }
  }
}

// Responsive view
@media only screen and (max-width:768px) {
  .custom_table{
      :global(.ant-table-thead >tr){
          th{
              padding: 0.5rem;
              font-size: 11px;
          }
      } 
      :global(.ant-table-tbody){
          :global(.ant-table-row){
              :global(.ant-table-cell){
                  padding: 0.5rem;
              }
          }
      }
  }
}
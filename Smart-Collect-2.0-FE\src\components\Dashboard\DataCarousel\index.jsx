import { Flex, Typography} from "antd";
import Slider from "react-slick";
import React from "react";
import CROSS_IMG from "../../../assets/Images/cross-white.svg";
import { handleDateFormatter } from "../../../constant";
import Style from "./_carousel.module.scss";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import PropTypes from "prop-types";

const { Title, Text } = Typography;

export function DataCarousel({
  handleDisplaySelectedData,
  handleGetDashboardData,
  selectedDate,
  setSelectedDate,
}) {
  const data = JSON.parse(localStorage.getItem("engagementData")) || [];

  if (!data.length) return null; // Don't render if no data

  const handleRemoveDates = async() => {
    setSelectedDate(null);
    localStorage.removeItem("filterDate");
    await handleGetDashboardData();
  };

  const settings = {
    dots: false,
    infinite: false,
    speed: 500,
    slidesToShow: 4,
    slidesToScroll: 4,
    initialSlide: data.length > 4 ? data.length - 4 : 0,
    adaptiveHeight: true,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 3,
        },
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 2,
        },
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
    ],
  };

  return (
    <div className={Style.slider_container}>
      <Slider {...settings}>
        {data?.length
          ? data?.map((d, index) => {
              const isSelected =
                selectedDate === d?.CreatedDate && d?.total_engaged !== 0;

              return (
                <Flex
                  vertical
                  key={`${index}-${d.CreatedDate}`}
                  className={Style.d_card}
                  gap={0}
                  onClick={() => {
                    setSelectedDate((prev) => {
                      return d?.CreatedDate;
                    });
                    handleDisplaySelectedData({
                      date: d?.CreatedDate,
                      value: d?.total_engaged,
                    });
                  }}
                >
                  <Flex
                    vertical
                    className={`${Style.card} ${
                      isSelected ? Style.selected : ""
                    }`}
                  >
                    {isSelected && (
                      <Flex justify="end">
                        <input
                         type="image"
                          src={CROSS_IMG}
                          alt="Cross icon"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRemoveDates();
                          }}
                          style={{ cursor: "pointer", width: "11px" }}
                        />
                      </Flex>
                    )}
                    <Title
                      level={3}
                      className={`${Style.value} ${
                        isSelected ? Style.selectedText : ""
                      }`}
                    >
                      {d?.total_engaged}
                    </Title>
                    <Text
                      style={{ fontWeight: 400 }}
                      className={`${Style.date} ${
                        isSelected ? Style.selectedText : ""
                      }`}
                    >
                      {handleDateFormatter(d?.CreatedDate)}
                    </Text>
                  </Flex>
                </Flex>
              );
            })
          : null}
      </Slider>
    </div>
  );
}

DataCarousel.propTypes={
  handleDisplaySelectedData: PropTypes.func,
  handleGetDashboardData: PropTypes.func,
  selectedDate: PropTypes.string,
  setSelectedDate: PropTypes.func,
}
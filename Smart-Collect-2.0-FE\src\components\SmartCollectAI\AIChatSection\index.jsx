import { Flex, Typography } from "antd";
import React, { useEffect, useMemo, useRef } from "react";
import Style from "../_style.module.scss";
import EDIT_ICON from "../../../assets/Images/chat-edit.svg";
import CHAT_ICON from "../../../assets/Images/chat-icon.svg";
import TypingIndicator from "./TypingIndicator";
import PropTypes from "prop-types";

const { Text } = Typography;

export function AIChatSection({
  data,
  handleEditLastMessage,
  isTyping = false,
}) {
  const userDetails = JSON.parse(localStorage.getItem("user"));
  const messagesEndRef = useRef(null);

  // Find the last user message for edit capability
  const lastUserMessageIndex = useMemo(() => {
    for (let i = data.length - 1; i >= 0; i--) {
      if (data[i].sender === "user") {
        return i;
      }
    }
    return null;
  }, [data]);

  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [data, isTyping]);

  return (
    <div className={Style.chat_container}>
      {data?.length ? (
        data?.map((msg, index) => {
          const isUser = msg.sender === "user";
          const isLastUserMessage = index === lastUserMessageIndex;

          return (
            <Flex
              key={`${index}-${msg.sender}`}
              gap={10}
              align={isUser ? "center" : "start"}
              className={`${Style.chat_message} ${Style[msg.sender]}`}
            >
              <div
                className={Style.icon}
                style={{
                  width: isUser ? "12px" : "18px",
                  cursor: isUser ? "pointer" : "default",
                }}
              >
                {(() => {
                  if (isUser && isLastUserMessage && msg.isEdit) {
                    return (
                      <input
                        type="image"
                        src={EDIT_ICON}
                        alt="edit"
                        onClick={handleEditLastMessage.bind(null, {
                          oldMessage: data[lastUserMessageIndex],
                          index,
                        })}
                      />
                    );
                  }

                  if (!isUser) {
                    return <img src={CHAT_ICON} alt="chat" />;
                  }

                  return null;
                })()}
              </div>

              <div
                className={Style.messages_container}
                style={{
                  background: isUser ? "#F3F3F4" : "transparent",
                  color: isUser ? "black" : "#407BFF",
                  padding: isUser ? "0.6rem 1rem" : "0",
                  whiteSpace: "pre-wrap",
                }}
              >
                {msg.text}
              </div>

              {!isUser && msg.csv_download_url && (
                <Text>
                  <a
                    style={{
                      color: "#113c9b",
                      textDecoration: "underline",
                      cursor: "pointer",
                    }}
                    href={msg.csv_download_url}
                    download
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Download the data.
                  </a>
                </Text>
              )}
            </Flex>
          );
        })
      ) : (
        <Flex justify="center" align="center" className={Style.user_container}>
          <Text>Hello {userDetails?.username}</Text>
        </Flex>
      )}

      {/* Typing Indicator */}
      {isTyping && (
        <Flex
          gap={10}
          align="start"
          className={`${Style.chat_message} ${Style.ai}`}
        >
          <div className={Style.icon} style={{ width: "20px" }}>
            <img src={CHAT_ICON} alt="chat" />
          </div>
          <div style={{ color: "#407BFF" }}>
            <TypingIndicator />
          </div>
        </Flex>
      )}

      {/* Invisible div to trigger scrolling */}
      <div ref={messagesEndRef} />
    </div>
  );
}

AIChatSection.propTypes = {
  data: PropTypes.array,
  handleEditLastMessage: PropTypes.func,
  isTyping: PropTypes.bool,
};

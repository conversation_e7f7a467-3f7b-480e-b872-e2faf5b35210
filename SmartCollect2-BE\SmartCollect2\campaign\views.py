# from django.shortcuts import render
import json
import logging
import traceback
from copy import deepcopy
from datetime import datetime
from typing import Dict

import WebApp.models as webappmodels
from django.db import transaction
from drf_spectacular.utils import OpenApiParameter, OpenApiTypes, extend_schema
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import ModelViewSet, ReadOnlyModelViewSet
from rest_framework_simplejwt.authentication import JWTAuthentication
from WebApp.models import AccountSummary

import campaign.filters as campfilters
import campaign.models as campmodels
import campaign.serializers as campserializers
from rest_framework.viewsets import ViewSet

# Create your views here.
class OnboardBankCopyCampaigns:
    DEFAULT_BANK_ID = 5

    def __init__(self, newbankid, config_file_path=None):
        self.newbankid = newbankid
        self.bank = campmodels.BankMst.objects.get(BankMstID=self.DEFAULT_BANK_ID)
        self.newbank = campmodels.BankMst.objects.get(BankMstID=self.newbankid)
        self.config_file_path = config_file_path or 'bank_copy_config.json'
        self.copy_config = self._load_config()

        # Mapping dictionaries
        self.languageoldtonew = {}
        self.watemplatemappingoldtonew = {}
        self.wamstoldtonew = {}
        self.ivrtemplatemappingoldtonew = {}
        self.ivrmstoldtonew = {}
        self.voicebottemplatemappingoldtonew = {}
        self.blastertemplatemappingoldtonew = {}
        self.campmstoldtonew = {}
        self.commflowmstoldtonew = {}
        self.commflowoldtonew = {}
        self.filtersoldtonew = {}

    def _load_config(self):
        """Load configuration from JSON file"""
        try:
            with open(self.config_file_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            logging.warning(f"Config file {self.config_file_path} not found. Using default configuration.")
            return self._get_default_config()
        except json.JSONDecodeError as e:
            logging.info(f"Error parsing config file: {e}")
            return self._get_default_config()

    def _get_default_config(self):
        """Return default configuration when no config file is found"""
        return {
            "languages": {"copy": True, "specific_ids": []},
            "whatsapp_templates": {"copy": True, "specific_ids": []},
            "whatsapp_flows": {"copy": True, "specific_ids": []},
            "ivr_templates": {"copy": True, "specific_ids": []},
            "ivr_flows": {"copy": True, "specific_ids": []},
            "voicebot_templates": {"copy": True, "specific_ids": []},
            "blaster_templates": {"copy": True, "specific_ids": []},
            "campaigns": {"copy": True, "specific_ids": []},
            "filters": {"copy": True, "specific_ids": []}
        }

    def _should_copy_item(self, config_key, item_id):
        """Determine if an item should be copied based on configuration"""
        config = self.copy_config.get(config_key, {"copy": True, "specific_ids": []})

        if not config.get("copy", True):
            return False

        specific_ids = config.get("specific_ids", [])
        if specific_ids:
            return item_id in specific_ids
        return True

    def _validate_bank_ownership(self, model_class, item_id, item_name, bank_field='BankMstID'):
        """
        Validate that an item belongs to the default bank
        Args:
            model_class: Django model class
            item_id: ID of the item to validate
            item_name: Human readable name for error messages
            bank_field: Name of the bank field in the model
        """
        try:
            item = model_class.objects.get(pk=item_id)
            bank_id = getattr(item, bank_field)

            # Handle both direct bank ID and bank object cases
            actual_bank_id = bank_id.BankMstID if hasattr(bank_id, 'BankMstID') else bank_id

            if actual_bank_id != self.DEFAULT_BANK_ID:
                raise ValueError(
                    f"Validation Error: {item_name} ID {item_id}, "
                    f"does not belong to default bank_id {self.DEFAULT_BANK_ID}. Operation stopped."
                )
            return True
        except model_class.DoesNotExist:
            raise ValueError(
                f"Validation Error: {item_name} ID {item_id} does not exist. Operation stopped."
            )

    def _validate_specific_ids(self):
        """Validate all specific IDs mentioned in config belong to default bank"""
        config_validations = [
            ("languages", campmodels.LanguageMst, "Language"),
            ("whatsapp_templates", campmodels.WhatsAppTemplateMapping, "WhatsApp Template"),
            ("whatsapp_flows", campmodels.WhatsAppFlowMst, "WhatsApp Flow"),
            ("ivr_templates", campmodels.IVRTemplateMapping, "IVR Template"),
            ("ivr_flows", campmodels.IVRFlowMst, "IVR Flow"),
            ("voicebot_templates", campmodels.VoiceBotTemplateMapping, "VoiceBot Template"),
            ("blaster_templates", campmodels.BlasterTemplateMapping, "Blaster Template"),
            ("campaigns", campmodels.CampaignMst, "Campaign"),
            ("filters", campmodels.Filters, "Filter")
        ]

        for config_key, model_class, item_name in config_validations:
            config = self.copy_config.get(config_key, {})
            specific_ids = config.get("specific_ids", [])

            for item_id in specific_ids:
                self._validate_bank_ownership(model_class, item_id, item_name)

    def copymain(self):
        # Validate specific IDs before starting any operations
        try:
            self._validate_specific_ids()
        except ValueError as e:
            logging.info(str(e))
            return Response(
                {"message": "Validation failed", "error": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )

        with transaction.atomic():
            try:
                if self.copy_config.get("languages", {}).get("copy", True):
                    self.duplicatelanguages()
                if self.copy_config.get("whatsapp_templates", {}).get("copy", True):
                    self.duplicatewhatsapptemplates()
                if self.copy_config.get("whatsapp_flows", {}).get("copy", True):
                    self.duplicatewhatsappflows()
                if self.copy_config.get("ivr_templates", {}).get("copy", True):
                    self.duplicateivrtemplates()
                if self.copy_config.get("ivr_flows", {}).get("copy", True):
                    self.duplicateivrflows()
                if self.copy_config.get("voicebot_templates", {}).get("copy", True):
                    self.duplicateaicalltemplates()
                if self.copy_config.get("blaster_templates", {}).get("copy", True):
                    self.duplicateblastertemplates()
                if self.copy_config.get("campaigns", {}).get("copy", True):
                    self.duplicatecampaignandcommflow()
                if self.copy_config.get("filters", {}).get("copy", True):
                    self.duplicatefilters()

            except Exception as e:
                logging.info(e)
                logging.info(traceback.format_exc())
                transaction.set_rollback(True)
                return Response(
                    {"message": "Error copying bank data", "error": str(e)},
                    status=status.HTTP_400_BAD_REQUEST,
                )

    def duplicatelanguages(self):
        languages = campmodels.LanguageMst.objects.filter(BankMstID=self.bank)
        for language in languages:
            if self._should_copy_item("languages", language.LngMstID):
                # Validate language belongs to default bank
                self._validate_bank_ownership(
                    campmodels.LanguageMst, language.LngMstID, "Language"
                )

                newobj = deepcopy(language)
                newobj.LngMstID = None
                newobj.CreatedDate = datetime.now().date()
                newobj.BankMstID = self.newbank
                newobj.save()
                self.languageoldtonew[language.LngMstID] = newobj
                print(f"Language copied: ID {language.LngMstID} -> {newobj.LngMstID}")

    def duplicatewhatsapptemplates(self):
        templates = campmodels.WhatsAppTemplateMapping.objects.filter(BankMstID=self.bank)
        for template in templates:
            if self._should_copy_item("whatsapp_templates", template.WhatsAppTemplateMappingID):
                # Validate template belongs to default bank
                self._validate_bank_ownership(
                    campmodels.WhatsAppTemplateMapping,
                    template.WhatsAppTemplateMappingID,
                    "WhatsApp Template"
                )

                newtempmapobj = deepcopy(template)
                newtempmapobj.WhatsAppTemplateMappingID = None
                newtempmapobj.BankMstID = self.newbank
                newtempmapobj.CreatedDate = datetime.now()
                newtempmapobj.LngMstID = self.languageoldtonew.get(template.LngMstID.LngMstID)
                newtempmapobj.BankMstID = self.newbank
                newtempmapobj.TemplateBody = template.TemplateBody

                # Only save if language mapping exists
                if newtempmapobj.LngMstID:
                    newtempmapobj.save()
                    self.watemplatemappingoldtonew[template.WhatsAppTemplateMappingID] = newtempmapobj
                    print(f"WhatsApp Template copied: ID {template.WhatsAppTemplateMappingID} -> {newtempmapobj.WhatsAppTemplateMappingID}")

                    # Copy user templates
                    usertemplates = campmodels.WhatsAppUserTemplate.objects.filter(
                        WhatsAppTemplateMappingID=template
                    )
                    for usertemplate in usertemplates:
                        newusertempobj = deepcopy(usertemplate)
                        newusertempobj.WhatsAppUserTemplateID = None
                        newusertempobj.BankMstID = self.newbank
                        newusertempobj.WhatsAppTemplateMappingID = newtempmapobj
                        newusertempobj.save()
                        print(f"WhatsApp User Template copied: ID {usertemplate.WhatsAppUserTemplateID} -> {newusertempobj.WhatsAppUserTemplateID}")

                    # Copy variables
                    variables = campmodels.WhatsappVariableMapping.objects.filter(
                        WhatsAppTemplateMappingID=template
                    )
                    for variable in variables:
                        newvarobj = deepcopy(variable)
                        newvarobj.WhatsappVariableMappingID = None
                        newvarobj.WhatsAppTemplateMappingID = newtempmapobj
                        newvarobj.save()
                        print(f"WhatsApp Variable copied: ID {variable.WhatsappVariableMappingID} -> {newvarobj.WhatsappVariableMappingID}")

    def duplicatewhatsappflows(self):
        flows = campmodels.WhatsAppFlowMst.objects.filter(BankMstID=self.bank)
        for flow in flows:
            if self._should_copy_item("whatsapp_flows", flow.WhatsAppFlowMstID):
                # Validate flow belongs to default bank
                self._validate_bank_ownership(
                    campmodels.WhatsAppFlowMst,
                    flow.WhatsAppFlowMstID,
                    "WhatsApp Flow"
                )

                newflowobj = deepcopy(flow)
                newflowobj.WhatsAppFlowMstID = None
                newflowobj.BankMstID = self.newbank
                newflowobj.save()
                self.wamstoldtonew[flow.WhatsAppFlowMstID] = newflowobj
                print(f"WhatsApp Flow copied: ID {flow.WhatsAppFlowMstID} -> {newflowobj.WhatsAppFlowMstID}")

                flowmappings = campmodels.WhatsAppFlowMapping.objects.filter(WhatsAppFlowMstID=flow)
                for flowmapping in flowmappings:
                    language_mapping = self.languageoldtonew.get(flowmapping.LngMstID.LngMstID)
                    template_mapping = self.watemplatemappingoldtonew.get(
                        flowmapping.WhatsAppTemplateMappingID.WhatsAppTemplateMappingID
                    )

                    # Only create flow mapping if dependencies exist
                    if language_mapping and template_mapping:
                        newflowmappingobj = deepcopy(flowmapping)
                        newflowmappingobj.WhatsAppFlowID = None
                        newflowmappingobj.CreatedDate = datetime.now()
                        newflowmappingobj.FlowID = newflowobj.WhatsAppFlowMstID
                        newflowmappingobj.LngMstID = language_mapping
                        newflowmappingobj.WhatsAppTemplateMappingID = template_mapping
                        newflowmappingobj.WhatsAppFlowMstID = newflowobj
                        newflowmappingobj.BankMstID = self.newbank

                        if flowmapping.NextTemplate and flowmapping.NextTemplate in self.watemplatemappingoldtonew:
                            newflowmappingobj.NextTemplate = self.watemplatemappingoldtonew[
                                flowmapping.NextTemplate
                            ].WhatsAppTemplateMappingID
                        else:
                            newflowmappingobj.NextTemplate = None

                        newflowmappingobj.save()
                        print(f"WhatsApp Flow Mapping copied: ID {flowmapping.WhatsAppFlowID} -> {newflowmappingobj.WhatsAppFlowID}")

    def duplicateivrtemplates(self):
        templates = campmodels.IVRTemplateMapping.objects.filter(BankMstID=self.bank)
        for template in templates:
            if self._should_copy_item("ivr_templates", template.IVRTemplateMappingID):
                # Validate template belongs to default bank
                self._validate_bank_ownership(
                    campmodels.IVRTemplateMapping,
                    template.IVRTemplateMappingID,
                    "IVR Template"
                )

                language_mapping = self.languageoldtonew.get(template.LngMstID.LngMstID)

                if language_mapping:
                    newtempmapobj = deepcopy(template)
                    newtempmapobj.IVRTemplateMappingID = None
                    newtempmapobj.CreatedDate = datetime.now()
                    newtempmapobj.LngMstID = language_mapping
                    newtempmapobj.BankMstID = self.newbank
                    newtempmapobj.save()
                    self.ivrtemplatemappingoldtonew[template.IVRTemplateMappingID] = newtempmapobj
                    print(f"IVR Template copied: ID {template.IVRTemplateMappingID} -> {newtempmapobj.IVRTemplateMappingID}")

                    # Copy user templates
                    usertemplates = campmodels.IVRUserTemplate.objects.filter(
                        IVRTemplateMappingID=template
                    )
                    for usertemplate in usertemplates:
                        newusertempobj = deepcopy(usertemplate)
                        newusertempobj.IVRUserTemplateID = None
                        newusertempobj.BankMstID = self.newbank
                        newusertempobj.IVRTemplateMappingID = newtempmapobj
                        newusertempobj.save()
                        print(f"IVR User Template copied: ID {usertemplate.IVRUserTemplateID} -> {newusertempobj.IVRUserTemplateID}")

                    # Copy variables
                    variables = campmodels.IVRVariableMapping.objects.filter(
                        IVRTemplateMappingID=template
                    )
                    for variable in variables:
                        newvarobj = deepcopy(variable)
                        newvarobj.IVRVariableMappingID = None
                        newvarobj.IVRTemplateMappingID = newtempmapobj
                        newvarobj.save()
                        print(f"IVR Variable copied: ID {variable.IVRVariableMappingID} -> {newvarobj.IVRVariableMappingID}")

    def duplicateivrflows(self):
        flows = campmodels.IVRFlowMst.objects.filter(BankMstID=self.bank)
        for flow in flows:
            if self._should_copy_item("ivr_flows", flow.IVRFlowMstID):
                # Validate flow belongs to default bank
                self._validate_bank_ownership(
                    campmodels.IVRFlowMst,
                    flow.IVRFlowMstID,
                    "IVR Flow"
                )

                newflowobj = deepcopy(flow)
                newflowobj.IVRFlowMstID = None
                newflowobj.BankMstID = self.newbank
                newflowobj.save()
                self.ivrmstoldtonew[flow.IVRFlowMstID] = newflowobj
                print(f"IVR Flow copied: ID {flow.IVRFlowMstID} -> {newflowobj.IVRFlowMstID}")

                flowmappings = campmodels.IVRFlowMapping.objects.filter(IVRFlowMstID=flow)
                for flowmapping in flowmappings:
                    language_mapping = self.languageoldtonew.get(flowmapping.LngMstID.LngMstID)
                    template_mapping = self.ivrtemplatemappingoldtonew.get(
                        flowmapping.IVRTemplateMappingID.IVRTemplateMappingID
                    )

                    if language_mapping and template_mapping:
                        newflowmappingobj = deepcopy(flowmapping)
                        newflowmappingobj.IVRFlowID = None
                        newflowmappingobj.CreatedDate = datetime.now()
                        newflowmappingobj.FlowID = newflowobj.IVRFlowMstID
                        newflowmappingobj.LngMstID = language_mapping
                        newflowmappingobj.IVRTemplateMappingID = template_mapping
                        newflowmappingobj.CurrentTemplate = template_mapping.IVRTemplateMappingID
                        newflowmappingobj.IVRFlowMstID = newflowobj
                        newflowmappingobj.BankMstID = self.newbank

                        if flowmapping.NextTemplate and flowmapping.NextTemplate in self.ivrtemplatemappingoldtonew:
                            newflowmappingobj.NextTemplate = self.ivrtemplatemappingoldtonew[
                                flowmapping.NextTemplate
                            ].IVRTemplateMappingID
                        else:
                            newflowmappingobj.NextTemplate = None

                        newflowmappingobj.save()
                        print(f"IVR Flow Mapping copied: ID {flowmapping.IVRFlowID} -> {newflowmappingobj.IVRFlowID}")

    def duplicateaicalltemplates(self):
        templates = campmodels.VoiceBotTemplateMapping.objects.filter(BankMstID=self.bank)
        for template in templates:
            if self._should_copy_item("voicebot_templates", template.VoiceBotTemplateMappingID):
                # Validate template belongs to default bank
                self._validate_bank_ownership(
                    campmodels.VoiceBotTemplateMapping,
                    template.VoiceBotTemplateMappingID,
                    "VoiceBot Template"
                )

                language_mapping = self.languageoldtonew.get(template.LngMstID.LngMstID)

                if language_mapping:
                    newtempmapobj = deepcopy(template)
                    newtempmapobj.VoiceBotTemplateMappingID = None
                    newtempmapobj.CreatedDate = datetime.now()
                    newtempmapobj.LngMstID = language_mapping
                    newtempmapobj.BankMstID = self.newbank
                    newtempmapobj.save()
                    self.voicebottemplatemappingoldtonew[template.VoiceBotTemplateMappingID] = newtempmapobj
                    print(f"VoiceBot Template copied: ID {template.VoiceBotTemplateMappingID} -> {newtempmapobj.VoiceBotTemplateMappingID}")

                    # Copy user templates
                    usertemplates = campmodels.VoiceBotUserTemplate.objects.filter(
                        VoiceBotTemplateMappingID=template
                    )
                    for usertemplate in usertemplates:
                        newusertempobj = deepcopy(usertemplate)
                        newusertempobj.VoiceBotUserTemplateID = None
                        newusertempobj.BankMstID = self.newbank
                        newusertempobj.VoiceBotTemplateMappingID = newtempmapobj
                        newusertempobj.save()
                        print(f"VoiceBot User Template copied: ID {usertemplate.VoiceBotUserTemplateID} -> {newusertempobj.VoiceBotUserTemplateID}")

    def duplicateblastertemplates(self):
        templates = campmodels.BlasterTemplateMapping.objects.filter(BankMstID=self.bank)
        for template in templates:
            if self._should_copy_item("blaster_templates", template.BlasterTemplateMappingID):
                # Validate template belongs to default bank
                self._validate_bank_ownership(
                    campmodels.BlasterTemplateMapping,
                    template.BlasterTemplateMappingID,
                    "Blaster Template"
                )

                language_mapping = self.languageoldtonew.get(template.LngMstID.LngMstID)

                if language_mapping:
                    newtempmapobj = deepcopy(template)
                    newtempmapobj.BlasterTemplateMappingID = None
                    newtempmapobj.CreatedDate = datetime.now().date()
                    newtempmapobj.LngMstID = language_mapping
                    newtempmapobj.BankMstID = self.newbank
                    newtempmapobj.save()
                    self.blastertemplatemappingoldtonew[template.BlasterTemplateMappingID] = newtempmapobj
                    print(f"Blaster Template copied: ID {template.BlasterTemplateMappingID} -> {newtempmapobj.BlasterTemplateMappingID}")

                    # Copy user templates
                    usertemplates = campmodels.BlasterUserTemplate.objects.filter(
                        BlasterTemplateMappingID=template
                    )
                    for usertemplate in usertemplates:
                        newusertempobj = deepcopy(usertemplate)
                        newusertempobj.BlasterUserTemplateID = None
                        newusertempobj.BankMstID = self.newbank
                        newusertempobj.BlasterTemplateMappingID = newtempmapobj
                        newusertempobj.save()
                        print(f"Blaster User Template copied: ID {usertemplate.BlasterUserTemplateID} -> {newusertempobj.BlasterUserTemplateID}")

    def _duplicatecampaign(self, campaign: campmodels.CampaignMst):
        if self._should_copy_item("campaigns", campaign.CampaignMstID):
            # Validate campaign belongs to default bank
            self._validate_bank_ownership(
                campmodels.CampaignMst,
                campaign.CampaignMstID,
                "Campaign"
            )

            newcampaignobj = deepcopy(campaign)
            newcampaignobj.CampaignMstID = None
            newcampaignobj.BankMstID = self.newbank
            newcampaignobj.CreatedDate = datetime.now().date()
            newcampaignobj.save()
            self.campmstoldtonew[campaign.CampaignMstID] = newcampaignobj
            print(f"Campaign copied: ID {campaign.CampaignMstID} -> {newcampaignobj.CampaignMstID}")
            return newcampaignobj
        return None

    def _duplicatecommflowmst(self, commflowmst: campmodels.CommFlowMst):
        newcommflowmstobj = deepcopy(commflowmst)
        newcommflowmstobj.CommFlowMstID = None
        newcommflowmstobj.BankName = self.newbank.BankName
        newcommflowmstobj.CreatedDate = datetime.now()
        newcommflowmstobj.BankMstID = self.newbank
        newcommflowmstobj.save()
        self.commflowmstoldtonew[commflowmst.CommFlowMstID] = newcommflowmstobj
        print(f"CommFlowMst copied: ID {commflowmst.CommFlowMstID} -> {newcommflowmstobj.CommFlowMstID}")
        return newcommflowmstobj

    def _duplicatecommflow(
        self,
        commflow: campmodels.CommFlow,
        commflowmst: campmodels.CommFlowMst,
        campaign: campmodels.CampaignMst,
    ):
        newcommflowobj = deepcopy(commflow)
        newcommflowobj.CommFlowID = None
        newcommflowobj.CampaignMstID = campaign
        newcommflowobj.CommFlowMstID = commflowmst
        newcommflowobj.save()
        self.commflowoldtonew[commflow.CommFlowID] = newcommflowobj
        print(f"CommFlow copied: ID {commflow.CommFlowID} -> {newcommflowobj.CommFlowID}")
        return newcommflowobj

    def _updateflowids(
        self, newcommflowobj: campmodels.CommFlow, commflow: campmodels.CommFlow
    ):
        if newcommflowobj.CommunicationType == "whatsapp":
            if commflow.FlowID and commflow.FlowID in self.wamstoldtonew:
                newcommflowobj.FlowID = self.wamstoldtonew[commflow.FlowID].WhatsAppFlowMstID
            if commflow.TemplateID and commflow.TemplateID in self.watemplatemappingoldtonew:
                newcommflowobj.TemplateID = self.watemplatemappingoldtonew[commflow.TemplateID].WhatsAppTemplateMappingID
        elif newcommflowobj.CommunicationType == "ivr":
            if commflow.FlowID and commflow.FlowID in self.ivrmstoldtonew:
                newcommflowobj.FlowID = self.ivrmstoldtonew[commflow.FlowID].IVRFlowMstID
            if commflow.TemplateID and commflow.TemplateID in self.ivrtemplatemappingoldtonew:
                newcommflowobj.TemplateID = self.ivrtemplatemappingoldtonew[commflow.TemplateID].IVRTemplateMappingID
        elif newcommflowobj.CommunicationType == "ai":
            if commflow.FlowID and commflow.FlowID in self.voicebottemplatemappingoldtonew:
                newcommflowobj.FlowID = self.voicebottemplatemappingoldtonew[commflow.FlowID].VoiceBotTemplateMappingID
            if commflow.TemplateID and commflow.TemplateID in self.voicebottemplatemappingoldtonew:
                newcommflowobj.TemplateID = self.voicebottemplatemappingoldtonew[commflow.TemplateID].VoiceBotTemplateMappingID
        elif newcommflowobj.CommunicationType == "blaster":
            if commflow.FlowID and commflow.FlowID in self.blastertemplatemappingoldtonew:
                newcommflowobj.FlowID = self.blastertemplatemappingoldtonew[commflow.FlowID].BlasterTemplateMappingID
            if commflow.TemplateID and commflow.TemplateID in self.blastertemplatemappingoldtonew:
                newcommflowobj.TemplateID = self.blastertemplatemappingoldtonew[commflow.TemplateID].BlasterTemplateMappingID

    def duplicatecampaignandcommflow(self):
        campaigns = campmodels.CampaignMst.objects.filter(BankMstID=self.bank)
        for campaign in campaigns:
            newcampaignobj = self._duplicatecampaign(campaign)

            if newcampaignobj:  # Only proceed if campaign was copied
                commflows = campmodels.CommFlow.objects.filter(CampaignMstID=campaign)
                if commflows.exists():
                    newcommflowmstobj = self._duplicatecommflowmst(commflows.first().CommFlowMstID)

                    for commflow in commflows:
                        newcommflowobj = self._duplicatecommflow(
                            commflow, newcommflowmstobj, newcampaignobj
                        )
                        self._updateflowids(newcommflowobj, commflow)
                        newcommflowobj.save()

    def duplicatefilters(self):
        filters = campmodels.Filters.objects.filter(BankMstID=self.bank)
        for filter_obj in filters:
            if self._should_copy_item("filters", filter_obj.FiltersID):
                # Validate filter belongs to default bank
                self._validate_bank_ownership(
                    campmodels.Filters,
                    filter_obj.FiltersID,
                    "Filter"
                )

                newfilterobj = deepcopy(filter_obj)
                newfilterobj.FiltersID = None
                newfilterobj.BankMstID = self.newbank
                newfilterobj.save()
                self.filtersoldtonew[filter_obj.FiltersID] = newfilterobj
                print(f"Filter copied: ID {filter_obj.FiltersID} -> {newfilterobj.FiltersID}")

        filtermst = campmodels.FilterMst.objects.filter(BankMstID=self.bank)
        for filter_obj in filtermst:
            # Check if all dependencies are available
            filter_available = filter_obj.FiltersID.FiltersID in self.filtersoldtonew
            commflow_available = not filter_obj.CommFlowID or filter_obj.CommFlowID.CommFlowID in self.commflowoldtonew
            campaign_available = not filter_obj.CampaignMstID or filter_obj.CampaignMstID.CampaignMstID in self.campmstoldtonew
            commflowmst_available = not filter_obj.CommFlowMstID or filter_obj.CommFlowMstID.CommFlowMstID in self.commflowmstoldtonew

            if filter_available and commflow_available and campaign_available and commflowmst_available:
                newfilterobj = deepcopy(filter_obj)
                newfilterobj.ID = None
                newfilterobj.BankMstID = self.newbank
                newfilterobj.FiltersID = self.filtersoldtonew[filter_obj.FiltersID.FiltersID]

                if filter_obj.CommFlowID:
                    newfilterobj.CommFlowID = self.commflowoldtonew[filter_obj.CommFlowID.CommFlowID]
                if filter_obj.CampaignMstID:
                    newfilterobj.CampaignMstID = self.campmstoldtonew[filter_obj.CampaignMstID.CampaignMstID]
                if filter_obj.CommFlowMstID:
                    newfilterobj.CommFlowMstID = self.commflowmstoldtonew[filter_obj.CommFlowMstID.CommFlowMstID]

                newfilterobj.save()
                print(f"FilterMst copied: ID {filter_obj.ID} -> {newfilterobj.ID}")


@extend_schema(
    tags=["Campaign"],
    description="Create a new campaign",
    request={
        "Name": "string",
        "CampaignType": "string",
        "Priority": 0,
    },
    responses={
        201: campserializers.CampaignMstSerializer,
        400: {
            "description": "Bad Request",
            "examples": {
                "application/json": {"status": "error", "message": "Name not provided"}
            },
        },
    },
)
class CreateCampaign(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]

    def post(self, request):
        data = dict(request.data)
        user = request.user
        bank = user.BankMstID

        if not data.get("Name"):
            return Response(
                {"status": "error", "message": "Name not provided"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not data.get("CampaignType"):
            return Response(
                {"status": "error", "message": "CampaignType not provided"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        data["BankMstID"] = bank.BankMstID
        if data["BankMstID"] is None:
            return Response(
                {"status": "error", "message": "BankMstID not provided"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if data.get("Language") is None:
            return Response(
                {"status": "error", "message": "Language not provided"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        # if data.get("CampaignType") == "normal":

        data["IsActive"] = False
        data["CreatedDate"] = datetime.now().date()
        data["Priority"] = "0"
        data["Status"] = "Saved"

        serializer = campserializers.CampaignMstSerializer(data=data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {
                    "message": "Campaign created successfully",
                    "campaign_id": serializer.data,
                    "status": "success",
                },
                status=status.HTTP_201_CREATED,
            )
        return Response(
            {"status": "error", "errors": serializer.errors},
            status=status.HTTP_400_BAD_REQUEST,
        )


@extend_schema(
    tags=["Filters"],
    description="Create a new filter",
    request={
        "Filters": [
            {
                "Column": "string",
                "Comparison": "string",
                "Value": "string",
                "range_from": "string",
                "range_to": "string",
            }
        ],
        "CampaignMstID": 0,
        "Name": "string",
    },
    responses={
        201: {
            "message": "Filters created successfully",
            "filterdata": [
                {
                    "FiltersID": 0,
                    "BankMstID": 0,
                    "Column": "string",
                    "Comparison": "string",
                    "Value": "string",
                }
            ],
            "filterMstdata": [
                {
                    "ID": 0,
                    "Name": "string",
                    "CommFlowID": 0,
                    "IsActive": False,
                    "BankMstID": 0,
                    "FiltersID": 0,
                    "CampaignMstID": 0,
                    "CommFlowMstID": 0,
                }
            ],
            "status": "success",
        },
        400: {
            "description": "Bad Request",
            "examples": {
                "application/json": {
                    "status": "error",
                    "message": "Filters not provided",
                }
            },
        },
    },
)
class CreateFilters(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]

    def post(self, request):
        filters = request.data
        user = request.user
        bank = user.BankMstID

        if "Filters" not in filters:
            logging.info("Filters not provided in request data")
            return Response(
                {"status": "error", "message": "Filters not provided"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if "CampaignMstID" not in filters:
            logging.info("CampaignMstID not provided in request data")
            return Response(
                {"status": "error", "message": "CampaignMstID not provided"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        newfilters = []
        existingfilters = []
        for filter_data in filters["Filters"]:
            temp = filter_data.copy()
            if temp.get("FiltersID") is not None and temp.get("FiltersID") != "":
                existingfilters.append(filter_data["FiltersID"])
                continue
            temp["BankMstID"] = bank.BankMstID
            temp["ValueType"] = filter_data["ValueType"]
            if "Comparison" not in temp or "Value" not in temp or "Column" not in temp:
                logging.info("Comparison/Value/Column Name not provided in filter data")
                return Response(
                    {
                        "status": "error",
                        "message": "Comparison/Value/Column Name not provided",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
            if "range" in filter_data["Comparison"]:
                temp["Comparison"] = ">="
                temp["Value"] = filter_data["range_from"]
                newfilters.append(temp.copy())  # We are adding a copy here because we will modify temp later
                temp["Comparison"] = "<="
                temp["Value"] = filter_data["range_to"]
                newfilters.append(temp.copy())
                print("Range filter added:", newfilters)
            elif "in" in filter_data["Comparison"]:
                temp["Comparison"] = "in"
                try:
                    vallist = filter_data["Value"]
                    if not isinstance(vallist, list):
                        logging.info("Value must be a list for in comparison")
                        return Response(
                            {
                                "status": "error",
                                "message": "Value must be a list for in comparison",
                            },
                            status=status.HTTP_400_BAD_REQUEST,
                        )
                    quotedstring = ", ".join([f"'{val}'" for val in vallist])
                    temp["Value"] = f"{quotedstring}"
                    newfilters.append(temp)
                except json.JSONDecodeError:
                    logging.info(f"Invalid JSON format for Value: {filter_data['Value']}")
                    return Response(
                        {
                            "status": "error",
                            "message": f"Invalid JSON format for Value: {filter_data['Value']}",
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )
            else:
                newfilters.append(temp)

        with transaction.atomic() as atomic:
            try:
                serializer = campserializers.FiltersSerializer(
                    data=newfilters, many=True
                )
                if serializer.is_valid():
                    serializer.save()
                    # extract IDs of newly created filters
                    filter_ids = [
                        filter_data["FiltersID"]
                        for filter_data in serializer.data
                        if filter_data["FiltersID"] not in existingfilters
                    ]

                    filtermst = []
                    name = request.data.get("Name")
                    if not name:
                        atomic.rollback(True)
                        logging.info("Name not provided for filter creation")
                        logging.info(serializer.errors)
                        return Response(
                            {"status": "error", "message": "Name not provided"},
                            status=status.HTTP_400_BAD_REQUEST,
                        )
                    for id in filter_ids:
                        temp = {
                            "Name": name,
                            "FiltersID": id,
                            "BankMstID": bank.BankMstID,
                            "CampaignMstID": request.data["CampaignMstID"],
                        }
                        filtermst.append(temp)
                    serializer2 = campserializers.FilterMstSerializer(
                        data=filtermst, many=True
                    )
                    if serializer2.is_valid():
                        serializer2.save()
                        return Response(
                            {
                                "message": "Filters created successfully",
                                "filterdata": serializer.data,
                                "filterMstdata": serializer2.data,
                                "status": "success",
                            },
                            status=status.HTTP_201_CREATED,
                        )
                    logging.info(serializer2.errors)
                    return Response(
                        {"status": "error", "errors": serializer2.errors},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                logging.info(serializer.errors)
                return Response(
                    {"status": "error", "errors": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            except Exception as e:
                logging.info(e)
                atomic.set_rollback(True)
                return Response(
                    {"message": "Error creating filters", "error": str(e)},
                    status=status.HTTP_400_BAD_REQUEST,
                )


@extend_schema(
    tags=["Campaign"],
    description="Create a new communication flow",
    request={
        "FlowName": "string",
        "CommFlow": [
            {
                "CommFlowID": 0,
                "CommunicationType": "string",
                "TemplateID": 0,
                "FlowID": 0,
                "Days": "Comma Separated integer values",
                "BeforeAfter": "string",
                "BasedOn": "string",
                "CampaignMstID": 0,
            }
        ],
    },
    responses={
        201: {
            "message": "Communication Flow created successfully",
            "mstdata": {
                "CommFlowMstID": 0,
                "ColumnName": "string",
                "BasedOnTable": "string",
                "BankName": 0,
                "CreatedDate": "date",
                "BankMstID": 0,
                "IsActive": False,
                "Type": "string",
                "BasedOnColumn": "string",
                "FlowName": "string",
            },
            "flowdata": [
                {
                    "CommFlowID": 0,
                    "BankMstID": 0,
                    "CommFlowMstID": 0,
                    "CommunicationType": "string",
                    "TemplateID": 0,
                    "FlowID": 0,
                    "Days": "Comma Separated integer values",
                    "BeforeAfter": "string",
                    "BasedOn": "string",
                    "CampaignMstID": 0,
                    "ExtraColumn1": "string",
                    "ExtraColumn2": "string",
                    "ExtraColumn3": "string",
                    "ExtraColumn4": "string",
                    "ExtraColumn5": "string",
                    "BasedOnTable": "string",
                }
            ],
        },
        400: {
            "description": "Bad Request",
            "examples": {
                "application/json": {
                    "status": "error",
                    "message": "FlowName not provided",
                }
            },
        },
    },
)
class CreateCommunicationFlow(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]

    def post(self, request):
        data = dict(request.data)
        print('Creating Comm Flow', data)
        user = request.user
        bank = user.BankMstID

        mstdata = {
            "BankName": bank.BankName,
            "BankMstID": bank.BankMstID,
            "FlowName": data["FlowName"],
            "Type": "Normal",
            "IsActive": False,
        }
        mstid = None
        commflowmstid = data.get("CommFlowMstID")
        if commflowmstid is not None and commflowmstid != "":
            mstid = int(commflowmstid)
            mstobj = campmodels.CommFlowMst.objects.get(CommFlowMstID=mstid)
            mstdata = campserializers.CommunicationMstSerializer(
                instance=mstobj, data=mstdata
            )
            if mstdata.is_valid():
                mstdata.save()
                mstdata = mstdata.data
            else:
                return Response(
                    {"status": "error", "errors": mstdata.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            serializer = campserializers.CommunicationMstSerializer(data=mstdata)
            if serializer.is_valid():
                serializer.save()
                mstdata = serializer.data
                mstid = mstdata["CommFlowMstID"]
            else:
                return Response(
                    {"status": "error", "errors": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        if mstid is not None:
            commflow = []
            existingflows = []
            for flow in data["CommFlow"]:
                temp = flow.copy()
                if temp.get("CommFlowID") is not None and temp.get("CommFlowID") != "":
                    existingflows.append(flow["CommFlowID"])
                    continue
                temp["CommFlowMstID"] = mstid
                temp["BankMstID"] = bank.BankMstID
                commflow.append(temp)
            serializer2 = campserializers.CommunicationDetailsSerializer(
                data=commflow, many=True
            )

            if serializer2.is_valid():
                serializer2.save()
                return Response(
                    {
                        "message": "Communication Flow created successfully",
                        "mstdata": mstdata,
                        "flowdata": serializer2.data,
                        "status": "success",
                    },
                    status=status.HTTP_201_CREATED,
                )
            return Response(
                {"status": "error", "errors": serializer2.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )
        return Response(
            {"status": "error", "errors": serializer.errors},
            status=status.HTTP_400_BAD_REQUEST,
        )


@extend_schema(
    tags=["WhatsApp"],
    description="Register a new WhatsApp template",
    request=campserializers.WhatsAppTemplateMappingSerializer,
    responses={
        201: campserializers.WhatsAppTemplateMappingSerializer,
        400: {
            "description": "Bad Request",
            "examples": {
                "application/json": {"status": "error", "message": "Language not found"}
            },
        },
    },
)
class RegisterWhatsappTemplate(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]

    def post(self, request):
        data = request.data
        languageobj = campmodels.LanguageMst.objects.get(
            Language=data["Language"], BankMstID=request.user.BankMstID
        )
        if not languageobj:
            return Response(
                {"message": "Language not found", "status": "error"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        with transaction.atomic():
            try:
                whmst = campmodels.WhatsAppFlowMst.objects.create(
                    FlowName=data["Name"],
                    BankMstID=request.user.BankMstID,
                    IsActive=False,
                )

                whtempmap = campmodels.WhatsAppTemplateMapping.objects.create(
                    Status="Pending",
                    TemplateName=data["Name"],
                    IsActive=False,
                    BankMstID=request.user.BankMstID,
                    LngMstID=languageobj,
                    IsAdminCreated=False,
                    TemplateBody=data["TemplateText"],
                )

                WhatsAppUserTemplateObj = campmodels.WhatsAppUserTemplate(
                    TemplateName=data["Name"],
                    # TemplateBody=data['TemplateText'],
                    BankMstID=request.user.BankMstID,
                    # IsActive=False,
                    # EmployeeMstID=request.user,
                    WhatsAppTemplateMappingID=whtempmap,
                )

                buttontempatedata = []
                for button, buttondata in data.get("ButtonMapping", {}).items():
                    setattr(
                        WhatsAppUserTemplateObj,
                        f"Button{button}",
                        buttondata.get("Name"),
                    )
                    setattr(
                        WhatsAppUserTemplateObj,
                        f"Response{button}",
                        buttondata.get("Response"),
                    )
                    buttontempatedata.append(
                        {
                            "Status": "Pending",
                            "TemplateName": f"{buttondata['Name']}_button_{button}",
                            "IsActive": False,
                            "BankMstID": request.user.BankMstID,
                            "LngMstID": languageobj,
                            "IsAdminCreated": False,
                            "TemplateBody": buttondata["Response"],
                            "Extra_Column1": buttondata.get("Name"),
                        }
                    )

                for count, sample in data.get("SampleMapping", {}).items():
                    setattr(WhatsAppUserTemplateObj, f"Variable{count}", sample)

                buttonobjs = campmodels.WhatsAppTemplateMapping.objects.bulk_create(
                    [
                        campmodels.WhatsAppTemplateMapping(**buttondata)
                        for buttondata in buttontempatedata
                    ]
                )

                WhatsAppUserTemplateObj.save()

                flowmappingdata = [
                    {
                        "FlowID": whmst.WhatsAppFlowMstID,
                        "FlowName": data["Name"],
                        "IsActive": False,
                        "BankMstID": request.user.BankMstID,
                        "WhatsAppFlowMstID": whmst,
                        "LngMstID": languageobj,
                        "WhatsAppTemplateMappingID": whtempmap,
                        "IsStart": True,
                    }
                ]

                for count, buttons in enumerate(buttonobjs):
                    # Mapping the button text to the response in the flow mapping
                    if count == 0:
                        flowmappingdata[0]["Response"] = buttons.Extra_Column1  # Extra Column 1 has the button text
                        flowmappingdata[0]["NextTemplate"] = (
                            buttons.WhatsAppTemplateMappingID
                        )
                    else:
                        temp = flowmappingdata[0].copy()
                        temp["Response"] = buttons.Extra_Column1  # Extra Column 1 has the button text
                        temp["NextTemplate"] = buttons.WhatsAppTemplateMappingID
                        temp["IsStart"] = False
                        flowmappingdata.append(temp)

                flowmappingobjs = campmodels.WhatsAppFlowMapping.objects.bulk_create(
                    [
                        campmodels.WhatsAppFlowMapping(**flowdata)
                        for flowdata in flowmappingdata
                    ]
                )

                variablesdata = []
                for variable, field in data.get("VariableMapping", {}).items():
                    variablesdata.append(
                        {
                            "VariableNo": variable,
                            "VariableField": field,
                            "WhatsAppTemplateMappingID": whtempmap,
                        }
                    )

                variableobjs = campmodels.WhatsappVariableMapping.objects.bulk_create(
                    [
                        campmodels.WhatsappVariableMapping(**variabledata)
                        for variabledata in variablesdata
                    ]
                )
                return Response(
                    {
                        "message": "WhatsApp template registered successfully",
                        "whmst": whmst.WhatsAppFlowMstID,
                        "status": "success",
                    },
                    status=status.HTTP_201_CREATED,
                )
            except Exception as e:
                logging.info(e)
                transaction.set_rollback(True)
                return Response(
                    {"message": "Error registering WhatsApp template", "error": str(e)},
                    status=status.HTTP_400_BAD_REQUEST,
                )


@extend_schema(
    tags=["Blaster"],
    description="Register a new Blaster template",
    request=campserializers.BlasterTemplateMappingSerializer,
    responses={
        201: campserializers.BlasterTemplateMappingSerializer,
        400: {
            "description": "Bad Request",
            "examples": {
                "application/json": {"status": "error", "message": "Language not found"}
            },
        },
    },
)
class RegisterBlasterTemplate(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]

    def post(self, request):
        data = request.data
        languageobj = campmodels.LanguageMst.objects.get(
            Language=data["Language"], BankMstID=request.user.BankMstID
        )
        with transaction.atomic():
            try:
                blaster = campmodels.BlasterTemplateMapping.objects.create(
                    TemplateName=data["Name"],
                    BankMstID=request.user.BankMstID,
                    IsActive=False,
                    LngMstID=languageobj,
                    IsAdminCreated=False,
                    Status="Pending",
                    TemplateBody=data["TemplateText"],
                )

                variables = []
                for variable, field in data.get("VariableMapping", {}).items():
                    variables.append(
                        {
                            "TemplateName": data["Name"],
                            "BankMstID": request.user.BankMstID,
                            "VariableNumber": variable,
                            "VariableField": field,
                            "BlasterTemplateMappingID": blaster,
                        }
                    )
                campmodels.BlasterUserTemplate.objects.bulk_create(
                    [
                        campmodels.BlasterUserTemplate(**variabledata)
                        for variabledata in variables
                    ]
                )
                return Response(
                    {
                        "message": "Blaster template registered successfully",
                        "blaster": blaster.BlasterTemplateMappingID,
                        "status": "success",
                    },
                    status=status.HTTP_201_CREATED,
                )
            except Exception as e:
                logging.info(e)
                transaction.set_rollback(True)
                return Response(
                    {
                        "message": "Error registering Blaster template",
                        "error": str(e),
                        "status": "error",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )


@extend_schema(
    tags=["VoiceBot"],
    description="Register a new VoiceBot template",
    request=campserializers.VoiceBotTemplateMappingSerializer,
    responses={
        201: campserializers.VoiceBotTemplateMappingSerializer,
        400: {
            "description": "Bad Request",
            "examples": {
                "application/json": {"status": "error", "message": "Language not found"}
            },
        },
    },
)
class RegisterVoiceBotTemplate(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]

    def post(self, request):
        data = request.data
        languageobj = campmodels.LanguageMst.objects.get(
            Language=data["Language"], BankMstID=request.user.BankMstID
        )
        bankobj = request.user.BankMstID
        with transaction.atomic():
            try:
                voice = campmodels.VoiceBotTemplateMapping(
                    TemplateName=data["Name"],
                    BankMstID=bankobj,
                    IsActive=False,
                    LngMstID=languageobj,
                    IsAdminCreated=False,
                    Status="Pending",
                    TemplateBody=data["TemplateText"],
                )

                startdata = request.data.get("start", {})
                enddata = request.data.get("end", {})

                voice.StartMsg = startdata.get("message")
                voice.EndMsg = enddata.get("message")
                voice.save()

                variablesdata = []
                for variable, field in startdata.get("VariableMapping", {}).items():
                    variablesdata.append(
                        {
                            "TemplateName": f"{data['Name']}_start",
                            "BankMstID": bankobj,
                            "VariableNumber": variable,
                            "VariableField": field,
                            "VoiceBotTemplateMappingID": voice,
                            "StartEnd": "Start",
                        }
                    )

                for variable, field in enddata.get("VariableMapping", {}).items():
                    variablesdata.append(
                        {
                            "TemplateName": f"{data['Name']}_end",
                            "BankMstID": bankobj,
                            "VariableNumber": variable,
                            "VariableField": field,
                            "VoiceBotTemplateMappingID": voice,
                            "StartEnd": "End",
                        }
                    )

                variableobjs = campmodels.VoiceBotUserTemplate.objects.bulk_create(
                    [
                        campmodels.VoiceBotUserTemplate(**variabledata)
                        for variabledata in variablesdata
                    ]
                )

                return Response(
                    {
                        "message": "VoiceBot template registered successfully",
                        "voice": voice.VoiceBotTemplateMappingID,
                        "status": "success",
                    },
                    status=status.HTTP_201_CREATED,
                )
            except Exception as e:
                logging.info(e)
                transaction.set_rollback(True)
                return Response(
                    {
                        "message": "Error registering VoiceBot template",
                        "error": str(e),
                        "status": "error",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )


@extend_schema(
    tags=["IVR"],
    description="Register a new IVR template",
    request=campserializers.IVRTemplateMappingSerializer,
    responses={
        201: campserializers.IVRTemplateMappingSerializer,
        400: {
            "description": "Bad Request",
            "examples": {
                "application/json": {"status": "error", "message": "Language not found"}
            },
        },
    },
)
class RegisterIVRTemplate(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]

    def post(self, request):
        data = request.data

        with transaction.atomic():
            try:
                ivr = campmodels.IVRFlowMst.objects.create(
                    FlowName=data["Name"],
                    BankMstID=request.user.BankMstID,
                    IsActive=False,
                )

                ivrtempmap = campmodels.IVRTemplateMapping.objects.create(
                    Status="Pending",
                    TemplateName=data["Name"],
                    IsActive=False,
                    LngMstID=campmodels.LanguageMst.objects.get(
                        Language=data["Language"], BankMstID=request.user.BankMstID
                    ),
                    IsAdminCreated=False,
                    BankMstID=request.user.BankMstID,
                    # TemplateBody=data['TemplateText']
                    TemplateBody=json.dumps(data["TemplateText"]),
                )

                ivrusertemp = campmodels.IVRUserTemplate.objects.create(
                    TemplateName=data["Name"],
                    BankMstID=request.user.BankMstID,
                    IVRTemplateMappingID=ivrtempmap,
                )

                ivrflow = campmodels.IVRFlowMapping.objects.create(
                    FlowID=ivr.IVRFlowMstID,
                    FlowName=data["Name"],
                    Response=data["TemplateText"],
                    IsActive=False,
                    LngMstID=campmodels.LanguageMst.objects.get(
                        Language=data["Language"], BankMstID=request.user.BankMstID
                    ),
                    IVRTemplateMappingID=ivrtempmap,
                    IVRFlowMstID=ivr,
                    IsStart=True,
                    ButtonPressed=None,
                    NextTemplate=None,
                    CurrentTemplate=ivrtempmap.IVRTemplateMappingID,
                    BankMstID=request.user.BankMstID,
                )

                for variable, field in data.get("VariableMapping", {}).items():
                    ivrusertemp = campmodels.IVRVariableMapping.objects.create(
                        VariableNo=variable,
                        VariableField=field,
                        IVRTemplateMappingID=ivrtempmap,
                    )

                for button, response in data.get("response", {}).items():
                    newivrtempmap = campmodels.IVRTemplateMapping.objects.create(
                        Status="Pending",
                        TemplateName=f"{data['Name']}_{button}",
                        IsActive=False,
                        LngMstID=campmodels.LanguageMst.objects.get(
                            Language=data["Language"], BankMstID=request.user.BankMstID
                        ),
                        IsAdminCreated=False,
                        BankMstID=request.user.BankMstID,
                        TemplateBody=response,
                    )

                    newivrflow = campmodels.IVRFlowMapping.objects.create(
                        FlowID=ivr.IVRFlowMstID,
                        FlowName=f"{data['Name']}_{button}",
                        Response=response,
                        IsActive=False,
                        LngMstID=campmodels.LanguageMst.objects.get(
                            Language=data["Language"], BankMstID=request.user.BankMstID
                        ),
                        IVRTemplateMappingID=newivrtempmap,
                        IVRFlowMstID=ivr,
                        IsStart=False,
                        ButtonPressed=button,
                        NextTemplate=newivrtempmap.IVRTemplateMappingID,
                        CurrentTemplate=ivrtempmap.IVRTemplateMappingID,
                        BankMstID=request.user.BankMstID,
                    )

                return Response(
                    {
                        "message": "IVR template registered successfully",
                        "ivr": ivr.IVRFlowMstID,
                    },
                    status=status.HTTP_201_CREATED,
                )
            except Exception as e:
                logging.info(e)
                transaction.set_rollback(True)
                return Response(
                    {"message": "Error registering IVR template", "error": str(e)},
                    status=status.HTTP_400_BAD_REQUEST,
                )


@extend_schema(
    tags=["SMS"],
    description="Register a new SMS template",
    responses={
        201: {
            "description": "SMS template registered successfully",
            "examples": {
                "application/json": {
                    "message": "SMS template registered successfully",
                    "sms": 1,
                }
            },
        },
        400: {
            "description": "Bad Request",
            "examples": {
                "application/json": {"status": "error", "message": "Language not found"}
            },
        },
    },
)
class RegisterSmsTemplate(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]

    def post(self, request):
        data = request.data
        with transaction.atomic():
            try:
                sms = campmodels.SMSTemplateMapping.objects.create(
                    Status="Pending",
                    TemplateName=data["Name"],
                    IsActive=False,
                    LngMstID=campmodels.LanguageMst.objects.get(
                        Language=data["Language"], BankMstID=request.user.BankMstID
                    ),
                    IsAdminCreated=False,
                    BankMstID=request.user.BankMstID,
                    TemplateBody=data["TemplateText"],
                )

                for variable, field in data.get("VariableMapping", {}).items():
                    smsvarmap = campmodels.SMSUserTemplate.objects.create(
                        TemplateName=data["Name"],
                        BankMstID=request.user.BankMstID,
                        VariableNumber=variable,
                        VariableField=field,
                        SMSTemplateMappingID=sms,
                    )
                return Response(
                    {
                        "message": "SMS template registered successfully",
                        "sms": sms.SMSTemplateMappingID,
                    },
                    status=status.HTTP_201_CREATED,
                )
            except Exception as e:
                logging.info(e)
                transaction.set_rollback(True)
                return Response(
                    {"message": "Error registering SMS template", "error": str(e)},
                    status=status.HTTP_400_BAD_REQUEST,
                )


@extend_schema(
    tags=["Campaign"],
    description="Update Flow ID in CommFlow",
    request={"CommFlowID": "string", "FlowID": "string"},
    responses={
        200: {
            "description": "Flow IDs updated successfully",
            "examples": {
                "application/json": {"message": "Flow IDs updated successfully"}
            },
        },
        400: {
            "description": "Bad Request",
            "examples": {
                "application/json": {
                    "status": "error",
                    "message": "CommFlowID or FlowID not provided",
                }
            },
        },
    },
)
class UpdateFlowIdCommFlow(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]

    def post(self, request):
        data = request.data
        with transaction.atomic():
            try:
                for flow in data:
                    commid = flow["CommFlowID"]
                    flowid = flow["FlowID"]
                    if not commid or not flowid:
                        transaction.set_rollback(True)
                        return Response(
                            {
                                "message": "CommFlowID or FlowID not provided",
                                "status": "error",
                            },
                            status=status.HTTP_400_BAD_REQUEST,
                        )
                    campmodels.CommFlow.objects.filter(CommFlowID=commid).update(
                        FlowID=flowid
                    )
                return Response(
                    {"message": "Flow IDs updated successfully"},
                    status=status.HTTP_200_OK,
                )
            except Exception as e:
                logging.info(e)
                transaction.set_rollback(True)
                return Response(
                    {"message": "Error updating Flow IDs", "error": str(e)},
                    status=status.HTTP_400_BAD_REQUEST,
                )


@extend_schema(
    tags=["Campaign"],
    description="Finalize a campaign",
    request={"CampaignMstID": "string", "Priority": "string", "Name": "string"},
    responses={
        200: {
            "description": "Campaign finalized successfully",
            "examples": {
                "application/json": {"message": "Campaign finalized successfully"}
            },
        },
        400: {
            "description": "Bad Request",
            "examples": {
                "application/json": {
                    "status": "error",
                    "message": "CampaignMstID or Priority not provided",
                }
            },
        },
    },
)
class FinalizeCampaign(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]

    def post(self, request):
        data = request.data
        campid = data["CampaignMstID"]
        priority = data["Priority"]
        name = data["Name"]
        campstatus = data.get("Status")
        if str(campstatus).lower() == 'save':
            campstatus = "Saved"
        else:
            campstatus = "Pending Approval"

        campmodels.CampaignMst.objects.filter(CampaignMstID=campid).update(
            IsActive=False, Priority=priority, Status=campstatus, Name=name
        )
        return Response(
            {"message": "Campaign finalized successfully", "status": "success"},
            status=status.HTTP_200_OK,
        )


@extend_schema(
    tags=["Filters"],
    description="Get filters",
    responses={
        200: campserializers.FiltersSerializer,
        404: {
            "description": "Not Found",
            "examples": {
                "application/json": {"status": "error", "message": "Filter not found"}
            },
        },
    },
)
class GetFilters(ModelViewSet):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    serializer_class = campserializers.FiltersSerializer
    filterset_fields = ["BankMstID", "Column", "Comparison"]
    pagination_class = None

    def get_queryset(self):
        user = self.request.user
        bank = user.BankMstID
        filters = campmodels.Filters.objects.filter(BankMstID=bank)
        return filters

    # delete filter
    def destroy(self, request, *args, **kwargs):
        filter_id = kwargs.get("pk")
        try:
            fitler_mst_obj = campmodels.FilterMst.objects.filter(FiltersID=filter_id)
            if fitler_mst_obj.exists():
                fitler_mst_obj.delete()
            filter_obj = campmodels.Filters.objects.get(FiltersID=filter_id)
            filter_obj.delete()
            return Response(
                {"message": "Filter deleted successfully"},
                status=status.HTTP_202_ACCEPTED,
            )
        except campmodels.Filters.DoesNotExist:
            return Response(
                {"message": "Filter not found"}, status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logging.info(e)
            return Response(
                {"message": "Error deleting filter", "error": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )


@extend_schema(
    tags=["Campaign"],
    description="Get campaigns",
    responses={
        200: campserializers.CampaignMstSerializer,
        404: {
            "description": "Not Found",
            "examples": {
                "application/json": {"status": "error", "message": "Campaign not found"}
            },
        },
    },
)
class GetCampaigns(ModelViewSet):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    filterset_fields = [
        "Name",
        "IsActive",
        "BankMstID",
        "Priority",
        "Status",
        "CampaignType",
    ]
    pagination_class = None

    def get_serializer_class(self):
        if self.action == "retrieve":
            return campserializers.CampaignMstDetailsSerializer
        return campserializers.CampaignMstSerializer

    def get_queryset(self):
        user = self.request.user
        bank = user.BankMstID
        campaigns = campmodels.CampaignMst.objects.filter(BankMstID=bank)
        return campaigns

    # update campaign
    def update(self, request, *args, **kwargs):
        campaignmst_id = kwargs.get("pk")
        try:
            mstobj = campmodels.CampaignMst.objects.get(CampaignMstID=campaignmst_id)
            if mstobj:
                serializer = campserializers.CampaignMstSerializer(
                    mstobj, data=request.data
                )
                if serializer.is_valid():
                    serializer.save()
                    return Response(
                        {"message": "Campaign updated successfully"},
                        status=status.HTTP_200_OK,
                    )
                return Response(
                    {"message": "Error updating campaign", "errors": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            else:
                return Response(
                    {"message": "Campaign not found"}, status=status.HTTP_404_NOT_FOUND
                )
        except campmodels.CampaignMst.DoesNotExist:
            return Response(
                {"message": "Campaign not found"}, status=status.HTTP_404_NOT_FOUND
            )

    # delete campaign
    def destroy(self, request, *args, **kwargs):
        campaignmst_id = kwargs.get("pk")
        try:
            mstobj = campmodels.CampaignMst.objects.get(CampaignMstID=campaignmst_id)
            if mstobj:
                if mstobj.Status in ["Saved", "Rejected"]:
                    commflow = campmodels.CommFlow.objects.filter(CampaignMstID=mstobj)
                    if commflow.exists():
                        commflow.delete()
                    filters = campmodels.Filters.objects.filter(
                        filtermst__CampaignMstID=mstobj
                    )  # Use Related name to fetch the filters
                    filtermst = campmodels.FilterMst.objects.filter(
                        CampaignMstID=mstobj
                    )
                    if filters.exists():
                        if filtermst.exists():
                            filtermst.delete()
                            filters.delete()
                    mstobj.delete()
                    return Response(
                        {"message": "Campaign deleted successfully"},
                        status=status.HTTP_202_ACCEPTED,
                    )
                else:
                    return Response(
                        {
                            "message": "Campaign cannot be deleted as it is already approved or is in progress"
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )
        except campmodels.CampaignMst.DoesNotExist:
            return Response(
                {"message": "Campaign not found"}, status=status.HTTP_404_NOT_FOUND
            )


@extend_schema(
    tags=["Campaign"],
    description="Get communication flows",
    responses={
        200: campserializers.CommFlowSerializer,
        404: {
            "description": "Not Found",
            "examples": {
                "application/json": {
                    "status": "error",
                    "message": "Communication flow not found",
                }
            },
        },
    },
)
class GetCommunicationFlows(ModelViewSet):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    pagination_class = None

    def get_serializer_class(self):
        return campserializers.CommFlowSerializer

    def get_queryset(self):
        user = self.request.user
        bank = user.BankMstID
        flows = campmodels.CommFlow.objects.filter(BankMstID=bank)
        return flows

    # delete communication flow
    def destroy(self, request, *args, **kwargs):
        commflow_id = kwargs.get("pk")
        with transaction.atomic():
            try:
                commflow = campmodels.CommFlow.objects.get(CommFlowID=commflow_id)
                if commflow:
                    commflow.delete()
                    return Response(
                        {"message": "Communication flow deleted successfully"},
                        status=status.HTTP_202_ACCEPTED,
                    )
                else:
                    return Response(
                        {"message": "Communication flow not found"},
                        status=status.HTTP_404_NOT_FOUND,
                    )
            except campmodels.CommFlow.DoesNotExist:
                print("Communication flow not found")
                return Response(
                    {"message": "Communication flow not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )
            except Exception as e:
                logging.info(e)
                transaction.set_rollback(True)
                return Response(
                    {"message": "Error deleting communication flow", "error": str(e)},
                    status=status.HTTP_400_BAD_REQUEST,
                )


@extend_schema(
    tags=["WhatsApp"],
    description="Get WhatsApp flows",
    responses={
        200: campserializers.WhatsAppFlowMstSerializer,
        404: {
            "description": "Not Found",
            "examples": {
                "application/json": {
                    "status": "error",
                    "message": "WhatsApp flow not found",
                }
            },
        },
    },
)
class GetWhatsAppFlows(ReadOnlyModelViewSet):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    pagination_class = None
    # filters -> campfilters.WhatsAppFlowFilter
    filterset_class = campfilters.WhatsAppFlowMstFilter

    def get_serializer_class(self):
        if self.action == "retrieve":
            return campserializers.WhatsAppFlowMstDetailsSerializer
        return campserializers.WhatsAppFlowMstSerializer

    def get_queryset(self):
        user = self.request.user
        bank = user.BankMstID
        flows = campmodels.WhatsAppFlowMst.objects.filter(BankMstID=bank).distinct()
        return flows


@extend_schema(
    tags=["Blaster"],
    description="Get Blaster flows",
    responses={
        200: campserializers.BlasterTemplateMappingSerializer,
        404: {
            "description": "Not Found",
            "examples": {
                "application/json": {
                    "status": "error",
                    "message": "Blaster flow not found",
                }
            },
        },
    },
)
class GetBlasterFlows(ReadOnlyModelViewSet):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    pagination_class = None
    filterset_class = campfilters.BlasterTemplateMappingFilter

    def get_serializer_class(self):
        if self.action == "retrieve":
            return campserializers.BlasterTemplateMappingDetailsSerializer
        return campserializers.BlasterTemplateMappingSerializer

    def get_queryset(self):
        user = self.request.user
        bank = user.BankMstID
        flows = campmodels.BlasterTemplateMapping.objects.filter(BankMstID=bank)
        return flows


@extend_schema(
    tags=["VoiceBot"],
    description="Get VoiceBot flows",
    responses={
        200: campserializers.VoiceBotTemplateMappingSerializer,
        404: {
            "description": "Not Found",
            "examples": {
                "application/json": {
                    "status": "error",
                    "message": "VoiceBot flow not found",
                }
            },
        },
    },
)
class GetVoiceBotFlows(ReadOnlyModelViewSet):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    pagination_class = None
    filterset_class = campfilters.VoiceBotTemplateMappingFilter

    def get_serializer_class(self):
        if self.action == "retrieve":
            return campserializers.VoiceBotTemplateMappingDetailsSerializer
        return campserializers.VoiceBotTemplateMappingSerializer

    def get_queryset(self):
        user = self.request.user
        bank = user.BankMstID
        flows = campmodels.VoiceBotTemplateMapping.objects.filter(BankMstID=bank)
        return flows


@extend_schema(
    tags=["IVR"],
    description="Get IVR flows",
    responses={
        200: campserializers.IVRFlowMappingSerializer,
        404: {
            "description": "Not Found",
            "examples": {
                "application/json": {"status": "error", "message": "IVR flow not found"}
            },
        },
    },
)
class GetIVRFlows(ReadOnlyModelViewSet):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    pagination_class = None
    filterset_class = campfilters.IVRFlowMstFilter

    def get_serializer_class(self):
        if self.action == "retrieve":
            return campserializers.IVRFlowMappingDetailsSerializer
        return campserializers.IVRFlowSerializer

    def get_queryset(self):
        user = self.request.user
        bank = user.BankMstID
        flows = campmodels.IVRFlowMst.objects.filter(BankMstID=bank)
        return flows


@extend_schema(
    tags=["SMS"],
    description="Get SMS flows",
    responses={
        200: campserializers.SmsFlowSerializer,
        404: {
            "description": "Not Found",
            "examples": {
                "application/json": {"status": "error", "message": "SMS flow not found"}
            },
        },
    },
)
class GetSmsFlows(ReadOnlyModelViewSet):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    serializer_class = campserializers.SmsFlowSerializer
    pagination_class = None

    def get_queryset(self):
        user = self.request.user
        bank = user.BankMstID
        flows = campmodels.SMSTemplateMapping.objects.filter(BankMstID=bank)
        return flows
    
    


@extend_schema(
    tags=["Calling"],
    description="Get Calling flows",
    responses={
        200: campserializers.CallingTemplateMappingSerializer,
        404: {
            "description": "Not Found",
            "examples": {
                "application/json": {
                    "status": "error",
                    "message": "Calling flow not found",
                }
            },
        },
    },
)

class GetCallingFlows(ReadOnlyModelViewSet):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    pagination_class = None

    def get_serializer_class(self):
        if self.action == "retrieve":
            return campserializers.CallingTemplateMappingDetailsSerializer
        return campserializers.CallingTemplateMappingSerializer

    def get_queryset(self):
        user = self.request.user
        bank = user.BankMstID
        flows = campmodels.CallingTemplateMapping.objects.filter(BankMstID=bank)
        return flows


@extend_schema(
    tags=["Campaign"],
    description="Get campaign category options",
    parameters=[
        OpenApiParameter(
            name="requesttype",
            description="Type of request: campaigncategory|campaigncategorydata|variablemapping",
            type=OpenApiTypes.STR,
            required=True,
        ),
        OpenApiParameter(
            name="columnname",
            description="Column name for campaign category data",
            type=OpenApiTypes.STR,
        ),
    ],
    request={
        "requesttype": "campaigncategory|campaigncategorydata|variablemapping",
        "columnname": "string",
    },
    responses={
        200: {
            "description": "Campaign category options retrieved successfully",
            "examples": {
                "application/json": {
                    "message": "Campaign category options retrieved successfully"
                }
            },
        },
        400: {
            "description": "Bad Request",
            "examples": {
                "application/json": {
                    "status": "error",
                    "message": "Invalid request type",
                }
            },
        },
    },
)
class CampaignCategoryOptions(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]

    VariableColumnNames = {
        "BankName": "text",
        "CustomerCode": "text",
        "CustomerName": "text",
        "MobileNumber": "text",
        "Gender": "text",
        "DateOfBirth": "date",
        "DisbursementID": "text",
        "DisbursementDate": "text",
        "DisbursementAmt": "number",
        "LoanType": "text",
        "DPD": "number",
        "LoanClassification": "text",
        "LastPaymentDate": "date",
        "LastCollectedAmount": "number",
        "CurrentBalance": "number",
        "OverDueAmt": "number",
        "PrincipleOutstanding": "number",
        "InterestOutstanding": "number",
        "TotalOutstanding": "number",
        "PrinciplePending": "number",
        "InterestPending": "number",
        "TotalPending": "number",
        "ClosingDate": "date",
        "EMIAmount": "number",
        "NextEMIDate": "date",
        "CreatedDate": "date",
        "Branch_id": "text",
        "Branch": "text",
        "Region_id": "text",
        "Region": "text",
        "State_id": "text",
        "State": "text",
        "groupid": "text",
        "groupname": "text",
        "CurrentDPD": "number",
        "LastMonthDPD": "number",
        "Division": "text",
        "District_id": "text",
        "CO": "text",
        "Circle_id": "text",
        "Zone": "text",
        "Zone_id": "text",
        "Circle": "text",
        "Division_id": "text",
        "CO_id": "text",
        "HeadOffice_id": "text",
        "HeadOffice": "text",
        "District": "text",
        "primarylanguage": "text",
        "secondarylanguage" :"text",
        "PromiseAmount": "text",
        "PromiseDate": "date"
    }

    CategoryColumnNames = {
        "Gender": "dropdown",
        "DateOfBirth": "date",
        "DisbursementAmt": "number",
        "LoanType": "dropdown",
        "DPD": "number",
        "LoanClassification": "dropdown",
        "LastPaymentDate": "date",
        "LastCollectedAmount": "number",
        "CurrentBalance": "number",
        "OverDueAmt": "number",
        "PrincipleOutstanding": "number",
        "InterestOutstanding": "number",
        "TotalOutstanding": "number",
        "PrinciplePending": "number",
        "InterestPending": "number",
        "TotalPending": "number",
        "ClosingDate": "date",
        "EMIAmount": "number",
        "CreatedDate": "date",
        "Branch_id": "dropdown",
        "Branch": "dropdown",
        "Region_id": "dropdown",
        "Region": "dropdown",
        "State_id": "dropdown",
        "State": "dropdown",
        "CurrentDPD": "number",
        "LastMonthDPD": "number",
        "Division": "dropdown",
        "District_id": "dropdown",
        "CO": "dropdown",
        "Circle_id": "dropdown",
        "Zone": "dropdown",
        "Zone_id": "dropdown",
        "Circle": "dropdown",
        "Division_id": "dropdown",
        "CO_id": "dropdown",
        "HeadOffice_id": "dropdown",
        "HeadOffice": "dropdown",
        "District": "dropdown",
        "ExtraColumn1": "dropdown",
        "ExtraColumn2": "dropdown",
        "ExtraColumn3": "dropdown",
        "ExtraColumn4": "dropdown",
        "ExtraColumn5": "dropdown",
        "First_Time_Arrear_Clients": "dropdown",
        "primarylanguage": "dropdown",
        "secondarylanguage" :"dropdown",
        "PromiseAmount": "number",
        "PromiseDate": "date"
    }

    def get(self, request):
        if request.GET.get("requesttype") == "campaigncategory":
            return Response(self.CategoryColumnNames, status=status.HTTP_200_OK)

        elif request.GET.get("requesttype") == "campaigncategorydata":
            columnname = request.GET.get("columnname")
            if columnname not in self.CategoryColumnNames.keys():
                return Response(
                    {"message": "Invalid column name"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if self.CategoryColumnNames[columnname] != "dropdown":
                return Response(
                    {
                        "message": "Invalid column type, Column does not require a dropdown list."
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            uniquevalues = []
            try:
                uniquevalues = (
                    AccountSummary.objects.filter(BankMstID=request.user.BankMstID)
                    .filter(
                        **{
                            f"{columnname}__isnull": False
                        }  # Ensure the column is not null
                    )
                    .values_list(columnname, flat=True)
                    .order_by(columnname)
                    .distinct()
                )
                uniquevalues = list(uniquevalues)
            except Exception as e:
                logging.info(e)

            data = {
                "values": uniquevalues,
            }
            return Response(data, status=status.HTTP_200_OK)

        elif request.GET.get("requesttype") == "variablemapping":
            return Response(self.VariableColumnNames, status=status.HTTP_200_OK)

        elif request.GET.get("requesttype") == "basedoncolumn":
            pass

        else:
            return Response(
                {"message": "Invalid request type"}, status=status.HTTP_400_BAD_REQUEST
            )


@extend_schema(
    tags=["Bank Control"],
    description="Get campaign category options",
    parameters=[
        OpenApiParameter(
            name="requesttype",
            description="Type of request: campaigncategoryall|campaigncategory|campaigncategorydata|variablemapping",
            type=OpenApiTypes.STR,
            required=True,
        ),
        OpenApiParameter(
            name="columnname",
            description="Column name for campaign category data",
            type=OpenApiTypes.STR,
        ),
    ],
    request={
        "requesttype": "campaigncategoryall|campaigncategory|campaigncategorydata|variablemapping",
        "columnname": "string",
    },
    responses={
        200: {
            "description": "Campaign category options retrieved successfully",
            "examples": {
                "application/json": {
                    "message": "Campaign category options retrieved successfully"
                }
            },
        },
        400: {
            "description": "Bad Request",
            "examples": {
                "application/json": {
                    "status": "error",
                    "message": "Invalid request type",
                }
            },
        },
    },
)
class CampaignCategoryOptionsV2(APIView):
    """
    API view for retrieving campaign category options and related metadata.

    This view provides various types of campaign-related data based on the request type,
    including column metadata, category data, variable mappings, and based-on columns.
    """

    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]

    def get(self, request):
        """
        Retrieve campaign category options based on the request type.

        This method handles different types of requests for campaign configuration:
        - 'campaigncategoryall': Returns all column metadata
        - 'campaigncategory': Returns bank-specific campaign category columns
        - 'campaigncategorydata': Returns unique values for a specific dropdown column
        - 'variablemapping': Returns variable mapping configuration for the bank
        - 'basedoncolumn': Returns based-on column configuration for the bank

        Parameters:
            request (HttpRequest): The HTTP request object containing query parameters:
                - requesttype (str): Type of request ('campaigncategoryall', 'campaigncategory',
                                    'campaigncategorydata', 'variablemapping', 'basedoncolumn')
                - columnname (str, optional): Required for 'campaigncategorydata' requests,
                                             specifies which column's data to retrieve

        Returns:
            Response: A Django REST framework Response object containing:
                - For 'campaigncategoryall': All column metadata
                - For 'campaigncategory': Bank-specific campaign category columns
                - For 'campaigncategorydata': List of unique values for the specified column
                - For 'variablemapping': Variable mapping configuration
                - For 'basedoncolumn': Based-on column configuration
                - Error responses with appropriate status codes for invalid requests
        """
        if request.GET.get("requesttype") == "campaigncategoryall":
            column_metadata = webappmodels.AccountSummary.column_metadata
            return Response(column_metadata, status=status.HTTP_200_OK)

        elif request.GET.get("requesttype") == "campaigncategory":
            bankmstid = request.user.BankMstID
            controlobj = webappmodels.BankControls.objects.filter(
                BankMstID=bankmstid
            ).first()
            if not controlobj:
                return Response(
                    {"message": "No columns found for this bank"},
                    status=status.HTTP_404_NOT_FOUND,
                )
            if not controlobj.Json:
                return Response(
                    {"message": "No columns found for this bank"},
                    status=status.HTTP_404_NOT_FOUND,
                )
            columns = controlobj.Json.get("CampaignCategoryColumns", [])
            column_metadata = {
                key: value
                for key, value in webappmodels.AccountSummary.column_metadata.items()
                if key in columns
            }
            return Response(column_metadata, status=status.HTTP_200_OK)

        elif request.GET.get("requesttype") == "campaigncategorydata":
            columnname = request.GET.get("columnname")
            column_metadata = webappmodels.AccountSummary.column_metadata
            if columnname not in column_metadata.keys():
                return Response(
                    {"message": "Invalid column name"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if column_metadata[columnname]["type"] != "dropdown":
                return Response(
                    {
                        "message": "Invalid column type, Column does not require a dropdown list."
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            uniquevalues = []
            try:
                uniquevalues = (
                    AccountSummary.objects.filter(BankMstID=request.user.BankMstID)
                    .filter(
                        **{
                            f"{columnname}__isnull": False
                        }  # Ensure the column is not null
                    )
                    .values_list(columnname, flat=True)
                    .order_by(columnname)
                    .distinct()
                )
                uniquevalues = list(uniquevalues)
            except Exception as e:
                logging.info(e)

            data = {
                "values": uniquevalues,
            }
            return Response(data, status=status.HTTP_200_OK)

        elif request.GET.get("requesttype") == "variablemapping":
            bankmstid = request.user.BankMstID
            controlobj = webappmodels.BankControls.objects.filter(
                BankMstID=bankmstid
            ).first()
            if not controlobj:
                return Response(
                    {"message": "No columns found for this bank"},
                    status=status.HTTP_404_NOT_FOUND,
                )
            if not controlobj.Json:
                return Response(
                    {"message": "No columns found for this bank"},
                    status=status.HTTP_404_NOT_FOUND,
                )
            variable_mapping: Dict = controlobj.Json.get("VariableMapping", {})
            if not variable_mapping:
                return Response(
                    {"message": "No variable mapping found for this bank"},
                    status=status.HTTP_404_NOT_FOUND,
                )
            variable_mapping = {
                key: value
                for key, value in webappmodels.AccountSummary.column_metadata.items()
                if key in variable_mapping
            }
            sec_cust = {"SecondaryCustomerName": "Secondary Customer Name"}
            variable_mapping.update(sec_cust)
            return Response(variable_mapping, status=status.HTTP_200_OK)

        elif request.GET.get("requesttype") == "basedoncolumn":
            bankmstid = request.user.BankMstID
            controlobj = webappmodels.BankControls.objects.filter(
                BankMstID=bankmstid
            ).first()
            if not controlobj:
                return Response(
                    {"message": "No columns found for this bank"},
                    status=status.HTTP_404_NOT_FOUND,
                )
            if not controlobj.Json:
                return Response(
                    {"message": "No columns found for this bank"},
                    status=status.HTTP_404_NOT_FOUND,
                )
            based_on_column = controlobj.Json.get("BasedOnColumn", {})
            if not based_on_column:
                return Response(
                    {"message": "No based on column mapping found for this bank"},
                    status=status.HTTP_404_NOT_FOUND,
                )
            based_on_column = {
                key: value
                for key, value in webappmodels.AccountSummary.column_metadata.items()
                if key in based_on_column
            }
            return Response(based_on_column, status=status.HTTP_200_OK)
        else:
            return Response(
                {"message": "Invalid request type"}, status=status.HTTP_400_BAD_REQUEST
            )


class CopyAndEditCampaign(APIView):
    """
    API view for copying and editing campaigns.
    This view handles copying and editing campaigns based on the provided bank ID and campaign ID.
    """

    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]

    def post(self, request):
        """
        Copy or edit a campaign.
        This method handles different types of requests for copying and editing campaigns:
        - 'copy': Creates a new campaign based on an existing one that has been submitted for approval
        - marks the status of copied campaign as saved
        """

        newcampaignname = request.data.get("newcampaignname")
        campidtocopy = request.data.get("CampaignMstID")
        bankid = request.user.BankMstID

        campaign = campmodels.CampaignMst.objects.filter(
            CampaignMstID=campidtocopy, BankMstID=bankid
        ).first()
        if not campaign:
            return Response(
                {"message": "Campaign not found"}, status=status.HTTP_404_NOT_FOUND
            )

        onboardobj = OnboardBankCopyCampaigns(bankid)
        newcampaignobj: campmodels.CampaignMst = onboardobj._duplicatecampaign(campaign)
        newcampaignobj.Name = newcampaignname
        commflows = campmodels.CommFlow.objects.filter(CampaignMstID=campaign)
        if commflows.exists():
            newcommflowmstobj = onboardobj._duplicatecommflowmst(commflows[0])
            for commflow in commflows:
                onboardobj._duplicatecommflow(
                    commflow, newcommflowmstobj, newcampaignobj
                )

        newcampaignobj.Status = "Saved"
        newcampaignobj.save()

        return Response(
            {"message": "Campaign copied and edited successfully"},
            status=status.HTTP_201_CREATED,
        )

# Calling team
@extend_schema(
    tags=["Calling"],
    description="Register a new Calling template",
    request=campserializers.CallingTemplateMappingSerializer,
    responses={
        201: campserializers.CallingTemplateMappingSerializer,
        400: {
            "description": "Bad Request",
            "examples": {
                "application/json": {"status": "error", "message": "Language not found"}
            },
        },
    },
)

class RegisterCallingTemplate(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    
    print("Inside RegisterCallingTemplate")
    def post(self, request):
        data = request.data
        with transaction.atomic():
            try:
                print("Inside try block")
                calling = campmodels.CallingTemplateMapping.objects.create(
                    TemplateName=data["Name"],
                    BankMstID=request.user.BankMstID,
                    IsActive=False,
                    IsAdminCreated=False,
                    Status="Pending",
                    TemplateBody=data["TemplateText"],
                )
                print("template created sucessfully")
                return Response(
                    {
                        "message": "Calling template registered successfully",
                        "calling": calling.CallingTemplateMappingID,
                        "status": "success",
                    },
                    status=status.HTTP_201_CREATED,
                )
            except Exception as e:
                print("Inside except ",e)
                logging.info(e)
                transaction.set_rollback(True)
                return Response(
                    {
                        "message": "Error registering Calling template",
                        "error": str(e),
                        "status": "error",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
                
                
                

@extend_schema(
    tags=["Campaign"],
    description="Get all campaign types",
    responses={
        200: {
            "description": "Campaign types retrieved successfully",
            "examples": {
                "application/json": [
                    {
                        "CampaignTypeID": 1,
                        "TypeName": "normal",
                        "DisplayName": "Normal",
                        "IsActive": True
                    },
                    {
                        "CampaignTypeID": 2,
                        "TypeName": "calling",
                        "DisplayName": "Calling",
                        "IsActive": True
                    }
                ]
            }
        }
    }
)
class CampaignTypesViewSet(ViewSet):
    """
    ViewSet to retrieve campaign types.
    This viewset returns a list of predefined campaign types with their IDs, names, display names, and active status.
    """
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]

    def list(self, request):
        campaign_types = [
            {"CampaignTypeID": 1, "TypeName": "normal", "DisplayName": "Normal", "IsActive": True},
            {"CampaignTypeID": 2, "TypeName": "calling", "DisplayName": "Calling", "IsActive": True}
        ]
        return Response(campaign_types, status=status.HTTP_200_OK)            

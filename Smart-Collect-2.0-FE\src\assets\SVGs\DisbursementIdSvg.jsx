import PropTypes from "prop-types"

export default function DisbursementIdSvg({width =17, height=14, ...props}) {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 17 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M10.362 7.233h3.707v-.91h-3.707v.91zm0-2.519h3.707v-.909h-3.707v.91zM2.738 9.821h6.016V9.67c0-.53-.267-.941-.8-1.232C7.42 8.147 6.684 8 5.746 8c-.938 0-1.674.146-2.208.437-.534.29-.8.701-.8 1.232v.15zm3.008-3.288c.384 0 .707-.131.97-.394.262-.263.394-.586.394-.97s-.132-.707-.395-.97a1.322 1.322 0 00-.969-.394c-.383 0-.706.131-.97.395a1.32 1.32 0 00-.394.97c0 .382.132.705.395.97.263.263.586.395.97.393zM1.69 13.177c-.419 0-.768-.14-1.048-.42-.28-.28-.42-.63-.421-1.049v-9.79c0-.42.14-.769.42-1.049.281-.28.63-.42 1.048-.42h13.43c.418 0 .767.14 1.047.42.28.281.42.63.42 1.049v9.791c0 .418-.14.768-.42 1.049-.28.28-.63.42-1.048.42H1.69zm0-.909h13.428c.14 0 .268-.058.385-.174a.53.53 0 00.175-.386v-9.79a.537.537 0 00-.175-.386.53.53 0 00-.385-.174H1.69a.536.536 0 00-.384.174.53.53 0 00-.175.386v9.791c0 .14.058.268.175.385a.53.53 0 00.384.174"
        fill="#000"
      />
    </svg>
  )
}
DisbursementIdSvg.propTypes ={
  width: PropTypes.number,
  height: PropTypes.number,
}
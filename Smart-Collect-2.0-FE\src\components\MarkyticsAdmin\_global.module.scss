@import url("../../index.css");

$light-blue: #e4f8f9;
$blue: #becfff;
$disable: #787777;
$table-radius: 22px;
$attempts-bg: #bdd1ff;
$body: #e4f8f9;
$green: #1cae4d;
.add_bank {
  padding-top: 1rem !important;
  width: 100%;
  .text,
  .remark {
    color: var(--dark-blue);
    font-weight: 400;
    white-space: nowrap;
    width: 100px;
    font-family: "Kanit", "Courier New", Courier, monospace;
  }

  .remark {
    width: 101px;
  }

  .custom_input {
    caret-color: #407bff;
    padding: 0.4rem;
    &:global(.ant-input-outlined) {
      border: none;
      background-color: $light-blue;
      color: var(--dark-blue);
      font-weight: 400;
      border-radius: 8px;
      box-shadow: 0px 2px 2px 0px #******** inset;
      font-family: "Kanit", "Courier New", Courier, monospace;
    }
  }

  .custom_select {
    width: 100%;
    box-shadow: 0px 2px 2px 0px #********;
    background-color: $light-blue;
    border-radius: 10px;

    :global(.ant-select-selector) {
      background-color: $light-blue;
      color: var(--dark-blue);
      border: none !important;
      font-family: "Kanit", "Courier New", Courier, monospace;
    }
    :global(.ant-select-selection-item) {
      font-weight: 400;
      color: var(--dark-blue);
    }
    :global(.ant-select-selection-placeholder) {
      color: #0f205052;
      font-weight: 400;
    }
  }

  .custom_number {
    &:global(.ant-picker-outlined),
    &:global(.ant-input-number) {
      background-color: $light-blue;
      border: none !important;
      padding: 0.1rem;
    }

    &:global(.ant-input-number) {
      width: 100%;
      box-shadow: 0px 2px 2px 0px #******** inset;
    }

    :global(.ant-input-number-input) {
      color: var(--dark-blue);
      font-weight: 400;
      font-family: "Kanit", "Courier New", Courier, monospace;
      padding: 0.3rem 0.5rem;
    }

    :global(.ant-input-number-handler-wrap) {
      display: none;
    }
  }

  .add_button {
    padding: 1.2rem 6rem;
    background-color: var(--dark-blue);
    color: white;
    font-family: "Kanit", "Courier New", Courier, monospace;
    &:hover {
      background-color: var(--dark-blue) !important;
      color: white !important;
      outline: none;
    }
  }

  .img {
    width: 15px;
    img {
      width: 100%;
      object-fit: contain;
    }
  }

  .customTable {
    padding-top: 1.5rem;
    .name {
      font-weight: 500;
      color: var(--dark-blue);
    }

    .icon_div {
      display: flex;
      justify-content: center;
      width: 100%;
      .icon_img {
        width: 15px;
        height: 15px;
        img {
          width: 100%;
          object-fit: contain;
          cursor: pointer;
        }
      }
    }

    .status {
      font-weight: 600;
    }

    //Global ant design classes
    // Pagination
    :global(.ant-pagination) {
      justify-content: center;
      margin: 0 !important;

      :global(.ant-pagination-prev),
      :global(.ant-pagination-next) {
        color: var(--dark-blue);
        border: 0 solid var(--blue);
        background: #e4f8f9;
        height: 25px;
        min-width: 15px;
        border-radius: 0px;
        margin: 0;
        button {
          border-radius: 0px;
        }
      }
      :global(.ant-pagination-item) {
        margin-right: 0;
        a {
          color: $disable;
          font-size: 0.9rem;
          line-height: 23px;
          font-family: "Kanit", "Courier New", Courier, monospace;
          font-weight: normal;
        }
        &:hover {
          background-color: transparent;
        }
      }
      :global(.ant-pagination-item-active) {
        border: none;
        a {
          color: var(--dark-blue);
          font-size: 1.2rem;
          padding: 0;
        }
      }
    }

    //Table container
    :global(.ant-table-container) {
      padding: 0;
      margin-bottom: 0.5rem;
      border-start-start-radius: $table-radius;
      border-start-end-radius: $table-radius;

      // Table header
      :global(.ant-table-header) {
        position: relative;
        margin: 0 auto;
        top: -21px;
        border-radius: $table-radius;
        :global(.ant-table-column-has-sorters) {
          background-color: var(--dark-blue);
          &:hover {
            background-color: var(--dark-blue);
          }

          :global(.ant-table-column-sorter-up),
          :global(.ant-table-column-sorter-down) {
            svg {
              fill: white;
            }
            &:global(.active) {
              svg {
                fill: rgb(24, 155, 249);
              }
            }
          }
        }
      }

      // Table virtual body
      :global(.ant-table-tbody-virtual) {
        margin-top: -8px;
      }

      &::before {
        box-shadow: none !important;
      }
    }

    // Table rows header
    :global(.ant-table-thead > tr) {
      th {
        border-start-end-radius: 0 !important;
        background-color: var(--dark-blue);
        border-inline-end: none !important;
        color: white;
        border-bottom: none;
        text-align: center;
        font-family: "Kanit", "Courier New", Courier, monospace;
        font-weight: normal;

        &:not(:last-child):not(.ant-table-selection-column):not(
            .ant-table-row-expand-icon-cell
          ):not([colspan])::before {
          width: 0;
        }

        :global(.ant-table-selection) {
          :global(.ant-checkbox-checked:not(.ant-checkbox-disabled)) {
            :global(.ant-checkbox-inner) {
              border-color: $green;
              background-color: $green;
              &::after {
                background-color: $green;
              }
            }
          }
          :global(.ant-checkbox-inner) {
            border-color: $green;
            &::after {
              background-color: $green;
            }
          }
        }
      }
    }

    // Table body
    :global(.ant-table-tbody) {
      // Body rows
      :global(.ant-table-row) {
        &:nth-child(even) {
          background-color: var(--light-green);
          border-radius: 8px;
        }
        :global(.ant-table-cell-row-hover) {
          background-color: transparent;
        }
        // Cols
        :global(.ant-table-cell) {
          font-weight: 600;
          text-align: center;
          border-bottom: 2px solid white;

          :global(.ant-typography) {
            color: var(--dark-blue);
            font-family: "Kanit", "Courier New", Courier, monospace;
            font-weight: normal;
          }

          &:global(.ant-table-cell-fix-right) {
            border-bottom: none !important;
          }
          &:global(.ant-table-cell-fix-right-first) {
            border-left: 2px solid white;
          }
          :global(.ant-checkbox) {
            :global(.ant-checkbox-inner) {
              border-color: var(--dark-blue);
            }
          }
          :global(.ant-checkbox-checked) {
            border-color: $green !important;
            :global(.ant-checkbox-inner) {
              background-color: $green;
              border-color: $green;
            }
          }
        }
      }
    }

    // Fixed Cols
    :global(.ant-table-cell-fix-right) {
      background-color: $body;
    }
  }
}

:global(.ant-select-dropdown) {
  margin-top: 1.2rem;
  background-color: $light-blue;

  :global(.ant-select-item-option) {
    color: var(--dark-blue);
    font-weight: 600;

    &:global(.ant-select-item-option-disabled) {
      color: rgba(0, 0, 0, 0.56);
    }
  }
  :global(.ant-select-item-option-active) {
    &:not(.ant-select-item-option-disabled) {
      background-color: $blue;
      font-weight: 600;
    }
  }
}

// Responsive View
@media screen and (max-width: 768px) {
  .add_bank {
    row-gap: 30px !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    :global(.ant-col) {
      padding: 0 !important;
    }
    .text,
    .remark {
      font-size: 11px;
      width: 100%;
    }
    .remark {
      width: 110px;
    }
    .custom_input {
      font-size: 10px;
    }
    .custom_select {
      :global(.ant-select-selection-item) {
        font-size: 10px;
      }
      :global(.ant-select-selection-placeholder) {
        font-size: 10px;
      }
    }
  }
  :global(.ant-select-dropdown) {
    :global(.ant-select-item-option) {
      font-size: 10px;
    }
  }
}

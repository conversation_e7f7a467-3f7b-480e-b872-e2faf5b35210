import { Flex, Table, Typography } from "antd";
import { useEffect, useState } from "react";
import AppHeader from "../AppHeader/index";
import { AXIOS } from "../../apis/ho-Instance";
import ApplicationLoader from "../ApplicationLoader";
import Style from "./_style.module.scss";
import { handleDateFormatter, ALL_CACHE_NAMES } from "../../constant";
import {
  getCacheKey,
  getFromCache,
  storeToCache,
} from "../../utils/cacheHelper";

const { Text } = Typography;
const CACHE_NAME = ALL_CACHE_NAMES.REALLOCATE_CACHE;

export default function ReallocateActionList() {
  const userDetails = JSON.parse(localStorage.getItem("user"));
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState([]);

  // Fetch API data and set state
  const getAllTableData = async () => {
    setIsLoading(true);
    // Build cache key for GET request (with params)
    const cacheKey = getCacheKey({
      endpoint: "reallocation_details/",
      params: {
        Designation: userDetails?.designation,
        bankmstid: userDetails?.BankMstID,
      },
    });
    // Try to get cached data for instant UI update
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setData(cachedData);
      setIsLoading(false);
    }
    try {
      const response = await AXIOS.get("reallocation_details/", {
        params: {
          Designation: userDetails?.designation,
          bankmstid: userDetails?.BankMstID,
        },
      });
      if (response.status === 200 && response.data?.reallocations?.length > 0) {
        setData(response.data.reallocations);
        // Store the latest data in cache
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data.reallocations,
        });
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      sorter: (a, b) => a.key - b.key,
      render: (key) => <Text>{key}.</Text>,
    },
    {
      title: "Customer Id",
      dataIndex: "CustomerMstID",
      sorter: (a, b) =>
        String(a.CustomerMstID || "").localeCompare(
          String(b.CustomerMstID || "")
        ),
      render: (value) => <Text>{value || "-"}</Text>,
    },
    {
      title: "Customer Name",
      dataIndex: "CustomerName",
      sorter: (a, b) =>
        String(a?.CustomerName ?? "").localeCompare(
          String(b?.CustomerName ?? "")
        ),
      render: (value) => <Text className={Style.name}>{value || "-"}</Text>,
    },
    {
      title: "Disbursement Id",
      dataIndex: "DisbursementID",
      sorter: (a, b) =>
        String(a.DisbursementID || "").localeCompare(
          String(b.DisbursementID || "")
        ),
      render: (value) => <Text>{value || "-"}</Text>,
    },
    {
      title: "Branch Name",
      dataIndex: "BranchName",
      sorter: (a, b) =>
        String(a.BranchName || "").localeCompare(String(b.BranchName || "")),
      render: (value) => <Text>{value ?? "-"}</Text>,
    },
    {
      title: "Old Collection officer",
      dataIndex: "OldCollectionOfficerName",
      sorter: (a, b) =>
        String(a.OldCollectionOfficerName || "").localeCompare(
          String(b.OldCollectionOfficerName || "")
        ),
      render: (value) => <Text className={Style.name}>{value || "-"}</Text>,
    },
    {
      title: "New Collection officer",
      dataIndex: "NewCollectionOfficerName",
      sorter: (a, b) =>
        String(a.NewCollectionOfficerName || "").localeCompare(
          String(b.NewCollectionOfficerName || "")
        ),
      render: (value) => <Text className={Style.name}>{value || "-"}</Text>,
    },
    {
      title: "Action Type",
      dataIndex: "ActionType",
      sorter: (a, b) =>
        String(a.ActionType || "").localeCompare(String(b.ActionType || "")),
      render: (value) => <Text>{value || "-"}</Text>,
    },
    {
      title: "Date Range",
      dataIndex: "dateRange",
      children: [
        {
          title: "From",
          dataIndex: "StartDate",
          render: (value) => (
            <Text>{value ? handleDateFormatter(value) : "-"}</Text>
          ),
        },
        {
          title: "To",
          dataIndex: "EndDate",
          render: (value) => (
            <Text>{value ? handleDateFormatter(value) : "-"}</Text>
          ),
        },
      ],
    },
    {
      title: "Status",
      dataIndex: "Status",
      render: (value) => <Text>{value ?? "-"}</Text>,
    },
  ];

  const dataSource = data.map((item, index) => ({
    ...item,
    key: index + 1,
  }));

  useEffect(() => {
    getAllTableData();
  }, []);
  return (
    <Flex gap={20} vertical className={Style.dnd_container}>
      <AppHeader title={"Reallocate Action List"} />

      <Flex vertical gap={20}>
        {isLoading ? (
          <ApplicationLoader />
        ) : (
          <Table
            bordered
            virtual
            className={Style.customTable}
            columns={columns}
            dataSource={dataSource}
            scroll={{ x: 2200, y: 300 }}
            pagination={{ pageSize: 10, showSizeChanger: false }}
          />
        )}
      </Flex>
    </Flex>
  );
}

@import url("../../../index.css");

$light-blue: #e4f8f9;
$blue: #becfff;

.box_container {
  padding: 1rem;
  background-color: #e4f8f9;
  border-radius: 10px;
}
.title {
  margin: 0;
  font-size: 1.2rem !important;
  font-weight: 400 !important;
  font-family: "Kanit", "Courier New", Courier, monospace;
  color: #0f2050;
}

.chart_container {
  background-color: white;
  border: 1px solid #113c9b;
  border-radius: 10px;
  box-shadow: 5px 5px 5px 0px #00000040;
  padding: 0.3rem;
  .title {
    margin: 0 0.5rem;
    font-size: 1rem !important;
    font-weight: 400 !important;
    font-family: "Kanit", "Courier New", Courier, monospace;
  }
}

.table_container {
  background-color: $light-blue;
  padding: 1rem;
  border-radius: 10px;

  .chart_container {
    background-color: white;
    border: 1px solid #113c9b;
    border-radius: 10px;
    box-shadow: 5px 5px 5px 0px #00000040;
    padding: 0.3rem;
    .title {
      margin: 0 0.5rem;
      font-size: 1rem !important;
      font-weight: 400 !important;
      font-family: "Kanit", "Courier New", Courier, monospace;
    }
  }
  .title {
    color: #0f2050;
  }
  .download_button {
    background-color: var(--dark-blue);
    padding: 0.1rem 0.5rem;
    border-radius: 8px;
    cursor: pointer;
    img {
      width: 18px;
    }
  }

  :global(.ant-segmented) {
    padding: 0.2rem 0.2rem;
    background-color: #dde8ff;
    border-radius: 50px !important;
    box-shadow: 5px 4px 4px 0px #00000040;

    :global(.ant-segmented-item) {
      :global(.ant-segmented-item-label) {
        color: var(--dark-blue);
        font-weight: 400;
        font-family: "Kanit", "Courier New", Courier, monospace;
        background-color: transparent;
      }
      &::after {
        background-color: transparent !important;
        display: none;
      }
    }
    :global(.ant-segmented-item-selected) {
      background-color: var(--dark-blue) !important;
      color: white !important;
      box-shadow: none;
      border-radius: 50px;
      :global(.ant-segmented-item-label) {
        color: white !important;
      }
    }
    :global(.ant-segmented-thumb) {
      background-color: #dde8ff;
      border-radius: 50px;
    }
  }
}

// Responsive view
@media only screen and (max-width: 768px) {
  .title {
    font-size: 14px !important;
  }
  .chart_container {
    .title {
      font-size: 13px !important;
    }
  }
  .table_container {
    .title {
      font-size: 14px;
    }
    :global(.ant-segmented) {
      padding: 0;
      :global(.ant-segmented-item) {
        :global(.ant-segmented-item-label) {
          font-size: 10px;
        }
      }

      :global(.ant-segmented-item-selected) {
        background-color: var(--dark-blue) !important;
        color: white !important;
        box-shadow: none;
        border-radius: 50px;
        :global(.ant-segmented-item-label) {
          color: white !important;
        }
      }
    }
    .chart_container {
      .title {
        font-size: 13px !important;
      }
    }
  }
}

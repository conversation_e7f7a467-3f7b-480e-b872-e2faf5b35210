import pandas as pd
import numpy as np
from datetime import datetime
from ..queries import insert_data_in_raw_table
from ..utils import *
import threading
from ..sp import execute_post_processing_procedures

def run_post_processing_in_background(bank_id):
    """
    Run post-processing procedures in a background thread
    """
    try:
        print(f"Starting background post-processing for bank_id: {bank_id}")
        execute_post_processing_procedures(bank_id)
        print(f"Background post-processing initiated for bank_id: {bank_id}")
    except Exception as e:
        print(f"Error starting background post-processing for bank_id {bank_id}: {str(e)}")


def process_rajapur_raw_data(df1):
    db_operation_success = False
    db_error_message = None
    df = pd.DataFrame()
    df1.columns = df1.columns.str.strip().str.replace(r'\s+', ' ', regex=True)
    frequency_mapping = {
        "fortnightly": 15,
        "fornightly": 15,  # Common typo
        "fortnight": 15,
        "monthly": 30,
        "weekly": 7,
        "daily": 1,
        "quarterly": 90,
        "bi-monthly": 60,
        "bimonthly": 60,
        'm':30
    }
    df['customerid'] = df1['CUST_CODE']
    df['disbursementid'] = df1['DisbursementID'].astype(str).str.lstrip('0')
    df['loantype'] = df1['Scheme_Description']
    df['customername'] = df1['Account_Name'].astype(str).str.upper().str.strip()
    df['disbursementamount'] = df1['Disbursements_AMOUNT']
    df['rateofinterest'] = df1['Interest_rate_applied']
    df['emiamount'] = df1['installment_EMI_amount']
    df['paymentfrequency'] = df1['PaymentFreequency'].astype(str).str.lower().str.strip()
    df['paymentfrequency'] = df['paymentfrequency'].map(frequency_mapping)
    df['numberofdayspastdue'] = df1['NumberOfDaysPastDue']
    df['mobileno'] = df1['MOBILE_NO']
    df['inststartdate']=pd.to_datetime(df1['INSTSTARTDATE'], errors='coerce')
    # df['collectionofficerid'] = df1['CollectionOfficerID']
    # df['collectionofficername'] = df1['CollectionOfficerName']
    df['branchname'] = df1['Branch_Name'].astype(str).str.upper().str.strip()
    df['overdueamount'] = df1['OverdueAmount']
    df['branchcode'] = df1['Branch_Code']
    # df['applicanttype'] = df1['ApplicantType']
    df['totaloutstanding'] = pd.to_numeric(df1['Total_Outstanding'], errors='coerce').abs()
    df['principlecollected'] = df1['princple_cr']
    # df['collectedamount'] = df1['TOTAL_AMT_P_I']
    # df['collectiondate'] = pd.to_datetime(df1['CollectionDate'], errors='coerce')
    df['interestcollected'] = df1['int_cr'] 
    df['originaldisbursementid'] = df1['DisbursementID'].astype(str).str.strip()   
    # Standard fields
    df['bankmstid'] = 29
    df['inserted_date'] = datetime.now().date()

    
    # Database operation
    # df.to_csv("rajapur.csv", index=False)
    try:
        result = insert_data_in_raw_table(df, bank_id=29)
        db_operation_success = result['success']
        if not result['success']:
            db_error_message = result['message']
    except Exception as e:
        db_operation_success = False
        db_error_message = str(e)
        print(f"Database update failed for bssbihar data: {e}")
    
    # # ========== START BACKGROUND POST-PROCESSING ==========
    if db_operation_success:
        # Start post-processing in background thread
        bank_id = 29
        background_thread = threading.Thread(
            target=run_post_processing_in_background,
            args=(bank_id,),
            daemon=True
        )
        background_thread.start()
        print(f"Post-processing started in background thread for bank_id: {bank_id}")
    else:
        print(f"Skipping post-processing due to database operation failure: {db_error_message}")
    
    return {
        "Total records processed": len(df),
        "New records inserted": result.get('inserted_count', 0) if db_operation_success else 0,
        "Duplicate records found": result.get('duplicate_count', 0) if db_operation_success else 0,
        "db_operation_success": db_operation_success,
        "db_error_message": db_error_message,
        "message": result.get('message', '') if db_operation_success else db_error_message,
    }
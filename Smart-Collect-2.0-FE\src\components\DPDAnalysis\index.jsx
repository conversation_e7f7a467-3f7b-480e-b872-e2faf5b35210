import { Flex } from "antd";
import AppHeader from "../AppHeader";
import React from "react";
import TabsComponent from "../TabsComponent";
import { BucketMovement } from "./BucketMovement";
import { DefaulterCustomers } from "./DefaulterCustomers";
import { Snapshot } from "./Snapshot";

export default function DPDAnalysis() {
  const onChange = (key) => {};
  const items = [
    //   {
    //      key: 'analysis',
    //      label: 'Analysis',
    //      children: <Flex gap={20} vertical>
    //        <Analysis analysisData={analysisData}/>
    //      </Flex>,
    //   },
    {
      key: "snapshot",
      label: "Snapshot",
      children: (
        <Flex gap={20} vertical>
          <Snapshot />
        </Flex>
      ),
    },
    {
      key: "bucket",
      label: "Bucket movement",
      children: (
        <Flex gap={20} vertical>
          <BucketMovement />
        </Flex>
      ),
    },
    {
      key: "customers",
      label: "1st Time Defaulter Customers",
      children: (
        <Flex gap={20} vertical>
          <DefaulterCustomers />
        </Flex>
      ),
    },
  ];

  return (
    <Flex vertical gap={20} style={{ width: "100%", overflowX: "hidden" }}>
      <AppHeader title={"DPD Analysis"} />
      <TabsComponent items={items} onChange={onChange} />
    </Flex>
  );
}

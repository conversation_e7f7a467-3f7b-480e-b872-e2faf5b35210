import pandas as pd
import numpy as np
from datetime import datetime
from ..queries import insert_data_in_raw_table
from ..utils import *

def process_smartfair_raw_data(df1):
    db_operation_success = False
    db_error_message = None
    df = pd.DataFrame()
    frequency_mapping = {
        "fortnightly": 15,
        "fornightly": 15,  # Common typo
        "fortnight": 15,
        "monthly": 30,
        "weekly": 7,
        "daily": 1,
        "quarterly": 90,
        "bi-monthly": 60,
        "bimonthly": 60,
        'm':30
    }
    df1.columns = df1.columns.str.strip().str.replace(r'\s+', ' ', regex=True)
    df['customerid'] = df1['CustomerID']
    df['disbursementid'] = df1['DisbursementID'].astype(str).str.lstrip('0')
    df['loantype'] = df1['LoanType']
    df['customername'] = df1['CustomerName'].astype(str).str.upper().str.strip()
    df['disbursementamount'] = df1['DisbursementAmount']
    df['rateofinterest'] = df1['Rateofinterest']
    df['repaymenttenure'] = df1['RepaymentTenure']
    df['emiamount'] = df1['EMIAmount']
    df['paymentfrequency'] = df1['PaymentFreequency'].astype(str).str.lower().str.strip()
    df['paymentfrequency'] = df['paymentfrequency'].map(frequency_mapping)
    df['numberofdayspastdue'] = df1['NumberOfDaysPastDue']
    
    # Update NumberOfDaysPastDue values: if 365+ (string) or numeric >= 365, set to 366
    def update_days_past_due(x):
        if pd.isna(x):
            return x
        # Convert to string to handle both string and numeric values
        str_x = str(x).strip()
        # Check if it's "365+" or if it's a numeric value > 365
        if str_x == "365+" or (str_x.replace('.', '', 1).isdigit() and float(str_x) > 365):
            return 366
        return x
    
    df['numberofdayspastdue'] = df['numberofdayspastdue'].apply(update_days_past_due)
    df['mobileno'] = df1['MobileNo.']
    df['inststartdate']=df1['InstStartDate']
    df['collectionofficerid'] = df1['CollectionOfficerID']
    df['collectionofficername'] = df1['CollectionOfficerName']
    df['branchname'] = "HO_ALL"
    df['branchcode'] = "001"
    df['applicanttype'] = df1['ApplicantType']
    df['totaloutstanding'] = df1['Total Outstanding']
    df['principlecollected'] = df1['PrincipleCollected']
    df['interestcollected'] = df1['InterestCollected'] 
    df['inststartdate'] = df1['InstStartDate']
    df['overdueamount'] = df1['OverdueAmount']
    df['collectedamount'] = df1['CollectedAmount']
    df['collectiondate'] = df1['CollectionDate']
    df['pos'] = df1['POS']
    df['originaldisbursementid'] = df1['DisbursementID'].astype(str).str.strip()   
    # Standard fields
    df['bankmstid'] = 34
    df['inserted_date'] = datetime.now().date()
    
    # Database operation
    # df.to_csv("creditfair.csv", index=False)
    try:
        result = insert_data_in_raw_table(df, bank_id=34)
        db_operation_success = result['success']
        if not result['success']:
            db_error_message = result['message']
    except Exception as e:
        db_operation_success = False
        db_error_message = str(e)
        print(f"Database update failed for bssbihar data: {e}")
    
    return {
        "Total records processed": len(df),
        "New records inserted": result.get('inserted_count', 0) if db_operation_success else 0,
        "Duplicate records found": result.get('duplicate_count', 0) if db_operation_success else 0,
        "db_operation_success": db_operation_success,
        "db_error_message": db_error_message,
        "message": result.get('message', '') if db_operation_success else db_error_message,
    }

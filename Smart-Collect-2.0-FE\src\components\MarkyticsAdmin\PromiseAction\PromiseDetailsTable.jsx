import { Button, Flex, message, Table, Typography } from "antd";
import React, { useEffect, useState } from "react";
import Style from "../_global.module.scss";
import { formatAmount, handleDateFormatter } from "../../../constant";
import { AXIOS } from "../../../apis/ho-Instance";
import PropTypes from "prop-types";
import { PromiseUpdateModal } from "../../Modals/PromiseUpdateModal";
import { EditOutlined } from "@ant-design/icons";

const { Text } = Typography;

export default function PromiseDetailsTable({ data }) {
  const [loading, setLoading] = useState(false);
  const [tableData, setTableData] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [showPromiseDelete, setShowPromiseDelete] = useState(false);
  const [selectedUpdateDetails, setSelectedUpdateDetails] = useState({
    id: null,
    date: "",
  });

  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  // Handle promise deletion
  const handleDeletePromise = async () => {
    setLoading(true);
    try {
      const res = await AXIOS.post(
        "delete-disbursement/",
        {
          response_ids: selectedRowKeys,
        },
        {
          headers: { "Content-Type": "application/json" },
          validateStatus: () => true,
        }
      );
      console.log(res);
      if (res.status === 200) {
        message.success(res?.data?.message || "Response deleted successfully");
      } else {
        message.warning(
          res.data?.message || "Failed to delete responses. Please try again."
        );
      }
    } catch (error) {
      console.warn("Error in Promise delete action", error?.message);
      message.info("Promises not deleted, Please try again");
    } finally {
      setLoading(false);
      setSelectedRowKeys([]);
    }
  };

  const rowSelection = { selectedRowKeys, onChange: onSelectChange };

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      render: (_, data) => <Text>{data?.key}.</Text>,
    },
    {
      title: "Response Id",
      dataIndex: "response_id",
      render: (value) => <Text>{value !== null ? value : "-"}</Text>,
    },
    {
      title: "Customer Name",
      dataIndex: "customer_name",
      render: (value) => <Text>{value ?? "-"}</Text>,
    },
    {
      title: "Disbursement Id",
      dataIndex: "disbursement_id",
      render: (value) => <Text>{value !== null ? value : "-"}</Text>,
    },
    {
      title: "Status",
      dataIndex: "status",
      render: (value) => <Text>{value ?? "-"}</Text>,
    },
    {
      title: "Amount",
      dataIndex: "amount",
      render: (value) => <Text>{formatAmount(value ?? 0)}</Text>,
    },
    {
      title: "Response Date",
      dataIndex: "response_date_time",
      render: (value) => (
        <Text>{value ? handleDateFormatter(value) : "--"}</Text>
      ),
    },
    {
      title: "Promise Date",
      dataIndex: "promise_date_time",
      render: (value, data) => {
        return value ? (
          <Flex justify="center" gap={5}>
            <Text>{handleDateFormatter(value)}</Text>
            <EditOutlined
              onClick={() => {
                setSelectedUpdateDetails({
                  id: data?.response_id,
                  date: data?.promise_date_time,
                });
                setShowPromiseDelete(true);
              }}
            />
          </Flex>
        ) : (
          "--"
        );
      },
    },
    {
      title: "Wrong Number",
      dataIndex: "wrong_number",
      render: (value) => <Text>{value ? "True" : "False"}</Text>,
    },
    {
      title: "dispute",
      dataIndex: "Dispute",
      render: (value) => <Text>{value ?? "--"}</Text>,
    },
    {
      title: "Mode of Payment",
      dataIndex: "mode_of_payment",
      render: (value) => (
        <Text>{value === null || value === "" ? "--" : value}</Text>
      ),
    },
  ];
  const dataSource = Array.isArray(tableData)
    ? tableData.map((data, i) => ({ key: i + 1, ...data }))
    : [];

  // Set te data
  useEffect(() => {
    setTableData(data);
  }, [data]);

  return (
    <Flex vertical gap={10}>
      <Flex justify="space-between" align="center">
        <Text className={Style.text} style={{ fontSize: "1rem" }}>
          Customer Details:
        </Text>
        {selectedRowKeys.length > 0 && (
          <Button
            variant="outlined"
            color="danger"
            onClick={handleDeletePromise}
            loading={loading}
          >
            Delete Promise
          </Button>
        )}
      </Flex>

      {/* Table */}
      <Table
        rowSelection={rowSelection}
        rowKey={"response_id"}
        bordered
        virtual
        className={Style.customTable}
        columns={columns}
        dataSource={dataSource}
        scroll={{
          x: 2000,
          y: 220,
        }}
        pagination={{
          pageSize: 10,
          showSizeChanger: false,
        }}
      />

      <PromiseUpdateModal
        modalStatus={showPromiseDelete}
        setModalStatus={setShowPromiseDelete}
        data={selectedUpdateDetails}
      />
    </Flex>
  );
}

PromiseDetailsTable.propTypes = {
  data: PropTypes.oneOfType([PropTypes.array, PropTypes.any]),
};

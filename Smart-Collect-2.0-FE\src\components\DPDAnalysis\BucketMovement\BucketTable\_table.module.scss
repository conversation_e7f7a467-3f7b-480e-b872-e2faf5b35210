@import url("../../../../index.css");

$row-bottom: #e5e3e3;
$bottom-color: #949494;
$column-blue: #c5eddf;
$column-yellow: #f2ecb8;
$light-blue: #e4f8f9;
$blue: #becfff;

.customTable {
  border-radius: 20px;

  :global(.ant-table.ant-table-bordered) {
    :global(.ant-table-container) {
      padding: 1rem;
      :global(.ant-table-content) {
        // Table header row and column styling
        :global(table > thead > tr) {
          // Second child (header row styling)
          &:nth-child(2) {
            th {
              color: #8495b4;
              text-align: center;
              &:last-child {
                color: #07223d;
                border-inline-end: 0;
              }
            }
          }
          // First child
          &:nth-child(1) {
            th {
              &:last-child {
                border-inline-end: 0;
              }
            }
          }
        }

        // Table header & body row border styles
        :global(table > thead > tr > th),
        :global(table > tbody > tr > td),
        :global(table > tfoot > tr > td) {
          background-color: white;
          border-inline-end: 1px solid $bottom-color;
          border-bottom: 1px solid $bottom-color !important;
          font-family: "Kanit", "Courier New", Courier, monospace;
          font-weight: 400;
        }

        // Body row column styling
        :global(table > tbody > tr) {
          // Body last rows td
          &:last-child {
            td {
              border-bottom: none !important;
            }
          }
          // First & Last Rows
          &:first-child,
          &:last-child {
            td {
              text-align: center;

              // First column
              &:first-child {
                text-align: start;
              }

              //Last Column
              &:last-child {
                border-inline-end: 0;
                :global(.ant-typography) {
                  color: #07223d;
                }
              }
              // Middle Columns -> Red
              &:not(:first-child):not(:last-child) {
                :global(.ant-typography) {
                  padding: 0.5rem 1.5rem;
                  border-radius: 5px;
                  background-color: $column-blue !important;
                }
              }
            }
          }

          // All other rows (except first & last)
          &:not(:first-child):not(:last-child) {
            td {
              text-align: center;

              // First column
              &:first-child {
                text-align: start;
              }
              //Last Column
              &:last-child {
                border-inline-end: 0;
                :global(.ant-typography) {
                  color: #07223d;
                }
              }
              // Middle Columns -> Green
              &:not(:first-child):not(:last-child) {
                :global(.ant-typography) {
                  padding: 0.5rem 1.5rem;
                  border-radius: 5px;
                  background-color: $column-yellow !important;
                }
              }
            }
          }
        }

        // Foot
        :global(table > tfoot) {
          tr {
            box-shadow: 0px 4px 4px 0px #0000001a inset;
            background-color: #e4f8f9;

            td {
              border-inline-end: 0;
              border-bottom: none !important;
              background-color: transparent;

              &:not(:first-child) {
                text-align: center;
              }
            }
          }
        }
      }

      // Styling all table cells typography
      :global(.ant-table-cell) {
        :global(.ant-typography) {
          color: #305496;
          font-weight: 600;
          word-break: normal;
        }
      }
    }
  }
}

.title_container {
  .title {
    margin: 0;
  }

  :global(.ant-segmented) {
    padding: 0.2rem 0.2rem;

    background-color: var(--dark-blue);
    border-radius: 50px;

    :global(.ant-segmented-item) {
      :global(.ant-segmented-item-label) {
        color: #ffffff;
        font-weight: 400;
        font-family: "Kanit", "Courier New", Courier, monospace;
        background-color: transparent;
      }
      &::after {
        background-color: transparent !important;
        display: none;
      }
    }

    :global(.ant-segmented-item-selected) {
      background-color: $light-blue !important;
      color: var(--dark-blue) !important;
      box-shadow: none;
      border-radius: 50px;
      :global(.ant-segmented-item-label) {
        color: var(--dark-blue) !important;
      }
    }
    :global(.ant-segmented-thumb) {
      background-color: $light-blue;
      border-radius: 50px;
    }
  }
}

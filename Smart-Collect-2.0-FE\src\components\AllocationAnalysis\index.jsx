import React from 'react';
import { Flex, Tabs} from 'antd';
import AppHeader from '../AppHeader';
import Style from './_allocation-analysis.module.scss';
import { EMITable } from './EMITable';
import { PromiseTable } from './PromiseTable';

export default function AllocationAnalysis() {
  const items = [
    {
      key: 'promise',
      label: 'Promise',
      children: <PromiseTable/>,
    },
    {
      key: 'emi-collection',
      label: 'EMI Collection',
      children: <EMITable/>,
    },
  ];
  return (
    <Flex vertical gap={20} className={Style.container}>
      <AppHeader title={"Allocation Analysis"} />
      <Tabs defaultActiveKey="promise" items={items} className={Style.tabs}/>
    </Flex>
  );
}

import { Avatar, Flex, List } from 'antd';
import AVATAR_IMG from "../../../../../../assets/Images/mage_robot.svg";
import { CloseOutlined } from '@ant-design/icons';
import React from 'react';
import Style from "./_style.module.scss";
import PropTypes from 'prop-types';


export function TemplateScripting({scriptData, visible, setVisible, showCross=true}) {
  return (
    visible && 
    <div className={Style.script}>
        {showCross && <Flex justify='end'>
          <i style={{fontSize:'10px', cursor:'pointer'}} ><CloseOutlined onClick={()=> setVisible(false)}/></i>
        </Flex>}
        <List
            className={Style.list}
            itemLayout="horizontal"
            dataSource={scriptData}
            renderItem={(item, index) => (
            <List.Item>
                <List.Item.Meta
                avatar={<Avatar size={25} src={AVATAR_IMG} className={Style.avatar}/>}
                title={<div style={{ whiteSpace: "pre-line" }} dangerouslySetInnerHTML={{ __html: item }} />}
                />
            </List.Item>
            )}
        />
    </div>

  )
}

TemplateScripting.propTypes={
  scriptData: PropTypes.array,
  visible: PropTypes.bool,
  setVisible: PropTypes.func,
  showCross: PropTypes.bool
}
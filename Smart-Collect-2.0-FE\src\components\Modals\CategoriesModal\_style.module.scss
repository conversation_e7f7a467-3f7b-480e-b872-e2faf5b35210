@import url("../../../index.css");

$light-blue:#E3F5F6;

.categories_modal{
    :global(.ant-modal-content){
        background-color: var(--dark-blue);
        color: white;
        padding: 1rem;

        :global(.ant-modal-header){
            background-color: var(--dark-blue);

            :global(.ant-modal-title){
               color: white; 
               text-align: center;
               font-size: 18px;
               font-family: 'Kanit','Courier New', Courier, monospace;
               font-weight: normal;
            }
        }

        :global(.ant-modal-close){
            color: white;
            :global(.ant-modal-close-x){
              font-size: 12px;  
            }
        }
        .custom_input{
            padding: 0.3rem;
            font-family: 'Kanit','Courier New', Courier, monospace;
        }
    }

    .next_button{
        background-color: $light-blue;
        font-weight: 500;
        border: none;
        padding: 1rem 3rem;
        font-family: 'Kanit','Courier New', Courier, monospace;
        &:not(:disabled){
           &:hover{
                background-color: $light-blue ;
                border: none;
                outline: none;
                color: black;
            }  
        }
       
        &:disabled{
            background:  #d9d9d9;
            color: gray;
            &:hover{
                background-color: #d9d9d9;
                color: gray;
            }
        }
    }

    .custom_select{
        margin-bottom: 2rem;
       
        :global(.ant-select-selector){
            border: none;
        }
        
        :global(.ant-select-selection-item){
            font-weight: 700;
        }

        :global(.ant-select-selection-placeholder){
            font-weight: 500;
        }
    }
}

@media only screen and (max-width:768px){
    .categories_modal{
        :global(.ant-modal-content){
            padding: 1rem;
    
            :global(.ant-modal-header){    
                :global(.ant-modal-title){
                   font-size: 13px;
                }
            }
            :global(.ant-modal-close){
                :global(.ant-modal-close-x){
                  font-size: 10px;  
                }
            }
            .custom_input{
              font-size: 12px;
              padding: 0.3rem;
            }
        }

        .custom_select{
            height: 28px;
            :global(.ant-select-selection-item),
            :global(.ant-select-selection-placeholder){
                font-size: 12px;
            }
        }

        .next_button{
            padding: 0.4rem 2rem;  
            font-size: 12px;         
        }
    }
}
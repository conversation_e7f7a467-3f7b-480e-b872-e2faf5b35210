import {
  createContext,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import PropTypes from "prop-types";
import { getTourSteps } from "./Tours/getTourSteps";
import { MenuTour } from "./Tours/MenuTour";
import { ProfileTour } from "./Tours/ProfileTour";
import { LogoutTour } from "./Tours/LogoutTour";
import { DateWiseCustomerEngagementTour } from "./Tours/DateWiseCustomerEngagementTour";
import { CustomerEngagementTour } from "./Tours/CustomerEngagementTour";
import { CustomerDetailsTour } from "./Tours/CustomerDetailsTour";
import { CustomerDetailsGraphTour } from "./Tours/CustomerDetailsGraphTour";
import { PayDetailsTour } from "./Tours/PayDetailsTour";

const TourContext = createContext({
  menuRef: null,
  profileRef: null,
  logoutRef: null,
  dateWiseCustomerEngagementRef: null,
  customerEngagementRef: null,
  customerDetailsRef: null,
  customerDetailsGraphRef: null,
  payDetailsRef: null,
  open: false,
  setOpen: () => {},
  steps: [],
  currentStep: 0,
  setCurrentStep: () => {},
  handleNext: () => {},
  handleSkip: () => {},
});

export const TourProvider = ({ children }) => {
  const storedUser = localStorage.getItem("user");
  const parsedUser = storedUser ? JSON.parse(storedUser) : {};
  const { is_first } = parsedUser;

  const menuRef = useRef(null);
  const profileRef = useRef(null);
  const logoutRef = useRef(null);
  const dateWiseCustomerEngagementRef = useRef(null);
  const customerDetailsRef = useRef(null);
  const customerEngagementRef = useRef(null);
  const customerDetailsGraphRef = useRef(null);
  const payDetailsRef = useRef(null);

  const [open, setOpen] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep((prev) => prev + 1);
    } else {
      setOpen(false);
    }
  };

  const handleSkip = () => {
    setOpen(false);
  };

  const steps = useMemo(
    () =>
      getTourSteps({
        menuRef,
        profileRef,
        logoutRef,
        dateWiseCustomerEngagementRef,
        customerEngagementRef,
        customerDetailsRef,
        customerDetailsGraphRef,
        payDetailsRef,
        components: {
          MenuTour: (
            <MenuTour handleNext={handleNext} handleSkip={handleSkip} />
          ),
          ProfileTour: (
            <ProfileTour handleNext={handleNext} handleSkip={handleSkip} />
          ),
          LogoutTour: (
            <LogoutTour handleNext={handleNext} handleSkip={handleSkip} />
          ),
          DateWiseCustomerEngagementTour: (
            <DateWiseCustomerEngagementTour
              handleNext={handleNext}
              handleSkip={handleSkip}
            />
          ),
          CustomerEngagementTour: (
            <CustomerEngagementTour
              handleNext={handleNext}
              handleSkip={handleSkip}
            />
          ),
          CustomerDetailsTour: (
            <CustomerDetailsTour
              handleNext={handleNext}
              handleSkip={handleSkip}
            />
          ),
          CustomerDetailsGraphTour: (
            <CustomerDetailsGraphTour
              handleNext={handleNext}
              handleSkip={handleSkip}
            />
          ),
          PayDetailsTour: (
            <PayDetailsTour handleNext={handleNext} handleSkip={handleSkip} />
          ),
        },
      }),
    []
  );

  useEffect(() => {
    if (!open) return;

    const timer = setTimeout(() => {
      handleNext();
    }, 10000);

    return () => clearTimeout(timer);
  }, [currentStep, open]);

  // Start the tour automatically on mount
  useEffect(() => {
    if (is_first === true) setOpen(true);
    else setOpen(false);
  }, []);

  const value = useMemo(
    () => ({
      menuRef,
      profileRef,
      logoutRef,
      dateWiseCustomerEngagementRef,
      customerEngagementRef,
      customerDetailsRef,
      customerDetailsGraphRef,
      payDetailsRef,
      open,
      steps,
      currentStep,
      setOpen,
      setCurrentStep,
      handleNext,
      handleSkip,
    }),
    [
      menuRef,
      profileRef,
      logoutRef,
      dateWiseCustomerEngagementRef,
      customerEngagementRef,
      customerDetailsRef,
      customerDetailsGraphRef,
      payDetailsRef,
      open,
      steps,
      currentStep,
      setOpen,
      setCurrentStep,
      handleNext,
      handleSkip,
    ]
  );

  return <TourContext.Provider value={value}>{children}</TourContext.Provider>;
};
export const useTour = () => useContext(TourContext);

TourProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

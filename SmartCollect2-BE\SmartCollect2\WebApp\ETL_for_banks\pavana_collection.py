import pandas as pd
import numpy as np
from sqlalchemy import create_engine,update,MetaData, Table, text
from datetime import datetime, timedelta
import os


DATABASE = "SmartCollect2"
USERNAME = "postgres"
PASSWORD = "Markytics12345"
HOST = "*************"
PORT = 5432

# Create database engine
engine = create_engine(f"postgresql://{USERNAME}:{PASSWORD}@{HOST}:{PORT}/{DATABASE}")

class DatabaseManager:
    """Database manager class to handle single engine instance and connections"""
    
    def __init__(self):
        self.engine = None
    
    def initialize_engine(self):
        """Initialize the database engine once"""
        if self.engine is None:
            connection_string = f"postgresql://{os.environ.get('DATABASE_USER')}:{os.environ.get('DATABASE_PASSWORD')}@{os.environ.get('DATABASE_HOST')}:{os.environ.get('DATABASE_PORT')}/{os.environ.get('DATABASE_NAME')}"
            self.engine = create_engine(connection_string)
            print("Database engine initialized successfully!")
        return self.engine
    
    def get_engine(self):
        """Get the engine instance, initialize if not already done"""
        if self.engine is None:
            self.initialize_engine()
        return self.engine


def get_branch_codes_from_db(bank_mst_id, branch_names):
    """
    Query function to get branch codes from database based on BankMstID and BranchName
    
    Args:
        bank_mst_id: Bank Master ID (e.g., 21)
        branch_names: List of branch names to lookup
    
    Returns:
        Dictionary mapping branch names to branch codes
    """
    db_manager = DatabaseManager()
    engine = db_manager.get_engine()
    if not branch_names:
        return {}
    
    try:
        # Use connection context manager for proper parameter handling
        with engine.connect() as connection:
            # For SQLAlchemy, use named parameters instead of positional
            query = """
            SELECT bm."BranchName", bm."BranchMstID"
            FROM "BranchMst" bm 
            WHERE bm."BranchName" = ANY(%(branch_names)s)
            AND bm."BankMstID" = %(bank_mst_id)s
            """
            
            # Execute query with named parameters
            result_df = pd.read_sql_query(
                query, 
                connection, 
                params={
                    'branch_names': list(branch_names),
                    'bank_mst_id': bank_mst_id
                }
            )
            
        # Create mapping dictionary: branch_name -> branch_code
        branch_mapping = dict(zip(result_df['BranchName'], result_df['BranchMstID']))
        print(f"Successfully mapped {len(branch_mapping)} branches from database")
        return branch_mapping
        
    except Exception as e:
        print(f"Error querying branch codes: {e}")
        print(f"Attempted to query {len(branch_names)} branch names: {list(branch_names)[:5]}...")  # Show first 5 for debugging
        return {}

def save_to_database(df, table_name="CollectionFile"):
    """
    Save processed DataFrame to database
    """
    if df.empty:
        print("No data to save to database")
        return False
    df1 = pd.DataFrame()
    df1['DisbursementID'] = df['disbursementid']
    df1['CustomerId'] = df['CustomerId']
    df1['BankMstID'] = df['BankMstID']
    df1['BranchMstID'] = df['BranchMstID']
    df1['CreatedDate'] = df['CreatedDate'].dt.strftime('%Y-%m-%d')
    df1['LoanType'] = df['LoanType']
    df1['CollectionDate'] = df['CollectionDate'].dt.strftime('%Y-%m-%d')
    df1['CollectedAmount'] = df['CollectedAmount']
    df1['BranchName'] = df['branchname']
    df1['PrincipleCollected'] = None
    df1['InterestCollected'] = None
    df1['InstStartDate'] = None
    df1['BMID'] = None
    df1['POS'] = None
    df1['ExtraColumn1'] = None
    df1['ExtraColumn2'] = None
    df1['ExtraColumn3'] = None
    df1['ExtraColumn4'] = None
    df1['TransactionNumber'] = None
    df1['inserted_date'] = datetime.now().date() 

    # print("\n5. Saving to CSV...")
    # df1.to_csv("processed_data.csv",index=False)
    
    try:
        db_manager = DatabaseManager()
        engine = db_manager.get_engine()
        
        # Save to database
        df1.to_sql(table_name, engine, if_exists='append', index=False)
        print(f"Successfully saved {len(df)} records to table '{table_name}'")
        return True
        
    except Exception as e:
        print(f"Error saving to database: {e}")
        return False

def process_collection_data(df1):
    """
    Process collection data and filter records with CollectedAmount > 0
    """
    # First, filter out records where Recovery Amt is zero or negative
    df1_filtered = df1[pd.to_numeric(df1['Recovery Amt'], errors='coerce') > 0].copy()
    
    if df1_filtered.empty:
        print("No records found with Recovery Amount greater than zero")
        return pd.DataFrame()
    
    print(f"Processing {len(df1_filtered)} records with Recovery Amount > 0 (filtered from {len(df1)} total records)")
    
    df = pd.DataFrame()
    df['CustomerId'] = df1_filtered['Main Cust No'].astype(str)
    
    # Create DisbursementID by combining BranchCode + LoanType + AccountNo
    df[['branchcode', 'branchname']] = df1_filtered["Br Name"].str.split('-', n=1, expand=True)
    df['branchname'] = df['branchname'].str.upper().str.strip()
    df['branchcode'] = df['branchcode'].str.strip()

    df['disbursementid'] = (df['branchcode'].astype(str).str.strip() + 
                           df1_filtered['Prd Cd'].astype(str).str.strip() + 
                           df1_filtered['Ac No'].astype(str).str.strip())
    
    # Only include records with CollectedAmount > 0
    df['CollectedAmount'] = pd.to_numeric(df1_filtered['Recovery Amt'], errors='coerce')
    
    # DEBUG: Check the actual date values before conversion
    print("\n=== DATE DEBUGGING ===")
    print("Sample Recovery Date values:")
    print(df1_filtered['Recovery Date'].head(10).tolist())
    print(f"Recovery Date data types: {df1_filtered['Recovery Date'].dtype}")
    print(f"Unique date formats (first 5): {df1_filtered['Recovery Date'].unique()[:5].tolist()}")
    
    # Try different date parsing approaches
    # Approach 1: Let pandas infer the format
    df['CollectionDate'] = pd.to_datetime(df1_filtered['Recovery Date'], errors='coerce')
    
    # Check how many dates were successfully parsed
    successful_dates = df['CollectionDate'].notna().sum()
    print(f"Successfully parsed dates with auto-detection: {successful_dates}/{len(df)}")
    
    # If auto-detection failed, try specific formats
    if successful_dates == 0:
        print("Auto-detection failed. Trying specific formats...")
        
        # Try common date formats
        date_formats = ['%d-%m-%Y', '%d/%m/%Y', '%Y-%m-%d', '%m/%d/%Y', '%d.%m.%Y']
        
        for fmt in date_formats:
            try:
                test_dates = pd.to_datetime(df1_filtered['Recovery Date'], format=fmt, errors='coerce')
                success_count = test_dates.notna().sum()
                print(f"Format '{fmt}': {success_count} successful conversions")
                
                if success_count > 0:
                    df['CollectionDate'] = test_dates
                    break
            except:
                continue
    
    # Calculate CreatedDate as CollectionDate + 1 day
    df['CreatedDate'] = df['CollectionDate'] + pd.Timedelta(days=1)
    
    df['LoanType'] = df1_filtered['Prd Cd']
    df['BankMstID'] = 21
    
    # Get unique branch names for database lookup
    unique_branch_names = df['branchname'].dropna().unique().tolist()
    
    # Get branch mapping from database
    branch_mapping = get_branch_codes_from_db(21, unique_branch_names)
    
    if branch_mapping:
        print(f"Branch mapping retrieved from database: {len(branch_mapping)} branches")
        df['BranchMstID'] = df['branchname'].map(branch_mapping)
    else:
        print("No branch mapping found in database")
        df['BranchMstID'] = None
    
    # DEBUG: Check dates before filtering
    print(f"\nBefore date filtering: {len(df)} records")
    print(f"Records with valid CollectionDate: {df['CollectionDate'].notna().sum()}")
    print(f"Records with NaT CollectionDate: {df['CollectionDate'].isna().sum()}")
    
    # Remove records where dates couldn't be parsed (CollectionDate is NaT)
    df = df.dropna(subset=['CollectionDate'])
    
    print(f"After date filtering: {len(df)} records")
    print(f"Final processed records: {len(df)}")
    return df


def read_excel_file(file_path):
    """
    Read Excel file and return DataFrame
    """
    try:
        df = pd.read_excel(file_path, dtype=str,skiprows=1)
        print(f"Successfully read Excel file: {file_path}")
        print(f"Total rows in file: {len(df)}")
        return df
    except Exception as e:
        print(f"Error reading Excel file {file_path}: {e}")
        return pd.DataFrame()


def save_to_csv(df, output_file_path):
    """
    Save processed DataFrame to CSV file
    """
    if df.empty:
        print("No data to save to CSV")
        return False
    
    try:
        df.to_csv(output_file_path, index=False)
        print(f"Successfully saved {len(df)} records to CSV: {output_file_path}")
        return True
        
    except Exception as e:
        print(f"Error saving to CSV: {e}")
        return False


def main():
    """
    Main function to orchestrate the collection data processing
    """
    print("="*50)
    print("COLLECTION DATA PROCESSING STARTED")
    print("="*50)
    
    # Configuration
    excel_file_path = r"C:\Users\<USER>\Downloads\Pavana\OVERDUEAI_20062025.xlsx"  # Update with your file path
    output_csv_path = "processed_collection_data.csv"  # Output CSV file
    database_table = "CollectionFile"  # Database table name
    
    try:
        # Step 1: Read Excel file
        print("\n1. Reading Excel file...")
        raw_data = read_excel_file(excel_file_path)
        
        if raw_data.empty:
            print("No data found in Excel file. Exiting...")
            return
        
        # Step 2: Process the data
        print("\n2. Processing collection data...")
        processed_data = process_collection_data(raw_data)
        
        if processed_data.empty:
            print("No valid records after processing. Exiting...")
            return
        
        # Step 3: Display summary
        print("\n3. Processing Summary:")
        print(f"   - Total records in file: {len(raw_data)}")
        print(f"   - Records with Recovery Amount > 0: {len(processed_data)}")
        print(f"   - Columns in processed data: {list(processed_data.columns)}")
        
        # Step 4: Preview the data
        print("\n4. Preview of processed data:")
        print(processed_data.head())
        
        # Step 6: Save to database (optional)
        print("\n6. Saving to database...")
        save_to_database(processed_data, database_table)
        
        print("\n" + "="*50)
        print("COLLECTION DATA PROCESSING COMPLETED SUCCESSFULLY!")
        print("="*50)
        
    except Exception as e:
        print(f"\nError in main processing: {e}")
        print("="*50)
        print("COLLECTION DATA PROCESSING FAILED!")
        print("="*50)


# Alternative function to process specific file
def process_collection_file(file_path, output_csv=None, save_to_db=False, table_name="CollectionFile"):
    """
    Process a specific collection file
    
    Args:
        file_path: Path to Excel file
        output_csv: Path for output CSV (optional)
        save_to_db: Whether to save to database
        table_name: Database table name
    
    Returns:
        Processed DataFrame
    """
    print(f"Processing file: {file_path}")
    
    # Read and process data
    raw_data = read_excel_file(file_path)
    if raw_data.empty:
        return pd.DataFrame()
    
    processed_data = process_collection_data(raw_data)
    
    # Save outputs if requested
    if output_csv:
        save_to_csv(processed_data, output_csv)
    
    if save_to_db:
        save_to_database(processed_data, table_name)
    
    return processed_data


if __name__ == "__main__":
    # Run the main processing function
    main()
@import url("../../../../../../index.css");

.script{
   background-color: white;
   padding: 1rem;
   border-radius: 10px;

   .list{   
    :global(.ant-list-item){
        border: none;
        :global(.ant-list-item-meta){
            :global(.ant-list-item-meta-content){
                :global(.ant-list-item-meta-title){
                  font-family: 'Kanit','Courier New', Courier, monospace;
                  font-weight: normal;  
                  color: var(--dark-blue);
                  font-size: small;
                  margin-bottom: 0;
                }
            }
        }
    }
   }
   .avatar{
    padding: 1px 2px;
    background-color: #E3F5F6;
      img{
        object-fit: contain;
      }
   }
}
from django.conf import settings
from rest_framework import serializers
from rest_framework.serializers import ModelSerializer

from Channels.models import Dialer

ASTERISK_DETAILS = settings.ASTERISK_DETAILS


class DialerSerializer(ModelSerializer):
    CustomerName = serializers.Char<PERSON><PERSON>(
        source="CustomerMstID.CustomerName", read_only=True
    )
    DisbursementID = serializers.CharField(
        source="LoanMstID.DisbursementID", read_only=True
    )
    CustomerID = serializers.CharField(
        source="LoanMstID.CustomerInfoID", read_only=True
    )
    LoanType = serializers.CharField(source="LoanMstID.LoanType", read_only=True)
    BranchName = serializers.CharField(source="BranchMstID.BranchName", read_only=True)
    Overdue_Amount = serializers.CharField(
        source="LoanMstID.OverDueAmt", read_only=True
    )
    RecordingURL = serializers.SerializerMethodField("RecordingURLMethod")

    def RecordingURLMethod(self, obj):
        prefix = f"{ASTERISK_DETAILS['GCP_BUCKET_URL']}/{ASTERISK_DETAILS['GCP_BUCKET_NAME']}"
        return f"{prefix.rstrip('/')}/{obj.recordingurl}"

    class Meta:
        model = Dialer
        fields = [
            "DialerID",
            "BankMstID",
            "BranchMstID",
            "LoanMstID",
            "CustomerMstID",
            "Call_ID",
            "DateTime",
            "RecordingURL",
            "CustomerName",
            "BranchName",
            "OverDueAmt",
            "DisbursementID",
            "CustomerInfoID",
            "LoanType",
        ]
        read_only_fields = ["DateTime"]

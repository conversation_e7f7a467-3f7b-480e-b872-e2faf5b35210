import React, { useEffect, useState } from "react";
import AppHeader from "../AppHeader";
import { Flex, message, Select, Table, Typography } from "antd";
import {
  ALL_CACHE_NAMES,
  formatAmount,
  formatDigits,
  handleDownloadTable,
} from "../../constant";
import ApplicationLoader from "../ApplicationLoader";
import { AXIOS } from "../../apis/ho-Instance";
import Style from "./_collection-analysis.module.scss";
import DOWNLOAD_IMG from "../../assets/Images/download.svg";
import {
  getCacheKey,
  getFromCache,
  getPostCacheKey,
  storeToCache,
} from "../../utils/cacheHelper";

const { Text } = Typography;
const CACHE_NAME = ALL_CACHE_NAMES.ANALYSIS_CACHE;

export default function CollectionAnalysis() {
  const [data, setData] = useState([]);
  const [prevData, setPrevData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedReason, setSelectedReason] = useState(undefined);
  const [selectedDates, setSelectedDates] = useState({
    fromDate: new Date().toISOString().split("T")[0],
    toDate: new Date().toISOString().split("T")[0],
  });

  // Get state
  const handleGetState = async () => {
    setIsLoading(true);
    const params = {
      from_date: selectedDates.fromDate,
      to_date: selectedDates.toDate,
    };
    const cacheKey = getCacheKey({
      endpoint: "collection-analysis/",
      params: params,
    });
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setData(cachedData);
      setPrevData(cachedData);
      setIsLoading(false);
    }
    try {
      const response = await AXIOS.get("collection_analysis/", {
        params: params,
      });
      if (
        response.status === 200 &&
        Array.isArray(response.data?.data) &&
        response.data?.data?.length
      ) {
        setData(response.data?.data);
        setPrevData(response.data?.data);
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: response.data?.data,
        });
      } else {
        setData([]);
        setPrevData([]);
      }
    } catch (error) {
      console.log("Error in EMI", error?.message);
      setData([]);
      setPrevData([]);
    } finally {
      setIsLoading(false);
    }
  };

  //Get Region, branch, CO.
  const handleGetOtherColsData = async ({ BUType, BUName }) => {
    setIsLoading(true);
    const body = {
      from_date: selectedDates.fromDate,
      to_date: selectedDates.toDate,
      BUType,
      BUName,
    };
    const cacheKey = getPostCacheKey({
      endpoint: "collection-analysis/",
      body,
    });
    const cachedData = await getFromCache({
      cacheName: CACHE_NAME,
      key: cacheKey,
    });
    if (cachedData) {
      setData(cachedData);
      setPrevData(cachedData);
      setIsLoading(false);
    }
    try {
      const res = await AXIOS.post("collection_analysis/", body);
      if (res.status === 200 && res.data?.length && Array.isArray(res.data)) {
        setData(res.data);
        setPrevData(res.data);
        await storeToCache({
          cacheName: CACHE_NAME,
          key: cacheKey,
          data: res.data,
        });
      } else {
        message.warning(`Sorry, there is not data related to ${BUName}`);
        setData(prevData);
      }
    } catch (error) {
      console.log("Error in Promise table", error?.message);
      message.warning(`No data found for ${BUName}`);
      setData(prevData);
    } finally {
      setIsLoading(false);
    }
  };

  // Change Date
  const handleDateSelection = (value) => {
    setSelectedReason(value);
    if (value === "yesterday") {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      setSelectedDates({
        fromDate: yesterday.toISOString().split("T")[0],
        toDate: yesterday.toISOString().split("T")[0],
      });
    } else if (value === "last_week") {
      const lastWeekDate = new Date();
      const yesterday = new Date();
      lastWeekDate.setDate(lastWeekDate.getDate() - 7);
      setSelectedDates({
        fromDate: yesterday.toISOString().split("T")[0],
        toDate: lastWeekDate.toISOString().split("T")[0],
      });
    }
  };

  // Extract dynamic keys from the first item of the API response (excluding lan & amount)
  const dynamicKeys = data?.length
    ? Object.keys(data[0]).filter(
        (key) =>
          ![
            "lan",
            "amount",
            "total_whatsapp",
            "total_AI_Call",
            "total_IVR",
            "total_Email",
            "total_SMS",
            "total_Dialers",
            "total_Blaster_Call",
          ].includes(key)
      )
    : [];

  // Create dynamic column definitions
  const dynamicColumns = dynamicKeys.map((key) => ({
    title: key,
    dataIndex: key,
    sorter: (a, b) => a[key]?.localeCompare?.(b[key]),
    render: (_, record) => (
      <Text
        className={Style.blueText}
        onClick={() =>
          handleGetOtherColsData({ BUType: key, BUName: record[key] })
        }
      >
        {record[key] || "--"}
      </Text>
    ),
  }));

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      sorter: (a, b) => a.key - b.key,
      render: (_, data) => <Text>{data?.key}.</Text>,
    },
    ...dynamicColumns,
    {
      title: "#Lan",
      dataIndex: "lan",
      sorter: (a, b) => (a.lan || 0) - (b.lan || 0),
      render: (value) => <Text>{value || "--"}</Text>,
    },
    {
      title: "Collected Amount",
      dataIndex: "amount",
      sorter: (a, b) => (a.amount || 0) - (b.amount || 0),
      render: (value) => <Text>Rs. {formatAmount(value || 0)}</Text>,
    },
    {
      title: "Attempted Before Collection",
      children: [
        {
          title: "Whatsapp",
          dataIndex: "total_whatsapp",
          render: (value) => (
            <Text className={Style.lan}>{formatDigits(value || 0)}</Text>
          ),
          sorter: (a, b) => (a.total_whatsapp || 0) - (b.total_whatsapp || 0),
        },
        {
          title: "AI Call",
          dataIndex: "total_AI_Call",
          render: (value) => (
            <Text className={Style.lan}>{formatDigits(value || 0)}</Text>
          ),
          sorter: (a, b) => (a.total_AI_Call || 0) - (b.total_AI_Call || 0),
        },
        {
          title: "IVR",
          dataIndex: "total_IVR",
          render: (value) => (
            <Text className={Style.lan}>{formatDigits(value || 0)}</Text>
          ),
          sorter: (a, b) => (a.total_IVR || 0) - (b.total_IVR || 0),
        },
        {
          title: "Email",
          dataIndex: "total_Email",
          render: (value) => (
            <Text className={Style.lan}>{formatDigits(value || 0)}</Text>
          ),
          sorter: (a, b) => (a.total_Email || 0) - (b.total_Email || 0),
        },
        {
          title: "SMS",
          dataIndex: "total_SMS",
          render: (value) => (
            <Text className={Style.lan}>{formatDigits(value || 0)}</Text>
          ),
          sorter: (a, b) => (a.total_SMS || 0) - (b.total_SMS || 0),
        },
        {
          title: "Dailers",
          dataIndex: "total_Dialers",
          render: (value) => (
            <Text className={Style.lan}>{formatDigits(value || 0)}</Text>
          ),
          sorter: (a, b) => (a.total_Dialers || 0) - (b.total_Dialers || 0),
        },
        {
          title: "Blaster Call",
          dataIndex: "total_Blaster_Call",
          render: (value) => (
            <Text className={Style.lan}>{formatDigits(value || 0)}</Text>
          ),
          sorter: (a, b) =>
            (a.total_Blaster_Call || 0) - (b.total_Blaster_Call || 0),
        },
      ],
    },
  ];

  const dataSource = Array.isArray(data)
    ? data.map((data, i) => ({ key: i + 1, ...data }))
    : [];

  useEffect(() => {
    handleGetState();
  }, [selectedDates]);

  // Handle downloading
  const handleDownload = async () => {
    if (data.length === 0) {
      return message.error("There is no data!");
    } else {
      await handleDownloadTable({
        excelName: "Collection Analysis",
        worksheetName: "Collection-Analysis",
        tableData: data,
      });
    }
  };

  return (
    <Flex vertical gap={20} className={Style.container}>
      <AppHeader title={"Collection Analysis"} />
      <Flex vertical gap={10}>
        <div className={Style.dropdownContainer}>
          <Select
            className={Style.dropdown}
            defaultValue={selectedReason}
            onChange={handleDateSelection}
            placeholder="Select type"
            options={[
              { label: "Yesterday", value: "yesterday" },
              { label: "Last week", value: "last_week" },
            ]}
            allowClear
            onClear={() => {
              setSelectedDates({
                fromDate: new Date().toISOString().split("T")[0],
                toDate: new Date().toISOString().split("T")[0],
              });
            }}
          />
          <button className={Style.download_button} onClick={handleDownload}>
            <img src={DOWNLOAD_IMG} alt="download-button" />
          </button>
        </div>

        {isLoading ? (
          <ApplicationLoader />
        ) : (
          <Table
            bordered
            virtual
            className={Style.custom_table}
            columns={columns}
            dataSource={dataSource}
            scroll={{
              x: 1600,
              y: 460,
            }}
            pagination={{
              showSizeChanger: false,
            }}
          />
        )}
      </Flex>
    </Flex>
  );
}

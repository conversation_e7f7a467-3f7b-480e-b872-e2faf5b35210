import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>icker,
  Divider,
  Flex,
  Input,
  Select,
  Typography,
  message,
} from "antd";
import CROSS_IMG from "../../assets/Images/cross.svg";
import FILTER_IMG from "../../assets/Images/solar_filter-broken.svg";
import ACTIVE_FILTER_IMG from "../../assets/Images/activefilter.svg";
import Style from "./_app-header.module.scss";
import dayjs from "dayjs";
import { AXIOS } from "../../apis/ho-Instance";
import { SMART_COLLECT_MENU_IDS, SUMMARY_FILTERS } from "../../constant";
import PropTypes from "prop-types";
import { useLocation } from "react-router";
import { parse } from "uuid";
const { Text } = Typography;

export default function FilterButton({
  visible,
  setVisible,
  applyFilters,
  ClearFilters,
  selectedDate,
  isDashboardOtherPages,
  pageId = null,
}) {
  const location = useLocation();
  const isSummaryPage = location.pathname.includes("/ho/campaign-summary");
  const [messageApi, contextHolder] = message.useMessage();
  const [loading, setLoading] = useState(false);
  const [filterActive, setFilterActive] = useState(false);
  const [branchList, setBranchList] = useState([]);
  const [filterKeys, setFilterKeys] = useState({
    fromDate: null,
    toDate: null,
    branch: null,
    conclusion: null,
    disbursementId: "",
    customerNames: "",
    loanTypes: "",
  });
  const engagementStartDate = JSON.parse(
    localStorage.getItem("EngagementStartDate")
  );

  // Check if the value is empty
  const isEmpty = (val) =>
    !val ||
    (typeof val === "string" && val.trim() === "") ||
    (Array.isArray(val) && val.length === 0);

  // Normalize input to handle both string and array inputs
  const normalizeInput = (input) => {
    if (Array.isArray(input))
      return input.map((item) => String(item).trim()).filter(Boolean);
    if (typeof input === "string") {
      return input.trim() === ""
        ? []
        : input
            .split(",")
            .map((item) => item.trim())
            .filter(Boolean);
    }
    return [];
  };

  // Handle change filter keys
  const handleFilterKeysChange = ({ type, key }) => {
    setFilterKeys((prev) => ({ ...prev, [type]: key }));
  };

  // Handle apply filters
  const handleApplyFilters = async () => {
    const {
      fromDate,
      toDate,
      branch,
      disbursementId,
      conclusion,
      customerNames,
      loanTypes,
    } = filterKeys;

    const isAllEmpty =
      isEmpty(fromDate) &&
      isEmpty(toDate) &&
      isEmpty(branch) &&
      (pageId !== SMART_COLLECT_MENU_IDS.DASHBOARD
        ? isEmpty(disbursementId)
        : true) &&
      (pageId === SMART_COLLECT_MENU_IDS.AI_CALLS
        ? isEmpty(conclusion)
        : true) &&
      (pageId === "TotalLoans" || pageId === "TotalLoansConnected"
        ? isEmpty(customerNames) && isEmpty(loanTypes) && isEmpty(conclusion)
        : true);

    if (isAllEmpty) {
      message.warning("Please fill at least one filter field before applying.");
      return;
    }

    if ((fromDate && !toDate) || (!fromDate && toDate)) {
      message.error("Please select both 'From Date' and 'To Date'.");
      return;
    }

    const updatedFromDate = fromDate
      ? dayjs(fromDate).format("YYYY-MM-DD")
      : null;
    const updatedToDate = fromDate ? dayjs(toDate).format("YYYY-MM-DD") : null;
    const updateDisbursement = normalizeInput(disbursementId);
    const updateCustomerNames = normalizeInput(customerNames);
    const updateLoanTypes = normalizeInput(loanTypes);

    // ✅ Load previous campaignMstIds from localStorage if available
    let campaignMstIds = [];
    const oldFilters = JSON.parse(
      localStorage.getItem(SUMMARY_FILTERS.summaryFilter)
    );
    if (oldFilters?.campaignMstIds?.length > 0) {
      campaignMstIds = oldFilters.campaignMstIds;
    }

    const formattedDates = {
      fromDate: updatedFromDate,
      toDate: updatedToDate,
      disbursementID: updateDisbursement,
      customerNames: updateCustomerNames,
      loanTypes: updateLoanTypes,
      BranchName: branch,
      conclusion: conclusion,
      campaignMstIds: campaignMstIds, // this is now always taken from localStorage
    };

    localStorage.setItem(
      `${isSummaryPage ? SUMMARY_FILTERS.summaryFilter : "filterDate"}`,
      JSON.stringify(formattedDates)
    );

    setLoading(true);
    try {
      await applyFilters({
        fromDate: formattedDates.fromDate,
        toDate: formattedDates.toDate,
        disbursementID: updateDisbursement,
        customerNames: updateCustomerNames,
        loanTypes: updateLoanTypes,
        BranchName: branch,
        conclusion: conclusion,
        campaignMstIds,
      });
      setVisible(false);
      setFilterActive(true);
      setLoading(false);
    } catch (error) {
      console.log("Error in apply filters", error);
    } finally {
      setLoading(false);
    }
  };

  // Clear all filters
  const handleClearFilters = () => {
    setFilterKeys({
      fromDate: null,
      toDate: null,
      branch: null,
      conclusion: null,
      disbursementId: "",
      customerNames: "",
      loanTypes: "",
      campaignMstIds: [],
    });

    // close modal
    setVisible(false);

    // Remove fromDate and toDate from localStorage
    localStorage.removeItem(
      `${isSummaryPage ? SUMMARY_FILTERS.summaryFilter : "filterDate"}`
    );
    setFilterActive(false);

    if (ClearFilters) {
      ClearFilters(); // Call the function from `TotalCustomersDashboard`
    }
  };

  // Helper function to check if any filter is active
  const checkAnyFilterActive = ({ parsedLocalFilterKeys, pageId }) => {
    const isNonEmpty = (value) => {
      if (Array.isArray(value)) return value.length > 0;
      if (typeof value === "string") return value.trim() !== "";
      return false;
    };

    const hasBasicFilter =
      parsedLocalFilterKeys?.fromDate ||
      parsedLocalFilterKeys?.toDate ||
      (Array.isArray(parsedLocalFilterKeys?.BranchName) &&
        parsedLocalFilterKeys?.BranchName?.length > 0);
    const hasDisbursementFilter =
      pageId !== SMART_COLLECT_MENU_IDS.DASHBOARD &&
      isNonEmpty(parsedLocalFilterKeys.disbursementID);
    const hasConclusionFilter =
      pageId === SMART_COLLECT_MENU_IDS.AI_CALLS &&
      isNonEmpty(parsedLocalFilterKeys.conclusion);
    const hasCustomerAndLoanTypeFilter =
      (pageId === "TotalLoans" || pageId === "TotalLoansConnected") &&
      (isNonEmpty(parsedLocalFilterKeys.customerNames) ||
        isNonEmpty(parsedLocalFilterKeys.loanTypes));

    return (
      hasBasicFilter ||
      hasDisbursementFilter ||
      hasConclusionFilter ||
      hasCustomerAndLoanTypeFilter
    );
  };

  const fetchBranches = async () => {
    try {
      const response = await AXIOS.get("branch_names/"); 
      if (response.data?.AllBranchNames) {
        const formattedBranches = response.data.AllBranchNames.map(
          ({ BranchName, BranchMstID }) => ({
            label: BranchName,
            value: BranchMstID,
          })
        );
        setBranchList(formattedBranches);
        console.log(formattedBranches)
        // Extract only the branch IDs
        const branchIds = formattedBranches.map((branch) => branch.value);
        // Store only IDs in localStorage
        localStorage.setItem(
          SUMMARY_FILTERS.summaryBranchIds,
          JSON.stringify(branchIds)
        );
      }
    } catch (error) {
      messageApi.error("Failed to fetch branches");
      console.error("Error fetching branches:", error);
    }
  };
  // Load stored dates from localStorage when the component mounts
  useEffect(() => {
    fetchBranches();

    const localFilterKeys = localStorage.getItem(
      `${isSummaryPage ? SUMMARY_FILTERS.summaryFilter : "filterDate"}`
    );
    if (localFilterKeys) {
      const parsedLocalFilterKeys = JSON.parse(localFilterKeys);
      // set filter keys
      setFilterKeys({
        fromDate: parsedLocalFilterKeys.fromDate
          ? dayjs(parsedLocalFilterKeys.fromDate)
          : null,
        toDate: parsedLocalFilterKeys.toDate
          ? dayjs(parsedLocalFilterKeys.toDate)
          : null,
        branch: parsedLocalFilterKeys.BranchName,
        conclusion: parsedLocalFilterKeys.conclusion || null,
        disbursementId: parsedLocalFilterKeys.disbursementID,
        customerNames: parsedLocalFilterKeys.customerNames || "",
        loanTypes: parsedLocalFilterKeys.loanTypes || "",
      });

      // Use the shared handler to determine filter activity
      setFilterActive(checkAnyFilterActive({ parsedLocalFilterKeys, pageId }));
    }
  }, []);

  // Set the filter's date
  useEffect(() => {
    if (selectedDate?.fromDate && selectedDate?.toDate) {
      setFilterKeys((prev) => ({
        ...prev,
        fromDate: dayjs(selectedDate.fromDate),
        toDate: dayjs(selectedDate.toDate),
      }));
      setFilterActive(true);
    } else if (!isDashboardOtherPages) {
      // If selectedDate is removed, check if any other filter is active
      const localFilterKeys = localStorage.getItem(
        `${isSummaryPage ? SUMMARY_FILTERS.summaryFilter : "filterDate"}`
      );
      let parsedLocalFilterKeys = {};
      if (localFilterKeys) {
        parsedLocalFilterKeys = JSON.parse(localFilterKeys);
      }
      // Remove the date from the check, since it's missing
      parsedLocalFilterKeys.fromDate = null;
      parsedLocalFilterKeys.toDate = null;
      if (!checkAnyFilterActive({ parsedLocalFilterKeys, pageId })) {
        setFilterKeys((prev) => ({ ...prev, fromDate: null, toDate: null }));
        setFilterActive(false);
      }
    }
  }, [selectedDate, isDashboardOtherPages, pageId]);

  return (
    <div className={Style.container}>
      {contextHolder}
      <Flex className={Style.button_container} onClick={() => setVisible(true)}>
        <img src={filterActive ? ACTIVE_FILTER_IMG : FILTER_IMG} alt="filter" />
      </Flex>

      {visible && (
        <Flex className={Style.modal} vertical>
          <Flex justify="space-between">
            <Flex justify="center" style={{ flex: 1 }}>
              <Text className={Style.text}>Filter</Text>
            </Flex>
            <input
              type="image"
              src={CROSS_IMG}
              alt="cross"
              style={{ cursor: "pointer" }}
              onClick={() => setVisible(false)}
            />
          </Flex>

          <Divider style={{ backgroundColor: "#0F2050", margin: "1rem 0" }} />

          <Flex gap={15} vertical>
            <Flex vertical>
              <Text className={Style.fields}>From Date:</Text>
              <DatePicker
                className={Style.date_picker}
                onChange={(date) =>
                  handleFilterKeysChange({ type: "fromDate", key: date })
                }
                value={filterKeys.fromDate}
                format="DD-MM-YYYY"
                placeholder="dd-mm-yyyy"
                disabledDate={(current) => {
                  if (!engagementStartDate) {
                    return current && !current.isSame(dayjs(), "day"); // Disable all except today
                  }
                  return (
                    current &&
                    (current < dayjs(engagementStartDate).startOf("day") ||
                      current > dayjs().endOf("day"))
                  );
                }}
              />
            </Flex>

            <Flex vertical>
              <Text className={Style.fields}>To Date:</Text>
              <DatePicker
                className={Style.date_picker}
                onChange={(date) =>
                  handleFilterKeysChange({ type: "toDate", key: date })
                }
                value={filterKeys.toDate}
                format="DD-MM-YYYY"
                placeholder="dd-mm-yyyy"
                disabledDate={(current) => {
                  if (!engagementStartDate) {
                    return current && !current.isSame(dayjs(), "day"); // Disable all except today
                  }
                  return (
                    current &&
                    (current < dayjs(engagementStartDate).startOf("day") ||
                      current > dayjs().endOf("day"))
                  );
                }}
              />
            </Flex>

            <Flex vertical>
              <Text className={Style.fields}>Select Branch:</Text>
              <Select
                showSearch={false}
                mode="multiple"
                onChange={(value) =>
                  handleFilterKeysChange({ type: "branch", key: value })
                }
                options={branchList}
                value={filterKeys.branch}
                placeholder="Select Branch"
                className={Style.custom_select}
              />
            </Flex>

            {pageId !== SMART_COLLECT_MENU_IDS.DASHBOARD && (
              <Flex vertical>
                <Text className={Style.fields}>Disbursement Id:</Text>
                <Input
                  className={Style.custom_input}
                  placeholder="Enter disbursementId"
                  value={String(filterKeys.disbursementId)}
                  onChange={(e) =>
                    handleFilterKeysChange({
                      type: "disbursementId",
                      key: e.target.value,
                    })
                  }
                />
              </Flex>
            )}

            {(pageId === "TotalLoans" || pageId === "TotalLoansConnected") && (
              <>
                <Flex vertical>
                  <Text className={Style.fields}>Customer Name:</Text>
                  <Input
                    className={Style.custom_input}
                    placeholder="Enter customer name"
                    value={String(filterKeys.customerNames)}
                    onChange={(e) =>
                      handleFilterKeysChange({
                        type: "customerNames",
                        key: e.target.value,
                      })
                    }
                  />
                </Flex>

                <Flex vertical>
                  <Text className={Style.fields}>Loan Type:</Text>
                  <Input
                    className={Style.custom_input}
                    placeholder="Enter loan type"
                    value={String(filterKeys.loanTypes)}
                    onChange={(e) =>
                      handleFilterKeysChange({
                        type: "loanTypes",
                        key: e.target.value,
                      })
                    }
                  />
                </Flex>
              </>
            )}

            {pageId === SMART_COLLECT_MENU_IDS.AI_CALLS && (
              <Flex vertical>
                <Text className={Style.fields}>Select Conclusion:</Text>
                <Select
                  mode="multiple"
                  onChange={(value) =>
                    handleFilterKeysChange({ type: "conclusion", key: value })
                  }
                  options={[
                    { label: "Pending", value: "Pending" },
                    { label: "Denied", value: "Denied" },
                    { label: "Already Paid", value: "Already Paid" },
                    { label: "Wrong Number", value: "wrong_number" },
                  ]}
                  value={filterKeys.conclusion}
                  placeholder="Select conclusion"
                  className={Style.custom_select}
                />
              </Flex>
            )}
            <Flex justify="space-evenly">
              <Button
                className={[Style.apply, filterActive ? Style.active : ""].join(
                  " "
                )}
                onClick={handleApplyFilters}
                loading={loading}
              >
                Apply
              </Button>
              <Button className={Style.clear} onClick={handleClearFilters}>
                Clear
              </Button>
            </Flex>
          </Flex>
        </Flex>
      )}
    </div>
  );
}

FilterButton.propTypes = {
  visible: PropTypes.bool,
  setVisible: PropTypes.func,
  applyFilters: PropTypes.func,
  ClearFilters: PropTypes.func,
  selectedDate: PropTypes.object,
  isDashboardOtherPages: PropTypes.bool,
  pageId: PropTypes.string,
};

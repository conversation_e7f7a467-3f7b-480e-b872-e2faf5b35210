// UserDetails.jsx
import React, { useState } from "react";
import { Input, Button, message } from "antd";
import Style from "../_forget-password.module.scss";
import { AXIOS } from "../../../apis/ho-Instance";
import PropTypes from "prop-types";

export default function UserDetails({ setStep, setEmail,setOtp, setUserID}) {
    const [messageApi, contextHolder] = message.useMessage();
    const [localUsername, setLocalUsername] = useState("");
    const [localEmail, setLocalEmail] = useState("");
    const[isLoading, setIsLoading]= useState(false);

    const handleNext = async (e)=>{
        e.preventDefault();
        if (!localUsername || !localEmail) {
            return messageApi.warning("Please fill in all fields.");
        }
        setIsLoading(true);
        const requestData = {
            action: "verify_email",
            username: localUsername,
            email: localEmail,
        };
        try {
            const response = await AXIOS.post("forget_password/", requestData);
            if (response.status === 200) {
                messageApi.success("Email verified successfully!");       
                setEmail(localEmail);
                setUserID(response.data.UserID);
                setOtp(response.data.otp);
                setTimeout(() => {
                    setStep(2);  
                }, 1500)}
            else {
                messageApi.error("Invalid username or email.");
                }
        }catch(error) {
          console.log("Error in verifying email:", error);
        }finally {
            setIsLoading(false);
        }    
    };

    return (
        <div>
            {contextHolder}
            <form onSubmit={handleNext} className={Style.form}>
                <div className={Style.inputContainer}>
                    <label className={Style.label} htmlFor="name">Username</label>
                    <Input value={localUsername} onChange={(e) => setLocalUsername(e.target.value)} className={Style.input} id="name" name="name"/>
                </div>
                <div className={Style.inputContainer}>
                    <label className={Style.label} htmlFor="email">Email</label>
                    <Input value={email} onChange={(e) => setLocalEmail(e.target.value)} className={Style.input} id="email" name="email"/>
                </div>
                <Button type="primary" htmlType="submit" block className={Style.button} loading={isLoading}>                
                    Verify Email
                </Button>
            </form>
        </div>
    );
}

UserDetails.propTypes={
    setStep: PropTypes.func, 
    setEmail: PropTypes.func,
    setOtp: PropTypes.func, 
    setUserID: PropTypes.func
}
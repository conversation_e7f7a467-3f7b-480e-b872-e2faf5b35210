import React, { useEffect, useState } from "react";
import { SMART_COLLECT_MENU_IDS } from "../../../constant";
import { Link, useLocation } from "react-router";
import { Typography, Menu } from "antd";
import PropTypes from "prop-types";

const { Title, Text } = Typography;

export function SideMenu({ menuItems, setCollapsedSideMenu }) {
  const location = useLocation();
  const [items, setItems] = useState([]);
  const [desName, setDesName] = useState("HO");

  const [activeKey, setActiveKey] = useState([
    SMART_COLLECT_MENU_IDS.DASHBOARD,
  ]);

  const getMenuIDFromURL = (pathname) => {
    const lastPath = pathname.split("/").pop();

    // Return the matched menuID or a default ("dashboard")
    let menuID =
      Object.values(SMART_COLLECT_MENU_IDS).find((d) => d === lastPath) ||
      SMART_COLLECT_MENU_IDS.DASHBOARD;

    // handle the client details
    if (menuID === SMART_COLLECT_MENU_IDS.CLIENT_DETAILS) {
      return SMART_COLLECT_MENU_IDS.ADMIN;
    }

    //handle the campaign child route
    if (
      [SMART_COLLECT_MENU_IDS.PERIODIC, SMART_COLLECT_MENU_IDS.NORMAL].includes(
        menuID
      )
    ) {
      return SMART_COLLECT_MENU_IDS.CAMPAIGN_MANAGEMENT;
    }

    return menuID;
  };

  useEffect(() => {
    const user = JSON.parse(localStorage.getItem("user"));
    const designation = user?.designation;

    setDesName(designation);

    const updatedItems = menuItems
      .filter((item) => {
        return !(
          item.key === SMART_COLLECT_MENU_IDS.CAMPAIGN_MANAGEMENT &&
          designation === "BM"
        );
      })
      .map((item) => {
        return {
          key: item.key,
          icon: (
            <div
              style={{
                width: "20px",
                height: "20px",
                marginInlineStart: "0.1rem",
              }}
            >
              <img
                src={item.icon}
                style={{ width: "100%", objectFit: "contain" }}
                alt="icon"
              />
            </div>
          ),
          label: (
            <Link
              to={`${item.link ? item.link : "#"}`}
              style={{
                fontWeight: "400",
                fontSize: "14px",
                letterSpacing: "0.5px",
              }}
            >
              {item.label}
            </Link>
          ),
          children: item.children
            ? [
                {
                  type: "group",
                  label: (
                    <Text
                      style={{
                        color: "white",
                        textAlign: "center",
                        width: "100%",
                        display: "inline-block",
                        fontWeight: "bold",
                        borderBottom: "1px solid white",
                      }}
                    >
                      {item.label}
                    </Text>
                  ), // Group label from Ant Design
                  children: item.children.map((subItem) => ({
                    key: subItem.key,
                    label: (
                      <Link
                        to={`${subItem.link ? subItem.link : "#"}`}
                        style={{
                          fontWeight: "400",
                          fontSize: "13.1px",
                          letterSpacing: "0.5px",
                        }}
                      >
                        {subItem.label}
                      </Link>
                    ),
                    icon: (
                      <div
                        style={{
                          width: "15px",
                          height: "15px",
                          marginInlineEnd: "0.5rem",
                        }}
                      >
                        <img
                          src={subItem.icon}
                          style={{ width: "100%", objectFit: "contain" }}
                          alt="icon"
                        />
                      </div>
                    ),
                  })),
                },
              ]
            : undefined,
        };
      });
    setItems(updatedItems);
  }, [menuItems]);

  useEffect(() => {
    const active = getMenuIDFromURL(location.pathname);
    setActiveKey(active);
  }, [location.pathname]);

  return (
    <Menu
      defaultSelectedKeys={[activeKey]}
      selectedKeys={[activeKey]}
      mode="inline"
      inlineCollapsed={true}
      items={items}
      // onClick={() => {
      //   setCollapsedSideMenu(true);
      // }}
      onClick={({ key }) => {
        if (key === SMART_COLLECT_MENU_IDS.MARKETING) {
          window.open(
            `https://marketingai.markytics.ai/smart-collect-login?designation=${desName}`,
            "_blank"
          );
          return;
        }

        if (key === SMART_COLLECT_MENU_IDS.BANK_ASSIST) {
          window.open(
            `https://bankassist.markytics.ai/smart-collect-login?designation=${desName}`,
            "_blank"
          );
          return;
        }

        setCollapsedSideMenu(true);
      }}
    />
  );
}

SideMenu.propTypes = {
  menuItems: PropTypes.array,
  setCollapsedSideMenu: PropTypes.func,
};

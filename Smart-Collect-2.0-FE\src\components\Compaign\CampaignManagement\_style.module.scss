@import url("../../../index.css");

$table-radius: 22px;
$attempts-bg: #bdd1ff;
$disable: #787777;
$body: #e4f8f9;
$green: #02951a;
$red: #ec3939;
$blue: #1743be;
.download_button {
  background-color: var(--dark-blue);
  padding: 0.2rem 1rem;
  border-radius: 10px;
  cursor: pointer;
  margin-right: 1.3rem;
  border: 0px solid var(--dark-blue);
  img {
    width: 20px;
  }
}
.customTable {
  padding-top: 1.5rem;
  .name {
    font-weight: 500;
    white-space: nowrap;
    color: var(--dark-blue);
  }

  .icon_div {
    display: flex;
    justify-content: center;
    width: 100%;
    .icon_img {
      width: 15px;
      height: 15px;
      img {
        width: 100%;
        object-fit: contain;
        cursor: pointer;
      }
    }
  }

  .status {
    font-weight: 600;
  }

  //Global ant design classes
  // Pagination
  :global(.ant-pagination) {
    justify-content: center;
    margin: 0 !important;

    :global(.ant-pagination-prev),
    :global(.ant-pagination-next) {
      color: var(--dark-blue);
      border: 0 solid var(--blue);
      background: #e4f8f9;
      height: 25px;
      min-width: 15px;
      border-radius: 0px;
      margin: 0;
      button {
        border-radius: 0px;
      }
    }
    :global(.ant-pagination-item) {
      margin-right: 0;
      height: 0;
      a {
        color: $disable;
        font-size: 0.9rem;
        line-height: 23px;
        font-family: 'Kanit','Courier New', Courier, monospace;
        font-weight: normal;
      }
      &:hover {
        background-color: transparent;
      }
    }
    :global(.ant-pagination-item-active) {
      border: none;
      a {
        color: var(--dark-blue);
        font-size: 1.2rem;
        padding: 0;
      }
    }
  }

  //Table container
  :global(.ant-table-container) {
    padding: 0;
    margin-bottom: 0.5rem;
    border-start-start-radius: $table-radius;
    border-start-end-radius: $table-radius;

      // Table header
      :global(.ant-table-header) {
          position: relative;
          margin: 0 auto;
          top: -21px;
          border-radius: $table-radius;
          :global(.ant-table-column-has-sorters) {
          background-color: var(--dark-blue);
          &:hover{
              background-color: var(--dark-blue);
          }
  
          :global(.ant-table-column-sorter-up),
          :global(.ant-table-column-sorter-down) {
              svg{
              fill: white;
              }
              &:global(.active){
              svg{
                  fill: rgb(24, 155, 249)
              }
              }
          }
          }
      }

    // Table virtual body
    :global(.ant-table-tbody-virtual) {
      margin-top: -8px;
    }

    &::before {
      box-shadow: none !important;
    }
  }

  // Table rows header
  :global(.ant-table-thead > tr) {
    th {
      border-start-end-radius: 0 !important;
      background-color: var(--dark-blue);
      border-inline-end: none !important;
      color: white;
      border-bottom: none;
      text-align: center;
      font-family: 'Kanit','Courier New', Courier, monospace;
      font-weight: normal;

      &:not(:last-child):not(.ant-table-selection-column):not(
          .ant-table-row-expand-icon-cell
        ):not([colspan])::before {
        width: 0;
      }
      &:global:nth-child(2){
        text-align: left;
      }
    }
  }

  // Table body
  :global(.ant-table-tbody) {
    // Body rows
    :global(.ant-table-row) {
      &:nth-child(even) {
        background-color: var(--light-green);
        border-radius: 8px;
      }
      :global(.ant-table-cell-row-hover) {
        background-color: transparent;
      }
      // Cols
      :global(.ant-table-cell) {
        font-weight: 600;
        text-align: center;
        border-bottom: 2px solid white;

        :global(.ant-typography) {
          color: var(--dark-blue);
          font-family: 'Kanit','Courier New', Courier, monospace;
          font-weight: normal;
        }

        &:global(.ant-table-cell-fix-right) {
          border-bottom: none !important;
        }
        &:global(.ant-table-cell-fix-right-first) {
          border-left: 2px solid white;
        }
        &:global:nth-child(2){
          text-align: left;
          :global(.ant-typography) {
            font-weight: 500;
          }
        }
      }
    }
  }

  // Fixed Cols
  :global(.ant-table-cell-fix-right) {
    background-color: $body;
  }
}

.add_button {
  background-color: var(--dark-blue);
  color: white;
  display: flex;
  align-items: center;
  border-radius: 50px;
  border:0px solid white;
  padding: 1.5rem 0.5rem;
  cursor: pointer;
  position: fixed;
  bottom: 20px;
  height: 40px;
  width: fit-content;

  .button_text {
    position: absolute;
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.5s ease;
    padding-left: 0.5rem;
    font-weight: 400;
    font-family: 'Kanit','Courier New', Courier, monospace;
  }

  .icon_img {
    width: 35px;
    img {
      width: 100%;
      object-fit: contain;
    }
  }

  &:hover {
    .button_text {
      opacity: 1;
      position: relative; /* Back to normal flow */
    }
  }
}

// Responsive view
@media screen and (max-width: 768px) {
  .download_button{
    padding: 0rem 0.5rem;
    border-radius: 4px;
    margin-top: 0.5rem;
    img{
      width: 15px;
    }
  }

  .customTable {
    // Table rows header
    :global(.ant-table-thead > tr) {
      th {
        font-size: 12px;
        padding: 0.5rem;
      }
    }
    :global(.ant-table-tbody) {
      :global(.ant-table-row) {
        :global(.ant-table-cell) {
          padding: 0.6rem;
          font-size: 12px !important;
          :global(.ant-typography){
            font-size: 12px !important;
          }
        }
      }
    }

    .icon_div {
      .icon_img {
        width: 13px;
        height: 13px;
      }
    }
  }

  .add_button {
    padding: 0rem 0.4rem;
    height: 30px;
  
    .button_text {
     font-size: 12px;
    }
  
    .icon_img {
      width: 25px;
    }
  }
}
import React from "react";
import { Tabs, Dropdown, Menu, Col, Flex } from "antd";
import { DownOutlined, CloseOutlined } from "@ant-design/icons";
import Style from "./_style.module.scss"; // Update with your actual style file
import PropTypes from "prop-types";

const MAX_VISIBLE_TABS = 5;

export default function LimitedTabsComponent({
  campaignTabs = [],
  currentCampaign,
  handleTabChange,
  handleRemoveCampCategory,
}) {
  // Ensure current campaign is string
  const currentKey = String(currentCampaign);

  // Make sure selected campaign is always visible
  const currentTab = campaignTabs.find((c) => String(c.id) === currentKey);
  const otherTabs = campaignTabs.filter((c) => String(c.id) !== currentKey);

  const visibleTabs = currentTab
    ? [currentTab, ...otherTabs.slice(0, MAX_VISIBLE_TABS - 1)]
    : otherTabs.slice(0, MAX_VISIBLE_TABS);

  const hiddenTabs = campaignTabs.filter(
    (c) => !visibleTabs.some((v) => String(v.id) === String(c.id))
  );

  const handleDropdownClick = ({ key }) => {
    handleTabChange(key);
  };

  // const dropdownMenu = (
  //   <Menu
  //     onClick={handleDropdownClick}
  //     className={Style.dropdown_menu}
  //     items={hiddenTabs.map((camp) => ({
  //       key: camp.id,
  //       label: camp.name,
  //     }))}
  //   />
  // );

  const allTabs = [
    ...visibleTabs.map((camp) => ({
      label: (
        <Flex align="center" justify="space-between" style={{ width: "100%" }}>
          {camp.name}
          {String(camp.id) === currentKey && (
            <CloseOutlined
              onClick={handleRemoveCampCategory}
              style={{ position: "absolute", right: "10px", top: "10px" }}
            />
          )}
        </Flex>
      ),
      key: String(camp.id),
    })),
    ...(hiddenTabs.length > 0
      ? [
          {
            label: (
              <Dropdown
                menu={{
                  items: hiddenTabs.map((camp) => ({
                    key: camp.id,
                    label: camp.name,
                  })),
                  onClick: handleDropdownClick,
                }}
                trigger={["click"]}
              >
                <span style={{ cursor: "pointer" }}>
                  More Campaigns <DownOutlined />
                </span>
              </Dropdown>
            ),
            key: "more",
            disabled: true,
          },
        ]
      : []),
  ];

  return campaignTabs?.length ? (
    <Col span={24} style={{ padding: "0px" }} className={Style.dashboard_tabs}>
      <Tabs
        activeKey={currentKey}
        onChange={(key) => {
          if (key !== "more") handleTabChange(key);
        }}
        items={allTabs}
        className={`${currentCampaign === null ? Style.tab_null : Style.tabs}`}
      />
    </Col>
  ) : null;
}

LimitedTabsComponent.propTypes = {
  campaignTabs: PropTypes.array,
  currentCampaign: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  handleTabChange: PropTypes.func.isRequired,
  handleRemoveCampCategory: PropTypes.func.isRequired,
};

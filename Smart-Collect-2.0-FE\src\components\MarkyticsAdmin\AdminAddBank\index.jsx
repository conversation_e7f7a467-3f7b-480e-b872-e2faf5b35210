import {
  Button,
  Col,
  Flex,
  Input,
  InputNumber,
  message,
  Row,
  Select,
  Typography,
} from "antd";
import React, { useState } from "react";
import AppHeader from "../../AppHeader";
import BANK_NAME_IMG from "../../../assets/Images/bank.png";
import USER_NAME_IMG from "../../../assets/Images/usename.png";
import STATUS_IMG from "../../../assets/Images/status.png";
import REMARK_IMG from "../../../assets/Images/remark.png";
import CLAIM_PERIOD_IMG from "../../../assets/Images/claim-period.svg";
import COMMUNICATION_CHANNEL_IMG from "../../../assets/Images/channel.svg";
import SUBSCRIPTION_TYPE_IMG from "../../../assets/Images/subscribe.svg";
import SELECT_COMPONENT_IMG from "../../../assets/Images/admin-select.svg";
import EMAIL_IMG from "../../../assets/Images/admin-email.svg";
import PHONE_IMG from "../../../assets/Images/admin-phone.svg";
import FIRST_NAME_IMG from "../../../assets/Images/admin-first.svg";
import LAST_NAME_IMG from "../../../assets/Images/admin-last.svg";
import PASSWORD_IMG from "../../../assets/Images/password.svg";
import BU_CODE_IMG from "../../../assets/Images/bucode.svg";
import BU_TYPE_IMG from "../../../assets/Images/butype.svg";
import REPORTING_ID_IMG from "../../../assets/Images/reporting.svg";
import REPORTING_TYPE_IMG from "../../../assets/Images/reportingID.svg";
import LANG_IMG from "../../../assets/Images/lang.svg";
import BRANCH_IMG from "../../../assets/Images/branch.svg";
import GEO_IMG from "../../../assets/Images/geography-alt.svg";
import Style from "../_global.module.scss";
import { AXIOS } from "../../../apis/ho-Instance";
import PropTypes from "prop-types";
import { data } from "react-router";

const { Text } = Typography;

export default function AdminAddBank() {
  const [messageApi, contextHolder] = message.useMessage();
  const [isLoading, setIsLoading] = useState(false);
  const [addData, setAddData] = useState({
    isActive: "Active",
    remark: "",
    subscriptionType: "Basic",
    communicationChannel: null,
    selectComponents: [],
    bankName: "",
    username: "",
    firstName: "",
    lastName: "",
    email: "",
    // designation:"HO",
    mobileNumber: "",
    claim_period: 1,
    password: "",
    branchCode: "",
    buCode: "",
    buType: "",
    buMobile: "",
    reportingBuId: 1,
    buEmail: "",
    buName: "",
    reportingBuType: "",
    branchName: "",
    lngMstId: 1,
    geographyIsActive: false,
  });

  const formatIndianPhoneNumber = (value) => {
    if (!value) return "";

    // Convert to string
    const stringValue = String(value);

    if (stringValue.length > 10) {
      messageApi.error("Please, Give Right Formatted Mobile Number");
      return "";
    }

    // Remove all non-numeric characters
    const cleaned = stringValue?.replace(/\D/g, "");

    // Ensure max length of 10 digits (excluding country code)
    const trimmed = cleaned.slice(0, 10);

    // Format as XXXXXXXXXX
    return trimmed.replace(/(\d{5})(\d{5})/, "$1$2");
  };

  // Update the state
  const handleInputChange = ({ field, value }) => {
    setAddData((prevData) => ({
      ...prevData,
      [field]: value,
    }));
  };

  // /Handle new bank
  const handleAddNewBank = async () => {
    const formattedMobile = formatIndianPhoneNumber(addData.mobileNumber);
    const formattedBuMobile = formatIndianPhoneNumber(addData.buMobile);

    if (addData.lngMstId === null || addData.lngMstId === undefined) {
      return message.error("Language Id can not be null or 0");
    }
    if (
      !formattedMobile ||
      formattedMobile.length !== 10 ||
      !formattedBuMobile ||
      formattedBuMobile.length !== 10
    ) {
      const errorMsg =
        !formattedMobile || formattedMobile.length !== 10
          ? "Please enter a valid 10-digit Mobile Number"
          : "Please enter a valid 10-digit BU Mobile Number";
      messageApi.error(errorMsg);
      setIsLoading(false);
      return;
    }
    if (
      !addData.bankName ||
      !addData.branchName ||
      !addData.branchCode ||
      !addData.buCode ||
      !addData.buEmail ||
      !addData.buMobile ||
      !addData.buName ||
      !addData.buType ||
      !addData.claim_period ||
      !addData.communicationChannel ||
      !addData.email ||
      !addData.firstName ||
      !addData.lastName ||
      !addData.mobileNumber ||
      !addData.password ||
      !addData.remark ||
      !addData.subscriptionType ||
      !addData.username ||
      (addData.subscriptionType === "Custom" &&
        addData.selectComponents.length === 0)
    ) {
      messageApi.error("Please fill in all required fields before submitting!");
      return;
    }
    setIsLoading(true);
    const body = {
      bank_name: addData.bankName,
      username: addData.username,
      bank_status: addData.isActive,
      email: addData.email,
      first_name: addData.firstName,
      last_name: addData.lastName,
      subscription_type: addData.subscriptionType,
      mobile_number: addData.mobileNumber,
      comm_channel: addData.communicationChannel,
      remark: addData.remark,
      claim_period: addData.claim_period,
      components:
        addData.subscriptionType === "Custom" ? addData.selectComponents : [],
      password: addData.password,
      bu_code: addData.buCode,
      bu_name: addData.buName,
      bu_type: addData.buType,
      reporting_bu_id: addData.reportingBuId,
      reporting_bu_type: addData.reportingBuType,
      bu_mobile: addData.buMobile,
      bu_email: addData.buEmail,
      geography_is_active: addData.geographyIsActive,
      branch_code: addData.branchCode,
      branch_name: addData.branchName,
      lngmstid: addData.lngMstId,
    };
    console.log(body);
    try {
      const res = await AXIOS.post("geography/", body, {
        validateStatus: () => true,
      });
      console.log(res);
      if ([200, 201].includes(res.status)) {
        messageApi.success("Bank & User Created SuccessFully!");
        setAddData({
          isActive: "Active",
          remark: "",
          subscriptionType: "Basic",
          communicationChannel: null,
          selectComponents: [],
          bankName: "",
          username: "",
          firstName: "",
          lastName: "",
          email: "",
          // designation:"HO",
          mobileNumber: "",
          claim_period: 1,
          password: "",
          buCode: "",
          buType: "",
          buMobile: "",
          reportingBuId: 1,
          buEmail: "",
          buName: "",
          reportingBuType: "",
          branchName: "",
          lngMstId: 1,
          geographyIsActive: false,
        });
      } else {
        message.error(res.data?.message || "Bank not created!");
      }
    } catch (error) {
      console.log("Error in Add new Bank", error);
      message.error("Something went wrong!");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      {contextHolder}
      <Flex vertical gap={10}>
        <AppHeader title={"Add Bank"} />
        <Row gutter={[20, 30]} className={Style.add_bank}>
          {/* name */}
          <Col md={8} xs={24}>
            <FormInput
              label="Bank Name"
              icon={BANK_NAME_IMG}
              value={addData.bankName}
              placeholder="Enter bank name"
              onChange={(value) =>
                handleInputChange({ field: "bankName", value })
              }
            />
          </Col>

          {/* username */}
          <Col md={8} xs={24}>
            <FormInput
              label="Username"
              icon={USER_NAME_IMG}
              value={addData.username}
              placeholder="Enter username"
              onChange={(value) =>
                handleInputChange({ field: "username", value })
              }
            />
          </Col>

          {/* status */}
          <Col md={8} xs={24}>
            <FormSelect
              icon={STATUS_IMG}
              label={"Bank Status"}
              value={addData.isActive ? "active" : "inactive"}
              options={[
                {
                  label: "Active",
                  value: "active",
                },
                {
                  label: "Inactive",
                  value: "inactive",
                },
              ]}
              onChange={(value) =>
                handleInputChange({
                  field: "isActive",
                  value: value === "active",
                })
              }
              placeholder="Select status"
            />
          </Col>

          {/* email */}
          <Col md={8} xs={24}>
            <FormInput
              icon={EMAIL_IMG}
              label={"Email"}
              value={addData.email}
              placeholder="Enter mail"
              onChange={(value) =>
                handleInputChange({
                  field: "email",
                  value,
                })
              }
            />
          </Col>

          {/* First */}
          <Col md={8} xs={24}>
            <FormInput
              icon={FIRST_NAME_IMG}
              label={"First Name"}
              placeholder="Enter first name"
              value={addData.firstName}
              onChange={(value) =>
                handleInputChange({
                  field: "firstName",
                  value,
                })
              }
            />
          </Col>

          {/* Last */}
          <Col md={8} xs={24}>
            <FormInput
              icon={LAST_NAME_IMG}
              label={"Last Name"}
              value={addData.lastName}
              placeholder="Enter last name"
              onChange={(value) =>
                handleInputChange({
                  field: "lastName",
                  value,
                })
              }
            />
          </Col>

          {/* branch code */}
          <Col md={8} xs={24}>
            <FormInput
              icon={BU_CODE_IMG}
              label={"Branch Code"}
              value={addData.branchCode}
              placeholder="Enter branch code"
              onChange={(value) =>
                handleInputChange({
                  field: "branchCode",
                  value,
                })
              }
            />
          </Col>

          {/* Subscription Type */}
          <Col md={8} xs={24}>
            <FormSelect
              icon={SUBSCRIPTION_TYPE_IMG}
              label={"Subscription Type"}
              width="110px"
              options={[
                {
                  label: "Basic",
                  value: "Basic",
                },
                {
                  label: "Premium",
                  value: "Premium",
                },
                {
                  label: "Advance",
                  value: "Advance",
                },
                {
                  label: "Custom",
                  value: "Custom",
                },
              ]}
              onChange={(value) =>
                handleInputChange({
                  field: "subscriptionType",
                  value,
                })
              }
              value={addData.subscriptionType}
            />
          </Col>

          {/* Select Component */}
          {addData.subscriptionType === "Custom" && (
            <Col md={8} xs={24}>
              <FormSelect
                icon={SELECT_COMPONENT_IMG}
                label={"Select Component"}
                mode={"multiple"}
                options={[
                  { label: "AI", value: "AI" },
                  { label: "DND", value: "DND" },
                  { label: "Report", value: "Report" },
                  { label: "Insight", value: "Insight" },
                  { label: "Activity", value: "Activity" },
                  { label: "Analysis", value: "Analysis" },
                  { label: "Dashboard", value: "Dashboard" },
                  { label: "Field Officer", value: "Field Officer" },
                  {
                    label: "Campaign Management",
                    value: "Campaign Management",
                  },
                ]}
                value={addData.selectComponents}
                onChange={(value) =>
                  handleInputChange({
                    field: "selectComponents",
                    value,
                  })
                }
              />
            </Col>
          )}

          {/* Mobile */}
          <Col md={8} xs={24}>
            <FormInputNumber
              icon={PHONE_IMG}
              label={"Mobile Number"}
              value={addData.mobileNumber}
              placeholder="Enter number"
              onChange={(value) => {
                const formattedNumber = formatIndianPhoneNumber(value);
                handleInputChange({
                  field: "mobileNumber",
                  value: formattedNumber,
                });
              }}
            />
          </Col>

          {/* Communication Channel */}
          <Col md={8} xs={24}>
            <FormSelect
              icon={COMMUNICATION_CHANNEL_IMG}
              label={"Communication Channel"}
              value={addData.communicationChannel}
              mode={"multiple"}
              width="150px"
              placeholder="Select channel"
              options={[
                { label: "WhatsApp", value: "whatsapp" },
                { label: "SMS", value: "sms" },
                { label: "Email", value: "email" },
                { label: "AI Calls", value: "ai" },
                { label: "IVR Calls", value: "ivr" },
                { label: "Blaster Calls", value: "blaster" },
              ]}
              onChange={(value) =>
                handleInputChange({
                  field: "communicationChannel",
                  value,
                })
              }
            />
          </Col>

          {/*password  */}
          <Col md={8} xs={24}>
            <FormInput
              label={"Password"}
              icon={PASSWORD_IMG}
              value={addData.password}
              placeholder="Enter password"
              onChange={(value) => {
                handleInputChange({ field: "password", value });
              }}
            />
          </Col>

          {/* bu code */}
          <Col md={8} xs={24}>
            <FormInput
              label={"Bu Code"}
              icon={BU_CODE_IMG}
              value={addData.buCode}
              placeholder="Enter code"
              onChange={(value) => {
                handleInputChange({ field: "buCode", value });
              }}
            />
          </Col>

          {/* bu type */}
          <Col md={8} xs={24}>
            <FormInput
              label={"Bu Type"}
              icon={BU_TYPE_IMG}
              value={addData.buType}
              placeholder="Enter type"
              onChange={(value) => {
                handleInputChange({ field: "buType", value });
              }}
            />
          </Col>

          {/* bu mobile */}
          <Col md={8} xs={24}>
            <FormInputNumber
              icon={PHONE_IMG}
              label={"Bu Mobile"}
              value={addData.buMobile}
              placeholder="Enter bu mobile"
              onChange={(value) => {
                const formattedNumber = formatIndianPhoneNumber(value);
                handleInputChange({
                  field: "buMobile",
                  value: formattedNumber,
                });
              }}
            />
          </Col>

          {/*bu email */}
          <Col md={8} xs={24}>
            <FormInput
              icon={EMAIL_IMG}
              label={"Bu Email"}
              value={addData.buEmail}
              placeholder="Enter bu mail"
              onChange={(value) =>
                handleInputChange({
                  field: "buEmail",
                  value,
                })
              }
            />
          </Col>

          {/*bu name */}
          <Col md={8} xs={24}>
            <FormInput
              icon={FIRST_NAME_IMG}
              label={"Bu Name"}
              value={addData.buName}
              placeholder="Enter bu name"
              onChange={(value) =>
                handleInputChange({
                  field: "buName",
                  value,
                })
              }
            />
          </Col>

          {/*reporting bu id */}
          <Col md={8} xs={24}>
            <FormInputNumber
              icon={REPORTING_ID_IMG}
              label={"Reporting Bu Id"}
              value={addData.reportingBuId}
              placeholder="Enter reporting bu id"
              isRequired={false}
              onChange={(value) =>
                handleInputChange({
                  field: "reportingBuId",
                  value,
                })
              }
            />
          </Col>

          {/*reporting bu type */}
          <Col md={8} xs={24}>
            <FormInput
              icon={REPORTING_TYPE_IMG}
              label={"Reporting Bu Type"}
              value={addData.reportingBuType}
              placeholder="Enter reporting type"
              isRequired={false}
              width="110px"
              onChange={(value) =>
                handleInputChange({
                  field: "reportingBuType",
                  value,
                })
              }
            />
          </Col>

          {/*lang */}
          <Col md={8} xs={24}>
            <FormInputNumber
              icon={LANG_IMG}
              label={"LangMstID"}
              value={addData.lngMstId}
              placeholder="Enter language id"
              onChange={(value) =>
                handleInputChange({
                  field: "lngMstId",
                  value,
                })
              }
            />
          </Col>

          {/*branch name */}
          <Col md={8} xs={24}>
            <FormInput
              icon={BRANCH_IMG}
              label={"Branch Name"}
              value={addData.branchName}
              placeholder="Enter branch name"
              onChange={(value) =>
                handleInputChange({
                  field: "branchName",
                  value,
                })
              }
            />
          </Col>

          {/* geography active */}
          <Col md={8} xs={24}>
            <FormSelect
              icon={GEO_IMG}
              label={"Geography Active"}
              width="110px"
              options={[
                {
                  label: "Active",
                  value: true,
                },
                {
                  label: "InActive",
                  value: false,
                },
              ]}
              onChange={(value) =>
                handleInputChange({
                  field: "geographyIsActive",
                  value,
                })
              }
              value={addData.geographyIsActive}
            />
          </Col>

          {/* claim */}
          <Col md={12} xs={24}>
            <FormInputNumber
              label={"Claim Period"}
              icon={CLAIM_PERIOD_IMG}
              value={addData.claim_period}
              placeholder="Enter claim"
              onChange={(value) =>
                handleInputChange({
                  field: "claim_period",
                  value,
                })
              }
            />
          </Col>

          {/* remark */}
          <Col span={12}>
            <FormInput
              icon={REMARK_IMG}
              label={"Remark"}
              value={addData.remark}
              placeholder="Enter remark"
              onChange={(value) =>
                handleInputChange({
                  field: "remark",
                  value,
                })
              }
            />
          </Col>

          {/* Add button */}
          <Col span={24}>
            <Flex align="center" justify="center">
              <Button
                className={Style.add_button}
                loading={isLoading}
                onClick={handleAddNewBank}
              >
                Add Bank
              </Button>
            </Flex>
          </Col>
        </Row>
      </Flex>
    </>
  );
}

const FormInput = ({
  label,
  icon,
  value,
  onChange,
  placeholder = "Enter",
  disabled = false,
  isRequired = true,
  width = "100px",
}) => (
  <Flex gap={20} align="center">
    <Flex gap={5} align="start">
      <div className={Style.img}>
        <img src={icon} alt={label} />
      </div>
      <Text className={Style.text} style={{ width }}>
        {label}: {isRequired && "*"}
      </Text>
    </Flex>
    <Input
      placeholder={placeholder}
      className={Style.custom_input}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      disabled={disabled}
    />
  </Flex>
);

const FormSelect = ({
  label,
  icon,
  value,
  onChange,
  options,
  placeholder = "Select",
  mode,
  width = "100px",
}) => (
  <Flex gap={20} align="center">
    <Flex gap={5} align="center">
      <div className={Style.img}>
        <img src={icon} alt={label} />
      </div>
      <Text className={Style.text} style={{ width }}>
        {label}:
      </Text>
    </Flex>
    <Select
      className={Style.custom_select}
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      options={options}
      mode={mode}
    />
  </Flex>
);

const FormInputNumber = ({
  label,
  icon,
  value,
  onChange,
  placeholder = "Enter Number",
}) => (
  <Flex gap={10} align="center">
    <Flex gap={5} align="center">
      <div className={Style.img}>
        <img src={icon} alt={label} />
      </div>
      <Text className={Style.remark}>{label}:</Text>
    </Flex>
    <InputNumber
      min={0}
      className={Style.custom_number}
      placeholder={placeholder}
      value={value}
      onChange={onChange}
    />
  </Flex>
);

// PropTypes
FormInput.propTypes = {
  label: PropTypes.string,
  icon: PropTypes.string,
  value: PropTypes.any,
  onChange: PropTypes.func,
  placeholder: PropTypes.string,
  disabled: PropTypes.bool,
  isRequired: PropTypes.bool,
  width: PropTypes.string,
};

FormSelect.propTypes = {
  label: PropTypes.string,
  icon: PropTypes.string,
  value: PropTypes.any,
  onChange: PropTypes.func,
  placeholder: PropTypes.string,
  mode: PropTypes.any,
  width: PropTypes.string,
  options: PropTypes.array,
};

FormInputNumber.propTypes = {
  label: PropTypes.string,
  icon: PropTypes.string,
  value: PropTypes.any,
  onChange: PropTypes.func,
  placeholder: PropTypes.string,
};

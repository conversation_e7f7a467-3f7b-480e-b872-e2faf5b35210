import { <PERSON><PERSON>, <PERSON><PERSON>icker, <PERSON>lex, message, Modal, Typography } from "antd";
import React, { useEffect, useState } from "react";
import Style from "./_style.module.scss";
import PropTypes from "prop-types";
import { AXIOS } from "../../../apis/ho-Instance";
import dayjs from "dayjs";

const { Text, Title } = Typography;

export function PromiseUpdateModal({ data, modalStatus, setModalStatus }) {
  console.log(data);
  const [selectedDate, setSelectedDate] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  // Handle the promise date update
  const handleUpdatePromiseDate = async () => {
    setIsLoading(true);
    if (!selectedDate) {
      message.error("Please select a date!");
      return;
    }
    try {
      await AXIOS.post("delete-disbursement/", {
        response_ids: [data.id],
        promise_date: selectedDate.format("YYYY-MM-DD"),
      });
      message.success("Promise date updated successfully");
    } catch (error) {
      console.log("Error updating promise date:", error);
      message.error("Failed to update promise date. Please try again.");
    } finally {
      setIsLoading(false);
      setModalStatus(false);
    }
  };

  useEffect(() => {
    setSelectedDate(data.date ? dayjs(data.date).set("hour", 12) : null);
  }, [data]);
  return (
    <Modal
      centered
      className={Style.modal_container}
      title={
        <Flex align="center" gap={5} justify="center">
          <Title level={4} className={Style.feedback_title}>
            New Promise Date
          </Title>
        </Flex>
      }
      open={modalStatus}
      onOk={() => {
        setModalStatus(false);
      }}
      onCancel={() => {
        setModalStatus(false);
      }}
      footer={null}
      width={350}
    >
      <Flex vertical gap={20}>
        <Flex gap={20} justify="space-between" style={{ padding: "1rem 0" }}>
          <Text className={Style.text}>Select new date:</Text>
          <DatePicker
            className={Style.date_picker}
            onChange={(date) => {
              setSelectedDate(date);
            }}
            value={selectedDate}
            format="DD-MM-YYYY"
            placeholder="dd-mm-yyyy"
          />
        </Flex>

        <Flex justify="center">
          <Button
            loading={isLoading}
            className={Style.custom_button}
            onClick={handleUpdatePromiseDate}
          >
            <Flex gap={5} align="center">
              <div className={Style.img}>
                {/* <img src={PHONE_IMG} alt="phone" /> */}
              </div>

              <Text>Update</Text>
            </Flex>
          </Button>
        </Flex>
      </Flex>
    </Modal>
  );
}

PromiseUpdateModal.propTypes = {
  data: PropTypes.object,
  modalStatus: PropTypes.bool.isRequired,
  setModalStatus: PropTypes.func.isRequired,
};

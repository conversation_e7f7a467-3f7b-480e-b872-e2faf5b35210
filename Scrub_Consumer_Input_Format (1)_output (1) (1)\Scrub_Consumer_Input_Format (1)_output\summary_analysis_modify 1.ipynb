{"cells": [{"cell_type": "code", "execution_count": 25, "id": "8d7c6036", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n"]}, {"cell_type": "code", "execution_count": 26, "id": "d0a354d0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: sqlalchemy in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (2.0.41)\n", "Requirement already satisfied: greenlet>=1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from sqlalchemy) (3.2.3)\n", "Requirement already satisfied: typing-extensions>=4.6.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from sqlalchemy) (4.13.0)\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install sqlalchemy"]}, {"cell_type": "code", "execution_count": 27, "id": "f3c48a85", "metadata": {}, "outputs": [], "source": ["from sqlalchemy import create_engine"]}, {"cell_type": "code", "execution_count": 28, "id": "9a5ca9b7", "metadata": {}, "outputs": [], "source": ["engine = create_engine('***************************************************************')"]}, {"cell_type": "code", "execution_count": 86, "id": "163cada4", "metadata": {}, "outputs": [], "source": ["designation = 1\n", "access_to = [1002, 1003, 1005, 1006, 1007, 1008, 1009, 1001, 1004]\n", "class DotDict(dict):\n", "    def __getattr__(self, attr):\n", "        return self.get(attr)\n", "    def __setattr__(self, attr, value):\n", "        self[attr] = value\n", "data =  {\n", "    \"State_id\": \"all\",  \n", "    \"Region_id\": [\"BUC8\", \"BUC14\"],    \n", "    \"Branch_id\": \"all\",          \n", "    \"CollectionOfficerID\": \"all\",  \n", "    \"LoanType\": \"all\",\n", "    \"BankMstID\" : [9],\n", "    \"tablewise\" : \"Branch\"\n", "}\n", "request = DotDict({\"data\": data})"]}, {"cell_type": "code", "execution_count": 87, "id": "11d4d872", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'data': {'State_id': 'all',\n", "  'Region_id': ['BUC8', 'BUC14'],\n", "  'Branch_id': 'all',\n", "  'CollectionOfficerID': 'all',\n", "  'LoanType': 'all',\n", "  'BankMstID': [9],\n", "  'tablewise': 'Branch'}}"]}, "execution_count": 87, "metadata": {}, "output_type": "execute_result"}], "source": ["request"]}, {"cell_type": "code", "execution_count": 88, "id": "cc130107", "metadata": {}, "outputs": [], "source": ["tablewise = request.data.pop('tablewise')"]}, {"cell_type": "code", "execution_count": 119, "id": "c867d70c", "metadata": {}, "outputs": [], "source": ["def generate_where_clause_scrub(request):\n", "    filter_data = request.data\n", "    conditions = []\n", "\n", "    for key, value in filter_data.items():\n", "        if value == \"all\" or value == []:\n", "            continue\n", "\n", "        if not isinstance(value, (list, tuple)):\n", "            value = [value]\n", "\n", "        # Escape each value\n", "        formatted_values = \", \".join(f\"''{v}''\" for v in value)\n", "        conditions.append(f'ds.\"{key}\" IN ({formatted_values})')\n", "\n", "    where_clause = \" AND \".join(conditions) if conditions else \"1=1\"\n", "    return where_clause\n"]}, {"cell_type": "code", "execution_count": null, "id": "6625a2d1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 120, "id": "5749f56e", "metadata": {}, "outputs": [], "source": ["where_clause = generate_where_clause_scrub(request=request)"]}, {"cell_type": "code", "execution_count": 121, "id": "3c8010ac", "metadata": {}, "outputs": [{"data": {"text/plain": ["'ds.\"Region_id\" IN (\\'\\'BUC8\\'\\', \\'\\'BUC14\\'\\') AND ds.\"BankMstID\" IN (\\'\\'9\\'\\')'"]}, "execution_count": 121, "metadata": {}, "output_type": "execute_result"}], "source": ["where_clause"]}, {"cell_type": "code", "execution_count": null, "id": "01be1764", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 122, "id": "6669f5d7", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"SELECT * FROM public.scrub_summary(\n", "    'ds.\"Region\"::TEXT AS region, ds.\"Region_id\"::TEXT AS region_id, ds.\"BankMstID\"::TEXT AS bankmstid',\n", "    '{where_clause}'\n", ");\"\"\""]}, {"cell_type": "code", "execution_count": 123, "id": "0dc54e66", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SELECT * FROM public.scrub_summary(\n", "    'ds.\"Region\"::TEXT AS region, ds.\"Region_id\"::TEXT AS region_id, ds.\"BankMstID\"::TEXT AS bankmstid',\n", "    'ds.\"Region_id\" IN (''BUC8'', ''BUC14'') AND ds.\"BankMstID\" IN (''9'')'\n", ");\n"]}], "source": ["print(query)"]}, {"cell_type": "code", "execution_count": 124, "id": "d4aca6a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'Region_id': ('BUC8', 'BUC14'), 'BankMstID': (9,)}"]}, "execution_count": 124, "metadata": {}, "output_type": "execute_result"}], "source": ["params"]}, {"cell_type": "code", "execution_count": 125, "id": "37a9b638", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>region</th>\n", "      <th>region_id</th>\n", "      <th>bankmstid</th>\n", "      <th>reference_no</th>\n", "      <th>score</th>\n", "      <th>dpd</th>\n", "      <th>disbursementid</th>\n", "      <th>risk_category</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Pune</td>\n", "      <td>BUC14</td>\n", "      <td>9</td>\n", "      <td>HIND250222CR8376706210843</td>\n", "      <td>None</td>\n", "      <td>40</td>\n", "      <td>1018</td>\n", "      <td>LOW</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Pune</td>\n", "      <td>BUC14</td>\n", "      <td>9</td>\n", "      <td>HIND250222CR8376706210843</td>\n", "      <td>None</td>\n", "      <td>40</td>\n", "      <td>1018</td>\n", "      <td>LOW</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Pune</td>\n", "      <td>BUC14</td>\n", "      <td>9</td>\n", "      <td>HIND250222CR8376706210843</td>\n", "      <td>None</td>\n", "      <td>40</td>\n", "      <td>1018</td>\n", "      <td>LOW - MEDIUM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Pune</td>\n", "      <td>BUC14</td>\n", "      <td>9</td>\n", "      <td>HIND250222CR8376706210843</td>\n", "      <td>None</td>\n", "      <td>40</td>\n", "      <td>1018</td>\n", "      <td>LOW - MEDIUM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Pune</td>\n", "      <td>BUC14</td>\n", "      <td>9</td>\n", "      <td>HIND250222CR8376706210843</td>\n", "      <td>None</td>\n", "      <td>40</td>\n", "      <td>1018</td>\n", "      <td>LOW - MEDIUM</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  region region_id bankmstid               reference_no score dpd  \\\n", "0   Pune     BUC14         9  HIND250222CR8376706210843  None  40   \n", "1   Pune     BUC14         9  HIND250222CR8376706210843  None  40   \n", "2   Pune     BUC14         9  HIND250222CR8376706210843  None  40   \n", "3   Pune     BUC14         9  HIND250222CR8376706210843  None  40   \n", "4   Pune     BUC14         9  HIND250222CR8376706210843  None  40   \n", "\n", "  disbursementid risk_category  \n", "0           1018           LOW  \n", "1           1018           LOW  \n", "2           1018  LOW - MEDIUM  \n", "3           1018  LOW - MEDIUM  \n", "4           1018  LOW - MEDIUM  "]}, "execution_count": 125, "metadata": {}, "output_type": "execute_result"}], "source": ["from sqlalchemy import create_engine, text\n", "import pandas as pd\n", "\n", "engine = create_engine('***************************************************************')\n", "\n", "query = text(query)\n", "\n", "df_summary = pd.read_sql(query, engine)\n", "df_summary.head()\n"]}, {"cell_type": "code", "execution_count": 126, "id": "c37e253b", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['BUC14', 'BUC8'], dtype=object)"]}, "execution_count": 126, "metadata": {}, "output_type": "execute_result"}], "source": ["df_summary['region_id'].unique()"]}, {"cell_type": "code", "execution_count": 54, "id": "6dc58660", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>region</th>\n", "      <th>region_id</th>\n", "      <th>bankmstid</th>\n", "      <th>reference_no</th>\n", "      <th>score</th>\n", "      <th>dpd</th>\n", "      <th>disbursementid</th>\n", "      <th>risk_category</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Mumbai</td>\n", "      <td>BUC13</td>\n", "      <td>9</td>\n", "      <td>HIND250222CR6146706210843</td>\n", "      <td>None</td>\n", "      <td>38</td>\n", "      <td>1000</td>\n", "      <td>HIGH</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Mumbai</td>\n", "      <td>BUC13</td>\n", "      <td>9</td>\n", "      <td>HIND250222CR6146706210843</td>\n", "      <td>None</td>\n", "      <td>38</td>\n", "      <td>1000</td>\n", "      <td>HIGH</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Mumbai</td>\n", "      <td>BUC13</td>\n", "      <td>9</td>\n", "      <td>HIND250222CR6146706210843</td>\n", "      <td>None</td>\n", "      <td>38</td>\n", "      <td>1000</td>\n", "      <td>HIGH</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Mumbai</td>\n", "      <td>BUC13</td>\n", "      <td>9</td>\n", "      <td>HIND250222CR6146706210843</td>\n", "      <td>None</td>\n", "      <td>38</td>\n", "      <td>1000</td>\n", "      <td>HIGH</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Mumbai</td>\n", "      <td>BUC13</td>\n", "      <td>9</td>\n", "      <td>HIND250222CR6146706210843</td>\n", "      <td>None</td>\n", "      <td>38</td>\n", "      <td>1000</td>\n", "      <td>HIGH</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>303</th>\n", "      <td>Kolkata</td>\n", "      <td>BUC12</td>\n", "      <td>9</td>\n", "      <td>HIND250222CR9586706210843</td>\n", "      <td>None</td>\n", "      <td>43</td>\n", "      <td>1011</td>\n", "      <td>HIGH</td>\n", "    </tr>\n", "    <tr>\n", "      <th>304</th>\n", "      <td>Kolkata</td>\n", "      <td>BUC12</td>\n", "      <td>9</td>\n", "      <td>HIND250222CR9586706210843</td>\n", "      <td>None</td>\n", "      <td>43</td>\n", "      <td>1011</td>\n", "      <td>HIGH</td>\n", "    </tr>\n", "    <tr>\n", "      <th>305</th>\n", "      <td>Kolkata</td>\n", "      <td>BUC12</td>\n", "      <td>9</td>\n", "      <td>HIND250222CR9586706210843</td>\n", "      <td>None</td>\n", "      <td>43</td>\n", "      <td>1011</td>\n", "      <td>HIGH</td>\n", "    </tr>\n", "    <tr>\n", "      <th>306</th>\n", "      <td>Kolkata</td>\n", "      <td>BUC12</td>\n", "      <td>9</td>\n", "      <td>HIND250222CR9586706210843</td>\n", "      <td>None</td>\n", "      <td>43</td>\n", "      <td>1011</td>\n", "      <td>HIGH</td>\n", "    </tr>\n", "    <tr>\n", "      <th>307</th>\n", "      <td>Kolkata</td>\n", "      <td>BUC12</td>\n", "      <td>9</td>\n", "      <td>HIND250222CR9586706210843</td>\n", "      <td>None</td>\n", "      <td>43</td>\n", "      <td>1011</td>\n", "      <td>LOW</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>308 rows × 8 columns</p>\n", "</div>"], "text/plain": ["      region region_id bankmstid               reference_no score dpd  \\\n", "0     Mumbai     BUC13         9  HIND250222CR6146706210843  None  38   \n", "1     Mumbai     BUC13         9  HIND250222CR6146706210843  None  38   \n", "2     Mumbai     BUC13         9  HIND250222CR6146706210843  None  38   \n", "3     Mumbai     BUC13         9  HIND250222CR6146706210843  None  38   \n", "4     Mumbai     BUC13         9  HIND250222CR6146706210843  None  38   \n", "..       ...       ...       ...                        ...   ...  ..   \n", "303  Kolkata     BUC12         9  HIND250222CR9586706210843  None  43   \n", "304  Kolkata     BUC12         9  HIND250222CR9586706210843  None  43   \n", "305  Kolkata     BUC12         9  HIND250222CR9586706210843  None  43   \n", "306  Kolkata     BUC12         9  HIND250222CR9586706210843  None  43   \n", "307  Kolkata     BUC12         9  HIND250222CR9586706210843  None  43   \n", "\n", "    disbursementid risk_category  \n", "0             1000          HIGH  \n", "1             1000          HIGH  \n", "2             1000          HIGH  \n", "3             1000          HIGH  \n", "4             1000          HIGH  \n", "..             ...           ...  \n", "303           1011          HIGH  \n", "304           1011          HIGH  \n", "305           1011          HIGH  \n", "306           1011          HIGH  \n", "307           1011           LOW  \n", "\n", "[308 rows x 8 columns]"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["df_summary\n"]}, {"cell_type": "code", "execution_count": 127, "id": "2cf0ac3c", "metadata": {}, "outputs": [], "source": ["def master_dpd_names_portfolio():\n", "    dpd_categories = [\n", "                    {'dpd_range_start': 0, 'dpd_range_end': 0, 'dpd_category_name': '0'},\n", "                    {'dpd_range_start': 1, 'dpd_range_end': 30, 'dpd_category_name': '1-30'},\n", "                    {'dpd_range_start': 31, 'dpd_range_end': 60, 'dpd_category_name': '31-60'},\n", "                    {'dpd_range_start': 61, 'dpd_range_end': 90, 'dpd_category_name': '61-90'},\n", "                    {'dpd_range_start': 91, 'dpd_range_end': 180, 'dpd_category_name': '91-180'},\n", "                    {'dpd_range_start': 181, 'dpd_range_end': 360, 'dpd_category_name': '181-360'},\n", "                    {'dpd_range_start': 361, 'dpd_range_end': 9999, 'dpd_category_name': '360+'}\n", "                    ]\n", "    return dpd_categories\n", "\n", "def create_dpd_buckets(dpd_categories):\n", "    dpd_buckets = []\n", "    for category in dpd_categories:\n", "        bucket = {\n", "            'name': category['dpd_category_name'],\n", "            'range': range(category['dpd_range_start'], category['dpd_range_end'] + 1)\n", "        }\n", "        dpd_buckets.append(bucket)\n", "    return dpd_buckets"]}, {"cell_type": "code", "execution_count": 128, "id": "eba39d12", "metadata": {}, "outputs": [], "source": ["dpd_categories = master_dpd_names_portfolio()\n", "dpd_buckets = create_dpd_buckets(dpd_categories)"]}, {"cell_type": "code", "execution_count": 129, "id": "139eadac", "metadata": {}, "outputs": [], "source": ["def categorize_dpd(dpd):\n", "    for bucket in dpd_buckets:\n", "        if dpd in bucket[\"range\"]:\n", "            return bucket[\"name\"]\n", "    return \"Unknown\"\n"]}, {"cell_type": "code", "execution_count": 148, "id": "cdd03f41", "metadata": {}, "outputs": [], "source": ["from rest_framework.response import Response\n", "from rest_framework import status\n", "\n", "def your_view_function(request):\n", "    # Assuming df and categorize_dpd are already defined in the scope\n", "\n", "    # Categorize DPD values\n", "    df[\"dpd_category\"] = df[\"DPD\"].astype(int).apply(categorize_dpd)\n", "\n", "    # Define the desired DPD range order\n", "    dpd_ranges = [\"0\", \"1-30\", \"31-60\", \"61-90\", \"91-180\", \"181-360\", \"360+\"]\n", "    print(\"DPD Ranges:\", dpd_ranges)\n", "\n", "    # Create pivot table (excluding totals)\n", "    pivot_table_no_totals = df.pivot_table(\n", "        values=\"credit_report_id\",\n", "        index=\"dpd_category\",\n", "        columns=\"risk_category\",\n", "        aggfunc=\"count\",\n", "    ).reindex(dpd_ranges, fill_value=0)\n", "\n", "    print(\"Pivot Table Without Totals:\\n\", pivot_table_no_totals)\n", "\n", "    # Add totals to pivot table\n", "    pivot_with_totals = pivot_table_no_totals.copy()\n", "    pivot_with_totals[\"Total\"] = pivot_with_totals.sum(axis=1)\n", "    pivot_with_totals.loc[\"Total\"] = pivot_with_totals.sum(axis=0)\n", "\n", "    # Reset index for serialization\n", "    pivot_with_totals = pivot_with_totals.reset_index()\n", "\n", "    # Convert to dict for JSON response\n", "    pivot_with_totals_dict = pivot_with_totals.to_dict(orient=\"records\")\n", "\n", "    return Response(\n", "        {\"final_response\": pivot_with_totals_dict},\n", "        status=status.HTTP_200_OK\n", "    )\n"]}, {"cell_type": "code", "execution_count": 150, "id": "661bda9d", "metadata": {}, "outputs": [], "source": ["def generate_dpd_bucket_query(where_clause=None):\n", "    return f\"\"\"\n", "        SELECT \n", "            CASE \n", "                WHEN s.dpd IS NULL THEN 'Unknown'\n", "                WHEN s.dpd = 0 THEN 'Current'\n", "                WHEN s.dpd BETWEEN 1 AND 30 THEN '1-30 Days'\n", "                WHEN s.dpd BETWEEN 31 AND 60 THEN '31-60 Days'\n", "                WHEN s.dpd BETWEEN 61 AND 90 THEN '61-90 Days'\n", "                WHEN s.dpd BETWEEN 91 AND 180 THEN '91-180 Days'\n", "                WHEN s.dpd BETWEEN 181 AND 365 THEN '181-365 Days'\n", "                WHEN s.dpd > 365 THEN '>365 Days'\n", "                ELSE 'Invalid'\n", "            END AS dpd_bucket,\n", "            COUNT(*) AS count\n", "        FROM summary s\n", "        {f\"WHERE {where_clause}\" if where_clause else \"\"}\n", "        GROUP BY dpd_bucket\n", "        ORDER BY \n", "            CASE \n", "                WHEN dpd_bucket = 'Current' THEN 1\n", "                WHEN dpd_bucket = '1-30 Days' THEN 2\n", "                WHEN dpd_bucket = '31-60 Days' THEN 3\n", "                WHEN dpd_bucket = '61-90 Days' THEN 4\n", "                WHEN dpd_bucket = '91-180 Days' THEN 5\n", "                WHEN dpd_bucket = '181-365 Days' THEN 6\n", "                WHEN dpd_bucket = '>365 Days' THEN 7\n", "                WHEN dpd_bucket = 'Unknown' THEN 8\n", "                ELSE 9\n", "            END;\n", "    \"\"\"\n"]}, {"cell_type": "code", "execution_count": 151, "id": "061a3cbe", "metadata": {}, "outputs": [], "source": ["query = generate_dpd_bucket_query(\"s.\\\"BankMstID\\\" = 14 AND s.\\\"Region_id\\\" IN ('BUC8', 'BUC14')\")\n"]}, {"cell_type": "code", "execution_count": 153, "id": "9ef0d350", "metadata": {}, "outputs": [], "source": ["df_summary[\"dpd_category\"] = df_summary[\"dpd\"].astype(int).apply(categorize_dpd)"]}, {"cell_type": "code", "execution_count": 152, "id": "53b019a3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>region</th>\n", "      <th>region_id</th>\n", "      <th>bankmstid</th>\n", "      <th>reference_no</th>\n", "      <th>score</th>\n", "      <th>dpd</th>\n", "      <th>disbursementid</th>\n", "      <th>risk_category</th>\n", "      <th>dpd_category</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Pune</td>\n", "      <td>BUC14</td>\n", "      <td>9</td>\n", "      <td>HIND250222CR8376706210843</td>\n", "      <td>None</td>\n", "      <td>40</td>\n", "      <td>1018</td>\n", "      <td>LOW</td>\n", "      <td>31-60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Pune</td>\n", "      <td>BUC14</td>\n", "      <td>9</td>\n", "      <td>HIND250222CR8376706210843</td>\n", "      <td>None</td>\n", "      <td>40</td>\n", "      <td>1018</td>\n", "      <td>LOW</td>\n", "      <td>31-60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Pune</td>\n", "      <td>BUC14</td>\n", "      <td>9</td>\n", "      <td>HIND250222CR8376706210843</td>\n", "      <td>None</td>\n", "      <td>40</td>\n", "      <td>1018</td>\n", "      <td>LOW - MEDIUM</td>\n", "      <td>31-60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Pune</td>\n", "      <td>BUC14</td>\n", "      <td>9</td>\n", "      <td>HIND250222CR8376706210843</td>\n", "      <td>None</td>\n", "      <td>40</td>\n", "      <td>1018</td>\n", "      <td>LOW - MEDIUM</td>\n", "      <td>31-60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Pune</td>\n", "      <td>BUC14</td>\n", "      <td>9</td>\n", "      <td>HIND250222CR8376706210843</td>\n", "      <td>None</td>\n", "      <td>40</td>\n", "      <td>1018</td>\n", "      <td>LOW - MEDIUM</td>\n", "      <td>31-60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81</th>\n", "      <td>Pune</td>\n", "      <td>BUC14</td>\n", "      <td>9</td>\n", "      <td>HIND250222CR9946706210843</td>\n", "      <td>None</td>\n", "      <td>40</td>\n", "      <td>1020</td>\n", "      <td>MEDIUM</td>\n", "      <td>31-60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82</th>\n", "      <td>Pune</td>\n", "      <td>BUC14</td>\n", "      <td>9</td>\n", "      <td>HIND250222CR9946706210843</td>\n", "      <td>None</td>\n", "      <td>40</td>\n", "      <td>1020</td>\n", "      <td>MEDIUM</td>\n", "      <td>31-60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>83</th>\n", "      <td>Pune</td>\n", "      <td>BUC14</td>\n", "      <td>9</td>\n", "      <td>HIND250222CR9946706210843</td>\n", "      <td>None</td>\n", "      <td>40</td>\n", "      <td>1020</td>\n", "      <td>MEDIUM</td>\n", "      <td>31-60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84</th>\n", "      <td>Pune</td>\n", "      <td>BUC14</td>\n", "      <td>9</td>\n", "      <td>HIND250222CR9946706210843</td>\n", "      <td>None</td>\n", "      <td>40</td>\n", "      <td>1020</td>\n", "      <td>HIGH</td>\n", "      <td>31-60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>85</th>\n", "      <td>Pune</td>\n", "      <td>BUC14</td>\n", "      <td>9</td>\n", "      <td>HIND250222CR9946706210843</td>\n", "      <td>None</td>\n", "      <td>40</td>\n", "      <td>1020</td>\n", "      <td>HIGH</td>\n", "      <td>31-60</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>86 rows × 9 columns</p>\n", "</div>"], "text/plain": ["   region region_id bankmstid               reference_no score dpd  \\\n", "0    Pune     BUC14         9  HIND250222CR8376706210843  None  40   \n", "1    Pune     BUC14         9  HIND250222CR8376706210843  None  40   \n", "2    Pune     BUC14         9  HIND250222CR8376706210843  None  40   \n", "3    Pune     BUC14         9  HIND250222CR8376706210843  None  40   \n", "4    Pune     BUC14         9  HIND250222CR8376706210843  None  40   \n", "..    ...       ...       ...                        ...   ...  ..   \n", "81   Pune     BUC14         9  HIND250222CR9946706210843  None  40   \n", "82   Pune     BUC14         9  HIND250222CR9946706210843  None  40   \n", "83   Pune     BUC14         9  HIND250222CR9946706210843  None  40   \n", "84   Pune     BUC14         9  HIND250222CR9946706210843  None  40   \n", "85   Pune     BUC14         9  HIND250222CR9946706210843  None  40   \n", "\n", "   disbursementid risk_category dpd_category  \n", "0            1018           LOW        31-60  \n", "1            1018           LOW        31-60  \n", "2            1018  LOW - MEDIUM        31-60  \n", "3            1018  LOW - MEDIUM        31-60  \n", "4            1018  LOW - MEDIUM        31-60  \n", "..            ...           ...          ...  \n", "81           1020        MEDIUM        31-60  \n", "82           1020        MEDIUM        31-60  \n", "83           1020        MEDIUM        31-60  \n", "84           1020          HIGH        31-60  \n", "85           1020          HIGH        31-60  \n", "\n", "[86 rows x 9 columns]"]}, "execution_count": 152, "metadata": {}, "output_type": "execute_result"}], "source": ["df_summary"]}, {"cell_type": "code", "execution_count": null, "id": "7d1130c0", "metadata": {}, "outputs": [], "source": ["final_response = {}\n", "access_to = request.data.get('access_to', None)\n", "designation = request.data.get('designation', None)"]}, {"cell_type": "code", "execution_count": 49, "id": "3fbf6ae8", "metadata": {}, "outputs": [], "source": ["access_string = f\"({', '.join(map(str, access_to))})\""]}, {"cell_type": "code", "execution_count": 50, "id": "ec3ccf6a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'state_id': 'all', 'region_id': 'all', 'branch_id': 'all', 'entity': 'all', 'product': 'all', 'village': 'all', 'group_name': 'all', 'category': 'all'}\n"]}], "source": ["filters = apply_filters_one(request)\n", "filters_list = apply_filters(filters, access_string)"]}, {"cell_type": "code", "execution_count": 37, "id": "599dd464", "metadata": {}, "outputs": [], "source": ["if designation in [1, 2] and filters_list[0] == 'null':\n", "    filters_list[0] = f\"state_id IN {access_string}\"\n", "if designation == 4 and filters_list[1] == 'null':\n", "    filters_list[1] = f\"region_id IN {access_string}\"\n", "if designation == 5 and filters_list[2] == 'null':\n", "    filters_list[2] = f\"branch_id IN {access_string}\""]}, {"cell_type": "code", "execution_count": 38, "id": "6c061add", "metadata": {}, "outputs": [], "source": ["final_query_filters = \" AND \".join(filter for filter in filters_list if filter != 'null')\n", "# Define selected columns for the query\n", "col_values = \"total_amount_disbursed, current_pos, product_name, number_of_instalments, total_demand, amount_collected, sanctioned_date, loan_cycle_id, date_of_last_payment, state, state_id, region, region_id, branch, branch_id\""]}, {"cell_type": "code", "execution_count": 39, "id": "780e2b25", "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "    EXEC [dbo].[scrub_summary]\n", "    @col_values = '{col_values}',\n", "    @where_conditions_values = '{final_query_filters}';\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 40, "id": "bcd3dfb8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "    EXEC [dbo].[scrub_summary]\n", "    @col_values = 'total_amount_disbursed, current_pos, product_name, number_of_instalments, total_demand, amount_collected, sanctioned_date, loan_cycle_id, date_of_last_payment, state, state_id, region, region_id, branch, branch_id',\n", "    @where_conditions_values = 'state_id IN (1001, 1002, 1003, 1004, 1005, 1006, 1007, 1008, 1009)';\n", "\n"]}], "source": ["print(query)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}
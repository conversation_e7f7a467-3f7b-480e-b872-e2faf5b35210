@import url("../../../index.css");

.card_container{
    border-color: var(--light-blue);
    cursor: pointer;
    
    :global(.ant-card-head){
        min-height: 45px;
        border-color: var(--light-blue);
        :global(.ant-card-head-title){
            color: var(--dark-blue);
            font-family: 'kanit','Courier New', Courier, monospace;
            text-align: center;
            font-weight: 500;
        }
    }

    :global(.ant-card-body){
        padding: 1rem;

        .text{
            font-family: 'kanit','Courier New', Courier, monospace;
            color: #0f2050b8;
        }

        .count{
            font-family: 'kanit','Courier New', Courier, monospace;
            color: #407BFF;
            font-weight: 500;
        }
    }

    &:hover{
        box-shadow: 0px 4px 4px 0px #00000040;
    }
}

// Responsive View
@media only screen and (max-width: 768px) {
    .card_container{
        :global(.ant-card-head){
            :global(.ant-card-head-title){
               font-size: 13px;
            }
        }
        :global(.ant-card-body){
            .text,.count{
               font-size: 12px;
            }
        }
    }
}
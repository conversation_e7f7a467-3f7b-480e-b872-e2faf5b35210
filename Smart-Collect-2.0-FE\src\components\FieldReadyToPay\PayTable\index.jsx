import { Table, Typography } from "antd";
import React, { useState } from "react";
import HISTORY_IMG from "../../../assets/Images/history_icon.png";
import PHONE_IMG from "../../../assets/Images/histroy.png";
import Style from "./_pay.module.scss";
import { FeedbackModal } from "../../Modals/FeedbackModal";
import { ConversionHistory } from "../../Modals/ConversionHistory";
import {
  SMART_COLLECT_MENU_IDS,
  formatAmount,
  handleDateFormatter,
} from "../../../constant";
import { FoDashboardModal } from "../../Modals/FoDashboardModal";
import PropTypes from "prop-types";

const { Text } = Typography;

export function PayTable({ tableData, pageId = null }) {
  const [modalState, setModalState] = useState({
    feedback: { isOpen: false, data: null },
    history: { isOpen: false, data: null },
    customer: { isOpen: false, data: null },
  });

  const openModal = ({ type, data }) => {
    setModalState((prev) => ({
      ...prev,
      [type]: { isOpen: true, data },
    }));
  };

  const closeModal = (type) => {
    setModalState((prev) => ({
      ...prev,
      [type]: { isOpen: false, data: null },
    }));
  };

  const columns = [
    {
      title: "Sr. No.",
      dataIndex: "key",
      rowScope: "row",
      width: 90,
      render: (_, data) => <Text>{data.key}.</Text>,
    },
    {
      title: "Name",
      dataIndex: "CustomerName",
      render: (_, data) => (
        <Text
          className={Style.name}
          onClick={() => openModal({ type: "customer", data })}
        >
          {data.CustomerName}
        </Text>
      ),
    },
    {
      title: "Contact",
      dataIndex: "MobileNumber",
      render: (_, data) => (
        <Text className={Style.contact}>{data.MobileNumber}</Text>
      ),
      responsive: ["md"],
    },
    {
      title: "Overdue Amount",
      dataIndex: "OverDueAmt",
      render: (_, data) => (
        <Text style={{ color: "#407BFF", fontWeight: 700 }}>
          Rs.{formatAmount(data.OverDueAmt ?? 0)}
        </Text>
      ),
    },
    {
      title: "Promise Amount",
      dataIndex: "Amount",
      render: (_, data) => (
        <Text style={{ color: "#407BFF", fontWeight: 700 }}>
          {data.Amount ? `Rs.${formatAmount(data.Amount)}` : "--"}
        </Text>
      ),
    },
    {
      title: "Promise Date",
      dataIndex: "PromiseDateTime",
      render: (_, data) => (
        <Text>
          {data.PromiseDateTime
            ? handleDateFormatter(data.PromiseDateTime.split("T")[0])
            : "--"}
        </Text>
      ),
    },
    {
      title: "Feedback",
      dataIndex: "feedback",
      //   fixed: 'right',
      width: 100,
      render: (_, data) => (
        <div className={Style.icon_div}>
          <div className={Style.icon_img}>
            <input
              type="image"
              alt="feedback"
              src={HISTORY_IMG}
              onClick={() => openModal({ type: "feedback", data })}
            />
          </div>
        </div>
      ),
    },
    {
      title: "History",
      dataIndex: "history",
      width: 100,
      render: (_, data) => (
        <div className={Style.icon_div}>
          <div className={Style.icon_img}>
            <input
              type="image"
              alt="history"
              src={PHONE_IMG}
              onClick={() => openModal({ type: "history", data })}
            />
          </div>
        </div>
      ),
    },
  ];

  const dataSource = Array.isArray(tableData)
    ? tableData.map((data, i) => ({ key: i + 1, ...data }))
    : [];
  return (
    <>
      <Table
        virtual
        className={Style.customTable}
        columns={columns}
        dataSource={dataSource}
        scroll={{
          x: 1000,
          y: 460,
        }}
        pagination={{
          showSizeChanger: false,
        }}
      />
      <FeedbackModal
        pageId={SMART_COLLECT_MENU_IDS.READY_TO_PAY}
        overdueAmount={modalState.feedback.data?.OverDueAmt}
        branchMstId={modalState.feedback.data?.BranchMstID}
        loanmstid={modalState.feedback.data?.LoanMstID}
        modalStatus={modalState.feedback.isOpen}
        handleSubmit={() => closeModal("feedback")}
        handleCancel={() => closeModal("feedback")}
      />
      <ConversionHistory
        customerName={modalState.history.data?.CustomerName}
        loanMstId={modalState.history.data?.LoanMstID}
        modalStatus={modalState.history.isOpen}
        handleCancel={() => closeModal("history")}
      />
      <FoDashboardModal
        data={modalState.customer.data}
        modalStatus={modalState.customer.isOpen}
        handleCancel={() => closeModal("customer")}
      />
    </>
  );
}

PayTable.propTypes = {
  tableData: PropTypes.array,
  pageId: PropTypes.string,
};

@import url("../../index.css");

$table-radius: 22px;
$attempts-bg: #bdd1ff;
$disable: #787777;
$body: #e4f8f9;

.download_button {
  background-color: var(--dark-blue);
  padding: 0.2rem 1rem;
  border-radius: 10px;
  cursor: pointer;
  width: 50px;
  align-items: end;
  margin-right: 1.3rem;
  border-color: transparent;
  img {
    width: 20px;
  }
}

.custom_table {
  padding-top: 1.5rem;

  .text {
    font-weight: 500;
    white-space: nowrap;
    color: var(--dark-blue);
  }
  .blueText {
    font-weight: 700;
    color: var(--blue);
    cursor: pointer;
  }

  //Global ant design classes
  // Pagination
  :global(.ant-pagination) {
    justify-content: center;
    margin: 0 !important;

    :global(.ant-pagination-prev),
    :global(.ant-pagination-next) {
      color: var(--dark-blue);
      border: 0 solid var(--blue);
      background: #e4f8f9;
      height: 25px;
      min-width: 15px;
      border-radius: 0px;
      margin: 0;
      button {
        border-radius: 0px;
      }
    }
    :global(.ant-pagination-item) {
      margin-right: 0;
      height: 0;
      a {
        color: $disable;
        font-size: 0.9rem;
        line-height: 23px;
        font-weight: normal;
        font-family: 'Kanit','Courier New', Courier, monospace;
      }
      &:hover {
        background-color: transparent;
      }
    }
    :global(.ant-pagination-item-active) {
      border: none;
      a {
        color: var(--dark-blue);
        font-size: 1.2rem;
        padding: 0;
      }
    }
  }

  //Table container
  :global(.ant-table-container) {
    padding: 0 1rem;
    margin-bottom: 0.5rem;
    background: var(--light-green);
    border-start-start-radius: $table-radius;
    border-start-end-radius: $table-radius;

    // Table header
    :global(.ant-table-header) {
      position: relative;
      margin: 0 auto;
      top: -21px;
      border-radius: $table-radius;

      :global(.ant-table-column-has-sorters) {
        background-color: var(--dark-blue);
        &:hover{
          background-color: var(--dark-blue);
        }
    
        :global(.ant-table-column-sorter-up),
        :global(.ant-table-column-sorter-down) {
          svg{
            fill: white;
          }
          &:global(.active){
            svg{
                fill: rgb(24, 155, 249)
                }
                }
            }
            }
    }
    
    // Table virtual body
    :global(.ant-table-tbody-virtual) {
      margin-top: -8px;
    }

    &::before {
      box-shadow: none !important;
    }
  }

  // Table rows header
  :global(.ant-table-thead > tr) {
    // First row
    &:nth-child(1) {
      // Fist 4 columns
      th {
        &:nth-child(1),
        &:nth-child(2),
        &:nth-child(3),
        &:nth-child(4) {
          border-inline-end: none !important;
          border-bottom: 0px !important;
        }
        &:nth-last-child(3),
        &:nth-last-child(2),
        &:nth-last-child(1) {
          border-inline-start: 1px solid white;
          border-bottom: 1px solid white !important;
        }
      }
    }

    // Second row
    &:nth-child(2) {
      th {
        &:nth-child(1) {
          border-inline-start: 1px solid white;
        }
      }
    }
    th {
      border-start-end-radius: 0 !important;
      background-color: var(--dark-blue);
      color: white;
      border-bottom: none;
      text-align: center;
      font-family: 'Kanit','Courier New', Courier, monospace;
      font-weight: normal;
      padding: 0.5rem;
      &:not(:last-child):not(.ant-table-selection-column):not(
          .ant-table-row-expand-icon-cell
        ):not([colspan])::before {
        width: 0;
      }
    }
  }

  // Table body
  :global(.ant-table-tbody) {
    // Body rows
    :global(.ant-table-row) {
      // Cols
      :global(.ant-table-cell) {
        text-align: center;
        border-bottom: 2px solid white;
        border-inline-end: 0px;
        font-family: 'Kanit','Courier New', Courier, monospace;
        font-weight: normal;
        padding: 0.5rem;

        :global(.ant-typography) {
          font-weight: 400;
          font-family: 'Kanit','Courier New', Courier, monospace;
          cursor: pointer;
        }

        // first column
        &:nth-last-child(2),
        &:nth-last-child(3),
        &:nth-last-child(4),
        &:nth-last-child(5),
        &:nth-last-child(6) {
          border-inline-start: 2px solid white;
        }

        &:global(.ant-table-cell-row-hover) {
          background-color: transparent;
        }
      }
    }
  }
}

.refresh_btn {
  border-color: var(--dark-blue);
  color: var(--dark-blue);
  outline: none;
  box-shadow: none;
  &:hover {
    border-color: var(--dark-blue) !important;
    color: var(--dark-blue) !important;
  }
}

// Responsive view
@media screen and (max-width: 768px) {
  .download_button,
  .refresh_btn{
    border-radius: 4px;
    margin-top: .5rem;
    img{
      width: 15px;
    }
  }

  .refresh_btn{
    font-size: 12px;
    height: auto;
    padding: 0.24rem .5rem;
  }

  .custom_table {
    // Table rows header
    :global(.ant-table-thead > tr) {
      th {
        font-size: 12px;
        padding: 0.5rem;
      }
    }
    :global(.ant-table-tbody) {
      :global(.ant-table-row) {
        :global(.ant-table-cell) {
          padding: 0.6rem;
          font-size: 12px !important;
          :global(.ant-typography){
            font-size: 12px !important;
          }
        }
      }
    }

    .icon_div {
      .icon_img {
        width: 13px;
        height: 13px;
      }
    }
  }
}